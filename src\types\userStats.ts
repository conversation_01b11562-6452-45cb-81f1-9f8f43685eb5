/**
 * User statistics data model
 */
export interface UserStats {
  weekStreak: number
  workoutsCompleted: number
  lbsLifted: number
}

/**
 * Type guard to validate UserStats object
 */
export function isUserStats(obj: unknown): obj is UserStats {
  if (!obj || typeof obj !== 'object') {
    return false
  }

  const stats = obj as Record<string, unknown>

  return (
    typeof stats.weekStreak === 'number' &&
    typeof stats.workoutsCompleted === 'number' &&
    typeof stats.lbsLifted === 'number'
  )
}

/**
 * Default UserStats object
 */
export const defaultUserStats: UserStats = {
  weekStreak: 0,
  workoutsCompleted: 0,
  lbsLifted: 0,
}
