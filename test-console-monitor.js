// Console monitoring script - paste this into browser console
// Run on http://localhost:3000/workout/exercise/27474

console.log('🔍 Starting console monitoring for infinite loop detection...');

let messageCount = 0;
let errorCount = 0;
let warningCount = 0;
let startTime = Date.now();
let messageHistory = [];
let loopDetection = new Map();

// Store original console methods
const originalConsole = {
    log: console.log,
    warn: console.warn,
    error: console.error,
    info: console.info
};

// Override console methods to track messages
function trackMessage(type, message, ...args) {
    messageCount++;
    const timestamp = Date.now();
    const messageStr = String(message).substring(0, 100);
    
    // Track message frequency for loop detection
    const messageKey = messageStr;
    if (!loopDetection.has(messageKey)) {
        loopDetection.set(messageKey, []);
    }
    
    const times = loopDetection.get(messageKey);
    times.push(timestamp);
    
    // Keep only messages from last 5 seconds
    const recent = times.filter(time => timestamp - time < 5000);
    loopDetection.set(message<PERSON>ey, recent);
    
    // If more than 20 similar messages in 5 seconds, it's likely a loop
    if (recent.length > 20) {
        originalConsole.error(`🔄 INFINITE LOOP DETECTED: ${messageKey}`);
        originalConsole.error(`   Frequency: ${recent.length} messages in 5 seconds`);
    }
    
    // Track by type
    if (type === 'error') errorCount++;
    if (type === 'warn') warningCount++;
    
    // Store in history (keep last 50 messages)
    messageHistory.push({
        type,
        message: messageStr,
        timestamp: timestamp - startTime,
        args: args.length
    });
    
    if (messageHistory.length > 50) {
        messageHistory.shift();
    }
    
    // Call original method
    originalConsole[type](message, ...args);
}

// Override console methods
console.log = (...args) => trackMessage('log', ...args);
console.warn = (...args) => trackMessage('warn', ...args);
console.error = (...args) => trackMessage('error', ...args);
console.info = (...args) => trackMessage('info', ...args);

// Report function
function getReport() {
    const elapsed = (Date.now() - startTime) / 1000;
    const messageRate = messageCount / elapsed;
    
    const report = {
        elapsed: elapsed.toFixed(1) + 's',
        totalMessages: messageCount,
        errors: errorCount,
        warnings: warningCount,
        messageRate: messageRate.toFixed(1) + '/sec',
        status: messageRate > 10 ? '❌ HIGH RATE' : messageRate > 5 ? '⚠️ MODERATE' : '✅ NORMAL',
        topMessages: getTopMessages(),
        recentMessages: messageHistory.slice(-10)
    };
    
    return report;
}

function getTopMessages() {
    const messageCounts = new Map();
    
    loopDetection.forEach((times, message) => {
        messageCounts.set(message, times.length);
    });
    
    return Array.from(messageCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([message, count]) => ({ message, count }));
}

// Auto-report every 10 seconds
const reportInterval = setInterval(() => {
    const report = getReport();
    originalConsole.log('📊 Console Monitor Report:', report);
    
    if (report.messageRate > 20) {
        originalConsole.error('🚨 CRITICAL: Very high message rate detected!');
        originalConsole.log('Top repeating messages:', report.topMessages);
    }
}, 10000);

// Manual report function
window.getConsoleReport = getReport;

// Stop monitoring function
window.stopConsoleMonitoring = () => {
    clearInterval(reportInterval);
    console.log = originalConsole.log;
    console.warn = originalConsole.warn;
    console.error = originalConsole.error;
    console.info = originalConsole.info;
    originalConsole.log('🛑 Console monitoring stopped');
    originalConsole.log('📊 Final Report:', getReport());
};

originalConsole.log('✅ Console monitoring started');
originalConsole.log('📝 Commands:');
originalConsole.log('   getConsoleReport() - Get current report');
originalConsole.log('   stopConsoleMonitoring() - Stop monitoring');
originalConsole.log('🎯 Navigate to exercise page and monitor for 30 seconds...');
