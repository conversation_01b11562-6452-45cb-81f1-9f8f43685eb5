import { test, expect, type Page } from '@playwright/test'

test.describe('bfcache Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // Clear all cookies/storage before each test
    await page.context().clearCookies()
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })
  })

  async function login(page: Page) {
    await page.goto('/login')
    await page.waitForLoadState('networkidle')

    // Fill in login form
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('/program', { timeout: 10000 })
  }

  test('should not prevent bfcache when navigating away from pages', async ({
    page,
  }) => {
    // Step 1: Login
    await login(page)

    // Step 2: Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Step 3: Navigate to a different page
    await page.goto('/program')
    await page.waitForLoadState('networkidle')

    // Step 4: Use browser back button
    await page.goBack()

    // Step 5: Check if page was restored from bfcache
    // If bfcache is working, the page should load instantly without network requests
    const navigationEntries = await page.evaluate(() => {
      const entries = performance.getEntriesByType(
        'navigation'
      ) as PerformanceNavigationTiming[]
      return entries.map((entry) => ({
        type: entry.type,
        transferSize: entry.transferSize,
        duration: entry.duration,
      }))
    })

    // Check the last navigation entry
    const lastNavigation = navigationEntries[navigationEntries.length - 1]

    // Type 2 indicates back_forward navigation
    // transferSize of 0 indicates the page was loaded from cache
    expect(lastNavigation.type).toBe(2) // back_forward

    // Note: We can't directly test bfcache vs regular cache, but we can verify
    // that no beforeunload listeners are preventing bfcache
  })

  test('should not have beforeunload event listeners that prevent bfcache', async ({
    page,
  }) => {
    await login(page)

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Check for beforeunload event listeners
    const hasBeforeUnloadListener = await page.evaluate(() => {
      // Check if window has beforeunload listener
      // This is a bit tricky as we can't directly inspect listeners
      // But we can check if setting window.onbeforeunload has any effect
      const originalOnBeforeUnload = window.onbeforeunload
      let listenerCalled = false

      // Try to trigger any existing beforeunload handler
      window.onbeforeunload = () => {
        listenerCalled = true
        return null
      }

      // Create and dispatch a beforeunload event
      const event = new Event('beforeunload')
      window.dispatchEvent(event)

      // Restore original
      window.onbeforeunload = originalOnBeforeUnload

      // If our dummy handler wasn't called, there might be other listeners
      // But for our purposes, we mainly care that we're not preventing unload
      return listenerCalled || originalOnBeforeUnload !== null
    })

    expect(hasBeforeUnloadListener).toBe(false)
  })

  test('should still show navigation confirmation for active workouts', async ({
    page,
  }) => {
    await login(page)

    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Start a workout by clicking on an exercise (if available)
    const exerciseCard = await page
      .locator('[data-testid="exercise-card"]')
      .first()
    if (await exerciseCard.isVisible()) {
      // Set up dialog handler before clicking
      page.on('dialog', async (dialog) => {
        expect(dialog.type()).toBe('confirm')
        expect(dialog.message()).toContain('You have an active workout')
        await dialog.dismiss() // Cancel navigation
      })

      await exerciseCard.click()
      await page.waitForLoadState('networkidle')

      // Try to navigate away using a link/button that uses navigateWithConfirmation
      // This would typically be a navigation button in the app
      // For now, we can't test this directly without knowing the specific UI elements
    }
  })
})
