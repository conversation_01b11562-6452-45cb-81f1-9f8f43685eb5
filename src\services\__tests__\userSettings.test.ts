/**
 * Tests for user settings service
 */

import { getUserSettings, getUserSettingsSync } from '../userSettings'
import { userProfileApi } from '@/api/userProfile'

// Mock the userProfileApi
vi.mock('@/api/userProfile', () => ({
  userProfileApi: {
    getUserInfo: vi.fn(),
  },
}))

describe('UserSettings Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getUserSettings', () => {
    it('should return default settings when API call succeeds', async () => {
      // Mock successful API response
      vi.mocked(userProfileApi.getUserInfo).mockResolvedValue({
        StatusCode: 200,
        Result: {
          Email: '<EMAIL>',
          FirstName: 'Test',
          LastName: 'User',
        },
      })

      const settings = await getUserSettings()

      expect(settings).toEqual({
        isQuickMode: null, // Updated to match mobile app behavior
        isStrengthPhase: false,
        isFreePlan: false,
        isFirstWorkoutOfStrengthPhase: false,
        lightSessionDays: null,
      })
    })

    it('should return default settings when API call fails', async () => {
      // Mock API failure
      vi.mocked(userProfileApi.getUserInfo).mockRejectedValue(
        new Error('API Error')
      )

      const settings = await getUserSettings()

      expect(settings).toEqual({
        isQuickMode: null, // Updated to match mobile app behavior
        isStrengthPhase: false,
        isFreePlan: false,
        isFirstWorkoutOfStrengthPhase: false,
        lightSessionDays: null,
      })
    })
  })

  describe('getUserSettingsSync', () => {
    it('should return default settings synchronously', () => {
      const settings = getUserSettingsSync()

      expect(settings).toEqual({
        isQuickMode: null, // Updated to match mobile app behavior
        isStrengthPhase: false,
        isFreePlan: false,
        isFirstWorkoutOfStrengthPhase: false,
        lightSessionDays: null,
      })
    })
  })
})
