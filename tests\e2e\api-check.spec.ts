/* eslint-disable no-console */
import { test, request } from '@playwright/test'

test.describe('API Endpoint Checks', () => {
  test('should check API endpoints directly without browser', async () => {
    const apiContext = await request.newContext({
      baseURL: 'https://drmuscle.azurewebsites.net',
      ignoreHTTPSErrors: true,
    })

    console.log('Testing Dr. Muscle API endpoints...')

    // Test if API is reachable
    try {
      const healthResponse = await apiContext.get('/api/health', {
        timeout: 10000,
      })
      console.log('API health check status:', healthResponse.status())
      console.log('API health check headers:', healthResponse.headers())
    } catch (error) {
      console.log('API health check failed:', error.message)
    }

    // Test login endpoint with wrong credentials to check if it's accessible
    try {
      const loginResponse = await apiContext.post('/token', {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: 'grant_type=password&username=<EMAIL>&password=wrongpassword',
        timeout: 10000,
      })
      console.log('Login endpoint status:', loginResponse.status())
      const responseBody = await loginResponse.text()
      console.log('Login endpoint response:', responseBody.substring(0, 200))

      // Check CORS headers
      const headers = loginResponse.headers()
      console.log('CORS headers:', {
        'access-control-allow-origin': headers['access-control-allow-origin'],
        'access-control-allow-credentials':
          headers['access-control-allow-credentials'],
      })
    } catch (error) {
      console.log('Login endpoint error:', error.message)
    }

    // Test workout endpoint (requires auth, so should return 401)
    try {
      const workoutResponse = await apiContext.post(
        '/api/Workout/GetUserWorkoutTemplateGroup',
        {
          data: {},
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 10000,
        }
      )
      console.log('Workout endpoint status:', workoutResponse.status())
      console.log('Workout endpoint headers:', workoutResponse.headers())

      if (workoutResponse.status() === 405) {
        console.log('ERROR: Workout endpoint returns 405 - Method Not Allowed')
        console.log('This suggests the endpoint might need GET instead of POST')
      }
    } catch (error) {
      console.log('Workout endpoint error:', error.message)
    }

    // Try workout endpoint with GET
    try {
      const workoutGetResponse = await apiContext.get(
        '/api/Workout/GetUserWorkoutTemplateGroup',
        {
          timeout: 10000,
        }
      )
      console.log('Workout GET endpoint status:', workoutGetResponse.status())
    } catch (error) {
      console.log('Workout GET endpoint error:', error.message)
    }

    await apiContext.dispose()
  })

  test('should check production website directly', async () => {
    const webContext = await request.newContext({
      baseURL: 'https://x.dr-muscle.com',
      ignoreHTTPSErrors: true,
    })

    console.log('Testing production website...')

    // Check if login page loads
    try {
      const loginPage = await webContext.get('/login', {
        timeout: 10000,
      })
      console.log('Login page status:', loginPage.status())
      const html = await loginPage.text()
      console.log(
        'Login page contains "Dr. Muscle":',
        html.includes('Dr. Muscle')
      )
      console.log(
        'Login page contains login form:',
        html.includes('Email') && html.includes('Password')
      )
    } catch (error) {
      console.log('Login page error:', error.message)
    }

    await webContext.dispose()
  })
})
