import { render, screen, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import ExercisePage from '../page'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')

const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
}

vi.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
}))

// Mock NavigationContext
vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: () => ({
    navConfig: {},
    setNavConfig: vi.fn(),
    setTitle: vi.fn(),
  }),
}))

describe('ExercisePage - Direct Navigation', () => {
  const mockExercise = {
    Id: 3216,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
  }

  const mockWorkout = [
    {
      WorkoutTemplates: [
        {
          Exercises: [mockExercise],
        },
      ],
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    mockRouter.push.mockClear()
    mockRouter.replace.mockClear()
  })

  it('should handle direct navigation to exercise page when workout is not loaded', async () => {
    // Setup: No workout loaded initially
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: null,
      isLoadingWorkout: true,
      workoutError: null,
      startWorkout: vi.fn(),
      exercises: [],
      exerciseWorkSetsModels: [],
      // ... other required props
    } as any)

    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: [],
      currentExerciseIndex: 0,
      setCurrentExerciseById: vi.fn(),
      getCachedExerciseRecommendation: vi.fn(),
      loadingStates: new Map(),
      // ... other required props
    } as any)

    // Act: Render the page with exercise ID
    const params = { id: '3216' }
    render(<ExercisePage params={params} />)

    // Assert: Should show loading state initially
    expect(screen.getByText('Loading exercise data...')).toBeInTheDocument()
  })

  it('should load workout data and display exercise when navigating directly', async () => {
    // Setup: Simulate workout data loading
    const mockStartWorkout = vi.fn()
    const mockSetCurrentExerciseById = vi.fn()

    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: mockWorkout,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: mockStartWorkout,
      exercises: [mockExercise],
      exerciseWorkSetsModels: [],
      workoutSession: { id: 'test-session', exercises: [] },
      getRecommendation: vi.fn().mockResolvedValue(null),
      // ... other required props
    } as any)

    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      setCurrentExerciseById: mockSetCurrentExerciseById,
      currentExercise: mockExercise,
      getCachedExerciseRecommendation: vi.fn(),
      loadingStates: new Map(),
      // ... other required props
    } as any)

    // Act: Render the page
    const params = { id: '3216' }
    render(<ExercisePage params={params} />)

    // Assert: Should eventually display the exercise
    await waitFor(() => {
      expect(mockSetCurrentExerciseById).toHaveBeenCalledWith(3216)
    })
  })

  it('should redirect to workout page if exercise ID is invalid', async () => {
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: mockWorkout,
      isLoadingWorkout: false,
      workoutError: null,
      exercises: [mockExercise],
      startWorkout: vi.fn(),
      workoutSession: null,
      // ... other required props
    } as any)

    vi.mocked(useWorkoutStore).mockReturnValue({
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      setCurrentExerciseById: vi.fn(),
      getCachedExerciseRecommendation: vi.fn(),
      loadingStates: new Map(),
      // ... other required props
    } as any)

    // Act: Render with invalid exercise ID
    const params = { id: '9999' }
    render(<ExercisePage params={params} />)

    // Assert: Should redirect to workout page
    await waitFor(() => {
      expect(mockRouter.replace).toHaveBeenCalledWith('/workout')
    })
  })
})
