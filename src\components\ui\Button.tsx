'use client'

import React, { forwardRef, ButtonHTMLAttributes } from 'react'
import { cn } from '@/lib/utils'
import { themeConfig } from '@/config/theme'

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  loading?: boolean
  children: React.ReactNode
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      fullWidth = false,
      loading = false,
      className,
      onClick,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      // Haptic feedback for mobile
      if (navigator.vibrate && themeConfig.mobile.haptic.enabled) {
        navigator.vibrate(themeConfig.mobile.haptic.duration)
      }

      if (onClick && !loading) {
        onClick(e)
      }
    }

    const sizeClasses = {
      sm: 'h-11 px-4 text-sm', // 44px height (minimum touch target)
      md: 'h-12 px-6 text-base', // 48px height (comfortable)
      lg: 'h-14 px-8 text-lg', // 56px height (large)
    }

    const baseClasses = cn(
      // Base button styles
      'inline-flex items-center justify-center',
      'font-medium transition-all duration-200',
      'touch-manipulation select-none',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
      // Size
      sizeClasses[size],
      // Touch target optimization
      'min-w-[44px]', // Minimum touch target
      'active:scale-95', // Touch feedback
      // Full width
      fullWidth && 'w-full',
      // Loading state
      loading && 'cursor-wait'
    )

    // Variant styles using theme configuration
    const variantStyles = {
      primary: cn(
        'bg-brand-primary text-text-inverse',
        'hover:bg-brand-primary/90',
        'shadow-theme-md hover:shadow-theme-lg',
        'rounded-theme',
        'focus-visible:ring-brand-primary'
      ),
      secondary: cn(
        'bg-bg-secondary text-brand-primary',
        'border border-brand-primary/20 hover:border-brand-primary/40',
        'hover:bg-bg-tertiary',
        'rounded-theme',
        'focus-visible:ring-brand-primary'
      ),
      ghost: cn(
        'text-brand-primary',
        'hover:bg-brand-primary/10',
        'rounded-theme',
        'focus-visible:ring-brand-primary'
      ),
      danger: cn(
        'bg-red-600 text-white',
        'hover:bg-red-700',
        'shadow-theme-md hover:shadow-theme-lg',
        'rounded-theme',
        'focus-visible:ring-red-600'
      ),
    }

    const variantClasses = variantStyles[variant]

    return (
      <button
        ref={ref}
        type="button"
        className={cn(baseClasses, variantClasses, className)}
        onClick={handleClick}
        disabled={disabled || loading}
        data-variant={variant}
        data-size={size}
        {...props}
      >
        {loading ? (
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            <span>Loading...</span>
          </div>
        ) : (
          children
        )}
      </button>
    )
  }
)

Button.displayName = 'Button'
