/**
 * Constants for auth store
 */

export const CACHE_VERSION = 1
export const USER_INFO_CACHE_TTL = 7 * 24 * 60 * 60 * 1000 // 1 week in milliseconds
export const SESSION_TIMEOUT = 30 * 60 * 1000 // 30 minutes of inactivity
// export const SESSION_CHECK_INTERVAL = 60 * 1000 // Check every minute - for future use

export const initialState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  hasHydrated: false,
  cachedUserInfo: null,
  cacheVersion: CACHE_VERSION,
  lastActivity: Date.now(),
}
