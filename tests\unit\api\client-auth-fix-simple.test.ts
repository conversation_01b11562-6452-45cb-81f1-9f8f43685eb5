import { describe, it, expect, vi, beforeEach } from 'vitest'

// Mock the logger to avoid import errors
vi.mock('@/utils/logger', () => ({
  logger: {
    warn: vi.fn(),
    error: vi.fn(),
  },
}))

// Mock CSRF utility
vi.mock('@/utils/csrf', () => ({
  withCSRFToken: vi.fn((headers) => headers),
}))

// Mock the global fetch
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock window.location
const mockLocation = { href: '' }
Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
})

describe('API Client Auth Integration Test', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockFetch.mockReset()
    mockLocation.href = ''
  })

  it('should handle auth flow correctly', async () => {
    // Import the real client (not mocked)
    const { apiClient } = await import('@/api/client')

    // Test 1: Request interceptor should fetch token when not set
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ authenticated: true, token: 'test-token-123' }),
    })

    const config = {
      url: '/api/test',
      method: 'GET',
      headers: {},
    }

    // Get and call the request interceptor
    const requestInterceptor =
      apiClient.interceptors.request.handlers[0]?.fulfilled
    const resultConfig = await requestInterceptor(config)

    expect(mockFetch).toHaveBeenCalledWith('/api/auth/token', {
      credentials: 'include',
    })
    expect(resultConfig.headers['Authorization']).toBe('Bearer test-token-123')
  })

  it('should handle 401 errors without refresh endpoint', async () => {
    // Import the real client (not mocked)
    const { apiClient } = await import('@/api/client')

    // Mock the DELETE request to clear cookies
    mockFetch.mockResolvedValueOnce({ ok: true })

    const error = {
      response: {
        status: 401,
        data: {},
      },
      config: {
        url: '/api/test',
        _retry: false,
      },
    }

    // Get and call the response error interceptor
    const responseInterceptor =
      apiClient.interceptors.response.handlers[0]?.rejected

    try {
      await responseInterceptor(error)
    } catch (e: any) {
      expect(e.name).toBe('AuthenticationError')
      expect(e.message).toBe('Authentication required. Please login again.')
    }

    // Verify redirect to login
    expect(mockLocation.href).toBe('/login')

    // Verify cookies were cleared
    expect(mockFetch).toHaveBeenCalledWith('/api/auth/exchange', {
      method: 'DELETE',
      credentials: 'include',
    })
  })
})
