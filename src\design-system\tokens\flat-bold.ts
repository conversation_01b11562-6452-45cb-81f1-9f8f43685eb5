/**
 * Flat Bold Theme Tokens
 * Power & precision with high-impact colors and sharp edges
 */

import { Theme } from '../types'

export const flatBoldTheme: Theme = {
  name: 'flat-bold',
  colors: {
    background: {
      primary: '#000000',
      secondary: '#FFFFFF',
      tertiary: '#F5F5F5',
      overlay: 'rgba(0, 0, 0, 0.9)',
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#000000',
      tertiary: '#666666',
      inverse: '#000000',
    },
    brand: {
      primary: '#00FF88', // Electric green
      secondary: '#00CC6A',
      accent: '#00FF88',
    },
    status: {
      success: '#00FF88',
      error: '#FF0040',
      warning: '#FFB800',
      info: '#0080FF',
    },
    interactive: {
      hover: '#00FF88',
      active: '#00CC6A',
      disabled: '#333333',
    },
  },
  typography: {
    fonts: {
      heading: '"Bebas Neue", sans-serif',
      body: '"Inter", -apple-system, BlinkMacSystemFont, sans-serif',
      mono: '"IBM Plex Mono", monospace',
    },
    sizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.75rem',
      '3xl': '2.5rem',
      '4xl': '4rem',
    },
    weights: {
      light: 300,
      regular: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeights: {
      tight: 1.1,
      normal: 1.4,
      relaxed: 1.6,
    },
  },
  spacing: {
    unit: 4,
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
    '4xl': '6rem',
  },
  shadows: {
    none: 'none',
    sm: 'none',
    md: 'none',
    lg: 'none',
    xl: 'none',
    inner: 'none',
  },
  animations: {
    duration: {
      fast: '100ms',
      normal: '200ms',
      slow: '300ms',
    },
    easing: {
      default: 'linear',
      smooth: 'ease-out',
      spring: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    },
  },
  borders: {
    radius: {
      none: '0',
      sm: '0',
      md: '0',
      lg: '0',
      full: '0',
    },
    width: {
      hairline: '1px',
      thin: '2px',
      medium: '4px',
      thick: '8px',
    },
  },
}
