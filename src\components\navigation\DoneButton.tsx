import React from 'react'
import { useRouter } from 'next/navigation'

export function DoneButton() {
  const router = useRouter()

  const handleDoneClick = () => {
    // Add haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(10)
    }
    router.push('/program')
  }

  return (
    <button
      onClick={handleDoneClick}
      className="text-blue-500 font-medium px-3 py-1 rounded-lg hover:bg-blue-50 transition-colors"
      aria-label="Done"
    >
      Done
    </button>
  )
}
