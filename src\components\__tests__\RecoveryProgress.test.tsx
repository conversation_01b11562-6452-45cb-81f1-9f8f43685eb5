import React from 'react'
import { render, screen } from '@testing-library/react'
import { RecoveryProgress } from '../RecoveryProgress'
import '@testing-library/jest-dom'

describe('RecoveryProgress', () => {
  it('renders with 0% progress', () => {
    render(<RecoveryProgress percentage={0} />)
    expect(screen.getByText('0%')).toBeInTheDocument()
  })

  it('renders with 50% progress', () => {
    render(<RecoveryProgress percentage={50} />)
    expect(screen.getByText('50%')).toBeInTheDocument()
  })

  it('renders with 100% progress', () => {
    render(<RecoveryProgress percentage={100} />)
    expect(screen.getByText('100%')).toBeInTheDocument()
  })

  it('handles invalid percentage values', () => {
    render(<RecoveryProgress percentage={NaN} />)
    expect(screen.getByText('0%')).toBeInTheDocument()
  })

  it('handles negative percentage values', () => {
    render(<RecoveryProgress percentage={-10} />)
    expect(screen.getByText('-10%')).toBeInTheDocument()
  })

  it('handles percentage values over 100', () => {
    render(<RecoveryProgress percentage={150} />)
    expect(screen.getByText('150%')).toBeInTheDocument()
  })

  it('applies custom size and strokeWidth', () => {
    const { container } = render(
      <RecoveryProgress percentage={75} size={100} strokeWidth={10} />
    )
    const svg = container.querySelector('svg')
    expect(svg).toHaveAttribute('width', '100')
    expect(svg).toHaveAttribute('height', '100')
  })

  it('applies custom className', () => {
    const { container } = render(
      <RecoveryProgress percentage={25} className="custom-class" />
    )
    const svg = container.querySelector('svg')
    expect(svg).toHaveClass('custom-class')
  })

  it('calculates correct SVG stroke-dashoffset for different percentages', () => {
    const { container, rerender } = render(<RecoveryProgress percentage={0} />)

    // For 0%, the offset should be equal to circumference (full dash)
    let progressCircle = container.querySelectorAll('circle')[1]
    const circumference = 2 * Math.PI * 36 // radius = (80-8)/2 = 36
    expect(progressCircle).toHaveAttribute(
      'stroke-dashoffset',
      circumference.toString()
    )

    // For 50%, the offset should be half the circumference
    rerender(<RecoveryProgress percentage={50} />)
    ;[, progressCircle] = container.querySelectorAll('circle')
    expect(progressCircle).toHaveAttribute(
      'stroke-dashoffset',
      (circumference * 0.5).toString()
    )

    // For 100%, the offset should be 0 (no dash)
    rerender(<RecoveryProgress percentage={100} />)
    ;[, progressCircle] = container.querySelectorAll('circle')
    expect(progressCircle).toHaveAttribute('stroke-dashoffset', '0')
  })

  it('uses correct colors based on percentage', () => {
    const { container, rerender } = render(<RecoveryProgress percentage={25} />)

    // Less than 50% should be red
    let progressCircle = container.querySelectorAll('circle')[1]
    expect(progressCircle).toHaveAttribute('stroke', '#EF4444')

    // Between 50% and 75% should be amber
    rerender(<RecoveryProgress percentage={60} />)
    ;[, progressCircle] = container.querySelectorAll('circle')
    expect(progressCircle).toHaveAttribute('stroke', '#F59E0B')

    // 75% and above should be green
    rerender(<RecoveryProgress percentage={80} />)
    ;[, progressCircle] = container.querySelectorAll('circle')
    expect(progressCircle).toHaveAttribute('stroke', '#10B981')
  })
})
