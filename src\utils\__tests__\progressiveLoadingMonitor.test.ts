import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  ProgressiveLoadingMonitor,
  startProgressiveLoadingMonitoring,
  checkProgressiveLoadingPerformance,
  getProgressiveLoadingReport,
  resetProgressiveLoadingMonitor,
} from '../progressiveLoadingMonitor'
import { userInfoPerformance, getUserInfoMetrics } from '../userInfoPerformance'

// Mock userInfoPerformance
vi.mock('../userInfoPerformance', () => ({
  userInfoPerformance: {
    reset: vi.fn(),
  },
  getUserInfoMetrics: vi.fn(),
}))

// Mock gtag
declare global {
  interface Window {
    gtag: typeof globalThis.gtag
  }
}

describe('ProgressiveLoadingMonitor', () => {
  let consoleLogSpy: any
  let consoleWarnSpy: any
  let consoleErrorSpy: any
  let mockGtag: any

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock console methods
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    // Mock gtag
    mockGtag = vi.fn()
    window.gtag = mockGtag as any

    // Reset monitor
    resetProgressiveLoadingMonitor()
  })

  afterEach(() => {
    consoleLogSpy.mockRestore()
    consoleWarnSpy.mockRestore()
    consoleErrorSpy.mockRestore()
    delete (window as any).gtag
  })

  describe('startMonitoring', () => {
    it('should start monitoring and log in development', () => {
      vi.stubEnv('NODE_ENV', 'development')

      startProgressiveLoadingMonitoring()

      expect(consoleLogSpy).toHaveBeenCalledWith(
        '[Progressive Loading Monitor] Started monitoring session'
      )
    })

    it('should not log in production', () => {
      vi.stubEnv('NODE_ENV', 'production')

      startProgressiveLoadingMonitoring()

      expect(consoleLogSpy).not.toHaveBeenCalled()
    })
  })

  describe('checkPerformance', () => {
    const mockMetrics = {
      loginToFirstByte: 1500,
      userInfoLoadTime: 1000,
      statsLoadTime: 2000,
      overallPageReady: 3500,
      metricLoadTimes: {
        streak: 100,
        workouts: 200,
        volume: 300,
      },
      cacheMetrics: {
        hitRate: 80,
        hits: 4,
        misses: 1,
        writeLatency: 10,
        readLatency: 5,
      },
      retryMetrics: {
        userInfoRetries: 0,
        statsRetries: 0,
        totalRetries: 0,
      },
      analytics: {
        timestamp: Date.now(),
        sessionId: 'test-session-123',
      },
    }

    it('should not check performance if monitoring not started', () => {
      vi.mocked(getUserInfoMetrics).mockReturnValue(mockMetrics as any)

      checkProgressiveLoadingPerformance()

      expect(consoleWarnSpy).not.toHaveBeenCalled()
    })

    it('should report violations in development', () => {
      vi.stubEnv('NODE_ENV', 'development')
      vi.mocked(getUserInfoMetrics).mockReturnValue({
        ...mockMetrics,
        loginToFirstByte: 3000, // Exceeds threshold
        statsLoadTime: 3000, // Exceeds threshold
        cacheMetrics: {
          ...mockMetrics.cacheMetrics,
          hitRate: 50, // Below threshold
        },
      } as any)

      startProgressiveLoadingMonitoring()
      checkProgressiveLoadingPerformance()

      expect(consoleWarnSpy).toHaveBeenCalledWith(
        '[Progressive Loading Monitor] Performance issues detected:'
      )
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('Login to first byte took 3000ms')
      )
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('Stats load took 3000ms')
      )
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('Cache hit rate is 50.0%')
      )
    })

    it('should send alerts in production', () => {
      vi.stubEnv('NODE_ENV', 'production')
      vi.mocked(getUserInfoMetrics).mockReturnValue({
        ...mockMetrics,
        overallPageReady: 5000, // Exceeds threshold
      } as any)

      startProgressiveLoadingMonitoring()
      checkProgressiveLoadingPerformance()

      expect(mockGtag).toHaveBeenCalledWith('event', 'performance_violation', {
        event_category: 'progressive_loading',
        event_label: expect.stringContaining('Overall page ready took 5000ms'),
        value: 1,
        custom_parameter_1: expect.any(String),
        session_id: 'test-session-123',
      })
    })

    it('should not send duplicate alerts', () => {
      vi.stubEnv('NODE_ENV', 'production')
      vi.mocked(getUserInfoMetrics).mockReturnValue({
        ...mockMetrics,
        userInfoLoadTime: 2000, // Exceeds threshold
      } as any)

      startProgressiveLoadingMonitoring()

      // Check performance twice
      checkProgressiveLoadingPerformance()
      checkProgressiveLoadingPerformance()

      // Should only send alert once
      expect(mockGtag).toHaveBeenCalledTimes(1)
    })

    it('should handle null values gracefully', () => {
      vi.mocked(getUserInfoMetrics).mockReturnValue({
        ...mockMetrics,
        loginToFirstByte: null,
        userInfoLoadTime: null,
        statsLoadTime: null,
        overallPageReady: null,
      } as any)

      startProgressiveLoadingMonitoring()

      expect(() => checkProgressiveLoadingPerformance()).not.toThrow()
      expect(consoleWarnSpy).not.toHaveBeenCalled()
    })
  })

  describe('generateReport', () => {
    it('should generate a comprehensive report', () => {
      const mockMetrics = {
        loginToFirstByte: 1234,
        userInfoLoadTime: 567,
        statsLoadTime: 890,
        overallPageReady: 2691,
        metricLoadTimes: {
          streak: 100,
          workouts: 200,
          volume: 300,
        },
        cacheMetrics: {
          hitRate: 75.5,
          hits: 10,
          misses: 3,
          writeLatency: 12.5,
          readLatency: 5.2,
        },
        retryMetrics: {
          userInfoRetries: 1,
          statsRetries: 2,
          totalRetries: 3,
          lastRetryReason: 'Network timeout',
        },
        analytics: {
          timestamp: Date.now(),
          sessionId: 'test-session-456',
        },
      }

      vi.mocked(getUserInfoMetrics).mockReturnValue(mockMetrics as any)

      const report = getProgressiveLoadingReport()

      expect(report).toContain('=== Progressive Loading Performance Report ===')
      expect(report).toContain('Login to First Byte: 1234ms')
      expect(report).toContain('UserInfo Load: 567ms')
      expect(report).toContain('Stats Load: 890ms')
      expect(report).toContain('Overall Page Ready: 2691ms')
      expect(report).toContain('Streak: 100ms')
      expect(report).toContain('Workouts: 200ms')
      expect(report).toContain('Volume: 300ms')
      expect(report).toContain('Hit Rate: 75.5%')
      expect(report).toContain('Hits: 10')
      expect(report).toContain('Misses: 3')
      expect(report).toContain('Read Latency: 5.2ms')
      expect(report).toContain('Write Latency: 12.5ms')
      expect(report).toContain('UserInfo: 1')
      expect(report).toContain('Stats: 2')
      expect(report).toContain('Total: 3')
      expect(report).toContain('Last Reason: Network timeout')
      expect(report).toContain('Session ID: test-session-456')
    })

    it('should handle missing data gracefully', () => {
      const mockMetrics = {
        loginToFirstByte: null,
        userInfoLoadTime: null,
        statsLoadTime: null,
        overallPageReady: null,
        metricLoadTimes: {
          streak: null,
          workouts: null,
          volume: null,
        },
        cacheMetrics: {
          hitRate: 0,
          hits: 0,
          misses: 0,
          writeLatency: null,
          readLatency: null,
        },
        retryMetrics: {
          userInfoRetries: 0,
          statsRetries: 0,
          totalRetries: 0,
        },
        analytics: {
          timestamp: Date.now(),
          sessionId: 'test-session-789',
        },
      }

      vi.mocked(getUserInfoMetrics).mockReturnValue(mockMetrics as any)

      const report = getProgressiveLoadingReport()

      expect(report).toContain('Login to First Byte: N/A')
      expect(report).toContain('UserInfo Load: N/A')
      expect(report).toContain('Stats Load: N/A')
      expect(report).toContain('Overall Page Ready: N/A')
      expect(report).toContain('Streak: N/A')
      expect(report).toContain('Read Latency: N/A')
      expect(report).not.toContain('API Retries:') // Should not show if no retries
    })
  })

  describe('reset', () => {
    it('should reset monitor and userInfoPerformance', () => {
      startProgressiveLoadingMonitoring()
      resetProgressiveLoadingMonitor()

      expect(userInfoPerformance.reset).toHaveBeenCalled()

      // Should not check performance after reset
      checkProgressiveLoadingPerformance()
      expect(consoleWarnSpy).not.toHaveBeenCalled()
    })
  })

  describe('singleton behavior', () => {
    it('should return the same instance', () => {
      const instance1 = ProgressiveLoadingMonitor.getInstance()
      const instance2 = ProgressiveLoadingMonitor.getInstance()

      expect(instance1).toBe(instance2)
    })
  })
})
