import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { WorkoutCardSkeleton } from '../WorkoutCardSkeleton'
import { ExerciseItemSkeleton } from '../ExerciseItemSkeleton'

describe('Skeletons Showcase', () => {
  describe('Workout Page Loading State', () => {
    it('should render complete workout page skeleton', () => {
      render(
        <div className="min-h-screen bg-gray-50">
          <div className="p-4">
            <div className="mx-auto max-w-lg">
              {/* Title skeleton */}
              <div className="mb-6 h-8 w-48 bg-gray-200 rounded animate-pulse" />

              {/* Workout info skeleton */}
              <div className="mb-6">
                <WorkoutCardSkeleton />
              </div>

              {/* Exercise list skeletons */}
              <div className="mb-8 space-y-3">
                <ExerciseItemSkeleton />
                <ExerciseItemSkeleton />
                <ExerciseItemSkeleton />
                <ExerciseItemSkeleton />
                <ExerciseItemSkeleton />
              </div>
            </div>
          </div>
        </div>
      )

      // Verify workout card skeleton
      expect(screen.getByTestId('workout-card-skeleton')).toBeInTheDocument()

      // Verify exercise item skeletons
      const exerciseSkeletons = screen.getAllByTestId('exercise-item-skeleton')
      expect(exerciseSkeletons).toHaveLength(5)
    })
  })

  describe('Progressive Loading Pattern', () => {
    it('should transition from skeleton to real content smoothly', () => {
      const { rerender } = render(
        <div className="space-y-3">
          <ExerciseItemSkeleton />
          <ExerciseItemSkeleton />
          <ExerciseItemSkeleton />
        </div>
      )

      // Verify skeletons are rendered
      expect(screen.getAllByTestId('exercise-item-skeleton')).toHaveLength(3)

      // Simulate data loading complete
      rerender(
        <div className="space-y-3">
          <div className="rounded-lg bg-white p-4 shadow-sm">
            <h3 className="font-medium text-gray-900">Bench Press</h3>
          </div>
          <div className="rounded-lg bg-white p-4 shadow-sm">
            <h3 className="font-medium text-gray-900">Squat</h3>
          </div>
          <div className="rounded-lg bg-white p-4 shadow-sm">
            <h3 className="font-medium text-gray-900">Deadlift</h3>
          </div>
        </div>
      )

      // Verify real content is rendered
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
      expect(screen.getByText('Squat')).toBeInTheDocument()
      expect(screen.getByText('Deadlift')).toBeInTheDocument()

      // Verify skeletons are gone
      expect(
        screen.queryByTestId('exercise-item-skeleton')
      ).not.toBeInTheDocument()
    })
  })

  describe('Accessibility Showcase', () => {
    it('should provide proper screen reader experience during loading', () => {
      render(
        <div>
          <h1>Today's Workout</h1>
          <WorkoutCardSkeleton />
          <div className="space-y-3" role="list">
            <ExerciseItemSkeleton />
            <ExerciseItemSkeleton />
            <ExerciseItemSkeleton />
          </div>
        </div>
      )

      // Check that all skeletons have proper accessibility attributes
      const workoutSkeleton = screen.getByTestId('workout-card-skeleton')
      expect(workoutSkeleton).toHaveAttribute('role', 'status')
      expect(workoutSkeleton).toHaveAttribute('aria-busy', 'true')
      expect(workoutSkeleton).toHaveAttribute(
        'aria-label',
        'Loading workout information'
      )

      const exerciseSkeletons = screen.getAllByTestId('exercise-item-skeleton')
      exerciseSkeletons.forEach((skeleton) => {
        expect(skeleton).toHaveAttribute('role', 'status')
        expect(skeleton).toHaveAttribute('aria-busy', 'true')
        expect(skeleton).toHaveAttribute('aria-label', 'Loading exercise')
      })
    })
  })

  describe('Layout Consistency', () => {
    it('should maintain consistent layout between skeleton and real content', () => {
      // Render with skeletons
      const { container: skeletonContainer } = render(
        <div className="p-4">
          <div className="mb-6">
            <WorkoutCardSkeleton />
          </div>
          <div className="space-y-3">
            <ExerciseItemSkeleton />
            <ExerciseItemSkeleton />
          </div>
        </div>
      )

      // Verify skeleton structure exists
      expect(skeletonContainer.querySelector('.mb-6')).toBeTruthy()

      // Render with real content
      const { container: contentContainer } = render(
        <div className="p-4">
          <div className="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-2 text-xl font-semibold">Upper Body Day</h2>
            <p className="text-gray-600">5 exercises</p>
          </div>
          <div className="space-y-3">
            <div className="rounded-lg bg-white p-4 shadow-sm">
              <h3 className="font-medium text-gray-900">Bench Press</h3>
            </div>
            <div className="rounded-lg bg-white p-4 shadow-sm">
              <h3 className="font-medium text-gray-900">Overhead Press</h3>
            </div>
          </div>
        </div>
      )

      // Both should have the same structure
      expect(skeletonContainer.querySelector('.mb-6')).toBeTruthy()
      expect(contentContainer.querySelector('.mb-6')).toBeTruthy()
      expect(skeletonContainer.querySelector('.space-y-3')).toBeTruthy()
      expect(contentContainer.querySelector('.space-y-3')).toBeTruthy()
    })
  })

  describe('Performance Optimization', () => {
    it('should render many skeletons efficiently', () => {
      const startTime = performance.now()

      render(
        <div className="space-y-3">
          {Array.from({ length: 20 }, (_, i) => (
            <ExerciseItemSkeleton key={i} />
          ))}
        </div>
      )

      const renderTime = performance.now() - startTime

      // Should render quickly (under 50ms for 20 items)
      expect(renderTime).toBeLessThan(50)

      // Verify all skeletons rendered
      expect(screen.getAllByTestId('exercise-item-skeleton')).toHaveLength(20)
    })
  })
})
