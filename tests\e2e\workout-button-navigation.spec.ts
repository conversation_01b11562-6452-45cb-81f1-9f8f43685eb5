import { test, expect } from '@playwright/test'

test.describe('Workout Button Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login')
    await page.fill(
      'input[type="email"]',
      process.env.TEST_EMAIL || '<EMAIL>'
    )
    await page.fill(
      'input[type="password"]',
      process.env.TEST_PASSWORD || 'Dr123456'
    )
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('/program')

    // Navigate to workout
    await page.click('text=Continue to workout')
    await page.waitForURL('/workout')
  })

  test('should show "Finish and save workout" button after saving sets and returning to workout page', async ({
    page,
  }) => {
    // Start workout
    await page.click('button:has-text("Start Workout")')

    // Wait for exercise page
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Log a set
    await page.click('button:has-text("Log Set")')
    await page.fill('input[placeholder="Weight"]', '50')
    await page.fill('input[placeholder="Reps"]', '10')
    await page.click('button:has-text("Save Set")')

    // Wait for modal to close
    await page.waitForSelector('text=Save Set', { state: 'hidden' })

    // Navigate back to workout page
    await page.goto('/workout')

    // Verify "Finish and save workout" button is visible
    const finishButton = page.locator(
      'button:has-text("Finish and save workout")'
    )
    await expect(finishButton).toBeVisible()
    await expect(finishButton).toBeEnabled()
  })

  test('should show "Continue Workout" button when workout is started but no sets completed', async ({
    page,
  }) => {
    // Start workout
    await page.click('button:has-text("Start Workout")')

    // Wait for exercise page
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Navigate back to workout page without saving any sets
    await page.goto('/workout')

    // Verify "Continue Workout" button is visible
    const continueButton = page.locator('button:has-text("Continue Workout")')
    await expect(continueButton).toBeVisible()

    // Verify "Finish and save workout" button is NOT visible
    const finishButton = page.locator(
      'button:has-text("Finish and save workout")'
    )
    await expect(finishButton).not.toBeVisible()
  })

  test('should maintain workout session state across page navigations', async ({
    page,
  }) => {
    // Start workout
    await page.click('button:has-text("Start Workout")')
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Log multiple sets
    // eslint-disable-next-line no-await-in-loop
    for (let i = 0; i < 2; i++) {
      // eslint-disable-next-line no-await-in-loop
      await page.click('button:has-text("Log Set")')
      // eslint-disable-next-line no-await-in-loop
      await page.fill('input[placeholder="Weight"]', `${50 + i * 5}`)
      // eslint-disable-next-line no-await-in-loop
      await page.fill('input[placeholder="Reps"]', `${10 - i}`)
      // eslint-disable-next-line no-await-in-loop
      await page.click('button:has-text("Save Set")')
      // eslint-disable-next-line no-await-in-loop
      await page.waitForSelector('text=Save Set', { state: 'hidden' })

      // Wait for next set or exercise
      // eslint-disable-next-line no-await-in-loop
      await page.waitForTimeout(500)
    }

    // Navigate back to workout page
    await page.goto('/workout')

    // Should see finish button
    await expect(
      page.locator('button:has-text("Finish and save workout")')
    ).toBeVisible()

    // Navigate to program page and back
    await page.goto('/program')
    await page.click('text=Continue to workout')
    await page.waitForURL('/workout')

    // Should still see finish button
    await expect(
      page.locator('button:has-text("Finish and save workout")')
    ).toBeVisible()
  })
})
