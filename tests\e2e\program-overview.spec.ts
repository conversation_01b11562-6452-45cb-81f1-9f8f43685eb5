import { test, expect } from '@playwright/test'
import { login, waitForNavigation, mockAPIResponse, waitForLoadingComplete, getPerformanceMetrics, checkAccessibility, measureAnimationFPS, testData, pullToRefresh } from './helpers'

test.describe('Program Overview Page E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses for consistent testing
    await mockAPIResponse(page, 'token', testData.loginSuccess)
    await mockAPIResponse(page, 'api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo', {
      UserWorkoutProgramModel: testData.program,
      UserProgramStats: testData.programStats,
      TotalWorkoutCompleted: 10,
      ConsecutiveWeeks: 2,
      LastWorkoutDateStr: testData.programStats.lastWorkoutDate
    })
    await mockAPIResponse(page, 'api/Workout/GetUserWorkoutTemplateGroup', {
      NextWorkoutTemplate: {
        Id: 1,
        Name: "Today's Workout",
        Exercises: Array(5).fill(null).map((_, i) => ({ Id: i + 1, Name: `Exercise ${i + 1}` }))
      }
    })
    
    // Start at login page
    await page.goto('/login')
  })

  test('should complete full journey: Login → Program → Workout', async ({ page }) => {
    // Login
    await login(page, '<EMAIL>', 'password123')
    
    // Should redirect to program overview after login
    await waitForNavigation(page, '/program')
    await waitForLoadingComplete(page)
    
    // Verify program overview page elements
    await expect(page.getByRole('heading', { level: 1 })).toBeVisible()
    await expect(page.getByTestId('animated-counter')).toBeVisible()
    await expect(page.getByTestId('program-stats-grid')).toBeVisible()
    
    // Check for Continue to Workout button
    const continueButton = page.getByRole('button', { name: /Continue to Workout/i })
    await expect(continueButton).toBeVisible()
    
    // Click Continue to Workout
    await continueButton.click()
    
    // Should navigate to workout page
    await waitForNavigation(page, '/workout')
    await expect(page.getByRole('heading', { name: /Today's Workout/i })).toBeVisible()
  })

  test('should display program data correctly', async ({ page }) => {
    await login(page, '<EMAIL>', 'password123')
    await waitForNavigation(page, '/program')
    await waitForLoadingComplete(page)
    
    // Check program header
    const programName = page.getByRole('heading', { level: 1 })
    await expect(programName).toHaveText('Beginner Full Body')
    
    // Check exercise count
    await expect(page.getByText('5 exercises')).toBeVisible()
    
    // Check animated counter
    const counter = page.getByTestId('animated-counter-value')
    await expect(counter).toBeVisible()
    
    // Wait for animation to complete
    await page.waitForTimeout(1200)
    
    // Counter should show 10 (TotalWorkoutCompleted)
    const counterText = await counter.textContent()
    expect(counterText).toBe('10')
    
    // Check stats grid (only 2 stats)
    const statCards = page.locator('[data-testid="stat-card"]')
    await expect(statCards).toHaveCount(2)
    
    // Check workout streak
    await expect(page.getByText('Workout Streak')).toBeVisible()
    await expect(page.getByText('2 weeks')).toBeVisible()
    
    // Check last workout
    await expect(page.getByText('Last Workout')).toBeVisible()
  })

  test('should handle navigation correctly', async ({ page }) => {
    await login(page, '<EMAIL>', 'password123')
    await waitForNavigation(page, '/program')
    await waitForLoadingComplete(page)
    
    // Navigate to workout
    const continueButton = page.getByRole('button', { name: /Continue to Workout/i })
    await continueButton.click()
    await waitForNavigation(page, '/workout')
    
    // Go back to program
    await page.goBack()
    await waitForNavigation(page, '/program')
    
    // Verify we're back on program page
    await expect(page.getByTestId('animated-counter')).toBeVisible()
  })

  test('should handle direct workout access', async ({ page }) => {
    await login(page, '<EMAIL>', 'password123')
    
    // Try to access workout directly
    await page.goto('/workout')
    
    // Should redirect to program first
    await waitForNavigation(page, '/program')
    await expect(page.getByTestId('animated-counter')).toBeVisible()
  })

  test('should meet performance targets', async ({ page }) => {
    await login(page, '<EMAIL>', 'password123')
    await waitForNavigation(page, '/program')
    
    // Wait for page to be interactive
    await page.waitForLoadState('networkidle')
    
    const metrics = await getPerformanceMetrics(page)
    
    // Page should load in < 1 second
    expect(metrics.firstContentfulPaint).toBeLessThan(1000)
    expect(metrics.domContentLoaded).toBeLessThan(1000)
  })

  test('should handle offline mode after first load', async ({ page, context }) => {
    await login(page, '<EMAIL>', 'password123')
    await waitForNavigation(page, '/program')
    
    // Wait for service worker to cache
    await page.waitForTimeout(2000)
    
    // Go offline
    await context.setOffline(true)
    
    // Reload page
    await page.reload()
    
    // Should still show cached data
    await expect(page.getByRole('heading', { level: 1 })).toBeVisible()
    await expect(page.getByTestId('animated-counter')).toBeVisible()
    
    // Go back online
    await context.setOffline(false)
  })

  test('should handle errors gracefully', async ({ page }) => {
    // Override mock with error response
    await page.unroute('**/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo')
    await mockAPIResponse(page, 'api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo', { error: 'Server Error' }, 500)
    
    await login(page, '<EMAIL>', 'password123')
    await waitForNavigation(page, '/program')
    
    // Should show error state
    await expect(page.getByText(/Error loading program|Something went wrong/i)).toBeVisible({ timeout: 10000 })
    await expect(page.getByRole('button', { name: /Try Again/i })).toBeVisible()
    
    // Remove error mock and restore success mock
    await page.unroute('**/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo')
    await mockAPIResponse(page, 'api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo', {
      UserWorkoutProgramModel: testData.program,
      UserProgramStats: testData.programStats,
      TotalWorkoutCompleted: 10,
      ConsecutiveWeeks: 2,
      LastWorkoutDateStr: testData.programStats.lastWorkoutDate
    })
    
    // Click retry
    await page.getByRole('button', { name: /Try Again/i }).click()
    
    // Should load successfully
    await expect(page.getByTestId('animated-counter')).toBeVisible({ timeout: 10000 })
  })

  test('should have smooth animations', async ({ page }) => {
    await login(page, '<EMAIL>', 'password123')
    await waitForNavigation(page, '/program')
    await waitForLoadingComplete(page)
    
    // Check for smooth counter animation
    const fps = await measureAnimationFPS(page, 1000)
    
    // Should have ~60fps (allowing for some variance)
    expect(fps).toBeGreaterThan(50)
  })

  test('should be accessible', async ({ page }) => {
    await login(page, '<EMAIL>', 'password123')
    await waitForNavigation(page, '/program')
    await waitForLoadingComplete(page)
    
    // Check for accessibility
    const a11y = await checkAccessibility(page)
    
    // Basic accessibility checks
    expect(a11y.hasReport).toBe(true)
    expect(a11y.headingCount).toBeGreaterThan(0)
    expect(a11y.buttonCount).toBeGreaterThan(0)
    
    // Check for proper heading hierarchy
    const h1 = await page.getByRole('heading', { level: 1 }).count()
    expect(h1).toBe(1)
    
    // Check for button with proper attributes
    const button = page.getByRole('button', { name: /Continue to Workout/i })
    await expect(button).toBeVisible()
    
    // Check for proper ARIA labels where needed
    const statCards = page.locator('[data-testid="stat-card"]')
    const cardCount = await statCards.count()
    expect(cardCount).toBe(2)
    
    for (let i = 0; i < cardCount; i++) {
      const card = statCards.nth(i)
      // Each stat card should have readable text
      const text = await card.textContent()
      expect(text).toBeTruthy()
    }
  })

  test('should handle pull-to-refresh', async ({ page, browserName }) => {
    // Only test on mobile browsers
    test.skip(browserName === 'chromium', 'Pull-to-refresh is mobile only')
    
    await login(page, '<EMAIL>', 'password123')
    await waitForNavigation(page, '/program')
    await waitForLoadingComplete(page)
    
    // Simulate pull-to-refresh gesture
    await pullToRefresh(page)
    
    // Should trigger refresh (check for loading state)
    // Note: This will pass once pull-to-refresh is implemented
    // For now, just verify the page is still functional
    await expect(page.getByTestId('animated-counter')).toBeVisible()
  })
})