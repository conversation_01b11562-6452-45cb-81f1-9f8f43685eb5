import { test, expect, Page } from '@playwright/test'

test.describe('API Error Handling - Workout Tests', () => {
  let page: Page
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p }) => {
    page = p
    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')
  })

  test('should handle 404 Not Found for workout data', async () => {
    await page.route('**/GetCustomerProgramInfo', (route) => {
      route.fulfill({
        status: 404,
        json: {
          error: 'not_found',
          error_description: 'No active program found for user',
        },
      })
    })

    await page.goto('/workout')

    await expect(page.locator('text=No active program found')).toBeVisible()
    await expect(
      page.locator('button:has-text("Start New Program")')
    ).toBeVisible()
  })

  test('should handle 500 Internal Server Error gracefully', async () => {
    await page.route('**/GetCustomerProgramInfo', (route) => {
      route.fulfill({
        status: 500,
        json: {
          error: 'internal_error',
          error_description: 'An unexpected error occurred',
        },
      })
    })

    await page.goto('/workout')

    await expect(page.locator('[role="alert"]')).toContainText(
      /unexpected error|something went wrong/i
    )
    await expect(page.locator('button:has-text("Retry")')).toBeVisible()
  })

  test('should handle timeout errors with retry', async () => {
    let attemptCount = 0

    await page.route('**/GetWorkoutInfo', (route) => {
      attemptCount++
      if (attemptCount === 1) {
        // First attempt times out
        route.abort('timedout')
      } else {
        // Second attempt succeeds
        route.fulfill({
          status: 200,
          json: {
            Result: {
              Id: 'workout-123',
              Name: 'Test Workout',
              Order: 1,
            },
          },
        })
      }
    })

    await page.goto('/workout')

    // Should show timeout error
    await expect(page.locator('text=Request timeout')).toBeVisible()

    // Click retry
    await page.click('button:has-text("Retry")')

    // Should eventually load
    await expect(page.locator('text=Test Workout')).toBeVisible()
  })

  test('should handle malformed API responses', async () => {
    await page.route('**/GetWorkoutInfo', (route) => {
      route.fulfill({
        status: 200,
        body: 'This is not JSON', // Invalid JSON response
        headers: {
          'content-type': 'text/plain',
        },
      })
    })

    await page.goto('/workout')

    await expect(page.locator('[role="alert"]')).toContainText(
      /invalid response|parsing error/i
    )
  })

  test('should handle validation errors when saving sets', async () => {
    await page.route('**/SaveWorkoutLog', (route) => {
      route.fulfill({
        status: 422,
        json: {
          error: 'validation_error',
          error_description: 'Invalid set data',
          details: {
            weight: 'Weight must be greater than 0',
            reps: 'Reps must be between 1 and 999',
          },
        },
      })
    })

    // Navigate to workout
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Try to save a set
    await page.click('[data-testid="exercise-item"]')
    await page.fill('input[name="weight"]', '-10') // Invalid weight
    await page.fill('input[name="reps"]', '0') // Invalid reps
    await page.click('button:has-text("Save")')

    await expect(page.locator('[role="alert"]')).toContainText(
      /weight must be greater than 0/i
    )
  })

  test('should handle concurrent modification errors', async () => {
    await page.route('**/SaveWorkoutLog', (route) => {
      route.fulfill({
        status: 409,
        json: {
          error: 'conflict',
          error_description:
            'This workout has been modified by another session',
        },
      })
    })

    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
    await page.click('[data-testid="exercise-item"]')
    await page.fill('input[name="weight"]', '100')
    await page.fill('input[name="reps"]', '10')
    await page.click('button:has-text("Save")')

    await expect(page.locator('[role="alert"]')).toContainText(
      /modified by another session/i
    )
    await expect(page.locator('button:has-text("Refresh")')).toBeVisible()
  })

  test('should handle quota exceeded errors', async () => {
    await page.route('**/SaveWorkoutLog', (route) => {
      route.fulfill({
        status: 429,
        json: {
          error: 'quota_exceeded',
          error_description: 'API quota exceeded. Please try again later.',
          retry_after: 3600,
        },
      })
    })

    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
    await page.click('[data-testid="exercise-item"]')
    await page.fill('input[name="weight"]', '100')
    await page.fill('input[name="reps"]', '10')
    await page.click('button:has-text("Save")')

    await expect(page.locator('[role="alert"]')).toContainText(
      /quota exceeded/i
    )
  })
})
