import { setAuthToken, hasAuthToken } from '@/api/client'

// Global state to prevent multiple simultaneous token restoration attempts
let isRestoring = false
let restorationPromise: Promise<boolean> | null = null

// Use sessionStorage to persist restoration state across page navigations
const RESTORATION_KEY = 'dr-muscle-token-restored'

function hasRestoredSuccessfully(): boolean {
  if (typeof window === 'undefined') return false
  return sessionStorage.getItem(RESTORATION_KEY) === 'true'
}

function markAsRestored(): void {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem(RESTORATION_KEY, 'true')
  }
}

function clearRestorationFlag(): void {
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem(RESTORATION_KEY)
  }
}

/**
 * Internal function that performs the actual token restoration
 */
async function performTokenRestore(): Promise<boolean> {
  try {
    // Only run on client side
    if (typeof window === 'undefined') {
      return false
    }

    // Only log in development to reduce console noise
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.info('[Auth] Starting token restoration...')
    }

    // Fetch the auth token from the server
    const response = await fetch('/api/auth/token', {
      credentials: 'include',
    })

    if (!response.ok) {
      // Only log in development to reduce console noise
      if (process.env.NODE_ENV === 'development') {
        console.warn('[Auth] Failed to retrieve auth token from server')
      }
      return false
    }

    const data = await response.json()

    if (data.authenticated && data.token) {
      // Set the Authorization header in the API client
      setAuthToken(data.token)
      // Only log success in development
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.info('[Auth] Auth token restored successfully')
      }
      return true
    }

    return false
  } catch (error) {
    // Only log in development to reduce console noise
    if (process.env.NODE_ENV === 'development') {
      console.error('[Auth] Error restoring auth token:', error)
    }
    return false
  }
}

/**
 * Attempts to restore the auth token from the server-side cookie
 * This is called on app initialization to set the Authorization header
 * if the user has a valid session
 *
 * Uses singleton pattern to prevent multiple simultaneous calls
 */
export async function restoreAuthToken(): Promise<boolean> {
  // If we already have an auth token in the API client, no need to restore
  if (hasAuthToken()) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log(
        '[Auth] Auth token already present in API client, skipping restoration'
      )
    }
    markAsRestored() // Mark as restored for future calls
    return true
  }

  // If already restored successfully, don't restore again
  if (hasRestoredSuccessfully()) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('[Auth] Token already restored successfully, skipping')
    }
    return true
  }

  // If already restoring, return the existing promise
  if (isRestoring && restorationPromise) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log(
        '[Auth] Token restoration already in progress, returning existing promise'
      )
    }
    return restorationPromise
  }

  // Mark as restoring and create new promise
  isRestoring = true
  restorationPromise = performTokenRestore()

  try {
    const result = await restorationPromise
    if (result) {
      markAsRestored()
    }
    return result
  } finally {
    // Reset state after completion
    isRestoring = false
    restorationPromise = null
  }
}

/**
 * Reset the token restoration state
 * This can be called to force a new restoration attempt
 */
export function resetTokenRestoration(): void {
  isRestoring = false
  restorationPromise = null
  clearRestorationFlag()
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('[Auth] Token restoration state reset')
  }
}
