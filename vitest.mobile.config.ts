import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    name: 'mobile',
    environment: 'happy-dom',
    setupFiles: ['./tests/setup/mobile.setup.ts'],
    globals: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      include: ['src/**/*.{ts,tsx}'],
      exclude: [
        'src/**/*.d.ts',
        'src/**/*.test.{ts,tsx}',
        'src/**/*.spec.{ts,tsx}',
      ],
      thresholds: {
        global: {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90,
        },
      },
    },
    // Mobile-specific test configuration
    testTimeout: 10000, // Longer timeout for mobile simulation
    include: [
      'tests/unit/**/*.mobile.test.{ts,tsx}',
      'tests/integration/**/*.mobile.test.{ts,tsx}',
      'src/**/*.mobile.test.{ts,tsx}',
    ],
    exclude: [
      'tests/e2e/**/*',
      'node_modules/**/*',
    ],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/tests': path.resolve(__dirname, './tests'),
    },
  },
  // Simulate mobile environment
  define: {
    'process.env.VITEST_MOBILE': 'true',
    'global.innerWidth': 375,
    'global.innerHeight': 667,
    'global.screen': {
      width: 375,
      height: 667,
      orientation: { type: 'portrait-primary' },
    },
    'global.navigator.userAgent': '"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"',
    'global.navigator.platform': '"iPhone"',
    'global.navigator.maxTouchPoints': 5,
  },
}) 