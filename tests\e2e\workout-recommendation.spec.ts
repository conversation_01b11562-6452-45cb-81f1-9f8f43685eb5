import { test, expect } from '@playwright/test'

test.describe('Workout Recommendation Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the login page before each test
    await page.goto('/login')

    // Perform login
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for successful login and navigation to complete.
    // Instead of a strict URL check, we'll just wait for the next page to load.
    await page.waitForNavigation({ waitUntil: 'networkidle', timeout: 15000 })
  })

  test('should display weight recommendations after clicking an exercise', async ({
    page,
  }) => {
    // Navigate to the workout page if not already there
    if (!page.url().includes('/workout')) {
      await page.goto('/workout')
    }

    // Wait for the workout overview container to be visible
    const workoutContainer = page.locator(
      '[data-testid="workout-overview-container"]'
    )
    await expect(workoutContainer).toBeVisible({ timeout: 20000 })

    // Find and click on the first exercise card
    const firstExerciseCard = page
      .locator('a[href*="/workout/exercise/"]')
      .first()
    await expect(firstExerciseCard).toBeVisible({ timeout: 10000 })
    await firstExerciseCard.click()

    // After clicking, we should be on an exercise page
    await expect(page).toHaveURL(/\/workout\/exercise\/\d+/, { timeout: 15000 })

    // Now, verify that the recommendation is displayed.
    // We'll look for a container that holds the set information.
    // Assuming a data-testid="set-info-container" exists on the SetScreen component
    const setInfoContainer = page
      .locator('[data-testid="set-info-container"]')
      .first()
    await expect(setInfoContainer).toBeVisible({ timeout: 10000 })

    // Check for "Sets", "Reps", and "Weight" labels and their values
    await expect(
      setInfoContainer.locator('p:has-text("Sets") + p')
    ).not.toBeEmpty({ timeout: 5000 })
    await expect(
      setInfoContainer.locator('p:has-text("Reps") + p')
    ).not.toBeEmpty({ timeout: 5000 })
    const weightValue = setInfoContainer.locator('p:has-text("Weight") + p')
    await expect(weightValue).not.toBeEmpty({ timeout: 5000 })

    // Ensure the weight is not 0
    const weightText = await weightValue.innerText()
    expect(parseFloat(weightText)).toBeGreaterThan(0)

    console.log(`✅ Recommendation displayed: ${weightText}`)
  })
})
