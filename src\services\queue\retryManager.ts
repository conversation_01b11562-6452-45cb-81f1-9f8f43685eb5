/**
 * Retry management utilities
 */
export class RetryManager {
  /**
   * Calculate backoff delay with jitter
   */
  static calculateBackoff(retryCount: number, baseDelay: number): number {
    const maxDelay = 30000 // 30 seconds
    const exponentialDelay = Math.min(
      baseDelay * Math.pow(2, retryCount - 1),
      maxDelay
    )

    // Add jitter (±20%)
    const jitter = exponentialDelay * 0.2 * (Math.random() * 2 - 1)

    return Math.floor(exponentialDelay + jitter)
  }

  /**
   * Check if an error is retryable
   */
  static isRetryableError(error: unknown): boolean {
    // Network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return true
    }

    // Check if error is an object with status property
    if (
      error &&
      typeof error === 'object' &&
      'status' in error &&
      typeof error.status === 'number'
    ) {
      // HTTP errors (retry on 5xx and 429)
      if (error.status >= 500 || error.status === 429) {
        return true
      }
    }

    // Check if error has a message property
    if (error instanceof Error) {
      // Specific error messages
      if (
        error.message.includes('network') ||
        error.message.includes('timeout') ||
        error.message.includes('ECONNRESET')
      ) {
        return true
      }
    }

    return false
  }

  /**
   * Wrap a promise with abort capability
   */
  static withAbort<T>(promise: Promise<T>, signal: AbortSignal): Promise<T> {
    return new Promise((resolve, reject) => {
      // Handle abort
      const abortHandler = () => {
        reject(new Error('Request aborted'))
      }

      if (signal.aborted) {
        abortHandler()
        return
      }

      signal.addEventListener('abort', abortHandler)

      // Handle promise
      promise
        .then((result) => {
          signal.removeEventListener('abort', abortHandler)
          resolve(result)
        })
        .catch((error) => {
          signal.removeEventListener('abort', abortHandler)
          reject(error)
        })
    })
  }
}
