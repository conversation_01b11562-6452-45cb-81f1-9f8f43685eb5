import { categorizeError } from './errorCategorizer'

export interface RetryOptions {
  maxRetries?: number
  initialDelay?: number
  maxDelay?: number
  backoffFactor?: number
}

/**
 * Retry an operation with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  isOffline: boolean,
  retryOptions: RetryOptions = {}
): Promise<T> {
  const {
    maxRetries = 3,
    initialDelay = 1000,
    maxDelay = 30000,
    backoffFactor = 2,
  } = retryOptions

  let lastError: Error

  // Use traditional for loop instead of for...of
  for (let attempt = 0; attempt <= maxRetries; attempt += 1) {
    try {
      // eslint-disable-next-line no-await-in-loop
      return await operation()
    } catch (error) {
      lastError = error as Error

      // Check if error is retryable
      const errorState = categorizeError(lastError, { isOffline })
      if (!errorState.canRetry) {
        throw lastError
      }

      // If this is the last attempt, throw
      if (attempt === maxRetries) {
        throw lastError
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(
        initialDelay * Math.pow(backoffFactor, attempt),
        maxDelay
      )

      // Wait before retrying
      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve) => {
        setTimeout(resolve, delay)
      })
    }
  }

  throw lastError!
}
