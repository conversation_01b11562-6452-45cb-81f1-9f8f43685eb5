import { workoutApi } from '@/api/workouts'
import type { WorkoutLogSerieModel } from '@/types'
import { logger } from '@/utils/logger'
import type { FailedRequest } from './syncQueue'

/**
 * Process individual sync requests
 */
export class SyncRequestProcessor {
  /**
   * Process a single request based on its type
   */
  static async processRequest(request: FailedRequest): Promise<void> {
    switch (request.type) {
      case 'saveSet':
        await workoutApi.saveWorkoutSet(request.data as WorkoutLogSerieModel)
        logger.log(
          '[SyncProcessor] Successfully processed request:',
          request.id
        )
        break
      // Add other request types as needed
      default:
        logger.warn('[SyncProcessor] Unknown request type:', request.type)
    }
  }

  /**
   * Process a batch of requests and return successful IDs
   */
  static async processBatch(
    requests: FailedRequest[]
  ): Promise<{ successfulIds: string[]; failedRequests: FailedRequest[] }> {
    const successfulIds: string[] = []
    const updatedRequests = [...requests]

    for (let i = 0; i < requests.length; i++) {
      const request = requests[i]
      if (!request) {
        successfulIds.push(`null-${i}`) // Track null entries for removal
        // eslint-disable-next-line no-continue
        continue
      }

      try {
        // eslint-disable-next-line no-await-in-loop
        await this.processRequest(request)
        successfulIds.push(request.id || `legacy-${i}`)
      } catch (error) {
        // Failed to process request
        logger.error(
          '[SyncProcessor] Failed to process request:',
          request.id,
          error
        )
        // Update error message but keep in queue
        const failedRequest = updatedRequests[i]
        if (failedRequest) {
          failedRequest.error =
            error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }

    // Remove successful requests by ID
    const remainingRequests = updatedRequests.filter((request, index) => {
      if (!request) return !successfulIds.includes(`null-${index}`)
      const requestId = request.id || `legacy-${index}`
      return !successfulIds.includes(requestId)
    })

    return { successfulIds, failedRequests: remainingRequests }
  }
}
