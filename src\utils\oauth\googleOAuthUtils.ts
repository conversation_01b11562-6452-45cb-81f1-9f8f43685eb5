/**
 * Google OAuth Utility Functions for Dr. Muscle X
 */

import type { GoogleTokenPayload } from '@/types/googleOAuthTypes'
import { GoogleOAuthConfig } from './googleOAuthConfig'

/**
 * Decode a Google JWT token
 * @param token - JWT token string
 * @returns Decoded token payload or null if invalid
 */
export function decodeGoogleJWT(token: string): GoogleTokenPayload | null {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format')
    }

    // Decode base64url
    const base64Url = parts[1]!
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
        .join('')
    )

    const payload = JSON.parse(jsonPayload) as GoogleTokenPayload

    // Validate required fields
    if (!payload.sub || !payload.email || !payload.iss || !payload.aud) {
      throw new Error('Invalid token payload')
    }

    // Validate issuer
    if (
      payload.iss !== 'https://accounts.google.com' &&
      payload.iss !== 'accounts.google.com'
    ) {
      throw new Error('Invalid token issuer')
    }

    // Validate audience (client ID)
    if (payload.aud !== GoogleOAuthConfig.GOOGLE_CLIENT_ID) {
      throw new Error('Invalid token audience')
    }

    // Check expiration
    const now = Math.floor(Date.now() / 1000)
    if (payload.exp < now) {
      throw new Error('Token expired')
    }

    return payload
  } catch (error) {
    // Token decode failed
    return null
  }
}

/**
 * Get user info from Google access token
 * @param accessToken - Google access token
 * @returns User info or null
 */
export async function getGoogleUserInfo(accessToken: string): Promise<{
  id: string
  email: string
  verified_email: boolean
  name?: string
  given_name?: string
  family_name?: string
  picture?: string
} | null> {
  try {
    const response = await fetch(
      `https://www.googleapis.com/oauth2/v1/userinfo?access_token=${accessToken}`
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    // User info fetch failed
    return null
  }
}

/**
 * Check if Google Sign-In SDK is available
 */
export function isGoogleSignInAvailable(): boolean {
  return typeof window !== 'undefined' && !!window.google?.accounts?.id
}

/**
 * Cancel Google One Tap flow
 */
export function cancelGoogleOneTap(): void {
  if (window.google?.accounts?.id) {
    window.google.accounts.id.cancel()
  }
}

/**
 * Disable auto-select for Google One Tap
 */
export function disableGoogleAutoSelect(): void {
  if (window.google?.accounts?.id) {
    window.google.accounts.id.disableAutoSelect()
  }
}

/**
 * Revoke Google authorization
 * @param email - User email to revoke
 * @param callback - Optional callback after revocation
 */
export function revokeGoogleAuth(email: string, callback?: () => void): void {
  if (window.google?.accounts?.id) {
    window.google.accounts.id.revoke(email, callback)
  }
}

/**
 * Generate a secure nonce for OAuth flow
 * @returns Base64 encoded nonce
 */
export function generateNonce(): string {
  const array = new Uint8Array(32)
  crypto.getRandomValues(array)
  return btoa(String.fromCharCode(...array))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '')
}

/**
 * Create a retry wrapper for OAuth operations
 * @param operation - The operation to retry
 * @param config - Retry configuration
 * @returns Promise that resolves with the operation result
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  config = GoogleOAuthConfig.RETRY_CONFIG
): Promise<T> {
  let lastError: Error
  let currentDelay: number = config.initialDelay

  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      // eslint-disable-next-line no-await-in-loop
      return await operation()
    } catch (error) {
      lastError = error as Error

      if (attempt === config.maxAttempts) {
        throw lastError
      }

      // Wait before next attempt
      const delayToUse = currentDelay
      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve) => setTimeout(resolve, delayToUse))

      // Increase delay for next attempt
      currentDelay = Math.min(
        currentDelay * config.backoffMultiplier,
        config.maxDelay
      )
    }
  }

  throw lastError!
}
