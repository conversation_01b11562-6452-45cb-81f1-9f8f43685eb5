'use client'

import React from 'react'
import { ChevronLeftIcon } from '@/components/icons'

interface IOSNavigationBarProps {
  title: string
  showBackButton?: boolean
  onBackClick?: () => void
  leftElement?: React.ReactNode
  rightElement?: React.ReactNode
  className?: string
}

export function IOSNavigationBar({
  title,
  showBackButton = false,
  onBackClick,
  leftElement,
  rightElement,
  className = '',
}: IOSNavigationBarProps) {
  return (
    <>
      <header
        className={`fixed top-0 left-0 right-0 z-50 backdrop-blur-md bg-bg-primary/80 border-b border-brand-primary/10 ${className}`}
      >
        <div className="flex items-center justify-between h-11 px-4">
          {/* Left Section */}
          <div className="flex-1 flex items-center">
            {leftElement ||
              (showBackButton && (
                <button
                  onClick={() => {
                    // Add haptic feedback
                    if ('vibrate' in navigator) {
                      navigator.vibrate(10)
                    }
                    onBackClick?.()
                  }}
                  className="flex items-center -ml-2 px-2 py-2 rounded-lg hover:bg-bg-secondary transition-colors"
                  aria-label="Go back"
                >
                  <ChevronLeftIcon size={24} className="text-brand-primary" />
                </button>
              ))}
          </div>

          {/* Center Section - Title */}
          <div className="flex-1 flex items-center justify-center">
            <h1 className="text-base font-semibold text-text-primary truncate">
              {title}
            </h1>
          </div>

          {/* Right Section */}
          <div className="flex-1 flex items-center justify-end">
            {rightElement}
          </div>
        </div>
      </header>

      {/* Spacer to prevent content from going under fixed header */}
      <div className="h-11" />
    </>
  )
}
