/**
 * Workout API - Write Operations
 *
 * Functions for creating and updating workout data
 */

import { apiClient } from '../client'
import { AxiosError } from 'axios'
import { logger } from '@/utils/logger'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import type { WorkoutLogSerieModel, BooleanModel } from '@/types'
import type {
  WorkoutCompletionData,
  WorkoutCompletionResponse,
  ExerciseSwapRequest,
} from './types'

/**
 * Save a workout set (reps, weight, RIR)
 * @param setData - The set data to save
 * @returns Success response
 */
export async function saveWorkoutSet(
  setData: WorkoutLogSerieModel
): Promise<BooleanModel> {
  try {
    const username = getCurrentUserEmail()
    if (!username) {
      throw new Error('User not authenticated')
    }

    // Ensure UserId is set
    const payload = {
      ...setData,
      UserId: setData.UserId || username,
      Username: username,
    }

    logger.log('Saving workout set:', payload)

    const response = await apiClient.post('/api/WorkoutLog/SaveSet', payload)

    // Handle wrapped response
    const result = response.data.Result ?? response.data
    logger.log('Save set response:', result)

    return {
      Result: result === true || result?.Result === true,
      Code: response.data.Code || 200,
      ErrorMessage: response.data.ErrorMessage,
    }
  } catch (error) {
    logger.error('Failed to save workout set:', error)

    // Return error as BooleanModel
    if (error instanceof AxiosError) {
      return {
        Result: false,
        Code: error.response?.status || 500,
        ErrorMessage:
          error.response?.data?.ErrorMessage ||
          error.message ||
          'Failed to save set',
      }
    }

    return {
      Result: false,
      Code: 500,
      ErrorMessage: 'Failed to save set',
    }
  }
}

/**
 * Complete a workout session
 * @param workoutData - Data about the completed workout
 * @returns Completion response with next workout date
 */
export async function completeWorkout(
  workoutData: WorkoutCompletionData
): Promise<WorkoutCompletionResponse> {
  try {
    logger.log('Completing workout with data:', workoutData)

    // Use SaveWorkoutV3Pro endpoint that mobile app uses
    const response = await apiClient.post('/api/Workout/SaveWorkoutV3Pro', {
      WorkoutId: workoutData.WorkoutId,
      WorkoutTemplateId: parseInt(workoutData.WorkoutId, 10), // API expects number
    })

    const { data } = response

    return {
      Result: data.Result || data.StatusCode === 200 || false,
      Code: data.Code || data.StatusCode || 200,
      ErrorMessage: data.ErrorMessage,
      NextWorkoutDate: data.NextWorkoutDate,
    }
  } catch (error) {
    logger.error('Failed to complete workout:', error)
    throw error
  }
}

/**
 * Skip a workout with optional reason
 * @param reason - Optional reason for skipping
 * @returns Success response
 */
export async function skipWorkout(reason?: string): Promise<BooleanModel> {
  const response = await apiClient.post('/api/Workout/SkipWorkout', {
    Reason: reason || 'User skipped',
  })

  const { data } = response
  return {
    Result: data.Result || false,
    Code: data.Code || 200,
    ErrorMessage: data.ErrorMessage,
  }
}

/**
 * Swap one exercise for another in the current workout
 * @param currentExerciseId - ID of the exercise to replace
 * @param newExerciseId - ID of the new exercise
 * @returns Success response
 */
export async function swapExercise(
  currentExerciseId: number,
  newExerciseId: number
): Promise<BooleanModel> {
  const swapRequest: ExerciseSwapRequest = {
    CurrentExerciseId: currentExerciseId,
    NewExerciseId: newExerciseId,
  }

  const response = await apiClient.post(
    '/api/Exercise/SwapExercise',
    swapRequest
  )

  const { data } = response
  return {
    Result: data.Result || false,
    Code: data.Code || 200,
    ErrorMessage: data.ErrorMessage,
  }
}
