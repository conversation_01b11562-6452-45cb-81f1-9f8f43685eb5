import React from 'react'

interface WorkoutCardSkeletonProps {
  className?: string
}

export function WorkoutCardSkeleton({
  className = '',
}: WorkoutCardSkeletonProps) {
  return (
    <div
      data-testid="workout-card-skeleton"
      role="status"
      aria-busy="true"
      aria-label="Loading workout information"
      className={`rounded-lg bg-white p-6 shadow-sm animate-pulse ${className}`}
    >
      {/* Title skeleton */}
      <div
        data-testid="workout-card-title-skeleton"
        className="h-7 w-3/4 bg-gray-200 rounded mb-2"
      />

      {/* Subtitle skeleton */}
      <div
        data-testid="workout-card-subtitle-skeleton"
        className="h-5 w-1/2 bg-gray-200 rounded"
      />
    </div>
  )
}
