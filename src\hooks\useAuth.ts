import { useMutation } from '@tanstack/react-query'
import { useAuthStore } from '@/stores/authStore'
import { useProgramStore } from '@/stores/programStore'
import { authApi } from '@/api/auth'
import { userProfileApi } from '@/api/userProfile'
import { programApi } from '@/api/program'
import { startSession, endSession } from '@/utils/userInfoPerformance'
import type { LoginModel, LoginFormData } from '@/types'

/**
 * Custom hook for authentication operations
 * Integrates auth store with React Query for optimized state management
 */
export function useAuth() {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    setAuth,
    logout: storeLogout,
    setError,
    clearError,
  } = useAuthStore()

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginModel) => {
      clearError()
      return {
        data: await authApi.login(credentials),
        originalEmail: credentials.Username,
      }
    },
    onSuccess: async ({ data, originalEmail }) => {
      // Set auth immediately so userName (email) is available
      // Pass the original email in case the API response doesn't include userName
      setAuth(data, originalEmail)

      // Start fetching UserInfo immediately during the 300ms success screen
      // This runs in parallel with program data prefetch
      const prefetchUserInfo = async () => {
        try {
          // Start performance tracking
          const sessionId = startSession()

          // Fetch user info (with retry logic built in)
          const userInfo = await userProfileApi.getUserInfo().catch((error) => {
            console.warn('[Login] Failed to prefetch user info:', error.message)
            endSession(sessionId, false)
            return null
          })

          if (userInfo) {
            // Update auth store with user info
            const { updateUser, setCachedUserInfo } = useAuthStore.getState()

            // Handle both wrapped and direct response formats
            const userData = userInfo.Result || userInfo

            if (userData.FirstName || userData.LastName) {
              // Update user in auth store
              updateUser({
                firstName: userData.FirstName,
                lastName: userData.LastName,
              })

              // Cache the full user info
              setCachedUserInfo({
                firstName: userData.FirstName,
                lastName: userData.LastName,
                ...userData,
              })
            }

            endSession(sessionId, true)
          }
        } catch (error) {
          console.error('[Login] Error during user info prefetch:', error)
        }
      }

      // Start fetching program data immediately and cache it
      const { setCachedProgram, setCachedProgress, setCachedStats } =
        useProgramStore.getState()

      // Prefetch program data in background with staggered requests
      // Add small delays to avoid overwhelming the server
      const prefetchProgramData = async () => {
        try {
          // Fetch program first (most important)
          const program = await programApi.getUserProgram().catch((error) => {
            console.warn('Failed to prefetch program:', error.message)
            return null
          })
          if (program) setCachedProgram(program)

          // Small delay before next request
          await new Promise<void>((resolve) => {
            setTimeout(resolve, 500)
          })

          // Fetch progress
          const progress = await programApi
            .getProgramProgress()
            .catch((error) => {
              console.warn('Failed to prefetch progress:', error.message)
              return null
            })
          if (progress) setCachedProgress(progress)

          // Small delay before next request
          await new Promise<void>((resolve) => {
            setTimeout(resolve, 500)
          })

          // Fetch stats
          const stats = await programApi.getProgramStats().catch((error) => {
            console.warn('Failed to prefetch stats:', error.message)
            return null
          })
          if (stats) setCachedStats(stats)
        } catch (error) {
          // Log but don't throw - these are non-critical background fetches
          console.error('Error during program data prefetch:', error)
        }
      }

      // Start all prefetches in background (don't await)
      // They run in parallel during the 300ms success screen
      prefetchUserInfo()
      prefetchProgramData()
    },
    onError: (error: Error) => {
      // Only log in development to reduce console noise
      if (process.env.NODE_ENV === 'development') {
        console.error('Login error:', error.message)
      }

      // Check if it's a network error from our interceptor
      if (error.name === 'NetworkError') {
        setError(error.message)
      } else if (error.message) {
        setError(error.message)
      } else {
        setError('Login failed. Please try again.')
      }
    },
  })

  // Logout mutation - no API call needed, just local cleanup
  const logoutMutation = useMutation({
    mutationFn: async () => {
      // No API call - Dr. Muscle backend doesn't have a logout endpoint
      // Just perform local logout
      return Promise.resolve()
    },
    onSuccess: () => {
      storeLogout()
    },
  })

  // Helper function to login with form data
  const login = async (formData: LoginModel | LoginFormData) => {
    // Handle both LoginModel and LoginFormData formats
    const credentials: LoginModel =
      'Username' in formData
        ? (formData as LoginModel)
        : {
            Username: (formData as LoginFormData).email,
            Password: (formData as LoginFormData).password,
          }

    return loginMutation.mutateAsync(credentials)
  }

  // Helper function to logout
  const logout = () => {
    logoutMutation.mutate()
  }

  return {
    // State
    user,
    token,
    isAuthenticated,
    isLoading: loginMutation.isPending || logoutMutation.isPending || isLoading,
    error,

    // Actions
    login,
    logout,
    clearError,

    // Mutations (for direct access if needed)
    loginMutation,
    logoutMutation,
  }
}
