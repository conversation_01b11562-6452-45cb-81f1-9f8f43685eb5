import { render } from '@testing-library/react'
import { WeightLiftedIcon } from '../WeightLiftedIcon'

describe('WeightLiftedIcon', () => {
  it('renders without errors', () => {
    const { container } = render(<WeightLiftedIcon />)
    const svg = container.querySelector('svg')
    expect(svg).toBeInTheDocument()
  })

  it('applies custom className', () => {
    const { container } = render(
      <WeightLiftedIcon className="text-green-500" />
    )
    const svg = container.querySelector('svg')
    expect(svg).toHaveClass('text-green-500')
  })

  it('renders with default size', () => {
    const { container } = render(<WeightLiftedIcon />)
    const svg = container.querySelector('svg')
    expect(svg).toHaveAttribute('width', '24')
    expect(svg).toHaveAttribute('height', '24')
  })

  it('renders with custom size', () => {
    const { container } = render(<WeightLiftedIcon size={28} />)
    const svg = container.querySelector('svg')
    expect(svg).toHaveAttribute('width', '28')
    expect(svg).toHaveAttribute('height', '28')
  })

  it('maintains SVG structure', () => {
    const { container } = render(<WeightLiftedIcon />)
    const paths = container.querySelectorAll('path')
    expect(paths).toHaveLength(1)
  })
})
