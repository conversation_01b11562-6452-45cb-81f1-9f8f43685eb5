import { test, expect } from '@playwright/test'

// Helper function for login and navigation to workout
const loginAndWaitForWorkout = async (
  page: import('@playwright/test').Page
) => {
  // Navigate to login page if not already there
  if (!page.url().includes('/login')) {
    await page.goto('/login')
  }

  // Fill login form
  await page.fill(
    'input[name="email"]',
    process.env.TEST_USER_EMAIL || '<EMAIL>'
  )
  await page.fill(
    'input[name="password"]',
    process.env.TEST_USER_PASSWORD || 'pass'
  )

  // Submit login
  await page.click('button[type="submit"]')

  // Wait for navigation to workout page
  await page.waitForURL('/workout', { timeout: 10000 })

  // Verify workout page is loaded
  await expect(page.getByTestId('workout-overview')).toBeVisible({
    timeout: 5000,
  })
}

test.describe('Performance Optimization - User Journey', () => {
  test.beforeEach(async ({ page }) => {
    // Start at login page
    await page.goto('/login')
  })

  test('should complete login to workout journey in under 3 seconds', async ({
    page,
  }) => {
    const startTime = Date.now()

    // Fill login form
    await page.fill(
      'input[name="email"]',
      process.env.TEST_USER_EMAIL || '<EMAIL>'
    )
    await page.fill(
      'input[name="password"]',
      process.env.TEST_USER_PASSWORD || 'pass'
    )

    // Start measuring performance
    await page.evaluate(() => {
      performance.mark('test-login-start')
    })

    // Submit login
    await page.click('button[type="submit"]')

    // Wait for success screen
    await expect(page.getByTestId('login-success-screen')).toBeVisible({
      timeout: 3000,
    })

    // Verify success screen shows immediately with animations
    await expect(page.getByTestId('success-icon')).toBeVisible()
    await expect(page.getByText('Welcome')).toBeVisible()

    // Check loading progress is visible
    await expect(page.getByTestId('loading-progress')).toBeVisible()

    // Wait for auto-navigation to workout page
    await page.waitForURL('/workout', { timeout: 5000 })

    // Verify workout page is interactive
    await expect(page.getByText(/workout/i)).toBeVisible()

    // Mark completion
    await page.evaluate(() => {
      performance.mark('test-workout-interactive')
    })

    const endTime = Date.now()
    const totalTime = endTime - startTime

    // Total journey should be under 5 seconds
    expect(totalTime).toBeLessThan(5000)

    // Get performance metrics
    const metrics = await page.evaluate(() => {
      const loginStart = performance.getEntriesByName('test-login-start')[0]
      const workoutInteractive = performance.getEntriesByName(
        'test-workout-interactive'
      )[0]

      if (loginStart && workoutInteractive) {
        return {
          loginToInteractive:
            workoutInteractive.startTime - loginStart.startTime,
        }
      }
      return null
    })

    // Perceived loading should be under 3 seconds
    if (metrics) {
      expect(metrics.loginToInteractive).toBeLessThan(3000)
    }
  })

  test('should show cached data on return visit', async ({ page }) => {
    // First visit - login and load workout
    await loginAndWaitForWorkout(page)

    // Verify workout data is loaded
    await expect(page.getByTestId('workout-overview')).toBeVisible()
    const workoutTitle = await page.getByTestId('workout-title').textContent()

    // Reload page to simulate return visit
    await page.reload()

    // Should show cached data immediately (no loading state)
    await expect(page.getByTestId('workout-overview')).toBeVisible({
      timeout: 500,
    })

    // Verify same workout data is shown
    const cachedWorkoutTitle = await page
      .getByTestId('workout-title')
      .textContent()
    expect(cachedWorkoutTitle).toBe(workoutTitle)
  })

  test('should handle offline gracefully', async ({ page, context }) => {
    // Login first
    await loginAndWaitForWorkout(page)

    // Go offline
    await context.setOffline(true)

    // Try to navigate - should still work with cached data
    await page.goto('/workout')

    // Should show workout from cache
    await expect(page.getByTestId('workout-overview')).toBeVisible()

    // Should show offline indicator
    await expect(page.getByText(/offline/i)).toBeVisible()

    // Go back online
    await context.setOffline(false)

    // Offline indicator should disappear
    await expect(page.getByText(/offline/i)).not.toBeVisible({ timeout: 5000 })
  })

  test('should have smooth animations and transitions', async ({ page }) => {
    // Login
    await loginAndWaitForWorkout(page)

    // Check for animation classes on success screen
    const hasAnimations = await page.evaluate(() => {
      const successIcon = document.querySelector('[data-testid="success-icon"]')
      const successMessage = document.querySelector(
        '[data-testid="success-message"]'
      )

      return {
        iconHasAnimation: successIcon?.classList.contains(
          'animate-scale-bounce'
        ),
        messageHasAnimation:
          successMessage?.querySelector('.animate-slide-up') !== null,
      }
    })

    expect(hasAnimations.iconHasAnimation).toBe(true)
    expect(hasAnimations.messageHasAnimation).toBe(true)

    // Check for smooth progress bar animation
    const progressBar = page.getByTestId('loading-progress-fill')
    const initialWidth = await progressBar.evaluate((el) => el.style.width)

    // Wait a bit and check if width changed smoothly
    await page.waitForTimeout(500)
    const updatedWidth = await progressBar.evaluate((el) => el.style.width)

    expect(parseInt(updatedWidth)).toBeGreaterThan(parseInt(initialWidth))
  })

  test('should be accessible with keyboard navigation', async ({ page }) => {
    // Navigate to login with Tab
    await page.keyboard.press('Tab') // Skip any skip links
    await page.keyboard.press('Tab') // Focus email input

    // Verify email input is focused
    const emailFocused = await page.evaluate(() => {
      return document.activeElement?.getAttribute('name') === 'email'
    })
    expect(emailFocused).toBe(true)

    // Fill form with keyboard
    await page.keyboard.type('<EMAIL>')
    await page.keyboard.press('Tab') // Move to password
    await page.keyboard.type('pass')
    await page.keyboard.press('Tab') // Move to submit button
    await page.keyboard.press('Enter') // Submit

    // Should navigate to success screen
    await expect(page.getByTestId('login-success-screen')).toBeVisible()

    // Check skip button is keyboard accessible
    await page.keyboard.press('Tab')
    const skipButtonFocused = await page.evaluate(() => {
      return document.activeElement?.textContent?.includes('Skip')
    })

    if (skipButtonFocused) {
      await page.keyboard.press('Enter')
      // Should navigate to workout
      await page.waitForURL('/workout')
    }
  })

  test('should meet Core Web Vitals targets', async ({ page }) => {
    // Enable performance observer
    await page.evaluateOnNewDocument(() => {
      window.webVitals = {
        LCP: null,
        FID: null,
        CLS: 0,
      }

      // Observe LCP
      new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        window.webVitals.LCP = lastEntry.startTime
      }).observe({ entryTypes: ['largest-contentful-paint'] })

      // Observe CLS
      new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (!entry.hadRecentInput) {
            window.webVitals.CLS += entry.value
          }
        })
      }).observe({ entryTypes: ['layout-shift'] })
    })

    // Navigate through the app
    await loginAndWaitForWorkout(page)

    // Get Web Vitals
    const vitals = await page.evaluate(() => window.webVitals)

    // LCP should be under 2.5s (good)
    if (vitals.LCP !== null) {
      expect(vitals.LCP).toBeLessThan(2500)
    }

    // CLS should be under 0.1 (good)
    expect(vitals.CLS).toBeLessThan(0.1)
  })

  test('should handle errors gracefully', async ({ page, context }) => {
    // Intercept API calls to simulate errors
    await context.route('**/api/**', (route) => {
      route.abort('failed')
    })

    // Try to login
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'password')
    await page.click('button[type="submit"]')

    // Should show error message, not crash
    await expect(page.getByText(/error|failed|try again/i)).toBeVisible({
      timeout: 5000,
    })

    // App should still be responsive
    await expect(page.getByRole('button', { name: /log in/i })).toBeEnabled()
  })
})
