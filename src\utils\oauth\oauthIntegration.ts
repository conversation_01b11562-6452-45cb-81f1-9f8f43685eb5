/**
 * OAuth Integration Layer for Dr. Muscle X
 *
 * Bridges OAuth providers (Google and Apple) with Dr. Muscle authentication.
 * Provides unified interface, error handling, and performance monitoring.
 */

import type {
  OAuthProvider,
  OAuthUserData,
  OAuthSuccessCallback,
  OAuthErrorCallback,
  OAuthConfig,
} from '../../types/oauth'
import type { LoginSuccessResult } from '../../types'
import { logger } from '../logger'
import { oauthApi } from '../../api/oauth'
import { useAuthStore } from '../../stores/authStore'
import { OAuthErrorHandler } from './errorHandler'
import { OAuthPerformanceTracker } from './performanceTracker'
import { OAuthValidator } from './validator'
import { OAuthProviderHandlers } from './providerHandlers'

/**
 * OAuth Integration class
 * Provides unified operations for OAuth providers
 */
export class OAuthIntegration {
  /**
   * Initialize OAuth integration with configuration
   */
  static initialize(config: OAuthConfig): void {
    logger.info('OAuth Integration initialized', {
      providers: Object.keys(config),
      hasGoogleConfig: !!config.google,
      hasAppleConfig: !!config.apple,
    })

    // Validate configuration
    const validation = OAuthValidator.validateConfig(
      config as Record<string, unknown>
    )
    if (!validation.isValid) {
      logger.warn('OAuth configuration warnings', validation.warnings)
    }
  }

  /**
   * Handle successful OAuth authentication
   * @param userData - User data from OAuth provider
   * @param provider - OAuth provider type
   * @returns Login success result from backend
   */
  static async handleOAuthSuccess(
    userData: OAuthUserData,
    provider: OAuthProvider
  ): Promise<LoginSuccessResult> {
    try {
      const startMark = OAuthPerformanceTracker.markStart(provider, 'process')

      // Validate user data
      OAuthValidator.validateUserData(userData)

      // Log success event with sanitized data
      const sanitizedData = OAuthValidator.sanitizeUserData(userData)
      logger.info(`OAuth ${provider} success`, {
        email: sanitizedData.email,
        emailVerified: sanitizedData.emailVerified,
        hasName: !!sanitizedData.name,
        hasProfilePicture: !!sanitizedData.pictureUrl,
      })

      // Call backend API based on provider
      let loginResult: LoginSuccessResult

      if (provider === 'google') {
        // Extract first name from full name (mobile app sends first word only)
        const name = userData.name?.split(' ')[0] || ''

        loginResult = await oauthApi.googleLogin(userData.rawToken, {
          email: userData.email,
          name,
          bodyWeight: '', // Can be empty for OAuth
          massUnit: '', // Can be empty for OAuth
        })
      } else if (provider === 'apple') {
        // For Apple, we need the user ID (not the authorization code)
        const appleUserId = userData.providerId
        if (!appleUserId) {
          throw new Error('Apple User ID is required')
        }

        // Extract first name from full name if available
        const name = userData.name?.split(' ')[0] || ''

        loginResult = await oauthApi.appleLogin(appleUserId, {
          email: userData.email || '', // Email can be empty for Apple after first auth
          name,
          bodyWeight: '', // Can be empty for OAuth
          massUnit: '', // Can be empty for OAuth
        })
      } else {
        throw new Error(`Unsupported OAuth provider: ${provider}`)
      }

      // Store authentication data
      useAuthStore.getState().setAuth(loginResult)

      OAuthPerformanceTracker.markComplete(provider, 'process', startMark)
      OAuthPerformanceTracker.markPerformance(provider, 'success')

      return loginResult
    } catch (error) {
      const normalizedError = OAuthErrorHandler.normalizeError(error, provider)
      logger.error(`OAuth ${provider} processing failed`, normalizedError)
      OAuthPerformanceTracker.markPerformance(
        provider,
        'error',
        normalizedError.code
      )
      // eslint-disable-next-line @typescript-eslint/no-throw-literal
      throw normalizedError
    }
  }

  /**
   * Initialize OAuth provider
   * @param provider - OAuth provider to initialize
   * @param onSuccess - Success callback
   * @param onError - Error callback
   */
  static async initializeProvider(
    provider: OAuthProvider,
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback
  ): Promise<void> {
    try {
      OAuthPerformanceTracker.markPerformance(provider, 'init')

      await OAuthProviderHandlers.initializeProvider(
        provider,
        onSuccess,
        onError
      )

      logger.info(`OAuth provider ${provider} initialized successfully`)
    } catch (error) {
      const normalizedError = OAuthErrorHandler.normalizeError(error, provider)
      onError(normalizedError)
      // eslint-disable-next-line @typescript-eslint/no-throw-literal
      throw normalizedError
    }
  }

  /**
   * Sign in with OAuth provider
   * @param provider - OAuth provider
   * @param onSuccess - Success callback
   * @param onError - Error callback
   */
  static async signIn(
    provider: OAuthProvider,
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback
  ): Promise<void> {
    try {
      OAuthPerformanceTracker.markPerformance(provider, 'signIn')

      // Create wrapped callbacks that integrate with our system
      const wrappedSuccess: OAuthSuccessCallback = async (userData) => {
        try {
          await this.handleOAuthSuccess(userData, provider)

          // Call original success callback with processed data
          await onSuccess(userData)

          logger.info('OAuth sign-in completed', {
            provider,
            email: userData.email,
          })
        } catch (error) {
          const normalizedError = OAuthErrorHandler.normalizeError(
            error,
            provider
          )
          onError(normalizedError)
        }
      }

      const wrappedError: OAuthErrorCallback = (error) => {
        const normalizedError = OAuthErrorHandler.normalizeError(
          error,
          provider
        )
        onError(normalizedError)
      }

      await OAuthProviderHandlers.signInWithProvider(
        provider,
        wrappedSuccess,
        wrappedError
      )
    } catch (error) {
      const normalizedError = OAuthErrorHandler.normalizeError(error, provider)
      onError(normalizedError)
      // eslint-disable-next-line @typescript-eslint/no-throw-literal
      throw normalizedError
    }
  }

  /**
   * Check if OAuth provider is available
   * @param provider - OAuth provider to check
   * @returns Whether provider is available
   */
  static isProviderAvailable(provider: OAuthProvider): boolean {
    return OAuthProviderHandlers.isProviderAvailable(provider)
  }

  /**
   * Check if OAuth provider is ready (initialized)
   * @param provider - OAuth provider to check
   * @returns Whether provider is ready
   */
  static isProviderReady(provider: OAuthProvider): boolean {
    return OAuthProviderHandlers.isProviderReady(provider)
  }

  /**
   * Get display name for OAuth provider
   * @param provider - OAuth provider
   * @returns Display name
   */
  static getProviderDisplayName(provider: OAuthProvider): string {
    return OAuthProviderHandlers.getProviderDisplayName(provider)
  }

  /**
   * Get OAuth performance metrics
   * @returns Performance metrics for OAuth operations
   */
  static getPerformanceMetrics(): Record<string, unknown> {
    return OAuthPerformanceTracker.getMetrics()
  }

  // Expose error handler methods
  static normalizeError =
    OAuthErrorHandler.normalizeError.bind(OAuthErrorHandler)

  static createErrorHandler =
    OAuthErrorHandler.createErrorHandler.bind(OAuthErrorHandler)

  // Expose validator methods
  static validateUserData = OAuthValidator.validateUserData.bind(OAuthValidator)

  // Expose performance tracker methods
  static markPerformance = OAuthPerformanceTracker.markPerformance.bind(
    OAuthPerformanceTracker
  )
}

// Export singleton instance methods for convenience
export const oauthIntegration = {
  initialize: OAuthIntegration.initialize.bind(OAuthIntegration),
  handleOAuthSuccess:
    OAuthIntegration.handleOAuthSuccess.bind(OAuthIntegration),
  normalizeError: OAuthIntegration.normalizeError.bind(OAuthIntegration),
  validateUserData: OAuthIntegration.validateUserData.bind(OAuthIntegration),
  markPerformance: OAuthIntegration.markPerformance.bind(OAuthIntegration),
  initializeProvider:
    OAuthIntegration.initializeProvider.bind(OAuthIntegration),
  signIn: OAuthIntegration.signIn.bind(OAuthIntegration),
  isProviderAvailable:
    OAuthIntegration.isProviderAvailable.bind(OAuthIntegration),
  isProviderReady: OAuthIntegration.isProviderReady.bind(OAuthIntegration),
  getProviderDisplayName:
    OAuthIntegration.getProviderDisplayName.bind(OAuthIntegration),
  createErrorHandler:
    OAuthIntegration.createErrorHandler.bind(OAuthIntegration),
  getPerformanceMetrics:
    OAuthIntegration.getPerformanceMetrics.bind(OAuthIntegration),
}
