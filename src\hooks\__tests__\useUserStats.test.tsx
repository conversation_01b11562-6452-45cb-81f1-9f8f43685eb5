import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, waitFor, act } from '@testing-library/react'
import { useUserStats, usePrefetchUserStats } from '../useUserStats'
import {
  useUserStatsStore,
  selectUserStats,
  selectIsLoadingStats,
  selectStatsError,
} from '@/stores/userStatsStore'
import { UserStats } from '@/types/userStats'

// Mock the store
vi.mock('@/stores/userStatsStore')

describe('useUserStats', () => {
  const mockStats: UserStats = {
    weekStreak: 5,
    workoutsCompleted: 25,
    lbsLifted: 10000,
  }

  const mockFetchStats = vi.fn()
  const mockHasData = vi.fn()
  const mockIsStale = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Default mock implementation
    vi.mocked(useUserStatsStore).mockImplementation((selector: any) => {
      // Handle the actual selectors used by the hook
      if (selector === selectUserStats) {
        return mockStats
      }
      if (selector === selectIsLoadingStats) {
        return false
      }
      if (selector === selectStatsError) {
        return null
      }
      if (typeof selector === 'function') {
        const mockState = {
          stats: mockStats,
          isLoading: false,
          error: null,
          fetchStats: mockFetchStats,
          hasData: mockHasData,
          isStale: mockIsStale,
        }
        return selector(mockState)
      }
      return null as any
    })

    mockHasData.mockReturnValue(true)
    mockIsStale.mockReturnValue(false)
  })

  describe('initial fetch', () => {
    it('should fetch stats on mount if no data', async () => {
      mockHasData.mockReturnValue(false)

      renderHook(() => useUserStats())

      await waitFor(() => {
        expect(mockFetchStats).toHaveBeenCalledTimes(1)
      })
    })

    it('should fetch stats on mount if data is stale', async () => {
      mockHasData.mockReturnValue(true)
      mockIsStale.mockReturnValue(true)

      renderHook(() => useUserStats())

      await waitFor(() => {
        expect(mockFetchStats).toHaveBeenCalledTimes(1)
      })
    })

    it('should not fetch stats if data is fresh', () => {
      mockHasData.mockReturnValue(true)
      mockIsStale.mockReturnValue(false)

      renderHook(() => useUserStats())

      expect(mockFetchStats).not.toHaveBeenCalled()
    })
  })

  describe('return values', () => {
    it('should return stats data', () => {
      const { result } = renderHook(() => useUserStats())

      expect(result.current.stats).toEqual(mockStats)
    })

    it('should return loading state', () => {
      vi.mocked(useUserStatsStore).mockImplementation((selector: any) => {
        if (selector === selectUserStats) {
          return { weekStreak: 0, workoutsCompleted: 0, lbsLifted: 0 }
        }
        if (selector === selectIsLoadingStats) {
          return true
        }
        if (selector === selectStatsError) {
          return null
        }
        if (typeof selector === 'function') {
          const mockState = {
            stats: null,
            isLoading: true,
            error: null,
            fetchStats: mockFetchStats,
            hasData: mockHasData,
            isStale: mockIsStale,
          }
          return selector(mockState)
        }
        return null as any
      })

      const { result } = renderHook(() => useUserStats())

      expect(result.current.isLoading).toBe(true)
    })

    it('should return error state', () => {
      const mockError = 'Failed to fetch stats'
      vi.mocked(useUserStatsStore).mockImplementation((selector: any) => {
        if (selector === selectUserStats) {
          return { weekStreak: 0, workoutsCompleted: 0, lbsLifted: 0 }
        }
        if (selector === selectIsLoadingStats) {
          return false
        }
        if (selector === selectStatsError) {
          return mockError
        }
        if (typeof selector === 'function') {
          const mockState = {
            stats: null,
            isLoading: false,
            error: mockError,
            fetchStats: mockFetchStats,
            hasData: mockHasData,
            isStale: mockIsStale,
          }
          return selector(mockState)
        }
        return null as any
      })

      const { result } = renderHook(() => useUserStats())

      expect(result.current.error).toBe(mockError)
    })

    it('should provide refetch function', async () => {
      const { result } = renderHook(() => useUserStats())

      await act(async () => {
        await result.current.refetch()
      })

      expect(mockFetchStats).toHaveBeenCalled()
    })
  })

  describe('default stats', () => {
    it('should return default stats when store has null', () => {
      vi.mocked(useUserStatsStore).mockImplementation((selector: any) => {
        if (selector === selectUserStats) {
          return { weekStreak: 0, workoutsCompleted: 0, lbsLifted: 0 }
        }
        if (selector === selectIsLoadingStats) {
          return false
        }
        if (selector === selectStatsError) {
          return null
        }
        if (typeof selector === 'function') {
          const mockState = {
            stats: null,
            isLoading: false,
            error: null,
            fetchStats: mockFetchStats,
            hasData: mockHasData,
            isStale: mockIsStale,
          }
          return selector(mockState)
        }
        return null as any
      })

      mockHasData.mockReturnValue(false)

      const { result } = renderHook(() => useUserStats())

      expect(result.current.stats).toEqual({
        weekStreak: 0,
        workoutsCompleted: 0,
        lbsLifted: 0,
      })
    })
  })
})

describe('usePrefetchUserStats', () => {
  const mockFetchStats = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    vi.mocked(useUserStatsStore).mockImplementation((selector) => {
      if (typeof selector === 'function') {
        const mockState = {
          fetchStats: mockFetchStats,
        }
        return selector(mockState as any)
      }
      return null as any
    })
  })

  it('should provide prefetch function', async () => {
    const { result } = renderHook(() => usePrefetchUserStats())

    await act(async () => {
      await result.current.prefetch()
    })

    expect(mockFetchStats).toHaveBeenCalledTimes(1)
  })
})
