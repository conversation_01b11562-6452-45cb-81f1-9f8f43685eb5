'use client'

interface SaveErrorDisplayProps {
  error: string
  onRetry: () => void
}

export function SaveErrorDisplay({ error, onRetry }: SaveErrorDisplayProps) {
  return (
    <div className="px-4 mb-4">
      <div className="bg-error/10 border border-error/20 rounded-theme p-4">
        <p className="text-error">{error}</p>
        <button
          onClick={onRetry}
          className="mt-2 text-error underline font-medium"
        >
          Retry
        </button>
      </div>
    </div>
  )
}
