import type { WorkoutLogSerieModel } from '@/types'
import { logger } from '@/utils/logger'

export interface FailedRequest {
  id: string
  type: string
  data: WorkoutLogSerieModel | Record<string, unknown>
  timestamp: number
  error?: string
}

/**
 * Queue management for sync operations
 */
export class SyncQueueManager {
  /**
   * Get count of pending sync items
   */
  static getPendingCount(): number {
    try {
      const failed = JSON.parse(
        localStorage.getItem('drmuscle-failed-requests') || '[]'
      ).length
      const offline = JSON.parse(
        localStorage.getItem('drmuscle-offline-queue') || '[]'
      ).length
      return failed + offline
    } catch {
      return 0
    }
  }

  /**
   * Add item to sync queue (called when save fails)
   */
  static addToQueue(request: Omit<FailedRequest, 'id'>): string {
    const queueKey = navigator.onLine
      ? 'drmuscle-failed-requests'
      : 'drmuscle-offline-queue'

    const queue = JSON.parse(localStorage.getItem(queueKey) || '[]')
    const requestWithId: FailedRequest = {
      ...request,
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    }
    queue.push(requestWithId)
    localStorage.setItem(queueKey, JSON.stringify(queue))

    logger.log('[SyncQueue] Added to queue:', {
      id: requestWithId.id,
      type: requestWithId.type,
      queue: queueKey,
      totalInQueue: queue.length,
    })

    return requestWithId.id
  }

  /**
   * Get failed requests from storage
   */
  static getFailedRequests(): FailedRequest[] {
    try {
      return JSON.parse(
        localStorage.getItem('drmuscle-failed-requests') || '[]'
      )
    } catch {
      return []
    }
  }

  /**
   * Update failed requests in storage
   */
  static setFailedRequests(requests: FailedRequest[]): void {
    localStorage.setItem('drmuscle-failed-requests', JSON.stringify(requests))
  }

  /**
   * Get offline queue from storage
   */
  static getOfflineQueue(): FailedRequest[] {
    try {
      return JSON.parse(localStorage.getItem('drmuscle-offline-queue') || '[]')
    } catch {
      return []
    }
  }

  /**
   * Update offline queue in storage
   */
  static setOfflineQueue(requests: FailedRequest[]): void {
    localStorage.setItem('drmuscle-offline-queue', JSON.stringify(requests))
  }

  /**
   * Clear all sync data (use with caution)
   */
  static clearAll(): void {
    localStorage.removeItem('drmuscle-failed-requests')
    localStorage.removeItem('drmuscle-offline-queue')
  }
}
