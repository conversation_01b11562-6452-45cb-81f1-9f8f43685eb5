import { describe, it, expect, vi, beforeEach } from 'vitest'
import { getExerciseRecommendation } from '@/services/api/workout'
import { apiClient } from '@/api/client'

// Mock the API client
vi.mock('@/api/client', () => ({
  apiClient: {
    post: vi.fn(),
  },
}))

// Mock the auth utils
vi.mock('@/lib/auth-utils', () => ({
  getCurrentUserEmail: vi.fn(() => '<EMAIL>'),
}))

describe('getExerciseRecommendation', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should parse wrapped API response correctly', async () => {
    const mockResponse = {
      data: {
        StatusCode: 200,
        ErrorMessage: null,
        Result: {
          Series: 3,
          Reps: 8,
          Weight: {
            Unity: 0,
            Entered: 100,
            IsRound: true,
            Kg: 45.4,
            Lb: 100,
          },
          WarmupsCount: 2,
          IsNormalSets: true,
        },
      },
    }

    vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

    const request = {
      Username: '<EMAIL>',
      ExerciseId: 123,
      WorkoutId: 456,
      SetStyle: 'Normal',
      IsFlexibility: false,
      IsQuickMode: null,
      LightSessionDays: null,
      IsStrengthPhashe: false,
      IsFreePlan: false,
      IsFirstWorkoutOfStrengthPhase: false,
      VersionNo: 1,
    }

    const result = await getExerciseRecommendation(request)

    expect(result).toBeDefined()
    expect(result?.Weight?.Lb).toBe(100)
    expect(result?.Reps).toBe(8)
    expect(result?.Series).toBe(3)
  })

  it('should handle direct response format', async () => {
    const mockResponse = {
      data: {
        Series: 3,
        Reps: 10,
        Weight: {
          Unity: 0,
          Entered: 50,
          IsRound: true,
          Kg: 22.7,
          Lb: 50,
        },
      },
    }

    vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

    const request = {
      Username: '<EMAIL>',
      ExerciseId: 123,
      WorkoutId: 456,
      SetStyle: 'Normal',
      IsFlexibility: false,
    }

    const result = await getExerciseRecommendation(request)

    expect(result).toBeDefined()
    expect(result?.Weight?.Lb).toBe(50)
    expect(result?.Reps).toBe(10)
  })

  it('should return null for API errors', async () => {
    vi.mocked(apiClient.post).mockRejectedValueOnce(new Error('Network error'))

    const request = {
      Username: '<EMAIL>',
      ExerciseId: 123,
      WorkoutId: 456,
      SetStyle: 'Normal',
      IsFlexibility: false,
    }

    const result = await getExerciseRecommendation(request)

    expect(result).toBeNull()
  })

  it('should use correct endpoint for rest-pause sets', async () => {
    const mockResponse = {
      data: { StatusCode: 200, Result: { Weight: { Lb: 100 } } },
    }

    vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

    const request = {
      Username: '<EMAIL>',
      ExerciseId: 123,
      WorkoutId: 456,
      SetStyle: 'RestPause',
      IsFlexibility: false,
    }

    await getExerciseRecommendation(request)

    expect(apiClient.post).toHaveBeenCalledWith(
      '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew',
      expect.any(Object)
    )
  })

  it('should use normal endpoint for flexibility exercises', async () => {
    const mockResponse = {
      data: { StatusCode: 200, Result: { Weight: { Lb: 0 } } },
    }

    vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

    const request = {
      Username: '<EMAIL>',
      ExerciseId: 123,
      WorkoutId: 456,
      SetStyle: 'RestPause',
      IsFlexibility: true, // This should force normal endpoint
    }

    await getExerciseRecommendation(request)

    expect(apiClient.post).toHaveBeenCalledWith(
      '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
      expect.any(Object)
    )
  })
})