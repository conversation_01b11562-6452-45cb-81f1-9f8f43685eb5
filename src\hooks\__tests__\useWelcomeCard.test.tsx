import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useWelcomeCard } from '../useWelcomeCard'
import { workoutApi } from '@/api/workouts'
import { useAuthStore } from '@/stores/authStore'

// Mock dependencies
vi.mock('@/api/workouts')
vi.mock('@/stores/authStore')

describe('useWelcomeCard', () => {
  const mockUser = { email: '<EMAIL>', firstName: 'Test' }
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
    },
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  beforeEach(() => {
    vi.clearAllMocks()
    queryClient.clear()
    ;(useAuthStore as any).mockReturnValue(mockUser)
  })

  it('should return null when LastWorkoutDate is not available', async () => {
    const mockResponse = {
      WorkoutCount: 10,
      ConsecutiveWeeks: 2,
      // No LastWorkoutDate or SetsDate
    }

    vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(
      mockResponse as any
    )

    const { result } = renderHook(() => useWelcomeCard(), { wrapper })

    await waitFor(() => expect(result.current.isLoading).toBe(false))

    expect(result.current.recoveryInfo).toBeNull()
    expect(result.current.coachMessage).toBeNull()
    expect(result.current.programName).toBeNull()
  })

  it('should use SetsDate when LastWorkoutDate is null', async () => {
    const mockResponse = {
      LastWorkoutDate: null,
      SetsDate: [
        '2025-01-06T10:00:00Z',
        '2025-01-04T10:00:00Z',
        '2025-01-02T10:00:00Z',
      ],
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: {
          Label: 'Full Body Workout',
        },
        NextWorkoutTemplate: {
          Label: 'Workout A',
        },
      },
    }

    vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(
      mockResponse as any
    )

    const { result } = renderHook(() => useWelcomeCard(), { wrapper })

    await waitFor(() => expect(result.current.isLoading).toBe(false))

    expect(result.current.recoveryInfo).not.toBeNull()
    expect(result.current.recoveryInfo?.percentage).toBeGreaterThanOrEqual(0)
    expect(result.current.recoveryInfo?.percentage).toBeLessThanOrEqual(100)
    expect(result.current.coachMessage).toBe(
      result.current.recoveryInfo?.message
    )
    expect(result.current.programName).toBe('Full Body Workout')
    expect(result.current.nextWorkoutName).toBe('Workout A')
  })

  it('should calculate recovery correctly for split program', async () => {
    const mockResponse = {
      SetsDate: [
        new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString(), // 10 hours ago
      ],
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: {
          Label: 'Upper/Lower Split',
        },
      },
    }

    vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(
      mockResponse as any
    )

    const { result } = renderHook(() => useWelcomeCard(), { wrapper })

    await waitFor(() => expect(result.current.isLoading).toBe(false))

    expect(result.current.recoveryInfo).not.toBeNull()
    // Split program needs 18 hours, 10 hours elapsed = ~55% recovered
    expect(result.current.recoveryInfo?.percentage).toBeCloseTo(56, 0)
    expect(result.current.recoveryInfo?.isReady).toBe(false)
    expect(result.current.recoveryInfo?.message).toBe('Almost ready')
  })

  it('should show ready when recovery is complete', async () => {
    const mockResponse = {
      SetsDate: [
        new Date(Date.now() - 20 * 60 * 60 * 1000).toISOString(), // 20 hours ago
      ],
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: {
          Label: 'Upper/Lower Split',
        },
      },
    }

    vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(
      mockResponse as any
    )

    const { result } = renderHook(() => useWelcomeCard(), { wrapper })

    await waitFor(() => expect(result.current.isLoading).toBe(false))

    expect(result.current.recoveryInfo).not.toBeNull()
    expect(result.current.recoveryInfo?.percentage).toBe(100)
    expect(result.current.recoveryInfo?.isReady).toBe(true)
    expect(result.current.recoveryInfo?.message).toBe("Let's train!")
  })
})
