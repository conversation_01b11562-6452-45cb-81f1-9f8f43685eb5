import { test, expect, Page } from '@playwright/test'

test.describe('OAuth Provider Edge Cases Tests', () => {
  let page: Page

  test.beforeEach(async ({ page: p }) => {
    page = p

    // Mock OAuth SDKs
    await page.addInitScript(() => {
      const mockWindow = window as any

      mockWindow.google = {
        accounts: {
          id: {
            initialize: (config: any) => {
              mockWindow.__googleConfig = config
            },
            renderButton: (element: HTMLElement) => {
              const button = document.createElement('button')
              button.textContent = 'Sign in with Google'
              button.onclick = () => {
                if (mockWindow.__googleConfig?.callback) {
                  mockWindow.__googleConfig.callback({
                    credential: 'mock-google-jwt-token',
                    select_by: 'btn',
                  })
                }
              }
              element.appendChild(button)
            },
          },
        },
      }

      mockWindow.AppleID = {
        auth: {
          init: (config: any) => {
            mockWindow.__appleConfig = config
          },
          signIn: () =>
            Promise.resolve({
              authorization: {
                code: 'mock-apple-auth-code',
                id_token: 'mock-apple-id-token',
                state: 'mock-state',
              },
            }),
        },
      }
    })

    await page.goto('/login')
  })

  test('should handle OAuth token validation failures', async () => {
    // Mock backend to reject the OAuth token
    await page.route('**/api/oauth/google/token', (route) => {
      route.fulfill({
        status: 401,
        json: {
          error: 'invalid_token',
          error_description: 'The provided token is invalid or expired',
        },
      })
    })

    // Click Google sign in
    const googleButton = page.locator('button:has-text("Sign in with Google")')
    await googleButton.click()

    // Should show error message
    await expect(page.locator('[role="alert"]')).toContainText(
      /invalid token|authentication failed/i
    )

    // Should remain on login page
    await expect(page).toHaveURL('/login')
  })

  test('should handle OAuth with existing Dr. Muscle account', async () => {
    // Mock backend to return existing user
    await page.route('**/api/oauth/google/token', (route) => {
      route.fulfill({
        status: 200,
        json: {
          access_token: 'existing-user-token',
          refresh_token: 'existing-user-refresh',
          token_type: 'Bearer',
          expires_in: 3600,
          user: {
            id: 'existing-user-123',
            email: '<EMAIL>',
            name: 'Existing User',
            created_at: '2023-01-01T00:00:00Z',
            last_login: '2025-01-01T00:00:00Z',
          },
        },
      })
    })

    // Click Google sign in
    const googleButton = page.locator('button:has-text("Sign in with Google")')
    await googleButton.click()

    // Should redirect to program page
    await expect(page).toHaveURL('/program', { timeout: 5000 })

    // Should show welcome back message
    await expect(page.locator('text=Welcome back')).toBeVisible()
  })

  test('should handle OAuth account linking flow', async () => {
    // First, login with email/password
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'testpassword')
    await page.click('button[type="submit"]')

    // Mock successful login
    await page.route('**/api/token', (route) => {
      route.fulfill({
        status: 200,
        json: {
          access_token: 'email-login-token',
          userName: '<EMAIL>',
        },
      })
    })

    // Navigate to settings (mock)
    await page.goto('/settings')

    // Mock account linking endpoint
    await page.route('**/api/oauth/link/google', (route) => {
      route.fulfill({
        status: 200,
        json: {
          success: true,
          message: 'Google account linked successfully',
        },
      })
    })

    // Click link Google account
    const linkButton = page.locator('button:has-text("Link Google Account")')
    if (await linkButton.isVisible()) {
      await linkButton.click()

      // Should show success message
      await expect(
        page.locator('text=Google account linked successfully')
      ).toBeVisible()
    }
  })

  test('should handle OAuth with rate limiting', async () => {
    let attemptCount = 0

    // Mock backend to rate limit first 2 attempts
    await page.route('**/api/oauth/google/token', (route) => {
      attemptCount++
      if (attemptCount <= 2) {
        route.fulfill({
          status: 429,
          headers: {
            'Retry-After': '5',
          },
          json: {
            error: 'rate_limit_exceeded',
            error_description: 'Too many requests. Please try again later.',
          },
        })
      } else {
        route.fulfill({
          status: 200,
          json: {
            access_token: 'success-after-retry',
            refresh_token: 'refresh-token',
            token_type: 'Bearer',
            expires_in: 3600,
            user: {
              id: 'user-123',
              email: '<EMAIL>',
            },
          },
        })
      }
    })

    // Click Google sign in
    const googleButton = page.locator('button:has-text("Sign in with Google")')
    await googleButton.click()

    // Should show rate limit error
    await expect(page.locator('[role="alert"]')).toContainText(
      /too many requests|rate limit/i
    )

    // Wait and retry
    await page.waitForTimeout(1000)
    await googleButton.click()

    // Should still be rate limited
    await expect(page.locator('[role="alert"]')).toBeVisible()

    // Third attempt should succeed
    await page.waitForTimeout(1000)
    await googleButton.click()

    // Should eventually succeed
    await expect(page).toHaveURL('/program', { timeout: 10000 })
  })

  test('should persist OAuth session across page reloads', async () => {
    // Mock successful OAuth login
    await page.route('**/api/oauth/google/token', (route) => {
      route.fulfill({
        status: 200,
        json: {
          access_token: 'persistent-token',
          refresh_token: 'persistent-refresh',
          token_type: 'Bearer',
          expires_in: 3600,
          user: {
            id: 'user-123',
            email: '<EMAIL>',
          },
        },
      })
    })

    // Login with Google
    const googleButton = page.locator('button:has-text("Sign in with Google")')
    await googleButton.click()
    await expect(page).toHaveURL('/program')

    // Reload page
    await page.reload()

    // Should still be logged in
    await expect(page).toHaveURL('/program')

    // Should not show login page
    await page.goto('/login')
    await expect(page).toHaveURL('/program') // Should redirect back
  })
})
