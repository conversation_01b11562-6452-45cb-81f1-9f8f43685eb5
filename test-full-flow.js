/**
 * Test the full flow from login to exercise recommendation
 * Run with: node test-full-flow.js
 */

const { chromium } = require('playwright');

async function testFullFlow() {
  console.log('🚀 Starting full flow test...');
  
  const browser = await chromium.launch({ 
    headless: false, // Show browser for debugging
    slowMo: 1000 // Slow down for visibility
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Listen to console logs
  page.on('console', (msg) => {
    if (msg.type() === 'log' || msg.type() === 'error') {
      console.log(`[BROWSER ${msg.type().toUpperCase()}]`, msg.text());
    }
  });
  
  try {
    // Step 1: Navigate to login page
    console.log('📱 Step 1: Navigating to login page...');
    await page.goto('http://localhost:3000/login');
    await page.waitForLoadState('networkidle');
    
    // Step 2: Login
    console.log('🔐 Step 2: Logging in...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Dr123456');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to program page
    await page.waitForURL('**/program', { timeout: 10000 });
    console.log('✅ Login successful, redirected to program page');
    
    // Step 3: Start workout
    console.log('🏋️ Step 3: Starting workout...');
    
    // Look for the floating CTA button (default text is "Open Workout")
    const openWorkoutButton = page.locator('button:has-text("Open Workout")');
    if (await openWorkoutButton.isVisible()) {
      await openWorkoutButton.click();
      console.log('✅ Clicked Open Workout button');
    } else {
      // Try alternative selectors
      const startWorkoutButton = page.locator('button:has-text("Start Workout")');
      const continueWorkoutButton = page.locator('button:has-text("Continue Workout")');
      const ctaButton = page.locator('[data-testid="start-workout-button"]');

      if (await startWorkoutButton.isVisible()) {
        await startWorkoutButton.click();
        console.log('✅ Clicked Start Workout button');
      } else if (await continueWorkoutButton.isVisible()) {
        await continueWorkoutButton.click();
        console.log('✅ Clicked Continue Workout button');
      } else if (await ctaButton.isVisible()) {
        await ctaButton.click();
        console.log('✅ Clicked CTA button');
      } else {
        console.log('❌ No workout button found, checking page content...');
        const pageContent = await page.textContent('body');
        console.log('Page content preview:', pageContent.substring(0, 500));
        throw new Error('No workout button found');
      }
    }
    
    // Wait for workout page
    await page.waitForURL('**/workout', { timeout: 10000 });
    console.log('✅ Navigated to workout page');
    
    // Step 4: Click on first exercise
    console.log('🎯 Step 4: Clicking on first exercise...');
    
    // Wait for exercises to load
    await page.waitForSelector('[data-testid="exercise-card"]', { timeout: 10000 });

    // Click on first exercise
    const firstExercise = page.locator('[data-testid="exercise-card"]').first();
    await firstExercise.click();
    console.log('✅ Clicked on first exercise');
    
    // Wait for exercise page
    await page.waitForURL('**/workout/exercise/**', { timeout: 10000 });
    console.log('✅ Navigated to exercise page');
    
    // Step 5: Wait for recommendation to load and check the UI
    console.log('📊 Step 5: Checking exercise recommendation...');
    
    // Wait a bit for the recommendation to load
    await page.waitForTimeout(3000);
    
    // Check if weight and reps are displayed
    const exerciseInfo = page.locator('[data-testid="exercise-info"]');
    if (await exerciseInfo.isVisible()) {
      const infoText = await exerciseInfo.textContent();
      console.log('Exercise info text:', infoText);
      
      if (infoText.includes('lbs') || infoText.includes('kg')) {
        console.log('✅ SUCCESS: Weight recommendation is displayed!');
      } else if (infoText.includes('No weight recommendation')) {
        console.log('❌ ISSUE: "No weight recommendation" message is shown');
      } else {
        console.log('❓ UNCLEAR: Weight status unclear from UI text');
      }
      
      if (infoText.includes('reps')) {
        console.log('✅ SUCCESS: Reps recommendation is displayed!');
      } else {
        console.log('❌ ISSUE: No reps recommendation found');
      }
    } else {
      console.log('❌ ISSUE: Exercise info component not found');
    }
    
    // Check for any error messages
    const errorMessages = await page.locator('.text-red-500, .text-warning, .bg-red-100').allTextContents();
    if (errorMessages.length > 0) {
      console.log('⚠️ Error messages found:', errorMessages);
    }
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'exercise-page-debug.png', fullPage: true });
    console.log('📸 Screenshot saved as exercise-page-debug.png');
    
    console.log('✅ Full flow test completed');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // Take a screenshot of the error state
    await page.screenshot({ path: 'error-state-debug.png', fullPage: true });
    console.log('📸 Error screenshot saved as error-state-debug.png');
  } finally {
    await browser.close();
  }
}

// Run the test
testFullFlow().catch(console.error);
