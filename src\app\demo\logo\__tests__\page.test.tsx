import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import LogoDemoPage from '../page'

// Mock the Logo component
vi.mock('@/components', () => ({
  Logo: ({
    width = 200,
    height = 200,
    className = '',
    priority = false,
  }: any) => (
    // eslint-disable-next-line @next/next/no-img-element
    <img
      src="/logo.png"
      alt="Dr. Muscle"
      width={width}
      height={height}
      className={className}
      data-priority={priority}
    />
  ),
}))

describe('LogoDemoPage', () => {
  describe('Theme Application', () => {
    it('should use theme-aware background color instead of hardcoded gray-50', () => {
      const { container } = render(<LogoDemoPage />)

      const mainDiv = container.querySelector('.min-h-\\[100dvh\\]')
      expect(mainDiv).toHaveClass('bg-bg-primary')
      expect(mainDiv).not.toHaveClass('bg-gray-50')
    })

    it('should use theme-aware card backgrounds instead of hardcoded white', () => {
      const { container } = render(<LogoDemoPage />)

      const cards = container.querySelectorAll('.rounded-lg.shadow')
      // First 5 cards should have theme background
      for (let i = 0; i < 5; i++) {
        expect(cards[i]).toHaveClass('bg-bg-secondary')
        expect(cards[i]).not.toHaveClass('bg-white')
      }
    })

    it('should use theme-aware text colors instead of hardcoded gray-600', () => {
      const { container } = render(<LogoDemoPage />)

      const descriptions = container.querySelectorAll('p.text-sm')
      // First 5 descriptions
      for (let i = 0; i < 5; i++) {
        expect(descriptions[i]).toHaveClass('text-text-tertiary')
        expect(descriptions[i]).not.toHaveClass('text-gray-600')
      }
    })

    it('should use theme-aware dark section instead of hardcoded gray-900', () => {
      const { container } = render(<LogoDemoPage />)

      const darkSection = container.querySelectorAll('.rounded-lg.shadow')[5]
      expect(darkSection).toHaveClass('bg-bg-tertiary')
      expect(darkSection).not.toHaveClass('bg-gray-900')
    })

    it('should use theme text color for dark section text instead of hardcoded white', () => {
      render(<LogoDemoPage />)

      const darkSectionTitle = screen.getByText('On Dark Background')
      expect(darkSectionTitle).toHaveClass('text-text-primary')
      expect(darkSectionTitle).not.toHaveClass('text-white')
    })

    it('should use theme-aware text color for dark section description', () => {
      const { container } = render(<LogoDemoPage />)

      const darkDescription = container.querySelectorAll('p.text-sm')[5]
      expect(darkDescription).toHaveClass('text-text-secondary')
      expect(darkDescription).not.toHaveClass('text-gray-400')
    })

    it('should use theme-aware text color for feature list instead of hardcoded gray-700', () => {
      const { container } = render(<LogoDemoPage />)

      const featureList = container.querySelector('ul.space-y-2')
      expect(featureList).toHaveClass('text-text-primary')
      expect(featureList).not.toHaveClass('text-gray-700')
    })
  })

  describe('Original Functionality', () => {
    it('should render page title', () => {
      render(<LogoDemoPage />)
      expect(screen.getByText('Logo Component Demo')).toBeInTheDocument()
    })

    it('should render all logo variants', () => {
      render(<LogoDemoPage />)

      expect(screen.getByText('Default Logo')).toBeInTheDocument()
      expect(screen.getByText('Small Logo')).toBeInTheDocument()
      expect(screen.getByText('Large Logo')).toBeInTheDocument()
      expect(screen.getByText('With Custom Class')).toBeInTheDocument()
      expect(screen.getByText('Priority Loading')).toBeInTheDocument()
      expect(screen.getByText('On Dark Background')).toBeInTheDocument()
    })

    it('should render feature list', () => {
      render(<LogoDemoPage />)

      expect(screen.getByText('Logo Features')).toBeInTheDocument()
      expect(screen.getByText(/Responsive sizing/)).toBeInTheDocument()
    })
  })
})
