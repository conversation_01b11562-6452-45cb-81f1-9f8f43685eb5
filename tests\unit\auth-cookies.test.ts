import { describe, it, expect, vi, beforeEach } from 'vitest'
import { getCurrentUserEmail, getCurrentAuthState } from '@/lib/auth-utils'
import { useAuthStore } from '@/stores/authStore'

// Mock the auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: {
    getState: vi.fn(),
  },
}))

describe('Auth Cookie Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getCurrentUserEmail', () => {
    it('should return user email when authenticated', () => {
      const mockState = {
        user: { email: '<EMAIL>' },
        isAuthenticated: true,
      }
      vi.mocked(useAuthStore.getState).mockReturnValue(mockState as any)

      const email = getCurrentUserEmail()
      expect(email).toBe('<EMAIL>')
    })

    it('should return null when not authenticated', () => {
      const mockState = {
        user: null,
        isAuthenticated: false,
      }
      vi.mocked(useAuthStore.getState).mockReturnValue(mockState as any)

      const email = getCurrentUserEmail()
      expect(email).toBeNull()
    })
  })

  describe('getCurrentAuthState', () => {
    it('should return full auth state', () => {
      const mockState = {
        user: { email: '<EMAIL>' },
        isAuthenticated: true,
        token: 'test-token',
        refreshToken: 'test-refresh-token',
      }
      vi.mocked(useAuthStore.getState).mockReturnValue(mockState as any)

      const authState = getCurrentAuthState()
      expect(authState).toEqual({
        user: { email: '<EMAIL>' },
        isAuthenticated: true,
        token: 'test-token',
        refreshToken: 'test-refresh-token',
      })
    })
  })
})
