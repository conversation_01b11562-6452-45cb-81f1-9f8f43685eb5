/**
 * Metric tracking operations for UserInfo performance
 */

import { UserInfoPerformanceMarks } from './types'
import type { UserInfoPerformanceMetrics } from './types'
import { mark, measure } from './performanceUtils'

export class MetricOperations {
  static trackMetricLoaded(
    metrics: UserInfoPerformanceMetrics,
    metric: 'streak' | 'workouts' | 'volume'
  ): void {
    const markName = {
      streak: UserInfoPerformanceMarks.METRIC_STREAK_LOADED,
      workouts: UserInfoPerformanceMarks.METRIC_WORKOUTS_LOADED,
      volume: UserInfoPerformanceMarks.METRIC_VOLUME_LOADED,
    }[metric]

    mark(markName)

    // Measure from stats start to this metric
    const duration = measure(
      `metric-${metric}-load`,
      UserInfoPerformanceMarks.STATS_FETCH_START,
      markName
    )

    if (duration !== null) {
      metrics.metricLoadTimes[metric] = duration
    }

    // Check if all metrics are loaded
    const allLoaded = Object.values(metrics.metricLoadTimes).every(
      (time) => time !== null
    )

    if (allLoaded) {
      mark(UserInfoPerformanceMarks.ALL_METRICS_LOADED)
    }
  }

  static trackUserInfoComplete(
    metrics: UserInfoPerformanceMetrics,
    fromCache = false
  ): void {
    mark(UserInfoPerformanceMarks.USERINFO_FETCH_END)

    if (!fromCache) {
      const duration = measure(
        'userinfo-load-time',
        UserInfoPerformanceMarks.USERINFO_FETCH_START,
        UserInfoPerformanceMarks.USERINFO_FETCH_END
      )

      if (duration !== null) {
        metrics.userInfoLoadTime = duration
      }
    }
  }

  static trackStatsComplete(metrics: UserInfoPerformanceMetrics): void {
    mark(UserInfoPerformanceMarks.STATS_FETCH_END)

    const duration = measure(
      'stats-load-time',
      UserInfoPerformanceMarks.STATS_FETCH_START,
      UserInfoPerformanceMarks.STATS_FETCH_END
    )

    if (duration !== null) {
      metrics.statsLoadTime = duration
    }
  }

  static trackPageReady(metrics: UserInfoPerformanceMetrics): void {
    mark(UserInfoPerformanceMarks.PROGRAM_PAGE_READY)

    const duration = measure(
      'overall-page-ready',
      UserInfoPerformanceMarks.LOGIN_SUCCESS,
      UserInfoPerformanceMarks.PROGRAM_PAGE_READY
    )

    if (duration !== null) {
      metrics.overallPageReady = duration
    }
  }

  static trackUserInfoFirstByte(metrics: UserInfoPerformanceMetrics): void {
    mark(UserInfoPerformanceMarks.USERINFO_FIRST_BYTE)

    const duration = measure(
      'login-to-first-byte',
      UserInfoPerformanceMarks.LOGIN_SUCCESS,
      UserInfoPerformanceMarks.USERINFO_FIRST_BYTE
    )

    if (duration !== null) {
      metrics.loginToFirstByte = duration
    }
  }
}
