# Program Overview Page - Implementation Plan

> Detailed, step-by-step blueprint for building the Program Overview Page that displays program information immediately after login

## Overview and Goals

### Purpose

Create a separate Program Overview Page that loads immediately after successful login, displaying the user's current program information (name, description, progress, stats) before navigating to the workout page. This provides users with context about their fitness journey and creates a sense of progress and achievement.

### Key Goals

1. **Immediate Display**: Load program data in parallel with workout data during login
2. **Clean Information Architecture**: Present program details in a clear, mobile-optimized layout
3. **Progress Visualization**: Show user's progress through the program
4. **Smooth Transition**: Seamless navigation from login → program overview → workout
5. **Performance**: Zero delay in perceived loading time

### Success Criteria

- Program overview displays within 1 second of login
- All program data loads correctly (name, description, progress)
- Navigation flow feels natural and fast
- Mobile-first design with excellent touch UX
- No orphaned code or incomplete features

## Detailed Blueprint

### Phase 1: Data Layer Foundation

Build the backend infrastructure for program data management

### Phase 2: UI Components

Create reusable components for displaying program information

### Phase 3: Page Implementation

Implement the program overview page with routing

### Phase 4: Navigation Integration

Integrate into the existing login → workout flow

### Phase 5: Performance Optimization

Ensure instant loading with prefetching and caching

## Phase Breakdown

### Phase 1: Data Layer Foundation

#### 1.1 TypeScript Types for Program Data

- Define interfaces for program information
- Create type guards and validators
- Add to existing API types structure

#### 1.2 API Integration

- Create program API endpoints
- Implement React Query hooks
- Set up prefetching logic

#### 1.3 Program Store

- Create Zustand store for program state
- Implement persistence
- Add error handling

### Phase 2: UI Components

#### 2.1 Program Header Component

- Display program name and icon
- Show program type/category
- Mobile-optimized typography

#### 2.2 Progress Visualization

- Progress bar/ring component
- Stats display (days, workouts, etc.)
- Achievement indicators

#### 2.3 Program Description

- Expandable description text
- Key features/benefits list
- Mobile-friendly layout

#### 2.4 Action Buttons

- "Continue to Workout" primary CTA
- Secondary actions (view history, etc.)
- Touch-optimized sizing

### Phase 3: Page Implementation

#### 3.1 Program Overview Page

- Full page layout
- Loading states
- Error handling
- Mobile responsiveness

#### 3.2 Route Configuration

- Add to Next.js app router
- Set up navigation guards
- Configure redirects

### Phase 4: Navigation Integration

#### 4.1 Login Flow Update

- Modify login success to redirect to program overview
- Update success screen integration
- Maintain auth token flow

#### 4.2 Program to Workout Navigation

- Add navigation from program to workout
- Implement back button handling
- Preserve navigation state

### Phase 5: Performance Optimization

#### 5.1 Parallel Data Loading

- Load program data during login
- Prefetch workout data while on program page
- Implement progressive loading

#### 5.2 Caching Strategy

- Cache program data locally
- Implement stale-while-revalidate
- Handle offline scenarios

## Implementation Steps

### Step 1: TypeScript Types and API Models

Define the data structures for program information

### Step 2: API Client Methods

Create methods to fetch program data from the API

### Step 3: React Query Hooks

Implement hooks for data fetching with caching

### Step 4: Program Store Setup

Create Zustand store for program state management

### Step 5: Progress Ring Component

Build the circular progress visualization

### Step 6: Program Header Component

Create the header with name and category

### Step 7: Program Stats Component

Display workout count, days, achievements

### Step 8: Program Description Component

Implement expandable description section

### Step 9: Program Overview Page Layout

Assemble components into full page

### Step 10: Route Configuration

Add program overview to app router

### Step 11: Navigation Flow Update

Modify login to redirect to program overview

### Step 12: Prefetch Integration

Add program data prefetching to login

### Step 13: Loading States

Implement skeleton loaders for program page

### Step 14: Error Handling

Add error boundaries and retry logic

### Step 15: Performance Testing

Verify loading times and optimize

## TDD Implementation Prompts

### Prompt 1: Program Data Types and API Integration

```text
Create TypeScript types and API integration for program data following TDD. Your task:

1. First, write failing tests for:
   - Program data type definitions
   - API response parsing
   - Type guards for program data
   - Error handling for missing data

2. Create `src/types/program.ts` with interfaces:
   - ProgramModel: { id, name, description, category, totalDays, currentDay, workoutsCompleted, startDate, imageUrl }
   - ProgramProgress: { percentage, daysCompleted, totalWorkouts, currentWeek }
   - ProgramStats: { averageWorkoutTime, totalVolume, personalRecords }

3. Create `src/api/program.ts` with methods:
   - `getUserProgram()` → ProgramModel
   - `getProgramProgress(programId)` → ProgramProgress
   - `getProgramStats(programId)` → ProgramStats

4. Create React Query hooks in `src/hooks/useProgram.ts`:
   - `useProgram()` - fetch current program
   - `useProgramProgress()` - fetch progress data
   - `useProgramStats()` - fetch program statistics
   - Implement proper error handling and loading states

Requirements:
- Follow existing TypeScript patterns from api.ts
- Use React Query for caching and state management
- Include proper error types and handling
- All tests must pass before proceeding
```

### Prompt 2: Program Store Implementation

```text
Implement a Zustand store for program state management using TDD. Your task:

1. Write failing tests for:
   - Store initialization and state shape
   - Loading program data
   - Updating progress information
   - Error state handling
   - Persistence to localStorage

2. Create `src/stores/programStore.ts` with:
   - State: { program, progress, stats, isLoading, error, lastFetched }
   - Actions: `loadProgram`, `updateProgress`, `setError`, `clearError`
   - Persistence middleware for caching

3. Integrate with React Query hooks:
   - Sync query data with store
   - Handle optimistic updates
   - Implement cache invalidation

4. Test scenarios:
   - Load program successfully
   - Handle API errors gracefully
   - Persist data across sessions
   - Update progress in real-time

Requirements:
- Follow patterns from authStore.ts
- Implement proper TypeScript typing
- Use immer for immutable updates
- Include loading and error states
```

### Prompt 3: Progress Ring Component

```text
Create a mobile-optimized circular progress ring component using TDD. Your task:

1. Write tests for:
   - Render progress ring with percentage
   - Animate on mount (0 to current progress)
   - Display progress text in center
   - Handle different sizes (small, medium, large)
   - Respect reduced motion preferences

2. Create `src/components/ProgressRing.tsx`:
   - SVG-based circular progress indicator
   - Smooth animation using CSS transitions
   - Configurable colors and stroke width
   - Center text with percentage or custom content
   - Mobile-optimized touch targets

3. Props interface:
   - progress: number (0-100)
   - size: 'sm' | 'md' | 'lg' | number
   - strokeWidth?: number
   - color?: string
   - showPercentage?: boolean
   - children?: ReactNode (center content)

4. Styling requirements:
   - Use Tailwind CSS classes
   - Support dark mode
   - Smooth animations (respect prefers-reduced-motion)
   - Crisp rendering on high-DPI displays

Test with various progress values and ensure smooth animations.
```

### Prompt 4: Program Header Component

```text
Build a program header component that displays program name and category using TDD. Your task:

1. Write comprehensive tests for:
   - Display program name prominently
   - Show program category/type badge
   - Handle long program names gracefully
   - Display program icon/image if available
   - Loading skeleton state

2. Create `src/components/ProgramHeader.tsx`:
   - Large, bold program name (mobile-first typography)
   - Category badge with appropriate styling
   - Optional program image with fallback
   - Gradient background or visual interest
   - Loading skeleton for initial load

3. Props interface:
   - program: ProgramModel | undefined
   - isLoading: boolean
   - className?: string

4. Mobile optimizations:
   - Minimum 24px font size for program name
   - Touch-friendly spacing (minimum 8px padding)
   - Responsive image sizing
   - Text truncation for very long names

Requirements:
- Follow existing component patterns
- Use semantic HTML (h1 for program name)
- Implement proper loading states
- Support dark mode styling
```

### Prompt 5: Program Stats Grid Component

```text
Create a stats grid component showing workout progress metrics using TDD. Your task:

1. Write tests for:
   - Display multiple stat cards in grid
   - Show workout count, days completed, current week
   - Handle missing or zero values
   - Responsive grid layout (2x2 on mobile)
   - Loading state for each stat

2. Create `src/components/ProgramStats.tsx`:
   - Grid layout with stat cards
   - Icon + label + value for each stat
   - Animated number transitions
   - Mobile-optimized spacing
   - Skeleton loaders for initial load

3. Stat cards to include:
   - Total Workouts (completed/total)
   - Days in Program (current/total)
   - Current Week number
   - Completion percentage

4. Create `src/components/StatCard.tsx`:
   - Reusable card component
   - Icon support (using Heroicons)
   - Label and value with proper hierarchy
   - Optional trend indicator
   - Loading skeleton variant

Requirements:
- Use CSS Grid for layout
- Ensure 44px minimum touch targets
- Implement number formatting (1.2k, etc.)
- Support dark mode
```

### Prompt 6: Program Description Component

```text
Build an expandable program description component with mobile UX using TDD. Your task:

1. Write tests for:
   - Display truncated description initially
   - Expand/collapse on tap
   - Show "Read more" button when truncated
   - Handle HTML content safely
   - Smooth expand/collapse animation

2. Create `src/components/ProgramDescription.tsx`:
   - Initial 3-line truncated view
   - "Read more" button when content exceeds limit
   - Smooth height animation on expand
   - Safe HTML rendering (if needed)
   - Mobile-optimized typography

3. Features to implement:
   - Line clamping with CSS
   - Animated height transitions
   - Accessible expand/collapse
   - Touch-friendly tap targets
   - Loading skeleton state

4. Props interface:
   - description: string
   - maxLines?: number (default 3)
   - isLoading?: boolean
   - className?: string

Requirements:
- Use modern CSS for line clamping
- Implement smooth animations
- Ensure accessibility (ARIA attributes)
- Test with various content lengths
```

### Prompt 7: Program Overview Page Assembly

```text
Assemble the complete program overview page using existing components with TDD. Your task:

1. Write comprehensive tests for:
   - Page renders with all components
   - Loading state shows skeletons
   - Error state with retry option
   - Successful data display
   - Navigation to workout page
   - Back button handling

2. Create `src/app/program/page.tsx`:
   - Full-page mobile layout
   - Integrate all program components
   - "Continue to Workout" CTA button
   - Loading and error states
   - PWA safe area handling

3. Page structure:
   - ProgramHeader at top
   - ProgressRing prominently displayed
   - ProgramStats grid
   - ProgramDescription (expandable)
   - Fixed CTA button at bottom

4. Implement data fetching:
   - Use program hooks on mount
   - Show loading skeletons
   - Handle errors gracefully
   - Prefetch workout data

Requirements:
- Mobile-first responsive design
- Smooth scrolling behavior
- Fixed CTA button (always visible)
- Auth guard protection
- Performance: render < 100ms
```

### Prompt 8: Navigation Flow Integration

```text
Update the navigation flow to include program overview after login using TDD. Your task:

1. Write tests for new navigation flow:
   - Login redirects to /program
   - Program page redirects to /workout on CTA
   - Back button from workout goes to program
   - Direct /workout access redirects to /program first
   - Auth guards on both pages

2. Update `LoginSuccessScreenWithData.tsx`:
   - Change redirect from /workout to /program
   - Ensure smooth transition
   - Maintain prefetch logic

3. Update `src/app/workout/page.tsx`:
   - Add back button to program overview
   - Handle navigation state
   - Prevent data loss on back

4. Create navigation utilities:
   - `useNavigationFlow` hook
   - Constants for route paths
   - Navigation state management

Requirements:
- Maintain existing auth flow
- Preserve query parameters
- Handle edge cases (refresh, direct access)
- Test browser back/forward buttons
```

### Prompt 9: Parallel Data Prefetching

```text
Implement parallel loading of program and workout data for instant display using TDD. Your task:

1. Write tests for:
   - Parallel API calls on login
   - Program data available immediately
   - Workout data prefetched during program view
   - Cache hits for subsequent visits
   - Loading progress accuracy

2. Update `useLogin` hook:
   - Trigger program fetch on success
   - Update progress calculation
   - Handle partial failures

3. Create `useProgramPrefetch` hook:
   - Prefetch program data
   - Return loading progress
   - Handle errors gracefully
   - Integrate with success screen

4. Update `LoginSuccessScreen`:
   - Show program loading progress
   - Include in overall progress
   - Update status messages

Requirements:
- Maintain < 1s perceived load time
- Program data must be ready on redirect
- No blocking API calls
- Graceful degradation on errors
```

### Prompt 10: Program Page Loading States

```text
Create skeleton loaders for the program overview page using TDD. Your task:

1. Write tests for:
   - Skeleton components match real components
   - Smooth transition from skeleton to content
   - No layout shift during load
   - Appropriate animation timing

2. Create skeleton components:
   - `ProgramHeaderSkeleton`
   - `ProgressRingSkeleton`
   - `ProgramStatsSkeleton`
   - `ProgramDescriptionSkeleton`

3. Implement in program page:
   - Show skeletons during initial load
   - Stagger content appearance
   - Smooth fade transitions
   - Match exact dimensions

4. Animation requirements:
   - Pulse animation for skeletons
   - Fade in for real content
   - No jarring transitions
   - Respect reduced motion

Requirements:
- Zero CLS (Cumulative Layout Shift)
- Match exact component dimensions
- Consistent animation timing
- Mobile-optimized performance
```

### Prompt 11: Error Handling and Edge Cases

```text
Implement comprehensive error handling for program overview using TDD. Your task:

1. Write tests for error scenarios:
   - Program API fails
   - User has no program assigned
   - Network timeout
   - Invalid program data
   - Partial data loading failures

2. Create error components:
   - `ProgramErrorState` component
   - "No program assigned" empty state
   - Network error with retry
   - Generic error fallback

3. Implement error boundaries:
   - Wrap program page
   - Catch rendering errors
   - Provide recovery options
   - Log errors appropriately

4. Edge cases to handle:
   - User completes program
   - Program data changes mid-session
   - Expired program access
   - Missing required fields

Requirements:
- User-friendly error messages
- Always provide next actions
- Graceful degradation
- Maintain app stability
```

### Prompt 12: Program Stats Calculations

```text
Implement accurate program statistics calculations using TDD. Your task:

1. Write tests for:
   - Calculate completion percentage
   - Track workouts per week
   - Calculate days remaining
   - Handle missed workouts
   - Project completion date

2. Create `src/lib/programCalculations.ts`:
   - `calculateProgress(program, workouts)`
   - `getWeekNumber(startDate, currentDate)`
   - `calculateCompletionDate(program, progress)`
   - `getWorkoutFrequency(workouts)`

3. Integrate calculations:
   - Use in ProgramStats component
   - Update progress in real-time
   - Cache calculated values
   - Handle edge cases

4. Test scenarios:
   - New program (0% complete)
   - Mid-program progress
   - Completed program (100%)
   - Irregular workout patterns

Requirements:
- Accurate date calculations
- Handle timezone differences
- Account for rest days
- Performance: < 10ms calculations
```

### Prompt 13: Local Storage and Caching

```text
Implement program data caching for instant display using TDD. Your task:

1. Write tests for:
   - Save program data to localStorage
   - Load cached data on mount
   - Validate cache freshness
   - Clear stale data
   - Handle storage quota errors

2. Update program store:
   - Add cache management
   - Implement TTL (time-to-live)
   - Background refresh logic
   - Cache invalidation

3. Create cache utilities:
   - `saveProgramCache(data)`
   - `loadProgramCache()`
   - `isProgramCacheValid(timestamp)`
   - `clearProgramCache()`

4. Integration points:
   - Load cache before API call
   - Update cache on successful fetch
   - Show cached data immediately
   - Refresh in background

Requirements:
- 24-hour cache validity
- Graceful quota handling
- Compress data if needed
- Clear old cache entries
```

### Prompt 14: UI Improvements and Simplification

```text
Improve the Program Overview Page UI for better user experience using TDD. Your task:

1. Write tests for UI improvements:
   - Button visibility and styling
   - Simplified header without image
   - Exercise count display
   - Reduced stats grid (2 metrics only)
   - Animated workout counter
   - Scrollable page layout
   - Workout page cleanup

2. Fix "Continue to Workout" button visibility:
   - Investigate why button is invisible (but functional)
   - Ensure proper background color (primary-600)
   - Fix text color contrast (white text)
   - Verify button height (min-height: 56px)
   - Test touch target size (44px minimum)

3. Update ProgramHeader component:
   - Remove image/gradient section (lines 70-89)
   - Keep program name (h1) and category badge
   - Add exercise count below name: "XX exercises"
   - Get count from NextWorkoutTemplate?.Exercises?.length
   - Handle loading state for exercise count

4. Create AnimatedCounter component:
   - Large number display for total workouts
   - Animate from 0 to TotalWorkoutCompleted over 1s
   - Use CSS transitions or framer-motion
   - Display "Workouts Completed" label below
   - Replace existing ProgressRing component

5. Simplify ProgramStatsGrid:
   - Remove all 4 current stat cards
   - Show only 2 metrics:
     * Workout Streak: ConsecutiveWeeks + " weeks"
     * Last Workout: format LastWorkoutDateStr nicely
   - Ensure proper loading states
   - Handle missing data gracefully

6. Fix page scrollability:
   - Ensure content area is scrollable
   - Keep "Continue to Workout" button fixed at bottom
   - Test on various mobile viewport sizes
   - Verify no content is cut off

7. Clean up WorkoutOverview page:
   - Remove workout info card (lines 204-209)
   - This shows redundant "[program name]" and "XX exercises"
   - Keep only the exercise list and "Start Workout" button
   - Maintain existing navigation and functionality

Requirements:
- All changes must pass existing tests
- Maintain mobile-first design principles
- No breaking changes to navigation flow
- Preserve all data fetching logic
- Keep loading and error states
```

### Prompt 15: Performance Monitoring

```text
Add performance monitoring to program overview page using TDD. Your task:

1. Write tests for:
   - Measure page load time
   - Track component render times
   - Monitor API call duration
   - Check animation performance
   - Verify no memory leaks

2. Add User Timing API marks:
   - Program page start/end
   - Component mount times
   - Data fetch duration
   - First contentful paint

3. Create performance utilities:
   - `measureProgramPageLoad()`
   - `trackComponentPerformance()`
   - `reportProgramMetrics()`

4. Performance targets:
   - Page interactive < 1s
   - Smooth 60fps animations
   - No dropped frames
   - Memory stable over time

Requirements:
- Use Performance Observer API
- Log metrics to console (dev)
- Send to analytics (production)
- No performance overhead
```

### Prompt 16: Final Integration and Polish

```text
Complete the program overview feature with final polish and E2E tests using TDD. Your task:

1. Write E2E tests for complete flow:
   - Login → Program → Workout journey
   - All interactions work correctly
   - Performance meets targets
   - Works offline after first load
   - Handles all error cases

2. Polish items:
   - Add haptic feedback for interactions
   - Implement pull-to-refresh
   - Add success animations
   - Fine-tune transitions
   - Ensure accessibility

3. Code cleanup:
   - Remove console.logs
   - Optimize bundle size
   - Tree-shake unused code
   - Update documentation

4. Final checklist:
   - All tests passing
   - No TypeScript errors
   - Zero console warnings
   - Performance targets met
   - Mobile UX excellent

Requirements:
- E2E tests cover all scenarios
- Code review ready
- Documentation complete
- Ready for production
```

## Success Metrics

### Performance

- Program page loads in < 1 second after login
- Zero layout shift during loading
- Smooth 60fps animations
- < 50KB additional JavaScript

### User Experience

- Clear program information display
- Intuitive navigation flow
- Excellent mobile touch UX
- Graceful error handling

### Code Quality

- 100% test coverage for critical paths
- Zero TypeScript errors
- Clean, maintainable code
- No orphaned features

## Implementation Timeline

Estimated time: 6-8 hours for complete implementation

- Phase 1 (Data Layer): 1.5 hours
- Phase 2 (UI Components): 2 hours
- Phase 3 (Page Implementation): 1.5 hours
- Phase 4 (Navigation): 1 hour
- Phase 5 (Performance): 1-2 hours

## Notes for Implementation

1. **Always start with tests** - Write failing tests before any implementation
2. **Mobile-first design** - Every decision should prioritize mobile UX
3. **Incremental progress** - Complete each prompt fully before moving on
4. **Performance focus** - Monitor bundle size and runtime performance
5. **No orphaned code** - Every feature must be fully integrated

This plan provides a complete blueprint for building the Program Overview Page with zero ambiguity and maximum efficiency.
