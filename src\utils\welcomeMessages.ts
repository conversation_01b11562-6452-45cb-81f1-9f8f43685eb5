interface UserStatsData {
  streak?: number
  totalWorkouts?: number
  currentLevel?: string
  lastWorkoutDate?: Date
}

interface FormattedUserStats {
  streakText: string
  workoutCountText: string
  levelText: string
  timeSinceLastWorkout: string
}

/**
 * Get a time-appropriate welcome message
 */
export function getWelcomeMessage(userName?: string): string {
  const hour = new Date().getHours()
  const name = userName || 'Champion'

  let timeGreeting: string
  if (hour < 12) {
    timeGreeting = 'Good morning'
  } else if (hour < 17) {
    timeGreeting = 'Good afternoon'
  } else {
    timeGreeting = 'Good evening'
  }

  // For time-based greetings, prioritize them when appropriate
  if (Math.random() < 0.7) {
    // 70% chance to use time-based greeting
    if (hour < 12) return `Good morning, ${name}!`
    if (hour < 17) return `Good afternoon, ${name}!`
    return `Good evening, ${name}!`
  }

  const greetings = [
    `Welcome back, ${name}!`,
    `Ready to crush it, ${name}?`,
    `Let's do this, ${name}!`,
    `${timeGreeting}! Time to train, ${name}!`,
  ]

  // Use Math.random to select a greeting
  const index = Math.floor(Math.random() * greetings.length)
  return greetings[index] || `Welcome back, ${name}!`
}

/**
 * Get a motivational message based on user stats
 */
export function getMotivationalMessage(stats: UserStatsData = {}): string {
  const { streak = 0, totalWorkouts = 0, lastWorkoutDate } = stats

  // Calculate days since last workout
  let daysSinceLastWorkout = null
  if (lastWorkoutDate) {
    const today = new Date()
    const lastWorkout = new Date(lastWorkoutDate)
    daysSinceLastWorkout = Math.floor(
      (today.getTime() - lastWorkout.getTime()) / (1000 * 60 * 60 * 24)
    )
  }

  // Check for workout after break first (has priority)
  if (daysSinceLastWorkout && daysSinceLastWorkout > 7) {
    const comebackMessages = [
      '🔥 Welcome back! Ready to get back on track?',
      "💪 Great to see you return! Let's do this!",
      '🎯 Back in action! Time to rebuild momentum!',
    ]
    const index = Math.floor(Math.random() * comebackMessages.length)
    return (
      comebackMessages[index] || '🔥 Welcome back! Ready to get back on track?'
    )
  }

  // Streak-based messages (with some randomization)
  if (streak > 30) {
    const messages = [
      `🔥 ${streak} day streak! You're unstoppable!`,
      `🌟 ${streak} days of dedication! Incredible!`,
    ]
    return (
      messages[Math.floor(Math.random() * messages.length)] ||
      `🔥 ${streak} day streak! You're unstoppable!`
    )
  } else if (streak > 7) {
    const messages = [
      `💪 ${streak} days strong! Keep the momentum going!`,
      `🎯 ${streak} day streak! You're on fire!`,
    ]
    return (
      messages[Math.floor(Math.random() * messages.length)] ||
      `💪 ${streak} days strong! Keep the momentum going!`
    )
  } else if (streak > 0) {
    const messages = [
      `🎯 ${streak} day streak! Building great habits!`,
      `💪 ${streak} days in a row! Keep it up!`,
    ]
    return (
      messages[Math.floor(Math.random() * messages.length)] ||
      `🎯 ${streak} day streak! Building great habits!`
    )
  }

  // Milestone messages
  if (totalWorkouts === 1) {
    return '🎉 Your fitness journey begins today!'
  } else if (totalWorkouts === 50) {
    return "🏆 50 workouts completed! You're crushing it!"
  } else if (totalWorkouts === 100) {
    return '💯 100 workouts! Incredible dedication!'
  } else if (totalWorkouts === 500) {
    return "🌟 500 workouts! You're a legend!"
  } else if (totalWorkouts % 100 === 0 && totalWorkouts > 0) {
    return `🎊 ${totalWorkouts} workouts! Amazing milestone!`
  }

  // Recent workout messages
  if (daysSinceLastWorkout === 0) {
    return "💪 Back for another session! Let's go!"
  }

  // Default motivational messages
  const messages = [
    '💪 Time to become stronger than yesterday!',
    '🚀 Your future self will thank you!',
    '🏋️ Every rep counts towards your goals!',
    "⚡ Let's make today count!",
    '🎯 Focus. Execute. Achieve.',
    '💥 Time to push your limits!',
  ]

  const index = Math.floor(Math.random() * messages.length)
  return messages[index] || '💪 Time to become stronger than yesterday!'
}

/**
 * Format user stats for display
 */
export function getUserStats(stats: UserStatsData = {}): FormattedUserStats {
  const { streak = 0, totalWorkouts = 0, currentLevel, lastWorkoutDate } = stats

  // Format streak text
  const streakText =
    streak > 0 ? `${streak} day${streak === 1 ? '' : ''} streak` : 'New journey'

  // Format workout count
  const workoutCountText =
    totalWorkouts > 0
      ? `${totalWorkouts.toLocaleString()} workout${totalWorkouts === 1 ? '' : 's'}`
      : 'First workout'

  // Format level
  const levelText = currentLevel ? `${currentLevel} athlete` : 'Ready to start'

  // Calculate time since last workout
  let timeSinceLastWorkout = 'Welcome!'
  if (lastWorkoutDate) {
    const today = new Date()
    const lastWorkout = new Date(lastWorkoutDate)
    const daysSince = Math.floor(
      (today.getTime() - lastWorkout.getTime()) / (1000 * 60 * 60 * 24)
    )

    if (daysSince === 0) {
      timeSinceLastWorkout = 'Working out today'
    } else if (daysSince === 1) {
      timeSinceLastWorkout = 'Yesterday'
    } else {
      timeSinceLastWorkout = `${daysSince} days ago`
    }
  }

  return {
    streakText,
    workoutCountText,
    levelText,
    timeSinceLastWorkout,
  }
}
