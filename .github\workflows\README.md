# CI/CD Pipeline Documentation

## Overview

This repository uses a comprehensive GitHub Actions CI/CD pipeline that ensures code quality, security, and performance. The pipeline runs automatically on every push to main/develop branches and on all pull requests.

## Pipeline Architecture

The pipeline runs multiple jobs in parallel for fast feedback:

### Parallel Jobs (Fast Feedback)

#### 1. Code Quality Checks
- **TypeScript Validation**: Type checking with `tsc --noEmit`
- **ESLint**: Code quality and style enforcement
- **Prettier**: Code formatting verification

#### 2. Security Checks
- **Dependency Audit**: `npm audit` for vulnerability scanning
- **Security Linting**: ESLint security plugin checks
- **Secret Scanning**: TruffleHog scans for exposed secrets
- **License Compliance**: Checks for GPL and other restricted licenses

### Sequential Jobs

#### 3. Unit Tests & Coverage
- Runs all unit/integration tests with Vitest
- Enforces 80% coverage threshold
- Generates coverage reports for PR comments
- Fails if coverage drops below threshold

#### 4. Build & Bundle Analysis
- Builds the Next.js application
- Analyzes bundle size (must be < 150KB)
- Caches build artifacts for subsequent jobs
- Uses Next.js build cache for faster builds

#### 5. Critical Path E2E Tests
- Tests core user journeys with @critical tag
- Mobile-first testing on multiple devices:
  - iPhone 13 (390x844)
  - Pixel 5 (393x851)
  - Galaxy S9+ (360x740)
- Validates:
  - Login flow
  - Workout creation
  - 44px touch targets
  - PWA features
  - Performance metrics

#### 6. Full E2E Suite (Main branch & PRs)
- Comprehensive E2E testing
- All test scenarios including edge cases
- Mobile-specific gesture testing

#### 7. Performance Checks
- Lighthouse CI for performance monitoring
- Enforces:
  - First Contentful Paint < 1s
  - Time to Interactive < 1.5s
  - Mobile performance score > 90
  - Bundle size < 150KB

#### 8. Accessibility Tests
- Automated WCAG compliance testing
- Checks for:
  - Touch target sizes (44px minimum)
  - Color contrast
  - Alt text
  - Form labels
  - Keyboard navigation
  - ARIA landmarks

#### 9. API Contract Tests
- Validates API request/response shapes
- Ensures frontend-backend compatibility
- Tests error response formats
- Validates pagination contracts

## Pipeline Flow

```
Push/PR Trigger
    │
    ├─── Code Quality ──────┐
    ├─── Security Checks ───┤
    └─── Build & Analyze ───┤
                            │
                            ▼
                     Unit Tests & Coverage
                            │
                            ▼
                     Critical E2E Tests
                            │
                            ├─── Full E2E (conditional)
                            ├─── Performance Tests
                            ├─── Accessibility Tests
                            └─── API Contract Tests
                                      │
                                      ▼
                              Coverage Report (PR only)
                              Deploy Preview (PR only)
```

## Success Criteria

The pipeline will **FAIL** if:
- ❌ TypeScript errors exist
- ❌ ESLint errors (not warnings)
- ❌ Code formatting issues
- ❌ Any test fails
- ❌ Coverage drops below 80%
- ❌ Bundle exceeds 150KB
- ❌ Lighthouse mobile score < 90
- ❌ Security vulnerabilities found (moderate+)
- ❌ Build fails
- ❌ Critical E2E tests fail

## Artifacts

The pipeline generates:
- **Coverage reports**: HTML coverage with file-by-file breakdown
- **Build output**: Compiled Next.js application
- **Playwright reports**: Test results with screenshots/videos on failure
- **Lighthouse reports**: Performance metrics and suggestions
- **Bundle analysis**: Size breakdown by module

## Local Testing

Run the same checks locally:

```bash
# Code Quality
npm run typecheck           # TypeScript validation
npm run lint               # ESLint checks
npm run prettier:check     # Formatting check

# Security
npm audit                  # Dependency vulnerabilities
npm run lint:security      # Security linting
npm run audit:licenses     # License compliance

# Tests
npm run test:ci            # Unit tests
npm run test:coverage      # Tests with coverage
npm run test:e2e:critical  # Critical E2E tests only
npm run test:e2e:full      # Full E2E suite
npm run test:accessibility # A11y tests
npm run test:api:contracts # API contract tests

# Build & Performance
npm run build              # Production build
npm run analyze            # Bundle analysis
npm run lighthouse:ci      # Performance testing
```

## Configuration Files

- `.github/workflows/ci.yml`: Main CI/CD workflow
- `playwright.ci.config.ts`: CI-specific E2E configuration
- `playwright.a11y.config.ts`: Accessibility test configuration
- `vitest.config.mjs`: Unit test configuration with coverage
- `vitest.api.config.ts`: API contract test configuration
- `lighthouse-ci.config.js`: Performance testing thresholds

## Caching Strategy

The pipeline uses caching for:
- npm dependencies (`~/.npm`)
- Next.js build cache (`.next/cache`)
- Playwright browsers

## Troubleshooting

### Coverage Failures
```bash
npm run test:coverage
# Open coverage/index.html for detailed report
```

### E2E Test Failures
```bash
npm run test:e2e:ui        # Interactive debugging
# Check artifacts in GitHub Actions for screenshots
```

### Performance Failures
```bash
npm run lighthouse:mobile  # Test locally
npm run analyze           # Check bundle size
```

### Security Issues
```bash
npm audit fix             # Auto-fix vulnerabilities
npm run lint:security     # Check security issues
```

## Branch Protection

Recommended branch protection rules:
- Require PR reviews before merging
- Require status checks to pass:
  - `code-quality`
  - `security-checks`
  - `test-unit`
  - `build-and-analyze`
  - `e2e-critical`
- Require branches to be up to date
- Include administrators in restrictions

## Performance Optimizations

1. **Parallel Execution**: Independent jobs run simultaneously
2. **Smart Caching**: Dependencies and builds are cached
3. **Conditional Jobs**: Full E2E only on main/PR
4. **Fast Feedback**: Quick checks run first
5. **Self-hosted Runner**: Uses macOS runner for consistency