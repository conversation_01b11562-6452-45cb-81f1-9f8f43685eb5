import React from 'react'

interface DurationInputProps {
  duration: number
  onChange: (value: string) => void
  disabled?: boolean
  error?: string
  quickButtons?: number[]
}

const DEFAULT_QUICK_BUTTONS = [30, 45, 60, 90]

export function DurationInput({
  duration,
  onChange,
  disabled = false,
  error,
  quickButtons = DEFAULT_QUICK_BUTTONS,
}: DurationInputProps) {
  return (
    <div>
      <label
        htmlFor="duration-input"
        className="block text-sm font-medium text-gray-700 mb-2"
      >
        Duration
      </label>
      <div className="flex items-center gap-2">
        <input
          id="duration-input"
          type="number"
          value={duration}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          min={1}
          max={300}
          inputMode="numeric"
          className={`flex-1 px-4 py-3 text-lg border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            error ? 'border-red-500' : 'border-gray-300'
          } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
          aria-label="Duration"
          aria-invalid={!!error}
          aria-describedby={error ? 'duration-error' : undefined}
        />
        <span className="text-gray-600">seconds</span>
      </div>
      {error && (
        <p
          id="duration-error"
          className="mt-1 text-sm text-red-600"
          role="alert"
        >
          {error}
        </p>
      )}
      <div className="flex gap-2 mt-3">
        {quickButtons.map((quickDuration) => (
          <button
            key={quickDuration}
            type="button"
            onClick={() => onChange(quickDuration.toString())}
            disabled={disabled}
            className={`px-3 py-1 text-sm border rounded-md ${
              duration === quickDuration
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-gray-700 border-gray-300'
            } ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'}`}
          >
            {quickDuration}s
          </button>
        ))}
      </div>
    </div>
  )
}
