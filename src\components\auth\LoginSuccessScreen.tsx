'use client'

import { useState, useEffect, useRef } from 'react'
import {
  SuccessIcon,
  SuccessMessage,
  LoadingProgress,
} from '@/components/workout'

interface LoginSuccessScreenProps {
  /** Callback when the success animation completes */
  onComplete?: () => void
  /** Additional CSS classes */
  className?: string
}

const PROGRESS_DURATION = 1000 // Reduced from 2500 to 1000 (1 second)
const PROGRESS_INTERVAL = 50 // Update every 50ms

export function LoginSuccessScreen({
  onComplete,
  className = '',
}: LoginSuccessScreenProps) {
  const [progress, setProgress] = useState(0)
  const [loadingStatus, setLoadingStatus] = useState('Loading your workout...')
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = useRef<number>(Date.now())
  const hasCompletedRef = useRef<boolean>(false)

  // Determine welcome message
  const welcomeTitle = 'Welcome back!'

  // Update loading status based on progress
  useEffect(() => {
    if (progress < 30) {
      setLoadingStatus('Loading your workout...')
    } else if (progress < 60) {
      setLoadingStatus('Preparing your exercises...')
    } else if (progress < 90) {
      setLoadingStatus('Almost ready...')
    } else {
      setLoadingStatus('Starting your workout!')
    }
  }, [progress])

  // Animate progress
  useEffect(() => {
    startTimeRef.current = Date.now()

    intervalRef.current = setInterval(() => {
      const elapsed = Date.now() - startTimeRef.current
      const newProgress = Math.min(100, (elapsed / PROGRESS_DURATION) * 100)

      setProgress(newProgress)

      if (newProgress >= 100) {
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
          intervalRef.current = null
        }
      }
    }, PROGRESS_INTERVAL)

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  // Handle animation completion
  const handleIconAnimationComplete = () => {
    // Wait for progress to complete before calling onComplete
    if (progress >= 100 && !hasCompletedRef.current && onComplete) {
      hasCompletedRef.current = true
      onComplete()
    }
  }

  // Call onComplete when progress reaches 100%
  useEffect(() => {
    if (progress >= 100 && !hasCompletedRef.current && onComplete) {
      hasCompletedRef.current = true
      // Use requestAnimationFrame to avoid XHR violation
      requestAnimationFrame(() => {
        onComplete()
      })
    }
  }, [progress, onComplete])

  return (
    <div
      data-testid="login-success-screen"
      className={`
        min-h-[100dvh]
        flex flex-col items-center justify-center
        bg-white dark:bg-gray-900
        px-6 py-8
        ${className}
      `}
    >
      <div
        data-testid="login-success-content"
        className="max-w-md w-full space-y-8"
      >
        {/* Success Icon */}
        <div className="flex justify-center">
          <SuccessIcon
            size={140}
            onAnimationComplete={handleIconAnimationComplete}
          />
        </div>

        {/* Welcome Message */}
        <SuccessMessage title={welcomeTitle} autoPlay />

        {/* Loading Progress */}
        <div className="mt-8">
          <LoadingProgress
            progress={progress}
            status={loadingStatus}
            showPercentage
          />
        </div>
      </div>
    </div>
  )
}
