/**
 * Bundle size analyzer helper
 */
export function analyzeBundleSize() {
  if (typeof window !== 'undefined' && window.performance) {
    const resources = window.performance.getEntriesByType('resource')
    const jsResources = resources.filter((r) => r.name.endsWith('.js'))

    const totalSize = jsResources.reduce((sum, resource) => {
      const resourceTiming = resource as PerformanceResourceTiming
      return sum + (resourceTiming.transferSize || 0)
    }, 0)

    const stats = {
      totalJSSize: totalSize,
      jsFiles: jsResources.length,
      largestFile: Math.max(
        ...jsResources.map(
          (r) => (r as PerformanceResourceTiming).transferSize || 0
        )
      ),
      resources: jsResources.map((r) => {
        const resourceTiming = r as PerformanceResourceTiming
        return {
          name: r.name.split('/').pop(),
          size: resourceTiming.transferSize || 0,
          duration: r.duration,
        }
      }),
    }

    // Development mode performance tracking

    return stats
  }

  return null
}
