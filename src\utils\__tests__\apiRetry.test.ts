import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { AxiosError } from 'axios'
import {
  retryApiCall,
  shouldClearCacheOnError,
  USER_INFO_RETRY_OPTIONS,
  STATS_RETRY_OPTIONS,
} from '../apiRetry'
import { userInfoPerformance } from '../userInfoPerformance'

// Mock userInfoPerformance
vi.mock('../userInfoPerformance', () => ({
  userInfoPerformance: {
    trackRetry: vi.fn(),
  },
}))

describe('apiRetry', () => {
  beforeEach(() => {
    vi.useFakeTimers()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('retryApiCall', () => {
    it('should execute API call successfully without retry', async () => {
      const apiCall = vi.fn().mockResolvedValue({ data: 'success' })

      const result = await retryApiCall(apiCall)

      expect(result).toEqual({ data: 'success' })
      expect(apiCall).toHaveBeenCalledTimes(1)
      expect(userInfoPerformance.trackRetry).not.toHaveBeenCalled()
    })

    it('should retry on failure with exponential backoff', async () => {
      const apiCall = vi
        .fn()
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue({ data: 'success' })

      const promise = retryApiCall(apiCall, USER_INFO_RETRY_OPTIONS)

      // First attempt fails immediately
      await vi.advanceTimersByTimeAsync(0)
      expect(apiCall).toHaveBeenCalledTimes(1)

      // First retry after ~1 second
      await vi.advanceTimersByTimeAsync(1200) // 1000ms + jitter
      expect(apiCall).toHaveBeenCalledTimes(2)
      expect(userInfoPerformance.trackRetry).toHaveBeenCalledWith('userInfo', 1)

      // Second retry after ~2 seconds
      await vi.advanceTimersByTimeAsync(2400) // 2000ms + jitter
      expect(apiCall).toHaveBeenCalledTimes(3)
      expect(userInfoPerformance.trackRetry).toHaveBeenCalledWith('userInfo', 2)

      const result = await promise
      expect(result).toEqual({ data: 'success' })
    })

    it.skip('should not retry on 4xx errors (except 408 and 429)', async () => {
      const error400 = new AxiosError('Bad Request')
      error400.response = { status: 400 } as any

      const apiCall = vi.fn().mockRejectedValue(error400)

      await expect(retryApiCall(apiCall)).rejects.toThrow('Bad Request')
      expect(apiCall).toHaveBeenCalledTimes(1)
      expect(userInfoPerformance.trackRetry).not.toHaveBeenCalled()
    }, 10000)

    it('should retry on 408 (Request Timeout) errors', async () => {
      const error408 = new AxiosError('Request Timeout')
      error408.response = { status: 408 } as any

      const apiCall = vi
        .fn()
        .mockRejectedValueOnce(error408)
        .mockResolvedValue({ data: 'success' })

      const promise = retryApiCall(apiCall)

      await vi.advanceTimersByTimeAsync(0)
      await vi.advanceTimersByTimeAsync(1200)

      const result = await promise
      expect(result).toEqual({ data: 'success' })
      expect(apiCall).toHaveBeenCalledTimes(2)
    })

    it('should retry on 429 (Rate Limit) errors', async () => {
      const error429 = new AxiosError('Too Many Requests')
      error429.response = { status: 429 } as any

      const apiCall = vi
        .fn()
        .mockRejectedValueOnce(error429)
        .mockResolvedValue({ data: 'success' })

      const promise = retryApiCall(apiCall)

      await vi.advanceTimersByTimeAsync(0)
      await vi.advanceTimersByTimeAsync(1200)

      const result = await promise
      expect(result).toEqual({ data: 'success' })
      expect(apiCall).toHaveBeenCalledTimes(2)
    })

    it('should retry on 5xx server errors', async () => {
      const error500 = new AxiosError('Internal Server Error')
      error500.response = { status: 500 } as any

      const apiCall = vi
        .fn()
        .mockRejectedValueOnce(error500)
        .mockResolvedValue({ data: 'success' })

      const promise = retryApiCall(apiCall)

      await vi.advanceTimersByTimeAsync(0)
      await vi.advanceTimersByTimeAsync(1200)

      const result = await promise
      expect(result).toEqual({ data: 'success' })
      expect(apiCall).toHaveBeenCalledTimes(2)
    })

    it.skip('should timeout after specified duration', async () => {
      const apiCall = vi
        .fn()
        .mockImplementation(
          () => new Promise((resolve) => setTimeout(resolve, 15000))
        )

      const promise = retryApiCall(apiCall, USER_INFO_RETRY_OPTIONS, 5000)

      await vi.advanceTimersByTimeAsync(5001)

      await expect(promise).rejects.toThrow('API call timed out after 5000ms')
      expect(apiCall).toHaveBeenCalledTimes(1)
    }, 10000)

    it.skip('should handle abort during retry', async () => {
      const apiCall = vi
        .fn()
        .mockRejectedValueOnce(new Error('Network error'))
        .mockImplementation(
          () => new Promise((resolve) => setTimeout(resolve, 20000))
        )

      const promise = retryApiCall(apiCall, USER_INFO_RETRY_OPTIONS, 2000)

      // First attempt fails
      await vi.advanceTimersByTimeAsync(0)

      // Start retry delay
      await vi.advanceTimersByTimeAsync(1000)

      // Timeout during second attempt
      await vi.advanceTimersByTimeAsync(1001)

      await expect(promise).rejects.toThrow('API call timed out after 2000ms')
    }, 10000)

    it.skip('should use default timeout of 10 seconds', async () => {
      const apiCall = vi
        .fn()
        .mockImplementation(
          () => new Promise((resolve) => setTimeout(resolve, 15000))
        )

      const promise = retryApiCall(apiCall)

      await vi.advanceTimersByTimeAsync(10001)

      await expect(promise).rejects.toThrow('API call timed out after 10000ms')
    }, 15000)

    it('should track stats API retries correctly', async () => {
      const apiCall = vi
        .fn()
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue({ data: 'stats' })

      const promise = retryApiCall(apiCall, STATS_RETRY_OPTIONS)

      await vi.advanceTimersByTimeAsync(0)
      await vi.advanceTimersByTimeAsync(1200)

      await promise

      expect(userInfoPerformance.trackRetry).toHaveBeenCalledWith('stats', 1)
    })
  })

  describe('shouldClearCacheOnError', () => {
    it('should return false for non-Error values', () => {
      expect(shouldClearCacheOnError(null)).toBe(false)
      expect(shouldClearCacheOnError(undefined)).toBe(false)
      expect(shouldClearCacheOnError('string')).toBe(false)
      expect(shouldClearCacheOnError(123)).toBe(false)
    })

    it('should return true for 401 Unauthorized errors', () => {
      const error = new AxiosError('Unauthorized')
      error.response = { status: 401 } as any

      expect(shouldClearCacheOnError(error)).toBe(true)
    })

    it('should return true for 403 Forbidden errors', () => {
      const error = new AxiosError('Forbidden')
      error.response = { status: 403 } as any

      expect(shouldClearCacheOnError(error)).toBe(true)
    })

    it('should return false for 5xx server errors', () => {
      const error = new AxiosError('Server Error')
      error.response = { status: 500 } as any

      expect(shouldClearCacheOnError(error)).toBe(false)
    })

    it('should return false for 408 timeout errors', () => {
      const error = new AxiosError('Request Timeout')
      error.response = { status: 408 } as any

      expect(shouldClearCacheOnError(error)).toBe(false)
    })

    it('should return false for 429 rate limit errors', () => {
      const error = new AxiosError('Too Many Requests')
      error.response = { status: 429 } as any

      expect(shouldClearCacheOnError(error)).toBe(false)
    })

    it('should return false for timeout errors', () => {
      const error = new Error('Request timeout')
      error.name = 'TimeoutError'

      expect(shouldClearCacheOnError(error)).toBe(false)
    })

    it('should return false for network errors', () => {
      const error1 = new Error('Network request failed')
      expect(shouldClearCacheOnError(error1)).toBe(false)

      const error2 = new Error('Connection timeout')
      expect(shouldClearCacheOnError(error2)).toBe(false)

      const error3 = new Error('Something went wrong')
      error3.name = 'NetworkError'
      expect(shouldClearCacheOnError(error3)).toBe(false)
    })

    it('should return true for other errors', () => {
      const error = new Error('Invalid data')
      expect(shouldClearCacheOnError(error)).toBe(true)
    })
  })

  describe('retry options', () => {
    it('should have correct UserInfo retry options', () => {
      expect(USER_INFO_RETRY_OPTIONS).toEqual({
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 4000,
        factor: 2,
        jitter: 0.2,
        onRetry: expect.any(Function),
      })
    })

    it('should have correct Stats retry options', () => {
      expect(STATS_RETRY_OPTIONS).toEqual({
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 4000,
        factor: 2,
        jitter: 0.2,
        onRetry: expect.any(Function),
      })
    })
  })
})
