/**
 * Firebase OAuth Helper for Dr. Muscle X
 *
 * Implements OAuth authentication using Firebase Auth SDK
 * This replaces the direct OAuth implementations and provides
 * a unified interface for both Google and Apple Sign-In
 */

import {
  GoogleAuthProvider,
  OAuthProvider,
  signInWithPopup,
  signInWithRedirect,
  getRedirectResult,
  User,
  UserCredential,
  AuthError,
} from 'firebase/auth'
import type {
  OAuthSuccessCallback,
  OAuthErrorCallback,
  OAuthProvider as OAuthProviderType,
} from '../../types/oauth'
import { getFirebaseAuth } from '../../config/firebase'
import { PerformanceMonitor } from '../performance'
import { FirebaseProviders } from './providers/firebaseProviders'
import { AuthHandlers } from './handlers/authHandlers'

/**
 * Firebase OAuth Helper class
 * Provides unified OAuth implementation using Firebase Auth
 */
export class FirebaseOAuthHelper {
  /**
   * Initialize Firebase OAuth providers
   */
  static async initialize(): Promise<void> {
    await FirebaseProviders.initialize()
  }

  /**
   * Sign in with Google using Firebase Auth
   */
  static async signInWithGoogle(
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback,
    usePopup = true
  ): Promise<void> {
    await this.signIn(
      'google',
      FirebaseProviders.getGoogleProvider(),
      onSuccess,
      onError,
      usePopup
    )
  }

  /**
   * Sign in with Apple using Firebase Auth
   */
  static async signInWithApple(
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback,
    usePopup = true
  ): Promise<void> {
    await this.signIn(
      'apple',
      FirebaseProviders.getAppleProvider(),
      onSuccess,
      onError,
      usePopup
    )
  }

  /**
   * Generic sign in method for any OAuth provider
   */
  private static async signIn(
    providerType: OAuthProviderType,
    provider: GoogleAuthProvider | OAuthProvider,
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback,
    usePopup = true
  ): Promise<void> {
    try {
      PerformanceMonitor.mark(`firebase-oauth-${providerType}-start`)

      // Ensure initialized
      if (!FirebaseProviders.isReady()) {
        await FirebaseProviders.initialize()
      }

      const auth = getFirebaseAuth()
      let userCredential: UserCredential

      if (usePopup) {
        userCredential = await signInWithPopup(auth, provider)
      } else {
        await signInWithRedirect(auth, provider)
        return
      }

      await AuthHandlers.handleAuthSuccess(
        userCredential,
        providerType,
        onSuccess,
        onError
      )

      PerformanceMonitor.mark(`firebase-oauth-${providerType}-complete`)
      PerformanceMonitor.measure(
        `firebase-oauth-${providerType}`,
        `firebase-oauth-${providerType}-start`,
        `firebase-oauth-${providerType}-complete`
      )
    } catch (error) {
      AuthHandlers.handleAuthError(error as AuthError, providerType, onError)
    }
  }

  /**
   * Handle redirect result (for redirect flow)
   */
  static async handleRedirectResult(
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback
  ): Promise<void> {
    try {
      const auth = getFirebaseAuth()
      const result = await getRedirectResult(auth)

      if (result) {
        const providerType =
          result.providerId === 'apple.com' ? 'apple' : 'google'
        await AuthHandlers.handleAuthSuccess(
          result,
          providerType,
          onSuccess,
          onError
        )
      }
    } catch (error) {
      const authError = error as AuthError
      const providerType = authError.customData?.appName?.includes('apple')
        ? 'apple'
        : 'google'
      AuthHandlers.handleAuthError(authError, providerType, onError)
    }
  }

  /**
   * Sign out current user
   */
  static async signOut(): Promise<void> {
    const auth = getFirebaseAuth()
    await auth.signOut()
  }

  /**
   * Get current authenticated user
   */
  static getCurrentUser(): User | null {
    const auth = getFirebaseAuth()
    return auth.currentUser
  }

  /**
   * Check if Firebase OAuth is initialized
   */
  static isReady(): boolean {
    return FirebaseProviders.isReady()
  }
}

// Export for window access in development
if (typeof window !== 'undefined' && process.env.NODE_ENV !== 'production') {
  ;(
    window as Window & { FirebaseOAuthHelper?: typeof FirebaseOAuthHelper }
  ).FirebaseOAuthHelper = FirebaseOAuthHelper
}
