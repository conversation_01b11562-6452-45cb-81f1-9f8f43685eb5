import { describe, it, expect } from 'vitest'
import { extractExercisesFromWorkoutGroups } from '../useWorkoutDataLoader'
import type { WorkoutTemplateGroupModel, ExerciseModel } from '@/types'

describe('extractExercisesFromWorkoutGroups', () => {
  it('should return empty array when no groups', () => {
    expect(extractExercisesFromWorkoutGroups(null)).toEqual([])
    expect(extractExercisesFromWorkoutGroups([])).toEqual([])
  })

  it('should extract exercises from English field name (Exercises)', () => {
    const mockGroups: WorkoutTemplateGroupModel[] = [
      {
        Id: 1,
        Label: 'Day 1',
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout 1',
            Exercises: [
              { Id: 1, Label: 'Bench Press' } as ExerciseModel,
              { Id: 2, Label: 'Squat' } as ExerciseModel,
            ],
          },
        ],
      },
    ]

    const result = extractExercisesFromWorkoutGroups(mockGroups)
    expect(result).toHaveLength(2)
    expect(result[0].Label).toBe('Bench Press')
    expect(result[1].Label).toBe('Squat')
  })

  it('should extract exercises from French field name (Exercices)', () => {
    const mockGroups: any = [
      {
        Id: 1,
        Label: 'Day 1',
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout 1',
            Exercices: [
              { Id: 1, Label: 'Bench Press' },
              { Id: 2, Label: 'Squat' },
            ],
          },
        ],
      },
    ]

    const result = extractExercisesFromWorkoutGroups(mockGroups)
    expect(result).toHaveLength(2)
    expect(result[0].Label).toBe('Bench Press')
    expect(result[1].Label).toBe('Squat')
  })

  it('should return empty array when no exercises in template', () => {
    const mockGroups: WorkoutTemplateGroupModel[] = [
      {
        Id: 1,
        Label: 'Day 1',
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout 1',
            Exercises: [],
          },
        ],
      },
    ]

    const result = extractExercisesFromWorkoutGroups(mockGroups)
    expect(result).toEqual([])
  })

  it('should return empty array when no workout templates', () => {
    const mockGroups: WorkoutTemplateGroupModel[] = [
      {
        Id: 1,
        Label: 'Day 1',
        WorkoutTemplates: [],
      },
    ]

    const result = extractExercisesFromWorkoutGroups(mockGroups)
    expect(result).toEqual([])
  })

  it('should prioritize Exercises over Exercices if both exist', () => {
    const mockGroups: any = [
      {
        Id: 1,
        Label: 'Day 1',
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout 1',
            Exercises: [{ Id: 1, Label: 'From Exercises' }],
            Exercices: [{ Id: 2, Label: 'From Exercices' }],
          },
        ],
      },
    ]

    const result = extractExercisesFromWorkoutGroups(mockGroups)
    expect(result).toHaveLength(1)
    expect(result[0].Label).toBe('From Exercises')
  })
})
