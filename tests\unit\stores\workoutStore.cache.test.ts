import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useWorkoutStore } from '@/stores/workoutStore'
import type {
  GetUserProgramInfoResponseModel,
  WorkoutTemplateModel,
  WorkoutTemplateGroupModel,
  RecommendationModel,
} from '@/types'

// Mock data for testing
const mockUserProgramInfo: GetUserProgramInfoResponseModel = {
  RecommendedProgram: {
    Id: 1,
    Label: 'Beginner Strength Program',
    RemainingToLevelUp: 10,
    IconUrl: 'https://example.com/icon.png',
  },
  NextWorkoutTemplate: {
    Id: 1,
    Label: 'Push Day',
    IsSystemExercise: false,
    ScheduledDate: '2024-01-01T00:00:00Z',
    UserId: 'test-user',
    Exercises: [],
    WorkoutSettingsModel: {},
  },
  WorkoutTemplates: [],
}

const mockWorkoutTemplate: WorkoutTemplateModel = {
  Id: 1,
  UserId: 'test-user',
  Label: 'Push Day',
  Exercises: [],
  IsSystemExercise: false,
  WorkoutSettingsModel: {
    Id: 1,
    Pause: 120,
    Equipment: '',
    ChildWorkoutTemplateId: null,
    SetsModel: null,
    WorkoutProgramId: 1,
    IsFirstSet: false,
    IsFail: false,
    NbRepsMinimalInc: null,
    AvgDuration: null,
    IsNotRealData: false,
  },
}

const mockWorkoutGroup: WorkoutTemplateGroupModel = {
  Id: 1,
  IsFeaturedProgram: false,
  UserId: 'test-user',
  Label: 'Week 1',
  WorkoutTemplates: [mockWorkoutTemplate],
  IsSystemExercise: false,
  RequiredWorkoutToLevelUp: 20,
  Level: 1,
  RemainingToLevelUp: 19,
  NextProgramId: 2,
  ProgramId: 1,
}

const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 8,
  Weight: { Lb: 135, Kg: 61.23 },
  OneRMProgress: 0.85,
  RecommendationInKg: 61.23,
  OneRMPercentage: 80,
  WarmUpReps1: 5,
  WarmUpReps2: 3,
  WarmUpWeightSet1: { Lb: 95, Kg: 43.09 },
  WarmUpWeightSet2: { Lb: 115, Kg: 52.16 },
  WarmUpsList: [],
  WarmupsCount: 2,
  RpRest: 120,
  NbPauses: 0,
  NbRepsPauses: 0,
  IsEasy: false,
  IsMedium: false,
  IsBodyweight: false,
  Increments: { Lb: 5, Kg: 2.5 },
  Max: { Lb: 200, Kg: 90.72 },
  Min: { Lb: 45, Kg: 20.41 },
  IsNormalSets: true,
  IsDeload: false,
  IsBackOffSet: false,
  BackOffSetWeight: { Lb: 0, Kg: 0 },
  IsMaxChallenge: false,
  IsLightSession: false,
  FirstWorkSetReps: 8,
  FirstWorkSetWeight: { Lb: 135, Kg: 61.23 },
  FirstWorkSet1RM: { Lb: 167.5, Kg: 76.02 },
  IsPyramid: false,
  IsReversePyramid: false,
  HistorySet: [],
  ReferenceSetHistory: {
    Id: 0,
    ExerciseId: 1,
    Reps: 8,
    Weight: { Lb: 135, Kg: 61.23 },
    RIR: 2,
    IsWarmups: false,
    IsNext: false,
    IsFinished: false,
  },
  MinReps: 6,
  MaxReps: 10,
  isPlateAvailable: true,
  isDumbbellAvailable: true,
  isPulleyAvailable: false,
  isBandsAvailable: false,
  Speed: 1,
  RIR: 2,
  IsManual: false,
  ReferenseReps: 8,
  ReferenseWeight: { Lb: 135, Kg: 61.23 },
  IsDropSet: false,
}

describe('Workout Store - Enhanced Cache Data Structure', () => {
  beforeEach(() => {
    // Reset store to initial state before each test
    useWorkoutStore.setState({
      currentWorkout: null,
      exercises: [],
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      workoutSession: null,
      isLoading: false,
      error: null,
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      hasHydrated: false,
    })
    vi.clearAllMocks()
  })

  describe('Cache Data Interface', () => {
    it('should have correct CachedAPIData interface structure', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const { cachedData } = result.current

      expect(cachedData).toHaveProperty('userProgramInfo')
      expect(cachedData).toHaveProperty('userWorkouts')
      expect(cachedData).toHaveProperty('todaysWorkout')
      expect(cachedData).toHaveProperty('exerciseRecommendations')
      expect(cachedData).toHaveProperty('lastUpdated')

      expect(cachedData.lastUpdated).toHaveProperty('userProgramInfo')
      expect(cachedData.lastUpdated).toHaveProperty('userWorkouts')
      expect(cachedData.lastUpdated).toHaveProperty('todaysWorkout')
      expect(cachedData.lastUpdated).toHaveProperty('exerciseRecommendations')
    })

    it('should initialize with null values for all cached data', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const { cachedData } = result.current

      expect(cachedData.userProgramInfo).toBeNull()
      expect(cachedData.userWorkouts).toBeNull()
      expect(cachedData.todaysWorkout).toBeNull()
      expect(cachedData.exerciseRecommendations).toEqual({})
    })

    it('should initialize with zero timestamps', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const { lastUpdated } = result.current.cachedData

      expect(lastUpdated.userProgramInfo).toBe(0)
      expect(lastUpdated.userWorkouts).toBe(0)
      expect(lastUpdated.todaysWorkout).toBe(0)
      expect(lastUpdated.exerciseRecommendations).toEqual({})
    })
  })

  describe('Cache Storage Structure', () => {
    it('should store user program info with correct structure', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
      })

      expect(result.current.cachedData.userProgramInfo).toEqual(
        mockUserProgramInfo
      )
      expect(
        result.current.cachedData.lastUpdated.userProgramInfo
      ).toBeGreaterThan(0)
    })

    it('should store user workouts array with correct structure', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const workouts = [mockWorkoutTemplate, { ...mockWorkoutTemplate, Id: 2 }]

      act(() => {
        result.current.setCachedUserWorkouts(workouts)
      })

      expect(result.current.cachedData.userWorkouts).toEqual(workouts)
      expect(
        result.current.cachedData.lastUpdated.userWorkouts
      ).toBeGreaterThan(0)
    })

    it('should store todays workout groups with correct structure', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const todaysWorkout = [mockWorkoutGroup]

      act(() => {
        result.current.setCachedTodaysWorkout(todaysWorkout)
      })

      expect(result.current.cachedData.todaysWorkout).toEqual(todaysWorkout)
      expect(
        result.current.cachedData.lastUpdated.todaysWorkout
      ).toBeGreaterThan(0)
    })

    it('should store exercise recommendations by ID', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const exerciseId = 123

      act(() => {
        result.current.setCachedExerciseRecommendation(
          exerciseId,
          mockRecommendation
        )
      })

      expect(
        result.current.cachedData.exerciseRecommendations[exerciseId]
      ).toEqual(mockRecommendation)
      expect(
        result.current.cachedData.lastUpdated.exerciseRecommendations[
          exerciseId
        ]
      ).toBeGreaterThan(0)
    })

    it('should handle multiple exercise recommendations', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setCachedExerciseRecommendation(1, mockRecommendation)
        result.current.setCachedExerciseRecommendation(2, {
          ...mockRecommendation,
          Reps: 10,
        })
        result.current.setCachedExerciseRecommendation(3, {
          ...mockRecommendation,
          Reps: 12,
        })
      })

      expect(
        Object.keys(result.current.cachedData.exerciseRecommendations)
      ).toHaveLength(3)
      expect(result.current.cachedData.exerciseRecommendations[1]?.Reps).toBe(8)
      expect(result.current.cachedData.exerciseRecommendations[2]?.Reps).toBe(
        10
      )
      expect(result.current.cachedData.exerciseRecommendations[3]?.Reps).toBe(
        12
      )
    })
  })

  describe('Cache Versioning and Compatibility', () => {
    it('should handle cache version in store state', () => {
      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.cacheVersion).toBeDefined()
      expect(typeof result.current.cacheVersion).toBe('number')
    })

    it('should clear cache when version mismatch detected', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Set some cached data
      act(() => {
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
        result.current.setCachedUserWorkouts([mockWorkoutTemplate])
      })

      // Simulate version mismatch
      act(() => {
        result.current.handleCacheVersionMismatch()
      })

      expect(result.current.cachedData.userProgramInfo).toBeNull()
      expect(result.current.cachedData.userWorkouts).toBeNull()
    })

    it('should preserve cache version after valid cache operations', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const initialVersion = result.current.cacheVersion

      act(() => {
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
      })

      expect(result.current.cacheVersion).toBe(initialVersion)
    })
  })

  describe('Cache Size Limits', () => {
    it('should enforce maximum cache size for recommendations', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const MAX_RECOMMENDATIONS = 50 // From the plan

      // Add more than the limit
      act(() => {
        for (let i = 0; i < MAX_RECOMMENDATIONS + 10; i++) {
          result.current.setCachedExerciseRecommendation(i, mockRecommendation)
        }
      })

      // Should not exceed the limit
      const recommendationCount = Object.keys(
        result.current.cachedData.exerciseRecommendations
      ).length
      expect(recommendationCount).toBeLessThanOrEqual(MAX_RECOMMENDATIONS)
    })

    it('should calculate total cache size', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
        result.current.setCachedUserWorkouts([mockWorkoutTemplate])
        result.current.setCachedExerciseRecommendation(1, mockRecommendation)
      })

      const cacheSize = result.current.getCacheSize()
      expect(cacheSize).toBeGreaterThan(0)
      expect(cacheSize).toBeLessThan(500 * 1024) // 500KB limit from plan
    })

    it('should evict oldest recommendations when limit reached', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const MAX_RECOMMENDATIONS = 50

      // Add recommendations with slight delays to ensure different timestamps
      act(() => {
        for (let i = 0; i < MAX_RECOMMENDATIONS; i++) {
          result.current.setCachedExerciseRecommendation(i, mockRecommendation)
        }
      })

      // Add one more to trigger eviction
      act(() => {
        result.current.setCachedExerciseRecommendation(999, mockRecommendation)
      })

      // The newest one should exist
      expect(
        result.current.cachedData.exerciseRecommendations[999]
      ).toBeDefined()

      // Total should not exceed limit
      const count = Object.keys(
        result.current.cachedData.exerciseRecommendations
      ).length
      expect(count).toBeLessThanOrEqual(MAX_RECOMMENDATIONS)
    })
  })

  describe('Data Validation', () => {
    it('should validate user program info before caching', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const invalidData = {
        invalid: 'data',
      } as unknown as GetUserProgramInfoResponseModel

      act(() => {
        result.current.setCachedUserProgramInfo(invalidData)
      })

      // Should not cache invalid data
      expect(result.current.cachedData.userProgramInfo).toBeNull()
    })

    it('should validate workout templates before caching', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const invalidWorkouts = [
        { invalid: 'workout' },
      ] as unknown as WorkoutTemplateModel[]

      act(() => {
        result.current.setCachedUserWorkouts(invalidWorkouts)
      })

      // Should not cache invalid data
      expect(result.current.cachedData.userWorkouts).toBeNull()
    })

    it('should handle null recommendation gracefully', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setCachedExerciseRecommendation(1, null)
      })

      expect(result.current.cachedData.exerciseRecommendations[1]).toBeNull()
      expect(
        result.current.cachedData.lastUpdated.exerciseRecommendations[1]
      ).toBeGreaterThan(0)
    })

    it('should validate recommendation structure before caching', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const invalidRecommendation = {
        invalid: 'data',
      } as unknown as RecommendationModel

      act(() => {
        result.current.setCachedExerciseRecommendation(1, invalidRecommendation)
      })

      // Should not cache invalid data
      expect(
        result.current.cachedData.exerciseRecommendations[1]
      ).toBeUndefined()
    })
  })

  describe('Timestamp Tracking', () => {
    it('should update timestamp when caching user program info', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const beforeTime = Date.now()

      act(() => {
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
      })

      const timestamp = result.current.cachedData.lastUpdated.userProgramInfo
      expect(timestamp).toBeGreaterThanOrEqual(beforeTime)
      expect(timestamp).toBeLessThanOrEqual(Date.now())
    })

    it('should update timestamp when caching workouts', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const beforeTime = Date.now()

      act(() => {
        result.current.setCachedUserWorkouts([mockWorkoutTemplate])
      })

      const timestamp = result.current.cachedData.lastUpdated.userWorkouts
      expect(timestamp).toBeGreaterThanOrEqual(beforeTime)
      expect(timestamp).toBeLessThanOrEqual(Date.now())
    })

    it('should track timestamps per exercise recommendation', () => {
      vi.useFakeTimers()
      const { result } = renderHook(() => useWorkoutStore())
      const beforeTime = Date.now()

      act(() => {
        result.current.setCachedExerciseRecommendation(1, mockRecommendation)
      })

      // Wait a bit
      const delay = 10
      act(() => {
        vi.advanceTimersByTime(delay)
      })

      act(() => {
        result.current.setCachedExerciseRecommendation(2, mockRecommendation)
      })

      const timestamp1 =
        result.current.cachedData.lastUpdated.exerciseRecommendations[1]
      const timestamp2 =
        result.current.cachedData.lastUpdated.exerciseRecommendations[2]

      expect(timestamp1).toBeGreaterThanOrEqual(beforeTime)
      expect(timestamp2).toBeGreaterThanOrEqual(timestamp1)

      vi.useRealTimers()
    })

    it('should preserve timestamps for unchanged data', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
      })

      const originalTimestamp =
        result.current.cachedData.lastUpdated.userProgramInfo

      // Cache something else
      act(() => {
        result.current.setCachedUserWorkouts([mockWorkoutTemplate])
      })

      // Original timestamp should be unchanged
      expect(result.current.cachedData.lastUpdated.userProgramInfo).toBe(
        originalTimestamp
      )
    })
  })

  describe('Hydration State', () => {
    it('should track hydration state', () => {
      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.hasHydrated).toBe(false)

      act(() => {
        result.current.setHasHydrated(true)
      })

      expect(result.current.hasHydrated).toBe(true)
    })

    it('should not allow cache operations before hydration', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Ensure not hydrated
      expect(result.current.hasHydrated).toBe(false)

      // Try to get cached data
      const programInfo = result.current.getCachedUserProgramInfo()
      expect(programInfo).toBeNull()
    })
  })

  describe('Cache Type Safety', () => {
    it('should maintain type safety for program info', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
      })

      const cached = result.current.getCachedUserProgramInfo()
      if (cached) {
        // TypeScript should recognize these properties
        expect(cached.RecommendedProgram).toBeDefined()
        expect(cached.NextWorkoutTemplate).toBeDefined()
      }
    })

    it('should maintain type safety for workout templates', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setCachedUserWorkouts([mockWorkoutTemplate])
      })

      const cached = result.current.getCachedUserWorkouts()
      if (cached && cached[0]) {
        // TypeScript should recognize these properties
        expect(cached[0].Id).toBeDefined()
        expect(cached[0].Exercises).toBeDefined()
        expect(cached[0].WorkoutSettingsModel).toBeDefined()
      }
    })

    it('should maintain type safety for recommendations', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        result.current.setCachedExerciseRecommendation(1, mockRecommendation)
      })

      const cached = result.current.getCachedExerciseRecommendation(1)
      if (cached) {
        // TypeScript should recognize these properties
        expect(cached.Series).toBeDefined()
        expect(cached.Reps).toBeDefined()
        expect(cached.Weight).toBeDefined()
      }
    })
  })

  describe('Cache Initialization', () => {
    it('should initialize cache structure on store creation', () => {
      const { result } = renderHook(() => useWorkoutStore())

      expect(result.current.cachedData).toBeDefined()
      expect(result.current.cachedData.userProgramInfo).toBe(null)
      expect(result.current.cachedData.userWorkouts).toBe(null)
      expect(result.current.cachedData.todaysWorkout).toBe(null)
      expect(result.current.cachedData.exerciseRecommendations).toEqual({})
      expect(result.current.cachedData.lastUpdated).toBeDefined()
    })

    it('should handle concurrent cache updates', () => {
      const { result } = renderHook(() => useWorkoutStore())

      act(() => {
        // Simulate concurrent updates
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
        result.current.setCachedUserWorkouts([mockWorkoutTemplate])
        result.current.setCachedExerciseRecommendation(1, mockRecommendation)
      })

      // All updates should be applied
      expect(result.current.cachedData.userProgramInfo).toBeDefined()
      expect(result.current.cachedData.userWorkouts).toBeDefined()
      expect(result.current.cachedData.exerciseRecommendations[1]).toBeDefined()
    })
  })
})
