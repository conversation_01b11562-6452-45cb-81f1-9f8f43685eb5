# Start Workout Button Flow - Complete Implementation

## Overview

This document details the complete sequence when a user clicks the "Start Workout" button in the Dr. Muscle app, from the initial button click to the workout page display.

## 1. Button Click Handler (MainAIPage.xaml.cs)

### Initial Entry Point

```csharp
async void BtnStartTodayWorkout_Clicked(object sender, EventArgs e)
{
    askforEquipment();
    ShouldAnimate = false;
    // Remove animation effects if present
}
```

## 2. Equipment Selection Flow

### askforEquipment() Method

```csharp
private void askforEquipment()
{
    // Check if multiple equipment options are available
    var multipleEquip = 0;
    if (LocalDBManager.Instance.GetDBSetting("Equipment")?.Value == "true")
        multipleEquip = 1;
    if (LocalDBManager.Instance.GetDBSetting("HomeMainEquipment")?.Value == "true")
        multipleEquip += 1;
    if (LocalDBManager.Instance.GetDBSetting("OtherMainEquipment")?.Value == "true")
        multipleEquip += 1;

    if (multipleEquip > 1)
    {
        // Show equipment selection dialog
        ActionSheetConfig config = new ActionSheetConfig();
        config.SetTitle("Where are you training today?");

        // Add options for Gym, Home, Other
        config.Add("Gym", async () => {
            await SetUserEquipmentSettings("gym");
            await FetchMainDataWithLoader();
            WorkoutLogSets(false);
            SetButtonTitle();
            StartTodaysWorkout();
        });
        // Similar for Home and Other options
    }
    else
    {
        StartTodaysWorkout();
    }
}
```

## 3. Start Today's Workout

### StartTodaysWorkout() Method

```csharp
async void StartTodaysWorkout()
{
    // 1. Check internet connection
    if (!CrossConnectivity.Current.IsConnected)
    {
        await UserDialogs.Instance.AlertAsync("Please check internet connection");
        return;
    }

    // 2. Load tips array
    _tipsArray = GetTipsArray();

    // 3. Check if mobility workout should be started
    if (BtnCardStartWorkout.Text.ToUpper().Contains("NEXT WORKOUT") &&
        LocalDBManager.Instance.GetDBSetting("IsMobility")?.Value == "true")
    {
        // Ask user if they want to preview or start mobility workout
        ConfirmConfig supersetConfig = new ConfirmConfig()
        {
            OkText = $"Preview {upi?.NextWorkoutTemplate?.Label}",
            CancelText = $"Start {upi?.NextWorkoutTemplate?.Label}",
        };
        var x = await UserDialogs.Instance.ConfirmAsync(supersetConfig);

        CurrentLog.Instance.IsMobilityFinished = !x;
        CurrentLog.Instance.IsMobilityStarted = x;
    }

    // 4. Show tips popup (if applicable)
    if (Config.ShowWelcomePopUp2 == false || !CurrentLog.Instance.IsMobilityStarted)
    {
        var modalPage = new Views.GeneralPopup(
            "Lamp.png",
            _tipsArray[Config.ShowTipsNumber][0],
            _tipsArray[Config.ShowTipsNumber][1],
            "Start workout"
        );
        await PopupNavigation.Instance.PushAsync(modalPage);
    }

    // 5. Main workout flow
    if (App.IsV1UserTrial || App.IsFreePlan || await CanGoFurther())
    {
        if (LocalDBManager.Instance.GetDBSetting("IsMobility")?.Value == "true" &&
            !CurrentLog.Instance.IsMobilityFinished)
        {
            MoveToWorkout(); // Mobility workout
        }
        else
        {
            LoadRegularWorkout(); // Regular workout
        }
    }
}
```

## 4. Mobility Workout Flow

### MoveToWorkout() Method

```csharp
async void MoveToWorkout()
{
    CurrentLog.Instance.IsMobilityStarted = true;

    // Set appropriate mobility workout based on level
    if (LocalDBManager.Instance.GetDBSetting("MobilityLevel")?.Value == "Beginner")
        CurrentLog.Instance.CurrentWorkoutTemplate = new WorkoutTemplateModel()
        {
            Id = GetMobilityWorkoutId("Beginner"),
            Label = "Flexibility & Mobility 1",
            IsSystemExercise = true,
            Exercises = new List<ExerciceModel>()
        };
    // Similar for Intermediate and Advanced

    CurrentLog.Instance.WorkoutStarted = true;

    // Check for cached workout
    var workoutModel = LocalDBManager.Instance.GetDBSetting(
        $"Workout{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}"
    )?.Value;

    if (!string.IsNullOrEmpty(workoutModel))
    {
        CurrentLog.Instance.CurrentWorkoutTemplate =
            JsonConvert.DeserializeObject<WorkoutTemplateModel>(workoutModel);
    }

    // Navigate to workout page
    Device.BeginInvokeOnMainThread(async () =>
    {
        await PagesFactory.PushAsync<KenkoChooseYourWorkoutExercisePage>();
    });
}
```

## 5. Regular Workout Flow

### LoadRegularWorkout() Method (inside StartTodaysWorkout)

```csharp
// After mobility check, load regular workout
CurrentLog.Instance.IsMobilityStarted = false;

if (upi != null) // User Program Info exists
{
    WorkoutTemplateModel nextWorkout = upi.NextWorkoutTemplate;
    WorkoutTemplateGroupModel groupModel = upi.RecommendedProgram;

    if (upi.NextWorkoutTemplate == null)
    {
        await PagesFactory.PushAsync<ChooseDrMuscleOrCustomPage>();
    }
    else
    {
        // Fetch exercises if not loaded
        if (upi.NextWorkoutTemplate.Exercises == null)
        {
            nextWorkout = await DrMuscleRestClient.Instance
                .GetUserCustomizedCurrentWorkoutWithoutLoader(upi.NextWorkoutTemplate.Id);
        }

        if (nextWorkout != null)
        {
            // Set current workout context
            CurrentLog.Instance.CurrentWorkoutTemplateGroup = groupModel;
            CurrentLog.Instance.CurrentWorkoutTemplate = nextWorkout;
            CurrentLog.Instance.WorkoutTemplateCurrentExercise = nextWorkout.Exercises.First();
            CurrentLog.Instance.WorkoutStarted = true;

            // Check for cached workout
            var workoutModel = LocalDBManager.Instance.GetDBSetting(
                $"Workout{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}"
            )?.Value;

            if (!string.IsNullOrEmpty(workoutModel))
            {
                CurrentLog.Instance.CurrentWorkoutTemplate =
                    JsonConvert.DeserializeObject<WorkoutTemplateModel>(workoutModel);
            }

            // Navigate to workout page
            Device.BeginInvokeOnMainThread(async () =>
            {
                await PagesFactory.PushAsync<KenkoChooseYourWorkoutExercisePage>();
            });
        }
    }
}
else
{
    // Fetch user program info if not available
    upi = await DrMuscleRestClient.Instance.GetUserProgramInfo();
    if (upi != null)
    {
        StartTodaysWorkout(); // Recursive call
    }
    else
    {
        await PagesFactory.PushAsync<ChooseDrMuscleOrCustomPage>();
    }
}
```

## 6. KenkoChooseYourWorkoutExercisePage - Workout Display

### OnBeforeShow() Method

```csharp
public override async void OnBeforeShow()
{
    base.OnBeforeShow();

    // Initialize page state
    vHeaders = new Dictionary<long, View>();
    _isAskedforSwipe = false;
    IsPowerlifting = false;
    _areExercisesUnfnished = false;

    // Check if powerlifting workout
    if ((CurrentLog.Instance.CurrentWorkoutTemplate.Id >= 16336 &&
         CurrentLog.Instance.CurrentWorkoutTemplate.Id <= 16338) ||
        CurrentLog.Instance.CurrentWorkoutTemplate.Id >= 16343)
        IsPowerlifting = true;

    // Load exercises if not loaded
    if (CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Count == 0)
    {
        var currentTemp = await DrMuscleRestClient.Instance
            .GetUserCustomizedCurrentWorkout(CurrentLog.Instance.CurrentWorkoutTemplate.Id);
        CurrentLog.Instance.CurrentWorkoutTemplate = currentTemp;
    }

    // Load user body weight
    if (LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value != null)
        _userBodyWeight = Convert.ToDecimal(
            LocalDBManager.Instance.GetDBSetting("BodyWeight").Value,
            CultureInfo.InvariantCulture
        );

    // Update exercise list
    await UpdateExerciseList();
    OpenExercises.Clear();
    CurrentLog.Instance.IsFromEndExercise = false;
    isConnected = true;
}
```

### UpdateExerciseList() Method

```csharp
public async Task UpdateExerciseList()
{
    var exercises = new ObservableCollection<ExerciseWorkSetsModel>();
    exerciseItems = new ObservableCollection<ExerciseWorkSetsModel>();

    // 1. Load exercise database from JSON
    var exerList = LoadExerciseDatabase(); // Loads from Exercises.json

    // 2. Set workout name
    LblWorkoutName.Text = CurrentLog.Instance.CurrentWorkoutTemplate.Label;

    // 3. Process each exercise in the workout
    var count = 1;
    foreach (ExerciceModel ee in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
    {
        // Create ExerciseWorkSetsModel
        var e = new ExerciseWorkSetsModel()
        {
            Id = ee.Id,
            Label = ee.Label,
            IsBodyweight = ee.IsBodyweight,
            IsSystemExercise = ee.IsSystemExercise,
            VideoUrl = ee.VideoUrl,
            BodyPartId = ee.BodyPartId,
            CountNo = $"{count}",
            // ... other properties
        };

        // Check if exercise is swapped
        bool isSwapped = ((App)Application.Current).SwapExericesContexts.Swaps
            .Any(c => c.SourceExerciseId == e.Id);

        if (!isSwapped)
        {
            // Check if exercise is already finished
            if (LocalDBManager.Instance.GetDBSetting(
                $"workout{CurrentLog.Instance.CurrentWorkoutTemplate.Id}exercise{e.Id}"
            )?.Value == "true")
            {
                e.IsFinished = true;
                e.IsNextExercise = false;
            }
            exercises.Add(e);
        }
        else
        {
            // Handle swapped exercise
            var targetExercise = GetSwappedExercise(e.Id);
            exercises.Add(targetExercise);
        }

        count++;
    }

    // 4. Set next exercise
    var isSelected = exercises.FirstOrDefault(x => x.IsFinished == true);
    var exModel = exercises.FirstOrDefault(x => x.IsFinished == false);
    if (isSelected != null && exModel != null)
    {
        exModel.IsNextExercise = true;
    }

    // 5. Add footer items
    exerciseItems.Add(new ExerciseWorkSetsModel() { IsAddExercise = true });
    exerciseItems.Add(new ExerciseWorkSetsModel() { IsFinishWorkoutExe = true });

    // 6. Update UI
    Device.BeginInvokeOnMainThread(() =>
    {
        BindableLayout.SetItemsSource(ExerciseListView, exerciseItems);
    });

    // 7. Send workout loaded message
    MessagingCenter.Send<WorkoutLoadedMessage>(
        new WorkoutLoadedMessage(),
        "WorkoutLoadedMessage"
    );
}
```

### Exercise Recommendation Loading

When an exercise is selected or needs to load recommendations:

```csharp
private async Task FetchReco(ExerciseWorkSetsModel m, int? sessionDays = null)
{
    if (m.IsNextExercise)
    {
        // Check if exercise already has sets
        if (m.Count > 0)
        {
            m.Clear();
            return;
        }

        // Fetch recommendation from API
        var recommendation = await DrMuscleRestClient.Instance
            .GetRecommendationForExercise(new GetRecommendationForExerciseModel
            {
                Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                ExerciseId = m.Id,
                IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "true",
                SetStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value,
                WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id,
                SessionDays = sessionDays
            });

        if (recommendation != null)
        {
            m.RecoModel = recommendation;

            // Generate sets based on recommendation
            var setList = new List<WorkoutLogSerieModelRef>();

            // Add sets based on recommendation type
            if (m.RecoModel.IsNormalSets)
            {
                // Generate normal sets
                for (int i = 0; i < m.RecoModel.NbSets; i++)
                {
                    setList.Add(new WorkoutLogSerieModelRef
                    {
                        Weight = m.RecoModel.Weight,
                        Reps = m.RecoModel.Reps,
                        IsWarmup = false,
                        // ... other properties
                    });
                }
            }
            // Handle other set types (pyramid, rest-pause, etc.)

            // Add sets to exercise
            foreach (var set in setList)
            {
                m.Add(set);
            }

            // Update UI flags
            m.IsNextExercise = true;
            m.IsRecoLoaded = true;
        }
    }
}
```

## 7. Summary of Key Components

### State Management

- **CurrentLog.Instance**: Singleton that maintains current workout state
- **LocalDBManager**: Stores user preferences and cached data
- **WorkoutTemplateModel**: Contains workout structure and exercises
- **ExerciseWorkSetsModel**: Represents individual exercises with sets

### API Endpoints Used

1. **GetUserProgramInfo**: Fetches user's program and next workout
2. **GetUserCustomizedCurrentWorkout**: Gets full workout with exercises
3. **GetRecommendationForExercise**: Gets set/rep recommendations
4. **IsNewExerciseWithSessionInfo**: Checks if exercise is new

### Navigation Flow

1. MainAIPage → Equipment Selection (if needed)
2. Equipment Selection → Tips Popup
3. Tips Popup → KenkoChooseYourWorkoutExercisePage
4. Exercise list displayed with recommendations

### Key Features

- **Mobility vs Regular Workouts**: Different flows based on workout type
- **Equipment-based Customization**: Adjusts workout based on available equipment
- **Offline Support**: Caches workouts locally for offline access
- **Smart Recommendations**: AI-powered set/rep recommendations
- **Exercise Swapping**: Ability to swap exercises dynamically

# Dr. Muscle Web App - Start Workout API Flow

## Complete API Call Sequence for Starting a Workout

### 1. **User Authentication Check**

**Endpoint**: `/api/Account/CheckUserEmail`

```csharp
// Check if user is authenticated
var response = await DrMuscleRestClient.Instance.CheckUserEmail(email);
```

**Local Handling**:

- Stores auth token in `LocalDBManager`
- Caches user email and token for subsequent requests

---

### 2. **Get User Program Info**

**Endpoint**: `/api/Workout/GetUserProgramInfo`

```csharp
var upi = await DrMuscleRestClient.Instance.GetUserProgramInfo();
```

**Response Model**:

```json
{
  "RecommendedProgram": "Bodybuilding2",
  "NextWorkoutTemplate": {
    "Id": 12345,
    "Label": "Day A: Chest, shoulders, and triceps"
  },
  "RemainingToEatPlans": ["Intermittent fasting 16:8"]
}
```

**Local Handling**:

- Saves program info to `LocalDBManager`
- Updates `CurrentLog.Instance.CurrentProgram`

---

### 3. **Get Current Workout Template**

**Endpoint**: `/api/Workout/GetUserCustomizedCurrentWorkout`

```csharp
var workoutTemplate = await DrMuscleRestClient.Instance.GetUserCustomizedCurrentWorkout(workoutId);
```

**Request**: `workoutId` (from NextWorkoutTemplate.Id)
**Response Model**:

```json
{
  "Id": 12345,
  "Label": "Day A: Chest, shoulders, and triceps",
  "IsSystemExercise": true,
  "Exercises": [
    {
      "Id": 217,
      "Label": "Barbell bench press",
      "IsBodyweight": false,
      "IsEasy": false,
      "IsFinished": false,
      "IsSwapped": false,
      "IsNextExercise": true,
      "VideoUrl": "https://...",
      "LocalVideo": "bb_bench_press.mp4"
    }
  ]
}
```

**Local Handling**:

- Caches in `CurrentLog.Instance.CurrentWorkoutTemplate`
- Stores exercises in memory for quick access

---

### 4. **Check Exercise History (for each exercise)**

**Endpoint**: `/api/Exercise/IsNewExerciseWithSessionInfo`

```csharp
foreach (var exercise in workoutTemplate.Exercises)
{
    var sessionInfo = await DrMuscleRestClient.Instance.IsNewExerciseWithSessionInfo(
        new ExerciceModel { Id = exercise.Id }
    );
}
```

**Response Model**:

```json
{
  "IsNewExercise": false,
  "LastLogDate": "2024-01-15",
  "SessionDays": 3,
  "RecommendedSetStyle": 8
}
```

**Local Handling**:

- Determines if tutorial should be shown
- Updates exercise display based on history

---

### 5. **Get Exercise Recommendations (Batch or Individual)**

#### Option A: Batch Request (Preferred for multiple exercises)

**Endpoint**: `/api/Exercise/GetAllRecommendationNew`

```csharp
var requests = new List<GetRecommendationForExerciseModel>();
foreach (var exercise in exercisesToLoad)
{
    requests.Add(new GetRecommendationForExerciseModel
    {
        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
        ExerciseId = exercise.Id,
        IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "true",
        LightSessionDays = sessionDays,
        WorkoutId = workoutId,
        IsStrengthPhase = isStrengthPhase,
        SetStyle = setStyle
    });
}
var recommendations = await DrMuscleRestClient.Instance.GetAllRecommendationNew(requests);
```

#### Option B: Individual Requests (For single exercise)

**Endpoint**: `/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew`

```csharp
var recommendation = await DrMuscleRestClient.Instance.GetRecommendationNormalRIRForExerciseWithoutDeload(
    new GetRecommendationForExerciseModel { /* same as above */ }
);
```

**Response Model** (RecommendationModel):

```json
{
  "ExerciseId": 217,
  "Series": "8",
  "Reps": 8,
  "Weight": "60",
  "WeightDouble": 60.0,
  "IsBodyweight": false,
  "IsPlate": false,
  "RecommendationInKg": 60.0,
  "RecommendationInLb": 132.28,
  "NbRepsInReserve": 2,
  "WarmUpsList": [
    {
      "WeightValue": "20",
      "Reps": 10,
      "IsWarmup": true,
      "SetNo": "1"
    },
    {
      "WeightValue": "40",
      "Reps": 5,
      "IsWarmup": true,
      "SetNo": "2"
    }
  ],
  "History": [
    {
      "Weight": "55",
      "Reps": 8,
      "CreatedDate": "2024-01-10T10:30:00Z"
    }
  ],
  "FirstWorkSetWeight": 60.0,
  "NbPauses": 0,
  "IsDeload": false,
  "IsFirstWorkSet": true,
  "IsLastPlannedSet": false,
  "RestTime": 180
}
```

**Local Handling**:

```csharp
// Cache recommendations for 3 days
RecoContext.SaveContexts("Reco" + exercise.Id + setStyle, recommendation);
LocalDBManager.Instance.SetDBSetting("NbRepsGeneratedTime" + exercise.Id + setStyle,
    DateTime.Now.AddDays(3).Ticks.ToString());

// Create exercise work sets model
var exerciseWorkSets = new ExerciseWorkSetsModel
{
    Id = exercise.Id,
    Label = exercise.Label,
    RecommendationModel = recommendation,
    Sets = new ObservableCollection<WorkoutLogSerieModel>()
};

// Add warmup sets
foreach (var warmup in recommendation.WarmUpsList)
{
    exerciseWorkSets.Sets.Add(new WorkoutLogSerieModel
    {
        Reps = warmup.Reps,
        Weight = warmup.WeightValue,
        IsWarmUp = true,
        SetNo = warmup.SetNo
    });
}

// Add work sets
for (int i = 0; i < recommendation.Series; i++)
{
    exerciseWorkSets.Sets.Add(new WorkoutLogSerieModel
    {
        Reps = recommendation.Reps,
        Weight = recommendation.Weight,
        IsWarmUp = false,
        SetNo = (i + 1).ToString(),
        IsFirstWorkSet = i == 0,
        IsLastSet = i == recommendation.Series - 1
    });
}
```

---

### 6. **Load Exercise Videos/Images**

**Endpoint**: `/api/Exercise/GetExerciseVideo` (if needed)

```csharp
if (string.IsNullOrEmpty(exercise.LocalVideo))
{
    var videoUrl = await DrMuscleRestClient.Instance.GetExerciseVideo(exercise.Id);
    exercise.VideoUrl = videoUrl;
}
```

---

### 7. **Update User Preferences (if changed)**

**Endpoint**: `/api/Exercise/AddUpdateExerciseSettings`

```csharp
await DrMuscleRestClient.Instance.AddUpdateExerciseSettings(new ExerciseSettings
{
    ExerciseId = exerciseId,
    UserId = userId,
    SetStyle = selectedSetStyle,
    RepRangeMin = minReps,
    RepRangeMax = maxReps
});
```

---

## Complete Local State Management

### CurrentLog Singleton

```csharp
public class CurrentLog
{
    public static CurrentLog Instance { get; set; }

    public WorkoutTemplateModel CurrentWorkoutTemplate { get; set; }
    public string CurrentProgram { get; set; }
    public Dictionary<long, ExerciseWorkSetsModel> WorkoutLogSeriesByExerciseRef { get; set; }
    public ObservableCollection<ExerciseWorkSetsModel> ExerciseListObservable { get; set; }
    public bool IsWorkoutStarted { get; set; }
    public DateTime? WorkoutStartTime { get; set; }
}
```

### Local Database Schema

```sql
-- Settings table
CREATE TABLE DBSetting (
    Key TEXT PRIMARY KEY,
    Value TEXT
);

-- Recommendations cache
CREATE TABLE DBReco (
    Key TEXT PRIMARY KEY,
    Value TEXT,
    Timestamp INTEGER
);

-- Exercise history
CREATE TABLE WorkoutLogSerie (
    Id INTEGER PRIMARY KEY,
    ExerciseId INTEGER,
    Reps INTEGER,
    Weight REAL,
    CreatedDate TEXT,
    IsWarmUp BOOLEAN
);
```

### Cache Keys Used

- `"email"` - User email
- `"token"` - Auth token
- `"Reco{exerciseId}{setStyle}"` - Cached recommendations
- `"NbRepsGeneratedTime{exerciseId}{setStyle}"` - Cache expiry timestamp
- `"timer_remaining"` - Rest timer state
- `"workout_start_time"` - Workout start timestamp
- `"CurrentWorkoutId"` - Active workout ID

---

## Error Handling and Offline Support

### API Call Wrapper

```csharp
public async Task<T> MakeApiCall<T>(Func<Task<T>> apiCall, string cacheKey = null)
{
    try
    {
        // Check connectivity
        if (!CrossConnectivity.Current.IsConnected)
        {
            if (cacheKey != null)
            {
                // Try to load from cache
                var cached = LocalDBManager.Instance.GetDBReco(cacheKey);
                if (cached != null)
                    return JsonConvert.DeserializeObject<T>(cached.Value);
            }
            throw new Exception("No internet connection");
        }

        // Make API call with retry
        int retryCount = 0;
        while (retryCount < 4)
        {
            try
            {
                var result = await apiCall();

                // Cache successful result
                if (cacheKey != null && result != null)
                {
                    LocalDBManager.Instance.SetDBReco(cacheKey,
                        JsonConvert.SerializeObject(result));
                }

                return result;
            }
            catch (TaskCanceledException)
            {
                retryCount++;
                if (retryCount >= 4)
                    throw;
                await Task.Delay(500 * retryCount);
            }
        }
    }
    catch (Exception ex)
    {
        // Log error and show user message
        Analytics.TrackEvent("API_Error", new Dictionary<string, string>
        {
            { "endpoint", apiCall.Method.Name },
            { "error", ex.Message }
        });
        throw;
    }
}
```

---

## Summary

The start workout flow involves:

1. **Authentication verification**
2. **Loading user's program and next workout**
3. **Fetching workout template with exercises**
4. **Checking exercise history for each exercise**
5. **Getting AI recommendations (batch or individual)**
6. **Caching all data locally for offline use**
7. **Building the UI with exercises and sets**

Total API calls for a typical workout start:

- 1 auth check (if needed)
- 1 program info
- 1 workout template
- N exercise history checks (where N = number of exercises)
- 1 batch recommendation call OR N individual recommendation calls

The app prioritizes performance through:

- Batch API calls when possible
- 3-day recommendation caching
- Offline support with local SQLite storage
- Progressive loading (exercises load as user scrolls)
