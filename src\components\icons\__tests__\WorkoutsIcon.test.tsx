import { render } from '@testing-library/react'
import { WorkoutsIcon } from '../WorkoutsIcon'

describe('WorkoutsIcon', () => {
  it('renders without errors', () => {
    const { container } = render(<WorkoutsIcon />)
    const svg = container.querySelector('svg')
    expect(svg).toBeInTheDocument()
  })

  it('applies custom className', () => {
    const { container } = render(<WorkoutsIcon className="text-blue-500" />)
    const svg = container.querySelector('svg')
    expect(svg).toHaveClass('text-blue-500')
  })

  it('renders with default size', () => {
    const { container } = render(<WorkoutsIcon />)
    const svg = container.querySelector('svg')
    expect(svg).toHaveAttribute('width', '24')
    expect(svg).toHaveAttribute('height', '24')
  })

  it('renders with custom size', () => {
    const { container } = render(<WorkoutsIcon size={20} />)
    const svg = container.querySelector('svg')
    expect(svg).toHaveAttribute('width', '20')
    expect(svg).toHaveAttribute('height', '20')
  })

  it('maintains SVG structure', () => {
    const { container } = render(<WorkoutsIcon />)
    const paths = container.querySelectorAll('path')
    expect(paths).toHaveLength(1)
  })
})
