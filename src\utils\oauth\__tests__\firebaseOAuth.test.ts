import { describe, it, expect, vi, beforeEach, afterEach, Mock } from 'vitest'
import { FirebaseOAuthHelper } from '../firebaseOAuth'
import {
  GoogleAuthProvider,
  OAuthProvider,
  signInWithPopup,
  signInWithRedirect,
  getRedirectResult,
  AuthError,
} from 'firebase/auth'
import { getFirebaseAuth } from '@/config/firebase'
import { PerformanceMonitor } from '@/utils/performance'
import type {
  OAuthUserData,
  OAuthSuccessCallback,
  OAuthErrorCallback,
} from '@/types/oauth'

vi.mock('firebase/auth', () => ({
  GoogleAuthProvider: vi.fn().mockImplementation(() => ({
    addScope: vi.fn(),
  })),
  OAuthProvider: vi.fn().mockImplementation(() => ({
    addScope: vi.fn(),
  })),
  signInWithPopup: vi.fn(),
  signInWithRedirect: vi.fn(),
  getRedirectResult: vi.fn(),
}))

vi.mock('@/config/firebase', () => ({
  getFirebaseAuth: vi.fn(),
}))

vi.mock('@/utils/performance', () => ({
  PerformanceMonitor: {
    mark: vi.fn(),
    measure: vi.fn(),
  },
}))

describe('FirebaseOAuthHelper', () => {
  const mockAuth = {
    currentUser: null,
    signOut: vi.fn(),
  }

  const mockUser = {
    uid: 'firebase-uid-123',
    email: '<EMAIL>',
    emailVerified: true,
    displayName: 'Test User',
    photoURL: 'https://example.com/photo.jpg',
    getIdToken: vi.fn().mockResolvedValue('firebase-id-token'),
  }

  const mockUserCredential = {
    user: mockUser,
    providerId: 'google.com',
    _tokenResponse: {
      firstName: 'Test',
      lastName: 'User',
      given_name: 'Test',
      family_name: 'User',
      idToken: 'google-id-token',
    },
  }

  const onSuccess: OAuthSuccessCallback = vi.fn()
  const onError: OAuthErrorCallback = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    ;(getFirebaseAuth as Mock).mockReturnValue(mockAuth)
    // Reset static state
    ;(FirebaseOAuthHelper as any).isInitialized = false
    ;(FirebaseOAuthHelper as any).googleProvider = null
    ;(FirebaseOAuthHelper as any).appleProvider = null
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('initialize', () => {
    it('should initialize OAuth providers', async () => {
      await FirebaseOAuthHelper.initialize()

      expect(GoogleAuthProvider).toHaveBeenCalled()
      expect(OAuthProvider).toHaveBeenCalledWith('apple.com')
      expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
        'firebase-oauth-init-start'
      )
      expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
        'firebase-oauth-init-complete'
      )
      expect(PerformanceMonitor.measure).toHaveBeenCalledWith(
        'firebase-oauth-init',
        'firebase-oauth-init-start',
        'firebase-oauth-init-complete'
      )
      expect(FirebaseOAuthHelper.isReady()).toBe(true)
    })

    it('should not reinitialize if already initialized', async () => {
      await FirebaseOAuthHelper.initialize()
      vi.clearAllMocks()

      await FirebaseOAuthHelper.initialize()

      expect(GoogleAuthProvider).not.toHaveBeenCalled()
      expect(OAuthProvider).not.toHaveBeenCalled()
    })

    it('should handle initialization errors', async () => {
      const error = new Error('Init failed')
      ;(GoogleAuthProvider as Mock).mockImplementation(() => {
        throw error
      })

      await expect(FirebaseOAuthHelper.initialize()).rejects.toThrow(
        'Init failed'
      )
      expect(FirebaseOAuthHelper.isReady()).toBe(false)
    })
  })

  describe('signInWithGoogle', () => {
    beforeEach(async () => {
      await FirebaseOAuthHelper.initialize()
    })

    it('should sign in with Google using popup', async () => {
      ;(signInWithPopup as Mock).mockResolvedValue(mockUserCredential)

      await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError)

      expect(signInWithPopup).toHaveBeenCalledWith(mockAuth, expect.any(Object))
      expect(onSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          provider: 'google',
          providerId: 'firebase-uid-123',
          email: '<EMAIL>',
          emailVerified: true,
          name: 'Test User',
          pictureUrl: 'https://example.com/photo.jpg',
          firstName: 'Test',
          lastName: 'User',
          rawToken: 'firebase-id-token',
          metadata: expect.objectContaining({
            firebaseUid: 'firebase-uid-123',
            googleIdToken: 'google-id-token',
          }),
        })
      )
      expect(onError).not.toHaveBeenCalled()
    })

    it('should sign in with Google using redirect', async () => {
      ;(signInWithRedirect as Mock).mockResolvedValue(undefined)

      await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError, false)

      expect(signInWithRedirect).toHaveBeenCalledWith(
        mockAuth,
        expect.any(Object)
      )
      expect(signInWithPopup).not.toHaveBeenCalled()
      expect(onSuccess).not.toHaveBeenCalled() // Success handled on redirect return
    })

    it('should handle popup blocked error', async () => {
      const authError: AuthError = {
        code: 'auth/popup-blocked',
        message: 'Popup blocked',
        name: 'FirebaseError',
      }
      ;(signInWithPopup as Mock).mockRejectedValue(authError)

      await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith({
        code: 'user_cancelled',
        message: 'Sign-in cancelled by user',
        provider: 'google',
        providerError: authError,
      })
      expect(onSuccess).not.toHaveBeenCalled()
    })

    it('should handle network errors', async () => {
      const authError: AuthError = {
        code: 'auth/network-request-failed',
        message: 'Network error',
        name: 'FirebaseError',
      }
      ;(signInWithPopup as Mock).mockRejectedValue(authError)

      await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith({
        code: 'network_error',
        message: 'Network error during sign-in',
        provider: 'google',
        providerError: authError,
      })
    })

    it('should handle invalid credential errors', async () => {
      const authError: AuthError = {
        code: 'auth/invalid-credential',
        message: 'Invalid credential',
        name: 'FirebaseError',
      }
      ;(signInWithPopup as Mock).mockRejectedValue(authError)

      await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith({
        code: 'invalid_token',
        message: 'Invalid authentication credentials',
        provider: 'google',
        providerError: authError,
      })
    })

    it('should track performance metrics', async () => {
      ;(signInWithPopup as Mock).mockResolvedValue(mockUserCredential)

      await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError)

      expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
        'firebase-oauth-google-start'
      )
      expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
        'firebase-oauth-google-complete'
      )
      expect(PerformanceMonitor.measure).toHaveBeenCalledWith(
        'firebase-oauth-google',
        'firebase-oauth-google-start',
        'firebase-oauth-google-complete'
      )
    })
  })

  describe('signInWithApple', () => {
    beforeEach(async () => {
      await FirebaseOAuthHelper.initialize()
    })

    it('should sign in with Apple using popup', async () => {
      const appleUserCredential = {
        user: mockUser,
        providerId: 'apple.com',
        _tokenResponse: {
          fullName: 'Apple Test User',
          federatedId: 'apple-federated-123',
          idToken: 'apple-id-token',
        },
      }
      ;(signInWithPopup as Mock).mockResolvedValue(appleUserCredential)

      await FirebaseOAuthHelper.signInWithApple(onSuccess, onError)

      expect(signInWithPopup).toHaveBeenCalledWith(mockAuth, expect.any(Object))
      expect(onSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          provider: 'apple',
          providerId: 'firebase-uid-123',
          email: '<EMAIL>',
          firstName: 'Apple',
          lastName: 'Test User',
          metadata: expect.objectContaining({
            appleUserId: 'apple-federated-123',
            appleIdToken: 'apple-id-token',
          }),
        })
      )
    })

    it('should handle Apple sign in without name (subsequent logins)', async () => {
      const appleUserCredential = {
        user: mockUser,
        providerId: 'apple.com',
        _tokenResponse: {
          federatedId: 'apple-federated-123',
          idToken: 'apple-id-token',
          // No fullName on subsequent logins
        },
      }
      ;(signInWithPopup as Mock).mockResolvedValue(appleUserCredential)

      await FirebaseOAuthHelper.signInWithApple(onSuccess, onError)

      const userData = onSuccess.mock.calls[0][0] as OAuthUserData
      expect(userData.firstName).toBeUndefined()
      expect(userData.lastName).toBeUndefined()
      expect(userData.name).toBe('Test User') // Falls back to displayName
    })

    it('should handle unauthorized domain error', async () => {
      const authError: AuthError = {
        code: 'auth/unauthorized-domain',
        message: 'Unauthorized domain',
        name: 'FirebaseError',
      }
      ;(signInWithPopup as Mock).mockRejectedValue(authError)

      await FirebaseOAuthHelper.signInWithApple(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith({
        code: 'provider_error',
        message: 'This domain is not authorized for OAuth operations',
        provider: 'apple',
        providerError: authError,
        details: {
          hint: 'Add this domain to Firebase Console > Authentication > Settings > Authorized domains',
        },
      })
    })
  })

  describe('handleRedirectResult', () => {
    it('should handle successful redirect result for Google', async () => {
      const redirectResult = {
        ...mockUserCredential,
        providerId: 'google.com',
      }
      ;(getRedirectResult as Mock).mockResolvedValue(redirectResult)

      await FirebaseOAuthHelper.handleRedirectResult(onSuccess, onError)

      expect(getRedirectResult).toHaveBeenCalledWith(mockAuth)
      expect(onSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          provider: 'google',
          providerId: 'firebase-uid-123',
        })
      )
    })

    it('should handle successful redirect result for Apple', async () => {
      const redirectResult = {
        ...mockUserCredential,
        providerId: 'apple.com',
      }
      ;(getRedirectResult as Mock).mockResolvedValue(redirectResult)

      await FirebaseOAuthHelper.handleRedirectResult(onSuccess, onError)

      expect(onSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          provider: 'apple',
          providerId: 'firebase-uid-123',
        })
      )
    })

    it('should handle null redirect result', async () => {
      ;(getRedirectResult as Mock).mockResolvedValue(null)

      await FirebaseOAuthHelper.handleRedirectResult(onSuccess, onError)

      expect(onSuccess).not.toHaveBeenCalled()
      expect(onError).not.toHaveBeenCalled()
    })

    it('should handle redirect errors', async () => {
      const authError: AuthError = {
        code: 'auth/operation-not-allowed',
        message: 'Operation not allowed',
        name: 'FirebaseError',
        customData: { appName: 'apple-auth' },
      }
      ;(getRedirectResult as Mock).mockRejectedValue(authError)

      await FirebaseOAuthHelper.handleRedirectResult(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          provider: 'apple',
          message: 'Operation not allowed',
        })
      )
    })
  })

  describe('signOut', () => {
    it('should sign out current user', async () => {
      await FirebaseOAuthHelper.signOut()

      expect(mockAuth.signOut).toHaveBeenCalled()
    })
  })

  describe('getCurrentUser', () => {
    it('should return current user', () => {
      mockAuth.currentUser = mockUser as any

      const user = FirebaseOAuthHelper.getCurrentUser()

      expect(user).toBe(mockUser)
    })

    it('should return null when no user', () => {
      mockAuth.currentUser = null

      const user = FirebaseOAuthHelper.getCurrentUser()

      expect(user).toBeNull()
    })
  })

  describe('Error Handling', () => {
    it('should handle getIdToken failure', async () => {
      mockUser.getIdToken.mockRejectedValue(new Error('Token error'))
      ;(signInWithPopup as Mock).mockResolvedValue(mockUserCredential)

      await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith({
        code: 'invalid_token',
        message: 'Failed to process authentication data',
        provider: 'google',
        providerError: expect.any(Error),
      })
    })

    it('should handle generic authentication errors', async () => {
      const authError: AuthError = {
        code: 'auth/unknown-error',
        message: 'Unknown error occurred',
        name: 'FirebaseError',
      }
      ;(signInWithPopup as Mock).mockRejectedValue(authError)

      await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith({
        code: 'oauth_failed',
        message: 'Unknown error occurred',
        provider: 'google',
        providerError: authError,
      })
    })

    it('should auto-initialize when not initialized', async () => {
      ;(signInWithPopup as Mock).mockResolvedValue(mockUserCredential)

      // Clear initialized state
      ;(FirebaseOAuthHelper as any).isInitialized = false

      await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError)

      expect(GoogleAuthProvider).toHaveBeenCalled()
      expect(OAuthProvider).toHaveBeenCalledWith('apple.com')
      expect(onSuccess).toHaveBeenCalled()
    })
  })

  describe('Token Payload Construction', () => {
    it('should construct proper Google token payload', async () => {
      ;(signInWithPopup as Mock).mockResolvedValue(mockUserCredential)

      await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError)

      const userData = onSuccess.mock.calls[0][0] as OAuthUserData
      expect(userData.tokenPayload).toMatchObject({
        sub: 'firebase-uid-123',
        email: '<EMAIL>',
        email_verified: true,
        iss: 'accounts.google.com',
        aud: '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com',
        name: 'Test User',
        picture: 'https://example.com/photo.jpg',
      })
    })

    it('should construct proper Apple token payload', async () => {
      const appleUserCredential = {
        user: mockUser,
        providerId: 'apple.com',
        _tokenResponse: {},
      }
      ;(signInWithPopup as Mock).mockResolvedValue(appleUserCredential)

      await FirebaseOAuthHelper.signInWithApple(onSuccess, onError)

      const userData = onSuccess.mock.calls[0][0] as OAuthUserData
      expect(userData.tokenPayload).toMatchObject({
        sub: 'firebase-uid-123',
        email: '<EMAIL>',
        email_verified: true,
        iss: 'https://appleid.apple.com',
        aud: 'com.drmaxmuscle.max',
      })
    })
  })
})
