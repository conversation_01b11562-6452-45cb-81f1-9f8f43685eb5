import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SetLoggingModal } from '@/components/workout/SetLoggingModal'
import type { ExerciseModel } from '@/types'

// Mock data
const mockExercise: ExerciseModel = {
  Id: 1,
  Name: 'Bench Press',
  TargetReps: 8,
  TargetWeight: { Mass: 100, MassUnit: 'lbs' },
  Path: 'chest/benchpress',
  IsWarmup: false,
  IsMedium: false,
  IsBodyweight: false,
  IsNormalSet: true,
  IsDeload: false,
  IsFinished: false,
  IsMediumFinished: false,
  IsEasy: false,
  IsFirstMedium: false,
  IsFirstEasy: false,
  BodyWeight: null,
  EquipmentId: 'barbell',
  TimeSinceLastSet: '00:00:00',
  PreviousExercise: null,
  SwapExerciseTargetPath: null,
  RecommendationInKgRange: null,
  FirstWorkSet: null,
  From1RM: null,
  OneRM: null,
  IsMultiUnity: false,
  IsRecommended: true,
  HasPastLogs: true,
  IsTimeBased: false,
  IsPlate: false,
}

const mockRecommendation = {
  Lb: 105,
  Kg: 47.6, // 105 lbs in kg
}

describe('SetLoggingModal', () => {
  const mockOnSave = vi.fn()
  const mockOnClose = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  const defaultProps = {
    isOpen: true,
    exercise: mockExercise,
    setNumber: 1,
    recommendation: mockRecommendation,
    onSave: mockOnSave,
    onClose: mockOnClose,
  }

  describe('Modal Display', () => {
    it('should not render when closed', () => {
      // When
      render(<SetLoggingModal {...defaultProps} isOpen={false} />)

      // Then
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    })

    it('should render when open', () => {
      // When
      render(<SetLoggingModal {...defaultProps} />)

      // Then
      expect(screen.getByRole('dialog')).toBeInTheDocument()
      expect(screen.getByText(/log set 1 - bench press/i)).toBeInTheDocument()
    })

    it('should display target and recommendation', () => {
      // When
      render(<SetLoggingModal {...defaultProps} />)

      // Then
      expect(screen.getByText(/target: 8-10 reps/i)).toBeInTheDocument()
      expect(screen.getByText(/recommended: 105 lbs/i)).toBeInTheDocument()
    })
  })

  describe('Weight Input', () => {
    it('should show weight input with default value', () => {
      // When
      render(<SetLoggingModal {...defaultProps} />)

      // Then
      const weightInput = screen.getByLabelText('Weight') as HTMLInputElement
      expect(weightInput).toBeInTheDocument()
      expect(weightInput.value).toBe('105') // Uses recommendation
    })

    it('should update weight value', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)
      const weightInput = screen.getByLabelText('Weight')

      // When
      await user.clear(weightInput)
      await user.type(weightInput, '110')

      // Then
      expect(weightInput).toHaveValue(110)
    })

    it('should handle weight increment/decrement buttons', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)
      const weightInput = screen.getByLabelText('Weight')

      // When - Increment
      await user.click(screen.getByRole('button', { name: /increase weight/i }))

      // Then
      expect(weightInput).toHaveValue(110) // 105 + 5

      // When - Decrement
      await user.click(screen.getByRole('button', { name: /decrease weight/i }))
      await user.click(screen.getByRole('button', { name: /decrease weight/i }))

      // Then
      expect(weightInput).toHaveValue(100) // 110 - 10
    })

    it('should handle plate calculator button', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)

      // When
      await user.click(
        screen.getByRole('button', { name: /plate calculator/i })
      )

      // Then
      expect(screen.getByText(/plates needed/i)).toBeInTheDocument()
    })
  })

  describe('Reps Input', () => {
    it('should show reps input with target value', () => {
      // When
      render(<SetLoggingModal {...defaultProps} />)

      // Then
      const repsInput = screen.getByLabelText('Reps') as HTMLInputElement
      expect(repsInput).toBeInTheDocument()
      expect(repsInput.value).toBe('8') // Target reps
    })

    it('should update reps value', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)
      const repsInput = screen.getByLabelText('Reps')

      // When
      await user.clear(repsInput)
      await user.type(repsInput, '10')

      // Then
      expect(repsInput).toHaveValue(10)
    })

    it('should handle reps quick select buttons', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)

      // When
      await user.click(screen.getByRole('button', { name: '12' }))

      // Then
      expect(screen.getByLabelText('Reps')).toHaveValue(12)
    })
  })

  describe('RIR Input', () => {
    it('should show RIR dropdown', () => {
      // When
      render(<SetLoggingModal {...defaultProps} />)

      // Then
      const rirSelect = screen.getByRole('combobox', {
        name: /rir \(reps in reserve\)/i,
      })
      expect(rirSelect).toBeInTheDocument()
    })

    it('should update RIR value', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)
      const rirSelect = screen.getByRole('combobox', {
        name: /rir \(reps in reserve\)/i,
      })

      // When
      await user.selectOptions(rirSelect, '2')

      // Then
      expect(rirSelect).toHaveValue('2')
    })

    it('should show RIR explanation', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)

      // When
      await user.click(screen.getByRole('button', { name: /what is rir/i }))

      // Then
      expect(screen.getByText('Reps in Reserve (RIR)')).toBeInTheDocument()
      expect(screen.getByText(/how many more reps/i)).toBeInTheDocument()
    })
  })

  describe('Bodyweight Exercise', () => {
    it('should handle bodyweight exercise differently', () => {
      // Given
      const bodyweightExercise = {
        ...mockExercise,
        IsBodyweight: true,
        BodyWeight: { Mass: 180, MassUnit: 'lbs' as const },
      }

      // When
      render(
        <SetLoggingModal {...defaultProps} exercise={bodyweightExercise} />
      )

      // Then
      expect(screen.getByText(/bodyweight: 180 lbs/i)).toBeInTheDocument()
      expect(screen.getByLabelText('Additional Weight')).toBeInTheDocument()
    })
  })

  describe('Time-Based Exercise', () => {
    it('should show time input for time-based exercises', () => {
      // Given
      const timeExercise = {
        ...mockExercise,
        IsTimeBased: true,
      }

      // When
      render(<SetLoggingModal {...defaultProps} exercise={timeExercise} />)

      // Then
      expect(screen.getByLabelText(/duration \(seconds\)/i)).toBeInTheDocument()
      expect(screen.queryByLabelText(/^reps$/i)).not.toBeInTheDocument()
    })

    it('should handle time input', async () => {
      // Given
      const user = userEvent.setup()
      const timeExercise = {
        ...mockExercise,
        IsTimeBased: true,
      }
      render(<SetLoggingModal {...defaultProps} exercise={timeExercise} />)

      // When
      const timeInput = screen.getByLabelText('Duration (seconds)')
      await user.clear(timeInput)
      await user.type(timeInput, '45')

      // Then
      expect(timeInput).toHaveValue(45)
    })
  })

  describe('Form Submission', () => {
    it('should save with valid data', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)

      // When
      await user.clear(screen.getByLabelText('Weight'))
      await user.type(screen.getByLabelText('Weight'), '100')
      await user.clear(screen.getByLabelText('Reps'))
      await user.type(screen.getByLabelText('Reps'), '8')
      await user.selectOptions(
        screen.getByRole('combobox', { name: /rir \(reps in reserve\)/i }),
        '2'
      )
      await user.click(screen.getByRole('button', { name: /save set/i }))

      // Then
      expect(mockOnSave).toHaveBeenCalledWith({
        weight: 100,
        reps: 8,
        rir: 2,
        isWarmup: false,
        notes: '',
      })
    })

    it('should validate required fields', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)

      // When
      await user.clear(screen.getByLabelText('Weight'))
      await user.clear(screen.getByLabelText('Reps'))
      await user.click(screen.getByRole('button', { name: /save set/i }))

      // Then
      expect(mockOnSave).not.toHaveBeenCalled()
      expect(screen.getByText(/weight is required/i)).toBeInTheDocument()
      expect(screen.getByText(/reps must be at least 1/i)).toBeInTheDocument()
    })

    it('should handle warmup set', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)

      // When
      await user.click(screen.getByLabelText(/warmup set/i))
      await user.click(screen.getByRole('button', { name: /save set/i }))

      // Then
      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          isWarmup: true,
        })
      )
    })

    it('should handle notes', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)

      // When
      const notesField = screen.getByLabelText(/notes/i)
      // Use fireEvent.change for more reliable text input
      fireEvent.change(notesField, { target: { value: 'Felt strong' } })
      await user.click(screen.getByRole('button', { name: /save set/i }))

      // Then
      await waitFor(() => {
        expect(mockOnSave).toHaveBeenCalledWith(
          expect.objectContaining({
            notes: 'Felt strong',
          })
        )
      })
    })
  })

  describe('Modal Actions', () => {
    it('should close modal on cancel', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)

      // When
      await user.click(screen.getByRole('button', { name: /cancel/i }))

      // Then
      expect(mockOnClose).toHaveBeenCalled()
    })

    it('should close modal on backdrop click', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)

      // When
      const backdrop = screen.getByRole('dialog').parentElement
      await user.click(backdrop!)

      // Then
      expect(mockOnClose).toHaveBeenCalled()
    })

    it('should close modal on escape key', () => {
      // Given
      render(<SetLoggingModal {...defaultProps} />)

      // When
      fireEvent.keyDown(screen.getByRole('dialog'), { key: 'Escape' })

      // Then
      expect(mockOnClose).toHaveBeenCalled()
    })
  })

  describe('Previous Set Data', () => {
    it('should display previous set information', () => {
      // Given
      const previousSet = {
        weight: 95,
        reps: 8,
        rir: 3,
      }

      // When
      render(<SetLoggingModal {...defaultProps} previousSet={previousSet} />)

      // Then
      expect(
        screen.getByText(/previous: 8 × 95 lbs \(rir: 3\)/i)
      ).toBeInTheDocument()
    })

    it('should allow copying previous set', async () => {
      // Given
      const user = userEvent.setup()
      const previousSet = {
        weight: 95,
        reps: 8,
        rir: 3,
      }
      render(<SetLoggingModal {...defaultProps} previousSet={previousSet} />)

      // When
      await user.click(screen.getByRole('button', { name: /copy previous/i }))

      // Then
      expect(screen.getByLabelText('Weight')).toHaveValue(95)
      expect(screen.getByLabelText('Reps')).toHaveValue(8)
      expect(
        screen.getByRole('combobox', { name: /rir \(reps in reserve\)/i })
      ).toHaveValue('3')
    })
  })

  describe('Accessibility', () => {
    it('should trap focus within modal', async () => {
      // Given
      render(<SetLoggingModal {...defaultProps} />)

      // Wait for modal to mount and focus to be set
      await waitFor(() => {
        const weightInput = screen.getByLabelText('Weight')
        expect(weightInput).toHaveFocus()
      })
    })

    it('should have proper ARIA attributes', () => {
      // When
      render(<SetLoggingModal {...defaultProps} />)

      // Then
      const modal = screen.getByRole('dialog')
      expect(modal).toHaveAttribute('aria-modal', 'true')
      expect(modal).toHaveAttribute('aria-labelledby')
    })

    it('should announce errors to screen readers', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetLoggingModal {...defaultProps} />)

      // When
      await user.clear(screen.getByLabelText('Weight'))
      await user.click(screen.getByRole('button', { name: /save set/i }))

      // Then
      const error = screen.getByText(/weight is required/i)
      expect(error).toHaveAttribute('role', 'alert')
    })
  })

  describe('Mass Unit Display', () => {
    it('should display weights in kg when userMassUnit is kg', () => {
      // When
      render(<SetLoggingModal {...defaultProps} userMassUnit="kg" />)

      // Then
      expect(screen.getByText(/recommended: 47.6 kg/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/weight \(kg\)/i)).toBeInTheDocument()
    })

    it('should display weights in lbs when userMassUnit is lbs', () => {
      // When
      render(<SetLoggingModal {...defaultProps} userMassUnit="lbs" />)

      // Then
      expect(screen.getByText(/recommended: 105 lbs/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/weight \(lbs\)/i)).toBeInTheDocument()
    })

    it('should default to lbs when userMassUnit is not provided', () => {
      // When
      render(<SetLoggingModal {...defaultProps} />)

      // Then
      expect(screen.getByText(/recommended: 105 lbs/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/weight \(lbs\)/i)).toBeInTheDocument()
    })

    it('should display previous set weight in user mass unit', () => {
      // Given
      const previousSet = { weight: 100, reps: 8, rir: 2 }

      // When
      render(
        <SetLoggingModal
          {...defaultProps}
          previousSet={previousSet}
          userMassUnit="kg"
        />
      )

      // Then
      expect(screen.getByText(/previous: 8 × 100 kg/i)).toBeInTheDocument()
    })

    it('should display additional weight label for bodyweight exercises', () => {
      // Given
      const bodyweightExercise = { ...mockExercise, IsBodyweight: true }

      // When
      render(
        <SetLoggingModal
          {...defaultProps}
          exercise={bodyweightExercise}
          userMassUnit="kg"
        />
      )

      // Then
      expect(
        screen.getByLabelText(/additional weight \(kg\)/i)
      ).toBeInTheDocument()
    })
  })
})
