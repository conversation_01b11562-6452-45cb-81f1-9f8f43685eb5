import { test, expect } from '@playwright/test'

test.describe('Rest Timer Between Sets', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.goto('/login')
    await page.evaluate(() => {
      const authData = {
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
        user: {
          id: 'test-user',
          email: '<EMAIL>',
          username: 'testuser',
        },
      }
      localStorage.setItem('auth-storage', JSON.stringify(authData))
    })
  })

  test('should navigate to rest timer after saving a set', async ({ page }) => {
    // Navigate to workout
    await page.goto('/workout')

    // Start workout
    await page.getByTestId('start-workout-button').click()

    // Navigate to exercise
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Fill in set data
    await page.getByLabel('Weight').fill('100')
    await page.getByLabel('Reps').fill('10')

    // Save set
    await page.getByRole('button', { name: 'Save Set' }).click()

    // Should navigate to rest timer with between-sets parameter
    await expect(page).toHaveURL(/\/workout\/rest-timer\?between-sets=true/)

    // Should show rest timer
    await expect(page.getByRole('timer')).toBeVisible()
    await expect(page.getByText('Rest')).toBeVisible()

    // Should show next set info
    await expect(page.getByText(/Next: Set 2 of/)).toBeVisible()
  })

  test('should return to exercise after rest timer completes', async ({
    page,
  }) => {
    // Navigate directly to rest timer between sets
    await page.goto('/workout/rest-timer?between-sets=true')

    // Skip the timer
    await page.getByRole('button', { name: 'Skip' }).click()

    // Should navigate back to exercise
    await expect(page).toHaveURL(/\/workout\/exercise\/\d+/)
  })

  test('should show exercise complete after last set', async ({ page }) => {
    // Set up workout state with last set
    await page.goto('/workout')
    await page.evaluate(() => {
      // Mock being on the last set
      const workoutState = {
        currentSetIndex: 3, // Assuming 4 sets total (0-indexed)
        currentExerciseIndex: 0,
        exercises: [{ Id: 1, Label: 'Bench Press' }],
      }
      localStorage.setItem('workout-store', JSON.stringify(workoutState))
    })

    // Navigate to exercise
    await page.goto('/workout/exercise/1')

    // Save last set
    await page.getByRole('button', { name: 'Save Set' }).click()

    // Should show exercise complete view
    await expect(page.getByText('Great job!')).toBeVisible()
    await expect(page.getByText('Bench Press complete')).toBeVisible()
  })

  test('should show different rest durations for warmup vs work sets', async ({
    page,
  }) => {
    // This test verifies that the rest timer uses appropriate durations
    // based on whether it's after a warmup or work set

    // Navigate to rest timer
    await page.goto('/workout/rest-timer?between-sets=true')

    // Verify timer is visible and functional
    await expect(page.getByRole('timer')).toBeVisible()

    // Check that warmup rest shows appropriate label
    await page.evaluate(() => {
      const workoutState = {
        currentSetIndex: 0, // First set (warmup)
        exercises: [{ Id: 1, Label: 'Bench Press' }],
      }
      localStorage.setItem('workout-store', JSON.stringify(workoutState))
    })

    // Reload to apply state
    await page.reload()

    // Should show warmup rest
    await expect(page.getByText('Warmup Rest')).toBeVisible()
  })
})
