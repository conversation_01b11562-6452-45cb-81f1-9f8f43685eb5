import type { ProgramModel, ProgramProgress } from '@/types/program'

/**
 * Calculate which week of the program we're in
 */
export function getWeekNumber(startDate: string, currentDate: string): number {
  try {
    const start = new Date(startDate)
    const current = new Date(currentDate)

    // Validate dates
    if (Number.isNaN(start.getTime()) || Number.isNaN(current.getTime())) {
      return 1
    }

    const daysDiff = Math.floor(
      (current.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
    )
    return Math.floor(daysDiff / 7) + 1
  } catch {
    return 1
  }
}

/**
 * Calculate overall program progress based on workouts completed
 */
export function calculateProgress(
  program: ProgramModel,
  workouts: Array<{ date: string }>
): ProgramProgress {
  const now = new Date()
  const startDate = new Date(program.startDate)
  const daysSinceStart =
    Math.floor((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) +
    1
  const daysCompleted = Math.min(daysSinceStart, program.totalDays)

  const percentage =
    program.totalWorkouts > 0
      ? Math.round((program.workoutsCompleted / program.totalWorkouts) * 100)
      : 0

  const currentWeek = getWeekNumber(program.startDate, now.toISOString())

  // Calculate workouts this week
  const weekStart = new Date(startDate)
  weekStart.setDate(weekStart.getDate() + (currentWeek - 1) * 7)
  const weekEnd = new Date(weekStart)
  weekEnd.setDate(weekEnd.getDate() + 7)

  const workoutsThisWeek = workouts.filter((w) => {
    const workoutDate = new Date(w.date)
    return workoutDate >= weekStart && workoutDate < weekEnd
  }).length

  const remainingWorkouts = Math.max(
    0,
    program.totalWorkouts - program.workoutsCompleted
  )

  return {
    percentage: Math.min(percentage, 100),
    daysCompleted,
    currentWeek,
    totalWorkouts: program.totalWorkouts,
    workoutsThisWeek,
    remainingWorkouts,
  }
}

/**
 * Project when the program will be completed based on current pace
 */
export function calculateCompletionDate(
  program: ProgramModel,
  progress: ProgramProgress
): Date | null {
  // Already completed
  if (progress.percentage >= 100) {
    return null
  }

  const workoutsRemaining = program.totalWorkouts - program.workoutsCompleted

  // No workouts completed yet - use expected pace
  if (program.workoutsCompleted === 0) {
    const expectedWorkoutsPerWeek =
      program.totalWorkouts / (program.totalDays / 7)
    const weeksRemaining = workoutsRemaining / expectedWorkoutsPerWeek
    const daysRemaining = Math.ceil(weeksRemaining * 7)

    const completionDate = new Date()
    completionDate.setDate(completionDate.getDate() + daysRemaining)
    return completionDate
  }

  // Calculate based on current pace
  const daysElapsed = progress.daysCompleted
  const workoutsPerDay = program.workoutsCompleted / daysElapsed

  // Avoid division by zero and unrealistic projections
  if (workoutsPerDay <= 0) {
    return null
  }

  let daysToComplete = Math.ceil(workoutsRemaining / workoutsPerDay)

  // Cap at 1 year max
  const maxDays = 365
  daysToComplete = Math.min(daysToComplete, maxDays)

  const completionDate = new Date()
  completionDate.setDate(completionDate.getDate() + daysToComplete)

  return completionDate
}

/**
 * Calculate average workouts per week from workout history
 */
export function getWorkoutFrequency(workouts: Array<{ date: string }>): number {
  if (workouts.length < 2) {
    return 0
  }

  // Sort workouts by date
  const sortedWorkouts = [...workouts].sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  )

  const firstWorkout = new Date(sortedWorkouts[0]!.date)
  const lastWorkout = new Date(sortedWorkouts[sortedWorkouts.length - 1]!.date)

  const daysDiff =
    (lastWorkout.getTime() - firstWorkout.getTime()) / (1000 * 60 * 60 * 24)
  const weeks = daysDiff / 7

  // Avoid division by very small numbers
  if (weeks < 0.1) {
    return 0
  }

  return workouts.length / weeks
}

/**
 * Calculate days remaining in the program
 */
export function getDaysRemaining(program: ProgramModel): number {
  const remaining = program.totalDays - program.currentDay
  return Math.max(0, remaining)
}

/**
 * Calculate missed workouts based on expected pace
 */
export function getMissedWorkouts(program: ProgramModel): number {
  const weeksElapsed = Math.floor(program.currentDay / 7)
  const expectedWorkoutsPerWeek =
    program.totalWorkouts / (program.totalDays / 7)
  const expectedWorkouts = Math.floor(weeksElapsed * expectedWorkoutsPerWeek)

  const missed = expectedWorkouts - program.workoutsCompleted
  return Math.max(0, missed)
}

/**
 * Calculate expected workouts per week for the program
 */
export function getWorkoutsPerWeek(program: ProgramModel): number {
  const totalWeeks = program.totalDays / 7
  return program.totalWorkouts / totalWeeks
}

/**
 * Calculate current workout streak (consecutive days with workouts)
 */
export function calculateStreak(workouts: Array<{ date: string }>): number {
  if (workouts.length === 0) {
    return 0
  }

  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // Sort workouts by date (most recent first)
  const sortedWorkouts = [...workouts].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  )

  // Get unique dates (remove multiple workouts on same day)
  const uniqueDates = new Set<string>()
  sortedWorkouts.forEach((w) => {
    const date = new Date(w.date)
    date.setHours(0, 0, 0, 0)
    uniqueDates.add(date.toDateString())
  })

  const workoutDates = Array.from(uniqueDates).map((d) => new Date(d))

  if (workoutDates.length === 0) {
    return 0
  }

  // Check if there was a workout today or yesterday
  const mostRecent = workoutDates[0]!
  const daysSinceLastWorkout = Math.floor(
    (today.getTime() - mostRecent.getTime()) / (1000 * 60 * 60 * 24)
  )

  if (daysSinceLastWorkout > 1) {
    return 0 // Streak is broken
  }

  // Count consecutive days
  let streak = 1
  for (let i = 1; i < workoutDates.length; i++) {
    const current = workoutDates[i - 1]!
    const previous = workoutDates[i]!
    const daysDiff = Math.floor(
      (current.getTime() - previous.getTime()) / (1000 * 60 * 60 * 24)
    )

    if (daysDiff === 1) {
      streak++
    } else {
      break // Streak broken
    }
  }

  return streak
}
