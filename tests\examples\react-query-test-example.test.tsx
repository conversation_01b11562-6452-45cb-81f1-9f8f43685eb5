/**
 * Example test file demonstrating React Query testing patterns
 * This file shows best practices for testing components and hooks that use React Query
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useQuery, useMutation } from '@tanstack/react-query'
import {
  renderWithProviders,
  createQueryWrapper,
  createTestQueryClient,
  mockSuccessfulQuery,
  mockFailedQuery,
  screen,
  waitFor,
} from '../test-utils'
import {
  setQueryData,
  waitForQuerySuccess,
  createMockMutation,
  mockNetworkConditions,
} from '../react-query-helpers'

// Example 1: Testing a component with useQuery
function UserProfile({ userId }: { userId: string }) {
  const { data, isLoading, error } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => fetch(`/api/users/${userId}`).then((res) => res.json()),
  })

  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error: {(error as Error).message}</div>
  return <div>Hello, {data?.name}!</div>
}

describe('UserProfile Component', () => {
  it('should display user data when query succeeds', async () => {
    const mockUser = { id: '1', name: 'John Doe' }
    global.fetch = mockSuccessfulQuery(mockUser)

    renderWithProviders(<UserProfile userId="1" />)

    expect(screen.getByText('Loading...')).toBeInTheDocument()

    await waitFor(() => {
      expect(screen.getByText('Hello, John Doe!')).toBeInTheDocument()
    })
  })

  it('should display error when query fails', async () => {
    global.fetch = mockFailedQuery('User not found')

    renderWithProviders(<UserProfile userId="999" />)

    await waitFor(() => {
      expect(screen.getByText('Error: User not found')).toBeInTheDocument()
    })
  })
})

// Example 2: Testing a custom hook with React Query
function useUserData(userId: string) {
  return useQuery({
    queryKey: ['user', userId],
    queryFn: async () => {
      const response = await fetch(`/api/users/${userId}`)
      if (!response.ok) throw new Error('Failed to fetch user')
      return response.json()
    },
    enabled: !!userId,
  })
}

describe('useUserData Hook', () => {
  let queryClient: ReturnType<typeof createTestQueryClient>

  beforeEach(() => {
    queryClient = createTestQueryClient()
  })

  it('should fetch user data successfully', async () => {
    const mockUser = { id: '1', name: 'Jane Doe' }
    global.fetch = mockSuccessfulQuery(mockUser)

    const { result } = renderHook(() => useUserData('1'), {
      wrapper: createQueryWrapper(queryClient),
    })

    expect(result.current.isLoading).toBe(true)

    await waitForQuerySuccess(queryClient, ['user', '1'])

    expect(result.current.data).toEqual(mockUser)
    expect(result.current.isLoading).toBe(false)
  })

  it('should not fetch when userId is empty', () => {
    const { result } = renderHook(() => useUserData(''), {
      wrapper: createQueryWrapper(queryClient),
    })

    expect(result.current.isLoading).toBe(false)
    expect(result.current.data).toBeUndefined()
    expect(global.fetch).not.toHaveBeenCalled()
  })
})

// Example 3: Testing mutations
function useUpdateUser() {
  return useMutation({
    mutationFn: async (userData: { id: string; name: string }) => {
      const response = await fetch(`/api/users/${userData.id}`, {
        method: 'PUT',
        body: JSON.stringify(userData),
      })
      if (!response.ok) throw new Error('Failed to update user')
      return response.json()
    },
  })
}

describe('useUpdateUser Mutation', () => {
  it('should update user successfully', async () => {
    const updatedUser = { id: '1', name: 'Updated Name' }
    const mockMutation = createMockMutation({ data: updatedUser })
    global.fetch = mockMutation.mutationFn

    const { result } = renderHook(() => useUpdateUser(), {
      wrapper: createQueryWrapper(),
    })

    act(() => {
      result.current.mutate(updatedUser)
    })

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true)
      expect(result.current.data).toEqual(updatedUser)
    })
  })
})

// Example 4: Testing with pre-populated cache
describe('Testing with Cache', () => {
  it('should use cached data immediately', () => {
    const queryClient = createTestQueryClient()
    const cachedUser = { id: '1', name: 'Cached User' }

    // Pre-populate the cache
    setQueryData(queryClient, ['user', '1'], cachedUser)

    const { result } = renderHook(() => useUserData('1'), {
      wrapper: createQueryWrapper(queryClient),
    })

    // Data should be available immediately
    expect(result.current.data).toEqual(cachedUser)
    expect(result.current.isLoading).toBe(false)
  })
})

// Example 5: Testing offline behavior
describe('Offline Behavior', () => {
  it('should handle offline state gracefully', async () => {
    const cleanup = mockNetworkConditions({ isOnline: false })

    const { result } = renderHook(() => useUserData('1'), {
      wrapper: createQueryWrapper(),
    })

    await waitFor(() => {
      expect(result.current.isError).toBe(true)
    })

    cleanup()
  })
})

// Example 6: Testing optimistic updates
function useOptimisticUpdate() {
  const queryClient = createTestQueryClient()

  return useMutation({
    mutationFn: async (data: { id: string; name: string }) => {
      const response = await fetch(`/api/users/${data.id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      })
      return response.json()
    },
    onMutate: async (newData) => {
      // Cancel in-flight queries
      await queryClient.cancelQueries({ queryKey: ['user', newData.id] })

      // Save current data
      const previousData = queryClient.getQueryData(['user', newData.id])

      // Optimistically update
      queryClient.setQueryData(['user', newData.id], newData)

      return { previousData }
    },
    onError: (err, newData, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(['user', newData.id], context.previousData)
      }
    },
  })
}

describe('Optimistic Updates', () => {
  it('should update UI optimistically', async () => {
    const queryClient = createTestQueryClient()
    const originalUser = { id: '1', name: 'Original' }
    const updatedUser = { id: '1', name: 'Updated' }

    // Set initial data
    setQueryData(queryClient, ['user', '1'], originalUser)

    // Mock successful update
    global.fetch = mockSuccessfulQuery(updatedUser)

    const { result } = renderHook(() => useOptimisticUpdate(), {
      wrapper: createQueryWrapper(queryClient),
    })

    act(() => {
      result.current.mutate(updatedUser)
    })

    // Check optimistic update happened immediately
    const cachedData = queryClient.getQueryData(['user', '1'])
    expect(cachedData).toEqual(updatedUser)
  })
})
