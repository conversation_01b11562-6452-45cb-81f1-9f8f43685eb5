# Dr. Muscle X - Testing Documentation

## Quick Start - Development Testing

### Essential Commands

```bash
# During development (1-2 min)
npm run test:quick          # Type check + lint + unit tests

# Before committing (5-7 min)
npm run test:before-commit  # Quick tests + critical E2E

# Before creating PR (15-20 min)
npm run test:before-push    # Full test suite

# Interactive debugging
npm run test:e2e:ui        # Debug E2E tests interactively
```

### Development Workflow

1. **Make changes** → Run `npm run test:quick`
2. **Ready to commit** → Run `npm run test:before-commit`
3. **Creating PR** → Run `npm run test:before-push`

## Testing Strategy

### Test Types

- **Unit Tests:** Test individual functions and components in isolation
- **Integration Tests:** Test interactions between components and services
- **End-to-End Tests:** Test complete user workflows
- **Component Tests:** Test UI components with user interactions

### Testing Framework

- **Unit/Integration Tests:** Vitest + React Testing Library
- **E2E Tests:** Playwright with mobile device emulation
- **Mobile Testing:** Device simulation with touch event testing
- **PWA Testing:** Service Worker and offline functionality testing
- **Performance Testing:** Lighthouse CI with mobile-first metrics
- **Security Testing:** OWASP-based security test suite

### Coverage Goals

- **Minimum Coverage:** 80%
- **Target Coverage:** 90%
- **Critical Path Coverage:** 100%

### Test Categorization

- **@critical** - Essential user paths (login, workout, sync)
- **@mobile** - Mobile-specific functionality
- **@smoke** - Basic functionality checks
- **@regression** - Regression test suite

## Test Status

### Implemented Tests

#### TypeScript Type Tests (Prompt 2)

- **tests/unit/types/api.test.ts** - API types validation (21 tests)
  - WorkoutTemplateGroupModel type checking
  - WorkoutTemplateModel type checking
  - ExerciseModel and ExerciseModelWithReco type checking
  - RecommendationModel and WarmUps type checking
  - Authentication models (LoginModel, LoginSuccessResult, RegisterModel)
  - Logging models (WorkoutLogSerieModel, NewExerciceLogModel)
  - Utility models (MultiUnityWeight, ApiResponse, BooleanModel)
  - API request models (GetRecommendationForExerciseModel)
  - Optional fields validation
  - Type guards implementation pending

- **tests/unit/types/app.test.ts** - Application types validation (10 tests)
  - UI state types (UIState, LoadingState, ErrorState)
  - Form data types (LoginFormData, RegisterFormData, SetFormData)
  - Store state types (AuthStoreState, WorkoutStoreState)
  - Error types (ValidationError, APIError)
  - Utility types (AsyncState)
  - Component props types

- **tests/unit/types/utils.test.ts** - Utility types and functions (13 tests)
  - Type helper validation (Nullable, DeepPartial, ValueOf, ArrayElement)
  - Type guards (isApiResponse, isLoginSuccessResult, isMultiUnityWeight, isErrorResponse)
  - Validation functions (isValidEmail, isValidWeight, isNonEmptyArray, hasProperty)
  - Utility functions (createMultiUnityWeight, parseApiError)

- **tests/unit/types/exports.test.ts** - Type exports validation (4 tests)
  - API types export verification
  - App types export verification
  - Utility functions export verification
  - Utility types usage validation

**Total Type Tests:** 48 tests (Pending execution)

#### Authentication System Tests (Prompt 3)

- **tests/unit/stores/authStore.test.ts** - Auth store functionality (21 tests)
  - Initial state validation
  - Login flow with success and error handling
  - Token management and refresh logic
  - Logout functionality
  - Auth state persistence
  - Error handling and recovery
  - Loading states

- **tests/unit/api/client.test.ts** - API client configuration (16 tests)
  - Base configuration validation
  - Auth header injection for protected requests
  - Automatic token refresh on 401 responses
  - Error handling (network, timeout, 403)
  - Retry logic with exponential backoff
  - Request/response interceptors

- **tests/unit/api/auth.test.ts** - Auth API endpoints (13 tests)
  - Login endpoint with success/failure
  - Registration endpoint
  - Token refresh endpoint
  - Logout endpoint (graceful error handling)
  - Password reset functionality
  - User info retrieval

- **tests/unit/hooks/useAuth.test.ts** - React Query integration (10 tests)
  - Login mutation with React Query
  - Logout mutation with cache invalidation
  - Loading and error states
  - Auth state reactivity
  - Automatic token refresh

**Total Auth Tests:** 60 tests (Pending execution)

#### Rest Timer System Tests (Prompt 9)

- **tests/unit/hooks/useTimer.test.tsx** - Timer hook functionality (27 tests)
  - Timer initialization with duration
  - Start, pause, resume, reset, skip controls
  - Countdown functionality with RAF
  - Time formatting (MM:SS)
  - Callbacks (onComplete, onTick)
  - Auto-start functionality
  - Timer accuracy and drift handling
  - Cleanup on unmount
  - Edge cases (negative duration, rapid start/stop)

- **tests/unit/components/workout/RestTimer.test.tsx** - Rest timer component (23 tests)
  - Timer display in MM:SS format
  - Different rest times for warmup vs work sets
  - Next exercise/set information display
  - Timer countdown and completion
  - Pause/resume controls
  - Skip functionality
  - Visual progress ring
  - Audio notifications
  - Mobile optimizations (large touch targets, wake lock)
  - Keyboard shortcuts (Space for pause, S for skip)
  - Accessibility features
  - Background timer handling

- **tests/unit/components/workout/TimerScreen.test.tsx** - Timer screen integration (29 tests)
  - Full-screen timer display
  - Current exercise information
  - Next exercise/set preview
  - Timer auto-start on mount
  - Navigation after timer completion
  - Sound/vibration settings persistence
  - Visual feedback (warning colors)
  - Mobile features (prevent screen sleep)
  - Error handling for missing data
  - Integration with workout flow

**Total Rest Timer Tests:** 79 tests (All passing)

#### Workout Completion Tests (Prompt 10)

- **tests/unit/components/workout/WorkoutComplete.test.tsx** - Workout completion flow (20 tests)
  - Workout summary display (duration, exercises, sets, volume)
  - Celebration UI and animation
  - Workout statistics calculation
  - Average RIR display
  - Partial workout detection
  - Workout completion actions
  - Save workout with loading states
  - Navigation after completion
  - Workout notes functionality
  - Error handling and retry
  - Offline mode indication
  - Mobile optimization (touch targets)
  - Personal records display

**Total Workout Completion Tests:** 20 tests (All passing)

**Grand Total Tests:** 408 tests (All passing)

### Test Coverage Report

_No coverage data available yet_

### Test Summary

- **Total Tests:** 408
- **Passing:** 408 (100%)
- **Failing:** 0
- **Test Suites:** 26 passing

### Recent Test Achievements

1. **Prompt 9 - Rest Timer System:** Fixed all RAF mocking issues, all 79 tests passing
2. **Prompt 10 - Workout Completion:** Implemented 20 comprehensive tests following TDD
3. **Overall Progress:** Increased from 388 to 408 tests, maintaining 100% pass rate

## Test Organization

### File Structure

```
tests/
├── unit/                    # Unit tests
│   ├── components/          # Component tests
│   ├── hooks/              # Custom hook tests
│   ├── stores/             # State management tests
│   └── utils/              # Utility function tests
├── integration/            # Integration tests
│   ├── api/                # API integration tests
│   ├── auth/               # Authentication flow tests
│   └── mobile/             # Mobile-specific integration tests
├── e2e/                    # End-to-end tests
│   ├── mobile/             # Mobile device emulation tests
│   ├── pwa/                # PWA functionality tests
│   └── performance/        # Performance and lighthouse tests
└── fixtures/               # Test data and mocks
    ├── api/                # API response mocks
    ├── mobile/             # Mobile device configurations
    └── pwa/                # PWA testing utilities
```

### Naming Conventions

- Test files should end with `.test.js` or `.spec.js`
- Test descriptions should be clear and descriptive
- Use `describe()` blocks to group related tests
- Use `it()` or `test()` for individual test cases

### Test Data

- **Test Account:** `<EMAIL>` / `Dr123456`
- **API Mocks:** Located in `tests/fixtures/api/`
- **Device Configs:** Mobile viewports in `playwright.config.ts`

## Continuous Integration

### Enhanced GitHub Actions CI/CD Pipeline

The CI/CD pipeline has been significantly enhanced with comprehensive parallel and sequential jobs for fast feedback and thorough validation. It runs automatically on every push to main/develop branches and on all pull requests.

#### Parallel Jobs (Fast Feedback)

1. **Code Quality Checks**
   - TypeScript validation with `tsc --noEmit`
   - ESLint for code quality and style enforcement
   - Prettier for code formatting verification

2. **Security Checks**
   - Dependency vulnerabilities scanning with `npm audit`
   - Security-focused linting with ESLint security plugin
   - Secret scanning using TruffleHog
   - License compliance checks (blocks GPL licenses)

3. **Build & Bundle Analysis**
   - Next.js production build with caching
   - Bundle size analysis (enforces < 150KB limit)
   - Build artifact generation for subsequent jobs

#### Sequential Jobs

4. **Unit Tests & Coverage**
   - Runs all unit/integration tests with Vitest
   - Enforces 80% minimum coverage threshold
   - Generates detailed coverage reports
   - Automatic PR comments with coverage metrics

5. **Critical Path E2E Tests**
   - Tests core user journeys marked with @critical tag
   - Mobile-first testing on multiple devices:
     - iPhone 13 (390x844)
     - Pixel 5 (393x851)
     - Galaxy S9+ (360x740)
   - Validates login flow, workout creation, touch targets

6. **Full E2E Suite** (Main branch & PRs)
   - Comprehensive end-to-end testing
   - All test scenarios including edge cases
   - Mobile-specific gesture testing

7. **Performance Checks**
   - Lighthouse CI with strict mobile-first metrics:
     - First Contentful Paint < 1s
     - Time to Interactive < 1.5s
     - Mobile performance score > 90
     - Bundle size < 150KB

8. **Accessibility Tests**
   - Automated WCAG compliance testing with axe-core
   - Validates 44px minimum touch targets
   - Color contrast, alt text, form labels
   - Keyboard navigation and ARIA landmarks

9. **API Contract Tests**
   - Validates API request/response shapes
   - Ensures frontend-backend compatibility
   - Tests error response formats

10. **Security Tests**
    - OWASP security test suite + dependency scanning
    - XSS protection validation
    - Authentication security checks
    - CSRF protection verification

### Pipeline Features

- **Smart Caching**: npm dependencies, Next.js build cache, Playwright browsers
- **Self-hosted macOS Runner**: Consistent environment across all jobs
- **Detailed Artifacts**: Coverage reports, Playwright test results, Lighthouse reports
- **Automatic Deploy Preview**: Vercel preview deployments for PRs

### Quality Gates

- ❌ Pipeline fails if:
  - TypeScript errors exist
  - ESLint errors (not warnings)
  - Code formatting issues
  - Any test fails
  - Coverage drops below 80%
  - Bundle exceeds 150KB
  - Lighthouse mobile score < 90
  - Security vulnerabilities (moderate+)
  - Critical E2E tests fail

### New Test Scripts

```bash
# Security & Quality
npm run lint:security        # ESLint security checks
npm run audit:licenses       # License compliance

# E2E Testing
npm run test:e2e:critical    # Critical paths only
npm run test:e2e:full        # Complete E2E suite

# Specialized Testing
npm run test:accessibility   # WCAG compliance tests
npm run test:api:contracts   # API contract validation
npm run lighthouse:ci        # Performance testing
```

### Configuration Files

- `.github/workflows/ci-optimized.yml`: Main CI/CD workflow
- `.github/workflows/README.md`: Detailed pipeline documentation
- `playwright.ci.config.ts`: CI-specific E2E configuration
- `playwright.a11y.config.ts`: Accessibility test configuration
- `vitest.api.config.ts`: API contract test configuration
- `lighthouse-ci.config.js`: Performance testing thresholds

### Optimized CI/CD Pipeline

#### Parallel Execution Strategy

- **Quick Checks:** Type, lint, and format run in parallel
- **Test Sharding:** E2E tests split across multiple runners
- **Conditional Execution:** Full suite only on main/PR with label

#### Pipeline Stages

1. **Quick Validation (2-3 min)**
   - TypeScript compilation
   - ESLint + Prettier checks
   - Run in parallel for speed

2. **Unit Tests (2-3 min)**
   - Vitest with coverage reporting
   - 80% minimum coverage requirement
   - Parallel test execution

3. **Build & Analysis (3-5 min)**
   - Next.js production build
   - Bundle size analysis (<150KB)
   - Build artifact caching

4. **Critical E2E Tests (5-7 min)**
   - Tests marked with @critical
   - 2-way sharding for speed
   - Mobile Safari + Chrome only

5. **Full E2E Suite (10-15 min)**
   - All E2E tests
   - 4-way sharding
   - Runs on main branch or with 'full-test' label

6. **Quality Gates (Parallel)**
   - Security audit
   - License compliance
   - API contract tests
   - Lighthouse performance

### Quality Gates

- All tests must pass before merge
- Code coverage must be ≥80% (target 90%)
- Mobile performance score must be ≥90 (Lighthouse)
- PWA installation must work on mobile devices
- Bundle size must be ≤150KB (mobile bandwidth optimization)
- No high/critical security vulnerabilities
- TypeScript strict mode - no 'any' types

## Security Testing

### OWASP-Based Security Test Suite

#### Implemented Security Tests (tests/e2e/security.spec.ts)

1. **XSS Protection**
   - Toast notification XSS prevention
   - User input sanitization
   - Content Security Policy headers

2. **Authentication Security**
   - No token exposure in localStorage
   - httpOnly cookie verification
   - Session timeout implementation

3. **CSRF Protection**
   - CSRF tokens in state-changing requests

4. **Input Validation**
   - Email format validation
   - Password requirement enforcement

5. **Security Headers**
   - X-Content-Type-Options: nosniff
   - X-Frame-Options: DENY
   - X-XSS-Protection: 1; mode=block
   - Strict-Transport-Security
   - Content-Security-Policy

6. **Injection Attack Prevention**
   - SQL injection protection
   - Script injection prevention

7. **Data Exposure Protection**
   - No sensitive data in API responses
   - No debug information in production

8. **Secure Communication**
   - HTTPS-only resource loading
   - Open redirect prevention

### Security Improvements Implemented

- Fixed XSS vulnerability in toast.tsx
- Removed localStorage token storage
- Added CSRF token mechanism
- Added input sanitization utilities
- Enhanced CSP headers with HSTS
- Added session timeout (30 minutes)

## Running Tests

### Local Development

```bash
# Unit tests
npm test                    # Watch mode
npm run test:ci            # Single run
npm run test:coverage      # With coverage report

# E2E tests
npm run test:e2e           # Run all E2E tests
npm run test:e2e:critical  # Critical paths only
npm run test:e2e:ui       # Interactive mode

# Performance
npm run analyze            # Bundle size analysis
npm run lighthouse:mobile  # Performance audit

# Quick validation
npm run test:quick         # Type + lint + unit
```

### CI Optimizations

- **Parallel Workers:** 2 workers for E2E tests (was 1)
- **Test Sharding:** Split across multiple machines
- **Smart Caching:** Dependencies, builds, and test results
- **Conditional Tests:** Full suite only when needed

### Debugging Failed Tests

1. **TypeScript Errors**

   ```bash
   npm run typecheck
   ```

2. **Lint Issues**

   ```bash
   npm run lint
   npm run format  # Auto-fix
   ```

3. **E2E Failures**

   ```bash
   npm run test:e2e:ui     # Interactive debugging
   npm run test:e2e:debug  # Step-by-step
   ```

4. **Coverage Issues**
   ```bash
   npm run test:coverage
   # Open coverage/index.html
   ```

## Best Practices

### When Developing with LLM

1. **Always include in prompts:**

   ```
   Follow Dr. Muscle testing requirements:
   - Run npm run test:quick after changes
   - Run npm run test:before-commit before commits
   - No TypeScript 'any' types
   - Maintain 80%+ coverage
   - Bundle size <150KB
   ```

2. **Test immediately after AI generates code**
3. **Don't accumulate untested changes**
4. **Run full suite before creating PR**

### Writing Tests

1. **Test user behavior, not implementation**
2. **Use data-testid for E2E selectors**
3. **Mock external dependencies**
4. **Test mobile viewports for UI**
5. **Include error cases**

### Performance Tips

1. **Use test:quick during development**
2. **Run critical E2E for commits**
3. **Full suite only for PRs**
4. **Use --skip-build flag when possible**
5. **Leverage test sharding in CI**
