import { useCallback, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuthStore } from '@/stores/authStore'
import {
  ROUTES,
  REDIRECT_TO_PROGRAM_ROUTES,
  buildUrlWithParams,
  type RouteValue,
} from '@/lib/navigation'

export interface UseNavigationFlowOptions {
  /** Whether to check for direct access to protected routes */
  checkDirectAccess?: boolean
  /** Whether to prefetch next routes */
  prefetchRoutes?: boolean
}

/**
 * Hook to manage navigation flow between login, program, and workout pages
 */
export function useNavigationFlow(options: UseNavigationFlowOptions = {}) {
  const { checkDirectAccess = true, prefetchRoutes = true } = options

  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated, hasHydrated } = useAuthStore()

  // Check for direct access to workout page
  useEffect(() => {
    if (!checkDirectAccess || !hasHydrated || !isAuthenticated) return

    // If user directly accesses workout, redirect to program first
    if (REDIRECT_TO_PROGRAM_ROUTES.includes(pathname as RouteValue)) {
      // Check if coming from program page (browser back/forward)
      const { referrer } = document
      const isFromProgram = referrer && referrer.includes(ROUTES.PROGRAM)

      if (!isFromProgram) {
        // Store intended destination in session storage
        sessionStorage.setItem('intendedDestination', pathname)
        router.replace(ROUTES.PROGRAM)
      }
    }
  }, [pathname, hasHydrated, isAuthenticated, router, checkDirectAccess])

  // Prefetch common routes
  useEffect(() => {
    if (!prefetchRoutes || !isAuthenticated) return

    // Prefetch based on current route
    if (pathname === ROUTES.LOGIN) {
      router.prefetch(ROUTES.PROGRAM)
    }
    // Removed workout prefetch to prevent triggering getUserWorkout API call
    // The workout page will load when the user actually navigates to it
  }, [pathname, isAuthenticated, router, prefetchRoutes])

  /**
   * Navigate to program overview
   */
  const navigateToProgram = useCallback(
    (preserveParams = true) => {
      const url = preserveParams
        ? buildUrlWithParams(ROUTES.PROGRAM, window.location.href)
        : ROUTES.PROGRAM
      router.push(url)
    },
    [router]
  )

  /**
   * Navigate to workout page
   */
  const navigateToWorkout = useCallback(
    (preserveParams = true) => {
      const url = preserveParams
        ? buildUrlWithParams(ROUTES.WORKOUT, window.location.href)
        : ROUTES.WORKOUT
      router.push(url)
    },
    [router]
  )

  /**
   * Navigate back to previous page in flow
   */
  const navigateBack = useCallback(() => {
    // Custom back logic based on current route
    if (pathname === ROUTES.WORKOUT) {
      navigateToProgram()
    } else {
      router.back()
    }
  }, [pathname, router, navigateToProgram])

  /**
   * Check if there's a stored intended destination and navigate there
   */
  const checkIntendedDestination = useCallback(() => {
    const intended = sessionStorage.getItem('intendedDestination')
    if (intended) {
      sessionStorage.removeItem('intendedDestination')
      router.push(intended)
      return true
    }
    return false
  }, [router])

  return {
    currentRoute: pathname,
    isAuthenticated,
    navigateToProgram,
    navigateToWorkout,
    navigateBack,
    checkIntendedDestination,
  }
}
