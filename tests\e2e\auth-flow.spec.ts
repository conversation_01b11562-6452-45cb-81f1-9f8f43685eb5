/* eslint-disable no-console */
import { test, expect } from '@playwright/test'

test.describe('Authentication and Workout Flow', () => {
  test('should login and load workout successfully', async ({ page }) => {
    // Go to the login page
    await page.goto('/login')

    // Wait for the page to load
    await expect(page).toHaveTitle(/Dr\. Muscle X/)

    // Check if login form is visible
    await expect(page.getByLabel('Email')).toBeVisible()
    await expect(page.getByLabel('Password')).toBeVisible()

    // Fill in the login form
    await page
      .getByLabel('Email')
      .fill(process.env.TEST_EMAIL || '<EMAIL>')
    await page
      .getByLabel('Password')
      .fill(process.env.TEST_PASSWORD || 'testpassword')

    // Click the login button
    await page.getByRole('button', { name: /login/i }).click()

    // Wait for navigation or error
    await Promise.race([
      page.waitForURL('/workout', { timeout: 10000 }),
      page.waitForSelector('[role="alert"]', { timeout: 10000 }),
    ])

    // Check if we got an error
    const error = page.locator('[role="alert"]')
    if (await error.isVisible()) {
      const errorText = await error.textContent()
      console.log('Login error:', errorText)

      // Log network errors from console
      page.on('console', (msg) => {
        if (msg.type() === 'error') {
          console.log('Console error:', msg.text())
        }
      })

      // Take a screenshot for debugging
      await page.screenshot({ path: 'login-error.png', fullPage: true })

      throw new Error(`Login failed: ${errorText}`)
    }

    // If we reached the workout page, check for workout loading
    await expect(page).toHaveURL('/workout')

    // Wait for either workout content or error
    await Promise.race([
      page.waitForSelector('[data-testid="workout-content"]', {
        timeout: 10000,
      }),
      page.waitForSelector('[data-testid="error-message"]', { timeout: 10000 }),
    ])

    // Check if workout loaded successfully
    const workoutError = page.locator('[data-testid="error-message"]')
    if (await workoutError.isVisible()) {
      const errorText = await workoutError.textContent()
      console.log('Workout loading error:', errorText)

      // Take a screenshot for debugging
      await page.screenshot({ path: 'workout-error.png', fullPage: true })

      throw new Error(`Workout loading failed: ${errorText}`)
    }

    // Verify workout content is displayed
    await expect(page.locator('[data-testid="workout-content"]')).toBeVisible()
  })

  test('should handle network errors gracefully', async ({ page }) => {
    // Intercept API calls to simulate network errors
    await page.route('**/api/**', (route) => {
      route.abort('failed')
    })

    await page.goto('/login')

    // Try to login
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('testpassword')
    await page.getByRole('button', { name: /login/i }).click()

    // Should show network error
    await expect(page.locator('[role="alert"]')).toContainText(
      /network|connection/i
    )
  })

  test('should check API endpoints directly', async ({ request }) => {
    // Test the API URL directly
    const apiUrl =
      process.env.PLAYWRIGHT_BASE_URL === 'https://x.dr-muscle.com'
        ? 'https://drmuscle.azurewebsites.net'
        : 'http://localhost:5000'

    console.log('Testing API URL:', apiUrl)

    // Test if API is reachable
    try {
      const response = await request.get(`${apiUrl}/api/health`)
      console.log('API health check status:', response.status())
    } catch (error) {
      console.log('API health check failed:', error)
    }

    // Test login endpoint
    try {
      const response = await request.post(`${apiUrl}/token`, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: 'grant_type=password&username=<EMAIL>&password=wrongpassword',
      })
      console.log('Login endpoint status:', response.status())
      console.log('Login endpoint response:', await response.text())
    } catch (error) {
      console.log('Login endpoint error:', error)
    }
  })
})
