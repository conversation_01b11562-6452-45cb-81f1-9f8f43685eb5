import { describe, it, expect } from 'vitest'
import { isUserStats, UserStats, defaultUserStats } from '../userStats'

describe('UserStats', () => {
  describe('isUserStats type guard', () => {
    it('should return true for valid UserStats object', () => {
      const validStats: UserStats = {
        weekStreak: 5,
        workoutsCompleted: 25,
        lbsLifted: 10000,
      }
      expect(isUserStats(validStats)).toBe(true)
    })

    it('should return true for UserStats with zero values', () => {
      const zeroStats: UserStats = {
        weekStreak: 0,
        workoutsCompleted: 0,
        lbsLifted: 0,
      }
      expect(isUserStats(zeroStats)).toBe(true)
    })

    it('should return false for null', () => {
      expect(isUserStats(null)).toBe(false)
    })

    it('should return false for undefined', () => {
      expect(isUserStats(undefined)).toBe(false)
    })

    it('should return false for non-object values', () => {
      expect(isUserStats('string')).toBe(false)
      expect(isUserStats(123)).toBe(false)
      expect(isUserStats(true)).toBe(false)
      expect(isUserStats([])).toBe(false)
    })

    it('should return false for object missing weekStreak', () => {
      const invalid = {
        workoutsCompleted: 10,
        lbsLifted: 5000,
      }
      expect(isUserStats(invalid)).toBe(false)
    })

    it('should return false for object missing workoutsCompleted', () => {
      const invalid = {
        weekStreak: 3,
        lbsLifted: 5000,
      }
      expect(isUserStats(invalid)).toBe(false)
    })

    it('should return false for object missing lbsLifted', () => {
      const invalid = {
        weekStreak: 3,
        workoutsCompleted: 10,
      }
      expect(isUserStats(invalid)).toBe(false)
    })

    it('should return false for object with non-number weekStreak', () => {
      const invalid = {
        weekStreak: '5',
        workoutsCompleted: 10,
        lbsLifted: 5000,
      }
      expect(isUserStats(invalid)).toBe(false)
    })

    it('should return false for object with non-number workoutsCompleted', () => {
      const invalid = {
        weekStreak: 5,
        workoutsCompleted: '10',
        lbsLifted: 5000,
      }
      expect(isUserStats(invalid)).toBe(false)
    })

    it('should return false for object with non-number lbsLifted', () => {
      const invalid = {
        weekStreak: 5,
        workoutsCompleted: 10,
        lbsLifted: '5000',
      }
      expect(isUserStats(invalid)).toBe(false)
    })

    it('should return false for object with extra properties', () => {
      const statsWithExtra = {
        weekStreak: 5,
        workoutsCompleted: 10,
        lbsLifted: 5000,
        extraField: 'should not affect validation',
      }
      // Type guard only checks required fields exist and are correct type
      expect(isUserStats(statsWithExtra)).toBe(true)
    })
  })

  describe('defaultUserStats', () => {
    it('should have all fields set to 0', () => {
      expect(defaultUserStats.weekStreak).toBe(0)
      expect(defaultUserStats.workoutsCompleted).toBe(0)
      expect(defaultUserStats.lbsLifted).toBe(0)
    })

    it('should be a valid UserStats object', () => {
      expect(isUserStats(defaultUserStats)).toBe(true)
    })
  })
})
