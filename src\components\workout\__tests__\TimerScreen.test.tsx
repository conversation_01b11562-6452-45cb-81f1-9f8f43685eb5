import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { TimerScreen } from '../TimerScreen'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'

// Mock modules
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(),
}))

vi.mock('../RestTimer', () => ({
  RestTimer: () => <div data-testid="rest-timer">Rest Timer</div>,
}))

describe('TimerScreen', () => {
  const mockPush = vi.fn()
  const mockUseWorkout = {
    currentExercise: { Id: 1, Name: 'Bench Press' },
    getNextExercise: vi.fn(() => ({ Id: 2, Name: 'Squat' })),
    currentSetIndex: 0,
    totalSets: 3,
    isLastSet: false,
    isLastExercise: false,
    nextSet: vi.fn(),
    getRestDuration: vi.fn(() => 90),
    exercises: [{ Id: 1 }, { Id: 2 }],
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue({
      push: mockPush,
      back: vi.fn(),
      forward: vi.fn(),
      refresh: vi.fn(),
      replace: vi.fn(),
      prefetch: vi.fn(),
    } as any)
    vi.mocked(useWorkout).mockReturnValue(mockUseWorkout as any)
  })

  describe('Theme Application', () => {
    it('should use theme-aware background colors instead of hardcoded gray-50', () => {
      const { container } = render(
        <TimerScreen soundEnabled vibrationEnabled isBetweenSets={false} />
      )

      const mainDiv = container.querySelector('.min-h-\\[100dvh\\]')
      expect(mainDiv).toHaveClass('bg-bg-primary')
      expect(mainDiv).not.toHaveClass('bg-gray-50')
    })

    it('should use theme-aware colors for bottom bar instead of hardcoded white and gray', () => {
      const { container } = render(
        <TimerScreen soundEnabled vibrationEnabled isBetweenSets={false} />
      )

      const bottomBar = container.querySelector('.fixed.bottom-0')
      expect(bottomBar).toHaveClass('bg-bg-secondary')
      expect(bottomBar).toHaveClass('border-border-primary')
      expect(bottomBar).not.toHaveClass('bg-white')
      expect(bottomBar).not.toHaveClass('border-gray-200')
    })

    it('should use theme-aware button colors instead of hardcoded orange', () => {
      render(
        <TimerScreen soundEnabled vibrationEnabled isBetweenSets={false} />
      )

      const skipButton = screen.getByRole('button', { name: /skip/i })
      expect(skipButton).toHaveClass('bg-brand-primary')
      expect(skipButton).toHaveClass('hover:bg-brand-primary/90')
      expect(skipButton).toHaveClass('text-text-inverse')
      expect(skipButton).not.toHaveClass('bg-orange-500')
      expect(skipButton).not.toHaveClass('hover:bg-orange-600')
      expect(skipButton).not.toHaveClass('text-white')
    })
  })

  describe('Original Functionality', () => {
    it('should render rest timer component', () => {
      render(
        <TimerScreen soundEnabled vibrationEnabled isBetweenSets={false} />
      )

      expect(screen.getByTestId('rest-timer')).toBeInTheDocument()
    })

    it('should render skip button', () => {
      render(
        <TimerScreen soundEnabled vibrationEnabled isBetweenSets={false} />
      )

      expect(screen.getByRole('button', { name: /skip/i })).toBeInTheDocument()
    })
  })
})
