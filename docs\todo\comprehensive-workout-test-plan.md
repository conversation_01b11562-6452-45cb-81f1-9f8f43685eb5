# Comprehensive Workout Loop Test Plan

## Overview

This document outlines a comprehensive test plan for the Dr. Muscle workout loop, covering all features and edge cases from workout start to completion.

## Current Test Coverage Analysis

### Existing Tests

Based on analysis of the test files, the following areas have some coverage:

1. **Basic Workflow**
   - App launch and login
   - Starting a workout
   - Basic set recording (weight/reps)
   - Workout completion
   - Screenshot capture at key points

2. **Set Recording**
   - Basic weight and rep entry
   - Different set types (regular, drop, rest-pause, failure)
   - Weight and rep picker UI
   - Rest timer behavior
   - Set modification and deletion

3. **Authentication & Onboarding**
   - Account creation
   - Login/logout flows
   - Basic onboarding screens

### Missing Test Coverage

The following critical features lack comprehensive testing:

1. **Plate Calculator**
2. **RIR (Reps in Reserve) System**
3. **Supersets**
4. **Exercise Settings & Customization**
5. **Timer Options & Customization**
6. **Advanced Weight Entry (bodyweight, assisted, bands)**
7. **Exercise Substitution**
8. **Workout Templates & Programs**
9. **Performance Tracking & History**
10. **AI Recommendations**

## Comprehensive Test Scenarios

### 1. Account Creation & Onboarding

```
TEST: Complete_Onboarding_New_User
- Launch app fresh install
- Tap "Start Free Trial"
- Enter email and create account
- Complete profile setup:
  - Age selection
  - Gender selection
  - Weight entry (test both kg and lbs)
  - Height entry
  - Fitness level selection
  - Goal selection (muscle, strength, both)
  - Equipment selection
  - Experience level
- Verify program recommendation
- Accept/customize program
- Complete onboarding

TEST: Complete_Onboarding_Skip_Options
- Test minimal onboarding path
- Skip optional steps
- Verify defaults are applied
```

### 2. Workout Selection & Start

```
TEST: Start_Workout_From_Home
- Navigate to home screen
- Tap "Start Workout"
- Verify workout plan is displayed
- Check exercise list shows correctly
- Verify warm-up sets are marked
- Start first exercise

TEST: Start_Custom_Workout
- Go to workouts tab
- Create new workout
- Add exercises manually
- Set custom sets/reps
- Start custom workout

TEST: Start_Featured_Program_Workout
- Browse featured programs
- Select a program
- Start day 1 workout
- Verify program-specific features
```

### 3. Exercise Execution - Basic Flow

```
TEST: Complete_Basic_Exercise
- Open exercise
- View exercise info/video
- Start first set
- Complete set with weight/reps
- Save set
- Rest timer starts automatically
- Complete all sets
- Finish exercise

TEST: Complete_Exercise_With_Warmups
- Start exercise with warm-up sets
- Complete warm-up sets (verify lighter weights)
- Complete work sets
- Verify proper progression
```

### 4. Plate Calculator Tests

```
TEST: Open_Plate_Calculator_Barbell
- During set entry, tap plate calculator icon
- Enter target weight
- Select barbell type (Olympic/Standard)
- Verify plate breakdown is correct
- Test with different weights
- Verify both kg and lbs calculations

TEST: Plate_Calculator_Dumbbell
- Select dumbbell option
- Enter target weight
- Verify shows single dumbbell weight
- Test adjustable vs fixed dumbbells

TEST: Plate_Calculator_Custom_Plates
- Access plate settings
- Add custom plate weights
- Remove standard plates
- Verify calculations use custom plates
- Test home gym scenario

TEST: Plate_Calculator_Microplates
- Test with small weight increments
- Verify microplate usage (0.5kg, 1.25lbs)
- Test impossible weights (show closest possible)
```

### 5. RIR (Reps in Reserve) System

```
TEST: RIR_First_Work_Set
- Complete warm-up sets
- Start first work set
- Complete set
- Verify RIR prompt appears
- Test each RIR option (0-5+)
- Verify affects next set recommendation

TEST: RIR_Affects_Weight_Recommendation
- Set RIR very high (4-5)
- Verify next workout increases weight
- Set RIR very low (0-1)
- Verify weight stays same or decreases
- Test RIR trends over multiple workouts

TEST: RIR_Muscle_vs_Strength_Mode
- Test RIR in muscle building mode
- Verify targets RIR 1-3
- Switch to strength mode
- Verify targets RIR 3-5
- Check recommendations differ
```

### 6. Timer Features

```
TEST: Rest_Timer_Full_Cycle
- Complete a set
- Verify timer starts automatically
- Check correct rest time (based on exercise type)
- Let timer run to zero
- Verify notification/alert
- Start next set

TEST: Rest_Timer_Skip
- Complete a set
- Timer starts
- Tap "Skip Rest"
- Verify can start next set immediately
- Check no negative effects

TEST: Rest_Timer_Background
- Start rest timer
- Switch apps/background app
- Verify timer continues
- Return at different intervals
- Verify accurate time

TEST: Custom_Rest_Times
- Access exercise settings
- Set custom rest time
- Complete set
- Verify custom time is used
- Test different times for different exercises

TEST: Auto_Start_Timer_Setting
- Disable auto-start timer
- Complete set
- Verify timer doesn't start
- Manually start timer
- Re-enable and verify works
```

### 7. Supersets

```
TEST: Create_Superset_Pair
- Start exercise A
- Tap superset button
- Get prompted about supersets (first time)
- Select exercise B
- Complete set of A
- Verify switches to exercise B
- Complete set of B
- Verify switches back to A
- Continue alternating

TEST: Superset_Different_Weights
- Create superset with very different exercises
- E.g., Bench Press + Bicep Curls
- Verify weight recommendations are independent
- Test plate calculator for each

TEST: Superset_Rest_Timing
- Complete superset pair
- Verify reduced rest between A->B
- Verify full rest after B->A
- Test custom rest times for supersets

TEST: Cancel_Superset_Mid_Workout
- Start superset
- Complete some sets
- Cancel superset
- Verify returns to normal mode
- Complete exercises separately
```

### 8. Advanced Set Types

```
TEST: Drop_Sets
- Enable drop sets for exercise
- Complete first set at full weight
- Verify prompts for immediate lighter set
- Complete 2-3 drops
- Verify weight suggestions decrease
- Check no rest between drops

TEST: Rest_Pause_Sets
- Enable rest-pause mode
- Complete initial set to failure
- Verify 15-30 second mini-rest
- Complete mini-set
- Repeat 2-3 times
- Verify counted as one set

TEST: Pyramid_Sets
- Configure pyramid routine
- Start with light weight
- Verify weight increases each set
- Reach peak weight
- Verify weight decreases
- Complete pyramid

TEST: AMRAP_Sets (As Many Reps As Possible)
- Configure AMRAP set
- Start set
- Verify no target reps shown
- Complete max reps
- Enter actual reps achieved
- Verify affects future recommendations

TEST: Timed_Sets
- Select time-based exercise (planks)
- Start timer-based set
- Verify shows timer not reps
- Complete for time
- Save duration
- Test pause/resume

TEST: Mechanical_Drop_Sets
- Set up exercise variations
- E.g., Incline -> Flat -> Decline Push-ups
- Complete each variation
- Verify treated as one extended set
```

### 9. Weight Entry Variations

```
TEST: Bodyweight_Exercises
- Select bodyweight exercise (pull-ups)
- Verify weight shows "BW"
- Complete reps
- Test with added weight (BW+10kg)
- Test with assistance (BW-20kg)

TEST: Resistance_Bands
- Select band-compatible exercise
- Choose band resistance
- Verify can combine with weights
- Test band-only exercises
- Track band progression

TEST: Cable_Machine_Weights
- Select cable exercise
- Verify plate/pin number entry
- Test with different cable systems
- Verify conversion if needed

TEST: Assisted_Exercises
- Select assisted pull-ups/dips
- Enter assistance weight
- Verify calculations (BW - assistance)
- Track progression (less assistance)

TEST: Unusual_Weight_Increments
- Test gyms with odd plates (15kg, 35lbs)
- Enter exact weights
- Verify accepts decimals
- Test metric/imperial conversion
```

### 10. Exercise Settings & Modifications

```
TEST: Modify_Exercise_Settings
- Long-press exercise
- Access exercise settings
- Test each option:
  - Sets count
  - Rep ranges
  - Rest time
  - Tempo
  - Notes
- Verify changes apply immediately

TEST: Exercise_Substitution
- Can't do prescribed exercise
- Tap substitute option
- Browse alternatives
- Select similar exercise
- Verify maintains program logic
- Test equipment-based substitutions

TEST: Disable_Exercise
- Injury/limitation scenario
- Disable specific exercise
- Verify removed from workouts
- Test re-enabling later
- Check history maintained

TEST: Custom_Exercise_Creation
- Can't find exercise
- Create custom exercise
- Set muscle groups
- Set equipment needed
- Add to workout
- Verify tracking works
```

### 11. Workout Flow Variations

```
TEST: Pause_Resume_Workout
- Start workout
- Complete some sets
- Exit app/crash simulation
- Reopen app
- Verify prompts to resume
- Continue from last set

TEST: Skip_Exercise
- Start workout
- Complete first exercise
- Skip second exercise
- Verify can note reason
- Continue with remaining
- Check affects volume tracking

TEST: Add_Exercise_Mid_Workout
- During workout
- Feel good, want extra
- Add exercise on the fly
- Complete additional work
- Verify included in summary

TEST: Finish_Workout_Early
- Start workout
- Complete 2-3 exercises
- Tap finish workout
- Verify confirmation
- Check partial workout saved
- Note reason (time, fatigue)
```

### 12. Performance Tracking

```
TEST: View_Exercise_History_During_Workout
- Start exercise
- Tap history icon
- Verify shows last 5-10 sessions
- Check can see progression
- Return to current set
- Use history to guide weight selection

TEST: Beat_Previous_Performance
- Start exercise
- View last performance
- Complete set with more weight/reps
- Verify "PR" indicator
- Check celebration/acknowledgment
- Verify logged correctly

TEST: Track_Workout_Metrics
- Complete full workout
- Verify summary shows:
  - Total volume
  - Time duration
  - Exercises completed
  - PRs achieved
  - Calories (if enabled)
- Compare to previous workouts
```

### 13. AI Coach Integration

```
TEST: AI_Weight_Recommendations
- Complete exercise
- Check AI recommendation for next set
- Test accepting recommendation
- Test modifying recommendation
- Verify AI learns from modifications

TEST: AI_Deload_Suggestion
- Simulate fatigue (low RIR multiple sessions)
- Verify AI suggests deload
- Accept deload week
- Check reduced weights/volume
- Verify recovery tracking

TEST: AI_Exercise_Swap_Suggestion
- Plateau on exercise
- Continue for 3-4 weeks
- Verify AI suggests variation
- Test accepting/declining
- Check maintains progression
```

### 14. Edge Cases & Error Handling

```
TEST: Network_Failure_During_Workout
- Start workout online
- Disconnect network mid-workout
- Verify can continue offline
- Complete workout
- Reconnect
- Verify syncs properly

TEST: Invalid_Weight_Entry
- Try entering negative weight
- Try entering >1000kg
- Try entering text
- Verify validation works
- Check error messages

TEST: Rapid_Set_Completion
- Complete sets very quickly
- Verify handles rapid input
- Check data integrity
- Verify no UI glitches

TEST: Long_Workout_Session
- Run workout >2 hours
- Verify no timeouts
- Check battery optimization
- Verify all data saved
- Test session recovery

TEST: Concurrent_Device_Usage
- Start workout on phone
- Open app on tablet
- Verify conflict handling
- Test sync between devices
- Check no data loss
```

### 15. Accessibility Testing

```
TEST: VoiceOver_Full_Workout
- Enable VoiceOver/TalkBack
- Complete entire workout
- Verify all elements readable
- Test gesture navigation
- Check timer announcements

TEST: Large_Text_Mode
- Enable large text
- Verify UI adapts
- Check no text truncation
- Test readability

TEST: One_Handed_Operation
- Complete workout one-handed
- Verify all controls reachable
- Test gesture alternatives
- Check ease of use
```

## Test Data Requirements

### User Accounts

- New user (never worked out)
- Intermediate user (3 months history)
- Advanced user (1+ year history)
- User with injuries/limitations
- User with custom equipment

### Exercise Data

- Standard exercises (bench, squat, deadlift)
- Bodyweight exercises
- Cable exercises
- Machine exercises
- Custom exercises
- Time-based exercises

### Workout Templates

- Full body workout
- Upper/Lower split
- Push/Pull/Legs
- Custom routine
- Featured programs

## Performance Benchmarks

All tests should verify:

- Screen transitions < 300ms
- Set save time < 500ms
- Timer accuracy within 1 second
- No memory leaks during long sessions
- Smooth scrolling in exercise lists
- Responsive touch targets

## Platform-Specific Tests

### iOS-Specific

- Test with Face ID/Touch ID
- Verify Apple Health integration
- Test with Apple Watch
- Check iOS gesture navigation
- Test notification permissions

### Android-Specific

- Test with various screen sizes
- Verify Google Fit integration
- Test with Wear OS
- Check back button handling
- Test battery optimization

## Automation Strategy

1. **High Priority for Automation**
   - Basic workout flow
   - Set recording
   - Timer functionality
   - Data validation

2. **Manual Testing Required**
   - Plate calculator visualization
   - Exercise form videos
   - Complex gestures
   - Audio/haptic feedback

3. **Hybrid Approach**
   - AI recommendations (verify logic automatically, UX manually)
   - Performance tracking (automate calculations, manually verify UI)

## Success Criteria

- 100% of critical paths tested
- <2% test flakiness
- All edge cases handled gracefully
- No data loss scenarios
- Consistent experience across platforms
- Performance benchmarks met

## Implementation Priority

1. **Phase 1 - Core Functionality** (Week 1-2)
   - Basic workout flow
   - Set recording all types
   - Timer features
   - Plate calculator

2. **Phase 2 - Advanced Features** (Week 3-4)
   - Supersets
   - RIR system
   - Exercise settings
   - Performance tracking

3. **Phase 3 - Edge Cases** (Week 5)
   - Network handling
   - Error scenarios
   - Platform-specific features
   - Accessibility

## Maintenance Plan

- Run full suite daily
- Update tests with new features
- Regular performance benchmarking
- Quarterly test review and cleanup
- Monitor test execution times
- Track and fix flaky tests

---

This comprehensive test plan ensures complete coverage of the Dr. Muscle workout experience, from basic functionality to advanced features and edge cases.
