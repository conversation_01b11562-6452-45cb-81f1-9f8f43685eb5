import { test, expect, Page } from '@playwright/test'

test.describe('API Error Handling - Authentication Tests', () => {
  let page: Page
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p }) => {
    page = p
  })

  test('should handle 400 Bad Request on login', async () => {
    await page.goto('/login')

    await page.route('**/api/token', (route) => {
      route.fulfill({
        status: 400,
        json: {
          error: 'invalid_request',
          error_description: 'Missing required parameters',
        },
      })
    })

    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    await expect(page.locator('[role="alert"]')).toContainText(
      /invalid request|missing required/i
    )
  })

  test('should handle 401 Unauthorized with invalid credentials', async () => {
    await page.goto('/login')

    await page.route('**/api/token', (route) => {
      route.fulfill({
        status: 401,
        json: {
          error: 'invalid_grant',
          error_description: 'Invalid email or password',
        },
      })
    })

    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'wrongpassword')
    await page.click('button[type="submit"]')

    await expect(page.locator('[role="alert"]')).toContainText(
      /invalid email or password/i
    )
    await expect(page).toHaveURL('/login')
  })

  test('should handle 403 Forbidden for disabled accounts', async () => {
    await page.goto('/login')

    await page.route('**/api/token', (route) => {
      route.fulfill({
        status: 403,
        json: {
          error: 'account_disabled',
          error_description:
            'Your account has been disabled. Please contact support.',
        },
      })
    })

    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    await expect(page.locator('[role="alert"]')).toContainText(
      /account.*disabled/i
    )
  })

  test('should handle token refresh network failure', async () => {
    // First login successfully
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Simulate token expiration
    await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        parsed.state.tokenExpiry = Date.now() - 1000 // Expired
        localStorage.setItem('auth-storage', JSON.stringify(parsed))
      }
    })

    // Block refresh token requests
    await page.route('**/api/token/refresh', (route) =>
      route.abort('internetdisconnected')
    )

    // Navigate to protected page
    await page.goto('/workout')

    // Should show session expired message
    await expect(page.locator('text=Session expired')).toBeVisible({
      timeout: 10000,
    })

    // Should redirect to login
    await expect(page).toHaveURL('/login')
  })

  test('should handle OAuth SDK loading failure', async () => {
    await page.goto('/login')

    // Block Google SDK
    await page.route('**/accounts.google.com/**', (route) => route.abort())

    // Try to sign in with Google
    await page.click('button:has-text("Continue with Google")')

    // Should show error message
    await expect(
      page.locator('text=Failed to load Google Sign-In')
    ).toBeVisible({ timeout: 10000 })
  })

  test('should handle Apple SDK loading failure', async () => {
    await page.goto('/login')

    // Block Apple SDK
    await page.route('**/appleid.apple.com/**', (route) => route.abort())

    // Try to sign in with Apple
    await page.click('button:has-text("Continue with Apple")')

    // Should show error message
    await expect(page.locator('text=Failed to load Apple Sign-In')).toBeVisible(
      { timeout: 10000 }
    )
  })
})
