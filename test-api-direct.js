/**
 * Direct API test script
 * Run with: node test-api-direct.js
 */

const https = require('https');

const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'Dr123456';
const BASE_URL = 'https://drmuscle.azurewebsites.net';

async function makeRequest(url, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = https.request(url, options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data: body, headers: res.headers });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testAPI() {
  console.log('🔍 Testing Exercise Recommendation API directly...');
  
  try {
    // Step 1: Login
    console.log('🔐 Step 1: Logging in...');
    const loginData = new URLSearchParams({
      grant_type: 'password',
      username: TEST_EMAIL,
      password: TEST_PASSWORD
    });

    const loginResponse = await makeRequest(`${BASE_URL}/token`, 'POST', null, {
      'Content-Type': 'application/x-www-form-urlencoded'
    });

    // Manual request for login
    const loginReq = https.request(`${BASE_URL}/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        console.log('Login response status:', res.statusCode);
        console.log('Login response body:', body);
        
        if (res.statusCode === 200) {
          const tokenData = JSON.parse(body);
          const token = tokenData.access_token;
          console.log('✅ Login successful, token:', token.substring(0, 20) + '...');
          
          // Step 2: Get user program info
          getUserProgramInfo(token);
        } else {
          console.error('❌ Login failed:', body);
        }
      });
    });

    loginReq.on('error', (error) => {
      console.error('❌ Login request error:', error);
    });

    loginReq.write(loginData.toString());
    loginReq.end();

  } catch (error) {
    console.error('❌ Error during API test:', error);
  }
}

async function getUserProgramInfo(token) {
  console.log('📋 Step 2: Getting user program info...');

  // Use the correct endpoint with timezone info
  const timeZoneInfo = {
    TimeZoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
    Offset: new Date().getTimezoneOffset() / -60,
    IsDaylightSaving: false,
  };

  const req = https.request(`${BASE_URL}/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }, (res) => {
    let body = '';
    res.on('data', (chunk) => {
      body += chunk;
    });
    res.on('end', () => {
      console.log('Program info response status:', res.statusCode);
      
      if (res.statusCode === 200) {
        const programData = JSON.parse(body);
        console.log('✅ Program info received');
        console.log('Program data structure:', JSON.stringify(programData, null, 2));

        // The response structure might be different, let's check for various possible paths
        const nextWorkout = programData.Result?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate ||
                           programData.GetUserProgramInfoResponseModel?.NextWorkoutTemplate ||
                           programData.NextWorkoutTemplate ||
                           programData.UserWorkoutProgramModel?.NextWorkoutTemplate;

        console.log('Next workout template:', nextWorkout?.Id);
        console.log('Exercises count:', nextWorkout?.Exercises?.length || 0);

        if (nextWorkout?.Exercises?.length > 0) {
          const firstExercise = nextWorkout.Exercises[0];
          console.log('First exercise:', firstExercise.Id, firstExercise.Label);

          // Step 3: Test recommendation API
          testRecommendationAPI(token, firstExercise, nextWorkout.Id);
        } else {
          console.error('❌ No exercises found in workout template');
        }
      } else {
        console.error('❌ Failed to get program info:', body);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Program info request error:', error);
  });

  req.write(JSON.stringify(timeZoneInfo));
  req.end();
}

async function testRecommendationAPI(token, exercise, workoutId) {
  console.log('🎯 Step 3: Testing recommendation API...');
  
  const requestBody = {
    Username: TEST_EMAIL.replace(/\s+/g, '').toLowerCase(),
    ExerciseId: exercise.Id,
    WorkoutId: workoutId,
    SetStyle: exercise.SetStyle || 'Normal',
    IsFlexibility: exercise.IsFlexibility || false,
    IsQuickMode: null,
    LightSessionDays: null,
    SwapedExId: null,
    IsStrengthPhashe: false, // Note: API has typo
    IsFreePlan: false,
    IsFirstWorkoutOfStrengthPhase: false,
    VersionNo: 1,
  };

  console.log('📤 Request body:', JSON.stringify(requestBody, null, 2));

  // Determine endpoint
  const setStyle = (exercise.SetStyle || 'Normal').toLowerCase();
  const isRestPause = setStyle === 'restpause' || setStyle === 'rest-pause';
  const shouldUseNormal = exercise.IsFlexibility || !isRestPause;

  const endpoint = shouldUseNormal
    ? '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'
    : '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew';

  console.log(`🚀 Using endpoint: ${endpoint}`);

  const req = https.request(`${BASE_URL}${endpoint}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }, (res) => {
    let body = '';
    res.on('data', (chunk) => {
      body += chunk;
    });
    res.on('end', () => {
      console.log('📥 Recommendation response status:', res.statusCode);
      console.log('📥 Recommendation response headers:', res.headers);
      
      if (res.statusCode === 200) {
        try {
          const recommendationData = JSON.parse(body);
          console.log('📥 Recommendation response:', JSON.stringify(recommendationData, null, 2));
          
          // Check if we got weight and reps
          const recommendation = recommendationData?.Result || recommendationData;
          if (recommendation) {
            console.log('🏋️ Weight:', recommendation.Weight);
            console.log('🔢 Reps:', recommendation.Reps);
            console.log('📊 Sets:', recommendation.Series);
            
            if (recommendation.Weight && recommendation.Reps) {
              console.log('✅ SUCCESS: Got weight and reps!');
            } else {
              console.log('❌ ISSUE: Missing weight or reps in response');
            }
          } else {
            console.log('❌ ISSUE: No recommendation data in response');
          }
        } catch (e) {
          console.error('❌ Failed to parse recommendation response:', e);
          console.log('Raw response:', body);
        }
      } else {
        console.error('❌ Recommendation API failed:', body);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Recommendation request error:', error);
  });

  req.write(JSON.stringify(requestBody));
  req.end();
}

// Run the test
testAPI();
