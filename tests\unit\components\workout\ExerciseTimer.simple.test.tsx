import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseTimer } from '@/components/workout/ExerciseTimer'

describe('ExerciseTimer - Simple Tests', () => {
  const mockOnComplete = vi.fn()

  it('should render rest timer', () => {
    render(<ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />)

    expect(screen.getByText(/rest time/i)).toBeInTheDocument()
    expect(screen.getByText('1:30')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /start/i })).toBeInTheDocument()
  })

  it('should render exercise timer when exercising', () => {
    render(
      <ExerciseTimer
        isExercising
        restDuration={90}
        onRestComplete={mockOnComplete}
      />
    )

    expect(screen.getByText(/exercise time/i)).toBeInTheDocument()
    expect(screen.getByText('0:00')).toBeInTheDocument()
  })

  it('should show total workout time', () => {
    const startTime = new Date(Date.now() - 1800000) // 30 minutes ago
    render(
      <ExerciseTimer
        workoutStartTime={startTime}
        restDuration={90}
        onRestComplete={mockOnComplete}
      />
    )

    expect(screen.getByText(/total time: 30:00/i)).toBeInTheDocument()
  })

  it('should have accessible buttons', () => {
    render(<ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />)

    const buttons = screen.getAllByRole('button')
    expect(buttons.length).toBeGreaterThan(0)
    buttons.forEach((button) => {
      expect(button).toHaveStyle('min-height: 44px')
    })
  })
})
