# DrMuscle Apple Watch App - Remote Mac SSH Testing Instructions

## REQUIRED: SSH Connection Setup

**Before starting any development work, please request the following SSH connection details from the user:**

```
Please provide the SSH connection details for the remote Mac:

1. **SSH Host**: (IP address or hostname)
   Example: ************* or remote-mac.local

2. **SSH Port**: (if not default 22)
   Example: 2222

3. **Username**: (macOS user account)
   Example: developer

4. **SSH Key Path**: (path to private key file)
   Example: ~/.ssh/apple_watch_dev

5. **Project Path**: (where the Xcode project is located on remote Mac)
   Example: ~/DrMuscleWatch/v1/DrMuscleWatchApp

6. **Git Branch**: (which branch to work on)
   Example: main or Development_Watch_Carl_v1.1
```

**Test the connection immediately with:**

```bash
ssh -i [SSH_KEY_PATH] [USERNAME]@[HOST] -p [PORT] "echo 'SSH connection successful'"
```

---

## Overview

You are working on the DrMuscle Apple Watch app with direct SSH access to a remote Mac for ultra-fast testing cycles. This eliminates CI/CD delays and enables rapid TDD iteration just like the web project.

## Core Testing Workflow

### Step 1: Initial Connection & Project Setup

```bash
# Connect to remote Mac
ssh -i [SSH_KEY_PATH] [USERNAME]@[HOST] -p [PORT]

# Navigate to project directory
cd [PROJECT_PATH]

# Check git status and pull latest
git status
git pull origin [BRANCH_NAME]

# Verify Xcode project exists
ls -la *.xcodeproj
```

### Step 2: Project Analysis & Context 7 MCP Check

```bash
# Analyze project dependencies
grep -r "import " . --include="*.swift" | head -20
cat Package.swift 2>/dev/null || echo "No Package.swift found"

# Check project structure
find . -name "*.swift" -type f | head -10
```

**Before coding:** Check Context 7 MCP for identified technologies:

- Swift (current version)
- SwiftUI
- WatchKit
- Combine (if used)
- Any third-party dependencies found

### Step 3: Rapid Local Testing Loop

```bash
# Build the project
xcodebuild -project DrMuscleWatchApp.xcodeproj -scheme DrMuscleWatchApp -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm)' build

# Run specific test suite (replace TestTarget with actual name)
xcodebuild test -project DrMuscleWatchApp.xcodeproj -scheme DrMuscleWatchApp -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm)' -only-testing:DrMuscleWatchAppTests/LoginTests

# Run all tests
xcodebuild test -project DrMuscleWatchApp.xcodeproj -scheme DrMuscleWatchApp -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm)'

# Check for build/runtime errors
echo "Exit code: $?"
```

### Step 4: Self-Healing Error Resolution

When tests fail, follow this diagnostic sequence:

```bash
# 1. Check compilation errors
xcodebuild build 2>&1 | grep -E "(error|warning):" | head -10

# 2. Check simulator status
xcrun simctl list devices available | grep "Apple Watch"

# 3. Reset simulator if needed
xcrun simctl shutdown all
xcrun simctl erase all
xcrun simctl boot "Apple Watch Series 9 (45mm)"

# 4. Check provisioning/signing issues
security find-identity -v -p codesigning
```

## TDD Implementation Pattern

### 1. Write Failing Test First

```bash
# Create or edit test file
vim DrMuscleWatchAppTests/LoginViewModelTests.swift

# Quick syntax check
swiftc -typecheck DrMuscleWatchAppTests/LoginViewModelTests.swift
```

### 2. Run Specific Test to Confirm Failure

```bash
# Run only the new test to see it fail
xcodebuild test -project DrMuscleWatchApp.xcodeproj -scheme DrMuscleWatchApp -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm)' -only-testing:DrMuscleWatchAppTests/LoginViewModelTests/testLoginFailure
```

### 3. Implement Minimal Code to Pass

```bash
# Edit implementation file
vim DrMuscleWatchApp/ViewModels/LoginViewModel.swift

# Quick build check (faster than full test)
xcodebuild build -project DrMuscleWatchApp.xcodeproj -scheme DrMuscleWatchApp -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm)'
```

### 4. Run Test Again to Confirm Pass

```bash
# Re-run the specific test
xcodebuild test -project DrMuscleWatchApp.xcodeproj -scheme DrMuscleWatchApp -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm)' -only-testing:DrMuscleWatchAppTests/LoginViewModelTests/testLoginFailure
```

### 5. Run Full Test Suite

```bash
# Ensure no regressions
xcodebuild test -project DrMuscleWatchApp.xcodeproj -scheme DrMuscleWatchApp -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm)'
```

## Common Commands for Apple Watch Development

### Simulator Management

```bash
# List available simulators
xcrun simctl list devices

# Boot specific Apple Watch
xcrun simctl boot "Apple Watch Series 9 (45mm)"

# Install app on simulator
xcrun simctl install booted path/to/YourApp.app

# Launch app
xcrun simctl launch booted com.drmaxmuscle.max.watchkitapp
```

### Build Variants

```bash
# Debug build (faster)
xcodebuild build -configuration Debug

# Release build (for final testing)
xcodebuild build -configuration Release

# Archive for distribution
xcodebuild archive -project DrMuscleWatchApp.xcodeproj -scheme DrMuscleWatchApp-iOS -archivePath build/DrMuscleWatchApp.xcarchive
```

### Performance Monitoring

```bash
# Build time tracking
time xcodebuild build

# App size check
du -sh build/Release-watchsimulator/DrMuscleWatchApp.app

# Memory usage during tests
top -pid $(pgrep Simulator) -l 5
```

## Error Resolution Playbook

### Common Issues and Fixes

#### 1. Simulator Not Responding

```bash
# Kill all simulators
killall Simulator
xcrun simctl shutdown all

# Restart specific simulator
xcrun simctl boot "Apple Watch Series 9 (45mm)"
```

#### 2. Code Signing Issues

```bash
# Check certificates
security find-identity -v -p codesigning | grep "Apple Development"

# Clean build folder
xcodebuild clean

# Check provisioning profiles
ls ~/Library/MobileDevice/Provisioning\ Profiles/
```

#### 3. Build Cache Issues

```bash
# Clean derived data
rm -rf ~/Library/Developer/Xcode/DerivedData/*

# Clean project
xcodebuild clean -project DrMuscleWatchApp.xcodeproj -scheme DrMuscleWatchApp
```

#### 4. Test Target Issues

```bash
# Verify test target builds
xcodebuild build-for-testing -project DrMuscleWatchApp.xcodeproj -scheme DrMuscleWatchApp

# Run tests without building
xcodebuild test-without-building -xctestrun path/to/test.xctestrun
```

## Automated Self-Healing Script

Create this as a bash function for automatic error recovery:

```bash
#!/bin/bash
auto_heal_build() {
    echo "Attempting auto-heal for build issues..."

    # Step 1: Clean and retry
    xcodebuild clean
    if xcodebuild build; then
        echo "✅ Fixed with clean build"
        return 0
    fi

    # Step 2: Reset simulator
    xcrun simctl shutdown all
    xcrun simctl erase all
    xcrun simctl boot "Apple Watch Series 9 (45mm)"

    if xcodebuild build; then
        echo "✅ Fixed with simulator reset"
        return 0
    fi

    # Step 3: Clear derived data
    rm -rf ~/Library/Developer/Xcode/DerivedData/*

    if xcodebuild build; then
        echo "✅ Fixed with derived data cleanup"
        return 0
    fi

    echo "❌ Auto-heal failed - manual intervention needed"
    return 1
}
```

## Context 7 MCP Integration

Before implementing any feature, check Context 7 MCP for:

### Swift & Apple Frameworks

```bash
# Check these in Context 7 MCP:
Context 7: Swift
Context 7: SwiftUI
Context 7: WatchKit
Context 7: Combine
Context 7: Foundation
Context 7: Xcode
```

**Focus Areas:**

- Recent API changes and deprecated methods
- New SwiftUI features for watchOS
- WatchKit best practices and performance optimizations
- Swift concurrency patterns (async/await)
- Testing frameworks and patterns
- Code signing and deployment updates

## Performance Targets

### Speed Benchmarks

- **Build time**: < 30 seconds for incremental builds
- **Test execution**: < 60 seconds for full test suite
- **Single test**: < 10 seconds
- **Simulator boot**: < 15 seconds

### Optimization Commands

```bash
# Parallel building (if supported)
xcodebuild -jobs 4

# Skip unnecessary steps for testing
xcodebuild test -skip-testing:UITests

# Use release configuration for performance testing
xcodebuild test -configuration Release
```

## Integration with Documentation

After each successful implementation cycle:

```bash
# Update status in the DrMuscleWatch project
echo "$(date): Completed [feature name] - all tests passing" >> docs/status.md

# Commit changes
git add .
git commit -m "feat: implement [feature] with passing tests"

# Push to remote (for backup and CI)
git push origin [BRANCH_NAME]
```

## Success Criteria

The remote SSH setup is working correctly when:

- [ ] SSH connection established successfully
- [ ] Build completes in < 30 seconds
- [ ] Tests run in < 60 seconds
- [ ] Simulator boots reliably
- [ ] Auto-healing resolves 80%+ of common issues
- [ ] TDD cycle (test → code → test) takes < 2 minutes
- [ ] Login functionality works end-to-end

## Emergency Fallback

If the remote Mac becomes unresponsive:

```bash
# 1. Check SSH connection
ssh [USERNAME]@[HOST] -p [PORT] "echo 'alive'"

# 2. Restart Xcode processes
ssh [USERNAME]@[HOST] -p [PORT] "killall Xcode; killall Simulator"

# 3. Reboot remote Mac if necessary
ssh [USERNAME]@[HOST] -p [PORT] "sudo shutdown -r now"
```

If remote Mac is completely unavailable:

- Fall back to local CI/CD
- Document the issue in docs/status.md
- Notify user of the fallback

## Memory Bank Integration

Always follow the same workflow as the web project:

1. **Read Memory Bank files**:
   - `docs/plan.md`
   - `docs/todo.md`
   - `docs/status.md`
   - `docs/testing.md`

2. **Follow TDD strictly**:
   - Write failing tests first
   - Implement minimal code to pass
   - Refactor and improve
   - Update documentation

3. **Make incremental changes**: One small, logical change at a time

4. **Verify everything**: Test after every change, never assume code works

This setup should give you the same rapid iteration speed as the web project, allowing you to quickly implement, test, and fix the login functionality and beyond.
