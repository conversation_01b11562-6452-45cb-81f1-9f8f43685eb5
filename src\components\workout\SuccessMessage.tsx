'use client'

import { useEffect } from 'react'
import { useSuccessAnimation } from '@/hooks/useSuccessAnimation'
import type { SuccessAnimationOptions } from '@/types/animations'

interface SuccessMessageProps {
  /** Main title text */
  title: string
  /** Optional message text */
  message?: string
  /** Additional CSS classes */
  className?: string
  /** Whether to start animation automatically */
  autoPlay?: boolean
  /** Callback when animation completes */
  onAnimationComplete?: () => void
  /** Animation options */
  animationOptions?: SuccessAnimationOptions
}

export function SuccessMessage({
  title,
  message,
  className = '',
  autoPlay = true,
  onAnimationComplete,
  animationOptions,
}: SuccessMessageProps) {
  const { state, prefersReducedMotion, start } = useSuccessAnimation({
    ...animationOptions,
    onComplete: onAnimationComplete,
  })

  useEffect(() => {
    if (autoPlay) {
      start()
    }
  }, [autoPlay, start])

  const shouldAnimate =
    !prefersReducedMotion && (state === 'active' || state === 'entering')
  const isVisible = state !== 'idle'

  return (
    <div
      data-testid="success-message"
      className={`
        text-center
        space-y-4
        ${className}
      `}
    >
      <h1
        data-testid="success-title"
        className={`
          text-2xl font-bold text-gray-900 dark:text-white
          ${shouldAnimate ? 'animate-slide-up' : ''}
          ${!isVisible ? 'opacity-0' : ''}
        `}
      >
        {title}
      </h1>

      {message && (
        <p
          data-testid="success-text"
          className={`
            text-lg text-gray-600 dark:text-gray-300
            ${shouldAnimate ? 'animate-slide-up' : ''}
            ${!isVisible ? 'opacity-0' : ''}
          `}
          style={{ animationDelay: shouldAnimate ? '100ms' : undefined }}
        >
          {message}
        </p>
      )}
    </div>
  )
}
