import React from 'react'
import { ShimmerOverlay } from './ShimmerEffect'

interface CounterDisplayProps {
  value: string
  size: 'small' | 'medium' | 'large'
  hasAnimated: boolean
  showGlow: boolean
  showShimmer: boolean
  shimmerOffset?: number
  label?: string
}

const sizeClasses = {
  small: 'text-2xl',
  medium: 'text-4xl',
  large: 'text-6xl',
}

export function CounterDisplay({
  value,
  size,
  hasAnimated,
  showGlow,
  showShimmer,
  shimmerOffset = 0,
  label,
}: CounterDisplayProps) {
  return (
    <div className="relative inline-block">
      <div
        data-testid="animated-counter-value"
        className={`
          ${sizeClasses[size]} 
          font-bold 
          text-text-primary 
          tabular-nums 
          transition-all 
          duration-300 
          ${hasAnimated ? 'scale-100' : 'scale-95'}
          ${showGlow ? 'animate-glow' : ''}
        `}
        aria-atomic="true"
        aria-relevant="text"
        aria-live={showShimmer ? 'off' : 'polite'}
        aria-label={`${label || 'Value'}: ${value}`}
      >
        {value}
      </div>
      <ShimmerOverlay
        show={showShimmer}
        delay={shimmerOffset}
        className="rounded"
      />
      <style jsx>{`
        @keyframes glow {
          0% {
            filter: brightness(1) drop-shadow(0 0 0 transparent);
          }
          50% {
            filter: brightness(1.2) drop-shadow(0 0 8px rgba(212, 175, 55, 0.5));
          }
          100% {
            filter: brightness(1) drop-shadow(0 0 0 transparent);
          }
        }

        .animate-glow {
          animation: glow 0.3s ease-in-out;
        }
      `}</style>
    </div>
  )
}
