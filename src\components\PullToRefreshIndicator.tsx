import React from 'react'

export interface PullToRefreshIndicatorProps {
  /** Current pull distance in pixels */
  pullDistance: number
  /** Threshold distance to trigger refresh */
  threshold: number
  /** Whether refresh is in progress */
  isRefreshing: boolean
  /** Whether user is actively pulling */
  isPulling: boolean
}

export function PullToRefreshIndicator({
  pullDistance,
  threshold,
  isRefreshing,
  isPulling,
}: PullToRefreshIndicatorProps) {
  // Calculate progress percentage
  const progress = Math.min((pullDistance / threshold) * 100, 100)
  const isReady = pullDistance >= threshold

  // Don't render if not pulling and not refreshing
  if (!isPulling && !isRefreshing) {
    return null
  }

  return (
    <div
      className="absolute top-0 left-0 right-0 flex justify-center pointer-events-none z-50"
      style={{
        transform: `translateY(${pullDistance}px)`,
        transition: isPulling ? 'none' : 'transform 0.2s ease-out',
      }}
    >
      <div
        className={`
          mt-4 w-10 h-10 rounded-full bg-white dark:bg-gray-800 shadow-lg
          flex items-center justify-center transition-all duration-200
          ${isReady ? 'scale-110' : 'scale-100'}
        `}
      >
        {isRefreshing ? (
          // Loading spinner
          <svg
            className="animate-spin h-6 w-6 text-primary-600"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        ) : (
          // Pull indicator with rotation based on progress
          <svg
            className={`h-6 w-6 transition-transform duration-200 ${
              isReady ? 'text-primary-600' : 'text-gray-400'
            }`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            style={{
              transform: `rotate(${progress * 1.8}deg)`,
            }}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 14l-7 7m0 0l-7-7m7 7V3"
            />
          </svg>
        )}
      </div>
    </div>
  )
}
