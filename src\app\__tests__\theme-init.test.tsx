import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { ThemeInitScript } from '../theme-init'

describe('ThemeInitScript', () => {
  it('should render a script element', () => {
    const { container } = render(<ThemeInitScript />)
    const script = container.querySelector('script')
    expect(script).toBeTruthy()
    expect(script?.innerHTML).toContain('document.body')
  })

  it('should have script that checks for document.body existence', () => {
    const { container } = render(<ThemeInitScript />)
    const script = container.querySelector('script')
    const scriptContent = script?.innerHTML || ''

    // The script should now check for document.body before accessing it
    expect(scriptContent).toContain('if (document.body)')
    expect(scriptContent).toContain('document.body.style')
  })
})
