/**
 * Formats large numbers with K/M/B suffixes similar to the Dr. Muscle mobile app
 * Based on the SO30180672.FormatNumber implementation from the mobile app
 */

/**
 * Rounds a number to 3 significant digits
 * @param x The number to round
 * @returns The rounded number
 */
function maxThreeSignificantDigits(x: number): number {
  if (x === 0) return 0

  const absX = Math.abs(x)
  const sign = x < 0 ? -1 : 1

  // Calculate the number of digits before decimal
  const i = Math.floor(Math.log10(absX))
  // Keep only 3 significant digits
  const factor = Math.max(0, i - 2)
  const divisor = Math.pow(10, factor)

  return sign * Math.round(absX / divisor) * divisor
}

/**
 * Formats a number with K/M/B suffixes
 * @param num The number to format
 * @returns The formatted string (e.g., "1.23 M")
 */
export function formatLargeNumber(num: number): string {
  if (num === null || num === undefined) {
    return '0'
  }

  // First, round to 3 significant digits
  const rounded = maxThreeSignificantDigits(num)

  // Format based on size
  if (rounded > 999999999 || rounded < -999999999) {
    // Billions: 1,234,567,890 → "1.234 B"
    const billions = rounded / 1000000000
    // Use up to 3 decimal places, but remove trailing zeros
    const formatted = billions.toFixed(3).replace(/\.?0+$/, '')
    return `${formatted} B`
  } else if (rounded > 999999 || rounded < -999999) {
    // Millions: 1,234,567 → "1.23 M"
    const millions = rounded / 1000000
    // Use up to 2 decimal places, but remove trailing zeros
    const formatted = millions.toFixed(2).replace(/\.?0+$/, '')
    return `${formatted} M`
  } else if (rounded > 999 || rounded < -999) {
    // Thousands: 1,234 → "1.2 K"
    const thousands = rounded / 1000
    // Use up to 1 decimal place, but remove trailing zeros
    const formatted = thousands.toFixed(1).replace(/\.?0+$/, '')
    return `${formatted} K`
  } else {
    // Small numbers: display as-is with locale formatting
    return rounded.toLocaleString()
  }
}

/**
 * Examples:
 * - 875 → "875"
 * - 1234 → "1.2 K"
 * - 12345 → "12.3 K"
 * - 123456 → "123 K"
 * - 1234567 → "1.23 M"
 * - 12345678 → "12.3 M"
 * - 123456789 → "123 M"
 * - 1234567890 → "1.234 B"
 */
