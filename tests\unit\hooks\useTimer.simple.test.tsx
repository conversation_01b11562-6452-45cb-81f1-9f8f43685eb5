import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useTimer } from '@/hooks/useTimer'

// Simplified timer tests focusing on basic functionality
describe('useTimer - Simplified Tests', () => {
  beforeEach(() => {
    vi.useFakeTimers()
    // Simple RAF mock that doesn't interfere with timer
    global.requestAnimationFrame = vi.fn((cb) => {
      return setTimeout(() => cb(Date.now()), 16) as unknown as number
    })
    global.cancelAnimationFrame = vi.fn((id) => clearTimeout(id))

    // Also set window.cancelAnimationFrame for broader compatibility
    if (typeof window !== 'undefined') {
      window.requestAnimationFrame = global.requestAnimationFrame
      window.cancelAnimationFrame = global.cancelAnimationFrame
    }
  })

  afterEach(() => {
    vi.clearAllTimers()
    vi.useRealTimers()
  })

  it('should initialize with correct values', () => {
    const { result } = renderHook(() => useTimer(60))

    expect(result.current.timeRemaining).toBe(60)
    expect(result.current.isRunning).toBe(false)
    expect(result.current.isPaused).toBe(false)
    expect(result.current.isComplete).toBe(false)
    expect(result.current.formattedTime).toBe('1:00')
  })

  it('should format time correctly', () => {
    const { result: result1 } = renderHook(() => useTimer(0))
    expect(result1.current.formattedTime).toBe('0:00')

    const { result: result2 } = renderHook(() => useTimer(59))
    expect(result2.current.formattedTime).toBe('0:59')

    const { result: result3 } = renderHook(() => useTimer(125))
    expect(result3.current.formattedTime).toBe('2:05')
  })

  it('should start and stop timer', () => {
    const { result } = renderHook(() => useTimer(10))

    // Start timer
    act(() => {
      result.current.start()
    })
    expect(result.current.isRunning).toBe(true)

    // Pause timer
    act(() => {
      result.current.pause()
    })
    expect(result.current.isRunning).toBe(false)
    expect(result.current.isPaused).toBe(true)
  })

  it('should skip to completion', () => {
    const onComplete = vi.fn()
    const { result } = renderHook(() => useTimer(60, { onComplete }))

    act(() => {
      result.current.skip()
    })

    expect(result.current.timeRemaining).toBe(0)
    expect(result.current.isComplete).toBe(true)
    expect(onComplete).toHaveBeenCalled()
  })

  it('should reset timer', () => {
    const { result } = renderHook(() => useTimer(30))

    // Start and modify timer
    act(() => {
      result.current.start()
      result.current.skip()
    })

    expect(result.current.timeRemaining).toBe(0)

    // Reset
    act(() => {
      result.current.reset()
    })

    expect(result.current.timeRemaining).toBe(30)
    expect(result.current.isRunning).toBe(false)
    expect(result.current.isPaused).toBe(false)
  })

  it('should handle zero duration', () => {
    const onComplete = vi.fn()
    const { result } = renderHook(() => useTimer(0, { onComplete }))

    expect(result.current.isComplete).toBe(true)
    expect(result.current.timeRemaining).toBe(0)

    // onComplete should be called on next tick
    act(() => {
      vi.runAllTimers()
    })
    expect(onComplete).toHaveBeenCalled()
  })

  it('should auto-start when specified', () => {
    const { result } = renderHook(() => useTimer(10, { autoStart: true }))

    expect(result.current.isRunning).toBe(true)
  })

  it('should not auto-start by default', () => {
    const { result } = renderHook(() => useTimer(10))

    expect(result.current.isRunning).toBe(false)
  })

  it('should calculate progress correctly', () => {
    const { result } = renderHook(() => useTimer(100))
    expect(result.current.progress).toBe(100)

    // Manually update to test progress
    act(() => {
      result.current.start()
      // Simulate time passing by calling skip
      result.current.skip()
    })

    expect(result.current.progress).toBe(0)
  })
})
