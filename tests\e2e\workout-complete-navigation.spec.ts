import { test, expect } from '@playwright/test'

test.describe('Workout Complete Page Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // Mock the auth state
    await page.addInitScript(() => {
      const mockUser = {
        id: 123,
        email: '<EMAIL>',
        name: 'Test User',
      }
      const mockToken = 'mock-jwt-token'

      localStorage.setItem(
        'drmuscle-auth',
        JSON.stringify({
          state: {
            user: mockUser,
            token: mockToken,
          },
          version: 0,
        })
      )
    })
  })

  test('should display user menu (3-dot kebab menu) instead of share button', async ({
    page,
  }) => {
    // Navigate to workout complete page
    await page.goto('/workout/complete')

    // Wait for navigation to be visible
    await page.waitForSelector('nav', { timeout: 5000 })

    // Check that the kebab menu icon is present
    const kebabMenu = page.locator('button[aria-label="User menu"]')
    await expect(kebabMenu).toBeVisible()

    // Verify the kebab menu contains 3 dots (SVG circles)
    const menuIcon = kebabMenu.locator('svg')
    await expect(menuIcon).toBeVisible()
    const circles = menuIcon.locator('circle')
    await expect(circles).toHaveCount(3)

    // Verify share button is NOT present
    const shareButton = page.locator('button[aria-label="Share workout"]')
    await expect(shareButton).not.toBeVisible()
  })

  test('should open user menu dropdown when kebab menu is clicked', async ({
    page,
  }) => {
    // Navigate to workout complete page
    await page.goto('/workout/complete')

    // Wait for and click the kebab menu
    const kebabMenu = page.locator('button[aria-label="User menu"]')
    await kebabMenu.waitFor({ state: 'visible' })
    await kebabMenu.click()

    // Verify the dropdown menu appears
    const dropdown = page.locator('[role="menu"]')
    await expect(dropdown).toBeVisible()

    // Verify menu contains expected items
    await expect(dropdown.locator('text=<EMAIL>')).toBeVisible()
    await expect(dropdown.locator('button:has-text("Log out")')).toBeVisible()
  })

  test('should maintain consistent navigation across pages', async ({
    page,
  }) => {
    // Check program page has kebab menu
    await page.goto('/program')
    const programKebab = page.locator('button[aria-label="User menu"]')
    await expect(programKebab).toBeVisible()

    // Check workout complete page has same kebab menu
    await page.goto('/workout/complete')
    const completeKebab = page.locator('button[aria-label="User menu"]')
    await expect(completeKebab).toBeVisible()

    // Both should have the same structure (3 dots)
    const programCircles = programKebab.locator('svg circle')
    const completeCircles = completeKebab.locator('svg circle')
    await expect(programCircles).toHaveCount(3)
    await expect(completeCircles).toHaveCount(3)
  })
})
