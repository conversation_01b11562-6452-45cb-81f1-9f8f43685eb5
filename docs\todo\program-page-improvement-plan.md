# Program Page Improvement Plan

## Overview

Improve the program page by:

1. Enhancing the main stats display with card-based design and icons
2. Removing the stats grid at the bottom
3. Maintaining mobile-first performance and accessibility

## Current State

- 3 main stats displayed vertically with AnimatedCounter (weeks streak, workouts, lbs lifted)
- Stats grid at bottom showing 6 additional metrics
- Welcome message with user's first name at top
- Mobile-optimized with progressive loading

## Target State

- Welcome message remains at top
- 3 main stats in card-based design with icons
- Stats grid removed
- Clean, focused interface

## Implementation Strategy

### Phase 1: Icon Extraction and Component Creation

Extract the existing SVG icons from ProgramStatsGrid into reusable components that can be imported elsewhere.

### Phase 2: Enhance AnimatedCounter with Icon Support

Add icon support to AnimatedCounter component while maintaining backward compatibility.

### Phase 3: Create Card-Based Layout

Transform the stats display from simple counters to card-based design with icons.

### Phase 4: Remove Stats Grid

Clean up by removing the stats grid component.

### Phase 5: Polish and Testing

Final adjustments, responsive testing, and performance validation.

---

## Detailed Implementation Prompts

### Prompt 1: Extract and Create Icon Components

```text
I need to extract three SVG icons from the ProgramStatsGrid component and create them as reusable React components. The icons are currently defined inline in the ProgramStatsGrid component at /src/components/program/ProgramStatsGrid.tsx.

Please:
1. Create three new icon component files in /src/components/icons/:
   - StreakIcon.tsx (extract the flame/fire icon from lines 16-38)
   - WorkoutsIcon.tsx (extract the clipboard with checkmark from lines 40-56)
   - WeightLiftedIcon.tsx (extract the cloud icon from lines 58-74)

2. Each icon component should:
   - Accept className prop for styling flexibility
   - Use the existing icon pattern from the project (check ChevronLeftIcon for reference)
   - Maintain the same SVG properties and paths as the originals
   - Be properly typed with TypeScript

3. Add exports for these new icons to /src/components/icons/index.ts

4. Write unit tests for each icon component in their respective test files to verify:
   - Icons render without errors
   - className prop is properly applied
   - SVG structure is maintained

Make sure to follow the existing project patterns and maintain consistency with other icon components.
```

### Prompt 2: Add Icon Support to AnimatedCounter

```text
I need to enhance the AnimatedCounter component to support optional icons while maintaining backward compatibility. The component is located at /src/components/ui/AnimatedCounter.tsx.

Please:
1. Add a new optional prop to AnimatedCounterProps:
   - icon?: React.ReactNode

2. Update the component to render the icon when provided:
   - Place the icon to the left of the counter value
   - Ensure proper alignment and spacing
   - Maintain the existing animation behavior
   - Keep the icon visible during loading/shimmer states

3. Update the component's layout:
   - Use flexbox to align icon and value horizontally
   - Add appropriate gap between icon and value (suggest gap-3)
   - Ensure the icon scales appropriately with the size prop
   - Icon size should be: small=16px, medium=20px, large=24px

4. Write comprehensive tests to verify:
   - Component works without icon (backward compatibility)
   - Icon renders when provided
   - Icon scales with size prop
   - Layout remains accessible
   - Loading states work correctly with icon

5. Ensure the implementation maintains:
   - Existing animation behavior
   - Accessibility features (ARIA labels, live regions)
   - Loading and shimmer effects
   - All current functionality

The goal is to add icon support without breaking any existing usage of AnimatedCounter.
```

### Prompt 3: Create Stat Card Component

```text
I need to create a new StatCard component that wraps AnimatedCounter to provide a card-based design for the program page stats.

Please:
1. Create a new component at /src/components/program/StatCard.tsx that:
   - Wraps the AnimatedCounter component
   - Provides a card-like appearance with subtle background and border
   - Supports light/dark mode
   - Maintains mobile-first design principles
   - Has a minimum touch target of 44px

2. The component should accept these props:
   - value: number | null | undefined
   - label: string
   - icon: React.ReactNode
   - isLoading?: boolean
   - showShimmer?: boolean
   - shimmerOffset?: number
   - className?: string

3. Design specifications:
   - Background: bg-white dark:bg-gray-800 with subtle transparency
   - Border: border border-gray-200 dark:border-gray-700
   - Border radius: rounded-lg
   - Padding: p-4 (16px)
   - Shadow: shadow-sm for subtle depth
   - Hover state: slight scale (1.02) with transition

4. The card should:
   - Pass the icon to the enhanced AnimatedCounter
   - Use size="medium" for the counter (can be adjusted later)
   - Center content vertically and horizontally
   - Be responsive and work well in a horizontal layout

5. Write tests to verify:
   - Card renders with all required props
   - Dark mode styles apply correctly
   - Loading states work properly
   - Card is accessible with proper ARIA attributes
   - Touch target meets 44px minimum

6. Include proper TypeScript types and follow the project's component patterns.
```

### Prompt 4: Implement Horizontal Stats Layout

```text
I need to update the program page to display the three main stats in a horizontal card-based layout using the new StatCard component.

Please update /src/app/(dashboard)/program/page.tsx:
1. Import the new components:
   - StatCard from @/components/program/StatCard
   - StreakIcon, WorkoutsIcon, WeightLiftedIcon from @/components/icons

2. Replace the current vertical stats section (lines 271-310) with:
   - A horizontal container using flexbox
   - Three StatCard components for weeks streak, workouts, and lbs lifted
   - Proper icons for each stat (StreakIcon, WorkoutsIcon, WeightLiftedIcon)
   - Responsive layout that stacks on very small screens if needed

3. Layout specifications:
   - Use flex with gap-4 between cards
   - Add overflow-x-auto for horizontal scrolling on very small screens
   - Ensure cards have equal width (flex-1)
   - Center the stats section horizontally with max-width constraint
   - Add appropriate margin/padding

4. Update the data passing:
   - Pass userInfo?.WeeksStreak to first card with StreakIcon
   - Pass userInfo?.WorkoutsCompleted to second card with WorkoutsIcon
   - Pass userInfo?.TotalVolume to third card with WeightLiftedIcon
   - Maintain the existing labels and formatting

5. Preserve the loading behavior:
   - Keep the isLoading and showShimmer logic
   - Maintain staggered shimmer offsets (0ms, 100ms, 200ms)
   - Ensure progressive loading works correctly

6. Write integration tests to verify:
   - Stats display correctly with mock data
   - Loading states work properly
   - Layout is responsive
   - Icons display correctly with stats
   - No regressions in functionality

Make sure the implementation maintains all existing functionality while improving the visual presentation.
```

### Prompt 5: Remove Stats Grid and Clean Up

```text
I need to remove the stats grid from the program page and clean up any unused code.

Please:
1. In /src/app/(dashboard)/program/page.tsx:
   - Remove the ProgramStatsGrid component usage (around line 313-316)
   - Remove the import for ProgramStatsGrid
   - Remove any comments or code related to the stats grid

2. Clean up spacing:
   - Adjust margins/padding to ensure proper spacing after stats removal
   - Verify the workout actions section has appropriate top margin
   - Ensure overall page layout remains balanced

3. Verify the ProgramStatsGrid component is not used elsewhere:
   - Search for any other imports or usage
   - If not used, mark for potential deletion (but don't delete yet)

4. Update any related documentation or comments that reference the stats grid

5. Write tests to verify:
   - Page renders correctly without stats grid
   - No console errors or warnings
   - Layout remains consistent
   - All other functionality works as before

6. Performance check:
   - Ensure removing the stats grid improves or maintains load time
   - Verify bundle size impact

This step completes the UI improvement by focusing user attention on the three main metrics.
```

### Prompt 6: Polish and Responsive Adjustments

```text
I need to polish the program page implementation with final adjustments for optimal mobile experience.

Please:
1. Review and optimize the horizontal stats layout:
   - Test on 320px, 375px, and 430px viewports
   - Adjust card sizing if needed for smallest screens
   - Consider using grid instead of flex if it provides better control
   - Ensure no horizontal overflow on any screen size

2. Fine-tune the StatCard component:
   - Adjust icon sizes if needed for better visual balance
   - Optimize padding/spacing for mobile screens
   - Ensure text doesn't wrap awkwardly
   - Add min-width to prevent cards from becoming too narrow

3. Typography adjustments:
   - Verify font sizes work well with icons
   - Ensure numbers with many digits (e.g., 1,234,567) display properly
   - Check label text remains readable

4. Icon color coordination:
   - Add appropriate color classes to icons for better visual hierarchy
   - Consider using accent colors that match the app's design system
   - Ensure icons are visible in both light and dark modes

5. Animation polish:
   - Verify icon doesn't interfere with number animation
   - Ensure smooth transitions for all interactive elements
   - Check that loading states look polished with icons

6. Accessibility review:
   - Test with screen readers
   - Verify keyboard navigation works properly
   - Ensure color contrast meets WCAG standards
   - Add any missing ARIA labels

7. Write comprehensive E2E tests:
   - Test the complete user flow
   - Verify stats load and display correctly
   - Test on multiple viewport sizes
   - Ensure no regressions in functionality

This final step ensures a polished, production-ready implementation.
```

### Prompt 7: Integration Testing and Deployment Preparation

```text
I need to perform comprehensive integration testing and prepare for deployment.

Please:
1. Run all existing tests:
   - npm run typecheck
   - npm run lint
   - npm run test
   - Fix any issues that arise

2. Manual testing checklist:
   - Run npm run dev and test at localhost:3000
   - Login with test credentials: <EMAIL>/Test@1234
   - Verify welcome message shows correct first name
   - Confirm all three stats load and animate correctly
   - Test pull-to-refresh functionality
   - Check dark mode toggle
   - Test on mobile viewport (320-430px)
   - Verify icons display correctly
   - Ensure stats grid is completely removed
   - Test loading states and error scenarios

3. Performance validation:
   - Run npm run build
   - Check bundle size with npm run analyze
   - Ensure we stay under 150KB JavaScript bundle limit
   - Verify load time < 1s
   - Test touch response < 50ms

4. Cross-browser testing:
   - Test on Chrome mobile
   - Test on Safari iOS (if available)
   - Verify PWA features work correctly

5. Update documentation:
   - Update status.md with completed work
   - Clean up todo.md
   - Document any known issues or future improvements

6. Final code review:
   - Ensure no console.log statements
   - Verify no TypeScript 'any' types
   - Check components are under 200 lines
   - Confirm all imports are used

7. Create a comprehensive commit:
   - Stage all changes
   - Write descriptive commit message following conventional commits
   - Include what was changed and why

This ensures the implementation is production-ready and maintains the high quality standards of the codebase.
```

---

## Testing Strategy

Each prompt includes specific testing requirements to ensure:

1. Unit tests for individual components
2. Integration tests for component interactions
3. E2E tests for complete user flows
4. Performance testing for mobile optimization
5. Accessibility testing for inclusive design

## Risk Mitigation

- Each step is small and self-contained
- Backward compatibility is maintained
- Tests are written before or alongside implementation
- Mobile-first approach ensures primary use case works perfectly
- Progressive enhancement allows graceful degradation

## Success Criteria

- Program page shows only welcome message and three card-based stats with icons
- Stats grid is removed
- Performance targets are met (< 1s load, < 50ms touch response)
- All tests pass
- Mobile experience is excellent across all target devices
- Accessibility standards are maintained
