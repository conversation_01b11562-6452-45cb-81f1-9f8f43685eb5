import { useQuery } from '@tanstack/react-query'
import { programApi } from '@/api/program'
import { useAuthStore } from '@/stores/authStore'

/**
 * Unified hook that fetches all program data in a single API call
 * This prevents duplicate requests to GetUserWorkoutLogAverageWithUserStatsV2
 */
export function useProgramDataUnified() {
  const { isAuthenticated } = useAuthStore()

  // Single query that fetches all data
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['programDataUnified'],
    queryFn: async () => {
      // Fetch all three data types in parallel using Promise.all
      // Each will use the cached data from fetchUnifiedProgramData
      const [program, progress, stats] = await Promise.all([
        programApi.getUserProgram(),
        programApi.getProgramProgress(),
        programApi.getProgramStats(),
      ])

      return { program, progress, stats }
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  })

  return {
    program: data?.program || null,
    progress: data?.progress || null,
    stats: data?.stats || null,
    isLoading,
    error,
    refetch,
    hasData: !!data,
  }
}
