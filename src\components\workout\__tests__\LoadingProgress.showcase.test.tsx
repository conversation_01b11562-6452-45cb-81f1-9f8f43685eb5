import { render } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { LoadingProgress } from '../LoadingProgress'

describe('LoadingProgress Showcase', () => {
  it('should render successfully in different configurations', () => {
    // Basic progress
    const { container: basic } = render(<LoadingProgress progress={25} />)
    expect(basic).toBeTruthy()

    // With status text
    const { container: withStatus } = render(
      <LoadingProgress progress={50} status="Loading workouts..." />
    )
    expect(withStatus).toBeTruthy()

    // With percentage display
    const { container: withPercentage } = render(
      <LoadingProgress progress={75} showPercentage />
    )
    expect(withPercentage).toBeTruthy()

    // Full featured
    const { container: fullFeatured } = render(
      <LoadingProgress progress={90} status="Almost there..." showPercentage />
    )
    expect(fullFeatured).toBeTruthy()

    // Edge cases
    const { container: zeroProgress } = render(<LoadingProgress progress={0} />)
    expect(zeroProgress).toBeTruthy()

    const { container: completeProgress } = render(
      <LoadingProgress progress={100} />
    )
    expect(completeProgress).toBeTruthy()

    // Custom styling
    const { container: customStyle } = render(
      <LoadingProgress progress={60} className="mt-4 mb-2" />
    )
    expect(customStyle).toBeTruthy()

    // Real world example
    const { container: realWorld } = render(
      <LoadingProgress
        progress={33}
        status="Loading your personalized workout plan..."
        showPercentage
      />
    )
    expect(realWorld).toBeTruthy()
  })
})
