import { test, expect, Page } from '@playwright/test'
import {
  mockWorkoutAPI,
  mockExerciseSets,
  mockRecommendations,
} from './helpers/workout-mocks'

test.describe('Workout Loading Refactor - Progressive Loading', () => {
  let page: Page

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage

    // Mock API responses
    await mockWorkoutAPI(page)

    // Navigate to workout page
    await page.goto('/workout')
  })

  test('should progressively load workout data', async () => {
    // Step 1: Verify initial loading state
    await expect(page.getByTestId('workout-loading')).toBeVisible()

    // Step 2: Exercises should appear first (from cache or initial API call)
    await expect(page.getByTestId('exercise-card')).toHaveCount(3, {
      timeout: 2000,
    })

    // Step 3: Each exercise should show set loading skeletons
    const firstExercise = page.getByTestId('exercise-card').first()
    await expect(firstExercise.getByTestId('set-skeleton')).toHaveCount(3)

    // Step 4: Sets should load progressively for each exercise
    await mockExerciseSets(page, 1, [
      { Id: 1, Reps: 10, Weight: { Lb: 135, Kg: 61.2 }, IsWarmup: false },
    ])

    // Verify first exercise has loaded sets
    await expect(firstExercise.getByTestId('set-item')).toHaveCount(1)
    await expect(firstExercise.getByText('135 lbs × 10')).toBeVisible()

    // Step 5: Recommendations should load after sets
    await mockRecommendations(page, 1, {
      FirstWorkSetWeight: { Lb: 140, Kg: 63.5 },
      FirstWorkSetReps: 8,
    })

    // Verify recommendation appears
    await expect(
      firstExercise.getByText('Recommended: 140 lbs × 8')
    ).toBeVisible()
  })

  test('should handle API failures gracefully', async () => {
    // Mock API failure for sets
    await page.route('**/api/Exercise/GetUserWorkoutSets*', (route) => {
      route.abort('failed')
    })

    // Navigate to workout
    await page.goto('/workout')

    // Exercises should still show (from cache or initial load)
    await expect(page.getByTestId('exercise-card')).toHaveCount(3)

    // Error state should be shown for sets
    const firstExercise = page.getByTestId('exercise-card').first()
    await expect(firstExercise.getByText('Failed to load sets')).toBeVisible()

    // Retry button should be available
    const retryButton = firstExercise.getByRole('button', { name: 'Retry' })
    await expect(retryButton).toBeVisible()

    // Mock successful response and retry
    await mockExerciseSets(page, 1, [
      { Id: 1, Reps: 10, Weight: { Lb: 135, Kg: 61.2 }, IsWarmup: false },
    ])

    await retryButton.click()

    // Sets should now load
    await expect(firstExercise.getByText('135 lbs × 10')).toBeVisible()
  })

  test('should support pull-to-refresh', async () => {
    // Navigate to workout
    await page.goto('/workout')

    // Wait for initial load
    await expect(page.getByTestId('exercise-card')).toHaveCount(3)

    // Perform pull-to-refresh gesture
    const workoutContainer = page.getByTestId('workout-container')
    await workoutContainer.hover()
    await page.mouse.down()
    await page.mouse.move(0, 150) // Pull down 150px

    // Verify refresh indicator appears
    await expect(page.getByTestId('pull-to-refresh-indicator')).toBeVisible()

    // Release to trigger refresh
    await page.mouse.up()

    // Verify refresh happens (loading state briefly appears)
    await expect(page.getByTestId('workout-refreshing')).toBeVisible()

    // Data should reload
    await expect(page.getByTestId('exercise-card')).toHaveCount(3)
  })

  test('should maintain cache during refresh', async () => {
    // Navigate to workout
    await page.goto('/workout')

    // Wait for initial load
    await expect(page.getByTestId('exercise-card')).toHaveCount(3)
    const firstExercise = page.getByTestId('exercise-card').first()
    await expect(firstExercise.getByText('Bench Press')).toBeVisible()

    // Mock API failure
    await page.route('**/api/Workout/GetUserWorkoutTemplateGroup*', (route) => {
      route.abort('failed')
    })

    // Trigger refresh
    await page.reload()

    // Cached data should still be visible
    await expect(firstExercise.getByText('Bench Press')).toBeVisible()

    // Stale indicator should appear
    await expect(page.getByText('Offline - Showing cached data')).toBeVisible()
  })

  test('should meet performance targets', async () => {
    // Start performance measurement
    const startTime = Date.now()

    // Navigate to workout
    await page.goto('/workout')

    // Measure time to first exercise visible
    await expect(page.getByTestId('exercise-card').first()).toBeVisible()
    const timeToFirstExercise = Date.now() - startTime

    // Should be less than 50ms from cache
    expect(timeToFirstExercise).toBeLessThan(1000) // Allow some overhead for test environment

    // Measure time for all exercises to load
    await expect(page.getByTestId('exercise-card')).toHaveCount(3)
    const timeToAllExercises = Date.now() - startTime

    // Should be less than 2s on slow connection
    expect(timeToAllExercises).toBeLessThan(2000)
  })

  test('should handle error boundaries correctly', async () => {
    // Inject error into component
    await page.addScriptTag({
      content: `
        window.__throwTestError = () => {
          throw new Error('Test error for error boundary')
        }
      `,
    })

    // Navigate to workout
    await page.goto('/workout')

    // Trigger error
    await page.evaluate(() => {
      // @ts-expect-error - __throwTestError is injected for testing error boundaries
      window.__throwTestError()
    })

    // Error boundary should catch and display fallback
    await expect(page.getByText('Something went wrong')).toBeVisible()
    await expect(page.getByRole('button', { name: 'Try Again' })).toBeVisible()
  })
})
