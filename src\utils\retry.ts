/**
 * Retry utility with exponential backoff
 *
 * Implements retry logic with exponential backoff, jitter, and abort capability
 * to handle transient failures gracefully.
 */

export interface RetryOptions {
  maxRetries?: number
  baseDelay?: number
  maxDelay?: number
  factor?: number
  jitter?: number
  onRetry?: (error: Error, attempt: number) => void
  signal?: AbortSignal
}

export interface RetryResult<T> {
  success: boolean
  data?: T
  error?: Error
  attempts: number
}

/**
 * Calculate exponential backoff delay with jitter
 *
 * @param attempt The current attempt number (0-based)
 * @param options Backoff calculation options
 * @returns Delay in milliseconds
 */
export function calculateBackoff(
  attempt: number,
  options: {
    baseDelay: number
    maxDelay: number
    factor: number
    jitter: number
  }
): number {
  const { baseDelay, maxDelay, factor, jitter } = options

  // Calculate exponential delay
  const exponentialDelay = Math.min(
    baseDelay * Math.pow(factor, attempt),
    maxDelay
  )

  // Apply jitter to prevent thundering herd
  const jitterRange = exponentialDelay * jitter
  const jitterAmount = (Math.random() * 2 - 1) * jitterRange

  return Math.max(0, Math.round(exponentialDelay + jitterAmount))
}

/**
 * Sleep for a given duration with abort support
 *
 * @param ms Duration in milliseconds
 * @param signal Optional abort signal
 * @returns Promise that resolves after the delay
 */
function sleep(ms: number, signal?: AbortSignal): Promise<void> {
  return new Promise((resolve, reject) => {
    if (signal?.aborted) {
      reject(new Error('Sleep aborted'))
      return
    }

    const timeoutId = setTimeout(resolve, ms)

    // Listen for abort signal
    if (signal) {
      const handleAbort = () => {
        clearTimeout(timeoutId)
        reject(new Error('Sleep aborted'))
      }

      signal.addEventListener('abort', handleAbort, { once: true })
    }
  })
}

/**
 * Execute a function with retry logic and exponential backoff
 *
 * @param fn The async function to retry
 * @param options Retry configuration options
 * @returns Promise with the result or error after all retries
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 30000,
    factor = 2,
    jitter = 0.2,
    onRetry,
    signal,
  } = options

  let lastError: Error | null = null

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    // Check if aborted
    if (signal?.aborted) {
      throw new Error('Operation aborted')
    }

    try {
      // eslint-disable-next-line no-await-in-loop
      return await fn()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break
      }

      // Call retry callback if provided
      onRetry?.(lastError, attempt + 1)

      // Calculate delay with exponential backoff
      const delay = calculateBackoff(attempt, {
        baseDelay,
        maxDelay,
        factor,
        jitter,
      })

      // Wait before retrying
      // eslint-disable-next-line no-await-in-loop
      await sleep(delay, signal)
    }
  }

  // All retries exhausted
  throw lastError || new Error('Operation failed after retries')
}

/**
 * Execute a function with retry logic and return detailed result
 *
 * @param fn The async function to retry
 * @param options Retry configuration options
 * @returns Promise with detailed retry result
 */
export async function retryWithDetails<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<RetryResult<T>> {
  let attempts = 0

  try {
    const data = await retryWithBackoff(async () => {
      attempts++
      return fn()
    }, options)

    return {
      success: true,
      data,
      attempts,
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error : new Error(String(error)),
      attempts,
    }
  }
}

/**
 * Create a retry function with preset options
 *
 * @param options Default retry options
 * @returns Retry function with preset options
 */
export function createRetryFn(options: RetryOptions = {}) {
  return <T>(fn: () => Promise<T>, overrides?: RetryOptions): Promise<T> => {
    return retryWithBackoff(fn, { ...options, ...overrides })
  }
}

/**
 * Check if an error is retryable
 *
 * @param error The error to check
 * @returns Whether the error is retryable
 */
export function isRetryableError(error: unknown): boolean {
  if (!(error instanceof Error)) {
    return false
  }

  // Network errors are typically retryable
  if (error.message.toLowerCase().includes('network')) {
    return true
  }

  // Timeout errors are retryable
  if (error.message.toLowerCase().includes('timeout')) {
    return true
  }

  // Check for specific HTTP status codes if available
  if ('status' in error && typeof error.status === 'number') {
    const { status } = error as { status: number }
    // Retry on 5xx errors and specific 4xx errors
    return status >= 500 || status === 429 || status === 408
  }

  return false
}
