import { apiClient } from './client'
import { UserStats } from '@/types/userStats'
import { logger } from '@/utils/logger'
import { retryApiCall, STATS_RETRY_OPTIONS } from '@/utils/apiRetry'

/**
 * Get the current week number in YYYYWW format (e.g., 202528 for week 28 of 2025)
 */
function getCurrentWeekNumber(): number {
  const now = new Date()
  const year = now.getFullYear()

  // Get the first day of the year
  const firstDay = new Date(year, 0, 1)

  // Calculate the week number
  const days = Math.floor(
    (now.getTime() - firstDay.getTime()) / (24 * 60 * 60 * 1000)
  )
  const weekNumber = Math.ceil((days + firstDay.getDay() + 1) / 7)

  return year * 100 + weekNumber
}

/**
 * Fetch complete user statistics using the mobile app's two-step approach
 * This gets the full historical data including all workouts and total weight lifted
 */
export async function fetchCompleteUserStats(): Promise<UserStats> {
  try {
    logger.log(
      '[UserStats API] Fetching complete stats from GetLogAverageWithSetsV2...'
    )

    // Step 1: Get workout program info with timezone (mobile app pattern)
    // COMMENTED OUT: Not using the response, skipping to speed up loading
    /*
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone

    logger.log(
      '[UserStats API] Step 1: Calling GetUserWorkoutProgramTimeZoneInfo...'
    )
    try {
      await retryApiCall(
        () =>
          apiClient.post('/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo', {
            standardName: timezone,
          }),
        STATS_RETRY_OPTIONS
      )
    } catch (error) {
      logger.error('[UserStats API] Error in Step 1:', error)
      throw error
    }

    if (process.env.NODE_ENV === 'development') {
      logger.log('[UserStats API] Program info response received')
    }
    */

    // Step 2: Get complete workout history with exercise stats
    logger.log('[UserStats API] Step 2: Calling GetLogAverageWithSetsV2...')
    let historyResponse
    try {
      historyResponse = await retryApiCall(
        () => apiClient.post('/api/WorkoutLog/GetLogAverageWithSetsV2', {}),
        STATS_RETRY_OPTIONS
      )
    } catch (error) {
      logger.error('[UserStats API] Error in Step 2:', error)
      throw error
    }

    const { data } = historyResponse
    const historyData = data.Result || data

    if (process.env.NODE_ENV === 'development') {
      logger.log(
        '[UserStats API] History response keys:',
        Object.keys(historyData).slice(0, 10)
      )
      if (historyData.HistoryExerciseModel) {
        logger.log('[UserStats API] HistoryExerciseModel found:', {
          hasWorkoutCount:
            !!historyData.HistoryExerciseModel.TotalWorkoutCompleted,
          hasTotalWeight: !!historyData.HistoryExerciseModel.TotalWeight,
        })
      }
    }

    // Extract stats from HistoryExerciseModel (mobile app pattern)
    let weekStreak = 0
    let workoutsCompleted = 0
    let lbsLifted = 0

    // Get workout count from HistoryExerciseModel
    if (historyData.HistoryExerciseModel?.TotalWorkoutCompleted) {
      workoutsCompleted = historyData.HistoryExerciseModel.TotalWorkoutCompleted
      logger.log(
        '[UserStats API] Total workouts from HistoryExerciseModel:',
        workoutsCompleted
      )
    }

    // Get total weight lifted from HistoryExerciseModel
    if (historyData.HistoryExerciseModel?.TotalWeight?.Lb) {
      lbsLifted = Math.round(historyData.HistoryExerciseModel.TotalWeight.Lb)
      logger.log(
        '[UserStats API] Total lbs lifted from HistoryExerciseModel:',
        lbsLifted
      )
    }

    // Get consecutive weeks - calculate current streak from the array
    if (
      historyData.ConsecutiveWeeks &&
      Array.isArray(historyData.ConsecutiveWeeks) &&
      historyData.ConsecutiveWeeks.length > 0
    ) {
      // The array contains all historical streaks, we need to determine the current streak
      // The most recent streak is the last item if it includes the current week
      const allStreaks = historyData.ConsecutiveWeeks
      const currentWeek = getCurrentWeekNumber()

      // Find the streak that includes the current or previous week
      for (let i = allStreaks.length - 1; i >= 0; i--) {
        const streak = allStreaks[i]
        if (streak.MaxWeek >= currentWeek - 1) {
          // This is the current streak (or just ended last week)
          weekStreak = streak.ConsecutiveWeeks || 0
          break
        }
      }

      logger.log(
        '[UserStats API] Current week streak from ConsecutiveWeeks array:',
        weekStreak
      )
    } else if (typeof historyData.ConsecutiveWeeks === 'number') {
      weekStreak = historyData.ConsecutiveWeeks
    }

    const stats: UserStats = {
      weekStreak,
      workoutsCompleted,
      lbsLifted,
    }

    logger.log('[UserStats API] Complete stats extracted:', stats)
    return stats
  } catch (error) {
    logger.error('[UserStats API] Error fetching complete stats:', error)
    throw error
  }
}
