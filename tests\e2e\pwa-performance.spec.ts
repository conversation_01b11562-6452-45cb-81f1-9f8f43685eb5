import { test, expect, Page } from '@playwright/test'

test.describe('PWA Performance Tests', () => {
  let page: Page
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p }) => {
    page = p
  })

  test('should meet PWA performance benchmarks', async () => {
    await page.goto('/')

    // Run performance audit
    const metrics = (await page.evaluate(() => {
      return new Promise((resolve) => {
        // Wait for load complete
        if (document.readyState === 'complete') {
          resolve(performance.getEntriesByType('navigation')[0])
        } else {
          window.addEventListener('load', () => {
            resolve(performance.getEntriesByType('navigation')[0])
          })
        }
      })
    })) as PerformanceNavigationTiming

    // Check key metrics
    const ttfb = metrics.responseStart - metrics.fetchStart
    const domContentLoaded =
      metrics.domContentLoadedEventEnd - metrics.fetchStart
    const fullyLoaded = metrics.loadEventEnd - metrics.fetchStart

    // PWA performance requirements
    expect(ttfb).toBeLessThan(600) // Time to first byte < 600ms
    expect(domContentLoaded).toBeLessThan(1500) // DOM ready < 1.5s
    expect(fullyLoaded).toBeLessThan(3000) // Fully loaded < 3s
  })

  test('should handle large workout data efficiently', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Mock large workout response
    await page.route('**/GetWorkoutInfo', (route) => {
      const exercises = Array(50)
        .fill(null)
        .map((_, i) => ({
          ExerciseId: `exercise-${i}`,
          ExerciseName: `Exercise ${i}`,
          Sets: Array(5)
            .fill(null)
            .map((_, j) => ({
              SetId: `set-${i}-${j}`,
              Weight: 100 + j * 10,
              Reps: 10 - j,
            })),
        }))

      route.fulfill({
        status: 200,
        json: { Result: { Exercises: exercises } },
      })
    })

    // Measure render performance
    const startTime = Date.now()
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
    await page.waitForSelector('[data-testid="exercise-list"]')
    const renderTime = Date.now() - startTime

    // Should handle large data efficiently
    expect(renderTime).toBeLessThan(2000) // Render in less than 2 seconds

    // Check if virtualization is used for long lists
    const visibleExercises = await page
      .locator('[data-testid="exercise-item"]')
      .count()
    // If less than 50 visible, virtualization might be in use (good)
    expect(visibleExercises).toBeLessThanOrEqual(50)
  })
})
