'use client'

interface UserAvatarProps {
  user: { email?: string; name?: string } | null
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function UserAvatar({
  user,
  size = 'md',
  className = '',
}: UserAvatarProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
  }

  const iconSizeClasses = {
    sm: 'w-5 h-5',
    md: 'w-6 h-6',
    lg: 'w-7 h-7',
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl',
  }

  const initial = user?.name?.[0] || user?.email?.[0]

  return (
    <div
      className={`${sizeClasses[size]} rounded-full bg-blue-600 flex items-center justify-center ${className}`}
      aria-label={`User avatar${initial ? ` showing ${initial.toUpperCase()}` : ''}`}
    >
      {initial ? (
        <span className={`text-white font-medium ${textSizeClasses[size]}`}>
          {initial.toUpperCase()}
        </span>
      ) : (
        <svg
          className={`${iconSizeClasses[size]} text-white`}
          fill="currentColor"
          viewBox="0 0 20 20"
          aria-label="User avatar"
        >
          <path
            fillRule="evenodd"
            d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
            clipRule="evenodd"
          />
        </svg>
      )}
    </div>
  )
}
