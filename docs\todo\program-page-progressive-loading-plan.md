# Program Page Progressive Loading Implementation Plan

## Overview

Implement progressive loading for the program page that loads immediately after login, showing user information and metrics progressively as data arrives from APIs.

## Requirements Summary

### User Experience Flow

1. **Immediate Display**: Page loads instantly with "Welcome back" (or existing name if cached)
2. **Progressive Updates**:
   - Welcome header updates to show user's name when UserInfo API responds
   - Three metrics (week streak, workouts completed, lbs lifted) start at 0 with shimmer effect
   - Each metric animates from 0 to actual value as data arrives (0.4s animation)
3. **Visual Effects**:
   - Moving gradient shimmer on numbers only (not entire cards)
   - Offset timing for visual interest
   - Smooth fade-out of shimmer when data arrives
   - Brief glow/highlight effect on transition
   - Individual celebration as each metric loads

### Technical Requirements

1. **API Calls**:
   - Start UserInfo API call during login success screen (300ms window)
   - Run in parallel with existing program data calls
   - Handle wrapped response format (`Result` property)
   - Stats come from `GetUserWorkoutLogAverageWithUserStatsV2` API
2. **Caching**:
   - Cache UserInfo for 1 week
   - Store in authStore with persistence
   - Always animate from 0 (never show cached values initially)
3. **Performance**:
   - 3 retry attempts with exponential backoff on failure
   - 10-second timeout for API calls
   - Log performance metrics (time to first byte, time to complete)
   - Continue loading in background if user navigates away
4. **Mobile Features**:
   - Haptic feedback when numbers finish loading
   - Pull-to-refresh support
   - Screen reader announcements for loading states

## Architecture Design

### Data Flow

```
Login Success Screen (300ms)
├── Start UserInfo API call
├── Start Program API calls (existing)
└── Navigate to Program Page
    ├── Show immediate UI (Welcome back + 0 values with shimmer)
    ├── Update name when UserInfo arrives
    └── Animate metrics as data arrives
```

### State Management

- Enhance `useUserInfo` hook with caching and progressive loading
- Store cached UserInfo in authStore with 1-week TTL
- Add performance tracking utilities

### Component Updates

1. **WelcomeHeader**: Already handles progressive name display correctly
2. **AnimatedCounter**: Add shimmer effect support
3. **ProgramStatsGrid**: Update to handle loading states with shimmer

## Implementation Steps

### Phase 1: Foundation (Cache & Store Setup)

1. Enhance authStore with UserInfo caching
2. Add cache TTL management (1 week)
3. Create performance tracking utilities
4. Add retry logic with exponential backoff

### Phase 2: API Integration

1. Modify login flow to start UserInfo call during success screen
2. Update useUserInfo hook with cache support
3. Handle API timeout (10 seconds)
4. Implement background loading continuation

### Phase 3: UI Components

1. Add shimmer effect to AnimatedCounter
2. Implement moving gradient animation
3. Add offset timing for multiple shimmers
4. Create smooth fade-out transition

### Phase 4: Visual Polish

1. Add glow/highlight effect on data arrival
2. Implement individual metric celebrations
3. Add haptic feedback for mobile
4. Ensure proper thousand separators

### Phase 5: Mobile & Accessibility

1. Add pull-to-refresh integration
2. Implement screen reader announcements
3. Add loading state alt text
4. Test mobile performance

### Phase 6: Error Handling & Monitoring

1. Implement retry logic (3 attempts)
2. Handle null/undefined metrics (hide them)
3. Add performance logging
4. Clear corrupted cache data

## Code Generation Prompts

### Prompt 1: Cache and Store Foundation

```text
Enhance the authStore to support UserInfo caching with a 1-week TTL. Add these features:

1. Add cachedUserInfo state with timestamp
2. Implement setCachedUserInfo and getCachedUserInfo methods
3. Add isCacheStale check for 1-week TTL
4. Ensure persistence across sessions using zustand persist
5. Add cache version management for migrations
6. Include performance timestamp tracking

The cached data should include firstName, lastName, and any other user profile fields from the GetUserInfoPyramid API response.

Create comprehensive tests for cache operations including TTL expiration, persistence, and version migrations.
```

### Prompt 2: Performance Tracking Utilities

```text
Create a performance tracking utility for the program page that monitors:

1. Time from login to first UserInfo byte
2. Time to complete UserInfo load
3. Time for each metric to load
4. Overall page ready time

Include methods to:
- Start/stop timers
- Log metrics to console in development
- Send analytics events in production
- Track cache hit/miss rates
- Monitor API retry attempts

Add TypeScript types for all performance events and integrate with existing PerformanceMonitor patterns.
```

### Prompt 3: Retry Logic Implementation

```text
Implement a retry utility with exponential backoff for API calls:

1. Support 3 retry attempts by default
2. Exponential backoff: 1s, 2s, 4s
3. Add jitter to prevent thundering herd
4. Support timeout (10 seconds total)
5. Allow abort/cancellation
6. Log retry attempts for monitoring

Integrate with existing API client and specifically apply to UserInfo and Stats API calls. Include comprehensive error handling and tests.
```

### Prompt 4: Enhanced useUserInfo Hook

```text
Enhance the existing useUserInfo hook to support progressive loading:

1. Check cache first (but don't display cached values)
2. Start API call immediately
3. Return loading states for UI
4. Update authStore when data arrives
5. Handle wrapped API response format
6. Support background loading if component unmounts
7. Integrate retry logic
8. Track performance metrics

Maintain backward compatibility with existing usage. Add tests for all scenarios including cache hits, misses, and API failures.
```

### Prompt 5: Login Flow Integration

```text
Modify the login flow to start loading UserInfo during the success screen:

1. In useAuth hook, add UserInfo API call alongside program data prefetch
2. Start call immediately after successful login
3. Don't wait for completion (fire and forget)
4. Ensure 300ms success screen isn't extended
5. Handle errors gracefully without blocking navigation

The calls should run in parallel with proper error isolation. Update tests to verify the new behavior.
```

### Prompt 6: Shimmer Effect Component

```text
Create a ShimmerEffect component for the AnimatedCounter:

1. Moving gradient shine effect (light gray to white to light gray)
2. Smooth 1.5s animation loop
3. Supports width/height props
4. Fade out transition when hiding
5. Accessible with proper ARIA labels

Use CSS animations for performance. The gradient should move from left to right continuously. Include stories/tests for the component.
```

### Prompt 7: Enhanced AnimatedCounter

```text
Enhance AnimatedCounter component to support shimmer loading state:

1. Add showShimmer prop
2. Display shimmer overlay on the number (not whole component)
3. Smoothly fade out shimmer when transitioning to real number
4. Add brief glow effect during transition
5. Support offset prop for staggered animations
6. Maintain counting animation (0.4s with ease)

For large numbers (>10,000), ensure proper formatting with thousand separators during animation. Add haptic feedback on completion for mobile.
```

### Prompt 8: Program Stats Loading States

```text
Update ProgramStatsGrid to handle progressive loading:

1. Show all metric cards immediately with 0 values
2. Add shimmer to each metric's AnimatedCounter
3. Stagger shimmer animations (100ms offset)
4. Remove shimmer as each metric loads
5. Hide metrics that return null/undefined
6. Add individual celebration animation

Integrate with the stats data from GetUserWorkoutLogAverageWithUserStatsV2 API. Ensure smooth transitions and proper error handling.
```

### Prompt 9: Pull-to-Refresh Integration

```text
Add pull-to-refresh support for UserInfo and stats:

1. Use existing usePullToRefresh hook
2. Refresh UserInfo and stats data
3. Show loading states during refresh
4. Animate from current values (not 0) during refresh
5. Update cache with fresh data
6. Show success feedback

Ensure the refresh doesn't interfere with ongoing animations. Test on mobile devices with touch gestures.
```

### Prompt 10: Accessibility Enhancements

```text
Add accessibility features for progressive loading:

1. Screen reader announcements when data loads
2. Alt text for shimmer states: "Loading week streak"
3. Announce final values: "Week streak: 12 weeks"
4. Proper ARIA roles and labels
5. Focus management during loading
6. High contrast mode support

Test with screen readers and ensure all loading states are properly communicated. Follow WCAG 2.1 AA guidelines.
```

### Prompt 11: Error Handling and Recovery

```text
Implement comprehensive error handling:

1. Clear corrupted cache data automatically
2. Fall back to "Welcome back" on UserInfo failure
3. Keep metrics at 0 if stats fail
4. Show subtle error states (no intrusive messages)
5. Log errors for monitoring
6. Retry failed requests on pull-to-refresh

Handle edge cases like partial data, network timeouts, and malformed responses. Ensure the UI remains stable and usable.
```

### Prompt 12: Performance Monitoring Integration

```text
Add production performance monitoring:

1. Track key metrics:
   - Time to display name
   - Time to load each stat
   - Cache hit rates
   - API response times
   - Retry counts

2. Send analytics events for:
   - Page load started
   - UserInfo loaded
   - Each metric loaded
   - All data complete
   - Any errors

3. Development console logging
4. Production analytics integration

Use performance marks and measures API. Integrate with existing monitoring infrastructure.
```

### Prompt 13: Final Integration and Testing

```text
Complete the integration and add comprehensive tests:

1. Integration tests for the full flow
2. Test with network throttling
3. Verify mobile haptic feedback
4. Test screen reader flow
5. Verify performance metrics
6. Test error scenarios
7. Verify cache persistence

Add E2E tests for the complete user journey from login to fully loaded program page. Document any gotchas or edge cases discovered.
```

## Success Criteria

1. ✅ Program page loads instantly after login
2. ✅ Name updates progressively when API responds
3. ✅ Metrics animate from 0 with shimmer effects
4. ✅ Each metric celebrates individually on load
5. ✅ All animations complete within 0.4s
6. ✅ Retry logic handles network failures
7. ✅ 1-week cache reduces API calls
8. ✅ Performance metrics tracked in production
9. ✅ Mobile haptic feedback works
10. ✅ Accessibility features implemented

## Testing Strategy

1. **Unit Tests**: Each component and utility
2. **Integration Tests**: Full data flow
3. **Performance Tests**: Verify animation timing
4. **Mobile Tests**: Touch gestures and haptic
5. **Accessibility Tests**: Screen reader flow
6. **Error Tests**: Network failures and timeouts

## Rollout Plan

1. Implement core functionality (Phases 1-3)
2. Test in development with throttled network
3. Add polish and mobile features (Phases 4-5)
4. Deploy to staging for real device testing
5. Monitor performance metrics
6. Roll out to production
