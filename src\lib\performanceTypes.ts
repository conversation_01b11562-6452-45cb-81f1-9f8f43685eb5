// Type definitions for Performance API types not in standard TypeScript lib
export interface PerformanceEventTiming extends PerformanceEntry {
  processingStart: number
  startTime: number
}

export interface LayoutShift extends PerformanceEntry {
  hadRecentInput: boolean
  value: number
}

// Use type assertion approach instead of extending Window
export type WindowWithGtag = Window & {
  gtag?: (...args: unknown[]) => void
}

export interface PerformanceMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  timestamp: number
}

// Export performance targets for testing
export const PERFORMANCE_TARGETS = {
  FCP: 800, // First Contentful Paint
  LCP: 1200, // Largest Contentful Paint
  TTI: 1500, // Time to Interactive
  FID: 50, // First Input Delay
  CLS: 0.1, // Cumulative Layout Shift
  bundleSize: 150 * 1024, // 150KB in bytes
  transitionTime: 100, // 100ms transitions
  renderTime: 16, // 16ms for 60fps
}
