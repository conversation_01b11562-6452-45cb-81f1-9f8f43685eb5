import React from 'react'

interface FloatingCTAButtonProps {
  onClick: () => void
  label?: string
  ariaLabel?: string
  isLoading?: boolean
  disabled?: boolean
}

export function FloatingCTAButton({
  onClick,
  label = 'Open Workout',
  ariaLabel = 'Open Workout - Start your next workout session',
  isLoading = false,
  disabled = false,
}: FloatingCTAButtonProps) {
  return (
    <div
      data-testid="floating-cta-container"
      className="fixed bottom-6 left-0 right-0 z-50"
      role="navigation"
      aria-label="Primary actions"
    >
      <div className="max-w-lg mx-auto w-full px-4">
        <button
          onClick={onClick}
          disabled={disabled || isLoading}
          className={`w-full px-7 py-5 min-h-[62px] bg-gradient-to-r from-brand-primary to-brand-secondary text-text-inverse font-semibold text-base uppercase tracking-wider rounded-theme shadow-lg shadow-brand-primary/25 transition-all focus:outline-none focus:ring-2 focus:ring-brand-primary/50 focus:ring-offset-2 focus:ring-offset-bg-primary ${
            disabled || isLoading
              ? 'opacity-60 cursor-not-allowed'
              : 'hover:scale-[1.02] active:scale-[0.98] hover:shadow-xl hover:shadow-brand-primary/30'
          }`}
          aria-label={ariaLabel}
          data-testid="start-workout-button"
        >
          <div className="flex items-center justify-center space-x-2">
            {isLoading && (
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-text-inverse border-t-transparent" />
            )}
            <span>{isLoading ? 'Starting...' : label}</span>
          </div>
        </button>
      </div>
    </div>
  )
}
