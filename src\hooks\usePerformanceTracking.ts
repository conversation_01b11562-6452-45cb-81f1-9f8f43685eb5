import { useCallback } from 'react'
import {
  PerformanceMonitor,
  PerformanceMarks,
  PerformanceMetrics,
} from '@/utils/performance'

/**
 * Custom hook for performance tracking throughout the app
 * Provides easy-to-use methods for marking key events
 */
export function usePerformanceTracking() {
  const markLoginStart = useCallback(() => {
    PerformanceMonitor.mark(PerformanceMarks.LOGIN_START)
  }, [])

  const markLoginSuccess = useCallback(() => {
    PerformanceMonitor.mark(PerformanceMarks.LOGIN_SUCCESS)
  }, [])

  const markSuccessScreenStart = useCallback(() => {
    PerformanceMonitor.mark(PerformanceMarks.SUCCESS_SCREEN_START)
  }, [])

  const markSuccessScreenComplete = useCallback(() => {
    PerformanceMonitor.mark(PerformanceMarks.SUCCESS_SCREEN_COMPLETE)
  }, [])

  const markDataFetchStart = useCallback(() => {
    PerformanceMonitor.mark(PerformanceMarks.DATA_FETCH_START)
  }, [])

  const markDataFetchComplete = useCallback(() => {
    PerformanceMonitor.mark(PerformanceMarks.DATA_FETCH_COMPLETE)
  }, [])

  const markWorkoutPageInteractive = useCallback(() => {
    PerformanceMonitor.mark(PerformanceMarks.WORKOUT_PAGE_INTERACTIVE)
  }, [])

  const reportMetrics = useCallback((): PerformanceMetrics => {
    return PerformanceMonitor.reportKeyMetrics()
  }, [])

  return {
    markLoginStart,
    markLoginSuccess,
    markSuccessScreenStart,
    markSuccessScreenComplete,
    markDataFetchStart,
    markDataFetchComplete,
    markWorkoutPageInteractive,
    reportMetrics,
  }
}
