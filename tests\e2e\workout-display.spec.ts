/* eslint-disable no-console */
import { test, expect } from '@playwright/test'

test.describe('Workout Display', () => {
  test('should display workout for existing mobile app user', async ({
    page,
  }) => {
    // Test with real Dr. Muscle credentials if available
    const testEmail = process.env.TEST_EMAIL || '<EMAIL>'
    const testPassword = process.env.TEST_PASSWORD || 'Dr123456'

    // Go to login page
    await page.goto('/login')

    // Login
    await page.getByLabel('Email').fill(testEmail)
    await page.getByLabel('Password').fill(testPassword)
    await page.getByRole('button', { name: /login/i }).click()

    // Wait for navigation to workout page
    await page.waitForURL('/workout', { timeout: 10000 })

    // Check console logs for API response
    page.on('console', (msg) => {
      if (msg.type() === 'log' && msg.text().includes('Program info')) {
        console.log('Console log:', msg.text())
      }
    })

    // Wait for workout content or no workout message
    await Promise.race([
      page.waitForSelector('[data-testid="start-workout-button"]', {
        timeout: 10000,
      }),
      page.waitForSelector('text=No workout scheduled', { timeout: 10000 }),
    ])

    // Check if workout is displayed
    const hasWorkout = await page
      .locator('[data-testid="start-workout-button"]')
      .isVisible()
    const noWorkout = await page
      .locator('text=No workout scheduled')
      .isVisible()

    console.log('Workout display test results:', {
      hasWorkout,
      noWorkout,
      url: page.url(),
    })

    // Take screenshot for debugging
    await page.screenshot({ path: 'workout-display.png', fullPage: true })

    // If no workout, check for test API link
    if (noWorkout) {
      const testApiLink = page.locator('text=Test API Response')
      if (await testApiLink.isVisible()) {
        // Click test API link
        await testApiLink.click()
        await page.waitForLoadState('networkidle')

        // Click test button
        await page
          .getByRole('button', {
            name: /Test GetUserWorkoutProgramTimeZoneInfo/i,
          })
          .click()

        // Wait for result
        await page.waitForSelector('text=Result:', { timeout: 10000 })

        // Get API response
        const resultText = await page.locator('pre').textContent()
        console.log('API Response from test page:', resultText)
      }
    }

    // Assert that either workout is shown or proper message is displayed
    expect(hasWorkout || noWorkout).toBeTruthy()
  })

  test('should test API endpoint directly', async ({ page, request }) => {
    // Login first to get auth token
    await page.goto('/login')
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('Dr123456')
    await page.getByRole('button', { name: /login/i }).click()

    // Wait for auth to complete
    await page.waitForURL('/workout', { timeout: 10000 })

    // Get auth token from localStorage
    const authData = await page.evaluate(() => {
      const authStore = localStorage.getItem('drmuscle-auth')
      if (authStore) {
        const parsed = JSON.parse(authStore)
        return parsed.state?.token
      }
      return null
    })

    console.log('Auth token found:', !!authData)

    // Test API directly
    if (authData) {
      const apiUrl = 'https://drmuscle.azurewebsites.net'
      const timeZoneInfo = {
        TimeZoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
        Offset: new Date().getTimezoneOffset() / -60,
        IsDaylightSaving: false,
      }

      const response = await request.post(
        `${apiUrl}/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo`,
        {
          headers: {
            Authorization: `Bearer ${authData}`,
            'Content-Type': 'application/json',
          },
          data: timeZoneInfo,
        }
      )

      console.log('Direct API test status:', response.status())
      const responseData = await response.json()
      console.log('Direct API response:', JSON.stringify(responseData, null, 2))
    }
  })
})
