import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, act } from '@testing-library/react'
import { StatCard } from '../StatCard'

// Setup for tests
beforeEach(() => {
  vi.useFakeTimers()
})

afterEach(() => {
  vi.useRealTimers()
})

describe('StatCard', () => {
  function TestIcon() {
    return <svg data-testid="test-icon" />
  }

  it('renders with all required props', () => {
    render(<StatCard value={100} label="Test Stat" icon={<TestIcon />} />)

    expect(
      screen.getByRole('article', { name: 'Test Stat statistic' })
    ).toBeInTheDocument()
    expect(screen.getByText('Test Stat')).toBeInTheDocument()
    expect(screen.getByTestId('test-icon')).toBeInTheDocument()

    // Wait for animation to complete
    act(() => {
      vi.advanceTimersByTime(500)
    })

    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent(
      '100'
    )
  })

  it('applies correct styling classes', () => {
    const { container } = render(
      <StatCard value={50} label="Workouts" icon={<TestIcon />} />
    )

    const card = container.firstChild
    expect(card).toHaveClass('bg-white')
    expect(card).toHaveClass('dark:bg-gray-800')
    expect(card).toHaveClass('border')
    expect(card).toHaveClass('rounded-lg')
    expect(card).toHaveClass('shadow-sm')
    expect(card).toHaveClass('p-6')
  })

  it('applies custom className', () => {
    const { container } = render(
      <StatCard
        value={25}
        label="Test"
        icon={<TestIcon />}
        className="custom-class"
      />
    )

    expect(container.firstChild).toHaveClass('custom-class')
  })

  it('passes loading state to AnimatedCounter', () => {
    render(
      <StatCard
        value={100}
        label="Loading Test"
        icon={<TestIcon />}
        isLoading
      />
    )

    expect(screen.getByTestId('shimmer-overlay')).toBeInTheDocument()
  })

  it('passes shimmer props to AnimatedCounter', () => {
    render(
      <StatCard
        value={0}
        label="Shimmer Test"
        icon={<TestIcon />}
        showShimmer
        shimmerOffset={100}
      />
    )

    expect(screen.getByTestId('shimmer-overlay')).toBeInTheDocument()
  })

  it('handles null/undefined values', () => {
    const { rerender } = render(
      <StatCard value={null} label="Null Test" icon={<TestIcon />} />
    )

    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent('0')

    rerender(
      <StatCard value={undefined} label="Undefined Test" icon={<TestIcon />} />
    )

    expect(screen.getByTestId('animated-counter-value')).toHaveTextContent('0')
  })

  it('has minimum height for consistent layout', () => {
    const { container } = render(
      <StatCard value={5} label="Short" icon={<TestIcon />} />
    )

    expect(container.firstChild).toHaveClass('min-h-[140px]')
  })

  it('centers content vertically and horizontally', () => {
    const { container } = render(
      <StatCard value={999} label="Centered" icon={<TestIcon />} />
    )

    expect(container.firstChild).toHaveClass('flex')
    expect(container.firstChild).toHaveClass('items-center')
    expect(container.firstChild).toHaveClass('justify-center')
  })
})
