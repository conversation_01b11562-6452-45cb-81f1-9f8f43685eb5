import { test, expect } from '@playwright/test'
import { login } from './helpers'

test.describe('WorkoutOverview Loading State Theme', () => {
  test.beforeEach(async ({ page }) => {
    // Login first
    await login(page)
  })

  test('should not show white background in WorkoutOverview loading state', async ({
    page,
  }) => {
    // Set up network delay to catch loading state
    await page.route('**/api/workout/today', async (route) => {
      // Add delay to ensure we see loading state
      await new Promise((resolve) => setTimeout(resolve, 1000))
      await route.continue()
    })

    // Navigate directly to workout page
    await page.goto('/workout')

    // Check loading state background immediately
    const loadingContainer = page.locator('[data-testid="workout-overview"]')

    // Wait for loading state to be visible
    await expect(loadingContainer).toBeVisible({ timeout: 1000 })

    // Check for any white backgrounds in the fixed bottom area during loading
    const fixedBottomArea = page.locator('.fixed.bottom-0')

    if ((await fixedBottomArea.count()) > 0) {
      // Get computed styles
      const bgColor = await fixedBottomArea.evaluate((el) => {
        return window.getComputedStyle(el).backgroundColor
      })

      // Should not be white
      expect(bgColor).not.toBe('rgb(255, 255, 255)')
      expect(bgColor).not.toBe('white')
      expect(bgColor).not.toBe('#ffffff')
      expect(bgColor).not.toBe('#fff')

      // Check border color too
      const borderColor = await fixedBottomArea.evaluate((el) => {
        return window.getComputedStyle(el).borderTopColor
      })

      // Should not be gray-200 (rgb(229, 231, 235))
      expect(borderColor).not.toBe('rgb(229, 231, 235)')
    }

    // Also check the page body background
    const bodyBgColor = await page.evaluate(() => {
      return window.getComputedStyle(document.body).backgroundColor
    })

    expect(bodyBgColor).not.toBe('rgb(255, 255, 255)')
    expect(bodyBgColor).not.toBe('white')
  })

  test('should use theme colors in loading state button', async ({ page }) => {
    // Set up network delay
    await page.route('**/api/workout/today', async (route) => {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      await route.continue()
    })

    // Navigate to workout
    await page.goto('/workout')

    // Check loading button styling
    const loadingButton = page.locator('button:has-text("Loading...")')

    if ((await loadingButton.count()) > 0) {
      await expect(loadingButton).toBeVisible()

      // Should have theme-appropriate disabled styling
      const buttonClasses = await loadingButton.getAttribute('class')
      expect(buttonClasses).not.toContain('bg-gray-400')
      expect(buttonClasses).toContain('bg-')
    }
  })

  test('should transition smoothly from loading to loaded state without white flash', async ({
    page,
  }) => {
    let whiteFlashDetected = false

    // Monitor for white flash
    await page.addInitScript(() => {
      const observer = new MutationObserver(() => {
        // Check all elements with fixed positioning
        const fixedElements = document.querySelectorAll('.fixed')
        fixedElements.forEach((el) => {
          const bgColor = window.getComputedStyle(el).backgroundColor
          if (
            bgColor === 'white' ||
            bgColor === 'rgb(255, 255, 255)' ||
            bgColor === '#ffffff' ||
            bgColor === '#fff'
          ) {
            ;(window as any).__whiteFlashInFixed = true
          }
        })
      })

      observer.observe(document.body, {
        subtree: true,
        childList: true,
        attributes: true,
        attributeFilter: ['class', 'style'],
      })
    })

    // Navigate to workout
    await page.goto('/workout')

    // Wait for workout to load
    await page.waitForSelector('[data-testid="exercise-card"]', {
      timeout: 10000,
    })

    // Check if white flash was detected
    whiteFlashDetected = await page.evaluate(() => {
      return (window as any).__whiteFlashInFixed || false
    })

    expect(whiteFlashDetected).toBe(false)
  })
})
