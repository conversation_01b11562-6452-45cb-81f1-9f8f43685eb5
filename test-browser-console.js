/**
 * Test script to run in browser console after logging in
 * Copy and paste this into the browser console on localhost:3000
 */

async function testExerciseRecommendationInBrowser() {
  console.log('🔍 Testing Exercise Recommendation API in browser...')
  
  try {
    // Step 1: Check if we have auth token
    console.log('🔐 Step 1: Checking auth status...')
    
    const tokenResponse = await fetch('/api/auth/token', {
      credentials: 'include',
    })
    
    if (!tokenResponse.ok) {
      console.error('❌ Failed to get auth token:', tokenResponse.status)
      return
    }
    
    const tokenData = await tokenResponse.json()
    console.log('✅ Auth token status:', tokenData)
    
    if (!tokenData.authenticated || !tokenData.token) {
      console.error('❌ Not authenticated or no token available')
      return
    }
    
    // Step 2: Test direct API call to Dr. Muscle API
    console.log('🎯 Step 2: Testing direct API call...')
    
    const requestBody = {
      Username: '<EMAIL>',
      ExerciseId: 12980, // Airborne Lunge
      WorkoutId: 14019, // Bodyweight 4 workout
      SetStyle: 'Normal',
      IsFlexibility: false,
      IsQuickMode: null,
      LightSessionDays: null,
      SwapedExId: null,
      IsStrengthPhashe: false, // Note: API has typo
      IsFreePlan: false,
      IsFirstWorkoutOfStrengthPhase: false,
      VersionNo: 1,
    }
    
    console.log('📤 Request body:', requestBody)
    
    const apiResponse = await fetch('https://drmuscle.azurewebsites.net/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${tokenData.token}`
      },
      body: JSON.stringify(requestBody)
    })
    
    console.log('📥 API Response status:', apiResponse.status)
    
    if (!apiResponse.ok) {
      const errorText = await apiResponse.text()
      console.error('❌ API call failed:', errorText)
      return
    }
    
    const recommendationData = await apiResponse.json()
    console.log('📥 API Response:', recommendationData)
    
    // Check if we got weight and reps
    const recommendation = recommendationData?.Result || recommendationData
    if (recommendation) {
      console.log('🏋️ Weight:', recommendation.Weight)
      console.log('🔢 Reps:', recommendation.Reps)
      console.log('📊 Sets:', recommendation.Series)
      
      if (recommendation.Weight && recommendation.Reps) {
        console.log('✅ SUCCESS: Got weight and reps from direct API call!')
      } else {
        console.log('❌ ISSUE: Missing weight or reps in response')
      }
    } else {
      console.log('❌ ISSUE: No recommendation data in response')
    }
    
    // Step 3: Test through web app's API service
    console.log('🌐 Step 3: Testing through web app API service...')
    
    // Import the web app's API function
    const { getExerciseRecommendation } = await import('/src/services/api/workout.ts')
    
    const webAppRequest = {
      Username: '<EMAIL>',
      ExerciseId: 12980,
      WorkoutId: 14019,
      SetStyle: 'Normal',
      IsFlexibility: false,
      IsQuickMode: null,
      LightSessionDays: null,
      SwapedExId: undefined,
      IsStrengthPhashe: false,
      IsFreePlan: false,
      IsFirstWorkoutOfStrengthPhase: false,
      VersionNo: 1,
    }
    
    console.log('📤 Web app request:', webAppRequest)
    
    const webAppResult = await getExerciseRecommendation(webAppRequest)
    console.log('📥 Web app result:', webAppResult)
    
    if (webAppResult?.Weight && webAppResult?.Reps) {
      console.log('✅ SUCCESS: Web app API also works!')
    } else {
      console.log('❌ ISSUE: Web app API failed or returned incomplete data')
    }
    
  } catch (error) {
    console.error('❌ Error during test:', error)
  }
}

// Run the test
console.log('🚀 Starting exercise recommendation test...')
console.log('Make sure you are logged in first!')
testExerciseRecommendationInBrowser()
