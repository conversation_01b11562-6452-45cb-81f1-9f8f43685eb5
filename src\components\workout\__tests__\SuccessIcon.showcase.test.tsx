import { render } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { SuccessIcon } from '../SuccessIcon'

describe('SuccessIcon Showcase', () => {
  it('should render successfully in different states', () => {
    // Default render
    const { container: defaultContainer } = render(<SuccessIcon />)
    expect(defaultContainer).toBeTruthy()

    // With custom size
    const { container: customSizeContainer } = render(
      <SuccessIcon size={200} />
    )
    expect(customSizeContainer).toBeTruthy()

    // With custom color
    const { container: customColorContainer } = render(
      <SuccessIcon color="#ff6b6b" />
    )
    expect(customColorContainer).toBeTruthy()

    // Without autoplay
    const { container: noAutoplayContainer } = render(
      <SuccessIcon autoPlay={false} />
    )
    expect(noAutoplayContainer).toBeTruthy()

    // With callback
    const { container: withCallbackContainer } = render(
      <SuccessIcon
        onAnimationComplete={() => {
          // Animation complete!
        }}
      />
    )
    expect(withCallbackContainer).toBeTruthy()
  })
})
