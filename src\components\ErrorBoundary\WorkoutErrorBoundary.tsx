import React, { type ErrorInfo } from 'react'
import { ErrorBoundary } from '@/components/ErrorBoundary'

interface WorkoutErrorFallbackProps {
  error: Error
  retry: () => void
}

function WorkoutErrorFallback({ error, retry }: WorkoutErrorFallbackProps) {
  const errorMessage = error?.message || ''
  const isNetworkError =
    error?.name === 'NetworkError' ||
    errorMessage.toLowerCase().includes('network') ||
    errorMessage.toLowerCase().includes('fetch')

  const isAPIError =
    errorMessage.toLowerCase().includes('api') ||
    errorMessage.toLowerCase().includes('401') ||
    errorMessage.toLowerCase().includes('403')

  const getErrorMessage = () => {
    if (isNetworkError) {
      return "We couldn't load your workout data. Please check your internet connection and try again."
    }
    if (isAPIError) {
      return 'There was a problem accessing your workout. Your session may have expired. Please try logging in again.'
    }
    return "Something went wrong while loading your workout. Don't worry, your progress is safe."
  }

  return (
    <div className="min-h-[100dvh] bg-gray-50 p-4">
      <div className="mx-auto max-w-lg">
        <div className="rounded-lg bg-white p-6 shadow-lg">
          {/* Error Icon */}
          <div className="mb-4 flex justify-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
              <svg
                className="h-8 w-8 text-red-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          </div>

          {/* Error Title */}
          <h2 className="mb-2 text-center text-xl font-bold text-gray-900">
            {isNetworkError ? 'Connection Problem' : 'Workout Error'}
          </h2>

          {/* Error Message */}
          <p className="mb-6 text-center text-gray-600">{getErrorMessage()}</p>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={retry}
              className="w-full rounded-lg bg-blue-600 px-6 py-3 font-semibold text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              data-testid="retry-workout-button"
            >
              Try Again
            </button>

            {isAPIError && (
              <button
                onClick={() => {
                  // Navigate to login
                  window.location.href = '/login'
                }}
                className="w-full rounded-lg border border-gray-300 bg-white px-6 py-3 font-semibold text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                data-testid="login-button"
              >
                Go to Login
              </button>
            )}

            <button
              onClick={() => {
                // Navigate to home
                window.location.href = '/'
              }}
              className="w-full rounded-lg border border-gray-300 bg-white px-6 py-3 font-semibold text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              data-testid="home-button"
            >
              Go to Home
            </button>
          </div>

          {/* Debug Info in Development */}
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-6">
              <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                Error Details
              </summary>
              <div className="mt-2 rounded bg-gray-100 p-3">
                <pre className="overflow-x-auto text-xs text-gray-700">
                  {error.stack || error.message}
                </pre>
              </div>
            </details>
          )}
        </div>

        {/* Helpful Tips */}
        <div className="mt-6 rounded-lg bg-blue-50 p-4">
          <h3 className="mb-2 font-semibold text-blue-900">
            Tips for Workout Issues:
          </h3>
          <ul className="space-y-1 text-sm text-blue-800">
            <li>• Make sure you have a stable internet connection</li>
            <li>• Try refreshing the page if the problem persists</li>
            <li>• Your workout progress is automatically saved</li>
            <li>• Contact support if you continue having issues</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

interface WorkoutErrorBoundaryProps {
  children: React.ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

export function WorkoutErrorBoundary({
  children,
  onError,
}: WorkoutErrorBoundaryProps) {
  const handleError = (error: Error, errorInfo: ErrorInfo) => {
    // Temporarily disabled to reduce console noise
    // Log to error monitoring service in production
    // if (process.env.NODE_ENV === 'production') {
    //   // TODO: Send to error monitoring service (e.g., Sentry)
    //   console.error('Workout Error:', {
    //     error: error.message,
    //     stack: error.stack,
    //     componentStack: errorInfo.componentStack,
    //     timestamp: new Date().toISOString(),
    //   })
    // }

    // Call parent error handler if provided
    onError?.(error, errorInfo)
  }

  return (
    <ErrorBoundary
      fallback={WorkoutErrorFallback}
      onError={handleError}
      context="workout"
    >
      {children}
    </ErrorBoundary>
  )
}
