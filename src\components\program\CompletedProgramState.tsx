'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import type { ProgramModel, ProgramStats } from '@/types'

export interface CompletedProgramStateProps {
  program: ProgramModel
  stats?: ProgramStats
}

export function CompletedProgramState({
  program,
  stats,
}: CompletedProgramStateProps) {
  const router = useRouter()

  return (
    <div className="min-h-[100dvh] flex flex-col items-center justify-center p-4 bg-gradient-to-b from-green-50 to-white dark:from-green-900/20 dark:to-gray-900">
      <div className="text-center space-y-6 max-w-md">
        {/* Trophy icon */}
        <div className="relative">
          <div className="absolute inset-0 animate-ping">
            <svg
              className="w-24 h-24 mx-auto text-yellow-400 opacity-20"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M7 2v5h10V2a1 1 0 011 1v5a7 7 0 01-7 7 7 7 0 01-7-7V3a1 1 0 011-1zm0 7a5 5 0 005 5 5 5 0 005-5H7zm-2 8v4a1 1 0 001 1h12a1 1 0 001-1v-4h-2v3H7v-3H5z" />
            </svg>
          </div>
          <svg
            className="w-24 h-24 mx-auto text-yellow-500 relative z-10"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M7 2v5h10V2a1 1 0 011 1v5a7 7 0 01-7 7 7 7 0 01-7-7V3a1 1 0 011-1zm0 7a5 5 0 005 5 5 5 0 005-5H7zm-2 8v4a1 1 0 001 1h12a1 1 0 001-1v-4h-2v3H7v-3H5z" />
          </svg>
        </div>

        {/* Completion message */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Congratulations!
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            You've completed{' '}
            <span className="font-semibold">{program.name}</span>
          </p>
        </div>

        {/* Achievement stats */}
        {stats && (
          <div className="grid grid-cols-2 gap-4 py-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
              <div className="text-2xl font-bold text-primary-600">
                {stats.totalWorkoutsCompleted}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Workouts Completed
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
              <div className="text-2xl font-bold text-primary-600">
                {stats.personalRecords}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Personal Records
              </div>
            </div>
          </div>
        )}

        {/* Program completed message */}
        <div className="bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <p className="text-green-800 dark:text-green-200 text-sm">
            Program Completed! You've successfully finished all workouts in this
            program. Great job on your dedication and consistency!
          </p>
        </div>

        {/* Action buttons */}
        <div className="space-y-3">
          <button
            onClick={() => router.push('/achievements')}
            className="w-full px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 active:bg-primary-800 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 font-semibold"
          >
            View Achievements
          </button>
          <button
            onClick={() => router.push('/programs')}
            className="w-full px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Browse New Programs
          </button>
          <button
            onClick={() => router.push('/workout')}
            className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 underline"
          >
            Continue with free workout
          </button>
        </div>
      </div>
    </div>
  )
}
