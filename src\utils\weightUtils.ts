/**
 * Weight formatting and calculation utilities
 */

import type { MultiUnityWeight } from '@/types'

/**
 * Format weight value with unit
 * @param weight - Weight object with Lb and Kg values
 * @param unit - Unit to display ('lbs' or 'kg')
 * @returns Formatted weight string
 */
export function formatWeight(
  weight: MultiUnityWeight,
  unit: 'lbs' | 'kg' = 'lbs'
): string {
  if (!weight) return '0 lbs'

  const value = unit === 'lbs' ? weight.Lb : weight.Kg
  const formattedValue = Number.isInteger(value) ? value : value.toFixed(1)

  return `${formattedValue} ${unit}`
}

/**
 * Calculate percentage change between two values
 * @param current - Current value
 * @param previous - Previous value
 * @returns Percentage change or null if previous is 0
 */
export function calculatePercentageChange(
  current: number,
  previous: number
): number | null {
  if (previous === 0) return null

  const change = ((current - previous) / previous) * 100
  return Math.round(change * 10) / 10 // Round to 1 decimal place
}

/**
 * Round weight to a reasonable precision to avoid floating-point display issues
 * @param weight - Weight value to round
 * @returns Rounded weight value
 */
export function roundWeight(weight: number): number {
  // Round to 2 decimal places to handle floating-point precision errors
  // while maintaining reasonable precision for fractional weights
  return Math.round(weight * 100) / 100
}
