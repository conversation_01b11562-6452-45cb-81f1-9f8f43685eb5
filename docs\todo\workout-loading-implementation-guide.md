The Dr. Muscle mobile app uses a sophisticated workout loading system with AI-powered recommendations. Here's everything you need to recreate it in your web app.

1. Data Models (TypeScript/JavaScript)
   typescript

// Core workout template model
interface WorkoutTemplateModel {
id: number;
userId: string;
label: string;
exercises: ExerciseModel[];
isSystemExercise: boolean;
workoutSettingsModel?: WorkoutTemplateSettingsModel;
}

// Exercise model
interface ExerciseModel {
id: number;
label: string;
isSystemExercise: boolean;
isSwapTarget: boolean;
isFinished: boolean;
bodyPartId?: number;
isUnilateral: boolean;
isTimeBased: boolean;
equipmentId?: number;
isEasy: boolean;
isMedium: boolean;
isBodyweight: boolean;
videoUrl?: string;
isWeighted: boolean;
isPyramid: boolean;
repsMaxValue?: number;
repsMinValue?: number;
timer?: number;
isNormalSets: boolean;
}

// Workout log series (individual set)
interface WorkoutLogSerieModel {
id: number;
exercise: ExerciseModel;
bodypartId?: number;
userId: string;
logDate: Date;
reps: number;
weight: MultiUnityWeight;
oneRM: MultiUnityWeight;
isWarmups: boolean;
isbodyweight: boolean;
rIR?: number; // Reps in Reserve
}

// Weight with unit conversion
interface MultiUnityWeight {
kg: number;
lb: number;
weightUnit: 'kg' | 'lb';
}

// Recommendation model
interface RecommendationModel {
series: number;
reps: number;
weight: MultiUnityWeight;
oneRMProgress: number;
recommendationInKg: number;
oneRMPercentage: number;
warmUpsList: WarmUp[];
nbPauses: number;
nbRepsPauses: number;
isDeload: boolean;
isBackOffSet: boolean;
backOffSetWeight?: MultiUnityWeight;
isMaxChallenge: boolean;
isLightSession: boolean;
lastLogDate?: Date;
firstWorkSetReps: number;
firstWorkSetWeight: MultiUnityWeight;
firstWorkSet1RM: MultiUnityWeight;
isPyramid: boolean;
isReversePyramid: boolean;
historySet: WorkoutLogSerieModel[];
minReps: number;
maxReps: number;
rIR?: number;
} 2. API Service Implementation
typescript

class WorkoutApiService {
private baseUrl = 'https://api.drmuscleapp.com'; // Replace with actual API URL

// Get user's workout program with timezone
async getUserWorkoutProgram(timezone: string): Promise<GetUserWorkoutLogAverageResponse> {
const response = await fetch(`${this.baseUrl}/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo`, {
method: 'POST',
headers: {
'Content-Type': 'application/json',
'Authorization': `Bearer ${this.getAuthToken()}`
},
body: JSON.stringify({ timeZone: timezone })
});
return response.json();
}

// Get specific workout template with exercises
async getCustomizedWorkout(workoutId: number): Promise<WorkoutTemplateModel> {
const response = await fetch(`${this.baseUrl}/api/Workout/GetUserCustomizedCurrentWorkout`, {
method: 'POST',
headers: {
'Content-Type': 'application/json',
'Authorization': `Bearer ${this.getAuthToken()}`
},
body: JSON.stringify(workoutId)
});
return response.json();
}

// Get recommendations for normal sets
async getNormalSetRecommendations(params: GetRecommendationForExerciseModel): Promise<RecommendationModel> {
const response = await fetch(`${this.baseUrl}/api/Exercise/GetRecommendationNormalRIRForExercise`, {
method: 'POST',
headers: {
'Content-Type': 'application/json',
'Authorization': `Bearer ${this.getAuthToken()}`
},
body: JSON.stringify(params)
});
return response.json();
}

// Get recommendations for rest-pause sets
async getRestPauseRecommendations(params: GetRecommendationForExerciseModel): Promise<RecommendationModel> {
const response = await fetch(`${this.baseUrl}/api/Exercise/GetRecommendationRestPauseRIRForExercise`, {
method: 'POST',
headers: {
'Content-Type': 'application/json',
'Authorization': `Bearer ${this.getAuthToken()}`
},
body: JSON.stringify(params)
});
return response.json();
}

// Save workout log
async saveWorkout(workoutData: SaveWorkoutModel): Promise<any> {
const response = await fetch(`${this.baseUrl}/api/Workout/SaveWorkoutV3Pro`, {
method: 'POST',
headers: {
'Content-Type': 'application/json',
'Authorization': `Bearer ${this.getAuthToken()}`
},
body: JSON.stringify(workoutData)
});
return response.json();
}
} 3. Workout Loading Flow Implementation
typescript

class WorkoutManager {
private apiService = new WorkoutApiService();
private currentWorkout: WorkoutTemplateModel | null = null;
private exerciseRecommendations: Map<number, RecommendationModel> = new Map();

// Step 1: Load user's workout program
async loadUserProgram(): Promise<void> {
try {
const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
const programData = await this.apiService.getUserWorkoutProgram(timezone);

      // Store program data
      this.storeProgramData(programData);

      // Extract next workout template
      if (programData.getUserProgramInfoResponseModel?.nextWorkoutTemplate) {
        const workoutId = programData.getUserProgramInfoResponseModel.nextWorkoutTemplate.id;
        await this.loadWorkoutTemplate(workoutId);
      }
    } catch (error) {
      console.error('Failed to load workout program:', error);
      // Fallback to cached data if offline
      this.loadCachedWorkout();
    }

}

// Step 2: Load specific workout template
async loadWorkoutTemplate(workoutId: number): Promise<void> {
try {
this.currentWorkout = await this.apiService.getCustomizedWorkout(workoutId);

      // Cache workout for offline use
      this.cacheWorkout(this.currentWorkout);

      // Load recommendations for each exercise
      await this.loadAllRecommendations();
    } catch (error) {
      console.error('Failed to load workout template:', error);
    }

}

// Step 3: Load recommendations for all exercises
async loadAllRecommendations(): Promise<void> {
if (!this.currentWorkout) return;

    const recommendationPromises = this.currentWorkout.exercises.map(async (exercise) => {
      const recommendation = await this.getRecommendationForExercise(exercise);
      if (recommendation) {
        this.exerciseRecommendations.set(exercise.id, recommendation);
      }
    });

    await Promise.all(recommendationPromises);

}

// Get recommendation based on set style
private async getRecommendationForExercise(exercise: ExerciseModel): Promise<RecommendationModel | null> {
const userSettings = this.getUserSettings();
const setStyle = userSettings.setStyle || 'Normal';

    const params: GetRecommendationForExerciseModel = {
      username: userSettings.email,
      exerciseId: exercise.id,
      workoutId: this.currentWorkout?.id,
      isQuickMode: userSettings.isQuickMode || false,
      lightSessionDays: this.calculateLightSessionDays(),
      swapedExId: null,
      isStrengthPhase: userSettings.isStrengthPhase || false,
      isFreePlan: userSettings.isFreePlan || false,
      isFirstWorkoutOfStrengthPhase: false,
      versionNo: 1
    };

    try {
      // Use normal sets for certain exercises
      if (setStyle === 'Normal' || exercise.id === 16508 || exercise.bodyPartId === 12) {
        return await this.apiService.getNormalSetRecommendations(params);
      } else {
        return await this.apiService.getRestPauseRecommendations(params);
      }
    } catch (error) {
      console.error('Failed to get recommendation:', error);
      return null;
    }

}

// Get complete workout data for display
getWorkoutData(): WorkoutDisplayData {
if (!this.currentWorkout) {
throw new Error('No workout loaded');
}

    return {
      workout: this.currentWorkout,
      exercises: this.currentWorkout.exercises.map(exercise => ({
        ...exercise,
        recommendation: this.exerciseRecommendations.get(exercise.id)
      }))
    };

}
} 4. Weight Conversion Utilities
typescript

class WeightConverter {
static kgToLb(kg: number): number {
return kg \* 2.20462;
}

static lbToKg(lb: number): number {
return lb / 2.20462;
}

static createMultiUnityWeight(value: number, unit: 'kg' | 'lb'): MultiUnityWeight {
return {
kg: unit === 'kg' ? value : this.lbToKg(value),
lb: unit === 'lb' ? value : this.kgToLb(value),
weightUnit: unit
};
}

static formatWeight(weight: MultiUnityWeight, targetUnit: 'kg' | 'lb'): string {
const value = targetUnit === 'kg' ? weight.kg : weight.lb;
return `${value.toFixed(2)} ${targetUnit}`;
}
} 5. Workout Execution Flow
typescript

class WorkoutExecutor {
private currentExerciseIndex = 0;
private completedSets: WorkoutLogSerieModel[] = [];

// Start workout
async startWorkout(workout: WorkoutTemplateModel): Promise<void> {
this.currentExerciseIndex = 0;
this.completedSets = [];

    // Navigate to first exercise
    await this.loadExercise(0);

}

// Load specific exercise
async loadExercise(index: number): Promise<ExerciseExecutionData> {
const exercise = this.getCurrentWorkout().exercises[index];
const recommendation = await this.getRecommendation(exercise.id);

    return {
      exercise,
      recommendation,
      warmupSets: recommendation?.warmUpsList || [],
      workingSets: this.generateWorkingSets(exercise, recommendation),
      restTime: this.calculateRestTime(exercise, recommendation)
    };

}

// Generate working sets based on recommendation
private generateWorkingSets(exercise: ExerciseModel, recommendation: RecommendationModel): WorkingSet[] {
const sets: WorkingSet[] = [];

    if (recommendation.isNormalSets) {
      // Normal sets (same weight/reps)
      for (let i = 0; i < recommendation.series; i++) {
        sets.push({
          setNumber: i + 1,
          targetReps: recommendation.reps,
          weight: recommendation.weight,
          isWarmup: false,
          rIR: recommendation.rIR
        });
      }
    } else if (recommendation.isPyramid) {
      // Pyramid sets (increasing weight, decreasing reps)
      // Implementation based on pyramid logic
    } else if (recommendation.isReversePyramid) {
      // Reverse pyramid (decreasing weight, increasing reps)
      // Implementation based on reverse pyramid logic
    }

    // Add back-off set if applicable
    if (recommendation.isBackOffSet && recommendation.backOffSetWeight) {
      sets.push({
        setNumber: sets.length + 1,
        targetReps: recommendation.reps + 2,
        weight: recommendation.backOffSetWeight,
        isWarmup: false,
        isBackOffSet: true
      });
    }

    return sets;

}

// Log completed set
async logSet(setData: CompletedSetData): Promise<void> {
const logEntry: WorkoutLogSerieModel = {
id: 0, // Will be assigned by server
exercise: setData.exercise,
userId: this.getUserId(),
logDate: new Date(),
reps: setData.actualReps,
weight: setData.weight,
oneRM: this.calculateOneRM(setData.actualReps, setData.weight),
isWarmups: setData.isWarmup,
isbodyweight: setData.exercise.isBodyweight,
rIR: setData.rIR
};

    this.completedSets.push(logEntry);

    // Save to server in real-time or batch
    if (this.shouldSaveImmediately()) {
      await this.saveSetToServer(logEntry);
    }

}

// Calculate 1RM using Epley formula
private calculateOneRM(reps: number, weight: MultiUnityWeight): MultiUnityWeight {
if (reps === 1) return weight;

    const oneRMkg = weight.kg * (1 + reps / 30);
    return WeightConverter.createMultiUnityWeight(oneRMkg, 'kg');

}

// Complete workout
async completeWorkout(): Promise<void> {
const workoutData = {
workoutId: this.getCurrentWorkout().id,
completedSets: this.completedSets,
duration: this.calculateDuration(),
completedDate: new Date()
};

    await this.apiService.saveWorkout(workoutData);

}
} 6. Local Storage/Caching Strategy
typescript

class WorkoutCache {
private readonly CACHE*KEYS = {
CURRENT_PROGRAM: 'workout_program',
WORKOUT_TEMPLATE: 'workout_template*',
RECOMMENDATIONS: 'exercise*recommendations*',
USER_SETTINGS: 'user_settings',
COMPLETED_SETS: 'completed_sets'
};

// Cache workout template
cacheWorkout(workout: WorkoutTemplateModel): void {
localStorage.setItem(
`${this.CACHE_KEYS.WORKOUT_TEMPLATE}${workout.id}`,
JSON.stringify(workout)
);
}

// Cache recommendations
cacheRecommendation(exerciseId: number, recommendation: RecommendationModel): void {
localStorage.setItem(
`${this.CACHE_KEYS.RECOMMENDATIONS}${exerciseId}`,
JSON.stringify(recommendation)
);
}

// Get cached workout
getCachedWorkout(workoutId: number): WorkoutTemplateModel | null {
const cached = localStorage.getItem(`${this.CACHE_KEYS.WORKOUT_TEMPLATE}${workoutId}`);
return cached ? JSON.parse(cached) : null;
}

// Store completed sets for offline sync
storeCompletedSets(sets: WorkoutLogSerieModel[]): void {
const existing = this.getCompletedSets();
const updated = [...existing, ...sets];
localStorage.setItem(this.CACHE_KEYS.COMPLETED_SETS, JSON.stringify(updated));
}

// Get pending sets for sync
getCompletedSets(): WorkoutLogSerieModel[] {
const cached = localStorage.getItem(this.CACHE_KEYS.COMPLETED_SETS);
return cached ? JSON.parse(cached) : [];
}

// Clear synced sets
clearSyncedSets(): void {
localStorage.removeItem(this.CACHE_KEYS.COMPLETED_SETS);
}
} 7. Complete Implementation Flow
typescript

// Main workout loading flow
async function loadWorkoutForUser(): Promise<void> {
const workoutManager = new WorkoutManager();

// 1. Load user program (includes next workout info)
await workoutManager.loadUserProgram();

// 2. Workout template and exercises are loaded automatically
const workoutData = workoutManager.getWorkoutData();

// 3. Display workout UI
displayWorkout(workoutData);

// 4. For each exercise, recommendations are already loaded
workoutData.exercises.forEach(exercise => {
if (exercise.recommendation) {
displayExerciseWithRecommendation(exercise);
}
});
}

// Exercise execution flow
async function executeExercise(exercise: ExerciseModel): Promise<void> {
const executor = new WorkoutExecutor();

// 1. Load exercise with recommendations
const exerciseData = await executor.loadExercise(exercise.index);

// 2. Display warmup sets
exerciseData.warmupSets.forEach(warmup => {
displayWarmupSet(warmup);
});

// 3. Display working sets
exerciseData.workingSets.forEach(set => {
displayWorkingSet(set);
});

// 4. Log each completed set
// User completes set -> call executor.logSet()

// 5. Move to next exercise or complete workout
}

8. Key Implementation Notes
   Authentication: All API calls require Bearer token authentication
   Timezone Handling: Always send user's timezone for accurate workout scheduling
   Offline Support: Cache all workout data locally for offline access
   Real-time Sync: Save completed sets immediately when online, batch when offline
   Unit Conversion: Always store weights in both kg and lb for instant conversion
   Progressive Overload: Recommendations include progressive weight/rep increases
   Set Styles: Support both normal sets and rest-pause sets based on user preference
   Exercise History: Load and display previous performance for each exercise
   This implementation provides the complete workflow for loading workouts in your web app, matching the mobile app's functionality.
