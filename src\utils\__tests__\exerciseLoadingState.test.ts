import { describe, it, expect, beforeEach, vi } from 'vitest'
import { ExerciseLoadingStateManager } from '../exerciseLoadingState'

describe('ExerciseLoadingStateManager', () => {
  let manager: ExerciseLoadingStateManager

  beforeEach(() => {
    manager = new ExerciseLoadingStateManager()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('State Transitions', () => {
    it('should start with no loading states', () => {
      expect(manager.isLoading(1)).toBe(false)
      expect(manager.getLoadingState(1)).toBeNull()
    })

    it('should transition to loading state', () => {
      manager.startLoading(1)

      const state = manager.getLoadingState(1)
      expect(state).not.toBeNull()
      expect(state?.isLoading).toBe(true)
      expect(state?.error).toBeNull()
      expect(state?.startTime).toBeLessThanOrEqual(Date.now())
    })

    it('should transition from loading to complete', () => {
      manager.startLoading(1)
      manager.completeLoading(1)

      const state = manager.getLoadingState(1)
      expect(state).not.toBeNull()
      expect(state?.isLoading).toBe(false)
      expect(state?.error).toBeNull()
      expect(state?.endTime).toBeDefined()
      expect(state?.endTime).toBeGreaterThanOrEqual(state?.startTime || 0)
    })

    it('should transition from loading to failed', () => {
      manager.startLoading(1)
      manager.failLoading(1, 'Network error')

      const state = manager.getLoadingState(1)
      expect(state).not.toBeNull()
      expect(state?.isLoading).toBe(false)
      expect(state?.error).toBe('Network error')
      expect(state?.endTime).toBeDefined()
    })

    it('should handle atomic state transitions', () => {
      // Start multiple exercises loading
      manager.startLoading(1)
      manager.startLoading(2)
      manager.startLoading(3)

      // Verify all are loading
      expect(manager.isLoading(1)).toBe(true)
      expect(manager.isLoading(2)).toBe(true)
      expect(manager.isLoading(3)).toBe(true)

      // Complete one
      manager.completeLoading(2)

      // Verify only one changed
      expect(manager.isLoading(1)).toBe(true)
      expect(manager.isLoading(2)).toBe(false)
      expect(manager.isLoading(3)).toBe(true)
    })
  })

  describe('Multiple Exercises', () => {
    it('should track multiple exercises simultaneously', () => {
      // Start loading multiple exercises
      manager.startLoading(1)
      manager.startLoading(2)
      manager.startLoading(3)

      // Verify all are tracked
      expect(manager.getAllLoadingExercises()).toHaveLength(3)
      expect(manager.getAllLoadingExercises()).toContain(1)
      expect(manager.getAllLoadingExercises()).toContain(2)
      expect(manager.getAllLoadingExercises()).toContain(3)

      // Complete some
      manager.completeLoading(1)
      manager.failLoading(2, 'Error')

      // Verify only one is still loading
      expect(manager.getAllLoadingExercises()).toHaveLength(1)
      expect(manager.getAllLoadingExercises()).toContain(3)
    })

    it('should handle rapid state changes', () => {
      // Rapid fire state changes
      manager.startLoading(1)
      manager.startLoading(1) // Duplicate start
      manager.completeLoading(1)
      manager.startLoading(1) // Restart

      const state = manager.getLoadingState(1)
      expect(state?.isLoading).toBe(true)
      expect(state?.error).toBeNull()
    })
  })

  describe('Error Information', () => {
    it('should include detailed error information', () => {
      manager.startLoading(1)

      const errorDetails = {
        message: 'Failed to fetch sets',
        code: 'NETWORK_ERROR',
        statusCode: 500,
      }

      manager.failLoading(1, errorDetails.message, errorDetails)

      const state = manager.getLoadingState(1)
      expect(state?.error).toBe(errorDetails.message)
      expect(state?.errorDetails).toEqual(errorDetails)
    })

    it('should clear error on successful retry', () => {
      manager.startLoading(1)
      manager.failLoading(1, 'First attempt failed')

      // Retry
      manager.startLoading(1)
      manager.completeLoading(1)

      const state = manager.getLoadingState(1)
      expect(state?.error).toBeNull()
      expect(state?.errorDetails).toBeUndefined()
    })
  })

  describe('Memory Management', () => {
    it('should clean up old completed states', () => {
      // Add some exercises
      for (let i = 1; i <= 10; i++) {
        manager.startLoading(i)
        manager.completeLoading(i)
      }

      // Verify all are tracked
      expect(manager.getTrackedCount()).toBe(10)

      // Advance time past cleanup threshold (5 minutes)
      vi.advanceTimersByTime(6 * 60 * 1000)

      // Trigger cleanup
      manager.cleanup()

      // Verify old states are removed
      expect(manager.getTrackedCount()).toBe(0)
    })

    it('should not clean up active loading states', () => {
      manager.startLoading(1)
      manager.startLoading(2)
      manager.completeLoading(2)

      // Advance time
      vi.advanceTimersByTime(6 * 60 * 1000)

      // Trigger cleanup
      manager.cleanup()

      // Active loading should remain
      expect(manager.getLoadingState(1)).not.toBeNull()
      expect(manager.isLoading(1)).toBe(true)

      // Completed should be removed
      expect(manager.getLoadingState(2)).toBeNull()
    })

    it('should not clean up recent failures', () => {
      manager.startLoading(1)
      manager.failLoading(1, 'Recent error')

      // Advance time but not past threshold
      vi.advanceTimersByTime(2 * 60 * 1000)

      // Trigger cleanup
      manager.cleanup()

      // Recent failure should remain
      expect(manager.getLoadingState(1)).not.toBeNull()
      expect(manager.getLoadingState(1)?.error).toBe('Recent error')
    })

    it('should automatically cleanup on operations', () => {
      // Create many old states
      for (let i = 1; i <= 100; i++) {
        manager.startLoading(i)
        manager.completeLoading(i)
      }

      // Advance time
      vi.advanceTimersByTime(6 * 60 * 1000)

      // New operation should trigger cleanup
      manager.startLoading(101)

      // Old states should be cleaned, new one should exist
      expect(manager.getTrackedCount()).toBeLessThan(100)
      expect(manager.isLoading(101)).toBe(true)
    })
  })

  describe('Helper Methods', () => {
    it('should correctly report loading status', () => {
      manager.startLoading(1)

      expect(manager.isLoading(1)).toBe(true)
      expect(manager.hasError(1)).toBe(false)
      expect(manager.isComplete(1)).toBe(false)
    })

    it('should correctly report error status', () => {
      manager.startLoading(1)
      manager.failLoading(1, 'Error')

      expect(manager.isLoading(1)).toBe(false)
      expect(manager.hasError(1)).toBe(true)
      expect(manager.isComplete(1)).toBe(false)
    })

    it('should correctly report complete status', () => {
      manager.startLoading(1)
      manager.completeLoading(1)

      expect(manager.isLoading(1)).toBe(false)
      expect(manager.hasError(1)).toBe(false)
      expect(manager.isComplete(1)).toBe(true)
    })

    it('should clear specific exercise state', () => {
      manager.startLoading(1)
      manager.startLoading(2)

      manager.clearState(1)

      expect(manager.getLoadingState(1)).toBeNull()
      expect(manager.getLoadingState(2)).not.toBeNull()
    })

    it('should clear all states', () => {
      manager.startLoading(1)
      manager.startLoading(2)
      manager.startLoading(3)

      manager.clearAll()

      expect(manager.getTrackedCount()).toBe(0)
      expect(manager.getAllLoadingExercises()).toHaveLength(0)
    })
  })
})
