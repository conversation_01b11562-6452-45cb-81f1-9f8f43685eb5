import { describe, it, expect, beforeEach, vi } from 'vitest'
import { RecommendationCache } from '../recommendationCache'
import type { RecommendationModel } from '@/types/api'

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {}
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value
    },
    removeItem: (key: string) => {
      delete store[key]
    },
    clear: () => {
      store = {}
    },
  }
})()

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('RecommendationCache', () => {
  let cache: RecommendationCache

  beforeEach(() => {
    localStorageMock.clear()
    cache = RecommendationCache.getInstance()
    cache.clear()
  })

  const createMockRecommendation = (): RecommendationModel => ({
    Weight: { Kg: 80, Lb: 176 },
    Reps: 8,
    Sets: 3,
    WarmUpSets: [{ Weight: { Kg: 40, Lb: 88 }, Reps: 10 }],
    IsDeload: false,
    FirstWorkSetWeight: { Kg: 80, Lb: 176 },
    FirstWorkSetReps: 8,
  })

  describe('generateKey', () => {
    it('should generate correct cache key', () => {
      const key = cache.generateKey('user123', 456, 789)
      expect(key).toBe('user123-456-789')
    })
  })

  describe('set and get', () => {
    it('should store and retrieve recommendation', () => {
      const key = cache.generateKey('user123', 456, 789)
      const recommendation = createMockRecommendation()

      cache.set(key, recommendation, 100)
      const retrieved = cache.get(key)

      expect(retrieved).toEqual(recommendation)
    })

    it('should return null for non-existent key', () => {
      const result = cache.get('non-existent-key')
      expect(result).toBeNull()
    })

    it('should handle storage errors gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      const setItemSpy = vi
        .spyOn(localStorageMock, 'setItem')
        .mockImplementation(() => {
          throw new Error('Storage error')
        })

      const key = cache.generateKey('user123', 456, 789)
      const recommendation = createMockRecommendation()

      // Should not throw
      expect(() => cache.set(key, recommendation, 100)).not.toThrow()

      setItemSpy.mockRestore()
      consoleSpy.mockRestore()
    })

    it('should handle quota exceeded error', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      const setItemSpy = vi
        .spyOn(localStorageMock, 'setItem')
        .mockImplementation(() => {
          const error = new DOMException('Quota exceeded')
          Object.defineProperty(error, 'name', { value: 'QuotaExceededError' })
          throw error
        })

      const key = cache.generateKey('user123', 456, 789)
      const recommendation = createMockRecommendation()

      // Should clear cache and retry
      expect(() => cache.set(key, recommendation, 100)).not.toThrow()

      setItemSpy.mockRestore()
      consoleSpy.mockRestore()
    })
  })

  describe('expiry', () => {
    it('should return null for expired entries', () => {
      const key = cache.generateKey('user123', 456, 789)
      const recommendation = createMockRecommendation()

      // Set with old timestamp
      const oldCache = {
        [key]: {
          key,
          data: recommendation,
          timestamp: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3 days ago
          workoutTemplateId: 100,
        },
      }
      localStorageMock.setItem(
        'dr-muscle-recommendation-cache',
        JSON.stringify(oldCache)
      )

      const result = cache.get(key)
      expect(result).toBeNull()
    })

    it('should return data for non-expired entries', () => {
      const key = cache.generateKey('user123', 456, 789)
      const recommendation = createMockRecommendation()

      // Set with recent timestamp
      const recentCache = {
        [key]: {
          key,
          data: recommendation,
          timestamp: Date.now() - 1 * 24 * 60 * 60 * 1000, // 1 day ago
          workoutTemplateId: 100,
        },
      }
      localStorageMock.setItem(
        'dr-muscle-recommendation-cache',
        JSON.stringify(recentCache)
      )

      const result = cache.get(key)
      expect(result).toEqual(recommendation)
    })
  })

  describe('clear', () => {
    it('should clear all cache entries', () => {
      const key1 = cache.generateKey('user123', 456, 789)
      const key2 = cache.generateKey('user123', 111, 222)
      const recommendation = createMockRecommendation()

      cache.set(key1, recommendation, 100)
      cache.set(key2, recommendation, 100)

      cache.clear()

      expect(cache.get(key1)).toBeNull()
      expect(cache.get(key2)).toBeNull()
    })
  })

  describe('clearByWorkout', () => {
    it('should clear only entries for specific workout', () => {
      const key1 = cache.generateKey('user123', 456, 789)
      const key2 = cache.generateKey('user123', 111, 789)
      const recommendation = createMockRecommendation()

      cache.set(key1, recommendation, 100)
      cache.set(key2, recommendation, 200)

      cache.clearByWorkout(100)

      expect(cache.get(key1)).toBeNull()
      expect(cache.get(key2)).toEqual(recommendation)
    })
  })

  describe('LRU eviction', () => {
    it('should evict oldest entries when cache is full', () => {
      const recommendation = createMockRecommendation()

      // Fill cache to max
      for (let i = 0; i < 100; i++) {
        const key = cache.generateKey('user123', i, 789)
        cache.set(key, recommendation, 100)
      }

      // Add one more (should trigger eviction)
      const newKey = cache.generateKey('user123', 999, 789)
      cache.set(newKey, recommendation, 100)

      // New key should exist
      expect(cache.get(newKey)).toEqual(recommendation)

      // Some old keys should be evicted
      const stats = cache.getStats()
      expect(stats.size).toBeLessThanOrEqual(100)
    })
  })

  describe('getStats', () => {
    it('should return correct statistics', () => {
      const recommendation = createMockRecommendation()

      cache.set(cache.generateKey('user123', 1, 789), recommendation, 100)
      cache.set(cache.generateKey('user123', 2, 789), recommendation, 100)

      const stats = cache.getStats()
      expect(stats.size).toBe(2)
      expect(stats.oldestEntry).toBeDefined()
      expect(stats.hitRate).toBe(0) // Not implemented yet
    })

    it('should handle empty cache', () => {
      const stats = cache.getStats()
      expect(stats.size).toBe(0)
      expect(stats.hitRate).toBe(0)
      expect(stats.oldestEntry).toBeNull()
    })
  })

  describe('isExpired', () => {
    it('should correctly identify expired entries', () => {
      const oldEntry = {
        key: 'test',
        data: createMockRecommendation(),
        timestamp: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3 days ago
        workoutTemplateId: 100,
      }

      expect(cache.isExpired(oldEntry)).toBe(true)
    })

    it('should correctly identify non-expired entries', () => {
      const recentEntry = {
        key: 'test',
        data: createMockRecommendation(),
        timestamp: Date.now() - 1 * 24 * 60 * 60 * 1000, // 1 day ago
        workoutTemplateId: 100,
      }

      expect(cache.isExpired(recentEntry)).toBe(false)
    })
  })
})
