import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseInfo } from '../ExerciseInfo'
import type { ExerciseModel, RecommendationModel } from '@/types'

describe('ExerciseInfo', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    Timer: 0,
    SetStyle: 'Normal',
    IsFlexibility: false,
  }

  const mockRecommendation: RecommendationModel = {
    Series: 3,
    Reps: 10,
    Weight: {
      Lb: 135,
      Kg: 61.2,
    },
    WarmupsCount: 2,
  }

  it('should display exercise name', () => {
    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        performancePercentage={null}
      />
    )

    expect(screen.getByText('Bench Press')).toBeInTheDocument()
  })

  it('should display recommended weight and reps for weighted exercises', () => {
    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={mockRecommendation}
        performancePercentage={null}
      />
    )

    expect(screen.getByText(/Target:/)).toBeInTheDocument()
    expect(screen.getByText(/10 reps × 135 lbs/)).toBeInTheDocument()
  })

  it('should display only reps for bodyweight exercises', () => {
    const bodyweightExercise = { ...mockExercise, IsBodyweight: true }
    render(
      <ExerciseInfo
        currentExercise={bodyweightExercise}
        recommendation={mockRecommendation}
        performancePercentage={null}
      />
    )

    expect(screen.getByText(/Target:/)).toBeInTheDocument()
    expect(screen.getByText(/10 reps/)).toBeInTheDocument()
    expect(screen.queryByText(/135 lbs/)).not.toBeInTheDocument()
  })

  it('should display duration for time-based exercises', () => {
    const timeBasedExercise = { ...mockExercise, IsTimeBased: true, Timer: 60 }
    render(
      <ExerciseInfo
        currentExercise={timeBasedExercise}
        recommendation={mockRecommendation}
        performancePercentage={null}
      />
    )

    expect(screen.getByText(/Target:/)).toBeInTheDocument()
    expect(screen.getByText(/60 seconds/)).toBeInTheDocument()
  })

  it('should display "No weight recommendation" when weight is missing or 0', () => {
    const recommendationNoWeight = {
      ...mockRecommendation,
      Weight: { Lb: 0, Kg: 0 },
    }
    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={recommendationNoWeight}
        performancePercentage={null}
      />
    )

    expect(screen.getByText(/Target:/)).toBeInTheDocument()
    expect(screen.getByText(/10 reps/)).toBeInTheDocument()
    expect(screen.getByText(/No weight recommendation/)).toBeInTheDocument()
  })

  it('should display performance percentage when provided', () => {
    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={mockRecommendation}
        performancePercentage={5.5}
      />
    )

    expect(screen.getByText('+5.5%')).toBeInTheDocument()
    expect(screen.getByText('+5.5%')).toHaveClass('text-green-600')
  })

  it('should display negative performance percentage in red', () => {
    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={mockRecommendation}
        performancePercentage={-3.2}
      />
    )

    expect(screen.getByText('-3.2%')).toBeInTheDocument()
    expect(screen.getByText('-3.2%')).toHaveClass('text-red-600')
  })
})
