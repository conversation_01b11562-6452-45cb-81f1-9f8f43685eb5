import React from 'react'
import { ShareIcon } from '@/components/icons'

export function ShareButton() {
  const handleShareClick = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Workout Complete!',
          text: 'I just completed my workout with Dr<PERSON> Muscle X!',
        })
      } catch (error) {
        // Share cancelled or failed
      }
    } else {
      // Fallback for browsers that don't support Web Share API
      // Could show a custom share modal or copy to clipboard
    }
  }

  return (
    <button
      onClick={handleShareClick}
      className="p-2 -mr-2 rounded-lg hover:bg-gray-100 transition-colors"
      aria-label="Share workout"
    >
      <ShareIcon size={24} className="text-gray-600" />
    </button>
  )
}
