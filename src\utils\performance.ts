/**
 * Performance monitoring utilities for Dr. Muscle X
 * Tracks key user journey metrics and API performance
 */
import { PerformanceMarks, type PerformanceMetrics } from './performanceMarks'
import { observeWebVitals } from './performanceWebVitals'
import { CachePerformanceTracker } from './performanceCacheTracker'

export { PerformanceMarks, observeWebVitals }
export type { PerformanceMetrics }

/**
 * Main performance monitoring class
 */
export class PerformanceMonitor {
  private static isSupported(): boolean {
    return (
      typeof performance !== 'undefined' &&
      typeof performance.mark === 'function' &&
      typeof performance.measure === 'function'
    )
  }

  /**
   * Mark a performance event
   */
  static mark(markName: string): void {
    if (!this.isSupported()) return

    try {
      performance.mark(markName)
    } catch (error) {
      // Silently fail if performance API is not available
    }
  }

  /**
   * Measure duration between two marks
   */
  static measure(
    measureName: string,
    startMark: string,
    endMark: string
  ): number | null {
    if (!this.isSupported()) return null

    try {
      performance.measure(measureName, startMark, endMark)
      const entries = performance.getEntriesByName(measureName)
      return entries.length > 0 ? entries[entries.length - 1]!.duration : null
    } catch (error) {
      return null
    }
  }

  /**
   * Track API call duration
   */
  static async trackApiCall<T>(
    apiName: string,
    apiCall: () => Promise<T>
  ): Promise<T> {
    const startMark = `api-${apiName}-start`
    const endMark = `api-${apiName}-end`

    this.mark(startMark)

    try {
      const result = await apiCall()
      this.mark(endMark)
      this.measure(`api-${apiName}`, startMark, endMark)

      // Performance tracking without console logging

      return result
    } catch (error) {
      this.mark(endMark)
      throw error
    }
  }

  /**
   * Clear all performance marks and measures
   */
  static clear(): void {
    if (!this.isSupported()) return

    try {
      performance.clearMarks()
      performance.clearMeasures()
    } catch (error) {
      // Silently fail
    }
  }

  /**
   * Get all performance entries
   */
  static getEntries(): PerformanceEntry[] {
    if (!this.isSupported()) return []

    try {
      return performance.getEntriesByType('measure')
    } catch (error) {
      return []
    }
  }

  /**
   * Log performance metrics (development only)
   */
  static logMetrics(): void {
    if (process.env.NODE_ENV === 'development') {
      // Performance metrics logged internally
    }
  }

  /**
   * Track cache performance metrics
   */
  static trackCacheMetrics(cacheStats: {
    hits: number
    misses: number
    hitRate: number
    averageLatency: number
    totalSize: number
    itemCount: number
  }): void {
    CachePerformanceTracker.trackCacheMetrics(cacheStats)
  }

  /**
   * Calculate and log key metrics
   */
  static reportKeyMetrics(): PerformanceMetrics {
    const metrics: PerformanceMetrics = {}

    // Login to interactive time
    const loginToInteractive = this.measure(
      'login-to-interactive',
      PerformanceMarks.LOGIN_START,
      PerformanceMarks.WORKOUT_PAGE_INTERACTIVE
    )
    if (loginToInteractive !== null) {
      metrics.loginToInteractive = loginToInteractive
    }

    // Success screen duration
    const successScreenDuration = this.measure(
      'success-screen-duration',
      PerformanceMarks.SUCCESS_SCREEN_START,
      PerformanceMarks.SUCCESS_SCREEN_COMPLETE
    )
    if (successScreenDuration !== null) {
      metrics.successScreenDuration = successScreenDuration
    }

    // Data fetch duration
    const dataFetchDuration = this.measure(
      'data-fetch-duration',
      PerformanceMarks.DATA_FETCH_START,
      PerformanceMarks.DATA_FETCH_COMPLETE
    )
    if (dataFetchDuration !== null) {
      metrics.dataFetchDuration = dataFetchDuration
    }

    // Collect API call metrics
    const apiEntries = this.getEntries().filter((entry) =>
      entry.name.startsWith('api-')
    )

    if (apiEntries.length > 0) {
      metrics.apiCalls = {}
      apiEntries.forEach((entry) => {
        const apiName = entry.name.replace('api-', '')
        metrics.apiCalls![apiName] = entry.duration
      })
    }

    // Retrieve cache metrics
    const cacheMetrics = CachePerformanceTracker.getCacheMetrics()
    if (cacheMetrics) {
      metrics.cacheMetrics = cacheMetrics
    }

    this.logMetrics()
    return metrics
  }
}

/**
 * Utility function to measure execution time
 */
export function measureDuration<T>(
  _name: string,
  fn: () => T
): { result: T; duration: number } {
  const start = performance.now()
  const result = fn()
  const duration = performance.now() - start

  // Performance duration tracked internally

  return { result, duration }
}

/**
 * Log all performance metrics (development only)
 */
export function logPerformanceMetrics(): void {
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  const entries = performance.getEntriesByType('measure')

  if (entries.length === 0) {
    // No entries to process
  }

  // Performance metrics available via getEntries()
}
