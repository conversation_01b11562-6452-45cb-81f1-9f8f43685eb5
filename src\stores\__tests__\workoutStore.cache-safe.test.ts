import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useWorkoutStore } from '../workoutStore'
import type {
  WorkoutTemplateGroupModel,
  WorkoutTemplateModel,
  GetUserProgramInfoResponseModel,
} from '@/types'

describe('WorkoutStore Cache-Safe Operations', () => {
  beforeEach(() => {
    vi.useFakeTimers()
    // Reset store before each test
    useWorkoutStore.setState({
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      hasHydrated: true,
    })
  })

  describe('Cache Persistence on API Errors', () => {
    it('should NOT clear cached workout data on API errors', () => {
      // Setup cached data
      const mockWorkout: WorkoutTemplateGroupModel = {
        Id: 1,
        Label: 'Test Program',
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Day 1',
            IsSystemExercise: true,
            UserId: 'user1',
            Exercises: [],
            WorkoutSettingsModel: {},
          },
        ],
        IsFeaturedProgram: false,
        UserId: 'user1',
        IsSystemExercise: true,
        RequiredWorkoutToLevelUp: 10,
        ProgramId: 1,
      }

      useWorkoutStore.getState().setCachedTodaysWorkout([mockWorkout])

      // Simulate API error
      useWorkoutStore.getState().setError('Network error')

      // Cache should still contain data
      const cachedData = useWorkoutStore.getState().getCachedTodaysWorkout()
      expect(cachedData).toEqual([mockWorkout])
    })

    it('should mark data as stale without removing it', () => {
      const mockProgramInfo: GetUserProgramInfoResponseModel = {
        RecommendedProgram: {
          Id: 1,
          Label: 'Test Program',
          Description: 'Test',
          IsSystemExercise: true,
          RemainingToLevelUp: 5,
        },
        NextWorkoutTemplate: {
          Id: 1,
          Label: 'Workout 1',
          IsSystemExercise: true,
          Exercises: [],
        },
        WorkoutTemplates: [],
      }

      // Set cached data
      useWorkoutStore.getState().setCachedUserProgramInfo(mockProgramInfo)
      const timestamp = Date.now()

      // Fast forward time past expiry
      vi.setSystemTime(timestamp + 25 * 60 * 60 * 1000) // 25 hours later

      // Check if stale
      const isStale = useWorkoutStore.getState().isCacheStale('userProgramInfo')
      expect(isStale).toBe(true)

      // But data should still be there
      const cachedData = useWorkoutStore.getState().getCachedUserProgramInfo()
      expect(cachedData).toEqual(mockProgramInfo)
    })
  })

  describe('User-Initiated Cache Clear', () => {
    it('should clear cache only when explicitly requested', () => {
      const mockWorkouts: WorkoutTemplateModel[] = [
        {
          Id: 1,
          Label: 'Workout 1',
          IsSystemExercise: true,
          UserId: 'user1',
          Exercises: [],
          WorkoutSettingsModel: {},
        },
      ]

      useWorkoutStore.getState().setCachedUserWorkouts(mockWorkouts)

      // Verify data is cached
      expect(useWorkoutStore.getState().getCachedUserWorkouts()).toEqual(
        mockWorkouts
      )

      // Clear cache explicitly
      useWorkoutStore.getState().clearCache()

      // Now cache should be empty
      expect(useWorkoutStore.getState().getCachedUserWorkouts()).toBeNull()
    })

    it('should not clear cache on resetWorkout', () => {
      const mockWorkout: WorkoutTemplateGroupModel[] = [
        {
          Id: 1,
          Label: 'Program',
          WorkoutTemplates: [],
          IsFeaturedProgram: false,
          UserId: 'user1',
          IsSystemExercise: true,
          RequiredWorkoutToLevelUp: 10,
          ProgramId: 1,
        },
      ]

      useWorkoutStore.getState().setCachedTodaysWorkout(mockWorkout)

      // Reset workout (but not cache)
      useWorkoutStore.getState().resetWorkout()

      // Cache should still have data
      expect(useWorkoutStore.getState().getCachedTodaysWorkout()).toEqual(
        mockWorkout
      )
    })
  })

  describe('Stale-While-Revalidate Pattern', () => {
    it('should return stale data immediately while fetching fresh', () => {
      const staleData: WorkoutTemplateModel[] = [
        {
          Id: 1,
          Label: 'Stale Workout',
          IsSystemExercise: true,
          UserId: 'user1',
          Exercises: [],
          WorkoutSettingsModel: {},
        },
      ]

      // Set stale data
      useWorkoutStore.getState().setCachedUserWorkouts(staleData)

      // Mark as stale by advancing time
      const timestamp = Date.now()
      vi.setSystemTime(timestamp + 25 * 60 * 60 * 1000)

      // Should still return stale data
      const cached = useWorkoutStore.getState().getCachedUserWorkouts()
      expect(cached).toEqual(staleData)

      // Should indicate it's stale
      const isStale = useWorkoutStore.getState().isCacheStale('userWorkouts')
      expect(isStale).toBe(true)
    })

    it('should track last successful fetch timestamp', () => {
      const mockData: GetUserProgramInfoResponseModel = {
        RecommendedProgram: {
          Id: 1,
          Label: 'Program',
          Description: 'Test',
          IsSystemExercise: true,
          RemainingToLevelUp: 5,
        },
        NextWorkoutTemplate: {
          Id: 1,
          Label: 'Workout',
          IsSystemExercise: true,
          Exercises: [],
        },
        WorkoutTemplates: [],
      }

      const beforeTimestamp = Date.now()
      useWorkoutStore.getState().setCachedUserProgramInfo(mockData)
      const afterTimestamp = Date.now()

      const lastUpdated =
        useWorkoutStore.getState().cachedData.lastUpdated.userProgramInfo
      expect(lastUpdated).toBeGreaterThanOrEqual(beforeTimestamp)
      expect(lastUpdated).toBeLessThanOrEqual(afterTimestamp)
    })
  })

  describe('Partial Updates', () => {
    it('should update only changed data without overwriting other cache', () => {
      // Set initial cache
      const programInfo: GetUserProgramInfoResponseModel = {
        RecommendedProgram: {
          Id: 1,
          Label: 'Program',
          Description: 'Test',
          IsSystemExercise: true,
          RemainingToLevelUp: 5,
        },
        NextWorkoutTemplate: {
          Id: 1,
          Label: 'Workout',
          IsSystemExercise: true,
          Exercises: [],
        },
        WorkoutTemplates: [],
      }

      const workouts: WorkoutTemplateModel[] = [
        {
          Id: 1,
          Label: 'Workout 1',
          IsSystemExercise: true,
          UserId: 'user1',
          Exercises: [],
          WorkoutSettingsModel: {},
        },
      ]

      useWorkoutStore.getState().setCachedUserProgramInfo(programInfo)
      useWorkoutStore.getState().setCachedUserWorkouts(workouts)

      // Update only program info
      const updatedProgramInfo = {
        ...programInfo,
        RecommendedProgram: {
          ...programInfo.RecommendedProgram,
          RemainingToLevelUp: 3, // Changed
        },
      }

      useWorkoutStore.getState().setCachedUserProgramInfo(updatedProgramInfo)

      // Program info should be updated
      expect(useWorkoutStore.getState().getCachedUserProgramInfo()).toEqual(
        updatedProgramInfo
      )

      // Workouts should remain unchanged
      expect(useWorkoutStore.getState().getCachedUserWorkouts()).toEqual(
        workouts
      )
    })

    it('should handle null updates without clearing other data', () => {
      const workouts: WorkoutTemplateModel[] = [
        {
          Id: 1,
          Label: 'Workout',
          IsSystemExercise: true,
          UserId: 'user1',
          Exercises: [],
          WorkoutSettingsModel: {},
        },
      ]

      useWorkoutStore.getState().setCachedUserWorkouts(workouts)

      // Set program info to null
      useWorkoutStore.getState().setCachedUserProgramInfo(null)

      // Workouts should still be there
      expect(useWorkoutStore.getState().getCachedUserWorkouts()).toEqual(
        workouts
      )
    })
  })

  describe('Cache Timestamps', () => {
    it('should track individual timestamps for each cache type', () => {
      const mockWorkout: WorkoutTemplateGroupModel[] = [
        {
          Id: 1,
          Label: 'Program',
          WorkoutTemplates: [],
          IsFeaturedProgram: false,
          UserId: 'user1',
          IsSystemExercise: true,
          RequiredWorkoutToLevelUp: 10,
          ProgramId: 1,
        },
      ]

      // Set initial time
      const baseTime = 1000000
      vi.setSystemTime(baseTime)

      // Set workout cache
      useWorkoutStore.getState().setCachedTodaysWorkout(mockWorkout)
      const workoutTime = baseTime

      // Wait a bit
      vi.setSystemTime(baseTime + 1000)

      // Set program info cache
      useWorkoutStore.getState().setCachedUserProgramInfo({
        RecommendedProgram: {
          Id: 1,
          Label: 'Program',
          Description: 'Test',
          IsSystemExercise: true,
          RemainingToLevelUp: 5,
        },
        NextWorkoutTemplate: {
          Id: 1,
          Label: 'Workout',
          IsSystemExercise: true,
          Exercises: [],
        },
        WorkoutTemplates: [],
      })

      const timestamps = useWorkoutStore.getState().cachedData.lastUpdated
      expect(timestamps.todaysWorkout).toBe(workoutTime)
      expect(timestamps.userProgramInfo).toBe(baseTime + 1000)
      expect(timestamps.userProgramInfo).toBeGreaterThan(
        timestamps.todaysWorkout
      )
    })
  })
})
