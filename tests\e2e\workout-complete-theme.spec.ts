import { test, expect } from '@playwright/test'
import { loginUser, waitForPageReady } from './helpers'

test.describe('Workout Complete - Theme Integration', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await loginUser(page)
    await waitForPageReady(page, '/workout')
  })

  test('should apply theme colors to nice work screen', async ({ page }) => {
    // Navigate to workout complete page
    await page.goto('/workout/complete')
    await waitForPageReady(page, '/workout/complete')

    // Get computed styles from CSS variables
    const styles = await page.evaluate(() => {
      const root = document.documentElement
      const computedStyle = getComputedStyle(root)
      return {
        bgPrimary: computedStyle.getPropertyValue('--color-bg-primary').trim(),
        bgSecondary: computedStyle
          .getPropertyValue('--color-bg-secondary')
          .trim(),
        textPrimary: computedStyle
          .getPropertyValue('--color-text-primary')
          .trim(),
        textSecondary: computedStyle
          .getPropertyValue('--color-text-secondary')
          .trim(),
        brandPrimary: computedStyle
          .getPropertyValue('--color-brand-primary')
          .trim(),
        success: computedStyle.getPropertyValue('--color-success').trim(),
        error: computedStyle.getPropertyValue('--color-error').trim(),
        info: computedStyle.getPropertyValue('--color-info').trim(),
      }
    })

    // Check main container background
    const mainContainer = page
      .locator('[data-testid="workout-stats"]')
      .locator('..')
    await expect(mainContainer).toHaveCSS('background-color', styles.bgPrimary)

    // Check "Nice work!" title color
    const title = page.getByText('Nice work!')
    await expect(title).toHaveCSS('color', styles.textPrimary)

    // Check summary card background
    const summaryCard = page.getByText('Summary').locator('..')
    await expect(summaryCard).toHaveCSS('background-color', styles.bgSecondary)

    // Check secondary text (labels)
    const exercisesLabel = page.getByText('Exercises')
    await expect(exercisesLabel).toHaveCSS('color', styles.textSecondary)

    // Check button styling
    const backButton = page.getByRole('button', { name: /Back to Home/i })
    await expect(backButton).toHaveCSS('background-color', styles.brandPrimary)
  })

  test('should change colors when switching themes', async ({ page }) => {
    // Navigate to workout complete page
    await page.goto('/workout/complete')
    await waitForPageReady(page, '/workout/complete')

    // Get initial colors for subtle-depth theme
    const initialTitle = page.getByText('Nice work!')
    const initialTitleColor = await initialTitle.evaluate(
      (el) => getComputedStyle(el).color
    )

    // Switch to flat-bold theme
    await page.evaluate(() => {
      document.documentElement.setAttribute('data-theme', 'flat-bold')
    })

    // Wait for theme transition
    await page.waitForTimeout(100)

    // Get new colors
    const newTitleColor = await initialTitle.evaluate(
      (el) => getComputedStyle(el).color
    )

    // Colors should be different
    expect(newTitleColor).not.toBe(initialTitleColor)

    // Verify flat-bold specific colors
    const flatBoldStyles = await page.evaluate(() => {
      const root = document.documentElement
      const computedStyle = getComputedStyle(root)
      return {
        brandPrimary: computedStyle
          .getPropertyValue('--color-brand-primary')
          .trim(),
      }
    })

    // In flat-bold theme, brand primary should be green (#10b981)
    expect(flatBoldStyles.brandPrimary).toBe('#10b981')
  })

  test('should use semantic colors for success/error/info states', async ({
    page,
  }) => {
    // Navigate to workout complete page
    await page.goto('/workout/complete')
    await waitForPageReady(page, '/workout/complete')

    // Inject test data to show all semantic states
    await page.evaluate(() => {
      // This would normally come from the workout data
      // For testing, we'll manually add the elements
      const statsContainer = document.querySelector(
        '[data-testid="workout-stats"]'
      )
      if (statsContainer) {
        // Add success state (Personal Record)
        const prDiv = document.createElement('div')
        prDiv.className = 'bg-success/10 border border-success rounded-lg p-4'
        prDiv.innerHTML =
          '<p class="text-success font-semibold mb-2">New Personal Record!</p>'
        statsContainer.appendChild(prDiv)

        // Add info state (Offline)
        const offlineDiv = document.createElement('div')
        offlineDiv.className = 'bg-info/10 border border-info rounded-lg p-4'
        offlineDiv.innerHTML = '<p class="text-info">Offline mode</p>'
        statsContainer.appendChild(offlineDiv)

        // Add error state
        const errorDiv = document.createElement('div')
        errorDiv.className = 'bg-error/10 border border-error rounded-lg p-4'
        errorDiv.innerHTML = '<p class="text-error">Save failed</p>'
        statsContainer.appendChild(errorDiv)
      }
    })

    // Get semantic colors
    const semanticColors = await page.evaluate(() => {
      const root = document.documentElement
      const computedStyle = getComputedStyle(root)
      return {
        success: computedStyle.getPropertyValue('--color-success').trim(),
        error: computedStyle.getPropertyValue('--color-error').trim(),
        info: computedStyle.getPropertyValue('--color-info').trim(),
      }
    })

    // Verify success state uses theme success color
    const prText = page.getByText('New Personal Record!')
    await expect(prText).toHaveCSS('color', semanticColors.success)

    // Verify info state uses theme info color
    const offlineText = page.getByText('Offline mode')
    await expect(offlineText).toHaveCSS('color', semanticColors.info)

    // Verify error state uses theme error color
    const errorText = page.getByText('Save failed')
    await expect(errorText).toHaveCSS('color', semanticColors.error)
  })
})
