import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useProgramPrefetch } from '../useProgramPrefetch'
import * as programApi from '@/api/program'
import type {
  ProgramModel,
  ProgramProgress,
  ProgramStats,
} from '@/types/program'

// Mock the program API
vi.mock('@/api/program', () => ({
  getUserProgram: vi.fn(),
  getProgramProgress: vi.fn(),
  getProgramStats: vi.fn(),
}))

describe('useProgramPrefetch', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    vi.clearAllMocks()
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  describe('Initial state', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useProgramPrefetch(), { wrapper })

      expect(result.current.progress).toBe(0)
      expect(result.current.isComplete).toBe(false)
      expect(result.current.error).toBeNull()
      expect(result.current.status).toBe('Starting...')
    })
  })

  describe('Successful prefetch', () => {
    it('should fetch all program data in parallel', async () => {
      const mockProgram = {
        id: 1,
        name: 'Beginner Program',
        description: 'A great starting program',
        category: 'strength',
        totalDays: 84,
        currentDay: 12,
        workoutsCompleted: 4,
        startDate: '2024-01-01',
      }

      const mockProgress = {
        percentage: 14.3,
        daysCompleted: 12,
        totalWorkouts: 36,
        currentWeek: 2,
        workoutsThisWeek: 1,
        remainingWorkouts: 32,
      }

      const mockStats = {
        averageWorkoutTime: 45,
        totalVolume: 125000,
        personalRecords: 5,
        consecutiveWeeks: 2,
        lastWorkoutDate: '2024-01-12',
        totalWorkoutsCompleted: 4,
      }

      vi.mocked(programApi.getUserProgram).mockResolvedValue(mockProgram)
      vi.mocked(programApi.getProgramProgress).mockResolvedValue(mockProgress)
      vi.mocked(programApi.getProgramStats).mockResolvedValue(mockStats)

      const { result } = renderHook(() => useProgramPrefetch(), { wrapper })

      act(() => {
        result.current.startPrefetch()
      })

      // Should start with initial progress
      expect(result.current.progress).toBe(25)
      expect(result.current.status).toBe('Loading program information...')

      // Wait for all promises to resolve
      await waitFor(() => {
        expect(result.current.isComplete).toBe(true)
      })

      expect(result.current.progress).toBe(100)
      expect(result.current.status).toBe('Ready!')
      expect(result.current.error).toBeNull()

      // Verify all API calls were made
      expect(programApi.getUserProgram).toHaveBeenCalledTimes(1)
      expect(programApi.getProgramProgress).toHaveBeenCalledTimes(1)
      expect(programApi.getProgramStats).toHaveBeenCalledTimes(1)

      // Verify data was cached in React Query
      expect(queryClient.getQueryData(['program'])).toEqual(mockProgram)
      expect(queryClient.getQueryData(['programProgress'])).toEqual(
        mockProgress
      )
      expect(queryClient.getQueryData(['programStats'])).toEqual(mockStats)
    })

    it('should update progress as each API call completes', async () => {
      vi.mocked(programApi.getUserProgram).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => {
              resolve({} as ProgramModel)
            }, 10)
          })
      )
      vi.mocked(programApi.getProgramProgress).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => {
              resolve({} as ProgramProgress)
            }, 20)
          })
      )
      vi.mocked(programApi.getProgramStats).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => {
              resolve({} as ProgramStats)
            }, 30)
          })
      )

      const { result } = renderHook(() => useProgramPrefetch(), { wrapper })

      act(() => {
        result.current.startPrefetch()
      })

      // Initial state
      expect(result.current.progress).toBe(25)

      // After program loads
      await waitFor(() => {
        expect(result.current.progress).toBeGreaterThanOrEqual(50)
      })

      // After all complete
      await waitFor(() => {
        expect(result.current.progress).toBe(100)
        expect(result.current.isComplete).toBe(true)
      })
    })
  })

  describe('Error handling', () => {
    it('should handle partial failures gracefully', async () => {
      vi.mocked(programApi.getUserProgram).mockResolvedValue({} as ProgramModel)
      vi.mocked(programApi.getProgramProgress).mockRejectedValue(
        new Error('Failed to load progress')
      )
      vi.mocked(programApi.getProgramStats).mockResolvedValue(
        {} as ProgramStats
      )

      const { result } = renderHook(() => useProgramPrefetch(), { wrapper })

      act(() => {
        result.current.startPrefetch()
      })

      await waitFor(() => {
        expect(result.current.isComplete).toBe(true)
      })

      // Should still complete successfully with partial data
      expect(result.current.progress).toBe(100)
      expect(result.current.error).toBeNull()
    })

    it('should handle complete failure', async () => {
      vi.mocked(programApi.getUserProgram).mockRejectedValue(
        new Error('Network error')
      )
      vi.mocked(programApi.getProgramProgress).mockRejectedValue(
        new Error('Network error')
      )
      vi.mocked(programApi.getProgramStats).mockRejectedValue(
        new Error('Network error')
      )

      const { result } = renderHook(() => useProgramPrefetch(), { wrapper })

      act(() => {
        result.current.startPrefetch()
      })

      await waitFor(() => {
        expect(result.current.error).toBeTruthy()
      })

      expect(result.current.isComplete).toBe(false)
      expect(result.current.error).toBe('Failed to load program data')
      expect(result.current.status).toBe('Error loading program data')
    })
  })

  describe('Multiple calls', () => {
    it('should prevent concurrent prefetches', async () => {
      vi.mocked(programApi.getUserProgram).mockResolvedValue({} as ProgramModel)
      vi.mocked(programApi.getProgramProgress).mockResolvedValue(
        {} as ProgramProgress
      )
      vi.mocked(programApi.getProgramStats).mockResolvedValue(
        {} as ProgramStats
      )

      const { result } = renderHook(() => useProgramPrefetch(), { wrapper })

      // Start multiple prefetches
      act(() => {
        result.current.startPrefetch()
        result.current.startPrefetch()
        result.current.startPrefetch()
      })

      await waitFor(() => {
        expect(result.current.isComplete).toBe(true)
      })

      // Should only call APIs once
      expect(programApi.getUserProgram).toHaveBeenCalledTimes(1)
      expect(programApi.getProgramProgress).toHaveBeenCalledTimes(1)
      expect(programApi.getProgramStats).toHaveBeenCalledTimes(1)
    })
  })

  describe('Reset functionality', () => {
    it('should reset state correctly', async () => {
      vi.mocked(programApi.getUserProgram).mockResolvedValue({} as ProgramModel)
      vi.mocked(programApi.getProgramProgress).mockResolvedValue(
        {} as ProgramProgress
      )
      vi.mocked(programApi.getProgramStats).mockResolvedValue(
        {} as ProgramStats
      )

      const { result } = renderHook(() => useProgramPrefetch(), { wrapper })

      // Complete a prefetch
      act(() => {
        result.current.startPrefetch()
      })

      await waitFor(() => {
        expect(result.current.isComplete).toBe(true)
      })

      // Reset
      act(() => {
        result.current.reset()
      })

      expect(result.current.progress).toBe(0)
      expect(result.current.isComplete).toBe(false)
      expect(result.current.error).toBeNull()
      expect(result.current.status).toBe('Starting...')
    })
  })

  describe('Status messages', () => {
    it('should show appropriate status messages during loading', async () => {
      vi.mocked(programApi.getUserProgram).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => {
              resolve({} as ProgramModel)
            }, 50)
          })
      )
      vi.mocked(programApi.getProgramProgress).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => {
              resolve({} as ProgramProgress)
            }, 100)
          })
      )
      vi.mocked(programApi.getProgramStats).mockImplementation(
        () =>
          new Promise((resolve) => {
            setTimeout(() => {
              resolve({} as ProgramStats)
            }, 150)
          })
      )

      const { result } = renderHook(() => useProgramPrefetch(), { wrapper })

      act(() => {
        result.current.startPrefetch()
      })

      expect(result.current.status).toBe('Loading program information...')

      await waitFor(() => {
        expect(result.current.status).toBe('Ready!')
      })
    })
  })
})
