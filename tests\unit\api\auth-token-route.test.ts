import { describe, it, expect, vi, beforeEach } from 'vitest'
import { GET } from '@/app/api/auth/token/route'
import { authCookies } from '@/lib/cookies'

// Mock the cookies module
vi.mock('@/lib/cookies', () => ({
  authCookies: {
    getAuthToken: vi.fn(),
  },
}))

describe('GET /api/auth/token', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return the auth token when present in cookies', async () => {
    const mockToken = 'test-auth-token-12345'
    vi.mocked(authCookies.getAuthToken).mockResolvedValue(mockToken)

    const response = await GET()
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data).toEqual({
      token: mockToken,
      authenticated: true,
    })
  })

  it('should return authenticated false when no token present', async () => {
    vi.mocked(authCookies.getAuthToken).mockResolvedValue(undefined)

    const response = await GET()
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data).toEqual({
      token: null,
      authenticated: false,
    })
  })

  it('should handle errors gracefully', async () => {
    vi.mocked(authCookies.getAuthToken).mockRejectedValue(
      new Error('Cookie read error')
    )

    const response = await GET()
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data).toEqual({
      error: 'Failed to retrieve auth token',
    })
  })
})
