/**
 * User Stats API Client
 *
 * Handles API calls for fetching user statistics
 */

import { apiClient } from '../client'
import { UserStats } from '@/types/userStats'
import { logger } from '@/utils/logger'
import { retryApiCall, STATS_RETRY_OPTIONS } from '@/utils/apiRetry'
import { fetchUnifiedProgramData } from '../unifiedProgramData'
import { extractUserStats, type StatsApiResponse } from './parser'

/**
 * Call GetUserWorkoutProgramTimeZoneInfo API
 * This is the first call in the mobile app pattern
 */
async function callGetUserWorkoutProgramTimeZoneInfo(): Promise<void> {
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  const timeZonePayload = {
    standardName: timezone,
  }

  if (process.env.NODE_ENV === 'development') {
    logger.log(
      '[UserStats API] Call 1 - GetUserWorkoutProgramTimeZoneInfo with:',
      timeZonePayload
    )
  }

  try {
    const response = await retryApiCall(
      () =>
        apiClient.post(
          '/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
          timeZonePayload
        ),
      STATS_RETRY_OPTIONS
    )

    if (process.env.NODE_ENV === 'development') {
      logger.log('[UserStats API] Call 1 Response:', response.data)
    }
  } catch (error) {
    logger.warn(
      '[UserStats API] First call failed, continuing with second call:',
      error
    )
  }
}

/**
 * Call GetLogAverageWithSetsV2 API
 * This is the second call in the mobile app pattern
 */
async function callGetLogAverageWithSetsV2(): Promise<StatsApiResponse> {
  if (process.env.NODE_ENV === 'development') {
    logger.log(
      '[UserStats API] Call 2 - GetLogAverageWithSetsV2 with empty object'
    )
  }

  const response = await retryApiCall(
    () => apiClient.post('/api/WorkoutLog/GetLogAverageWithSetsV2', {}),
    STATS_RETRY_OPTIONS
  )

  const { data } = response

  // Handle wrapped response format
  const responseData = data.Result || data

  if (process.env.NODE_ENV === 'development') {
    logger.log(
      '[UserStats API] Call 2 Raw response keys:',
      Object.keys(responseData)
    )
    if (responseData.HistoryExerciseModel) {
      logger.log(
        '[UserStats API] HistoryExerciseModel found:',
        responseData.HistoryExerciseModel
      )
    }
  }

  return responseData
}

/**
 * Call GetUserWorkoutLogAverageWithUserStatsV2 API
 * This is the fallback API call if the mobile app pattern fails
 */
async function callGetUserWorkoutLogAverageWithUserStatsV2(): Promise<StatsApiResponse> {
  const timeZoneInfo = {
    TimeZoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
    Offset: new Date().getTimezoneOffset() / -60,
    IsDaylightSaving: false,
  }

  const response = await retryApiCall(
    () =>
      apiClient.post(
        '/api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2',
        timeZoneInfo
      ),
    STATS_RETRY_OPTIONS
  )

  return response.data.Result || response.data
}

/**
 * Extract stats from HistoryExerciseModel in API response
 */
function extractStatsFromHistoryExerciseModel(
  responseData: StatsApiResponse
): UserStats {
  const stats: UserStats = {
    weekStreak: 0,
    workoutsCompleted: 0,
    lbsLifted: 0,
  }

  if (responseData.HistoryExerciseModel) {
    const historyModel = Array.isArray(responseData.HistoryExerciseModel)
      ? responseData.HistoryExerciseModel[0]
      : responseData.HistoryExerciseModel

    if (historyModel) {
      // Get total workouts from HistoryExerciseModel.TotalWorkoutCompleted
      if (typeof historyModel.TotalWorkoutCompleted === 'number') {
        stats.workoutsCompleted = historyModel.TotalWorkoutCompleted
      }

      // Get total weight from HistoryExerciseModel.TotalWeight.Lb
      if (
        historyModel.TotalWeight &&
        typeof historyModel.TotalWeight.Lb === 'number'
      ) {
        stats.lbsLifted = Math.round(historyModel.TotalWeight.Lb)
      }

      // Get consecutive weeks if available in HistoryExerciseModel
      if (typeof historyModel.ConsecutiveWeeks === 'number') {
        stats.weekStreak = historyModel.ConsecutiveWeeks
      }
    }

    if (process.env.NODE_ENV === 'development') {
      logger.log('[UserStats API] Extracted from HistoryExerciseModel:', stats)
    }
  }

  return stats
}

/**
 * Fetch user statistics from the API
 * First tries to use the unified data if available, then falls back to dedicated API calls
 */
export async function fetchUserStats(): Promise<UserStats> {
  try {
    // First, try to get data from the unified program data manager
    logger.log('[UserStats API] Attempting to fetch from unified data...')

    try {
      const unifiedData = await fetchUnifiedProgramData()
      if (unifiedData) {
        logger.log('[UserStats API] Using unified data for stats')
        const stats = extractUserStats(unifiedData)

        // If we got valid stats, return them
        if (
          stats.workoutsCompleted > 0 ||
          stats.lbsLifted > 0 ||
          stats.weekStreak > 0
        ) {
          return stats
        }
      }
    } catch (unifiedError) {
      logger.warn('[UserStats API] Failed to get unified data:', unifiedError)
    }

    // Fall back to the mobile app pattern
    logger.log('[UserStats API] Falling back to mobile app pattern...')

    // First API call: GetUserWorkoutProgramTimeZoneInfo
    await callGetUserWorkoutProgramTimeZoneInfo()

    // Second API call: GetLogAverageWithSetsV2 with empty object
    const responseData = await callGetLogAverageWithSetsV2()

    // Primary extraction from HistoryExerciseModel (mobile app pattern)
    const stats = extractStatsFromHistoryExerciseModel(responseData)

    // If we didn't get complete data from HistoryExerciseModel, fall back to existing extraction logic
    if (
      stats.workoutsCompleted === 0 ||
      stats.lbsLifted === 0 ||
      stats.weekStreak === 0
    ) {
      const fallbackStats = extractUserStats(responseData)

      // Use fallback values only for missing data
      if (stats.workoutsCompleted === 0) {
        stats.workoutsCompleted = fallbackStats.workoutsCompleted
      }
      if (stats.lbsLifted === 0) {
        stats.lbsLifted = fallbackStats.lbsLifted
      }
      if (stats.weekStreak === 0) {
        stats.weekStreak = fallbackStats.weekStreak
      }

      if (process.env.NODE_ENV === 'development') {
        logger.log('[UserStats API] Final stats after fallback:', stats)
      }
    }

    return stats
  } catch (error) {
    logger.error('[UserStats API] Error fetching user stats:', error)

    // Fall back to original single API call if new pattern fails
    try {
      logger.log('[UserStats API] Falling back to original API call...')
      const responseData = await callGetUserWorkoutLogAverageWithUserStatsV2()
      return extractUserStats(responseData)
    } catch (fallbackError) {
      logger.error('[UserStats API] Fallback also failed:', fallbackError)
      throw error
    }
  }
}
