import { formatLargeNumber } from './formatLargeNumber'

export function formatCounterNumber(num: number, label?: string): string {
  // Check if this is a weight/volume metric that should use compact notation
  const isWeightMetric =
    label &&
    (label.toLowerCase().includes('lbs') ||
      label.toLowerCase().includes('kg') ||
      label.toLowerCase().includes('lifted') ||
      label.toLowerCase().includes('weight'))

  // Use compact notation for weight metrics over 10,000
  if (isWeightMetric && Math.abs(num) > 9999) {
    return formatLargeNumber(num)
  }

  // Use standard locale formatting for other numbers
  return num.toLocaleString()
}
