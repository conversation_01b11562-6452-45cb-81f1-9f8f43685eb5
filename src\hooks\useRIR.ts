import { useWorkoutStore } from '@/stores/workoutStore'
import type { ExerciseModel } from '@/types'

interface RIROption {
  label: string
  value: number
  color: string
}

export function useRIR() {
  const store = useWorkoutStore()
  const {
    currentWorkout,
    currentExerciseIndex,
    currentSetIndex,
    workoutSession,
  } = store

  // Get current exercise first
  const getCurrentExercise =
    store.getCurrentExercise ||
    (() => currentWorkout?.Exercises?.[currentExerciseIndex] || null)
  const currentExercise = getCurrentExercise()

  // Get methods from store if they exist
  const getExerciseProgress =
    store.getExerciseProgress ||
    (() => {
      // Default implementation
      return {
        totalSets: 4,
        completedSets: 0,
        isFirstWorkSet: currentSetIndex === 0,
        currentSetIsWarmup: false,
        hasRIR:
          workoutSession?.exerciseRIRStatus?.[currentExercise?.Id || 0] ||
          false,
      }
    })
  const updateSetRIR = store.updateSetRIR || (async () => {})

  // Get exercise progress
  const exerciseProgress = currentExercise ? getExerciseProgress() : null

  // Check if RIR should be shown for current set
  const shouldShowRIR = (() => {
    if (!currentExercise) return false

    // Don't show for time-based exercises
    if (currentExercise.IsTimeBased) return false

    // Use exercise progress if available
    if (exerciseProgress) {
      return exerciseProgress.isFirstWorkSet && !exerciseProgress.hasRIR
    }

    // Fallback logic
    const hasCompletedRIR =
      workoutSession?.exerciseRIRStatus?.[currentExercise.Id] || false
    if (hasCompletedRIR) return false

    return currentSetIndex === 0
  })()

  // RIR options with colors (fixed color order)
  const rirOptions: RIROption[] = [
    { label: 'Very hard (0 left)', value: 0, color: 'red' },
    { label: 'Could do 1-2 more', value: 2, color: 'orange' },
    { label: 'Could do 3-4 more', value: 4, color: 'yellow' },
    { label: 'Could do 5-6 more', value: 6, color: 'blue' },
    { label: 'Could do 7+ more', value: 8, color: 'green' },
  ]

  // Map RIR description to numeric value
  const getRIRValue = (description: string): number | undefined => {
    const option = rirOptions.find((opt) => opt.label === description)
    return option?.value
  }

  // Save RIR to workout store
  const saveRIR = async (): Promise<void> => {
    if (!currentExercise) {
      throw new Error('No current exercise')
    }

    // Use updateSetRIR if available
    if (updateSetRIR) {
      await updateSetRIR(currentExercise.Id)
    }
  }

  // Helper to check if should capture RIR for a specific exercise and set
  const shouldCaptureRIR = (
    exercise: ExerciseModel,
    setIndex: number
  ): boolean => {
    // Don't capture RIR for time-based exercises
    if (exercise.IsTimeBased) {
      return false
    }

    // Check if this is the first work set
    const isFirstWorkSet = setIndex === 0

    return isFirstWorkSet
  }

  // Map string RIR values to numbers
  const mapRIRValueToNumber = (rirValue: string): number => {
    switch (rirValue) {
      case '0':
        return 0
      case '1-2':
        return 1 // Conservative estimate
      case '3-4':
        return 3 // Conservative estimate
      case '5-6':
        return 5 // Conservative estimate
      case '7+':
        return 7
      default:
        return 0
    }
  }

  // Get exercise RIR status
  const getExerciseRIRStatus = (): {
    hasCompletedRIR: boolean
    currentExercise: ExerciseModel | null
  } => {
    if (!currentWorkout || !currentWorkout.Exercises) {
      return { hasCompletedRIR: false, currentExercise: null }
    }

    const exercise = currentWorkout.Exercises[currentExerciseIndex]
    if (!exercise) {
      return { hasCompletedRIR: false, currentExercise: null }
    }

    // Check if RIR has been captured for this exercise
    const hasCompletedRIR =
      workoutSession?.exerciseRIRStatus?.[exercise.Id] || false

    return {
      hasCompletedRIR,
      currentExercise: exercise,
    }
  }

  return {
    shouldShowRIR,
    currentExercise,
    rirOptions,
    getRIRValue,
    saveRIR,
    shouldCaptureRIR,
    mapRIRValueToNumber,
    getExerciseRIRStatus,
  }
}
