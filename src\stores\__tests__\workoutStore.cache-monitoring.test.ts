import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { act } from '@testing-library/react'
import { useWorkoutStore } from '../workoutStore'
import type {
  GetUserProgramInfoResponseModel,
  WorkoutTemplateModel,
  WorkoutTemplateGroupModel,
  RecommendationModel,
} from '@/types'

// Mock data
const mockUserProgramInfo: GetUserProgramInfoResponseModel = {
  UserId: 'test-user',
  WeeklyStatus: 'Week 1',
  ProgramLabel: 'Test Program',
  NbDaysInTheWeek: 5,
  NbNonTrainingDays: 2,
  MondayIsFirst: false,
  TimeLogged: '2024-01-01T10:00:00',
  NextWorkoutDayText: 'Today',
  IsInIntroWorkout: false,
  IsInFirstWeek: true,
  TodaysWorkoutId: '1234',
  TodaysWorkoutText: 'Push Day',
  RecommendedProgram: {
    Id: 1,
    Label: 'Beginner Program',
    RemainingToLevelUp: 10,
    IconUrl: 'https://example.com/icon.png',
  },
  NextWorkoutTemplate: {
    Id: 1,
    Label: 'Push Day',
    IsSystemExercise: false,
    Exercises: [
      {
        Id: 123,
        Label: 'Bench Press',
        Path: 'chest/benchpress',
        TargetWeight: { Mass: 100, MassUnit: 'lbs' },
        TargetReps: 8,
        IsWarmup: false,
        HasPastLogs: true,
      },
    ],
  },
  NextNonTrainingDay: '2024-01-03',
  MondayHere: '2024-01-01',
  NextIntensityTechnique: 'Standard',
  ServerTimeUtc: '2024-01-01T10:00:00Z',
  MaxWorkoutSets: 20,
  NbMediumSets: 5,
  NbChallenges: 3,
  WorkoutTemplates: [],
}

const mockWorkout: WorkoutTemplateModel = {
  Id: 1,
  UserId: 'test-user',
  Label: 'Push Day',
  Exercises: [
    {
      Id: 123,
      Label: 'Bench Press',
      Path: 'chest/benchpress',
      TargetWeight: { Mass: 100, MassUnit: 'lbs' },
      TargetReps: 8,
      IsWarmup: false,
      HasPastLogs: true,
    },
  ],
  IsSystemExercise: false,
  WorkoutSettingsModel: {
    Id: 1,
    Pause: 120,
    Equipment: '',
    ChildWorkoutTemplateId: null,
    SetsModel: null,
    WorkoutProgramId: 1,
    IsFirstSet: false,
    IsFail: false,
    NbRepsMinimalInc: null,
    AvgDuration: null,
    IsNotRealData: false,
  },
}

const mockWorkoutGroup: WorkoutTemplateGroupModel = {
  Id: 1,
  Label: 'Beginner Program',
  WorkoutTemplates: [mockWorkout],
  IsFeaturedProgram: false,
  UserId: '',
  IsSystemExercise: true,
  RequiredWorkoutToLevelUp: 10,
  ProgramId: 1,
}

const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 100, Kg: 45.36 },
  Increments: { Lb: 5, Kg: 2.5 },
  LastLogDate: '2024-01-01',
  LastReps: 8,
  LastWeight: { Lb: 95, Kg: 43.09 },
  LastSeries: 3,
  IsWaitingForSwap: false,
  RM: 105,
  RIR: 2,
  History: [],
  IsBodyweight: false,
  IsTimeBased: false,
  IsUnilateral: false,
  IsPlate: false,
  RecommendedCountdown: null,
  NegativeWeight: null,
  PartialWeight: null,
  IsNegative: false,
  IsPartial: false,
}

describe('Cache Performance Monitoring', () => {
  beforeEach(() => {
    // Reset store to initial state
    useWorkoutStore.setState({
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      hasHydrated: true,
      cacheStats: {
        hits: 0,
        misses: 0,
        operationCount: 0,
        totalLatency: 0,
        hydrationTime: 0,
      },
    })

    // Mock performance.now()
    vi.spyOn(performance, 'now').mockReturnValue(1000)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Cache Hit/Miss Tracking', () => {
    it('should track cache hits when data is found', () => {
      // Given: Cached data exists
      const store = useWorkoutStore.getState()
      store.setCachedUserProgramInfo(mockUserProgramInfo)

      // When: Cache is accessed
      const result = store.getCachedUserProgramInfo()

      // Then: Hit is recorded
      const stats = store.getCacheStats()
      expect(stats.hits).toBe(1)
      expect(stats.misses).toBe(0)
      expect(stats.hitRate).toBe(1) // 100% hit rate
      expect(result).toEqual(mockUserProgramInfo)
    })

    it('should track cache misses when data is not found', () => {
      // Given: No cached data
      const store = useWorkoutStore.getState()

      // When: Cache is accessed
      const result = store.getCachedUserProgramInfo()

      // Then: Miss is recorded
      const stats = store.getCacheStats()
      expect(stats.hits).toBe(0)
      expect(stats.misses).toBe(1)
      expect(stats.hitRate).toBe(0) // 0% hit rate
      expect(result).toBeNull()
    })

    it('should calculate hit rate correctly with mixed hits and misses', () => {
      // Given: Some cached data
      const store = useWorkoutStore.getState()
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedExerciseRecommendation(123, mockRecommendation)

      // When: Multiple cache accesses with hits and misses
      store.getCachedUserProgramInfo() // Hit
      store.getCachedUserWorkouts() // Miss
      store.getCachedExerciseRecommendation(123) // Hit
      store.getCachedExerciseRecommendation(456) // Miss

      // Then: Hit rate calculated correctly
      const stats = store.getCacheStats()
      expect(stats.hits).toBe(2)
      expect(stats.misses).toBe(2)
      expect(stats.hitRate).toBe(0.5) // 50% hit rate
    })

    it('should handle zero operations gracefully', () => {
      // Given: No cache operations
      const store = useWorkoutStore.getState()

      // When: Stats requested
      const stats = store.getCacheStats()

      // Then: No division by zero
      expect(stats.hits).toBe(0)
      expect(stats.misses).toBe(0)
      expect(stats.hitRate).toBe(0)
      expect(stats.operationCount).toBe(0)
    })
  })

  describe('Cache Operation Latency', () => {
    it('should not track cache write operations', async () => {
      // Given: Performance timer
      let timeCounter = 1000
      vi.spyOn(performance, 'now').mockImplementation(() => {
        timeCounter += 0.5 // Simulate 0.5ms operation
        return timeCounter
      })

      const store = useWorkoutStore.getState()

      // When: Cache write operation
      act(() => {
        store.setCachedUserProgramInfo(mockUserProgramInfo)
      })

      // Then: No latency recorded for writes
      const stats = store.getCacheStats()
      expect(stats.operationCount).toBe(0)
      expect(stats.averageLatency).toBe(0)
    })

    it('should measure cache read operation latency', () => {
      // Given: Cached data and timer
      const store = useWorkoutStore.getState()
      store.setCachedUserProgramInfo(mockUserProgramInfo)

      let timeCounter = 2000
      vi.spyOn(performance, 'now').mockImplementation(() => {
        timeCounter += 0.2 // Simulate 0.2ms read
        return timeCounter
      })

      // Reset stats to track only read
      store.resetCacheStats()

      // When: Cache read operation
      const result = store.getCachedUserProgramInfo()

      // Then: Read latency recorded
      const stats = store.getCacheStats()
      expect(stats.operationCount).toBe(1)
      expect(stats.averageLatency).toBeCloseTo(0.2, 2)
      expect(result).toEqual(mockUserProgramInfo)
    })

    it('should calculate average latency across multiple operations', () => {
      // Given: Multiple operations with different latencies
      const store = useWorkoutStore.getState()

      // Set up some cached data first
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedExerciseRecommendation(123, mockRecommendation)

      vi.spyOn(performance, 'now')
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1000.5) // 0.5ms
        .mockReturnValueOnce(1001)
        .mockReturnValueOnce(1001.3) // 0.3ms
        .mockReturnValueOnce(1001.5)
        .mockReturnValueOnce(1002.2) // 0.7ms

      // When: Multiple cache READ operations
      store.getCachedUserProgramInfo() // 0.5ms
      store.getCachedExerciseRecommendation(123) // 0.3ms
      store.getCachedExerciseRecommendation(456) // 0.7ms (miss)

      // Then: Average latency calculated
      const stats = store.getCacheStats()
      expect(stats.operationCount).toBe(3)
      expect(stats.averageLatency).toBeCloseTo(0.5, 2) // (0.5 + 0.3 + 0.7) / 3
    })

    it('should track operations under 1ms threshold', () => {
      // Given: Fast operations
      const store = useWorkoutStore.getState()

      // Set up cached data first
      store.setCachedUserProgramInfo(mockUserProgramInfo)

      vi.spyOn(performance, 'now')
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1000.1) // 0.1ms

      // When: Fast cache READ operation
      store.getCachedUserProgramInfo()

      // Then: Performance threshold met
      const stats = store.getCacheStats()
      expect(stats.averageLatency).toBeLessThan(1)
      expect(stats.operationCount).toBe(1)
    })
  })

  describe('Cache Size Monitoring', () => {
    it('should track total cache size in bytes', () => {
      // Given: Various cached data
      const store = useWorkoutStore.getState()

      // When: Data is cached
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedUserWorkouts([mockWorkout])
      store.setCachedTodaysWorkout([mockWorkoutGroup])

      // Then: Size calculated correctly
      const stats = store.getCacheStats()
      expect(stats.totalSize).toBeGreaterThan(0)
      expect(stats.totalSize).toBeLessThan(500 * 1024) // Under 500KB limit
    })

    it('should count cached items correctly', () => {
      // Given: Multiple cached items
      const store = useWorkoutStore.getState()

      // When: Various data cached
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedUserWorkouts([mockWorkout])
      store.setCachedExerciseRecommendation(123, mockRecommendation)
      store.setCachedExerciseRecommendation(456, mockRecommendation)

      // Then: Item count accurate
      const stats = store.getCacheStats()
      expect(stats.itemCount).toBe(4) // programInfo + workouts + 2 recommendations
    })

    it('should identify oldest cached data', () => {
      // Given: Data cached at different times
      const store = useWorkoutStore.getState()
      const now = Date.now()

      // Cache data with different timestamps
      vi.setSystemTime(new Date(now - 2 * 60 * 60 * 1000)) // 2 hours ago
      store.setCachedUserProgramInfo(mockUserProgramInfo)

      vi.setSystemTime(new Date(now - 1 * 60 * 60 * 1000)) // 1 hour ago
      store.setCachedUserWorkouts([mockWorkout])

      vi.setSystemTime(new Date(now)) // Now
      store.setCachedExerciseRecommendation(123, mockRecommendation)

      // When: Stats requested
      const stats = store.getCacheStats()

      // Then: Oldest timestamp identified
      expect(stats.oldestDataAge).toBeGreaterThan(2 * 60 * 60 * 1000 - 1000) // ~2 hours
    })

    it('should count fresh vs stale data', () => {
      // Given: Mix of fresh and stale data
      const store = useWorkoutStore.getState()
      const now = Date.now()

      // Fresh data
      vi.setSystemTime(new Date(now))
      store.setCachedUserProgramInfo(mockUserProgramInfo)

      // Stale data (>24 hours old)
      vi.setSystemTime(new Date(now - 25 * 60 * 60 * 1000))
      store.setCachedUserWorkouts([mockWorkout])

      // When: Stats requested
      vi.setSystemTime(new Date(now))
      const stats = store.getCacheStats()

      // Then: Fresh/stale count accurate
      expect(stats.freshDataCount).toBe(1)
      expect(stats.staleDataCount).toBe(1)
    })
  })

  describe('Cache Debugging Utilities', () => {
    it('should provide comprehensive cache statistics', () => {
      // Given: Populated cache
      const store = useWorkoutStore.getState()
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedUserWorkouts([mockWorkout])
      store.setCachedExerciseRecommendation(123, mockRecommendation)

      // When: Debug stats requested
      const stats = store.getCacheStats()

      // Then: All stats provided
      expect(stats).toMatchObject({
        hits: expect.any(Number),
        misses: expect.any(Number),
        hitRate: expect.any(Number),
        operationCount: expect.any(Number),
        averageLatency: expect.any(Number),
        totalSize: expect.any(Number),
        itemCount: expect.any(Number),
        oldestDataAge: expect.any(Number),
        freshDataCount: expect.any(Number),
        staleDataCount: expect.any(Number),
      })
    })

    it('should log cache contents in development mode', () => {
      // Given: Development environment
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'development'
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation()

      const store = useWorkoutStore.getState()
      store.setCachedUserProgramInfo(mockUserProgramInfo)

      // When: Debug log requested
      store.logCacheContents()

      // Then: Cache logged to console
      expect(consoleSpy).toHaveBeenCalledWith(
        'Cache Stats:',
        expect.any(Object)
      )
      expect(consoleSpy).toHaveBeenCalledWith(
        'Cache Contents:',
        expect.any(Object)
      )

      // Cleanup
      consoleSpy.mockRestore()
      process.env.NODE_ENV = originalEnv
    })

    it('should not log cache contents in production mode', () => {
      // Given: Production environment
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation()

      const store = useWorkoutStore.getState()

      // When: Debug log requested
      store.logCacheContents()

      // Then: Nothing logged
      expect(consoleSpy).not.toHaveBeenCalled()

      // Cleanup
      consoleSpy.mockRestore()
      process.env.NODE_ENV = originalEnv
    })

    it('should provide cache clear utility', () => {
      // Given: Populated cache
      const store = useWorkoutStore.getState()
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedUserWorkouts([mockWorkout])
      store.setCachedExerciseRecommendation(123, mockRecommendation)

      // When: Cache cleared
      store.clearAllCache()

      // Then: All cache data removed
      expect(store.getCachedUserProgramInfo()).toBeNull()
      expect(store.getCachedUserWorkouts()).toBeNull()
      expect(store.getCachedExerciseRecommendation(123)).toBeUndefined()

      const stats = store.getCacheStats()
      expect(stats.itemCount).toBe(0)
      expect(stats.totalSize).toBeLessThan(250) // Just empty structure
    })

    it('should reset cache statistics', () => {
      // Given: Cache with history
      const store = useWorkoutStore.getState()
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.getCachedUserProgramInfo() // Generate hit
      store.getCachedUserWorkouts() // Generate miss

      // When: Stats reset
      store.resetCacheStats()

      // Then: Statistics cleared
      const stats = store.getCacheStats()
      expect(stats.hits).toBe(0)
      expect(stats.misses).toBe(0)
      expect(stats.operationCount).toBe(0)
      expect(stats.totalLatency).toBe(0)
    })
  })

  describe('Cache Hydration Monitoring', () => {
    it('should track hydration time', async () => {
      // Given: Store not hydrated
      useWorkoutStore.setState({ hasHydrated: false })

      let timeCounter = 1000
      vi.spyOn(performance, 'now').mockImplementation(() => {
        timeCounter += 10 // Simulate 10ms hydration
        return timeCounter
      })

      const store = useWorkoutStore.getState()

      // When: Hydration occurs
      act(() => {
        store.setHasHydrated(true)
      })

      // Then: Hydration time recorded
      const stats = store.getCacheStats()
      expect(stats.hydrationTime).toBeGreaterThan(0)
    })
  })

  describe('Cache Health Monitoring', () => {
    it('should detect cache effectiveness', () => {
      // Given: Poor cache performance
      const store = useWorkoutStore.getState()

      // Generate many misses
      for (let i = 0; i < 10; i++) {
        store.getCachedExerciseRecommendation(i)
      }

      // Generate few hits
      store.setCachedExerciseRecommendation(1, mockRecommendation)
      store.getCachedExerciseRecommendation(1)

      // When: Health checked
      const stats = store.getCacheStats()
      const health = store.getCacheHealth()

      // Then: Poor health detected
      expect(stats.hitRate).toBeLessThan(0.2) // <20% hit rate
      expect(health.isHealthy).toBe(false)
      expect(health.warnings).toContain('Low cache hit rate')
    })

    it('should warn about large cache size', () => {
      // Given: Large cached data
      const store = useWorkoutStore.getState()

      // Create large data object
      const largeData = {
        ...mockUserProgramInfo,
        LargeArray: new Array(1000).fill({
          data: 'x'.repeat(100),
        }),
      }

      store.setCachedUserProgramInfo(largeData as any)

      // When: Health checked
      const health = store.getCacheHealth()

      // Then: Size warning issued
      const stats = store.getCacheStats()
      if (stats.totalSize > 200 * 1024) {
        expect(health.warnings).toContain(
          'Cache size exceeds recommended limit'
        )
      }
    })

    it('should detect stale cache dominance', () => {
      // Given: Mostly stale data
      const store = useWorkoutStore.getState()
      const now = Date.now()

      // Add old data
      vi.setSystemTime(new Date(now - 25 * 60 * 60 * 1000))
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setCachedUserWorkouts([mockWorkout])
      store.setCachedTodaysWorkout([mockWorkoutGroup])

      // When: Health checked
      vi.setSystemTime(new Date(now))
      const health = store.getCacheHealth()

      // Then: Staleness warning
      expect(health.warnings).toContain('Majority of cache is stale')
    })
  })
})
