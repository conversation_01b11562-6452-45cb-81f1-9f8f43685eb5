export interface ErrorState {
  type: 'network' | 'auth' | 'validation' | 'unknown'
  message: string
  error: Error
  canRetry: boolean
  recovery?: string[]
  context?: unknown
  preservedData?: unknown
}

interface ErrorCategorizerOptions {
  isOffline: boolean
  workoutContext?: unknown
}

/**
 * Categorize errors into types with appropriate messages and recovery options
 */
export function categorizeError(
  error: Error,
  options: ErrorCategorizerOptions
): ErrorState {
  const { isOffline, workoutContext } = options
  let type: ErrorState['type'] = 'unknown'
  let message = 'An unexpected error occurred. Please try again.'
  let canRetry = true
  let addRecovery = false
  const recovery: string[] = []

  // Network errors
  if (
    error.name === 'NetworkError' ||
    error.message.includes('fetch') ||
    error.message.includes('ERR_NETWORK') ||
    !navigator.onLine
  ) {
    type = 'network'
    message = isOffline
      ? 'You are offline. Changes will sync when connection is restored.'
      : 'Connection issue. Please check your internet.'
    // Only add recovery for specific error messages that indicate we want recovery suggestions
    if (error.message.includes('ERR_NETWORK')) {
      recovery.push('Check your internet connection')
      recovery.push('Wait a moment and try again')
      addRecovery = true
    }
  }
  // Authentication errors
  else if (
    error.name === 'AuthenticationError' ||
    error.message.includes('Unauthorized') ||
    error.message.includes('401')
  ) {
    type = 'auth'
    message = 'Session expired. Please log in again.'
    canRetry = false
  }
  // Validation errors
  else if (
    error.name === 'ValidationError' ||
    error.message.includes('Invalid') ||
    error.message.includes('400')
  ) {
    type = 'validation'
    message = error.message
    canRetry = false
  }
  // Storage errors
  else if (error.message.includes('QuotaExceededError')) {
    type = 'unknown'
    message = 'Storage space is full.'
    recovery.push('Clear some space')
    recovery.push('Delete old workout data')
    addRecovery = true
  }

  // Build the base state with required properties
  const baseState: ErrorState = {
    type,
    message,
    error,
    canRetry,
  }

  // Add recovery only when explicitly needed
  if (addRecovery || workoutContext) {
    // Add workout-specific recovery options
    if (workoutContext) {
      recovery.push('Continue workout')
      recovery.push('Save locally')
      addRecovery = true
    }

    // Only add recovery array if it has items and we explicitly want to add it
    if (recovery.length > 0 && addRecovery) {
      baseState.recovery = recovery
    }
  }

  // Add context/preservedData for workout context scenarios
  if (workoutContext) {
    baseState.context = workoutContext
    baseState.preservedData = workoutContext
  }

  return baseState
}
