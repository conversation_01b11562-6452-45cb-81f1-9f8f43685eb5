/**
 * Type definitions for UserInfo performance tracking
 */

export const UserInfoPerformanceMarks = {
  // Login flow
  LOGIN_SUCCESS: 'userinfo-login-success',
  USERINFO_FETCH_START: 'userinfo-fetch-start',
  USERINF<PERSON>_FETCH_END: 'userinfo-fetch-end',
  USERINFO_FIRST_BYTE: 'userinfo-first-byte',

  // Stats loading
  STATS_FETCH_START: 'stats-fetch-start',
  STATS_FETCH_END: 'stats-fetch-end',
  METRIC_STREAK_LOADED: 'metric-streak-loaded',
  METRIC_WORKOUTS_LOADED: 'metric-workouts-loaded',
  METRIC_VOLUME_LOADED: 'metric-volume-loaded',

  // Cache operations
  USERINFO_CACHE_CHECK: 'userinfo-cache-check',
  USERINFO_CACHE_HIT: 'userinfo-cache-hit',
  USERINFO_CACHE_MISS: 'userinfo-cache-miss',
  USERINFO_CACHE_WRITE: 'userinfo-cache-write',

  // Page ready states
  PROGRAM_PAGE_MOUNTED: 'program-page-mounted',
  PROGRAM_PAGE_READY: 'program-page-ready',
  ALL_METRICS_LOADED: 'all-metrics-loaded',
} as const

export interface UserInfoPerformanceEvent {
  type: 'mark' | 'measure' | 'metric'
  name: string
  value?: number
  metadata?: Record<string, unknown>
}

export interface UserInfoPerformanceMetrics {
  // Time measurements
  loginToFirstByte: number | null
  userInfoLoadTime: number | null
  statsLoadTime: number | null
  overallPageReady: number | null

  // Individual metric load times
  metricLoadTimes: {
    streak: number | null
    workouts: number | null
    volume: number | null
  }

  // Cache performance
  cacheMetrics: {
    hitRate: number
    hits: number
    misses: number
    writeLatency: number | null
    readLatency: number | null
  }

  // API retry tracking
  retryMetrics: {
    userInfoRetries: number
    statsRetries: number
    totalRetries: number
    lastRetryReason?: string
  }

  // Analytics data
  analytics: {
    timestamp: number
    sessionId: string
    userId?: string
  }
}

export interface UserInfoSession {
  id: string
  startTime: number
  endTime?: number
  success?: boolean
  error?: string
}
