import type {
  WorkoutTemplateGroupModel,
  WorkoutTemplateModel,
  ExerciseModel,
  RecommendationModel,
  WorkoutSession,
  GetUserWorkoutLogAverageResponse,
  ExerciseWorkSetsModel,
  WorkoutLogSerieModel,
} from '@/types'
import type { ExerciseLoadingStateManager } from '@/utils/exerciseLoadingState'

export interface MockUseWorkoutReturn {
  // Data
  todaysWorkout: WorkoutTemplateGroupModel[] | null
  userProgramInfo: GetUserWorkoutLogAverageResponse | null | undefined
  currentWorkout: WorkoutTemplateModel | null
  currentExercise: ExerciseModel | null
  nextExercise: ExerciseModel | null
  currentSetIndex: number
  totalSets: number
  isLastSet: boolean
  isLastExercise: boolean
  isWarmupSet: boolean
  workoutSession: WorkoutSession | null

  // New hierarchical data structure with progressive loading
  exercises?: ExerciseWorkSetsModel[]
  currentExerciseWorkSets?: ExerciseWorkSetsModel | null
  exerciseLoadingStateManager?: ExerciseLoadingStateManager

  // Loading states
  isLoadingWorkout: boolean
  isLoading: boolean
  loadingStates: {
    programInfo: boolean
    userWorkout: boolean
    recommendation: boolean
  }

  // Errors
  workoutError: Error | null
  error: string | Error | null

  // Recommendation
  recommendation: RecommendationModel | null
  refetchRecommendation: () => void

  // Actions
  saveSet: (setData: WorkoutLogSerieModel) => Promise<void>
  startWorkout: () => void
  finishWorkout: () => Promise<void>
  nextSet: () => void
  restoreWorkout: (data: WorkoutSession) => void
  goToNextExercise: () => void
  getRecommendation: (exerciseId: number) => Promise<RecommendationModel | null>
  getRestDuration: () => number

  // Status
  isOffline: boolean

  // Progressive loading support
  expectedExerciseCount?: number

  // Optimistic loading states
  hasInitialData?: boolean
  isLoadingFresh?: boolean

  // Refresh function
  refreshWorkout?: () => Promise<void>

  // Helper functions for new structure
  getExerciseWorkSets?: (exerciseId: number) => ExerciseWorkSetsModel | null
  updateExerciseWorkSets?: (
    exerciseId: number,
    sets: WorkoutLogSerieModel[]
  ) => ExerciseWorkSetsModel | null

  // Legacy (for backward compatibility)
  setCurrentWorkout?: () => void
  completeWorkout?: () => void
  updateSetLog?: () => void
  addSetLog?: () => void
  updateExerciseRIR?: () => void
  getNextExercise?: () => void
}
