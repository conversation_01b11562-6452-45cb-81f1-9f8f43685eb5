import React, { useEffect, useState } from 'react'

export interface ProgressRingProps {
  /** Progress percentage (0-100) */
  progress: number
  /** Size of the ring */
  size?: 'sm' | 'md' | 'lg' | number
  /** Width of the stroke */
  strokeWidth?: number
  /** Custom color class for the progress stroke */
  color?: string
  /** Whether to show percentage text in center */
  showPercentage?: boolean
  /** Custom content to display in center (overrides percentage) */
  children?: React.ReactNode
  /** Additional CSS classes */
  className?: string
}

const SIZE_CLASSES = {
  sm: 'w-24 h-24',
  md: 'w-32 h-32',
  lg: 'w-40 h-40',
}

const SIZE_VALUES = {
  sm: 96,
  md: 128,
  lg: 160,
}

export function ProgressRing({
  progress: progressProp,
  size = 'md',
  strokeWidth = 6,
  color = 'text-primary',
  showPercentage = true,
  children,
  className,
}: ProgressRingProps) {
  // Clamp progress between 0 and 100
  const progress = Math.max(0, Math.min(100, progressProp))

  // State for animation
  const [animatedProgress, setAnimatedProgress] = useState(0)

  // Check for reduced motion preference
  const prefersReducedMotion =
    typeof window !== 'undefined' &&
    window.matchMedia('(prefers-reduced-motion: reduce)').matches

  // Animate progress on mount and when progress changes
  useEffect(() => {
    if (prefersReducedMotion) {
      setAnimatedProgress(progress)
      return
    }

    // Small delay to trigger animation
    const timer = setTimeout(() => {
      setAnimatedProgress(progress)
    }, 100)
    return () => clearTimeout(timer)
  }, [progress, prefersReducedMotion])

  // Calculate dimensions
  const isNumericSize = typeof size === 'number'
  const svgSize = isNumericSize ? size : SIZE_VALUES[size]
  const radius = (svgSize - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDashoffset =
    circumference - (animatedProgress / 100) * circumference

  return (
    <div
      data-testid="progress-ring-container"
      className={`relative inline-flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-full ${className || ''}`}
    >
      <svg
        role="img"
        aria-label={`${progress}% progress`}
        className={`transform -rotate-90 ${!isNumericSize ? SIZE_CLASSES[size] : ''}`}
        width={isNumericSize ? size : undefined}
        height={isNumericSize ? size : undefined}
      >
        {/* Background circle */}
        <circle
          cx={svgSize / 2}
          cy={svgSize / 2}
          r={radius}
          fill="none"
          stroke="currentColor"
          strokeWidth={strokeWidth}
          className="text-gray-200 dark:text-gray-700"
        />

        {/* Progress circle */}
        <circle
          data-testid="progress-path"
          cx={svgSize / 2}
          cy={svgSize / 2}
          r={radius}
          fill="none"
          stroke="currentColor"
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          className={`${color} transition-all ease-out ${prefersReducedMotion ? 'duration-0' : 'duration-1000'}`}
        />
      </svg>

      {/* Center content */}
      <div className="absolute inset-0 flex items-center justify-center">
        {children ||
          (showPercentage && (
            <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {progress}%
            </span>
          ))}
      </div>
    </div>
  )
}
