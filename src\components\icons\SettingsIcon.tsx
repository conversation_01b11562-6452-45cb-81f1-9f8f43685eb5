import React from 'react'

interface SettingsIconProps {
  className?: string
  onClick?: () => void
  size?: number
}

export function SettingsIcon({
  className = '',
  onClick,
  size = 24,
}: SettingsIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      onClick={onClick}
      aria-hidden="true"
    >
      <circle cx="12" cy="12" r="3" />
      <path d="M12 1v6m0 6v6m4.22-10.22l1.42-1.42M6.34 19.66l1.42-1.42M1 12h6m6 0h6m-.22 4.22l-1.42 1.42M17.66 6.34l-1.42-1.42" />
    </svg>
  )
}
