# Dr. Muscle Production API & Authentication Configuration

**Last Updated:** 2025-07-01

## Overview

This document contains the production API endpoints and authentication configurations used by the Dr. Muscle mobile apps and web application.

## FOUND PRODUCTION CONFIGURATIONS

### API ENDPOINT RESULTS:

```
✅ FOUND API ENDPOINT: https://drmuscle.azurewebsites.net/
CONFIDENCE LEVEL: High
SOURCE LOCATION: DrMaxMuscle/DrMuscleRestClient.cs lines 836, 840
EVIDENCE TYPE: Code
ADDITIONAL SOURCES:
- DrMuscleWatch/v1/DrMuscleWatchApp/DrMuscleWatchApp/Services/DrMuscleAPIClient.swift line 16
- DrMuscleWatch/v1/DrMuscleWatchApp/test-real-auth.swift line 7
```

### GOOGLE SIGN-IN RESULTS:

```
✅ GOOGLE CLIENT ID (iOS): ************-ldcslmjtnjib5bklf23efrhp8u9qrpq3.apps.googleusercontent.com
SOURCE: DrMaxMuscle/Platforms/iOS/GoogleService-Info.plist line 6

✅ GOOGLE CLIENT ID (Android): Multiple certificates configured
- ************-920imabd01c56gr486jpcsfl7iofadof.apps.googleusercontent.com
- ************-jaecneoq7ckco5j2l85j94i016c0g7j5.apps.googleusercontent.com
- ************-q8hpl1i3k4s1eghgksu4eltrj3q14fca.apps.googleusercontent.com
SOURCE: DrMaxMuscle/Platforms/Android/google-services.json lines 18, 25, 33

✅ GOOGLE CLIENT ID (Web/Server): ************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com
SOURCE LOCATIONS:
- DrMaxMuscle/Platforms/Android/google-services.json line 41
- DrMaxMuscle/MauiProgram.cs line 119
- DrMaxMuscle/Platforms/Android/AndroidManifest.xml line 6

ADDITIONAL CONFIG:
- Firebase Project: drmuscle-4a08d
- Firebase URL: https://drmuscle-4a08d.firebaseio.com
- GCM Sender ID: ************
```

### APPLE SIGN-IN RESULTS:

```
✅ APPLE TEAM ID: 7AAXZ47995
SOURCE: .github/workflows/apple-watch-build-workflow.yml line 643

✅ APPLE BUNDLE ID: com.drmaxmuscle.max
SOURCE LOCATIONS:
- DrMaxMuscle/Platforms/iOS/Info.plist line 8
- DrMaxMuscle/Platforms/iOS/GoogleService-Info.plist line 18

✅ APPLE SIGN-IN CAPABILITY: Enabled
SOURCE: DrMuscle/DrMuscle.iOS/Entitlements.plist line 16

ADDITIONAL CONFIG:
- App Store ID: **********
- Facebook App ID: fb1865252523754972
- Keychain Access Group: $(AppIdentifierPrefix)com.drmaxmuscle.max
```

## AUTHENTICATION FLOW DETAILS:

### API Authentication Endpoints:

- Token Endpoint: `/token` (for initial authentication)
- Apple Sign-In: `/api/Account/RegisterWithApple`
- Login: `/api/Account/Login`

### Social Authentication Implementation Notes:

1. **Google Sign-In**: Uses Firebase Authentication with multiple client certificates for Android (likely for different build types/environments)
2. **Apple Sign-In**: Configured with proper entitlements and team ID
3. **Web Client ID**: The web/server OAuth client ID (ending in -204je3om2b9im1irln4g1ib90uocr9gc) is specifically used for server-side validation

## VERIFICATION:

- ✅ API endpoint `https://drmuscle.azurewebsites.net/` is confirmed across multiple files
- ✅ Google Client IDs follow proper format (\*.apps.googleusercontent.com)
- ✅ Apple Team ID (7AAXZ47995) is a valid 10-character alphanumeric string
- ✅ Bundle ID matches iOS app configuration

## CRITICAL CONFIGURATION SUMMARY FOR WEB APP:

### Production API:

```
Base URL: https://drmuscle.azurewebsites.net/
```

### Google OAuth Configuration:

```
Web Client ID: ************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com
```

### Apple Sign-In Configuration:

```
Team ID: 7AAXZ47995
Bundle ID: com.drmaxmuscle.max
```

These are the exact configurations the production mobile apps are currently using successfully.

## CORS Configuration Status

### Configured Origins (as of 2025-07-01)

The following origins have been added to the Azure App Service CORS settings:

- **Production**: `https://x.dr-muscle.com`
- **Staging**: `https://dr-muscle.vercel.app`
- **Development**: `http://localhost:3000`, `http://localhost:3001`

### Settings

- **Access-Control-Allow-Credentials**: ✅ Enabled
- **Supports**: Token-based authentication with Bearer tokens
- **API Base URL**: `https://drmuscle.azurewebsites.net`

### Important Notes

1. **Credentials Required**: The `withCredentials: true` flag must be set in the axios client configuration
2. **Token Format**: Use `/token` endpoint with `application/x-www-form-urlencoded` content type
3. **Bearer Token**: Include in Authorization header for authenticated requests
4. **Login Model**: Uses `Username` field (not `email`) for authentication

### Authentication Flow Implementation Details

#### Login Request Format

```typescript
// Form data must use these exact field names:
const formData = new URLSearchParams()
formData.append('grant_type', 'password')
formData.append('username', credentials.Username)  // Note: Username, not email
formData.append('password', credentials.Password)

// Headers required:
headers: {
  'Content-Type': 'application/x-www-form-urlencoded',
}
```

#### Response Format

```typescript
interface LoginSuccessResult {
  access_token: string
  token_type: string // "bearer"
  expires_in: number
  refresh_token?: string
  userName: string
  '.issued': string
  '.expires': string
}
```

### Deployment Status (as of 2025-07-01)

- ✅ All ESLint warnings resolved
- ✅ TypeScript strict mode compliance
- ✅ CSS configuration fixed for Tailwind v4
- ✅ CORS configuration active on Azure
- ✅ API client configured with correct endpoints
- ✅ Login functionality updated for OAuth token endpoint
