import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { IOSNavigationBar } from './IOSNavigationBar'

describe('IOSNavigationBar', () => {
  it('renders with title', () => {
    render(<IOSNavigationBar title="Test Title" />)

    expect(screen.getByText('Test Title')).toBeInTheDocument()
  })

  it('renders without back button by default', () => {
    render(<IOSNavigationBar title="Test Title" />)

    expect(screen.queryByLabelText('Go back')).not.toBeInTheDocument()
  })

  it('renders with back button when showBackButton is true', () => {
    render(<IOSNavigationBar title="Test Title" showBackButton />)

    expect(screen.getByLabelText('Go back')).toBeInTheDocument()
  })

  it('calls onBackClick when back button is clicked', async () => {
    const handleBackClick = vi.fn()
    render(
      <IOSNavigationBar
        title="Test Title"
        showBackButton
        onBackClick={handleBackClick}
      />
    )

    await userEvent.click(screen.getByLabelText('Go back'))

    expect(handleBackClick).toHaveBeenCalledTimes(1)
  })

  it('renders right element when provided', () => {
    const rightElement = <button>Action</button>
    render(<IOSNavigationBar title="Test Title" rightElement={rightElement} />)

    expect(screen.getByText('Action')).toBeInTheDocument()
  })

  it('has correct height (44px iOS standard)', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const header = container.querySelector('header')
    const headerContent = header?.querySelector('.h-11')

    expect(headerContent).toBeInTheDocument()
    // h-11 in Tailwind is 44px (11 * 0.25rem * 16px/rem = 44px)
  })

  it('has fixed positioning and proper z-index', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const header = container.querySelector('header')

    expect(header?.classList.contains('fixed')).toBe(true)
    expect(header?.classList.contains('top-0')).toBe(true)
    expect(header?.classList.contains('left-0')).toBe(true)
    expect(header?.classList.contains('right-0')).toBe(true)
    expect(header?.classList.contains('z-50')).toBe(true)
  })

  it('has three-section layout with flexbox', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const sections = container.querySelectorAll('.flex-1')

    expect(sections).toHaveLength(3)
  })

  it('applies custom className', () => {
    const { container } = render(
      <IOSNavigationBar title="Test Title" className="custom-class" />
    )
    const header = container.querySelector('header')

    expect(header?.classList.contains('custom-class')).toBe(true)
  })

  it('renders chevron icon in back button', () => {
    const { container } = render(
      <IOSNavigationBar title="Test Title" showBackButton />
    )
    const svg = container.querySelector('svg')

    expect(svg).toBeInTheDocument()
    expect(svg?.getAttribute('width')).toBe('24')
    expect(svg?.getAttribute('height')).toBe('24')
  })

  it('truncates long titles', () => {
    const longTitle = 'This is a very long title that should be truncated'
    render(<IOSNavigationBar title={longTitle} />)

    const titleElement = screen.getByText(longTitle)
    expect(titleElement.classList.contains('truncate')).toBe(true)
  })

  it('includes spacer div to prevent content overlap', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const spacer = container.querySelector('div.h-11')

    expect(spacer).toBeInTheDocument()
    expect(spacer?.nextElementSibling).toBeNull() // Should be last element
  })

  it('has iOS-style blur effects', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const header = container.querySelector('header')

    expect(header?.classList.contains('backdrop-blur-md')).toBe(true)
    expect(header?.classList.contains('bg-white/80')).toBe(true)
    expect(header?.classList.contains('dark:bg-gray-900/80')).toBe(true)
    expect(header?.classList.contains('border-b')).toBe(true)
    expect(header?.classList.contains('border-gray-200/20')).toBe(true)
    expect(header?.classList.contains('dark:border-gray-700/20')).toBe(true)
  })

  it('supports browsers without backdrop-filter', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const header = container.querySelector('header')

    // Background color classes provide fallback when backdrop-filter is not supported
    expect(header?.classList.contains('bg-white/80')).toBe(true)
    expect(header?.classList.contains('dark:bg-gray-900/80')).toBe(true)
  })

  it('has dark mode support for all elements', () => {
    render(<IOSNavigationBar title="Test Title" showBackButton />)

    // Check title has dark mode text color
    const titleElement = screen.getByText('Test Title')
    expect(titleElement.classList.contains('text-gray-900')).toBe(true)
    expect(titleElement.classList.contains('dark:text-white')).toBe(true)

    // Check back button has dark mode hover state
    const backButton = screen.getByLabelText('Go back')
    expect(backButton.classList.contains('hover:bg-gray-100')).toBe(true)
    expect(backButton.classList.contains('dark:hover:bg-gray-800')).toBe(true)
  })
})
