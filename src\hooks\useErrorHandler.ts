import { useState, useCallback, useEffect } from 'react'
import { categorizeError, type ErrorState } from './errorCategorizer'
import { retryWithBackoff, type RetryOptions } from './retryWithBackoff'

interface ErrorHandlerOptions {
  showToast?: (toast: {
    type: string
    message: string
    duration: number
  }) => void
  workoutContext?: unknown
}

interface QueuedOperation {
  id: string
  operation: () => Promise<unknown>
  timestamp: number
}

export function useErrorHandler(options: ErrorHandlerOptions = {}) {
  const [errorState, setErrorState] = useState<ErrorState | null>(null)
  const [isOffline, setIsOffline] = useState(!navigator.onLine)
  const [retryQueue, setRetryQueue] = useState<QueuedOperation[]>([])

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setIsOffline(false)
    }

    const handleOffline = () => {
      setIsOffline(true)
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Process retry queue when coming back online
  useEffect(() => {
    const processQueue = async () => {
      if (retryQueue.length === 0) return

      const queue = [...retryQueue]
      const failed: QueuedOperation[] = []

      // Process items sequentially to avoid performance issues
      await queue.reduce(async (promise, item) => {
        await promise
        try {
          await item.operation()
        } catch (error) {
          // Only log in development to reduce console noise
          if (process.env.NODE_ENV === 'development') {
            console.error(
              `Failed to process queued operation ${item.id}:`,
              error
            )
          }
          failed.push(item)
        }
      }, Promise.resolve())

      setRetryQueue(failed)
    }

    if (!isOffline && retryQueue.length > 0) {
      processQueue()
    }
  }, [isOffline, retryQueue])

  const handleError = useCallback(
    (error: Error, config: { silent?: boolean } = {}) => {
      const errorState = categorizeError(error, {
        isOffline,
        workoutContext: options.workoutContext,
      })
      setErrorState(errorState)

      // Show toast notification unless silent
      if (!config.silent && options.showToast) {
        options.showToast({
          type: 'error',
          message: errorState.message,
          duration: 5000,
        })
      }

      // Temporarily disabled to reduce console noise
      // Log error for debugging in development only
      // if (process.env.NODE_ENV === 'development') {
      //   console.error('Error handled:', error, errorState)
      // }
    },
    [isOffline, options]
  )

  const clearError = useCallback(() => {
    setErrorState(null)
  }, [])

  const handleRetryWithBackoff = useCallback(
    async <T>(
      operation: () => Promise<T>,
      retryOptions: RetryOptions = {}
    ): Promise<T> => {
      return retryWithBackoff(operation, isOffline, retryOptions)
    },
    [isOffline]
  )

  const queueForRetry = useCallback(
    (id: string, operation: () => Promise<unknown>) => {
      if (!isOffline) {
        // If online, execute immediately
        return operation()
      }

      // Add to retry queue
      setRetryQueue((prev) => [
        ...prev,
        { id, operation, timestamp: Date.now() },
      ])

      return Promise.resolve()
    },
    [isOffline]
  )

  const processRetryQueue = useCallback(async () => {
    if (retryQueue.length === 0) return

    const queue = [...retryQueue]
    const failed: QueuedOperation[] = []

    // Process items sequentially
    await queue.reduce(async (promise, item) => {
      await promise
      try {
        await item.operation()
      } catch (error) {
        // Only log in development to reduce console noise
        if (process.env.NODE_ENV === 'development') {
          console.error(`Failed to process queued operation ${item.id}:`, error)
        }
        failed.push(item)
      }
    }, Promise.resolve())

    setRetryQueue(failed)
  }, [retryQueue])

  return {
    errorState,
    isOffline,
    retryQueue,
    handleError,
    clearError,
    retryWithBackoff: handleRetryWithBackoff,
    queueForRetry,
    processRetryQueue,
  }
}
