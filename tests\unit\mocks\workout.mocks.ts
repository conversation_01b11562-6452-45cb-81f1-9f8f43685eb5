import type {
  ExerciseModel,
  WorkoutTemplateModel,
  WorkoutTemplateGroupModel,
  WorkoutTemplateSettingsModel,
} from '@/types'

export const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsSystemExercise: false,
  IsSwapTarget: false,
  IsFinished: false,
  BodyPartId: 1,
  IsUnilateral: false,
  IsTimeBased: false,
  EquipmentId: 1,
  IsEasy: false,
  IsMedium: false,
  IsBodyweight: false,
  VideoUrl: 'https://example.com/bench-press',
  IsNextExercise: false,
  IsPlate: false,
  IsWeighted: true,
  IsPyramid: false,
  RepsMaxValue: 15,
  RepsMinValue: 5,
  Timer: 0,
  IsNormalSets: true,
  WorkoutGroupId: 1,
  IsBodypartPriority: false,
  IsFlexibility: false,
  IsOneHanded: false,
  LocalVideo: '',
  IsAssisted: false,
}

export const mockWorkoutSettingsModel: WorkoutTemplateSettingsModel = {
  Id: 1,
  Pause: 120,
  Equipment: '',
  ChildWorkoutTemplateId: null,
  SetsModel: null,
  WorkoutProgramId: 1,
  IsFirstSet: false,
  IsFail: false,
  NbRepsMinimalInc: null,
  AvgDuration: null,
  IsNotRealData: false,
}

export const mockWorkoutTemplate: WorkoutTemplateModel = {
  Id: 1,
  UserId: 'test-user',
  Label: 'Push Day',
  Exercises: [mockExercise],
  IsSystemExercise: false,
  WorkoutSettingsModel: mockWorkoutSettingsModel,
}

export const mockWorkoutTemplateGroup: WorkoutTemplateGroupModel = {
  Id: 1,
  IsFeaturedProgram: false,
  UserId: 'test-user',
  Label: 'Week 1',
  WorkoutTemplates: [mockWorkoutTemplate],
  IsSystemExercise: false,
  RequiredWorkoutToLevelUp: 5,
  ProgramId: 1,
}
