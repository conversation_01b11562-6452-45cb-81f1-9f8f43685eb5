import React from 'react'

interface RepsInputProps {
  reps: number
  onChange: (value: string) => void
  onIncrement: () => void
  onDecrement: () => void
  disabled?: boolean
  error?: string
  quickButtons?: number[]
}

const DEFAULT_QUICK_BUTTONS = [5, 8, 10, 12, 15]

export function RepsInput({
  reps,
  onChange,
  onIncrement,
  onDecrement,
  disabled = false,
  error,
  quickButtons = DEFAULT_QUICK_BUTTONS,
}: RepsInputProps) {
  return (
    <div>
      <label
        htmlFor="reps-input"
        className="block text-sm font-medium text-text-secondary mb-2"
      >
        Reps
      </label>
      <div className="flex items-center gap-2">
        <button
          type="button"
          onClick={onDecrement}
          disabled={disabled}
          className={`p-3 border rounded-lg ${
            disabled
              ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed'
              : 'bg-bg-secondary text-text-primary border-text-tertiary hover:bg-bg-tertiary'
          }`}
          aria-label="Decrease reps"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M20 12H4"
            />
          </svg>
        </button>

        <input
          id="reps-input"
          type="number"
          value={reps}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          min={1}
          max={100}
          inputMode="numeric"
          className={`flex-1 px-4 py-3 text-lg text-text-primary border rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-transparent ${
            error ? 'border-error' : 'border-text-tertiary'
          } ${disabled ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed' : 'bg-bg-secondary'}`}
          aria-label="Reps"
          aria-invalid={!!error}
          aria-describedby={error ? 'reps-error' : undefined}
        />

        <button
          type="button"
          onClick={onIncrement}
          disabled={disabled}
          className={`p-3 border rounded-lg ${
            disabled
              ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed'
              : 'bg-bg-secondary text-text-primary border-text-tertiary hover:bg-bg-tertiary'
          }`}
          aria-label="Increase reps"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4v16m8-8H4"
            />
          </svg>
        </button>
      </div>
      {error && (
        <p id="reps-error" className="mt-1 text-sm text-error" role="alert">
          {error}
        </p>
      )}
      <div className="flex gap-2 mt-3">
        {quickButtons.map((quickReps) => (
          <button
            key={quickReps}
            type="button"
            onClick={() => onChange(quickReps.toString())}
            disabled={disabled}
            className={`px-3 py-1 text-sm border rounded-md ${
              reps === quickReps
                ? 'bg-brand-primary text-text-inverse border-brand-primary'
                : 'bg-bg-secondary text-text-primary border-text-tertiary'
            } ${disabled ? 'opacity-50 cursor-not-allowed text-text-tertiary' : 'hover:bg-bg-tertiary'}`}
          >
            {quickReps}
          </button>
        ))}
      </div>
    </div>
  )
}
