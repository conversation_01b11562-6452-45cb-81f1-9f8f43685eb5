import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SetScreen } from '@/components/workout/SetScreen'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')

const mockPush = vi.fn()
const mockBack = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    back: mockBack,
  }),
}))

// Mock data
const mockExercise: ExerciseModel = {
  Id: 1,
  Label: 'Bench Press',
  IsBodyweight: false,
  IsUnilateral: false,
  IsTimeBased: false,
  VideoUrl: 'https://example.com/video',
  IsSystemExercise: true,
  IsSwapTarget: false,
  IsFinished: false,
  IsEasy: false,
  IsMedium: false,
  IsNextExercise: false,
  IsPlate: false,
  IsWeighted: false,
  IsPyramid: false,
  IsNormalSets: true,
  IsBodypartPriority: false,
  IsFlexibility: false,
  IsOneHanded: false,
  LocalVideo: '',
  IsAssisted: false,
}

const mockRecommendation: RecommendationModel = {
  Series: 4,
  Reps: 8,
  Weight: { Lb: 105, Kg: 47.6 },
  OneRMProgress: 85,
  RecommendationInKg: 47.6,
  OneRMPercentage: 75,
  WarmUpReps1: 5,
  WarmUpReps2: 3,
  WarmUpWeightSet1: { Lb: 70, Kg: 31.75 },
  WarmUpWeightSet2: { Lb: 85, Kg: 38.56 },
  WarmUpsList: [],
  WarmupsCount: 2,
  RpRest: 120,
  NbPauses: 0,
  NbRepsPauses: 0,
  IsEasy: false,
  IsMedium: false,
  IsBodyweight: false,
  Increments: { Lb: 5, Kg: 2.5 },
  Max: { Lb: 300, Kg: 136.08 },
  Min: { Lb: 45, Kg: 20.41 },
  IsNormalSets: true,
  IsDeload: false,
  IsBackOffSet: false,
  BackOffSetWeight: { Lb: 0, Kg: 0 },
  IsMaxChallenge: false,
  IsLightSession: false,
  FirstWorkSetReps: 8,
  FirstWorkSetWeight: { Lb: 105, Kg: 47.6 },
  FirstWorkSet1RM: { Lb: 130, Kg: 59 },
  IsPyramid: false,
  IsReversePyramid: false,
  HistorySet: [],
  ReferenceSetHistory: {
    Id: 1,
    Reps: 8,
    Weight: { Lb: 100, Kg: 45.36 },
    IsWarmups: false,
    IsNext: false,
    IsFinished: true,
  },
  MinReps: 6,
  MaxReps: 10,
  isPlateAvailable: true,
  isDumbbellAvailable: true,
  isPulleyAvailable: false,
  isBandsAvailable: false,
  Speed: 2,
  IsManual: false,
  ReferenseReps: 8,
  ReferenseWeight: { Lb: 100, Kg: 45.36 },
  IsDropSet: false,
}

describe('SetScreen', () => {
  const mockUseWorkout = useWorkout as ReturnType<typeof vi.fn>
  const mockUseWorkoutStore = useWorkoutStore as unknown as ReturnType<
    typeof vi.fn
  >

  const mockSaveSet = vi.fn()
  const mockNextSet = vi.fn()
  const mockNextExercise = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup default mocks
    mockUseWorkout.mockReturnValue({
      saveSet: mockSaveSet,
      recommendation: mockRecommendation,
      isLoading: false,
      error: null,
      refetchRecommendation: vi.fn(),
    })

    mockUseWorkoutStore.mockReturnValue({
      exercises: [mockExercise],
      currentExerciseIndex: 0,
      currentSetIndex: 1, // Set 2 of 4
      workoutSession: {
        id: 'test-session',
        startTime: new Date(),
      },
      nextSet: mockNextSet,
      nextExercise: mockNextExercise,
    })
  })

  describe('Display', () => {
    it('should display exercise name prominently', () => {
      // When
      render(<SetScreen />)

      // Then
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
      expect(screen.getByText('Bench Press')).toHaveClass(
        'text-2xl',
        'font-bold'
      )
    })

    it('should display set counter correctly', () => {
      // Given - Update to show first work set (after 2 warmups)
      mockUseWorkoutStore.mockReturnValue({
        exercises: [mockExercise],
        currentExerciseIndex: 0,
        currentSetIndex: 2, // First work set after 2 warmups
        workoutSession: {
          id: 'test-session',
          startTime: new Date(),
        },
        nextSet: mockNextSet,
        nextExercise: mockNextExercise,
      })

      // When
      render(<SetScreen />)

      // Then
      expect(screen.getByText('Set 3 of 4')).toBeInTheDocument()
    })

    it('should display target reps and weight', () => {
      // Given
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })

      // When
      render(<SetScreen />)

      // Then
      expect(screen.getByText(/Target: 8 reps × 105 lbs/i)).toBeInTheDocument()
    })

    it('should display performance percentage from previous', () => {
      // Given
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })

      // When
      render(<SetScreen />)

      // Then
      expect(screen.getByText(/\+5%/)).toBeInTheDocument() // 105 vs 100 lbs
    })

    it('should show warmup indicator for warmup sets', () => {
      // Given
      mockUseWorkoutStore.mockReturnValue({
        exercises: [mockExercise],
        currentExerciseIndex: 0,
        currentSetIndex: 0, // First set (warmup)
        isWarmup: true,
        workoutSession: { id: 'test-session', startTime: new Date() },
        nextSet: mockNextSet,
        nextExercise: mockNextExercise,
      })

      // When
      render(<SetScreen />)

      // Then
      expect(screen.getByText(/Warmup Set 1/i)).toBeInTheDocument()
    })
  })

  describe('Input Fields', () => {
    it('should show weight input with recommendation as default', () => {
      // Given
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })

      // When
      render(<SetScreen />)

      // Then
      const weightInput = screen.getByLabelText('Weight') as HTMLInputElement
      expect(weightInput).toBeInTheDocument()
      expect(weightInput.value).toBe('105')
    })

    it('should show reps input with target as default', () => {
      // Given
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })

      // When
      render(<SetScreen />)

      // Then
      const repsInput = screen.getByLabelText('Reps') as HTMLInputElement
      expect(repsInput).toBeInTheDocument()
      expect(repsInput.value).toBe('8')
    })

    it('should allow editing weight value', async () => {
      // Given
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })
      render(<SetScreen />)

      // When
      const weightInput = screen.getByLabelText('Weight') as HTMLInputElement
      fireEvent.change(weightInput, { target: { value: '110' } })

      // Then
      expect(weightInput).toHaveValue(110)
    })

    it('should allow editing reps value', async () => {
      // Given
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })
      render(<SetScreen />)

      // When
      const repsInput = screen.getByLabelText('Reps') as HTMLInputElement
      fireEvent.change(repsInput, { target: { value: '10' } })

      // Then
      expect(repsInput).toHaveValue(10)
    })

    it('should validate input ranges', async () => {
      // Given
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })
      render(<SetScreen />)

      // When - Invalid weight
      const weightInput = screen.getByLabelText('Weight') as HTMLInputElement
      fireEvent.change(weightInput, { target: { value: '0' } })

      // Then
      await waitFor(() => {
        expect(
          screen.getByText(/Weight must be at least 1/i)
        ).toBeInTheDocument()
      })

      // When - Invalid reps
      const repsInput = screen.getByLabelText('Reps') as HTMLInputElement
      fireEvent.change(repsInput, { target: { value: '0' } })

      // Then
      await waitFor(() => {
        expect(screen.getByText(/Reps must be at least 1/i)).toBeInTheDocument()
      })
    })
  })

  describe('Set Saving', () => {
    it('should save set data when save button clicked', async () => {
      // Given
      const user = userEvent.setup()
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet.mockResolvedValue({}),
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })
      render(<SetScreen />)

      // When
      await user.click(screen.getByRole('button', { name: /Save Set/i }))

      // Then
      expect(mockSaveSet).toHaveBeenCalledWith({
        exerciseId: 1,
        reps: 8,
        weight: { Lb: 105, Kg: 105 * 0.453592 },
        isWarmup: true, // currentSetIndex = 1 is still warmup (< WarmupsCount = 2)
        setNumber: 2,
        duration: undefined,
      })
    })

    it('should show RIR picker after first work set', async () => {
      // Given
      const user = userEvent.setup()
      mockUseWorkoutStore.mockReturnValue({
        exercises: [mockExercise],
        currentExerciseIndex: 0,
        currentSetIndex: 2, // First work set (after 2 warmups)
        isFirstWorkSet: true,
        workoutSession: { id: 'test-session', startTime: new Date() },
        nextSet: mockNextSet,
        nextExercise: mockNextExercise,
      })
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet.mockResolvedValue({}),
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })
      render(<SetScreen />)

      // When
      await user.click(screen.getByRole('button', { name: /Save Set/i }))

      // Then
      await waitFor(() => {
        expect(screen.getByText(/How many more reps?/i)).toBeInTheDocument()
      })
    })

    it('should progress to next set after save', async () => {
      // Given
      const user = userEvent.setup()
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet.mockResolvedValue({}),
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })
      render(<SetScreen />)

      // When
      await user.click(screen.getByRole('button', { name: /Save Set/i }))

      // Then
      await waitFor(() => {
        expect(mockNextSet).toHaveBeenCalled()
      })
    })

    it('should handle save errors gracefully', async () => {
      // Given
      const user = userEvent.setup()
      const error = new Error('Network error')
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet.mockRejectedValue(error),
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })
      render(<SetScreen />)

      // When
      await user.click(screen.getByRole('button', { name: /Save Set/i }))

      // Then
      await waitFor(() => {
        expect(screen.getByText(/Network error/i)).toBeInTheDocument()
        expect(
          screen.getByRole('button', { name: /Retry/i })
        ).toBeInTheDocument()
      })
    })

    it('should disable save button during save', async () => {
      // Given
      const user = userEvent.setup()
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet.mockImplementation(() => new Promise(() => {})), // Never resolves
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })
      render(<SetScreen />)

      // When
      const saveButton = screen.getByRole('button', { name: /Save Set/i })
      await user.click(saveButton)

      // Then
      expect(saveButton).toBeDisabled()
      expect(screen.getByText(/Saving.../i)).toBeInTheDocument()
    })
  })

  describe('Navigation', () => {
    it('should navigate to next exercise when last set completed', async () => {
      // Given
      const user = userEvent.setup()
      mockPush.mockClear()

      mockUseWorkoutStore.mockReturnValue({
        exercises: [mockExercise, { ...mockExercise, Id: 2, Label: 'Squat' }], // Two exercises
        currentExerciseIndex: 0,
        currentSetIndex: 3, // Last set of first exercise
        workoutSession: { id: 'test-session', startTime: new Date() },
        nextSet: mockNextSet,
        nextExercise: mockNextExercise,
      })
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet.mockResolvedValue({}),
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })
      render(<SetScreen />)

      // When
      await user.click(screen.getByRole('button', { name: /Save Set/i }))

      // Then
      await waitFor(() => {
        expect(mockNextExercise).toHaveBeenCalled()
      })
    })

    it('should show workout complete for last exercise', async () => {
      // Given
      const user = userEvent.setup()
      mockUseWorkoutStore.mockReturnValue({
        exercises: [mockExercise],
        currentExerciseIndex: 0,
        currentSetIndex: 3, // Last set
        isLastSet: true,
        isLastExercise: true,
        workoutSession: { id: 'test-session', startTime: new Date() },
        nextSet: mockNextSet,
        nextExercise: mockNextExercise,
      })
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet.mockResolvedValue({}),
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })
      render(<SetScreen />)

      // When
      await user.click(screen.getByRole('button', { name: /Save Set/i }))

      // Then
      await waitFor(() => {
        expect(screen.getByText(/Workout Complete!/i)).toBeInTheDocument()
      })
    })

    it('should allow back navigation with warning', async () => {
      // Given
      const user = userEvent.setup()
      mockBack.mockClear()
      render(<SetScreen />)

      // When
      await user.click(screen.getByRole('button', { name: /Back/i }))

      // Then
      expect(
        screen.getByText(/Are you sure you want to go back?/i)
      ).toBeInTheDocument()

      // When - Confirm
      await user.click(screen.getByRole('button', { name: /Yes, go back/i }))

      // Then
      expect(mockBack).toHaveBeenCalled()
    })
  })

  describe('Bodyweight Exercise', () => {
    it('should handle bodyweight exercises correctly', () => {
      // Given
      const bodyweightExercise = {
        ...mockExercise,
        IsBodyweight: true,
      }
      mockUseWorkoutStore.mockReturnValue({
        exercises: [bodyweightExercise],
        currentExerciseIndex: 0,
        currentSetIndex: 0,
        workoutSession: { id: 'test-session', startTime: new Date() },
        nextSet: mockNextSet,
        nextExercise: mockNextExercise,
      })
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: {
          ...mockRecommendation,
          IsBodyweight: true,
          Weight: { Lb: 20, Kg: 9.07 }, // Additional weight
        },
        isLoading: false,
        error: null,
      })

      // When
      render(<SetScreen />)

      // Then
      expect(screen.getByText(/Additional Weight/i)).toBeInTheDocument()
    })
  })

  describe('Time-Based Exercise', () => {
    it('should handle time-based exercises correctly', () => {
      // Given
      const timeExercise = {
        ...mockExercise,
        IsTimeBased: true,
      }
      mockUseWorkoutStore.mockReturnValue({
        exercises: [timeExercise],
        currentExerciseIndex: 0,
        currentSetIndex: 0,
        workoutSession: { id: 'test-session', startTime: new Date() },
        nextSet: mockNextSet,
        nextExercise: mockNextExercise,
      })
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: {
          ...mockRecommendation,
          IsTimeBased: true,
          Timer: 45,
        },
        isLoading: false,
        error: null,
      })

      // When
      render(<SetScreen />)

      // Then
      expect(screen.getByLabelText(/Duration/i)).toBeInTheDocument()
      expect(screen.getByText(/Target: 45 seconds/i)).toBeInTheDocument()
    })
  })

  describe('Loading and Error States', () => {
    it('should show loading state while fetching recommendation', () => {
      // Given
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: undefined,
        isLoading: true,
        error: null,
      })

      // When
      render(<SetScreen />)

      // Then
      expect(screen.getByText(/Loading exercise data.../i)).toBeInTheDocument()
    })

    it('should show error state when recommendation fails', () => {
      // Given
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: undefined,
        isLoading: false,
        error: new Error('Failed to fetch recommendation'),
      })

      // When
      render(<SetScreen />)

      // Then
      expect(
        screen.getByText(/Failed to load exercise data/i)
      ).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /Retry/i })).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      // Given
      mockUseWorkout.mockReturnValue({
        saveSet: mockSaveSet,
        recommendation: mockRecommendation,
        isLoading: false,
        error: null,
      })

      // When
      render(<SetScreen />)

      // Then
      expect(screen.getByLabelText('Weight')).toHaveAttribute('type', 'number')
      expect(screen.getByLabelText('Reps')).toHaveAttribute('type', 'number')
      expect(screen.getByRole('button', { name: /Save Set/i })).toHaveAttribute(
        'type',
        'submit'
      )
    })

    it('should trap focus on confirmation dialogs', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetScreen />)

      // When
      await user.click(screen.getByRole('button', { name: /Back/i }))

      // Then
      const dialog = screen.getByRole('dialog')
      expect(dialog).toHaveAttribute('aria-modal', 'true')
    })
  })
})
