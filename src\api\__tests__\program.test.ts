import { describe, it, expect, vi, beforeEach } from 'vitest'
import { programApi } from '../program'
import { apiClient } from '../client'
import type { GetUserWorkoutLogAverageResponse } from '@/types'

// Mock the API client
vi.mock('../client')

describe('Program API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getUserProgram', () => {
    it('should transform API response to ProgramModel', async () => {
      const mockResponse: GetUserWorkoutLogAverageResponse = {
        Sets: [],
        Histograms: [],
        TotalWorkoutCompleted: 15,
        ConsecutiveWeeks: 3,
        LastWorkoutDateStr: '2024-01-15',
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: {
            Id: 1,
            Label: 'Beginner Strength Program',
            RemainingToLevelUp: 20,
            IconUrl: '/images/strength.png',
          },
          NextWorkoutTemplate: {
            Id: 1,
            Label: 'Push Day A',
            IsSystemExercise: true,
          },
        },
      }

      // Mock the existing getUserProgramInfo call
      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: {
          StatusCode: 200,
          Result: mockResponse,
        },
      })

      const result = await programApi.getUserProgram()

      expect(result).toEqual({
        id: 1,
        name: 'Beginner Strength Program',
        description: 'Perfect for those new to strength training',
        category: 'Strength',
        totalDays: 90,
        currentDay: 15,
        workoutsCompleted: 15,
        startDate: expect.any(String),
        imageUrl: '/images/strength.png',
      })
    })

    it('should handle missing program info gracefully', async () => {
      const mockResponse: GetUserWorkoutLogAverageResponse = {
        Sets: [],
        Histograms: [],
        TotalWorkoutCompleted: 0,
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: {
          StatusCode: 200,
          Result: mockResponse,
        },
      })

      const result = await programApi.getUserProgram()

      expect(result).toBeNull()
    })

    it('should handle API errors', async () => {
      vi.mocked(apiClient.post).mockRejectedValueOnce(
        new Error('Network error')
      )

      await expect(programApi.getUserProgram()).rejects.toThrow('Network error')
    })
  })

  describe('getProgramProgress', () => {
    it('should calculate progress from API data', async () => {
      const mockResponse: GetUserWorkoutLogAverageResponse = {
        Sets: [],
        Histograms: [],
        TotalWorkoutCompleted: 15,
        ConsecutiveWeeks: 3,
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: {
            Id: 1,
            Label: 'Test Program',
            RemainingToLevelUp: 20,
          },
          NextWorkoutTemplate: {
            Id: 1,
            Label: 'Workout A',
            IsSystemExercise: true,
          },
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: {
          StatusCode: 200,
          Result: mockResponse,
        },
      })

      const result = await programApi.getProgramProgress()

      expect(result).toEqual({
        percentage: expect.any(Number),
        daysCompleted: 15,
        totalWorkouts: 35, // 15 completed + 20 remaining
        currentWeek: expect.any(Number),
        workoutsThisWeek: expect.any(Number),
        remainingWorkouts: 20,
      })
      expect(result!.percentage).toBeGreaterThanOrEqual(0)
      expect(result!.percentage).toBeLessThanOrEqual(100)
    })

    it('should handle missing progress data', async () => {
      const mockResponse: GetUserWorkoutLogAverageResponse = {
        Sets: [],
        Histograms: [],
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: {
          StatusCode: 200,
          Result: mockResponse,
        },
      })

      const result = await programApi.getProgramProgress()

      expect(result).toBeNull()
    })
  })

  describe('getProgramStats', () => {
    it('should aggregate stats from API data', async () => {
      const mockResponse: GetUserWorkoutLogAverageResponse = {
        Sets: [],
        Histograms: [],
        AvgWorkoutTime: {
          hours: 0,
          minutes: 45,
          seconds: 30,
          totalSeconds: 2730,
        },
        TotalWorkoutCompleted: 25,
        ConsecutiveWeeks: 4,
        LastWorkoutDateStr: '2024-01-15',
        NewRecords: [
          { exerciseId: 1, exerciseName: 'Bench Press', value: 225 },
          { exerciseId: 2, exerciseName: 'Squat', value: 315 },
        ],
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: {
          StatusCode: 200,
          Result: mockResponse,
        },
      })

      const result = await programApi.getProgramStats()

      expect(result).toEqual({
        averageWorkoutTime: 45.5,
        totalVolume: 0, // Would be calculated from Sets data
        personalRecords: 2,
        consecutiveWeeks: 4,
        lastWorkoutDate: '2024-01-15',
        totalWorkoutsCompleted: 25,
      })
    })

    it('should handle missing stats gracefully', async () => {
      const mockResponse: GetUserWorkoutLogAverageResponse = {
        Sets: [],
        Histograms: [],
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: {
          StatusCode: 200,
          Result: mockResponse,
        },
      })

      const result = await programApi.getProgramStats()

      expect(result).toEqual({
        averageWorkoutTime: 0,
        totalVolume: 0,
        personalRecords: 0,
        consecutiveWeeks: 0,
        totalWorkoutsCompleted: 0,
      })
    })
  })
})
