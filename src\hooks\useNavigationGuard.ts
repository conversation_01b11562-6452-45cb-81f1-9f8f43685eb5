'use client'

import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkoutStore } from '@/stores/workoutStore'

export function useNavigationGuard() {
  const router = useRouter()
  const { workoutSession } = useWorkoutStore()

  // Note: We intentionally don't use beforeunload event here because it prevents
  // the page from being stored in the browser's back-forward cache (bfcache).
  // This would cause poor performance when users navigate back/forward.
  // Instead, we rely on navigateWithConfirmation for programmatic navigation
  // and let the browser handle page unload normally.

  const navigateWithConfirmation = useCallback(
    (url: string) => {
      if (workoutSession && !workoutSession.endTime) {
        // TODO: Replace with custom modal component
        // For now, using browser confirm as a temporary solution
        // eslint-disable-next-line no-alert
        const confirmed = window.confirm(
          'You have an active workout. Are you sure you want to leave? Your progress will be saved.'
        )
        if (confirmed) {
          router.push(url)
        }
      } else {
        router.push(url)
      }
    },
    [workoutSession, router]
  )

  return {
    navigateWithConfirmation,
  }
}
