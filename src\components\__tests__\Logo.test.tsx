import { describe, it, expect, vi } from 'vitest'
import { render, screen, waitFor, act } from '@testing-library/react'
import { Logo } from '../Logo'

// Mock Next.js Image component
vi.mock('next/image', () => ({
  default: ({
    src,
    alt,
    width,
    height,
    className,
    onLoad,
    onError,
    priority,
    placeholder,
    blurDataURL,
    ...props
  }: {
    src: string
    alt: string
    width: number
    height: number
    className?: string
    onLoad?: () => void
    onError?: () => void
    priority?: boolean
    placeholder?: string
    blurDataURL?: string
  }) => {
    return (
      // eslint-disable-next-line @next/next/no-img-element, jsx-a11y/alt-text
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
        onLoad={onLoad}
        onError={onError}
        data-priority={priority}
        data-placeholder={placeholder}
        data-blur={blurDataURL}
        {...props}
      />
    )
  },
}))

describe('Logo Component', () => {
  it('should render with default props', () => {
    render(<Logo />)

    const logo = screen.getByAltText('Dr. Muscle X')
    expect(logo).toBeInTheDocument()
    expect(logo).toHaveAttribute('src', '/logo-white.png')
    expect(logo).toHaveAttribute('width', '200')
    expect(logo).toHaveAttribute('height', '200')
  })

  it('should support custom width and height props', () => {
    render(<Logo width={300} height={150} />)

    const logo = screen.getByAltText('Dr. Muscle X')
    expect(logo).toHaveAttribute('width', '300')
    expect(logo).toHaveAttribute('height', '150')
  })

  it('should support custom alt text', () => {
    render(<Logo alt="Custom Alt Text" />)

    const logo = screen.getByAltText('Custom Alt Text')
    expect(logo).toBeInTheDocument()
  })

  it('should support custom className', () => {
    render(<Logo className="custom-class" />)

    const wrapper = screen.getByTestId('logo-wrapper')
    expect(wrapper).toHaveClass('custom-class')
  })

  it('should include fade-in animation classes', () => {
    render(<Logo />)

    const logo = screen.getByAltText('Dr. Muscle X')
    expect(logo).toHaveClass('transition-opacity')
    expect(logo).toHaveClass('duration-300')
  })

  it('should handle loading state with opacity', () => {
    render(<Logo />)

    const logo = screen.getByAltText('Dr. Muscle X')
    // Initially should have opacity-0
    expect(logo).toHaveClass('opacity-0')
  })

  it('should transition to full opacity on load', async () => {
    render(<Logo />)

    const logo = screen.getByAltText('Dr. Muscle X')

    // Simulate image load
    await act(async () => {
      const loadEvent = new Event('load')
      logo.dispatchEvent(loadEvent)
    })

    await waitFor(() => {
      expect(logo).toHaveClass('opacity-100')
    })
  })

  it('should handle error state gracefully', async () => {
    render(<Logo />)

    const logo = screen.getByAltText('Dr. Muscle X')

    // Simulate image error
    await act(async () => {
      const errorEvent = new Event('error')
      logo.dispatchEvent(errorEvent)
    })

    // Should still be in DOM but with opacity-0
    await waitFor(() => {
      expect(logo).toBeInTheDocument()
      expect(logo).toHaveClass('opacity-0')
    })
  })

  it('should support priority loading', () => {
    render(<Logo priority />)

    const logo = screen.getByAltText('Dr. Muscle X')
    expect(logo).toHaveAttribute('data-priority', 'true')
  })

  it('should use blur placeholder for smooth loading', () => {
    render(<Logo />)

    const logo = screen.getByAltText('Dr. Muscle X')
    expect(logo).toHaveAttribute('data-placeholder', 'blur')
  })

  it('should have proper accessibility attributes', () => {
    render(<Logo />)

    const logo = screen.getByRole('img')
    expect(logo).toHaveAttribute('alt', 'Dr. Muscle X')
  })

  it('should be responsive by default', () => {
    render(<Logo />)

    const wrapper = screen.getByTestId('logo-wrapper')
    expect(wrapper).toHaveClass('relative')

    const logo = screen.getByAltText('Dr. Muscle X')
    expect(logo).toHaveStyle({ maxWidth: '100%', height: 'auto' })
  })
})
