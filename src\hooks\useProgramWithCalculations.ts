import { useMemo } from 'react'
import { useProgram, useProgramProgress, useProgramStats } from './useProgram'
import {
  calculateProgress,
  calculateCompletionDate,
  getDaysRemaining,
  getMissedWorkouts,
  getWorkoutsPerWeek,
} from '@/lib/programCalculations'
import type { ProgramProgress, ProgramStats } from '@/types'

/**
 * Hook that combines program API data with calculated metrics
 * Provides enhanced program statistics using calculation utilities
 */
export function useProgramWithCalculations() {
  const {
    program,
    isLoading: programLoading,
    error: programError,
    refetch: refetchProgram,
  } = useProgram()

  const {
    progress: apiProgress,
    isLoading: progressLoading,
    error: progressError,
    refetch: refetchProgress,
  } = useProgramProgress(program?.id)

  const {
    stats,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats,
  } = useProgramStats(program?.id)

  // Calculate enhanced progress using our utilities
  const enhancedProgress = useMemo(() => {
    if (!program) return null

    // Use API progress as base but enhance with calculations
    const calculatedProgress = calculateProgress(program, [])

    // Merge API data with calculated data, preferring API where available
    const progress: ProgramProgress = {
      percentage: apiProgress?.percentage ?? calculatedProgress.percentage,
      daysCompleted: calculatedProgress.daysCompleted,
      currentWeek: calculatedProgress.currentWeek,
      totalWorkouts:
        apiProgress?.totalWorkouts ?? calculatedProgress.totalWorkouts,
      workoutsThisWeek:
        apiProgress?.workoutsThisWeek ?? calculatedProgress.workoutsThisWeek,
      remainingWorkouts:
        apiProgress?.remainingWorkouts ?? calculatedProgress.remainingWorkouts,
    }

    return progress
  }, [program, apiProgress])

  // Calculate additional metrics
  const additionalMetrics = useMemo(() => {
    if (!program || !enhancedProgress) return null

    return {
      daysRemaining: getDaysRemaining(program),
      missedWorkouts: getMissedWorkouts(program),
      workoutsPerWeek: getWorkoutsPerWeek(program),
      completionDate: calculateCompletionDate(program, enhancedProgress),
    }
  }, [program, enhancedProgress])

  // Enhanced stats with total workouts from program
  const enhancedStats = useMemo(() => {
    if (!stats || !program) return stats

    return {
      ...stats,
      totalWorkouts: program.totalWorkouts,
    } as ProgramStats
  }, [stats, program])

  const isLoading = programLoading || progressLoading || statsLoading
  const error = programError || progressError || statsError

  const refetchAll = async () => {
    await Promise.all([refetchProgram(), refetchProgress(), refetchStats()])
  }

  return {
    program,
    progress: enhancedProgress,
    stats: enhancedStats,
    additionalMetrics,
    isLoading,
    error,
    refetch: refetchAll,
    hasPartialDataError: !programError && !!(progressError || statsError),
  }
}
