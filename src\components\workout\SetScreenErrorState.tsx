'use client'

interface SetScreenErrorStateProps {
  onRetry: () => void
}

export function SetScreenErrorState({ onRetry }: SetScreenErrorStateProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[100dvh] p-4">
      <div className="text-center">
        <p className="text-red-600 mb-4">Failed to load exercise data</p>
        <button
          onClick={onRetry}
          className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    </div>
  )
}
