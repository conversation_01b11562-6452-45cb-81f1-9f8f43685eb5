<!DOCTYPE html>
<html>
<head>
  <title>Infinite Loop Fix Test</title>
  <style>
    body { font-family: Arial; padding: 20px; background: #1a1a1a; color: #fff; }
    .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
    .success { background: #064e3b; border: 1px solid #10b981; }
    .error { background: #7f1d1d; border: 1px solid #ef4444; }
    .warning { background: #713f12; border: 1px solid #f59e0b; }
    .info { background: #1e40af; border: 1px solid #3b82f6; }
    .metrics { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0; }
    .metric { background: #374151; padding: 20px; border-radius: 8px; text-align: center; }
    .metric h3 { margin: 0 0 10px 0; color: #9ca3af; }
    .metric .value { font-size: 36px; font-weight: bold; }
    button { background: #3b82f6; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
    button:hover { background: #2563eb; }
    .log-container { background: #111827; padding: 20px; border-radius: 8px; max-height: 400px; overflow-y: auto; }
    pre { margin: 0; font-size: 12px; }
  </style>
</head>
<body>
  <h1>Infinite Loop Fix Test</h1>
  
  <div class="metrics">
    <div class="metric">
      <h3>Console Messages</h3>
      <div class="value" id="messageCount">0</div>
    </div>
    <div class="metric">
      <h3>Messages/Second</h3>
      <div class="value" id="messageRate">0</div>
    </div>
    <div class="metric">
      <h3>Test Duration</h3>
      <div class="value" id="duration">0s</div>
    </div>
  </div>

  <div id="status"></div>
  
  <button onclick="testExercisePage()">Test Exercise Page</button>
  <button onclick="clearLogs()">Clear Logs</button>
  
  <h3>Console Monitor</h3>
  <div class="log-container">
    <pre id="logs"></pre>
  </div>

  <script>
    let messageCount = 0;
    let startTime = null;
    let logBuffer = [];
    let updateInterval = null;

    function addStatus(message, type = 'info') {
      const status = document.getElementById('status');
      const div = document.createElement('div');
      div.className = `status ${type}`;
      div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
      status.insertBefore(div, status.firstChild);
    }

    function updateMetrics() {
      document.getElementById('messageCount').textContent = messageCount;
      
      if (startTime) {
        const elapsed = (Date.now() - startTime) / 1000;
        document.getElementById('duration').textContent = `${elapsed.toFixed(0)}s`;
        const rate = messageCount / elapsed;
        document.getElementById('messageRate').textContent = rate.toFixed(1);
        
        // Alert if rate is too high (more than 100/second)
        if (rate > 100) {
          addStatus(`WARNING: High message rate detected: ${rate.toFixed(1)} msgs/sec`, 'warning');
        }
        
        // Alert if total messages exceed threshold
        if (messageCount > 10000) {
          addStatus(`ERROR: Excessive messages detected: ${messageCount} total`, 'error');
        }
      }
    }

    function interceptConsole() {
      const originalLog = console.log;
      const originalWarn = console.warn;
      const originalError = console.error;
      
      console.log = function(...args) {
        messageCount++;
        const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : arg).join(' ');
        logBuffer.push(`[LOG] ${message}`);
        if (logBuffer.length > 100) logBuffer.shift();
        updateLogs();
        originalLog.apply(console, args);
      };
      
      console.warn = function(...args) {
        messageCount++;
        const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : arg).join(' ');
        logBuffer.push(`[WARN] ${message}`);
        if (logBuffer.length > 100) logBuffer.shift();
        updateLogs();
        originalWarn.apply(console, args);
      };
      
      console.error = function(...args) {
        messageCount++;
        const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : arg).join(' ');
        logBuffer.push(`[ERROR] ${message}`);
        if (logBuffer.length > 100) logBuffer.shift();
        updateLogs();
        originalError.apply(console, args);
      };
    }

    function updateLogs() {
      document.getElementById('logs').textContent = logBuffer.join('\n');
    }

    function clearLogs() {
      messageCount = 0;
      startTime = null;
      logBuffer = [];
      updateLogs();
      updateMetrics();
      document.getElementById('status').innerHTML = '';
    }

    function testExercisePage() {
      clearLogs();
      startTime = Date.now();
      
      addStatus('Starting test...', 'info');
      
      // Start metrics update
      if (updateInterval) clearInterval(updateInterval);
      updateInterval = setInterval(updateMetrics, 100);
      
      // Navigate to exercise page
      const exerciseUrl = 'http://localhost:3000/workout/exercise/27474';
      addStatus(`Opening ${exerciseUrl}`, 'info');
      window.open(exerciseUrl, '_blank');
      
      // Monitor for 30 seconds
      setTimeout(() => {
        clearInterval(updateInterval);
        updateMetrics();
        
        if (messageCount < 100) {
          addStatus(`✓ Test PASSED: Only ${messageCount} messages in 30 seconds`, 'success');
        } else if (messageCount < 1000) {
          addStatus(`⚠ Test WARNING: ${messageCount} messages in 30 seconds`, 'warning');
        } else {
          addStatus(`✗ Test FAILED: ${messageCount} messages in 30 seconds`, 'error');
        }
      }, 30000);
    }

    // Initialize console interception
    interceptConsole();
    addStatus('Console monitoring initialized', 'success');
  </script>
</body>
</html>