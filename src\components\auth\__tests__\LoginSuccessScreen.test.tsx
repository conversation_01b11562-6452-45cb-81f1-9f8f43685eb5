import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { LoginSuccessScreen } from '../LoginSuccessScreen'
import { useAuthStore } from '@/stores/authStore'

// Mock the auth store
vi.mock('@/stores/authStore')

// Mock the components
vi.mock('@/components/workout', () => ({
  SuccessIcon: ({ onAnimationComplete }: any) => {
    // Trigger callback immediately in tests
    if (onAnimationComplete) {
      setTimeout(() => onAnimationComplete(), 0)
    }
    return <div data-testid="success-icon">Success Icon</div>
  },
  SuccessMessage: ({ title, message }: any) => (
    <div data-testid="success-message">
      <h1>{title}</h1>
      {message && <p>{message}</p>}
    </div>
  ),
  LoadingProgress: ({ progress, status }: any) => (
    <div data-testid="loading-progress">
      <div>Progress: {progress}%</div>
      {status && <div>{status}</div>}
    </div>
  ),
}))

describe('LoginSuccessScreen', () => {
  const mockUser = {
    email: '<EMAIL>',
    name: 'Test User',
    token: 'mock-token',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAuthStore).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
    } as any)
  })

  it('should render all success components', () => {
    render(<LoginSuccessScreen />)

    expect(screen.getByTestId('success-icon')).toBeInTheDocument()
    expect(screen.getByTestId('success-message')).toBeInTheDocument()
    expect(screen.getByTestId('loading-progress')).toBeInTheDocument()
  })

  it('should display welcome message with user name', () => {
    render(<LoginSuccessScreen />)

    expect(screen.getByText('Welcome back, Test User!')).toBeInTheDocument()
  })

  it('should display generic welcome when user has no name', () => {
    vi.mocked(useAuthStore).mockReturnValue({
      user: { ...mockUser, name: undefined },
      isAuthenticated: true,
    } as any)

    render(<LoginSuccessScreen />)

    expect(screen.getByText('Welcome to Dr. Muscle X!')).toBeInTheDocument()
  })

  it('should display loading status message', () => {
    render(<LoginSuccessScreen />)

    expect(
      screen.getByText("World's Fastest AI Personal Trainer")
    ).toBeInTheDocument()
  })

  it('should show initial progress state', () => {
    render(<LoginSuccessScreen />)

    expect(screen.getByText('Progress: 0%')).toBeInTheDocument()
    expect(screen.getByText('Loading your workout...')).toBeInTheDocument()
  })

  it('should animate progress over time', async () => {
    vi.useFakeTimers()
    const { rerender } = render(<LoginSuccessScreen />)

    // Initial state
    expect(screen.getByText('Progress: 0%')).toBeInTheDocument()

    // Advance timer to show progress
    await vi.advanceTimersByTimeAsync(500)

    // Force re-render to see updated state
    rerender(<LoginSuccessScreen />)

    const progressText = screen.getByText((content) => {
      return (
        content.includes('Progress:') &&
        parseInt(content.match(/\d+/)?.[0] || '0') > 0
      )
    })
    expect(progressText).toBeInTheDocument()

    vi.useRealTimers()
  })

  it('should call onComplete when animation finishes', async () => {
    vi.useFakeTimers()
    const onComplete = vi.fn()
    render(<LoginSuccessScreen onComplete={onComplete} />)

    // Advance time to complete the progress
    await vi.advanceTimersByTimeAsync(2800) // 2500ms + 300ms delay

    expect(onComplete).toHaveBeenCalledTimes(1)

    vi.useRealTimers()
  })

  it('should update loading status during progress', async () => {
    vi.useFakeTimers()
    render(<LoginSuccessScreen />)

    // Advance to different progress stages
    await vi.advanceTimersByTimeAsync(800)
    expect(screen.getByText('Preparing your exercises...')).toBeInTheDocument()

    await vi.advanceTimersByTimeAsync(800)
    expect(screen.getByText('Almost ready...')).toBeInTheDocument()

    vi.useRealTimers()
  })

  it('should apply custom className', () => {
    render(<LoginSuccessScreen className="custom-class" />)

    const container = screen.getByTestId('login-success-screen')
    expect(container).toHaveClass('custom-class')
  })

  it('should have proper mobile-first layout', () => {
    render(<LoginSuccessScreen />)

    const container = screen.getByTestId('login-success-screen')
    expect(container).toHaveClass(
      'min-h-screen',
      'flex',
      'flex-col',
      'items-center',
      'justify-center'
    )
  })

  it('should have proper spacing between elements', () => {
    render(<LoginSuccessScreen />)

    const container = screen.getByTestId('login-success-content')
    expect(container).toHaveClass('space-y-8')
  })

  it('should have max width constraint', () => {
    render(<LoginSuccessScreen />)

    const container = screen.getByTestId('login-success-content')
    expect(container).toHaveClass('max-w-md', 'w-full')
  })

  it('should have proper padding for mobile', () => {
    render(<LoginSuccessScreen />)

    const container = screen.getByTestId('login-success-screen')
    expect(container).toHaveClass('px-6', 'py-8')
  })

  it('should handle undefined user gracefully', () => {
    vi.mocked(useAuthStore).mockReturnValue({
      user: null,
      isAuthenticated: false,
    } as any)

    render(<LoginSuccessScreen />)

    expect(screen.getByText('Welcome to Dr. Muscle X!')).toBeInTheDocument()
  })

  it('should clean up timer on unmount', () => {
    vi.useFakeTimers()
    const { unmount } = render(<LoginSuccessScreen />)

    unmount()

    // Advancing timers should not cause any state updates
    expect(() => {
      vi.advanceTimersByTime(5000)
    }).not.toThrow()

    vi.useRealTimers()
  })

  it('should complete progress to 100%', async () => {
    vi.useFakeTimers()
    render(<LoginSuccessScreen />)

    // Advance to completion
    await vi.advanceTimersByTimeAsync(2500)

    expect(screen.getByText('Progress: 100%')).toBeInTheDocument()

    vi.useRealTimers()
  })

  it('should trigger icon animation on mount', () => {
    render(<LoginSuccessScreen />)

    // The mock component is rendered which means animation started
    expect(screen.getByTestId('success-icon')).toBeInTheDocument()
  })

  it('should support dark mode styling', () => {
    render(<LoginSuccessScreen />)

    const container = screen.getByTestId('login-success-screen')
    expect(container).toHaveClass('bg-white', 'dark:bg-gray-900')
  })
})
