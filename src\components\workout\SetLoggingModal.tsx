'use client'

import { useState, useEffect, useRef } from 'react'
import type { ExerciseModel, WeightModel } from '@/types'

interface SetLoggingModalProps {
  isOpen: boolean
  exercise: ExerciseModel
  setNumber: number
  recommendation?: WeightModel | null
  previousSet?: {
    weight: number
    reps: number
    rir?: number
  }
  onSave: (data: {
    weight: number
    reps: number
    rir?: number
    isWarmup: boolean
    notes: string
  }) => void
  onClose: () => void
  error?: string | null
}

export function SetLoggingModal({
  isOpen,
  exercise,
  setNumber,
  recommendation,
  previousSet,
  onSave,
  onClose,
  error,
}: SetLoggingModalProps) {
  const [weight, setWeight] = useState('')
  const [reps, setReps] = useState('')
  const [rir, setRir] = useState('')
  const [isWarmup, setIsWarmup] = useState(false)
  const [notes, setNotes] = useState('')
  const [showRirHelp, setShowRirHelp] = useState(false)
  // TODO: Implement proper plate calculator logic from mobile app
  // const [showPlateCalculator, setShowPlateCalculator] = useState(false)
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({})

  const modalRef = useRef<HTMLDivElement>(null)
  const firstInputRef = useRef<HTMLInputElement>(null)

  // Initialize form with defaults
  useEffect(() => {
    if (isOpen) {
      // Set weight from recommendation or previous set
      // For now, use a default weight since ExerciseModel doesn't have TargetWeight
      const defaultWeight = recommendation?.Kg || previousSet?.weight || 50 // Default weight
      setWeight(defaultWeight.toString())

      // Set reps from previous or use default
      const defaultReps = previousSet?.reps || 10 // Default reps
      setReps(defaultReps.toString())

      // Set RIR from previous
      setRir(previousSet?.rir?.toString() || '')

      // Reset other fields
      setIsWarmup(false)
      setNotes('')
      setValidationErrors({})

      // Focus first input
      setTimeout(() => firstInputRef.current?.focus(), 100)
    }
  }, [isOpen, exercise, recommendation, previousSet])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose()
      }
    }
    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, onClose])

  if (!isOpen) return null

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!weight || parseFloat(weight) <= 0) {
      errors.weight = 'Weight is required'
    }

    if (exercise.IsTimeBased) {
      if (!reps || parseInt(reps) <= 0) {
        errors.duration = 'Duration is required'
      }
    } else if (!reps || parseInt(reps, 10) < 1) {
      errors.reps = 'Reps must be at least 1'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    onSave({
      weight: parseFloat(weight),
      reps: parseInt(reps, 10),
      rir: rir ? parseInt(rir, 10) : undefined,
      isWarmup,
      notes,
    })
  }

  const handleWeightChange = (delta: number) => {
    const currentWeight = parseFloat(weight) || 0
    const increment = 2.5 // Default to kg increment
    setWeight((currentWeight + delta * increment).toString())
  }

  const handleCopyPrevious = () => {
    if (previousSet) {
      setWeight(previousSet.weight.toString())
      setReps(previousSet.reps.toString())
      setRir(previousSet.rir?.toString() || '')
    }
  }

  const quickReps = [6, 8, 10, 12, 15, 20]

  return (
    <div
      className="fixed inset-0 z-50 flex items-end justify-center bg-black bg-opacity-50 sm:items-center"
      onClick={handleBackdropClick}
      onKeyDown={(e) => {
        if (e.key === 'Escape') onClose()
      }}
      role="presentation"
    >
      <div
        ref={modalRef}
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
        className="w-full max-w-lg rounded-t-2xl bg-white p-6 sm:rounded-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 id="modal-title" className="mb-4 text-xl font-bold">
          Log Set {setNumber} - {exercise.Label}
        </h2>

        {/* Target and recommendation info */}
        <div className="mb-4 space-y-1 text-sm">
          {exercise.RepsMinValue && exercise.RepsMaxValue && (
            <p className="text-gray-600">
              Target: {exercise.RepsMinValue}-{exercise.RepsMaxValue} reps
            </p>
          )}
          {recommendation && (
            <p className="font-medium text-blue-600">
              Recommended: {recommendation.Kg} kg / {recommendation.Lb} lbs
            </p>
          )}
          {previousSet && (
            <p className="text-gray-600">
              Previous: {previousSet.reps} × {previousSet.weight} kg
              {previousSet.rir && ` (RIR: ${previousSet.rir})`}
            </p>
          )}
        </div>

        {previousSet && (
          <button
            type="button"
            onClick={handleCopyPrevious}
            className="mb-4 w-full rounded-lg bg-gray-100 py-2 text-sm text-gray-700 hover:bg-gray-200"
          >
            Copy Previous
          </button>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Weight input */}
          <div>
            <label htmlFor="weight" className="mb-1 block text-sm font-medium">
              {exercise.IsBodyweight ? 'Additional Weight' : 'Weight'}
            </label>
            {exercise.IsBodyweight && (
              <p className="mb-2 text-sm text-gray-600">Using bodyweight</p>
            )}
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={() => handleWeightChange(-1)}
                aria-label="Decrease weight"
                className="rounded-lg bg-gray-100 px-3 py-2 hover:bg-gray-200"
              >
                -
              </button>
              <input
                ref={firstInputRef}
                type="number"
                id="weight"
                value={weight}
                onChange={(e) => setWeight(e.target.value)}
                className="flex-1 rounded-lg border px-3 py-2 text-center"
                step="any"
              />
              <button
                type="button"
                onClick={() => handleWeightChange(1)}
                aria-label="Increase weight"
                className="rounded-lg bg-gray-100 px-3 py-2 hover:bg-gray-200"
              >
                +
              </button>
              <span className="text-sm text-gray-600">kg</span>
            </div>
            {validationErrors.weight && (
              <p role="alert" className="mt-1 text-sm text-red-600">
                {validationErrors.weight}
              </p>
            )}
            {/* TODO: Implement proper plate calculator logic from mobile app
            {!exercise.IsBodyweight && (
              <button
                type="button"
                onClick={() => setShowPlateCalculator(true)}
                className="mt-2 text-sm text-blue-600 hover:text-blue-700"
              >
                Plate Calculator
              </button>
            )}
            */}
          </div>

          {/* Reps/Duration input */}
          <div>
            <label htmlFor="reps" className="mb-1 block text-sm font-medium">
              {exercise.IsTimeBased ? 'Duration (seconds)' : 'Reps'}
            </label>
            {!exercise.IsTimeBased && (
              <div className="mb-2 flex flex-wrap gap-2">
                {quickReps.map((r) => (
                  <button
                    key={r}
                    type="button"
                    onClick={() => setReps(r.toString())}
                    className="rounded-lg bg-gray-100 px-3 py-1 text-sm hover:bg-gray-200"
                  >
                    {r}
                  </button>
                ))}
              </div>
            )}
            <input
              type="number"
              id="reps"
              value={reps}
              onChange={(e) => setReps(e.target.value)}
              className="w-full rounded-lg border px-3 py-2"
              min="1"
            />
            {validationErrors.reps && (
              <p role="alert" className="mt-1 text-sm text-red-600">
                {validationErrors.reps}
              </p>
            )}
            {validationErrors.duration && (
              <p role="alert" className="mt-1 text-sm text-red-600">
                {validationErrors.duration}
              </p>
            )}
          </div>

          {/* RIR input */}
          {!exercise.IsTimeBased && (
            <div>
              <label htmlFor="rir" className="mb-1 block text-sm font-medium">
                RIR (Reps in Reserve)
                <button
                  type="button"
                  onClick={() => setShowRirHelp(!showRirHelp)}
                  className="ml-2 text-blue-600 hover:text-blue-700"
                  aria-label="What is RIR?"
                >
                  ?
                </button>
              </label>
              {showRirHelp && (
                <div className="mb-2 rounded-lg bg-blue-50 p-3 text-sm text-blue-800">
                  <p className="font-medium">Reps in Reserve (RIR)</p>
                  <p>How many more reps could you have done with good form?</p>
                  <ul className="mt-1 list-inside list-disc">
                    <li>0 = Could not do another rep</li>
                    <li>1-2 = Very hard, 1-2 reps left</li>
                    <li>3-4 = Hard, but manageable</li>
                    <li>5+ = Too easy, increase weight</li>
                  </ul>
                </div>
              )}
              <select
                id="rir"
                value={rir}
                onChange={(e) => setRir(e.target.value)}
                className="w-full rounded-lg border px-3 py-2"
              >
                <option value="">Select RIR</option>
                <option value="0">0 - Max effort</option>
                <option value="1">1</option>
                <option value="2">2</option>
                <option value="3">3</option>
                <option value="4">4</option>
                <option value="5">5+</option>
              </select>
            </div>
          )}

          {/* Warmup checkbox */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="warmup"
              checked={isWarmup}
              onChange={(e) => setIsWarmup(e.target.checked)}
              className="mr-2 h-4 w-4 rounded"
            />
            <label htmlFor="warmup" className="text-sm">
              Warmup Set
            </label>
          </div>

          {/* Notes */}
          <div>
            <label htmlFor="notes" className="mb-1 block text-sm font-medium">
              Notes (optional)
            </label>
            <textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="w-full rounded-lg border px-3 py-2"
              rows={2}
              placeholder="How did this set feel?"
            />
          </div>

          {/* Error message */}
          {error && <p className="text-sm text-red-600">{error}</p>}

          {/* Action buttons */}
          <div className="flex gap-2 pt-2">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 rounded-lg border border-gray-300 py-3 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 rounded-lg bg-blue-600 py-3 text-white hover:bg-blue-700"
            >
              Save Set
            </button>
          </div>
        </form>

        {/* TODO: Implement proper plate calculator logic from mobile app
        Plate Calculator Modal
        {showPlateCalculator && (
          <div className="fixed inset-0 z-60 flex items-center justify-center bg-black bg-opacity-50">
            <div className="w-full max-w-sm rounded-lg bg-white p-4">
              <h3 className="mb-4 text-lg font-bold">Plates Needed</h3>
              <p className="mb-4 text-sm text-gray-600">For {weight} kg:</p>
              Simplified plate calculation display
              <div className="mb-4 space-y-2">
                <p className="text-sm">45 kg × 2</p>
                <p className="text-sm">10 kg × 2</p>
                <p className="text-sm">Bar: 45 kg</p>
              </div>
              <button
                onClick={() => setShowPlateCalculator(false)}
                className="w-full rounded-lg bg-gray-100 py-2 hover:bg-gray-200"
              >
                Close
              </button>
            </div>
          </div>
        )}
        */}
      </div>
    </div>
  )
}
