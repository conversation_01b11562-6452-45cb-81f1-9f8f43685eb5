'use client'

import { useState } from 'react'
import type { ExerciseModel } from '@/types'

interface ExerciseSwapModalProps {
  isOpen: boolean
  currentExercise: ExerciseModel
  onSwap: (newExerciseId: number) => void
  onClose: () => void
}

// Mock alternative exercises - in real app, this would come from API
const mockAlternatives: ExerciseModel[] = [
  {
    Id: 101,
    Label: 'Dumbbell Press',
    IsSystemExercise: false,
    IsSwapTarget: false,
    IsFinished: false,
    BodyPartId: 1, // Chest
    IsUnilateral: false,
    IsTimeBased: false,
    EquipmentId: 1, // Dumbbell
    IsEasy: false,
    IsMedium: false,
    IsBodyweight: false,
    VideoUrl: '',
    IsNextExercise: false,
    IsPlate: false,
    IsWeighted: true,
    IsPyramid: false,
    RepsMaxValue: 12,
    RepsMinValue: 8,
    Timer: undefined,
    IsNormalSets: true,
    WorkoutGroupId: undefined,
    IsBodypartPriority: false,
    IsFlexibility: false,
    IsOneHanded: false,
    LocalVideo: '',
    IsAssisted: false,
  },
  {
    Id: 102,
    Label: 'Incline Bench Press',
    IsSystemExercise: false,
    IsSwapTarget: false,
    IsFinished: false,
    BodyPartId: 1, // Chest
    IsUnilateral: false,
    IsTimeBased: false,
    EquipmentId: 2, // Barbell
    IsEasy: false,
    IsMedium: false,
    IsBodyweight: false,
    VideoUrl: '',
    IsNextExercise: false,
    IsPlate: true,
    IsWeighted: true,
    IsPyramid: false,
    RepsMaxValue: 10,
    RepsMinValue: 6,
    Timer: undefined,
    IsNormalSets: true,
    WorkoutGroupId: undefined,
    IsBodypartPriority: false,
    IsFlexibility: false,
    IsOneHanded: false,
    LocalVideo: '',
    IsAssisted: false,
  },
]

export function ExerciseSwapModal({
  isOpen,
  currentExercise,
  onSwap,
  onClose,
}: ExerciseSwapModalProps) {
  const [selectedExercise, setSelectedExercise] = useState<number | null>(null)

  if (!isOpen) return null

  const handleSwap = () => {
    if (selectedExercise) {
      onSwap(selectedExercise)
    }
  }

  return (
    <div
      className="fixed inset-0 z-50 flex items-end justify-center bg-black bg-opacity-50 sm:items-center"
      onClick={onClose}
      onKeyDown={(e) => {
        if (e.key === 'Escape') onClose()
      }}
      role="presentation"
    >
      <div
        role="dialog"
        aria-label="Swap Exercise"
        className="w-full max-w-lg rounded-t-2xl bg-white p-6 sm:rounded-2xl"
        onClick={(e) => e.stopPropagation()}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <h2 className="mb-4 text-xl font-bold">Swap Exercise</h2>
        <p className="mb-4 text-sm text-gray-600">
          Replace <strong>{currentExercise.Label}</strong> with:
        </p>

        <div className="mb-4 max-h-64 space-y-2 overflow-y-auto">
          {mockAlternatives.map((exercise) => (
            <button
              key={exercise.Id}
              onClick={() => setSelectedExercise(exercise.Id)}
              className={`w-full rounded-lg border p-3 text-left hover:bg-gray-50 ${
                selectedExercise === exercise.Id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200'
              }`}
            >
              <h3 className="font-medium">{exercise.Label}</h3>
              <p className="text-sm text-gray-600">
                {exercise.RepsMinValue}-{exercise.RepsMaxValue} reps
              </p>
              {exercise.IsWeighted && !exercise.IsBodyweight && (
                <p className="mt-1 text-xs text-green-600">Weighted exercise</p>
              )}
            </button>
          ))}
        </div>

        <div className="flex gap-2">
          <button
            onClick={onClose}
            className="flex-1 rounded-lg border border-gray-300 py-3 text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSwap}
            disabled={!selectedExercise}
            className="flex-1 rounded-lg bg-blue-600 py-3 text-white hover:bg-blue-700 disabled:opacity-50"
          >
            Swap Exercise
          </button>
        </div>
      </div>
    </div>
  )
}
