import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  monitorFontLoading,
  isFontLoaded,
  waitForFont,
  getFontMetrics,
} from '../fontLoader'

// Mock document.fonts API
const mockFonts = {
  ready: Promise.resolve(),
  check: vi.fn(),
}

describe('Font Loader Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()

    // Mock document.fonts
    Object.defineProperty(document, 'fonts', {
      value: mockFonts,
      writable: true,
      configurable: true,
    })
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('monitorFontLoading', () => {
    it('should track font loading status', async () => {
      const promise = monitorFontLoading()

      // Fast-forward to allow promise to resolve
      await vi.runAllTimersAsync()
      await promise

      const metrics = getFontMetrics()

      expect(metrics).toHaveLength(2)
      expect(metrics[0]).toMatchObject({
        fontFamily: 'Inter',
        status: 'loaded',
      })
      expect(metrics[1]).toMatchObject({
        fontFamily: 'Space Grotesk',
        status: 'loaded',
      })
    })

    it('should handle font loading errors', async () => {
      // Make fonts.ready reject
      mockFonts.ready = Promise.reject(new Error('Font loading failed'))

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      await monitorFontLoading()

      expect(consoleSpy).toHaveBeenCalledWith(
        'Font loading error:',
        expect.any(Error)
      )

      const metrics = getFontMetrics()
      expect(metrics[0]?.status).toBe('failed')
      expect(metrics[1]?.status).toBe('failed')

      consoleSpy.mockRestore()
    })

    it('should measure font load times', async () => {
      // Simulate delayed font loading
      mockFonts.ready = new Promise((resolve) => {
        setTimeout(resolve, 100)
      })

      const promise = monitorFontLoading()

      // Advance timers
      vi.advanceTimersByTime(100)
      await promise

      const metrics = getFontMetrics()
      expect(metrics[0]?.loadTime).toBeGreaterThanOrEqual(100)
    })
  })

  describe('isFontLoaded', () => {
    it('should check if font is loaded', () => {
      mockFonts.check.mockReturnValue(true)

      const result = isFontLoaded('Inter')

      expect(mockFonts.check).toHaveBeenCalledWith('16px "Inter"')
      expect(result).toBe(true)
    })

    it('should return false for unloaded fonts', () => {
      mockFonts.check.mockReturnValue(false)

      const result = isFontLoaded('NonExistentFont')

      expect(result).toBe(false)
    })

    it('should handle missing fonts API', () => {
      // Remove fonts API
      delete (document as any).fonts

      const result = isFontLoaded('Inter')

      expect(result).toBe(false)
    })
  })

  describe('waitForFont', () => {
    it('should wait for font to load', async () => {
      // Mock font check to return true after 2 checks
      let checkCount = 0
      mockFonts.check.mockImplementation(() => {
        checkCount++
        return checkCount >= 2
      })

      const promise = waitForFont('Inter', 1000)

      // Advance timers to trigger checks
      vi.advanceTimersByTime(100)

      const result = await promise

      expect(result).toBe(true)
      expect(mockFonts.check).toHaveBeenCalledTimes(2)
    })

    it('should timeout if font does not load', async () => {
      mockFonts.check.mockReturnValue(false)

      const promise = waitForFont('SlowFont', 200)

      // Advance past timeout
      vi.advanceTimersByTime(250)

      const result = await promise

      expect(result).toBe(false)
    })

    it('should return immediately if font is already loaded', async () => {
      mockFonts.check.mockReturnValue(true)

      const result = await waitForFont('Inter')

      expect(result).toBe(true)
      expect(mockFonts.check).toHaveBeenCalledTimes(1)
    })
  })

  describe('Font Preloading', () => {
    it('should add preload links for critical fonts', async () => {
      const createElementSpy = vi.spyOn(document, 'createElement')
      const appendChildSpy = vi.spyOn(document.head, 'appendChild')

      // Import dynamically to get FontLoader class
      const fontLoaderModule = await import('../fontLoader')
      fontLoaderModule.FontLoader.preloadFonts()

      // Should create 2 link elements
      expect(createElementSpy).toHaveBeenCalledTimes(2)
      expect(createElementSpy).toHaveBeenCalledWith('link')

      // Should append links to head
      expect(appendChildSpy).toHaveBeenCalledTimes(2)

      // Check link properties
      const linkCalls = appendChildSpy.mock.calls
      linkCalls.forEach((call) => {
        const link = call[0] as HTMLLinkElement
        expect(link.rel).toBe('preload')
        expect(link.as).toBe('font')
        expect(link.type).toBe('font/woff2')
        expect(link.crossOrigin).toBe('anonymous')
      })
    })
  })
})
