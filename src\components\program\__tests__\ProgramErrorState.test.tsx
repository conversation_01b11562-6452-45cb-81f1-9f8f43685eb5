import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { ProgramErrorState } from '../ProgramErrorState'
import { useRouter } from 'next/navigation'

vi.mock('next/navigation')

describe('ProgramErrorState', () => {
  const mockRouter = {
    push: vi.fn(),
  }

  beforeEach(() => {
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    vi.clearAllMocks()
  })

  it('should render timeout error correctly', () => {
    const error = new Error('Network request timeout')
    error.name = 'TimeoutError'
    const onRetry = vi.fn()

    render(<ProgramErrorState error={error} onRetry={onRetry} />)

    expect(screen.getByText('Connection Timeout')).toBeInTheDocument()
    expect(screen.getByText(/check your internet/i)).toBeInTheDocument()
    expect(
      screen.getByRole('button', { name: /try again/i })
    ).toBeInTheDocument()

    fireEvent.click(screen.getByRole('button', { name: /try again/i }))
    expect(onRetry).toHaveBeenCalled()
  })

  it('should render expired program error correctly', () => {
    const error = new Error('Program access expired')
    error.name = 'ExpiredError'

    render(<ProgramErrorState error={error} />)

    expect(screen.getByText('Program Access Expired')).toBeInTheDocument()
    expect(screen.getByText(/renew to continue/i)).toBeInTheDocument()
    expect(
      screen.getByRole('button', { name: /renew access/i })
    ).toBeInTheDocument()

    fireEvent.click(screen.getByRole('button', { name: /renew access/i }))
    expect(mockRouter.push).toHaveBeenCalledWith('/programs')
  })

  it('should render network error correctly', () => {
    const error = new Error('Network connection failed')
    error.name = 'NetworkError'
    const onRetry = vi.fn()

    render(<ProgramErrorState error={error} onRetry={onRetry} />)

    expect(screen.getByText('No Internet Connection')).toBeInTheDocument()
    expect(
      screen.getByText(/check your internet connection/i)
    ).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument()
  })

  it('should render authentication error correctly', () => {
    const error = new Error('401 Unauthorized')

    render(<ProgramErrorState error={error} />)

    expect(screen.getByText('Authentication Required')).toBeInTheDocument()
    expect(screen.getByText(/session has expired/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /log in/i })).toBeInTheDocument()

    fireEvent.click(screen.getByRole('button', { name: /log in/i }))
    expect(mockRouter.push).toHaveBeenCalledWith('/login')
  })

  it('should render default error for unknown errors', () => {
    const error = new Error('Something went wrong')
    const onRetry = vi.fn()

    render(<ProgramErrorState error={error} onRetry={onRetry} />)

    expect(screen.getByText('Failed to Load Program')).toBeInTheDocument()
    expect(screen.getByText(error.message)).toBeInTheDocument()
    expect(
      screen.getByRole('button', { name: /try again/i })
    ).toBeInTheDocument()
  })

  it('should handle null error gracefully', () => {
    render(<ProgramErrorState error={null} />)

    expect(screen.getByText('Failed to Load Program')).toBeInTheDocument()
    expect(screen.getByText(/something went wrong/i)).toBeInTheDocument()
  })

  it('should show alternative action when retry is provided', () => {
    const error = new Error('Program access expired')
    error.name = 'ExpiredError'
    const onRetry = vi.fn()

    render(<ProgramErrorState error={error} onRetry={onRetry} />)

    expect(screen.getByText('Try different action')).toBeInTheDocument()

    fireEvent.click(screen.getByText('Try different action'))
    expect(onRetry).toHaveBeenCalled()
  })

  it('should apply custom className', () => {
    const error = new Error('Test error')
    const { container } = render(
      <ProgramErrorState error={error} className="custom-class" />
    )

    expect(container.firstChild).toHaveClass('custom-class')
  })
})
