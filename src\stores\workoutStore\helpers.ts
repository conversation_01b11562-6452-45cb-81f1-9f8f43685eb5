/**
 * Helper functions for the workout store
 */

import type { WorkoutState } from './types'

// Helper function to track cache operation metrics
export const trackCacheOperation = (
  set: (partial: Partial<WorkoutState>) => void,
  get: () => WorkoutState,
  hasResult: boolean,
  latency: number
) => {
  // Defer state updates to prevent updates during render
  queueMicrotask(() => {
    const state = get()
    const newHits = state.cacheStats.hits + (hasResult ? 1 : 0)
    const newMisses = state.cacheStats.misses + (hasResult ? 0 : 1)
    const newOperationCount = state.cacheStats.operationCount + 1
    const newTotalLatency = state.cacheStats.totalLatency + latency

    set({
      cacheStats: {
        ...state.cacheStats,
        hits: newHits,
        misses: newMisses,
        operationCount: newOperationCount,
        totalLatency: newTotalLatency,
        hitRate: newOperationCount > 0 ? newHits / newOperationCount : 0,
        averageLatency:
          newOperationCount > 0 ? newTotalLatency / newOperationCount : 0,
      },
    })
  })
}
