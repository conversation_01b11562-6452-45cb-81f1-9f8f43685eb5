# ADR-001: Downgrade Tailwind CSS from v4 to v3 for Production Stability

## Status

**Accepted** - July 2025

## Context

### Problem Statement

The Dr. Muscle X web application was experiencing CSS rendering issues in production where styles were not loading correctly on the login page (https://x.dr-muscle.com/login). Investigation revealed the root cause was a configuration incompatibility between Tailwind CSS v4.1.11 and Next.js production builds.

### Technical Details

**Current Configuration:**

- Tailwind CSS: v4.1.11 (alpha/beta version)
- PostCSS Plugin: @tailwindcss/postcss v4.1.11
- Next.js: v15.3.4
- PostCSS: v8.5.6

**Issues Identified:**

1. CSS build failures in production environments
2. Invalid CSS generation without proper selectors
3. PostCSS configuration incompatibility with Tailwind v4
4. Known issues with @tailwindcss/postcss plugin in Next.js production builds

**Error Pattern:**

```
TypeError: Cannot read properties of undefined (reading 'All')
at @tailwindcss/postcss/dist/index.js
```

### Research Findings

- Tailwind CSS v4 is still in development/alpha phase
- Multiple community reports of production build failures with v4 + Next.js
- Official recommendation from Tailwind team is to use PostCSS for Next.js integration
- v4's Lightning CSS integration is not yet stable with Next.js webpack pipeline

## Decision

**We will downgrade to Tailwind CSS v3.4.x** for production stability and reliability.

### Implementation Plan

1. **Package Updates:**

   ```json
   {
     "devDependencies": {
       "tailwindcss": "^3.4.1",
       "postcss": "^8.4.35",
       "autoprefixer": "^10.4.17"
     }
   }
   ```

2. **PostCSS Configuration:**

   ```js
   // postcss.config.js
   module.exports = {
     plugins: {
       tailwindcss: {},
       autoprefixer: {},
     },
   }
   ```

3. **CSS Import Update:**
   ```css
   /* src/styles/globals.css */
   @tailwind base;
   @tailwind components;
   @tailwind utilities;
   ```

## Alternatives Considered

### Option 1: Fix v4 Configuration

- **Pros:** Keep modern features, smaller bundle size
- **Cons:** Unstable in production, ongoing maintenance overhead
- **Decision:** Rejected due to production stability requirements

### Option 2: Manual Build with Tailwind CLI

- **Pros:** Complete control over CSS generation
- **Cons:** Complex build process, breaks hot reload in development
- **Decision:** Rejected due to development experience impact

### Option 3: Wait for v4 Stable Release

- **Pros:** Future-ready, modern features
- **Cons:** Unknown timeline, blocks current production issues
- **Decision:** Rejected due to immediate production needs

## Consequences

### Positive

- ✅ **Immediate production fix** - CSS will load correctly in production
- ✅ **Proven stability** - v3.4.x is battle-tested with Next.js
- ✅ **Community support** - Extensive documentation and troubleshooting resources
- ✅ **Build reliability** - Consistent builds across development and production
- ✅ **Developer experience** - Hot reload works properly in development

### Negative

- ❌ **Miss v4 features** - No access to new CSS features and performance improvements
- ❌ **Larger bundle size** - v3 generates more CSS than v4's optimized output
- ❌ **Future migration** - Will need to upgrade to v4 when stable

### Neutral

- 🔄 **Temporary solution** - Plan to upgrade to v4 once stable
- 🔄 **No breaking changes** - Existing Tailwind classes remain the same

## Implementation Timeline

1. **Immediate (Day 1):** Update package.json and configuration files
2. **Day 1:** Test build locally and verify CSS loading
3. **Day 1:** Deploy to production and confirm fix
4. **Ongoing:** Monitor Tailwind v4 stable release timeline
5. **Future:** Plan migration to v4 when production-ready

## Success Criteria

- [x] Production CSS loads correctly without console errors
- [x] Login page displays proper styling and branding
- [x] Build process completes successfully in CI/CD
- [x] No regression in development experience
- [ ] Performance metrics remain stable (monitoring)

## Review

**Next Review:**

- Evaluate Tailwind CSS v4 stable release status
- Assess production readiness for upgrade
- Plan migration timeline if v4 is stable

## References

- [Tailwind CSS v4 Alpha Documentation](https://tailwindcss.com/docs/v4-beta)
- [Next.js PostCSS Configuration Guide](https://nextjs.org/docs/app/guides/tailwind-css)
- [Community Issue: Tailwind 4 and Next.js Production Builds](https://community.vercel.com/t/some-unknown-tailwind-postcss-error-that-stops-deployment-on-vercel/8011)
- [GitHub Discussion: Tailwind 4 Next.js Integration](https://github.com/tailwindlabs/tailwindcss/discussions/15599)

---

**Decision Made By:** Development Team  
**Date:** January 2025  
**Supersedes:** None  
**Superseded By:** TBD (future v4 upgrade ADR)
