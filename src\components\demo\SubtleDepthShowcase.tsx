'use client'

import React from 'react'
import { Button } from '@/design-system/components/Button/Button'
import { StatCard } from '@/components/StatCard'
import { ExerciseCard } from '@/components/workout/ExerciseCard'
import type { ExerciseWorkSetsModel } from '@/types'

export function SubtleDepthShowcase() {
  const mockExercise = {
    Id: 1,
    Label: 'Bench Press',
    IsFinished: false,
    IsBodyweight: false,
    IsNextExercise: true,
    isLoadingSets: false,
    BodyPartId: 1,
    setsError: null,
    lastSetsUpdate: Date.now(),
    sets: [
      {
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        IsWarmups: false,
        IsNext: false,
        IsFinished: true,
      },
      {
        Reps: 8,
        Weight: { Lb: 155, Kg: 70.31 },
        IsWarmups: false,
        IsNext: false,
        IsFinished: true,
      },
      {
        Reps: 6,
        Weight: { Lb: 175, Kg: 79.38 },
        IsWarmups: false,
        IsNext: false,
        IsFinished: true,
      },
    ],
  } as ExerciseWorkSetsModel

  return (
    <div className="p-6 space-y-8 bg-bg-primary min-h-screen">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h1 className="heading-luxury-lg text-gradient-gold text-4xl">
          Subtle Depth Theme
        </h1>
        <p className="text-text-secondary tracking-luxury text-shadow-sm text-xl">
          Premium sophistication with rich, layered interfaces
        </p>
      </div>

      {/* Gradient Backgrounds Demo */}
      <div className="space-y-4">
        <h2 className="heading-luxury text-2xl text-text-primary mb-4">
          Premium Gradients
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gradient-premium p-6 rounded-theme shadow-theme-lg">
            <h3 className="text-text-primary font-heading tracking-luxury mb-2">
              Premium Background
            </h3>
            <p className="text-text-secondary">
              Subtle charcoal gradient for depth
            </p>
          </div>
          <div className="bg-gradient-metallic-gold p-6 rounded-theme shadow-theme-lg">
            <h3 className="text-text-inverse font-heading tracking-luxury mb-2">
              Metallic Gold
            </h3>
            <p className="text-text-inverse/80">Luxury gold gradient effect</p>
          </div>
        </div>
      </div>

      {/* Typography Demo */}
      <div className="space-y-4">
        <h2 className="heading-luxury text-2xl text-text-primary mb-4">
          Enhanced Typography
        </h2>
        <div className="bg-bg-secondary p-6 rounded-theme shadow-theme-md space-y-3">
          <h1 className="heading-luxury-lg text-gradient-gold">
            Playfair Display Heading
          </h1>
          <h2 className="heading-luxury text-text-primary">
            Luxury Letter Spacing
          </h2>
          <p className="text-text-secondary tracking-luxury-wide">
            Wide tracking for premium feel
          </p>
          <p className="text-brand-primary text-shadow-gold text-lg">
            Gold text shadow effect
          </p>
        </div>
      </div>

      {/* Button Showcase */}
      <div className="space-y-4">
        <h2 className="heading-luxury text-2xl text-text-primary mb-4">
          Interactive Elements
        </h2>
        <div className="flex flex-wrap gap-4">
          <Button variant="primary" size="lg">
            Primary with Shimmer
          </Button>
          <Button variant="secondary" size="lg">
            Secondary Gradient
          </Button>
          <Button variant="ghost" size="lg">
            Ghost with Hover
          </Button>
        </div>
      </div>

      {/* Component Examples */}
      <div className="space-y-4">
        <h2 className="heading-luxury text-2xl text-text-primary mb-4">
          Enhanced Components
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <StatCard
            icon={
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            }
            label="Total Volume"
            value="12,450"
            format={(v) => `${v} lbs`}
            trend="up"
          />
          <StatCard
            icon={
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            }
            label="Workout Time"
            value="45"
            format={(v) => `${v} min`}
          />
          <StatCard
            icon={
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            }
            label="Intensity"
            value="85"
            format={(v) => `${v}%`}
            trend="up"
          />
        </div>
      </div>

      {/* Exercise Card Example */}
      <div className="space-y-4">
        <h2 className="heading-luxury text-2xl text-text-primary mb-4">
          Workout Components
        </h2>
        <div className="max-w-md">
          <ExerciseCard
            exercise={mockExercise}
            onExerciseClick={() => {}}
            onRetry={() => {}}
            isCurrentExercise
          />
        </div>
      </div>

      {/* Shimmer Effect Demo */}
      <div className="space-y-4">
        <h2 className="heading-luxury text-2xl text-text-primary mb-4">
          Shimmer Effects
        </h2>
        <div className="bg-bg-secondary p-6 rounded-theme shadow-theme-lg shimmer">
          <h3 className="text-text-primary font-heading mb-2">
            Continuous Shimmer
          </h3>
          <p className="text-text-secondary">Loading state with gold shimmer</p>
        </div>
      </div>
    </div>
  )
}
