/**
 * Utility to clear workout cache when quota is exceeded
 */

import { logger } from '@/utils/logger'

export function clearWorkoutCache(): void {
  try {
    const workoutData = localStorage.getItem('drmuscle-workout')
    if (workoutData) {
      const parsed = JSON.parse(workoutData)
      // Clear the large cached data
      if (parsed.state?.cachedData) {
        parsed.state.cachedData.userWorkouts = null
        parsed.state.cachedData.exerciseRecommendations = {}
        parsed.state.cachedData.lastUpdated.exerciseRecommendations = {}

        // Try to save the cleaned data
        try {
          localStorage.setItem('drmuscle-workout', JSON.stringify(parsed))
          logger.log('Successfully cleared workout cache')
        } catch (e) {
          // If still too large, remove the entire item
          localStorage.removeItem('drmuscle-workout')
          logger.log('Removed entire workout cache due to quota')
        }
      }
    }
  } catch (error) {
    logger.error('Error clearing workout cache:', error)
    // As a last resort, remove the item entirely
    localStorage.removeItem('drmuscle-workout')
  }
}

// Auto-clear on page load if needed
if (typeof window !== 'undefined') {
  try {
    // Check if we can write to localStorage
    const testKey = 'drmuscle-quota-test'
    localStorage.setItem(testKey, 'test')
    localStorage.removeItem(testKey)
  } catch (e) {
    if (e instanceof DOMException && e.name === 'QuotaExceededError') {
      logger.warn(
        'localStorage quota exceeded on page load, clearing workout cache'
      )
      clearWorkoutCache()
    }
  }
}
