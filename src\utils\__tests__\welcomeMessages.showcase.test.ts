import { describe, it, expect, vi } from 'vitest'
import {
  getWelcomeMessage,
  getMotivationalMessage,
  getUserStats,
} from '../welcomeMessages'

describe('welcomeMessages Showcase', () => {
  it('should demonstrate various welcome scenarios', () => {
    // Morning greeting
    const morning = new Date('2024-01-01 08:00:00')
    vi.spyOn(global, 'Date').mockImplementation(() => morning)

    const morningGreeting = getWelcomeMessage('Alex')
    // console.log('Morning:', morningGreeting)
    expect(morningGreeting).toContain('Alex')

    // Afternoon greeting
    const afternoon = new Date('2024-01-01 15:00:00')
    vi.spyOn(global, 'Date').mockImplementation(() => afternoon)

    const afternoonGreeting = getWelcomeMessage('Jordan')
    // console.log('Afternoon:', afternoonGreeting)
    expect(afternoonGreeting).toContain('Jordan')

    // Evening greeting without name
    const evening = new Date('2024-01-01 19:00:00')
    vi.spyOn(global, 'Date').mockImplementation(() => evening)

    const eveningGreeting = getWelcomeMessage()
    // console.log('Evening (no name):', eveningGreeting)
    expect(eveningGreeting).toContain('Champion')

    vi.restoreAllMocks()
  })

  it('should demonstrate motivational messages for different user journeys', () => {
    // New user
    const newUserMessage = getMotivationalMessage({
      streak: 0,
      totalWorkouts: 1,
    })
    // console.log('New user:', newUserMessage)
    expect(newUserMessage).toContain('journey begins')

    // Building streak
    const streakMessage = getMotivationalMessage({
      streak: 5,
      totalWorkouts: 15,
    })
    // console.log('Building streak:', streakMessage)
    expect(streakMessage).toContain('5')

    // Long streak
    const longStreakMessage = getMotivationalMessage({
      streak: 45,
      totalWorkouts: 150,
    })
    // console.log('Long streak:', longStreakMessage)
    expect(longStreakMessage).toContain('45')

    // Milestone workout
    const milestoneMessage = getMotivationalMessage({
      streak: 0,
      totalWorkouts: 100,
    })
    // console.log('Milestone:', milestoneMessage)
    expect(milestoneMessage).toContain('100')

    // Coming back after break
    const lastMonth = new Date()
    lastMonth.setDate(lastMonth.getDate() - 30)
    const comebackMessage = getMotivationalMessage({
      streak: 0,
      totalWorkouts: 50,
      lastWorkoutDate: lastMonth,
    })
    // console.log('Comeback:', comebackMessage)
    expect(comebackMessage).toMatch(/back|return/i)

    // Same day workout (no streak to avoid conflict)
    const todayMessage = getMotivationalMessage({
      streak: 0,
      totalWorkouts: 75,
      lastWorkoutDate: new Date(),
    })
    // console.log('Same day:', todayMessage)
    expect(todayMessage).toContain('another session')
  })

  it('should demonstrate complete user stats formatting', () => {
    // Active user with full stats
    const activeStats = getUserStats({
      streak: 21,
      totalWorkouts: 156,
      currentLevel: 'Advanced',
      lastWorkoutDate: new Date(),
    })
    // console.log('Active user stats:', activeStats)
    expect(activeStats.streakText).toBe('21 day streak')
    expect(activeStats.workoutCountText).toBe('156 workouts')
    expect(activeStats.levelText).toBe('Advanced athlete')
    expect(activeStats.timeSinceLastWorkout).toBe('Working out today')

    // User returning after break
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)

    const returningStats = getUserStats({
      streak: 0,
      totalWorkouts: 1234,
      currentLevel: 'Intermediate',
      lastWorkoutDate: yesterday,
    })
    // console.log('Returning user stats:', returningStats)
    expect(returningStats.streakText).toBe('New journey')
    expect(returningStats.workoutCountText).toBe('1,234 workouts')
    expect(returningStats.levelText).toBe('Intermediate athlete')
    expect(returningStats.timeSinceLastWorkout).toBe('Yesterday')

    // Brand new user
    const newUserStats = getUserStats({})
    // console.log('New user stats:', newUserStats)
    expect(newUserStats.streakText).toBe('New journey')
    expect(newUserStats.workoutCountText).toBe('First workout')
    expect(newUserStats.levelText).toBe('Ready to start')
    expect(newUserStats.timeSinceLastWorkout).toBe('Welcome!')
  })
})
