import React from 'react'

interface BicepsIconProps {
  className?: string
  size?: number
}

export function BicepsIcon({ className = '', size = 24 }: BicepsIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      aria-hidden="true"
    >
      <path d="M9 4a3 3 0 0 0-3 3v4a5 5 0 0 0 10 0V7a3 3 0 0 0-3-3M9 7h6" />
      <path d="M8 14v4a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-4" />
    </svg>
  )
}
