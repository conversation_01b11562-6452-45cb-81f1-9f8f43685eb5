/**
 * Constants and types for program performance monitoring
 */

export const ProgramPerformanceMarks = {
  PROGRAM_PAGE_START: 'program-page-start',
  PROGRAM_PAGE_END: 'program-page-end',
  PROGRAM_DATA_FETCH_START: 'program-data-fetch-start',
  PROGRAM_DATA_FETCH_END: 'program-data-fetch-end',
  PRO<PERSON>AM_FIRST_PAINT: 'program-first-paint',
  PROGRAM_INTERACTIVE: 'program-interactive',
} as const

export interface PerformanceMetrics {
  pageLoad: number
  componentRenders: Record<string, number>
  dataFetch: number
  totalRenderTime: number
  memory?: {
    used: number // MB
    total: number // MB
    limit: number // MB
    percentage: number
  }
}

export interface PerformanceWithMemory extends Performance {
  memory?: {
    usedJSHeapSize: number
    totalJSHeapSize: number
    jsHeapSizeLimit: number
  }
}
