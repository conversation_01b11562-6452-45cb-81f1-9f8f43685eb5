import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { renderHook, act, waitFor } from '@testing-library/react'
import { useAuthStore } from '@/stores/authStore'

describe('Auth Store - UserInfo Cache', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks()
    ;(localStorage.getItem as vi.Mock).mockReturnValue(null)

    // Reset the store state
    useAuthStore.setState({
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      hasHydrated: false,
      cachedUserInfo: null,
      cacheVersion: 1,
    })

    // Mock Date.now() for consistent testing
    vi.spyOn(Date, 'now').mockReturnValue(1000000)

    // Mock performance.now() for consistent timing
    vi.spyOn(performance, 'now')
      .mockReturnValueOnce(0) // Start time
      .mockReturnValueOnce(0.5) // End time
  })

  afterEach(() => {
    vi.clearAllMocks()
    vi.restoreAllMocks()
  })

  describe('Cache Operations', () => {
    it('should cache user info with timestamp and version', () => {
      const { result } = renderHook(() => useAuthStore())
      const userInfo = {
        firstName: 'John',
        lastName: 'Doe',
        customField: 'value',
      }

      act(() => {
        result.current.setCachedUserInfo(userInfo)
      })

      const state = useAuthStore.getState()
      expect(state.cachedUserInfo).toEqual({
        data: userInfo,
        timestamp: 1000000,
        version: 1,
      })
    })

    it('should retrieve cached user info when valid', () => {
      const { result } = renderHook(() => useAuthStore())
      const userInfo = {
        firstName: 'Jane',
        lastName: 'Smith',
      }

      act(() => {
        result.current.setCachedUserInfo(userInfo)
      })

      const cached = result.current.getCachedUserInfo()
      expect(cached).toEqual(userInfo)
    })

    it('should return null when cache is empty', () => {
      const { result } = renderHook(() => useAuthStore())

      const cached = result.current.getCachedUserInfo()
      expect(cached).toBeNull()
    })

    it('should clear user info cache', () => {
      const { result } = renderHook(() => useAuthStore())

      // Set cache
      act(() => {
        result.current.setCachedUserInfo({ firstName: 'Test' })
      })

      // Clear cache
      act(() => {
        result.current.clearUserInfoCache()
      })

      const state = useAuthStore.getState()
      expect(state.cachedUserInfo).toBeNull()
    })
  })

  describe('Cache TTL', () => {
    it('should detect fresh cache', () => {
      const { result } = renderHook(() => useAuthStore())

      act(() => {
        result.current.setCachedUserInfo({ firstName: 'Test' })
      })

      expect(result.current.isCacheStale()).toBe(false)
    })

    it('should detect stale cache after 1 week', () => {
      const { result } = renderHook(() => useAuthStore())

      // Set cache
      act(() => {
        result.current.setCachedUserInfo({ firstName: 'Test' })
      })

      // Fast forward time by 1 week + 1 second
      vi.spyOn(Date, 'now').mockReturnValue(
        1000000 + 7 * 24 * 60 * 60 * 1000 + 1000
      )

      expect(result.current.isCacheStale()).toBe(true)
    })

    it('should return null for stale cache', () => {
      const { result } = renderHook(() => useAuthStore())

      // Set cache
      act(() => {
        result.current.setCachedUserInfo({ firstName: 'Test' })
      })

      // Fast forward time
      vi.spyOn(Date, 'now').mockReturnValue(
        1000000 + 7 * 24 * 60 * 60 * 1000 + 1000
      )

      const cached = result.current.getCachedUserInfo()
      expect(cached).toBeNull()
    })

    it('should handle cache exactly at TTL boundary', () => {
      const { result } = renderHook(() => useAuthStore())

      act(() => {
        result.current.setCachedUserInfo({ firstName: 'Test' })
      })

      // Fast forward exactly 1 week
      vi.spyOn(Date, 'now').mockReturnValue(1000000 + 7 * 24 * 60 * 60 * 1000)

      // Should still be valid at exact boundary
      expect(result.current.isCacheStale()).toBe(false)
    })
  })

  describe('Cache Version Management', () => {
    it('should return null for mismatched cache version', () => {
      const { result } = renderHook(() => useAuthStore())

      // Manually set cache with old version
      act(() => {
        useAuthStore.setState({
          cachedUserInfo: {
            data: { firstName: 'Old' },
            timestamp: Date.now(),
            version: 0, // Old version
          },
        })
      })

      const cached = result.current.getCachedUserInfo()
      expect(cached).toBeNull()
    })

    it('should maintain cache version on logout', () => {
      const { result } = renderHook(() => useAuthStore())

      act(() => {
        result.current.setCachedUserInfo({ firstName: 'Test' })
        result.current.logout()
      })

      const state = useAuthStore.getState()
      expect(state.cacheVersion).toBe(1)
      expect(state.cachedUserInfo).toBeNull()
    })
  })

  describe('Cache Persistence', () => {
    it('should persist cached user info', async () => {
      const { result } = renderHook(() => useAuthStore())
      const userInfo = {
        firstName: 'Persisted',
        lastName: 'User',
      }

      act(() => {
        result.current.setCachedUserInfo(userInfo)
      })

      // Wait for persist middleware
      await waitFor(() => {
        expect(localStorage.setItem).toHaveBeenCalledWith(
          'drmuscle-auth',
          expect.stringContaining('"cachedUserInfo"')
        )
      })
    })

    it('should restore cached user info on hydration', async () => {
      const cachedData = {
        data: { firstName: 'Restored', lastName: 'User' },
        timestamp: Date.now() - 1000, // 1 second ago
        version: 1,
      }

      const storedData = {
        state: {
          user: { email: '<EMAIL>' },
          token: 'token',
          refreshToken: 'refresh',
          isAuthenticated: true,
          cachedUserInfo: cachedData,
          cacheVersion: 1,
        },
        version: 0,
      }

      ;(localStorage.getItem as vi.Mock).mockReturnValue(
        JSON.stringify(storedData)
      )

      // Clear store and rehydrate
      useAuthStore.setState({
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        hasHydrated: false,
        cachedUserInfo: null,
        cacheVersion: 1,
      })

      await useAuthStore.persist.rehydrate()

      const { result } = renderHook(() => useAuthStore())
      const cached = result.current.getCachedUserInfo()

      expect(cached).toEqual(cachedData.data)
    })
  })

  describe('Performance Logging', () => {
    it('should log cache set performance in development', () => {
      const originalEnv = process.env.NODE_ENV
      vi.stubEnv('NODE_ENV', 'development')

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      const { result } = renderHook(() => useAuthStore())

      act(() => {
        result.current.setCachedUserInfo({ firstName: 'Test' })
      })

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringMatching(/\[AuthStore\] UserInfo cache set in \d+\.\d+ms/)
      )

      consoleSpy.mockRestore()
      vi.stubEnv('NODE_ENV', originalEnv)
    })

    it('should log cache retrieval performance in development', () => {
      const originalEnv = process.env.NODE_ENV
      vi.stubEnv('NODE_ENV', 'development')

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      const { result } = renderHook(() => useAuthStore())

      // Set cache first
      act(() => {
        result.current.setCachedUserInfo({ firstName: 'Test' })
      })

      // Reset spy to capture only the get call
      consoleSpy.mockClear()

      // Get cache
      act(() => {
        result.current.getCachedUserInfo()
      })

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringMatching(
          /\[AuthStore\] UserInfo cache retrieved in \d+\.\d+ms/
        )
      )

      consoleSpy.mockRestore()
      vi.stubEnv('NODE_ENV', originalEnv)
    })

    it('should not log performance in production', () => {
      const originalEnv = process.env.NODE_ENV
      vi.stubEnv('NODE_ENV', 'production')

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      const { result } = renderHook(() => useAuthStore())

      act(() => {
        result.current.setCachedUserInfo({ firstName: 'Test' })
        result.current.getCachedUserInfo()
      })

      expect(consoleSpy).not.toHaveBeenCalled()

      consoleSpy.mockRestore()
      vi.stubEnv('NODE_ENV', originalEnv)
    })
  })

  describe('Integration', () => {
    it('should work with updateUser to sync cache', () => {
      const { result } = renderHook(() => useAuthStore())

      // Set initial user
      act(() => {
        result.current.setUser({
          email: '<EMAIL>',
        })
      })

      // Cache user info
      act(() => {
        result.current.setCachedUserInfo({
          firstName: 'John',
          lastName: 'Doe',
        })
      })

      // Update user with cached data
      act(() => {
        const cached = result.current.getCachedUserInfo()
        if (cached) {
          result.current.updateUser({
            firstName: cached.firstName,
            lastName: cached.lastName,
          })
        }
      })

      expect(result.current.user).toEqual({
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
      })
    })

    it('should clear cache on logout', () => {
      const { result } = renderHook(() => useAuthStore())

      // Set auth and cache
      act(() => {
        result.current.setAuth({
          Result: true,
          UserData: { Email: '<EMAIL>', Name: 'Test' },
          UserToken: 'token',
          RefreshToken: 'refresh',
        })
        result.current.setCachedUserInfo({ firstName: 'Test' })
      })

      // Logout
      act(() => {
        result.current.logout()
      })

      expect(result.current.cachedUserInfo).toBeNull()
      expect(result.current.getCachedUserInfo()).toBeNull()
    })
  })
})
