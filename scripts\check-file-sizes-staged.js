#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const MAX_LINES = 225;
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];
const IGNORE_PATTERNS = [
  'test',
  'spec',
  '.d.ts',
  'generated'
];

function getStagedFiles() {
  try {
    const output = execSync('git diff --cached --name-only', { encoding: 'utf-8' });
    return output.split('\n').filter(file => file.trim());
  } catch (error) {
    return [];
  }
}

function shouldCheckFile(filePath) {
  const ext = path.extname(filePath);
  if (!EXTENSIONS.includes(ext)) return false;
  
  for (const pattern of IGNORE_PATTERNS) {
    if (filePath.includes(pattern)) return false;
  }
  
  return true;
}

function countLines(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    return content.split('\n').length;
  } catch (error) {
    return 0;
  }
}

function checkStagedFiles() {
  const stagedFiles = getStagedFiles();
  const filesToCheck = stagedFiles.filter(shouldCheckFile);
  
  if (filesToCheck.length === 0) {
    console.log('✅ No staged files to check');
    return true;
  }
  
  const oversizedFiles = [];
  
  filesToCheck.forEach(file => {
    const lineCount = countLines(file);
    if (lineCount > MAX_LINES) {
      oversizedFiles.push({ file, lineCount });
    }
  });
  
  if (oversizedFiles.length > 0) {
    console.error('\n❌ Staged files exceeding the maximum line limit of', MAX_LINES, 'lines:\n');
    oversizedFiles.forEach(({ file, lineCount }) => {
      console.error(`  ${file}: ${lineCount} lines (${lineCount - MAX_LINES} over limit)`);
    });
    console.error('\nPlease split these files before committing.');
    console.error('To bypass this check temporarily, use: git commit --no-verify\n');
    return false;
  }
  
  console.log('✅ All staged files are within the line limit');
  return true;
}

// Run the check
const success = checkStagedFiles();
process.exit(success ? 0 : 1);