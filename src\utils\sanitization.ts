/**
 * Sanitization utilities for preventing XSS and injection attacks
 */

/**
 * Escape HTML special characters to prevent XSS
 */
export function escapeHtml(str: string): string {
  const htmlEscapes: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
    '/': '&#x2F;',
  }

  return str.replace(/[&<>"'/]/g, (char) => htmlEscapes[char] || char)
}

/**
 * Sanitize user input by removing potentially dangerous characters
 * Allows alphanumeric, spaces, and common punctuation
 */
export function sanitizeInput(input: string): string {
  // Remove null bytes
  let sanitized = input.replace(/\0/g, '')

  // Remove control characters except newlines and tabs
  // eslint-disable-next-line no-control-regex
  sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')

  // Trim whitespace
  sanitized = sanitized.trim()

  return sanitized
}

/**
 * Validate and sanitize email addresses
 */
export function sanitizeEmail(email: string): string {
  const sanitized = sanitizeInput(email.toLowerCase())

  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(sanitized)) {
    throw new Error('Invalid email format')
  }

  return sanitized
}

/**
 * Sanitize numeric input
 */
export function sanitizeNumber(input: string | number): number {
  const num = typeof input === 'string' ? parseFloat(input) : input

  if (Number.isNaN(num) || !Number.isFinite(num)) {
    throw new Error('Invalid number')
  }

  return num
}

/**
 * Sanitize URL to prevent open redirect vulnerabilities
 */
export function sanitizeUrl(url: string): string {
  try {
    const parsed = new URL(url, window.location.origin)

    // Only allow http(s) protocols
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      throw new Error('Invalid protocol')
    }

    // For relative URLs, ensure they don't contain dangerous schemes
    if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
      // eslint-disable-next-line no-script-url
      if (url.includes('javascript:') || url.includes('data:')) {
        throw new Error('Invalid URL scheme')
      }
    }

    return parsed.toString()
  } catch {
    throw new Error('Invalid URL')
  }
}

/**
 * Strip HTML tags from string (for plain text display)
 */
export function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '')
}

/**
 * Validate and sanitize workout data
 */
export function sanitizeWorkoutData(data: unknown): unknown {
  if (typeof data !== 'object' || data === null) {
    return data
  }

  if (Array.isArray(data)) {
    return data.map((item) => sanitizeWorkoutData(item))
  }

  const sanitized: Record<string, unknown> = {}
  const dataObj = data as Record<string, unknown>

  // eslint-disable-next-line no-restricted-syntax
  for (const key of Object.keys(dataObj)) {
    const value = dataObj[key]

    if (typeof value === 'string') {
      sanitized[key] = sanitizeInput(value)
    } else if (typeof value === 'number') {
      sanitized[key] = sanitizeNumber(value)
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeWorkoutData(value)
    } else {
      sanitized[key] = value
    }
  }

  return sanitized
}
