/* eslint-disable no-console */
/**
 * Utility to clear failed requests with invalid weight data from localStorage
 * This is a one-time fix for the weight conversion bug that stored kg as lbs
 */

interface QueuedRequest {
  id?: string
  type: string
  error?: string
  data?: {
    weight?: {
      Lb?: number
      Kg?: number
    }
  }
}

export function clearBadWeightData() {
  if (typeof window === 'undefined') return

  try {
    // Clear failed requests that have invalid weight data
    const failedRequests = localStorage.getItem('drmuscle-failed-requests')
    if (failedRequests) {
      const requests: QueuedRequest[] = JSON.parse(failedRequests)
      const filteredRequests = requests.filter((req) => {
        // Remove saveSet requests where weight might be invalid
        // These will be re-saved correctly with the fix
        if (
          req.type === 'saveSet' &&
          req.error?.includes('Invalid Weight data')
        ) {
          // Temporarily disabled to reduce console noise
          // Only log in development
          // if (process.env.NODE_ENV === 'development') {
          //   console.log('[Weight Fix] Removing invalid request:', req.id)
          // }
          return false
        }
        return true
      })

      if (filteredRequests.length !== requests.length) {
        localStorage.setItem(
          'drmuscle-failed-requests',
          JSON.stringify(filteredRequests)
        )
        // Temporarily disabled to reduce console noise
        // Only log in development
        // if (process.env.NODE_ENV === 'development') {
        //   console.log(
        //     `[Weight Fix] Cleared ${requests.length - filteredRequests.length} invalid weight requests`
        //   )
        // }
      }
    }

    // Also clear offline queue with potential bad weight data
    const offlineQueue = localStorage.getItem('drmuscle-offline-queue')
    if (offlineQueue) {
      const queue: QueuedRequest[] = JSON.parse(offlineQueue)
      const filteredQueue = queue.filter((req) => {
        if (req.type === 'saveSet' && req.data?.weight) {
          // Check if weight values look wrong (kg value is smaller than lb value)
          const { Lb, Kg } = req.data.weight
          if (Lb && Kg && Kg < Lb * 0.4) {
            // Temporarily disabled to reduce console noise
            // console.log(
            //   '[Weight Fix] Removing invalid offline request:',
            //   req.id
            // )
            return false
          }
        }
        return true
      })

      if (filteredQueue.length !== queue.length) {
        localStorage.setItem(
          'drmuscle-offline-queue',
          JSON.stringify(filteredQueue)
        )
        // Temporarily disabled to reduce console noise
        // Only log in development
        // if (process.env.NODE_ENV === 'development') {
        //   console.log(
        //     `[Weight Fix] Cleared ${queue.length - filteredQueue.length} invalid offline requests`
        //   )
        // }
      }
    }
  } catch (error) {
    // Temporarily disabled to reduce console noise
    // Only log in development
    // if (process.env.NODE_ENV === 'development') {
    //   console.error('[Weight Fix] Error clearing bad weight data:', error)
    // }
  }
}
/* eslint-enable no-console */
