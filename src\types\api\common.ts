/**
 * Common API Types
 *
 * These TypeScript interfaces are shared across multiple API endpoints.
 */

/**
 * Standard API response wrapper
 * Note: Not all endpoints use this pattern
 * @template T The type of data in the response
 */
export interface ApiResponse<T> {
  Result: boolean
  Code: number
  ErrorMessage?: string
  Data?: T
}

/**
 * Simple response for operations that return success/failure
 */
export interface BooleanModel {
  Result: boolean
  Code?: number
  ErrorMessage?: string
}
