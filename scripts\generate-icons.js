const sharp = require('sharp')
const fs = require('fs')
const path = require('path')

async function generateIcons(sourcePath, outputDir) {
  // Check if source file exists
  if (!fs.existsSync(sourcePath)) {
    throw new Error('Source icon file not found')
  }

  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  const sizes = [72, 96, 128, 144, 152, 192, 384, 512]
  const generated = []
  let totalSize = 0

  // Get source image metadata
  const sourceImage = sharp(sourcePath)
  const metadata = await sourceImage.metadata()

  if (!metadata.width || !metadata.height) {
    throw new Error('Invalid source image')
  }

  // Generate icons for each size
  for (const size of sizes) {
    // Generate regular icon
    try {
      const regularPath = path.join(outputDir, `icon-${size}x${size}.png`)
      
      await sharp(sourcePath)
        .resize(size, size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }
        })
        .toFile(regularPath)

      const stats = fs.statSync(regularPath)
      totalSize += stats.size

      generated.push({
        size,
        type: 'regular',
        path: regularPath,
      })
    } catch (error) {
      throw new Error(`Failed to generate icon: ${error}`)
    }

    // Generate maskable icon with padding
    try {
      const maskablePath = path.join(outputDir, `icon-maskable-${size}x${size}.png`)
      
      // For maskable icons, create a white canvas and composite the icon
      // This ensures exact dimensions
      const innerSize = Math.floor(size * 0.6)
      const padding = Math.floor((size - innerSize) / 2)
      
      // First resize the source icon
      const resizedIcon = await sharp(sourcePath)
        .resize(innerSize, innerSize, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }
        })
        .toBuffer()
      
      // Create white background canvas of exact size
      await sharp({
        create: {
          width: size,
          height: size,
          channels: 4,
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        }
      })
        .composite([{
          input: resizedIcon,
          top: padding,
          left: padding
        }])
        .toFile(maskablePath)

      const stats = fs.statSync(maskablePath)
      totalSize += stats.size

      generated.push({
        size,
        type: 'maskable',
        path: maskablePath,
      })
    } catch (error) {
      throw new Error(`Failed to generate icon: ${error}`)
    }
  }

  return {
    source: sourcePath,
    outputDir,
    generated,
    totalSize,
  }
}

// Export for testing
module.exports = { generateIcons }

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2)
  const sourcePath = args[0] || path.join(process.cwd(), 'public', 'app-icon.png')
  const outputDir = args[1] || path.join(process.cwd(), 'public', 'icons')

  generateIcons(sourcePath, outputDir)
    .then((result) => {
      console.log('✅ Icon generation complete!')
      console.log(`Generated ${result.generated.length} icons`)
      console.log(`Total size: ${(result.totalSize / 1024 / 1024).toFixed(2)} MB`)
      console.log('\nGenerated icons:')
      result.generated.forEach((icon) => {
        console.log(`  - ${path.basename(icon.path)} (${icon.type})`)
      })
    })
    .catch((error) => {
      console.error('❌ Icon generation failed:', error.message)
      process.exit(1)
    })
}