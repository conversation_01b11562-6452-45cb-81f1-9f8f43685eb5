import { describe, it, expect, beforeEach, vi } from 'vitest'
import { apiClient } from '@/api/client'
import { authApi } from '@/api/auth'
import type { LoginModel, RegisterModel, LoginSuccessResult } from '@/types'

// Mock the API client
vi.mock('@/api/client', () => ({
  apiClient: {
    post: vi.fn(),
  },
}))

describe('Auth API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('login', () => {
    it('should call login endpoint with correct credentials', async () => {
      // Given: Valid login credentials
      const credentials: LoginModel = {
        Email: '<EMAIL>',
        Password: 'password123',
      }

      const mockResponse: LoginSuccessResult = {
        Result: true,
        UserData: {
          Email: credentials.Email,
          Name: 'Test User',
        },
        UserToken: 'auth-token-123',
        RefreshToken: 'refresh-token-123',
      }

      ;(apiClient.post as any).mockResolvedValueOnce({ data: mockResponse })

      // When: Login is called
      const result = await authApi.login(credentials)

      // Then: API is called correctly
      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/account/login',
        credentials
      )
      expect(result).toEqual(mockResponse)
    })

    it('should handle login errors', async () => {
      // Given: Invalid credentials
      const credentials: LoginModel = {
        Email: '<EMAIL>',
        Password: 'wrong',
      }

      const errorResponse = {
        response: {
          status: 401,
          data: {
            Result: false,
            ErrorMessage: 'Invalid email or password',
          },
        },
      }

      ;(apiClient.post as any).mockRejectedValueOnce(errorResponse)

      // When/Then: Login fails
      await expect(authApi.login(credentials)).rejects.toMatchObject(
        errorResponse
      )
      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/account/login',
        credentials
      )
    })
  })

  describe('register', () => {
    it('should call register endpoint with user data', async () => {
      // Given: Registration data
      const userData: RegisterModel = {
        Email: '<EMAIL>',
        Password: 'securepassword123',
        ConfirmPassword: 'securepassword123',
        Name: 'New User',
      }

      const mockResponse: LoginSuccessResult = {
        Result: true,
        UserData: {
          Email: userData.Email,
          Name: userData.Name!,
        },
        UserToken: 'new-auth-token',
        RefreshToken: 'new-refresh-token',
      }

      ;(apiClient.post as any).mockResolvedValueOnce({ data: mockResponse })

      // When: Register is called
      const result = await authApi.register(userData)

      // Then: API is called correctly
      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/account/register',
        userData
      )
      expect(result).toEqual(mockResponse)
    })

    it('should handle registration errors', async () => {
      // Given: Duplicate email
      const userData: RegisterModel = {
        Email: '<EMAIL>',
        Password: 'password123',
        ConfirmPassword: 'password123',
      }

      const errorResponse = {
        response: {
          status: 400,
          data: {
            Result: false,
            ErrorMessage: 'Email already exists',
          },
        },
      }

      ;(apiClient.post as any).mockRejectedValueOnce(errorResponse)

      // When/Then: Registration fails
      await expect(authApi.register(userData)).rejects.toMatchObject(
        errorResponse
      )
    })
  })

  describe('refreshToken', () => {
    it('should call refresh endpoint with refresh token', async () => {
      // Given: Valid refresh token
      const refreshToken = 'valid-refresh-token'
      const mockResponse = {
        access_token: 'new-access-token',
        refresh_token: 'new-refresh-token',
        expires_in: 3600,
      }

      ;(apiClient.post as any).mockResolvedValueOnce({ data: mockResponse })

      // When: Refresh is called
      const result = await authApi.refreshToken(refreshToken)

      // Then: API is called correctly
      expect(apiClient.post).toHaveBeenCalledWith('/api/account/refresh', {
        refreshToken,
      })
      expect(result).toEqual(mockResponse)
    })

    it('should handle invalid refresh token', async () => {
      // Given: Invalid refresh token
      const refreshToken = 'invalid-token'
      const errorResponse = {
        response: {
          status: 401,
          data: {
            Result: false,
            ErrorMessage: 'Invalid refresh token',
          },
        },
      }

      ;(apiClient.post as any).mockRejectedValueOnce(errorResponse)

      // When/Then: Refresh fails
      await expect(authApi.refreshToken(refreshToken)).rejects.toMatchObject(
        errorResponse
      )
    })
  })

  // Logout is handled client-side only - no API tests needed
})
