/**
 * Unit tests for AppleOAuthHelper
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { AppleOAuthHelper } from '../appleOAuth'
import type {
  AppleSignInResponse,
  AppleTokenPayload,
} from '../../../types/oauth'

// Mock performance API
vi.mock('../../performance', () => ({
  PerformanceMonitor: {
    mark: vi.fn(),
    measure: vi.fn(),
  },
}))

describe('AppleOAuthHelper', () => {
  // Mock window.AppleID
  const mockAppleID = {
    auth: {
      init: vi.fn(),
      signIn: vi.fn(),
    },
  }

  // Sample token payload
  const mockTokenPayload: AppleTokenPayload = {
    sub: '001838.a1b2c3d4e5f6',
    email: '<EMAIL>',
    email_verified: 'true',
    is_private_email: 'true',
    real_user_status: 2,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600,
    iss: 'https://appleid.apple.com',
    aud: 'com.drmaxmuscle.web',
    auth_time: Math.floor(Date.now() / 1000),
  }

  // Create a valid JWT token
  const createMockToken = (payload: AppleTokenPayload): string => {
    const header = { alg: 'RS256', typ: 'JWT' }
    const encodedHeader = btoa(JSON.stringify(header))
    const encodedPayload = btoa(JSON.stringify(payload))
    const signature = 'mock_signature'
    return `${encodedHeader}.${encodedPayload}.${signature}`
  }

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()

    // Setup window.AppleID
    ;(window as any).AppleID = mockAppleID

    // Mock crypto.getRandomValues
    vi.spyOn(global.crypto, 'getRandomValues').mockImplementation((array) => {
      const arr = array as Uint8Array
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    })

    // Mock localStorage and sessionStorage
    const mockStorage = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    }
    Object.defineProperty(window, 'localStorage', {
      value: mockStorage,
      writable: true,
    })
    Object.defineProperty(window, 'sessionStorage', {
      value: mockStorage,
      writable: true,
    })
  })

  afterEach(() => {
    // Clean up
    delete (window as any).AppleID
    vi.restoreAllMocks()
  })

  describe('SDK Loading', () => {
    it('should load Apple SDK successfully', async () => {
      // Remove AppleID to test loading
      delete (window as any).AppleID

      // Mock script loading
      const scriptElement = {
        src: '',
        async: false,
        defer: false,
        onload: null as any,
        onerror: null as any,
      }

      vi.spyOn(document, 'createElement').mockReturnValue(scriptElement as any)
      vi.spyOn(document.head, 'appendChild').mockImplementation(() => {
        // Simulate successful load
        setTimeout(() => {
          ;(window as any).AppleID = mockAppleID
          if (scriptElement.onload) scriptElement.onload()
        }, 0)
        return scriptElement as any
      })

      const onSuccess = vi.fn()
      const onError = vi.fn()

      const initPromise = AppleOAuthHelper.initialize(onSuccess, onError)

      await initPromise

      expect(document.createElement).toHaveBeenCalledWith('script')
      expect(scriptElement.src).toBe(
        'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js'
      )
      expect(scriptElement.async).toBe(true)
      expect(scriptElement.defer).toBe(true)
    })

    it('should handle SDK loading failure', async () => {
      // Remove AppleID to test loading
      delete (window as any).AppleID

      // Mock script loading failure
      const scriptElement = {
        src: '',
        async: false,
        defer: false,
        onload: null as any,
        onerror: null as any,
      }

      vi.spyOn(document, 'createElement').mockReturnValue(scriptElement as any)
      vi.spyOn(document.head, 'appendChild').mockImplementation(() => {
        // Simulate load error
        setTimeout(() => {
          if (scriptElement.onerror) scriptElement.onerror()
        }, 0)
        return scriptElement as any
      })

      const onSuccess = vi.fn()
      const onError = vi.fn()

      await expect(
        AppleOAuthHelper.initialize(onSuccess, onError)
      ).rejects.toThrow('Apple Sign-In SDK not available')

      expect(onError).toHaveBeenCalledWith({
        code: 'provider_error',
        message: 'Failed to initialize Apple Sign-In',
        provider: 'apple',
        providerError: expect.any(Error),
        details: {
          originalError: 'Apple Sign-In SDK not available',
        },
      })
    })

    it('should reuse existing load promise', async () => {
      // Remove AppleID to test loading
      delete (window as any).AppleID

      // Reset static properties
      AppleOAuthHelper['isInitialized'] = false
      AppleOAuthHelper['loadPromise'] = null

      const scriptElement = {
        src: '',
        async: false,
        defer: false,
        onload: null as any,
        onerror: null as any,
      }

      const createElementSpy = vi
        .spyOn(document, 'createElement')
        .mockReturnValue(scriptElement as any)
      vi.spyOn(document.head, 'appendChild').mockImplementation(() => {
        setTimeout(() => {
          ;(window as any).AppleID = mockAppleID
          if (scriptElement.onload) scriptElement.onload()
        }, 10)
        return scriptElement as any
      })

      const onSuccess1 = vi.fn()
      const onError1 = vi.fn()
      const onSuccess2 = vi.fn()
      const onError2 = vi.fn()

      // Start two initializations simultaneously
      const promise1 = AppleOAuthHelper.initialize(onSuccess1, onError1)
      const promise2 = AppleOAuthHelper.initialize(onSuccess2, onError2)

      await Promise.all([promise1, promise2])

      // Should only create one script element
      expect(createElementSpy).toHaveBeenCalledTimes(1)
    })
  })

  describe('Initialization', () => {
    it('should initialize with default config', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      await AppleOAuthHelper.initialize(onSuccess, onError)

      expect(mockAppleID.auth.init).toHaveBeenCalledWith({
        clientId: 'com.drmaxmuscle.web',
        scope: 'name email',
        redirectURI: expect.stringContaining('/auth/apple/callback'),
        state: expect.any(String),
        nonce: expect.any(String),
        usePopup: true,
      })

      expect(onError).not.toHaveBeenCalled()
    })

    it('should initialize with custom config', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      await AppleOAuthHelper.initialize(onSuccess, onError, {
        scope: 'name email openid',
        state: 'custom_state_123',
        nonce: 'custom_nonce_456',
        usePopup: false,
      })

      expect(mockAppleID.auth.init).toHaveBeenCalledWith({
        clientId: 'com.drmaxmuscle.web',
        scope: 'name email openid',
        redirectURI: expect.stringContaining('/auth/apple/callback'),
        state: 'custom_state_123',
        nonce: 'custom_nonce_456',
        usePopup: false,
      })
    })

    it('should store state and nonce in session storage', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      await AppleOAuthHelper.initialize(onSuccess, onError, {
        state: 'test_state',
        nonce: 'test_nonce',
      })

      expect(window.sessionStorage.setItem).toHaveBeenCalledWith(
        'apple-oauth-state',
        JSON.stringify({ state: 'test_state', nonce: 'test_nonce' })
      )
    })

    it('should handle initialization error', async () => {
      delete (window as any).AppleID

      const onSuccess = vi.fn()
      const onError = vi.fn()

      await expect(
        AppleOAuthHelper.initialize(onSuccess, onError)
      ).rejects.toThrow()

      expect(onError).toHaveBeenCalledWith({
        code: 'provider_error',
        message: 'Failed to initialize Apple Sign-In',
        provider: 'apple',
        providerError: expect.any(Error),
        details: {
          originalError: 'Apple Sign-In SDK not available',
        },
      })
    })
  })

  describe('Sign In Flow', () => {
    it('should handle successful sign-in with user data', async () => {
      const mockToken = createMockToken(mockTokenPayload)
      const mockResponse: AppleSignInResponse = {
        authorization: {
          code: 'auth_code_123',
          id_token: mockToken,
          state: 'state_123',
        },
        user: {
          email: '<EMAIL>',
          name: {
            firstName: 'John',
            lastName: 'Doe',
          },
        },
      }

      mockAppleID.auth.signIn.mockResolvedValue(mockResponse)

      const onSuccess = vi.fn()
      const onError = vi.fn()

      await AppleOAuthHelper.signIn(onSuccess, onError)

      expect(mockAppleID.auth.signIn).toHaveBeenCalled()
      expect(onSuccess).toHaveBeenCalledWith({
        provider: 'apple',
        providerId: '001838.a1b2c3d4e5f6',
        email: '<EMAIL>',
        emailVerified: true,
        name: 'John Doe',
        firstName: 'John',
        lastName: 'Doe',
        pictureUrl: undefined,
        rawToken: mockToken,
        tokenPayload: expect.objectContaining({
          sub: '001838.a1b2c3d4e5f6',
          email: '<EMAIL>',
        }),
        metadata: {
          authorizationCode: 'auth_code_123',
          state: 'state_123',
          isPrivateEmail: true,
          realUserStatus: 2,
          userProvided: true,
        },
      })
      expect(onError).not.toHaveBeenCalled()
    })

    it('should handle successful sign-in for returning user', async () => {
      const mockToken = createMockToken(mockTokenPayload)
      const mockResponse: AppleSignInResponse = {
        authorization: {
          code: 'auth_code_123',
          id_token: mockToken,
          state: 'state_123',
        },
        // No user object for returning users
      }

      // Mock stored user data
      const storedUsers = {
        '001838.a1b2c3d4e5f6': {
          name: 'John Doe',
          firstName: 'John',
          lastName: 'Doe',
        },
      }
      ;(window.localStorage.getItem as any).mockReturnValue(
        JSON.stringify(storedUsers)
      )

      mockAppleID.auth.signIn.mockResolvedValue(mockResponse)

      const onSuccess = vi.fn()
      const onError = vi.fn()

      await AppleOAuthHelper.signIn(onSuccess, onError)

      expect(onSuccess).toHaveBeenCalledWith({
        provider: 'apple',
        providerId: '001838.a1b2c3d4e5f6',
        email: '<EMAIL>',
        emailVerified: true,
        name: 'John Doe',
        firstName: 'John',
        lastName: 'Doe',
        pictureUrl: undefined,
        rawToken: mockToken,
        tokenPayload: expect.any(Object),
        metadata: {
          authorizationCode: 'auth_code_123',
          state: 'state_123',
          isPrivateEmail: true,
          realUserStatus: 2,
          userProvided: false,
        },
      })
    })

    it('should handle user cancellation', async () => {
      const cancelError = new Error('popup_closed_by_user')
      mockAppleID.auth.signIn.mockRejectedValue(cancelError)

      const onSuccess = vi.fn()
      const onError = vi.fn()

      await AppleOAuthHelper.signIn(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith({
        code: 'user_cancelled',
        message: 'Sign-in cancelled by user',
        provider: 'apple',
        providerError: cancelError,
      })
      expect(onSuccess).not.toHaveBeenCalled()
    })

    it('should handle sign-in errors', async () => {
      const signInError = new Error('Network error')
      mockAppleID.auth.signIn.mockRejectedValue(signInError)

      const onSuccess = vi.fn()
      const onError = vi.fn()

      await AppleOAuthHelper.signIn(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith({
        code: 'oauth_failed',
        message: 'Failed to initiate Apple Sign-In',
        provider: 'apple',
        providerError: signInError,
        details: {
          originalError: 'Network error',
        },
      })
      expect(onSuccess).not.toHaveBeenCalled()
    })

    it('should initialize before sign-in if not ready', async () => {
      const mockToken = createMockToken(mockTokenPayload)
      const mockResponse: AppleSignInResponse = {
        authorization: {
          code: 'auth_code_123',
          id_token: mockToken,
          state: 'state_123',
        },
      }

      mockAppleID.auth.signIn.mockResolvedValue(mockResponse)

      const onSuccess = vi.fn()
      const onError = vi.fn()

      // Reset initialization state
      AppleOAuthHelper['isInitialized'] = false

      await AppleOAuthHelper.signIn(onSuccess, onError)

      expect(mockAppleID.auth.init).toHaveBeenCalled()
      expect(mockAppleID.auth.signIn).toHaveBeenCalled()
      expect(onSuccess).toHaveBeenCalled()
    })
  })

  describe('JWT Token Handling', () => {
    it('should decode valid JWT token', () => {
      const token = createMockToken(mockTokenPayload)
      const decoded = AppleOAuthHelper.decodeJWT(token)

      expect(decoded).toEqual(mockTokenPayload)
    })

    it('should reject invalid JWT format', () => {
      const invalidToken = 'invalid.token'
      const decoded = AppleOAuthHelper.decodeJWT(invalidToken)

      expect(decoded).toBeNull()
    })

    it('should reject token with invalid issuer', () => {
      const invalidPayload = {
        ...mockTokenPayload,
        iss: 'https://invalid.issuer.com',
      }
      const token = createMockToken(invalidPayload)
      const decoded = AppleOAuthHelper.decodeJWT(token)

      expect(decoded).toBeNull()
    })

    it('should reject token with invalid audience', () => {
      const invalidPayload = {
        ...mockTokenPayload,
        aud: 'com.invalid.app',
      }
      const token = createMockToken(invalidPayload)
      const decoded = AppleOAuthHelper.decodeJWT(token)

      expect(decoded).toBeNull()
    })

    it('should reject expired token', () => {
      const expiredPayload = {
        ...mockTokenPayload,
        exp: Math.floor(Date.now() / 1000) - 3600, // Expired 1 hour ago
      }
      const token = createMockToken(expiredPayload)
      const decoded = AppleOAuthHelper.decodeJWT(token)

      expect(decoded).toBeNull()
    })

    it('should reject token missing required fields', () => {
      const incompletePayload = {
        sub: '001838.a1b2c3d4e5f6',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
        // Missing iss and aud
      }
      const token = createMockToken(incompletePayload as any)
      const decoded = AppleOAuthHelper.decodeJWT(token)

      expect(decoded).toBeNull()
    })
  })

  describe('Redirect Callback Handling', () => {
    it('should handle valid redirect callback', () => {
      const mockToken = createMockToken(mockTokenPayload)

      // Mock URL parameters
      Object.defineProperty(window, 'location', {
        value: {
          search: `?code=auth_code_123&state=state_123&id_token=${mockToken}`,
        },
        writable: true,
      })

      // Mock stored state
      ;(window.sessionStorage.getItem as any).mockReturnValue(
        JSON.stringify({ state: 'state_123', nonce: 'nonce_123' })
      )

      const onSuccess = vi.fn()
      const onError = vi.fn()

      AppleOAuthHelper.handleRedirectCallback(onSuccess, onError)

      expect(onSuccess).toHaveBeenCalledWith({
        provider: 'apple',
        providerId: '001838.a1b2c3d4e5f6',
        email: '<EMAIL>',
        emailVerified: true,
        name: undefined,
        firstName: undefined,
        lastName: undefined,
        pictureUrl: undefined,
        rawToken: mockToken,
        tokenPayload: expect.any(Object),
        metadata: expect.any(Object),
      })
      expect(onError).not.toHaveBeenCalled()
      expect(window.sessionStorage.removeItem).toHaveBeenCalledWith(
        'apple-oauth-state'
      )
    })

    it('should reject callback with invalid state', () => {
      const mockToken = createMockToken(mockTokenPayload)

      // Mock URL parameters with wrong state
      Object.defineProperty(window, 'location', {
        value: {
          search: `?code=auth_code_123&state=wrong_state&id_token=${mockToken}`,
        },
        writable: true,
      })

      // Mock stored state
      ;(window.sessionStorage.getItem as any).mockReturnValue(
        JSON.stringify({ state: 'expected_state', nonce: 'nonce_123' })
      )

      const onSuccess = vi.fn()
      const onError = vi.fn()

      AppleOAuthHelper.handleRedirectCallback(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith({
        code: 'oauth_failed',
        message: 'Failed to process Apple redirect callback',
        provider: 'apple',
        providerError: expect.any(Error),
        details: {
          originalError: 'Invalid state parameter',
        },
      })
      expect(onSuccess).not.toHaveBeenCalled()
    })

    it('should handle missing parameters', () => {
      // Mock URL parameters without required fields
      Object.defineProperty(window, 'location', {
        value: {
          search: '?state=state_123',
        },
        writable: true,
      })

      const onSuccess = vi.fn()
      const onError = vi.fn()

      AppleOAuthHelper.handleRedirectCallback(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith({
        code: 'oauth_failed',
        message: 'Failed to process Apple redirect callback',
        provider: 'apple',
        providerError: expect.any(Error),
        details: {
          originalError: 'Missing authorization code or ID token',
        },
      })
      expect(onSuccess).not.toHaveBeenCalled()
    })
  })

  describe('Utility Methods', () => {
    it('should check availability correctly', () => {
      expect(AppleOAuthHelper.isAvailable()).toBe(true)

      delete (window as any).AppleID
      expect(AppleOAuthHelper.isAvailable()).toBe(false)
    })

    it('should check ready state correctly', () => {
      AppleOAuthHelper['isInitialized'] = true
      expect(AppleOAuthHelper.isReady()).toBe(true)

      AppleOAuthHelper['isInitialized'] = false
      expect(AppleOAuthHelper.isReady()).toBe(false)

      AppleOAuthHelper['isInitialized'] = true
      delete (window as any).AppleID
      expect(AppleOAuthHelper.isReady()).toBe(false)
    })
  })

  describe('User Data Storage', () => {
    it('should store user data on first authorization', async () => {
      const mockToken = createMockToken(mockTokenPayload)
      const mockResponse: AppleSignInResponse = {
        authorization: {
          code: 'auth_code_123',
          id_token: mockToken,
          state: 'state_123',
        },
        user: {
          email: '<EMAIL>',
          name: {
            firstName: 'John',
            lastName: 'Doe',
          },
        },
      }

      mockAppleID.auth.signIn.mockResolvedValue(mockResponse)

      const onSuccess = vi.fn()
      const onError = vi.fn()

      await AppleOAuthHelper.signIn(onSuccess, onError)

      expect(window.localStorage.setItem).toHaveBeenCalledWith(
        'apple-users',
        expect.stringContaining('001838.a1b2c3d4e5f6')
      )
    })

    it('should handle localStorage errors gracefully', async () => {
      // Mock localStorage to throw error
      ;(window.localStorage.setItem as any).mockImplementation(() => {
        throw new Error('QuotaExceededError')
      })

      const mockToken = createMockToken(mockTokenPayload)
      const mockResponse: AppleSignInResponse = {
        authorization: {
          code: 'auth_code_123',
          id_token: mockToken,
          state: 'state_123',
        },
        user: {
          email: '<EMAIL>',
          name: {
            firstName: 'John',
            lastName: 'Doe',
          },
        },
      }

      mockAppleID.auth.signIn.mockResolvedValue(mockResponse)

      const onSuccess = vi.fn()
      const onError = vi.fn()

      // Should not throw even if localStorage fails
      await AppleOAuthHelper.signIn(onSuccess, onError)

      expect(onSuccess).toHaveBeenCalled()
      expect(onError).not.toHaveBeenCalled()
    })
  })

  describe('Boolean Parsing', () => {
    it('should parse boolean values correctly', async () => {
      // Test with string boolean
      const tokenWithStringBool = createMockToken({
        ...mockTokenPayload,
        email_verified: 'true',
        is_private_email: 'false',
      })

      const mockResponse: AppleSignInResponse = {
        authorization: {
          code: 'auth_code_123',
          id_token: tokenWithStringBool,
          state: 'state_123',
        },
      }

      mockAppleID.auth.signIn.mockResolvedValue(mockResponse)

      const onSuccess = vi.fn()
      const onError = vi.fn()

      await AppleOAuthHelper.signIn(onSuccess, onError)

      const userData = onSuccess.mock.calls[0][0]
      expect(userData.emailVerified).toBe(true)
      expect(userData.metadata.isPrivateEmail).toBe(false)
    })
  })

  describe('Configuration', () => {
    it('should use NEXT_PUBLIC_APPLE_SERVICES_ID environment variable when available', () => {
      // Save original env value
      const originalEnv = process.env.NEXT_PUBLIC_APPLE_SERVICES_ID

      // Set test environment variable
      process.env.NEXT_PUBLIC_APPLE_SERVICES_ID = 'com.test.services.id'

      // Get client ID
      const clientId = AppleOAuthHelper.getClientId()

      // Should use environment variable
      expect(clientId).toBe('com.test.services.id')

      // Restore original env
      process.env.NEXT_PUBLIC_APPLE_SERVICES_ID = originalEnv
    })

    it('should use default client ID when environment variable is not set', () => {
      // Save original env value
      const originalEnv = process.env.NEXT_PUBLIC_APPLE_SERVICES_ID

      // Remove environment variable
      delete process.env.NEXT_PUBLIC_APPLE_SERVICES_ID

      // Get client ID
      const clientId = AppleOAuthHelper.getClientId()

      // Should use default
      expect(clientId).toBe('com.drmaxmuscle.web')

      // Restore original env
      if (originalEnv !== undefined) {
        process.env.NEXT_PUBLIC_APPLE_SERVICES_ID = originalEnv
      }
    })
  })
})
