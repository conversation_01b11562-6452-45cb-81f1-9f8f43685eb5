'use client'

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuthStore } from '@/stores/authStore'

interface AuthGuardProps {
  children: React.ReactNode
  redirectTo?: string
  excludePaths?: string[]
  loadingComponent?: React.ReactNode
}

export function AuthGuard({
  children,
  redirectTo = '/login',
  excludePaths = [],
  loadingComponent,
}: AuthGuardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated, isLoading, hasHydrated } = useAuthStore()

  useEffect(() => {
    // Skip protection for excluded paths
    if (excludePaths.includes(pathname)) {
      return
    }

    // Skip if already on login page
    if (pathname === '/login') {
      return
    }

    // Only redirect after hydration is complete and user is not authenticated
    if (hasHydrated && !isLoading && !isAuthenticated) {
      const returnUrl = encodeURIComponent(pathname)
      router.replace(`${redirectTo}?from=${returnUrl}`)
    }
  }, [
    isAuthenticated,
    isLoading,
    hasHydrated,
    pathname,
    router,
    redirectTo,
    excludePaths,
  ])

  // Show loading state only while hydrating
  // Don't block on isLoading if user is already authenticated
  if (!hasHydrated) {
    return (
      loadingComponent || (
        <div
          data-testid="auth-loading"
          className="flex items-center justify-center min-h-[100dvh]"
        >
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600" />
        </div>
      )
    )
  }

  // Don't render children if not authenticated (except for excluded paths or login page)
  if (
    !isAuthenticated &&
    !excludePaths.includes(pathname) &&
    pathname !== '/login'
  ) {
    return null
  }

  return children
}
