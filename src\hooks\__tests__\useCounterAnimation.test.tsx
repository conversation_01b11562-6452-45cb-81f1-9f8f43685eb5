import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useCounterAnimation } from '../useCounterAnimation'

describe('useCounterAnimation', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should not double count when targetValue remains the same', () => {
    // Track all displayValue updates
    const displayValues: number[] = []

    const { result, rerender } = renderHook(
      ({ targetValue }) => useCounterAnimation({ targetValue, duration: 1000 }),
      { initialProps: { targetValue: 100 } }
    )

    // Capture initial state
    displayValues.push(result.current.displayValue)

    // Start animation
    act(() => {
      vi.advanceTimersByTime(500) // Halfway through animation
    })
    displayValues.push(result.current.displayValue)

    // Rerender with same value (simulating a parent re-render)
    rerender({ targetValue: 100 })

    // Continue animation
    act(() => {
      vi.advanceTimersByTime(500) // Complete animation
    })
    displayValues.push(result.current.displayValue)

    // Verify no double counting
    // The value should progress from 0 to 100 smoothly without restarting
    expect(displayValues[0]).toBe(0) // Initial
    expect(displayValues[1]).toBeGreaterThan(0) // Mid-animation
    expect(displayValues[1]).toBeLessThan(100) // Still animating
    expect(displayValues[2]).toBe(100) // Final value

    // Ensure the animation didn't restart (no duplicate low values after high values)
    const hasDoubleCount = displayValues.some((val, idx) => {
      if (idx === 0) return false
      return val < displayValues[idx - 1] * 0.5 // Significant drop would indicate restart
    })

    expect(hasDoubleCount).toBe(false)
  })

  it('should animate smoothly without restarting on displayValue changes', () => {
    const { result } = renderHook(() =>
      useCounterAnimation({ targetValue: 100, duration: 1000 })
    )

    // Capture values during animation
    const values: number[] = []

    // Start animation
    act(() => {
      vi.advanceTimersByTime(100)
    })
    values.push(result.current.displayValue)

    act(() => {
      vi.advanceTimersByTime(200)
    })
    values.push(result.current.displayValue)

    act(() => {
      vi.advanceTimersByTime(300)
    })
    values.push(result.current.displayValue)

    act(() => {
      vi.advanceTimersByTime(400)
    })
    values.push(result.current.displayValue)

    // Values should only increase (no restart/drop)
    for (let i = 1; i < values.length; i++) {
      expect(values[i]).toBeGreaterThanOrEqual(values[i - 1])
    }

    // Final value should be the target
    expect(result.current.displayValue).toBe(100)
  })

  it('should handle rapid targetValue changes without double counting', () => {
    const { result, rerender } = renderHook(
      ({ targetValue }) => useCounterAnimation({ targetValue, duration: 500 }),
      { initialProps: { targetValue: 50 } }
    )

    // Start first animation
    act(() => {
      vi.advanceTimersByTime(250) // Halfway
    })

    const midValue = result.current.displayValue
    expect(midValue).toBeGreaterThan(0)
    expect(midValue).toBeLessThan(50)

    // Change target before first animation completes
    rerender({ targetValue: 100 })

    // Animation should smoothly transition to new target
    act(() => {
      vi.advanceTimersByTime(500) // Complete new animation
    })

    expect(result.current.displayValue).toBe(100)
  })
})
