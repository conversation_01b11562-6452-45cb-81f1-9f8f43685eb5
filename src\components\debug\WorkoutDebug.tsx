'use client'

import { useWorkout } from '@/hooks/useWorkout'
import { useAuthStore } from '@/stores/authStore'

export function WorkoutDebug() {
  const { isAuthenticated, hasHydrated } = useAuthStore()
  const {
    todaysWorkout,
    isLoadingWorkout,
    workoutError,
    hasInitialData,
    isLoadingFresh,
    expectedExerciseCount,
  } = useWorkout()

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-sm z-50 opacity-90">
      <h3 className="font-bold mb-2">Workout Debug Info</h3>
      <div className="space-y-1">
        <div>
          Auth: {isAuthenticated ? '✅' : '❌'} | Hydrated:{' '}
          {hasHydrated ? '✅' : '❌'}
        </div>
        <div>
          Loading: {isLoadingWorkout ? '🔄' : '✅'} | Fresh:{' '}
          {isLoadingFresh ? '🔄' : '✅'}
        </div>
        <div>Has Initial Data: {hasInitialData ? '✅' : '❌'}</div>
        <div>
          Today's Workout:{' '}
          {todaysWorkout ? `✅ (${todaysWorkout.length})` : '❌'}
        </div>
        <div>Expected Exercises: {expectedExerciseCount}</div>
        <div>Error: {workoutError ? '❌' : '✅'}</div>
        {workoutError && (
          <div className="text-red-300 text-xs mt-1">
            {typeof workoutError === 'string'
              ? workoutError
              : workoutError.message || 'Unknown error'}
          </div>
        )}
      </div>
    </div>
  )
}
