import { test, expect } from '@playwright/test'

async function loginUser(page: any) {
  // Go to the login page
  await page.goto('/login')

  // Fill in the login form
  await page
    .getByLabel('Email')
    .fill(process.env.TEST_EMAIL || '<EMAIL>')
  await page
    .getByLabel('Password')
    .fill(process.env.TEST_PASSWORD || 'Dr123456')

  // Click the login button
  await page.getByRole('button', { name: /login/i }).click()

  // Wait for navigation to workout page
  await page.waitForURL('/workout', { timeout: 10000 })
}

test.describe('Exercise Direct Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // Login first
    await loginUser(page)
  })

  test('should load exercise when navigating directly to exercise URL', async ({
    page,
  }) => {
    // Navigate to workout page first to ensure we have data
    await page.goto('/workout')

    // Wait for workout to load
    await page.waitForSelector('[data-testid="workout-exercises"]', {
      timeout: 10000,
    })

    // Get the first exercise link
    const firstExerciseLink = await page
      .locator('[data-testid="exercise-card"]')
      .first()
      .getAttribute('data-exercise-id')

    if (!firstExerciseLink) {
      throw new Error('No exercise found in workout')
    }

    // Navigate directly to the exercise URL
    await page.goto(`/workout/exercise/${firstExerciseLink}`)

    // Should not show loading indefinitely
    await expect(page.locator('text=Loading exercise...')).toBeVisible()

    // Should eventually show the exercise screen with set inputs
    await expect(page.locator('[data-testid="reps-input"]')).toBeVisible({
      timeout: 10000,
    })
    await expect(page.locator('[data-testid="weight-input"]')).toBeVisible()

    // Should show save button
    await expect(page.locator('button:has-text("Save Set")')).toBeVisible()
  })

  test('should redirect to workout page when invalid exercise ID is used', async ({
    page,
  }) => {
    // Navigate directly to an invalid exercise
    await page.goto('/workout/exercise/99999')

    // Should redirect to workout page
    await page.waitForURL('/workout', { timeout: 10000 })

    // Should show workout overview
    await expect(
      page.locator('[data-testid="workout-exercises"]')
    ).toBeVisible()
  })

  test('should handle case when no workout is available', async ({ page }) => {
    // Mock scenario where user has no workout
    await page.route('**/api/Workout/GetTodaysWorkout', async (route) => {
      await route.fulfill({
        status: 200,
        json: {
          StatusCode: 200,
          Result: [],
        },
      })
    })

    // Navigate directly to exercise
    await page.goto('/workout/exercise/123')

    // Should redirect to workout page
    await page.waitForURL('/workout', { timeout: 10000 })
  })
})
