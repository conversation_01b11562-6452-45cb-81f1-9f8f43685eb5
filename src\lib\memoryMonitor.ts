/**
 * Memory usage monitoring
 */
export function getMemoryUsage() {
  interface PerformanceWithMemory extends Performance {
    memory?: {
      usedJSHeapSize: number
      totalJSHeapSize: number
      jsHeapSizeLimit: number
    }
  }

  if (
    typeof window !== 'undefined' &&
    (window.performance as PerformanceWithMemory).memory
  ) {
    const { memory } = window.performance as PerformanceWithMemory
    if (memory) {
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        percentUsed: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
      }
    }
  }
  return null
}
