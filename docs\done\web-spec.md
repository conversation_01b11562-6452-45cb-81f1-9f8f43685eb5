# Dr. Muscle X - Comprehensive Specification

> Ultra-fast TypeScript/Next.js web application to replace the slow and buggy MAUI mobile app with a core workout loop experience.

## Project Overview

### Purpose

Create a lightning-fast web application that provides the core workout functionality of the Dr. Muscle X World's Fastest AI Personal Trainer, targeting existing mobile users who are frustrated with the current slow and buggy mobile experience. This web app will serve as a modern, maintainable alternative while leveraging the existing production API infrastructure.

### Success Criteria

- **Primary**: User can complete one full workout without bugs
- **Performance**: Measurably faster than the current mobile app
- **Quality**: Strict TDD/BDD implementation with comprehensive test coverage
- **Launch**: Soft launch to existing users initially

## Technical Architecture

### Frontend Stack (Mobile-First PWA)

- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS 3.0+ with mobile-first breakpoints
- **PWA Features**:
  - Service Worker for offline caching
  - Web App Manifest for home screen installation
  - Background sync for workout data
  - Push notifications for rest timers
- **State Management**:
  - Zustand (global state)
  - React Query/TanStack Query (API state/caching)
- **HTTP Client**: Axios with interceptors
- **Testing**: Vitest + React Testing Library + Playwright (Mobile E2E)
- **Deployment**: Vercel (recommended) or Azure Static Web Apps

### Project Structure

```
dr-muscle-web/
├── src/
│   ├── app/                 # Next.js App Router pages
│   ├── components/          # Reusable React components
│   ├── lib/                # Utilities and configurations
│   ├── hooks/              # Custom React hooks
│   ├── types/              # TypeScript type definitions
│   ├── api/                # API client and endpoints
│   ├── stores/             # Zustand stores
│   └── styles/             # Global styles and Tailwind config
├── tests/
│   ├── unit/               # Unit tests
│   ├── integration/        # Integration tests
│   └── e2e/               # End-to-end tests
├── docs/
│   ├── architecture.md     # Technical architecture
│   ├── status.md          # Project status tracking
│   └── testing.md         # Test documentation
├── spec.md                # This document
├── prompt_plan.md         # LLM implementation prompts
└── package.json
```

### Repository Strategy

- **Separate Repository**: New repo independent from MAUI codebase
- **API Integration**: Connect to existing production DrMuscle API
- **Type Generation**: Generate TypeScript types from API OpenAPI/Swagger spec
- **No Code Duplication**: API-first approach prevents business logic duplication

## Core User Flow (MVP)

### Authentication Flow

1. **Login Screen** with multiple auth options:
   - Email/Password (primary)
   - Google Sign-In
   - Apple Sign-In (web)
2. **Auto-redirect** to today's workout on successful auth
3. **Token Management** with secure storage and refresh handling

### Workout Execution Flow

_Mirroring the watch app UX exactly:_

1. **Today's Workout Screen**
   - Display scheduled workout name
   - Show exercise list preview
   - "Start Workout" button

2. **Exercise Execution Loop**
   - Display exercise name, target reps, target weight
   - Tap-to-edit input for actual reps/weight
   - Save set functionality
   - RIR capture (first work set only)
   - Rest timer between sets
   - Progress to next set/exercise

3. **Workout Completion**
   - Save complete workout to API
   - Display completion summary
   - Return to main screen

## API Integration

### Production API Configuration

- **Base URL**: `https://drmuscle.azurewebsites.net`
- **Authentication**: Bearer token authentication
- **Social Auth**:
  - Google OAuth: `************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com`
  - Apple Sign-In: Team ID `7AAXZ47995`, Bundle ID `com.drmaxmuscle.max`

### Existing API Endpoints

Leverage production DrMuscle API:

- **Authentication**: `/token` (initial auth), `/api/Account/Login`
- **Social Auth**: `/api/Account/RegisterWithApple`
- **Workouts**: `/api/Workout/GetUserWorkoutTemplateGroup`
- **Exercise Details**: `/api/Exercise/GetRecommendationForExercise`
- **Set Logging**: `/api/Set/SaveUserSet`
- **User Data**: Profile and settings endpoints

### Data Models

Use existing `DrMuscleWebApiSharedModel` types:

- `WorkoutTemplateGroupModel`
- `WorkoutTemplateModel`
- `ExerciseModel`
- `SetLogModel`
- `RecommendationModel`

### Type Generation Strategy

1. Generate TypeScript interfaces from existing API
2. Create API client with typed methods
3. Implement request/response interceptors for auth and error handling

## Performance Requirements

### Mobile-First Performance Targets

- **Initial Load**: < 2 seconds on 3G (Largest Contentful Paint)
- **Time to Interactive**: < 3 seconds on mid-range mobile devices
- **Tap Response**: < 50ms (mobile touch sensitivity)
- **Bundle Size**: < 150KB initial JavaScript (mobile bandwidth)
- **Workout Transitions**: < 100ms between sets/exercises
- **Battery Impact**: Minimal CPU usage during rest periods

### Optimization Strategies

- **Next.js App Router**: Server Components where possible
- **Code Splitting**: Route-based and component-based splitting
- **Image Optimization**: Next.js Image component with WebP
- **Caching**: Aggressive React Query caching for workout data
- **Prefetching**: Preload workout data and exercise details
- **Modern Browsers Only**: No legacy polyfills (Chrome 100+, Safari 15+, Firefox 100+, Edge 100+)

## Testing Strategy

### Test-Driven Development (TDD)

Following the watch app patterns with Given/When/Then scenarios:

#### Unit Tests (Vitest + React Testing Library)

- **Components**: Test rendering, user interactions, state changes
- **Hooks**: Test custom hooks in isolation
- **API Client**: Test request/response handling, error cases
- **Stores**: Test Zustand store logic and state updates
- **Utilities**: Test helper functions and data transformations

#### Integration Tests

- **API Integration**: Test full request/response cycles with MSW
- **Form Submissions**: Test complete user input flows
- **Authentication**: Test auth state management and token handling
- **Workout Flow**: Test multi-step workout execution

#### End-to-End Tests (Playwright)

- **Complete Workout**: Full user journey from login to workout completion
- **Error Scenarios**: Network failures, API errors, validation errors
- **Performance**: Verify speed requirements are met
- **Cross-Browser**: Test on Chrome, Safari, Firefox, Edge

### BDD Acceptance Criteria Pattern

Each feature must include detailed scenarios:

```
**Scenario:** Save Set Data Successfully
  Given the user is on the Set Screen with reps 10 and weight 100
  When the user taps the "Save Set" button
  Then the set data is saved to the API
  And the user sees a success confirmation
  And the app navigates to the next set or exercise
```

## Error Handling Strategy

### API Error Handling

- **Network Errors**: Retry logic with exponential backoff
- **Authentication Errors**: Auto-redirect to login, token refresh
- **Validation Errors**: Display field-specific error messages
- **Server Errors**: User-friendly messages with retry options

### Offline Handling

- **Detection**: Monitor online/offline status
- **Local Storage**: Queue failed API requests for retry
- **User Feedback**: Clear indication of offline state
- **Sync**: Auto-sync when connection restored

### User Experience

- **Loading States**: Skeleton screens and progress indicators
- **Error States**: Clear error messages with action buttons
- **Validation**: Real-time form validation with helpful messages
- **Accessibility**: ARIA labels and keyboard navigation (basic level for MVP)

## Security Considerations

### Authentication

- **JWT Tokens**: Secure storage in httpOnly cookies or secure localStorage
- **Token Refresh**: Automatic refresh before expiration
- **CSRF Protection**: Use existing API's CSRF strategy
- **Input Validation**: Client and server-side validation

### Data Protection

- **HTTPS Only**: Force secure connections
- **Sensitive Data**: Never log workout data or personal information
- **API Keys**: Environment variables only, never in client code

## Development Workflow

### Code Quality

- **TypeScript**: Strict mode with no `any` types
- **ESLint**: Airbnb configuration with custom rules
- **Prettier**: Consistent code formatting
- **Husky**: Pre-commit hooks for linting and testing

### Git Strategy

- **Feature Branches**: One branch per prompt/task
- **Conventional Commits**: `feat:`, `fix:`, `test:`, `docs:` prefixes
- **Atomic Commits**: Small, logical commits
- **PR Reviews**: Required before merging to main

### Deployment

- **Preview Deployments**: Automatic Vercel deployments for PRs
- **Production**: Deploy from main branch
- **Environment Variables**: Separate configs for dev/staging/prod
- **Monitoring**: Basic error tracking and performance monitoring

## Data Flow Architecture

### State Management

```
User Input → Zustand Store → React Query → API → Database
                ↓
           Component Re-render ← UI Updates
```

### Caching Strategy

- **React Query**: Cache workout data for 5 minutes
- **Browser Cache**: Cache static assets aggressively
- **API Cache**: Respect existing API cache headers

### Real-time Updates

- **Polling**: Check for updated workouts every 5 minutes
- **Optimistic Updates**: Update UI before API confirmation
- **Conflict Resolution**: Handle concurrent edits gracefully

## Browser Support

### Target Browsers (Mobile-First Priority)

- **iOS Safari**: 15+ (PRIMARY - majority of users)
- **Chrome Mobile**: 100+ (Android users)
- **Samsung Internet**: Latest 2 versions (Android alternative)
- **Desktop Chrome/Safari/Firefox/Edge**: 100+ (SECONDARY)

### Excluded Browsers

- Internet Explorer (all versions)
- Legacy mobile browsers
- Browsers older than 2022

### Progressive Enhancement

- **Core Functionality**: Works without JavaScript (where possible)
- **Enhanced Experience**: Rich interactions with JavaScript
- **Fallbacks**: Graceful degradation for unsupported features

## MVP Feature List

### Phase 1 (Mobile-First Core Workout Loop)

1. **Authentication System**
   - Email/password login optimized for mobile keyboards
   - Secure token management with biometric integration where available
   - Auto-redirect to workout

2. **PWA Installation & Offline**
   - Home screen installation prompt
   - Offline workout caching (service worker)
   - Background sync for workout data
   - App-like navigation (no browser chrome)

3. **Today's Workout Display**
   - Mobile-optimized workout preview
   - Large, thumb-friendly "Start Workout" button
   - Swipe gestures for exercise preview

4. **Exercise Execution (Touch-Optimized)**
   - Large tap targets for reps/weight editing
   - Number pad optimized inputs
   - Haptic feedback for successful saves
   - Swipe navigation between sets

5. **RIR (Reps in Reserve) Capture**
   - Mobile-optimized picker with large touch targets
   - Quick tap selection for 5 RIR options
   - Save RIR with haptic confirmation

6. **Rest Timer (Mobile-Enhanced)**
   - Full-screen countdown timer
   - Vibration and audio notifications
   - Background timer (PWA service worker)
   - Quick skip with confirmation

### Future Phases (Post-MVP)

- Workout history and analytics
- Exercise library and custom workouts
- Progress tracking and charts
- Social features and sharing
- Advanced settings and preferences

## Quality Assurance

### Performance Testing

- **Lighthouse**: Score 90+ for Performance, Accessibility, Best Practices
- **Core Web Vitals**: Pass all Google thresholds
- **Load Testing**: Simulate concurrent users
- **Bundle Analysis**: Monitor and optimize bundle size

### Accessibility (Basic)

- **Keyboard Navigation**: Tab order and focus management
- **Screen Readers**: Basic ARIA labels
- **Color Contrast**: WCAG AA compliance
- **Mobile Touch**: Minimum 44px touch targets

### Mobile-First Testing Strategy

- **PRIMARY Mobile**: iOS Safari, Chrome Mobile, Samsung Internet
- **Device Testing**: iPhone SE, iPhone 14, Galaxy S22, Pixel 7
- **Network Conditions**: 3G, LTE, WiFi with throttling
- **Touch Testing**: Tap targets, swipe gestures, pinch-to-zoom disabled
- **Screen Sizes**: 320px-430px PRIMARY, larger screens secondary
- **Battery Testing**: CPU usage during extended workout sessions

## Launch Strategy

### Soft Launch Plan

1. **Internal Testing**: Team and stakeholders
2. **Beta Users**: 50-100 existing mobile users
3. **Feedback Collection**: In-app feedback and analytics
4. **Iteration**: Address critical issues before full launch
5. **Gradual Rollout**: Increase user percentage over time

### Success Metrics

- **Completion Rate**: >90% successful workout completions
- **Performance**: 3x faster than mobile app
- **User Satisfaction**: >4.5/5 rating from beta users
- **Error Rate**: <1% fatal errors
- **Adoption**: 25% of beta users switch to web app

### Rollback Plan

- **Feature Flags**: Ability to disable features instantly
- **Previous Version**: Keep mobile app as fallback
- **Data Integrity**: Ensure no data loss during rollback
- **Communication**: Clear user communication about issues

## Implementation Timeline

Following the LLM codegen process with discrete, testable chunks:

### Week 1: Foundation

- Project setup and configuration
- Authentication system
- API client and type generation
- Basic routing and navigation

### Week 2: Core Features

- Today's workout display
- Exercise execution UI
- Set logging and data persistence
- RIR capture system

### Week 3: Polish & Testing

- Rest timer implementation
- Error handling and edge cases
- Performance optimization
- Comprehensive testing

### Week 4: Launch Preparation

- Beta user onboarding
- Monitoring and analytics
- Documentation and training
- Soft launch execution

---

## References

- **Watch App Implementation**: `DrMuscleWatch/docs/plan.md`
- **Testing Patterns**: `DrMuscleWatch/docs/todos/`
- **API Models**: `DrMuscleWebApiSharedModel/`
- **LLM Codegen Process**: `docs/llm-codegen-process.md`

_"The robots LOVE TDD. Seriously. They eat it up."_ - Harper Reed
