import { apiClient } from './client'
import { AxiosError } from 'axios'
import { retryApiCall, USER_INFO_RETRY_OPTIONS } from '@/utils/apiRetry'
import { userInfoPerformance } from '@/utils/userInfoPerformance'
import { logger } from '@/utils/logger'
import type { BooleanModel, MultiUnityWeight } from '@/types'

/**
 * User profile API methods
 * Handles user information and profile management
 */
export const userProfileApi = {
  /**
   * Get current user information
   * @returns User profile data
   */
  async getUserInfo(): Promise<{
    StatusCode?: number
    ErrorMessage?: string | null
    Result?: {
      Email: string
      FirstName?: string
      LastName?: string
      MassUnit?: string
      Gender?: string
      BodyWeight?: MultiUnityWeight
    }
    // Also support direct response (backward compatibility)
    Email?: string
    FirstName?: string
    LastName?: string
    MassUnit?: string
    Gender?: string
    BodyWeight?: MultiUnityWeight
  }> {
    // Start performance timing
    userInfoPerformance.trackUserInfoFetch()

    try {
      // Use retry logic with 10 second timeout
      const response = await retryApiCall(
        async () => {
          const res = await apiClient.post('/api/Account/GetUserInfoPyramid')

          // Track first byte received
          userInfoPerformance.trackUserInfoFirstByte()

          return res
        },
        USER_INFO_RETRY_OPTIONS,
        10000 // 10 second timeout
      )

      // Track successful API call
      userInfoPerformance.trackUserInfoComplete(false)
      userInfoPerformance.trackCacheOperation('miss') // API call means cache miss

      return response.data
    } catch (error) {
      // Track failed API call
      userInfoPerformance.trackCacheOperation('miss')

      // Log error in development
      if (process.env.NODE_ENV === 'development') {
        logger.error('[User Profile API] UserInfo error:', error)
      }

      // Handle 404 and 405 specifically - this endpoint might not exist or require special headers
      if (
        error instanceof Error &&
        'response' in error &&
        ((error as AxiosError).response?.status === 404 ||
          (error as AxiosError).response?.status === 405)
      ) {
        logger.warn(
          'UserInfo endpoint error. This API might not be available or requires different method.'
        )
        // Return minimal user data to prevent breaking the app
        throw new Error('UserInfo endpoint not available')
      }
      throw error
    }
  },

  /**
   * Update user profile information
   * @param userData Updated user data
   * @returns Boolean result
   */
  async updateUserInfo(userData: {
    FirstName?: string
    LastName?: string
    MassUnit?: string
    Gender?: string
    BodyWeight?: MultiUnityWeight
  }): Promise<BooleanModel> {
    const response = await apiClient.post<BooleanModel>(
      '/Account/UserInfo',
      userData
    )
    return response.data
  },

  /**
   * Change user password
   * @param oldPassword Current password
   * @param newPassword New password
   * @param confirmPassword Confirm new password
   * @returns Boolean result
   */
  async changePassword(
    oldPassword: string,
    newPassword: string,
    confirmPassword: string
  ): Promise<BooleanModel> {
    const response = await apiClient.post<BooleanModel>(
      '/Account/ChangePassword',
      {
        OldPassword: oldPassword,
        NewPassword: newPassword,
        ConfirmPassword: confirmPassword,
      }
    )
    return response.data
  },
}
