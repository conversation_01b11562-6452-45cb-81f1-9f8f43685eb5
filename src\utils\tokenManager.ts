// Token management utilities to avoid circular dependencies

let authToken: string | null = null

export const setToken = (token: string) => {
  authToken = token
}

export const getToken = (): string | null => {
  return authToken
}

export const clearToken = () => {
  authToken = null
}

// Callback to update API client when token changes
let onTokenChange: ((token: string | null) => void) | null = null

export const setTokenChangeCallback = (
  callback: (token: string | null) => void
) => {
  onTokenChange = callback
}

export const updateTokenInClient = (token: string | null) => {
  if (onTokenChange) {
    onTokenChange(token)
  }
}
