import { render } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { SuccessMessage } from '../SuccessMessage'

describe('SuccessMessage Showcase', () => {
  it('should render successfully in different configurations', () => {
    // Title only
    const { container: titleOnly } = render(
      <SuccessMessage title="Welcome back!" />
    )
    expect(titleOnly).toBeTruthy()

    // Title and message
    const { container: withMessage } = render(
      <SuccessMessage
        title="Login Successful"
        message="Loading your personalized workout..."
      />
    )
    expect(withMessage).toBeTruthy()

    // Dr. Muscle X branding
    const { container: branded } = render(
      <SuccessMessage
        title="Welcome to Dr. Muscle X"
        message="World's Fastest AI Personal Trainer"
      />
    )
    expect(branded).toBeTruthy()

    // Without autoplay
    const { container: noAutoplay } = render(
      <SuccessMessage title="Manual Animation" autoPlay={false} />
    )
    expect(noAutoplay).toBeTruthy()

    // With callback
    const { container: withCallback } = render(
      <SuccessMessage
        title="Success!"
        onAnimationComplete={() => {
          // Message animation complete!
        }}
      />
    )
    expect(withCallback).toBeTruthy()

    // Custom styling
    const { container: customStyle } = render(
      <SuccessMessage title="Styled Message" className="mt-8 mb-4" />
    )
    expect(customStyle).toBeTruthy()
  })
})
