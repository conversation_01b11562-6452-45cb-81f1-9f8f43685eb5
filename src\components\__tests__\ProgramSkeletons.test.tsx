import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import {
  ProgramHeaderSkeleton,
  ProgressRingSkeleton,
  ProgramStatsSkeleton,
  ProgramDescriptionSkeleton,
  ProgramOverviewSkeleton,
} from '../ProgramSkeletons'
import React from 'react'

describe('ProgramSkeletons', () => {
  describe('ProgramHeaderSkeleton', () => {
    it('should render skeleton with proper structure', () => {
      render(<ProgramHeaderSkeleton />)

      const skeleton = screen.getByTestId('program-header-skeleton')
      expect(skeleton).toBeInTheDocument()
      expect(skeleton).toHaveClass('animate-pulse')
    })

    it('should have proper dimensions matching real component', () => {
      render(<ProgramHeaderSkeleton />)

      const titleSkeleton = screen.getByTestId('header-title-skeleton')
      expect(titleSkeleton).toHaveClass('h-8') // Matches title height
      expect(titleSkeleton).toHaveClass('w-3/4') // Partial width for realism
    })

    it('should respect prefers-reduced-motion', () => {
      window.matchMedia = vi.fn().mockImplementation((query) => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      }))

      render(<ProgramHeaderSkeleton />)

      const skeleton = screen.getByTestId('program-header-skeleton')
      expect(skeleton).not.toHaveClass('animate-pulse')
    })
  })

  describe('ProgressRingSkeleton', () => {
    it('should render circular skeleton', () => {
      render(<ProgressRingSkeleton />)

      const skeleton = screen.getByTestId('progress-ring-skeleton')
      expect(skeleton).toBeInTheDocument()
      expect(skeleton).toHaveClass('rounded-full')
    })

    it('should support different sizes', () => {
      const { rerender } = render(<ProgressRingSkeleton size="sm" />)
      expect(screen.getByTestId('progress-ring-skeleton')).toHaveClass(
        'w-24',
        'h-24'
      )

      rerender(<ProgressRingSkeleton size="md" />)
      expect(screen.getByTestId('progress-ring-skeleton')).toHaveClass(
        'w-32',
        'h-32'
      )

      rerender(<ProgressRingSkeleton size="lg" />)
      expect(screen.getByTestId('progress-ring-skeleton')).toHaveClass(
        'w-40',
        'h-40'
      )
    })

    it('should have inner circle for ring effect', () => {
      render(<ProgressRingSkeleton />)

      const innerCircle = screen.getByTestId('progress-ring-inner-skeleton')
      expect(innerCircle).toBeInTheDocument()
      expect(innerCircle).toHaveClass('rounded-full')
    })
  })

  describe('ProgramStatsSkeleton', () => {
    it('should render grid of skeleton cards', () => {
      render(<ProgramStatsSkeleton />)

      const grid = screen.getByTestId('program-stats-skeleton')
      expect(grid).toHaveClass('grid')
      expect(grid).toHaveClass('grid-cols-2')
      expect(grid).toHaveClass('md:grid-cols-4')
    })

    it('should render 4 skeleton cards', () => {
      render(<ProgramStatsSkeleton />)

      const cards = screen.getAllByTestId('stat-card-skeleton')
      expect(cards).toHaveLength(4)
    })

    it('should match StatCard dimensions', () => {
      render(<ProgramStatsSkeleton />)

      const cards = screen.getAllByTestId('stat-card-skeleton')
      cards.forEach((card) => {
        expect(card).toHaveClass('min-h-[100px]')
        expect(card).toHaveClass('p-4')
      })
    })
  })

  describe('ProgramDescriptionSkeleton', () => {
    it('should render multiple line skeletons', () => {
      render(<ProgramDescriptionSkeleton />)

      const lines = screen.getAllByTestId('description-line-skeleton')
      expect(lines.length).toBeGreaterThanOrEqual(3) // At least 3 lines
    })

    it('should have varying line widths for realism', () => {
      render(<ProgramDescriptionSkeleton />)

      const lines = screen.getAllByTestId('description-line-skeleton')
      const widths = new Set()

      lines.forEach((line) => {
        const classes = line.className.split(' ')
        const widthClass = classes.find((c) => c.startsWith('w-'))
        if (widthClass) widths.add(widthClass)
      })

      expect(widths.size).toBeGreaterThan(1) // Multiple different widths
    })

    it('should support custom line count', () => {
      render(<ProgramDescriptionSkeleton lines={5} />)

      const lines = screen.getAllByTestId('description-line-skeleton')
      expect(lines).toHaveLength(5)
    })
  })

  describe('ProgramOverviewSkeleton', () => {
    it('should render all skeleton components', () => {
      render(<ProgramOverviewSkeleton />)

      expect(screen.getByTestId('program-header-skeleton')).toBeInTheDocument()
      expect(screen.getByTestId('progress-ring-skeleton')).toBeInTheDocument()
      expect(screen.getByTestId('program-stats-skeleton')).toBeInTheDocument()
      expect(
        screen.getByTestId('program-description-skeleton')
      ).toBeInTheDocument()
    })

    it('should have proper layout structure', () => {
      render(<ProgramOverviewSkeleton />)

      const container = screen.getByTestId('program-overview-skeleton')
      expect(container).toHaveClass('space-y-6') // Vertical spacing
    })

    it('should include CTA button skeleton', () => {
      render(<ProgramOverviewSkeleton />)

      const ctaButton = screen.getByTestId('cta-button-skeleton')
      expect(ctaButton).toBeInTheDocument()
      expect(ctaButton).toHaveClass('h-14') // Touch-friendly height
      expect(ctaButton).toHaveClass('w-full')
    })

    it('should handle safe area insets', () => {
      render(<ProgramOverviewSkeleton />)

      const container = screen.getByTestId('program-overview-skeleton')
      expect(container).toHaveClass('pb-safe-bottom')
    })
  })

  describe('Animation and transitions', () => {
    it('should have consistent animation timing', () => {
      render(<ProgramOverviewSkeleton />)

      const animatedElements = screen.getAllByTestId(/skeleton/)
      animatedElements.forEach((element) => {
        if (element.classList.contains('animate-pulse')) {
          // Check that animations are present
          expect(element).toHaveClass('animate-pulse')
        }
      })
    })

    it('should not animate with reduced motion preference', () => {
      window.matchMedia = vi.fn().mockImplementation((query) => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      }))

      render(<ProgramOverviewSkeleton />)

      const animatedElements = screen.getAllByTestId(/skeleton/)
      animatedElements.forEach((element) => {
        expect(element).not.toHaveClass('animate-pulse')
      })
    })
  })
})
