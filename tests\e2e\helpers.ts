import { Page } from '@playwright/test'

/**
 * E2E test helper functions for common operations
 */

/**
 * Login helper function
 */
export async function login(page: Page, email: string, password: string) {
  // Fill in login form
  await page.fill('input[type="email"]', email)
  await page.fill('input[type="password"]', password)
  
  // Click login button
  await page.click('button[type="submit"]')
  
  // Wait for navigation or success indication
  await page.waitForURL(/\/(program|workout)/, { timeout: 10000 })
}

/**
 * Wait for navigation with retry logic
 */
export async function waitForNavigation(page: Page, path: string, options?: { timeout?: number }) {
  const timeout = options?.timeout || 10000
  await page.waitForURL(path, { timeout })
}

/**
 * Check if element is visible and interactable
 */
export async function waitForElement(page: Page, selector: string, options?: { timeout?: number }) {
  const timeout = options?.timeout || 5000
  const element = page.locator(selector)
  await element.waitFor({ state: 'visible', timeout })
  return element
}

/**
 * Mock API responses
 */
export async function mockAPIResponse(page: Page, endpoint: string, response: any, status = 200) {
  await page.route(`**/${endpoint}`, route => {
    route.fulfill({
      status,
      contentType: 'application/json',
      body: JSON.stringify(response)
    })
  })
}

/**
 * Clear all mocks
 */
export async function clearAllMocks(page: Page) {
  await page.unroute('**/*')
}

/**
 * Wait for loading to complete
 */
export async function waitForLoadingComplete(page: Page) {
  // Wait for any loading indicators to disappear
  await page.waitForSelector('.animate-pulse', { state: 'hidden', timeout: 5000 }).catch(() => {})
  await page.waitForSelector('[data-testid="loading-spinner"]', { state: 'hidden', timeout: 5000 }).catch(() => {})
  
  // Wait for network idle
  await page.waitForLoadState('networkidle')
}

/**
 * Check page performance metrics
 */
export async function getPerformanceMetrics(page: Page) {
  return await page.evaluate(() => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    const paint = performance.getEntriesByType('paint')
    
    return {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
      firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
    }
  })
}

/**
 * Simulate pull to refresh gesture
 */
export async function pullToRefresh(page: Page) {
  // Get viewport dimensions
  const viewport = page.viewportSize()
  if (!viewport) return
  
  const centerX = viewport.width / 2
  
  // Simulate pull down gesture
  await page.mouse.move(centerX, 100)
  await page.mouse.down()
  await page.mouse.move(centerX, 400, { steps: 10 })
  await page.mouse.up()
  
  // Wait for refresh to complete
  await page.waitForTimeout(500)
}

/**
 * Check for accessibility issues
 */
export async function checkAccessibility(page: Page) {
  const accessibilityReport = await page.accessibility.snapshot()
  
  return {
    hasReport: !!accessibilityReport,
    headingCount: await page.getByRole('heading').count(),
    buttonCount: await page.getByRole('button').count(),
    linkCount: await page.getByRole('link').count(),
  }
}

/**
 * Get animation frame rate
 */
export async function measureAnimationFPS(page: Page, duration = 1000): Promise<number> {
  const fps = await page.evaluate((duration) => {
    return new Promise<number>((resolve) => {
      let frameCount = 0
      const startTime = performance.now()
      
      function countFrame() {
        frameCount++
        if (performance.now() - startTime < duration) {
          requestAnimationFrame(countFrame)
        } else {
          resolve(frameCount)
        }
      }
      
      requestAnimationFrame(countFrame)
    })
  }, duration)
  
  return fps
}

/**
 * Test data generators
 */
export const testData = {
  program: {
    id: 1,
    name: 'Beginner Full Body',
    description: 'A comprehensive program for beginners',
    category: 'Beginner',
    totalDays: 84,
    currentDay: 15,
    workoutsCompleted: 10,
    startDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
    imageUrl: null
  },
  
  programProgress: {
    percentage: 18,
    daysCompleted: 15,
    totalWorkouts: 36,
    currentWeek: 3
  },
  
  programStats: {
    averageWorkoutTime: 45,
    totalVolume: 125000,
    personalRecords: 5,
    consecutiveWeeks: 2,
    lastWorkoutDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
  },
  
  loginSuccess: {
    success: true,
    token: 'test-jwt-token',
    user: {
      email: '<EMAIL>',
      name: 'Test User'
    }
  }
}