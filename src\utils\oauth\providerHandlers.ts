/**
 * OAuth Provider Handlers
 *
 * Handles provider-specific OAuth logic
 */

import type {
  OAuthProvider,
  OAuthUserData,
  OAuthSuccessCallback,
  OAuthErrorCallback,
} from '@/types/oauth'
import type { LoginSuccessResult } from '@/types'
import { FirebaseOAuthHelper } from './firebaseOAuth'
import { AppleOAuthHelper } from './appleOAuth'
import { oauthApi } from '@/api/oauth'
import { useAuthStore } from '@/stores/authStore'
import { logger } from '../logger'

/**
 * Provider configuration for authentication
 */
interface ProviderAuthConfig {
  email: string
  name: string
  bodyWeight: string
  massUnit: string
}

/**
 * OAuth Provider Handlers class
 */
export class OAuthProviderHandlers {
  /**
   * Handle Google OAuth authentication
   * @param userData - User data from Google
   * @returns Login success result
   */
  static async handleGoogleAuth(
    userData: OAuthUserData
  ): Promise<LoginSuccessResult> {
    // Extract first name from full name (mobile app sends first word only)
    const name = userData.name?.split(' ')[0] || ''

    const config: ProviderAuthConfig = {
      email: userData.email,
      name,
      bodyWeight: '', // Can be empty for OAuth
      massUnit: '', // Can be empty for OAuth
    }

    logger.debug('Processing Google OAuth', {
      email: config.email,
      hasName: !!config.name,
    })

    return oauthApi.googleLogin(userData.rawToken, config)
  }

  /**
   * Handle Apple OAuth authentication
   * @param userData - User data from Apple
   * @returns Login success result
   */
  static async handleAppleAuth(
    userData: OAuthUserData
  ): Promise<LoginSuccessResult> {
    // For Apple, we need the user ID (not the authorization code)
    const appleUserId = userData.providerId
    if (!appleUserId) {
      throw new Error('Apple User ID is required')
    }

    // Extract first name from full name if available
    const name = userData.name?.split(' ')[0] || ''

    const config: ProviderAuthConfig = {
      email: userData.email || '', // Email can be empty for Apple after first auth
      name,
      bodyWeight: '', // Can be empty for OAuth
      massUnit: '', // Can be empty for OAuth
    }

    logger.debug('Processing Apple OAuth', {
      hasEmail: !!config.email,
      hasName: !!config.name,
    })

    return oauthApi.appleLogin(appleUserId, config)
  }

  /**
   * Process OAuth authentication based on provider
   * @param userData - User data from OAuth provider
   * @param provider - OAuth provider type
   * @returns Login success result
   */
  static async processAuth(
    userData: OAuthUserData,
    provider: OAuthProvider
  ): Promise<LoginSuccessResult> {
    let loginResult: LoginSuccessResult

    switch (provider) {
      case 'google':
        loginResult = await this.handleGoogleAuth(userData)
        break

      case 'apple':
        loginResult = await this.handleAppleAuth(userData)
        break

      default:
        throw new Error(`Unsupported OAuth provider: ${provider}`)
    }

    // Store authentication data
    useAuthStore.getState().setAuth(loginResult)

    logger.info('OAuth authentication successful', {
      provider,
      email: userData.email,
    })

    return loginResult
  }

  /**
   * Initialize OAuth provider
   * @param provider - OAuth provider to initialize
   * @param onSuccess - Success callback
   * @param onError - Error callback
   */
  static async initializeProvider(
    provider: OAuthProvider,
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback
  ): Promise<void> {
    // Initialize based on provider - hybrid approach like mobile app
    switch (provider) {
      case 'google':
        // Google uses Firebase
        await FirebaseOAuthHelper.initialize()
        break

      case 'apple':
        // Apple uses direct SDK
        await AppleOAuthHelper.initialize(onSuccess, onError)
        break

      default:
        throw new Error(`Unsupported OAuth provider: ${provider}`)
    }

    logger.info(`OAuth provider ${provider} initialized successfully`)
  }

  /**
   * Sign in with OAuth provider
   * @param provider - OAuth provider
   * @param onSuccess - Success callback
   * @param onError - Error callback
   */
  static async signInWithProvider(
    provider: OAuthProvider,
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback
  ): Promise<void> {
    switch (provider) {
      case 'google':
        // Google uses Firebase
        await FirebaseOAuthHelper.signInWithGoogle(onSuccess, onError)
        break

      case 'apple':
        // Apple uses direct SDK (like mobile app)
        await AppleOAuthHelper.signIn(onSuccess, onError)
        break

      default:
        throw new Error(`Unsupported OAuth provider: ${provider}`)
    }
  }

  /**
   * Check if OAuth provider is available
   * @param provider - OAuth provider to check
   * @returns Whether provider is available
   */
  static isProviderAvailable(provider: OAuthProvider): boolean {
    switch (provider) {
      case 'google':
        // Google uses Firebase, check if window exists
        return typeof window !== 'undefined'

      case 'apple':
        // Apple uses direct SDK
        return AppleOAuthHelper.isAvailable()

      default:
        return false
    }
  }

  /**
   * Check if OAuth provider is ready (initialized)
   * @param provider - OAuth provider to check
   * @returns Whether provider is ready
   */
  static isProviderReady(provider: OAuthProvider): boolean {
    switch (provider) {
      case 'google':
        // Google uses Firebase
        return FirebaseOAuthHelper.isReady()

      case 'apple':
        // Apple uses direct SDK
        return AppleOAuthHelper.isReady()

      default:
        return false
    }
  }

  /**
   * Get display name for OAuth provider
   * @param provider - OAuth provider
   * @returns Display name
   */
  static getProviderDisplayName(provider: OAuthProvider): string {
    switch (provider) {
      case 'google':
        return 'Google'

      case 'apple':
        return 'Apple'

      default:
        return provider
    }
  }
}
