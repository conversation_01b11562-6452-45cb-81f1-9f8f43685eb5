import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { UserAvatar } from '../UserAvatar'

describe('UserAvatar', () => {
  it('renders user name initial when available', () => {
    render(
      <UserAvatar user={{ name: '<PERSON>', email: '<EMAIL>' }} />
    )
    expect(screen.getByText('J')).toBeInTheDocument()
  })

  it('renders email initial when name is not available', () => {
    render(<UserAvatar user={{ email: '<EMAIL>' }} />)
    expect(screen.getByText('T')).toBeInTheDocument()
  })

  it('renders user icon when no name or email available', () => {
    const { container } = render(<UserAvatar user={{}} />)
    const svg = container.querySelector('svg')
    expect(svg).toBeInTheDocument()
  })

  it('renders user icon when user is null', () => {
    const { container } = render(<UserAvatar user={null} />)
    const svg = container.querySelector('svg')
    expect(svg).toBeInTheDocument()
  })

  it('applies correct size classes', () => {
    const { container } = render(<UserAvatar user={null} size="sm" />)
    expect(container.firstChild).toHaveClass('w-8', 'h-8')
  })

  it('converts initial to uppercase', () => {
    render(<UserAvatar user={{ name: 'john' }} />)
    expect(screen.getByText('J')).toBeInTheDocument()
  })

  it('handles empty strings gracefully', () => {
    const { container } = render(<UserAvatar user={{ name: '', email: '' }} />)
    const svg = container.querySelector('svg')
    expect(svg).toBeInTheDocument()
  })
})
