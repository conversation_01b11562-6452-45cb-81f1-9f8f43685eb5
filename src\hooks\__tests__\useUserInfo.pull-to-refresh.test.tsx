import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useUserInfo } from '../useUserInfo'
import { authApi } from '@/api/auth'
import { useAuthStore } from '@/stores/authStore'
import React from 'react'

// Mock dependencies
vi.mock('@/stores/authStore')
vi.mock('@/api/auth')
vi.mock('@/utils/userInfoPerformance', () => ({
  userInfoPerformance: {
    startSession: vi.fn().mockReturnValue('test-session-id'),
    endSession: vi.fn(),
    recordCacheMetrics: vi.fn(),
  },
}))
vi.mock('@/utils/apiRetry', () => ({
  shouldClearCacheOnError: vi.fn().mockReturnValue(false),
}))

// Create wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
  return wrapper
}

describe('useUserInfo - Pull to Refresh', () => {
  const mockUser = { email: '<EMAIL>' }
  const mockUpdateUser = vi.fn()
  const mockSetCachedUserInfo = vi.fn()
  const mockGetCachedUserInfo = vi.fn()
  const mockIsCacheStale = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup auth store mock
    vi.mocked(useAuthStore).mockReturnValue({
      user: mockUser,
      updateUser: mockUpdateUser,
      isAuthenticated: true,
      getCachedUserInfo: mockGetCachedUserInfo,
      setCachedUserInfo: mockSetCachedUserInfo,
      isCacheStale: mockIsCacheStale,
      clearUserInfoCache: vi.fn(),
    } as any)

    // Default cache state
    mockGetCachedUserInfo.mockReturnValue(null)
    mockIsCacheStale.mockReturnValue(true)
  })

  it('should provide refetch function', () => {
    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    expect(result.current.refetch).toBeDefined()
    expect(typeof result.current.refetch).toBe('function')
  })

  it('should refetch user info when refetch is called', async () => {
    // Mock API response
    const mockUserInfo = {
      Result: {
        FirstName: 'John',
        LastName: 'Doe',
        Email: '<EMAIL>',
      },
    }
    vi.mocked(authApi.getUserInfo).mockResolvedValue(mockUserInfo)

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    // Wait for initial fetch
    await waitFor(() => {
      expect(authApi.getUserInfo).toHaveBeenCalledTimes(1)
    })

    // Clear mocks to track refetch
    vi.clearAllMocks()

    // Trigger refetch
    await result.current.refetch()

    // Should call API again
    await waitFor(() => {
      expect(authApi.getUserInfo).toHaveBeenCalledTimes(1)
    })

    // Should update user and cache
    expect(mockUpdateUser).toHaveBeenCalledWith({
      firstName: 'John',
      lastName: 'Doe',
    })
    expect(mockSetCachedUserInfo).toHaveBeenCalled()
  })

  it('should work with pull-to-refresh pattern', async () => {
    // Simulate cached data
    const cachedData = {
      FirstName: 'Cached',
      LastName: 'User',
    }
    mockGetCachedUserInfo.mockReturnValue(cachedData)
    mockIsCacheStale.mockReturnValue(false)

    // Mock fresh data
    const freshData = {
      Result: {
        FirstName: 'Fresh',
        LastName: 'Data',
        Email: '<EMAIL>',
      },
    }
    vi.mocked(authApi.getUserInfo).mockResolvedValue(freshData)

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    // Should still fetch fresh data even with cache
    await waitFor(() => {
      expect(authApi.getUserInfo).toHaveBeenCalled()
    })

    // Wait for the data to be processed
    await waitFor(() => {
      expect(mockUpdateUser).toHaveBeenCalled()
    })

    // Clear previous calls
    mockUpdateUser.mockClear()
    vi.mocked(authApi.getUserInfo).mockClear()

    // Simulate pull-to-refresh
    const refreshPromise = result.current.refetch()

    // Should be fetching
    await waitFor(() => {
      expect(authApi.getUserInfo).toHaveBeenCalled()
    })

    await refreshPromise

    // Should update with fresh data
    await waitFor(() => {
      expect(mockUpdateUser).toHaveBeenCalledWith({
        firstName: 'Fresh',
        lastName: 'Data',
      })
    })
  })

  it('should handle refetch errors gracefully', async () => {
    const error = new Error('Network error')
    vi.mocked(authApi.getUserInfo).mockRejectedValue(error)

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    // Wait for initial error
    await waitFor(() => {
      expect(result.current.error).toBeTruthy()
    })

    // Try to refetch
    const refetchResult = await result.current.refetch()

    // Should return error state
    expect(refetchResult.isError).toBe(true)
    expect(refetchResult.error).toBe(error)
  })

  it('should maintain loading states during refetch', async () => {
    // Setup cached data
    const cachedData = {
      FirstName: 'Cached',
      LastName: 'User',
    }
    mockGetCachedUserInfo.mockReturnValue(cachedData)
    mockIsCacheStale.mockReturnValue(false)

    // First, return data immediately
    vi.mocked(authApi.getUserInfo).mockResolvedValue({
      Result: { FirstName: 'Test', LastName: 'User' },
    })

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    // Wait for initial load
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    // Setup a pending promise for refetch
    let resolvePromise: (value: any) => void
    const refetchPromise = new Promise((resolve) => {
      resolvePromise = resolve
    })
    vi.mocked(authApi.getUserInfo).mockReturnValue(refetchPromise as any)

    // Trigger refetch
    const refetchAction = result.current.refetch()

    // Check background loading state
    await waitFor(() => {
      // Should not show loading if we have cached data
      expect(result.current.isLoading).toBe(false)
      expect(result.current.isBackgroundLoading).toBe(true)
    })

    // Resolve the refetch
    resolvePromise!({
      Result: { FirstName: 'Fresh', LastName: 'Data' },
    })

    await refetchAction

    // Should be done loading
    await waitFor(() => {
      expect(result.current.isBackgroundLoading).toBe(false)
    })
  })
})
