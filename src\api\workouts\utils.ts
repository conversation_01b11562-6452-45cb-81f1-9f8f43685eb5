/**
 * Workout API - Utility Functions
 *
 * Common utilities for workout API operations
 */

import { AxiosResponse } from 'axios'
import { logger } from '@/utils/logger'
import type { WorkoutTemplateModel } from '@/types'
import type { WorkoutTemplateApiResponse } from './types'

/**
 * Normalize workouts array by transforming 'Exercices' to 'Exercises' if needed
 * @param workouts - Array of workout templates
 * @returns Normalized array of workout templates
 */
function normalizeWorkouts(
  workouts: WorkoutTemplateApiResponse[]
): WorkoutTemplateModel[] {
  // Log the structure of the first workout to verify field names
  if (workouts.length > 0 && workouts[0]) {
    logger.log('GetUserWorkout: First workout structure:', {
      workout: workouts[0],
      keys: Object.keys(workouts[0]),
      hasExercises: 'Exercises' in workouts[0],
      hasExercices: 'Exercices' in workouts[0],
    })
  }

  // Transform 'Exercices' to 'Exercises' if needed
  return workouts.map((workout: WorkoutTemplateApiResponse) => {
    if ('Exercices' in workout && !('Exercises' in workout)) {
      const { Exercices, ...rest } = workout
      return {
        ...rest,
        Exercises: Exercices,
      }
    }
    return workout
  }) as WorkoutTemplateModel[]
}

/**
 * Handle workout API response and normalize the data
 * @param response - Axios response from workout API
 * @returns Array of workout templates
 */
export function handleWorkoutResponse(
  response: AxiosResponse
): WorkoutTemplateModel[] {
  // Log raw response for debugging
  logger.log('GetUserWorkout raw response:', {
    dataType: typeof response.data,
    isArray: Array.isArray(response.data),
    hasStatusCode: response.data && 'StatusCode' in response.data,
    hasResult: response.data && 'Result' in response.data,
    hasData: response.data && 'Data' in response.data,
    responseKeys: response.data ? Object.keys(response.data) : [],
    actualData: response.data,
  })

  // Handle both wrapped and direct responses (consistent with other API calls)
  if (response.data && typeof response.data === 'object') {
    // Check for wrapped response with StatusCode and Result/result
    if ('StatusCode' in response.data && response.data.StatusCode === 200) {
      // Check for both uppercase 'Result' and lowercase 'result'
      const result = response.data.Result || response.data.result
      if (result !== undefined) {
        // Result might be null, which is valid
        if (result === null) {
          logger.warn('GetUserWorkout: Result is null')
          return []
        }
        if (Array.isArray(result)) {
          return normalizeWorkouts(result)
        }
        // Check if result has a Workouts property (nested structure)
        if (typeof result === 'object' && result.Workouts) {
          logger.log('GetUserWorkout: Found nested Workouts array in result')
          if (Array.isArray(result.Workouts)) {
            return normalizeWorkouts(result.Workouts)
          }
        }
        // If it's a single workout object, wrap it in an array
        if (
          typeof result === 'object' &&
          (result.Exercices || result.Exercises)
        ) {
          logger.log(
            'GetUserWorkout: Single workout returned, wrapping in array'
          )
          return normalizeWorkouts([result])
        }
        logger.error(
          'GetUserWorkout: Result is not an array or valid workout object',
          { result }
        )
        return []
      }
    }

    // Check for ApiResponse structure with Data
    if ('Data' in response.data) {
      const data = response.data.Data
      // Data might be null, which is valid
      if (data === null || data === undefined) {
        logger.warn('GetUserWorkout: Data is null or undefined')
        return []
      }
      if (Array.isArray(data)) {
        return normalizeWorkouts(data)
      }
      logger.error('GetUserWorkout: Data is not an array', { data })
      return []
    }
  }

  // If direct array response
  if (Array.isArray(response.data)) {
    return normalizeWorkouts(response.data)
  }

  // If no StatusCode, assume it's a direct response
  if (
    response.data &&
    typeof response.data === 'object' &&
    !('StatusCode' in response.data)
  ) {
    // Check if it's a single workout object
    if (
      'Exercices' in response.data ||
      'Exercises' in response.data ||
      'Id' in response.data
    ) {
      logger.log('GetUserWorkout: Direct workout object, wrapping in array', {
        data: response.data,
        hasExercices: 'Exercices' in response.data,
        hasExercises: 'Exercises' in response.data,
        exercicesCount: response.data.Exercices?.length || 0,
        exercisesCount: response.data.Exercises?.length || 0,
      })
      return normalizeWorkouts([response.data])
    }

    // Check for other possible property names
    const possibleKeys = ['workouts', 'Workouts', 'data', 'items']
    // eslint-disable-next-line no-restricted-syntax
    for (const key of possibleKeys) {
      if (key in response.data && Array.isArray(response.data[key])) {
        logger.log(`GetUserWorkout: Found workouts under '${key}' property`)
        return normalizeWorkouts(response.data[key])
      }
    }

    // Last resort: if it has array-like properties
    const actualKey = Object.keys(response.data).find((k) =>
      Array.isArray(response.data[k])
    )
    if (actualKey) {
      logger.log(
        `GetUserWorkout: Found array data under '${actualKey}' property`,
        {
          key: actualKey,
          data: response.data[actualKey],
        }
      )
      return normalizeWorkouts(response.data[actualKey])
    }
  }

  logger.error('GetUserWorkout: Unable to parse response format', {
    response: response.data,
  })
  return []
}
