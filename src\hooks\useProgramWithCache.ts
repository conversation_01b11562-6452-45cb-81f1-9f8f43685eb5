import { useEffect, useState, useCallback } from 'react'
import { useQuery } from '@tanstack/react-query'
import { programApi } from '@/api/program'
import { useAuthStore } from '@/stores/authStore'
import { useProgramStore } from '@/stores/programStore'
import type { ProgramModel } from '@/types'
import { useProgramProgressWithCache } from './useProgramProgressWithCache'
import { useProgramStatsWithCache } from './useProgramStatsWithCache'

/**
 * Cache-enhanced hook to fetch current user's program information
 * Shows cached data immediately while fetching fresh data in background
 */
export function useProgramWithCache() {
  const { isAuthenticated } = useAuthStore()
  const {
    getCachedProgram,
    setCachedProgram,
    isProgramCacheStale,
    setIsLoadingProgram,
    setIsRefreshingProgram,
    setProgramError,
    hasHydrated,
  } = useProgramStore()

  // Local state for optimistic data
  const [optimisticProgram, setOptimisticProgram] =
    useState<ProgramModel | null>(null)
  const [hasInitialData, setHasInitialData] = useState(false)

  // Load cached data immediately after hydration
  useEffect(() => {
    if (hasHydrated) {
      const cached = getCachedProgram()
      if (cached) {
        setOptimisticProgram(cached)
        setHasInitialData(true)
      }
    }
  }, [hasHydrated, getCachedProgram])

  // Determine if we should fetch fresh data
  const shouldFetch =
    isAuthenticated && (isProgramCacheStale() || !optimisticProgram)
  const isBackgroundRefresh = hasInitialData && isProgramCacheStale()

  // React Query for API calls
  const {
    data: freshProgram,
    isLoading: isQueryLoading,
    error,
    refetch,
  } = useQuery<ProgramModel | null>({
    queryKey: ['program'],
    queryFn: async () => {
      // Set appropriate loading state
      if (!hasInitialData) {
        setIsLoadingProgram(true)
      } else {
        setIsRefreshingProgram(true)
      }

      try {
        const data = await programApi.getUserProgram()
        return data
      } finally {
        setIsLoadingProgram(false)
        setIsRefreshingProgram(false)
      }
    },
    enabled: shouldFetch,
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  })

  // Update cache and optimistic data when fresh data arrives
  useEffect(() => {
    if (freshProgram !== undefined) {
      setCachedProgram(freshProgram)
      setOptimisticProgram(freshProgram)
      setHasInitialData(true)
    }
  }, [freshProgram, setCachedProgram])

  // Update error state
  useEffect(() => {
    setProgramError(error instanceof Error ? error : null)
  }, [error, setProgramError])

  return {
    program: optimisticProgram,
    isLoading: !hasInitialData && isQueryLoading,
    isRefreshing: isBackgroundRefresh && isQueryLoading,
    error,
    refetch,
    hasInitialData,
  }
}

/**
 * Combined cache-enhanced hook that fetches all program data
 * Provides instant display of cached data with background refresh
 */
export function useProgramDataWithCache() {
  const {
    program,
    isLoading: isLoadingProgram,
    isRefreshing: isRefreshingProgram,
    error: programError,
    hasInitialData: hasInitialProgram,
  } = useProgramWithCache()

  const {
    progress,
    isLoading: isLoadingProgress,
    isRefreshing: isRefreshingProgress,
    error: progressError,
    hasInitialData: hasInitialProgress,
  } = useProgramProgressWithCache()

  const {
    stats,
    isLoading: isLoadingStats,
    isRefreshing: isRefreshingStats,
    error: statsError,
    hasInitialData: hasInitialStats,
  } = useProgramStatsWithCache()

  // Preload all data in parallel
  const prefetchAll = useCallback(async () => {
    await Promise.all([
      programApi.getUserProgram(),
      programApi.getProgramProgress(),
      programApi.getProgramStats(),
    ])
  }, [])

  return {
    program,
    progress,
    stats,
    isLoading: isLoadingProgram || isLoadingProgress || isLoadingStats,
    isRefreshing:
      isRefreshingProgram || isRefreshingProgress || isRefreshingStats,
    isLoadingProgram,
    isLoadingProgress,
    isLoadingStats,
    isRefreshingProgram,
    isRefreshingProgress,
    isRefreshingStats,
    error: programError || progressError || statsError,
    hasData: !!program,
    hasInitialData: hasInitialProgram || hasInitialProgress || hasInitialStats,
    prefetchAll,
  }
}

// Re-export the split hooks for backward compatibility
export { useProgramProgressWithCache } from './useProgramProgressWithCache'
export { useProgramStatsWithCache } from './useProgramStatsWithCache'
export { useProgramPrefetch } from './useProgramPrefetch'
