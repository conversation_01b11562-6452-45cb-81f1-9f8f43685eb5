import { describe, it, expect } from 'vitest'
import type { Nullable, DeepPartial, ValueOf, ArrayElement } from '@/types/utils'
import {
  isApiResponse,
  isLoginSuccessResult,
  isErrorResponse,
  isMultiUnityWeight,
  hasProperty,
  isNonEmptyArray,
  isValidEmail,
  isValidWeight,
  parseApiError,
  createMultiUnityWeight,
} from '@/types/utils'

describe('Utility Types', () => {
  describe('Type Helpers', () => {
    it('should correctly type Nullable', () => {
      const nullableString: Nullable<string> = 'hello'
      const nullString: Nullable<string> = null

      expect(nullableString).toBe('hello')
      expect(nullString).toBeNull()
    })

    it('should correctly type DeepPartial', () => {
      interface ComplexType {
        name: string
        details: {
          age: number
          address: {
            street: string
            city: string
          }
        }
      }

      const partial: DeepPartial<ComplexType> = {
        details: {
          address: {
            city: 'New York',
          },
        },
      }

      expect(partial.details?.address?.city).toBe('New York')
      expect(partial.details?.age).toBeUndefined()
    })

    it('should correctly type ValueOf', () => {
      const obj = {
        a: 1,
        b: 'hello',
        c: true,
      } as const

      type Values = ValueOf<typeof obj>
      const value: Values = 'hello'

      expect(value).toBe('hello')
    })

    it('should correctly type ArrayElement', () => {
      const arr = [1, 'hello', true] as const
      type Element = ArrayElement<typeof arr>
      const element: Element = 'hello'

      expect(element).toBe('hello')
    })
  })

  describe('Type Guards', () => {
    it('should validate ApiResponse structure', () => {
      const validResponse = {
        Result: true,
        Code: 200,
        Data: { id: 1 },
      }

      const invalidResponse = {
        result: true, // wrong case
        code: 200,
      }

      expect(isApiResponse(validResponse)).toBe(true)
      expect(isApiResponse(invalidResponse)).toBe(false)
    })

    it('should validate LoginSuccessResult', () => {
      const validLogin = {
        access_token: 'token123',
        token_type: 'Bearer',
        expires_in: 86400,
        userName: '<EMAIL>',
        issued: '2024-01-01T00:00:00Z',
        expires: '2024-01-02T00:00:00Z',
      }

      const invalidLogin = {
        token: 'token123', // wrong field name
        type: 'Bearer',
      }

      expect(isLoginSuccessResult(validLogin)).toBe(true)
      expect(isLoginSuccessResult(invalidLogin)).toBe(false)
    })

    it('should validate MultiUnityWeight', () => {
      const validWeight = {
        Lb: 135,
        Kg: 61.23,
      }

      const invalidWeight = {
        pounds: 135, // wrong field name
        kilograms: 61.23,
      }

      expect(isMultiUnityWeight(validWeight)).toBe(true)
      expect(isMultiUnityWeight(invalidWeight)).toBe(false)
    })

    it('should check for error responses', () => {
      const errorResponse = {
        Result: false,
        Code: 400,
        ErrorMessage: 'Invalid request',
      }

      const successResponse = {
        Result: true,
        Code: 200,
        Data: { id: 1 },
      }

      expect(isErrorResponse(errorResponse)).toBe(true)
      expect(isErrorResponse(successResponse)).toBe(false)
    })
  })

  describe('Validation Functions', () => {
    it('should validate email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('<EMAIL>')).toBe(true)
      expect(isValidEmail('invalid@')).toBe(false)
      expect(isValidEmail('@example.com')).toBe(false)
      expect(isValidEmail('no-at-sign')).toBe(false)
    })

    it('should validate weight values', () => {
      expect(isValidWeight(0)).toBe(false)
      expect(isValidWeight(-10)).toBe(false)
      expect(isValidWeight(45)).toBe(true)
      expect(isValidWeight(500)).toBe(true)
      expect(isValidWeight(1001)).toBe(false) // too heavy
      expect(isValidWeight(0.5)).toBe(false) // too light
    })

    it('should check non-empty arrays', () => {
      expect(isNonEmptyArray([1, 2, 3])).toBe(true)
      expect(isNonEmptyArray(['a'])).toBe(true)
      expect(isNonEmptyArray([])).toBe(false)
      expect(isNonEmptyArray(null as any)).toBe(false)
      expect(isNonEmptyArray(undefined as any)).toBe(false)
    })

    it('should check object properties', () => {
      const obj = { name: 'John', age: 30 }

      expect(hasProperty(obj, 'name')).toBe(true)
      expect(hasProperty(obj, 'age')).toBe(true)
      expect(hasProperty(obj, 'email')).toBe(false)
      expect(hasProperty(null, 'name')).toBe(false)
    })
  })

  describe('Utility Functions', () => {
    it('should create MultiUnityWeight from pounds', () => {
      const weight = createMultiUnityWeight(135, 'lbs')

      expect(weight.Lb).toBe(135)
      expect(weight.Kg).toBeCloseTo(61.23, 2)
    })

    it('should create MultiUnityWeight from kilograms', () => {
      const weight = createMultiUnityWeight(61.23, 'kg')

      expect(weight.Kg).toBe(61.23)
      expect(weight.Lb).toBeCloseTo(135, 0)
    })

    it('should parse API errors correctly', () => {
      const apiError = {
        Result: false,
        Code: 400,
        ErrorMessage: 'Invalid email format',
      }

      const httpError = {
        status: 401,
        statusText: 'Unauthorized',
        data: { message: 'Token expired' },
      }

      const unknownError = 'Something went wrong'

      expect(parseApiError(apiError)).toEqual({
        message: 'Invalid email format',
        code: 400,
        details: apiError,
      })

      expect(parseApiError(httpError)).toEqual({
        message: 'Token expired',
        code: 401,
        details: httpError,
      })

      expect(parseApiError(unknownError)).toEqual({
        message: 'Something went wrong',
        code: 0,
        details: unknownError,
      })
    })
  })
}) 