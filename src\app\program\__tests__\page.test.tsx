import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { renderWithProviders } from '../../../../tests/test-utils'
import { useRouter } from 'next/navigation'
import ProgramPage from '../page'
import { useProgramWithCalculationsAndCache } from '@/hooks/useProgramWithCalculationsAndCache'
import {
  useProgram,
  useProgramProgress,
  useProgramStats,
} from '@/hooks/useProgram'
import { useWelcomeCard } from '@/hooks/useWelcomeCard'
import type {
  ProgramModel,
  ProgramProgress,
  ProgramStats,
  UserProgramModel,
} from '@/types'
import React from 'react'

// Type for the hook return value
interface UseProgramWithCalculationsAndCacheReturn {
  program: UserProgramModel | null | undefined
  progress: ProgramProgress | null | undefined
  stats: ProgramStats | null | undefined
  additionalMetrics?: {
    daysRemaining: number
    completionDate: string | null
    missedWorkouts: number
    workoutsPerWeek: number
  }
  isLoading: boolean
  isRefreshing: boolean
  hasInitialData: boolean
  error: Error | null
  refetch: () => Promise<void>
  hasPartialDataError: boolean
  loadingStates?: {
    programLoading: boolean
    progressLoading: boolean
    statsLoading: boolean
    programRefreshing: boolean
    progressRefreshing: boolean
    statsRefreshing: boolean
  }
}

// Mock the hooks and router
vi.mock('next/navigation')
vi.mock('@/hooks/useProgramWithCalculationsAndCache')
vi.mock('@/hooks/useProgram')
vi.mock('@/hooks/useWelcomeCard')
vi.mock('@/stores/authStore', () => ({
  useAuthStore: vi.fn(() => ({
    isAuthenticated: true,
    isLoading: false,
    hasHydrated: true,
    user: { id: 1, email: '<EMAIL>' },
  })),
}))

const mockRouter = {
  push: vi.fn(),
  back: vi.fn(),
  prefetch: vi.fn(),
}

const mockProgram: ProgramModel = {
  id: 1,
  name: 'Beginner Strength Program',
  description:
    'Build a solid foundation with this comprehensive strength program designed for beginners.',
  category: 'Strength',
  totalDays: 84,
  currentDay: 15,
  workoutsCompleted: 12,
  totalWorkouts: 36,
  startDate: '2024-01-01',
}

const mockProgress: ProgramProgress = {
  percentage: 25,
  daysCompleted: 21,
  totalWorkouts: 36,
  currentWeek: 3,
  workoutsThisWeek: 2,
  remainingWorkouts: 24,
}

const mockStats: ProgramStats = {
  averageWorkoutTime: 45.5,
  totalVolume: 125000,
  personalRecords: 8,
  consecutiveWeeks: 3,
  lastWorkoutDate: '2024-01-15',
  totalWorkoutsCompleted: 12,
}

describe('ProgramPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue(
      mockRouter as ReturnType<typeof useRouter>
    )
    // Mock useWelcomeCard to return default values
    vi.mocked(useWelcomeCard).mockReturnValue({
      welcomeCardData: null,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })
  })

  describe('Loading states', () => {
    it('should show skeleton components while loading', () => {
      vi.mocked(useProgramWithCalculationsAndCache).mockReturnValue({
        program: undefined,
        progress: undefined,
        stats: undefined,
        isLoading: true,
        isRefreshing: false,
        error: null,
        refetch: vi.fn(),
        hasPartialDataError: false,
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      expect(
        screen.getByTestId('program-overview-skeleton')
      ).toBeInTheDocument()
    })

    it('should show all skeleton components in correct order', () => {
      vi.mocked(useProgramWithCalculationsAndCache).mockReturnValue({
        program: undefined,
        progress: undefined,
        stats: undefined,
        isLoading: true,
        isRefreshing: false,
        error: null,
        refetch: vi.fn(),
        hasPartialDataError: false,
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      expect(screen.getByTestId('program-header-skeleton')).toBeInTheDocument()
      expect(screen.getByTestId('progress-ring-skeleton')).toBeInTheDocument()
      expect(screen.getByTestId('program-stats-skeleton')).toBeInTheDocument()
      expect(
        screen.getByTestId('program-description-skeleton')
      ).toBeInTheDocument()
    })
  })

  describe('Successful data display', () => {
    beforeEach(() => {
      vi.mocked(useProgramWithCalculationsAndCache).mockReturnValue({
        program: mockProgram,
        progress: mockProgress,
        stats: mockStats,
        isLoading: false,
        isRefreshing: false,
        error: null,
        refetch: vi.fn(),
        hasPartialDataError: false,
      } as UseProgramWithCalculationsAndCacheReturn)
    })

    it('should render all program components with data', () => {
      renderWithProviders(<ProgramPage />)

      // Program header
      expect(
        screen.getByRole('heading', { name: mockProgram.name })
      ).toBeInTheDocument()
      expect(screen.getByText(mockProgram.category)).toBeInTheDocument()

      // Progress ring
      expect(
        screen.getByLabelText(`${mockProgress.percentage}% progress`)
      ).toBeInTheDocument()

      // Stats grid
      expect(screen.getByText('Total Workouts')).toBeInTheDocument()
      expect(screen.getByText('Days Completed')).toBeInTheDocument()
      expect(screen.getByText('Current Week')).toBeInTheDocument()
      expect(screen.getByText('Completion')).toBeInTheDocument()

      // Description
      expect(screen.getByText(mockProgram.description)).toBeInTheDocument()

      // CTA button
      expect(
        screen.getByRole('button', { name: /continue to workout/i })
      ).toBeInTheDocument()
    })

    it('should have proper mobile layout structure', () => {
      renderWithProviders(<ProgramPage />)

      const container = screen.getByTestId('program-overview-page')
      expect(container).toHaveClass('h-full')
      expect(container).toHaveClass('flex')
      expect(container).toHaveClass('flex-col')
    })

    it('should have floating CTA button', () => {
      renderWithProviders(<ProgramPage />)

      const ctaContainer = screen.getByTestId('floating-cta-container')
      expect(ctaContainer).toHaveClass('fixed')
      expect(ctaContainer).toHaveClass('bottom-6')
      expect(ctaContainer).toHaveClass('right-6')
      expect(ctaContainer).toHaveClass('z-50')
    })
  })

  describe('Navigation', () => {
    beforeEach(() => {
      vi.mocked(useProgramWithCalculationsAndCache).mockReturnValue({
        program: mockProgram,
        progress: mockProgress,
        stats: mockStats,
        isLoading: false,
        isRefreshing: false,
        error: null,
        refetch: vi.fn(),
        hasPartialDataError: false,
      } as UseProgramWithCalculationsAndCacheReturn)
    })

    it('should navigate to workout page when CTA is clicked', async () => {
      renderWithProviders(<ProgramPage />)

      const ctaButton = screen.getByRole('button', {
        name: /continue to workout/i,
      })
      fireEvent.click(ctaButton)

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/workout')
      })
    })

    it('should prefetch workout page on mount', () => {
      renderWithProviders(<ProgramPage />)

      expect(mockRouter.prefetch).toHaveBeenCalledWith('/workout')
    })

    it('should have touch-friendly CTA button', () => {
      renderWithProviders(<ProgramPage />)

      const ctaButton = screen.getByRole('button', {
        name: /continue to workout/i,
      })
      expect(ctaButton).toHaveClass('min-h-[56px]') // Touch-friendly height
      expect(ctaButton).toHaveClass('w-full')
    })
  })

  describe('Error states', () => {
    it('should show error state when program fetch fails', () => {
      const error = new Error('Failed to load program')
      vi.mocked(useProgram).mockReturnValue({
        program: undefined,
        isLoading: false,
        error,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramProgress).mockReturnValue({
        progress: undefined,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramStats).mockReturnValue({
        stats: undefined,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      expect(
        screen.getByRole('heading', { name: /failed to load program/i })
      ).toBeInTheDocument()
      expect(
        screen.getByRole('button', { name: /try again/i })
      ).toBeInTheDocument()
    })

    it('should show network timeout error', () => {
      const error = new Error('Network request timeout')
      error.name = 'TimeoutError'
      vi.mocked(useProgram).mockReturnValue({
        program: undefined,
        isLoading: false,
        error,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramProgress).mockReturnValue({
        progress: undefined,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramStats).mockReturnValue({
        stats: undefined,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      expect(screen.getByText(/connection timeout/i)).toBeInTheDocument()
      expect(screen.getByText(/check your internet/i)).toBeInTheDocument()
    })

    it('should handle invalid program data gracefully', () => {
      // Program with missing required fields
      const invalidProgram = {
        id: 1,
        name: null, // Missing name
        description: '',
        category: 'Strength',
      } as UserProgramModel

      vi.mocked(useProgram).mockReturnValue({
        program: invalidProgram,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramProgress).mockReturnValue({
        progress: mockProgress,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramStats).mockReturnValue({
        stats: mockStats,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      expect(screen.getByText(/invalid program data/i)).toBeInTheDocument()
    })

    it('should handle partial data loading failures', () => {
      // Program loads successfully but stats fail
      vi.mocked(useProgram).mockReturnValue({
        program: mockProgram,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramProgress).mockReturnValue({
        progress: mockProgress,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramStats).mockReturnValue({
        stats: undefined,
        isLoading: false,
        error: new Error('Stats failed to load'),
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      // Should still show program data
      expect(
        screen.getByRole('heading', { name: mockProgram.name })
      ).toBeInTheDocument()
      // Stats should show error or default state
      expect(screen.getByText(/stats unavailable/i)).toBeInTheDocument()
    })

    it('should retry fetching when retry button is clicked', async () => {
      const refetch = vi.fn()
      vi.mocked(useProgram).mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error('Failed'),
        refetch,
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramProgress).mockReturnValue({
        progress: undefined,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramStats).mockReturnValue({
        stats: undefined,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      const retryButton = screen.getByRole('button', { name: /try again/i })
      fireEvent.click(retryButton)

      expect(refetch).toHaveBeenCalled()
    })

    it('should show no program state when user has no program', () => {
      vi.mocked(useProgram).mockReturnValue({
        program: null,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramProgress).mockReturnValue({
        progress: null,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramStats).mockReturnValue({
        stats: null,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      expect(screen.getByText(/no program assigned/i)).toBeInTheDocument()
      expect(
        screen.getByRole('button', { name: /browse programs/i })
      ).toBeInTheDocument()
    })
  })

  describe('Edge cases', () => {
    it('should handle completed program (100% progress)', () => {
      const completedProgress: ProgramProgress = {
        ...mockProgress,
        percentage: 100,
        remainingWorkouts: 0,
        daysCompleted: 84,
      }

      vi.mocked(useProgram).mockReturnValue({
        program: mockProgram,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramProgress).mockReturnValue({
        progress: completedProgress,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramStats).mockReturnValue({
        stats: mockStats,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      expect(screen.getByText(/congratulations/i)).toBeInTheDocument()
      expect(
        screen.getByRole('button', { name: /view achievements/i })
      ).toBeInTheDocument()
    })

    it('should handle expired program access', () => {
      const error = new Error('Program access expired')
      error.name = 'ExpiredError'
      vi.mocked(useProgram).mockReturnValue({
        program: undefined,
        isLoading: false,
        error,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramProgress).mockReturnValue({
        progress: undefined,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramStats).mockReturnValue({
        stats: undefined,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      expect(screen.getByText(/program access expired/i)).toBeInTheDocument()
      expect(
        screen.getByRole('button', { name: /renew access/i })
      ).toBeInTheDocument()
    })

    it('should handle program data changes mid-session', async () => {
      // Initial program data
      vi.mocked(useProgramWithCalculationsAndCache).mockReturnValue({
        program: mockProgram,
        progress: mockProgress,
        stats: mockStats,
        isLoading: false,
        isRefreshing: false,
        error: null,
        refetch: vi.fn(),
        hasPartialDataError: false,
      } as UseProgramWithCalculationsAndCacheReturn)

      const { rerender } = renderWithProviders(<ProgramPage />)

      // Program data changes
      const updatedProgram = {
        ...mockProgram,
        name: 'Advanced Strength Program',
        currentDay: 30,
      }

      vi.mocked(useProgram).mockReturnValue({
        program: updatedProgram,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)

      rerender(<ProgramPage />)

      await waitFor(() => {
        expect(
          screen.getByRole('heading', { name: updatedProgram.name })
        ).toBeInTheDocument()
      })
    })

    it('should handle missing non-critical fields gracefully', () => {
      const programWithMissingFields = {
        ...mockProgram,
        description: null,
        imageUrl: undefined,
      }

      vi.mocked(useProgram).mockReturnValue({
        program: programWithMissingFields,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramProgress).mockReturnValue({
        progress: mockProgress,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)
      vi.mocked(useProgramStats).mockReturnValue({
        stats: mockStats,
        isLoading: false,
        error: null,
        refetch: vi.fn(),
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      // Should still render without crashing
      expect(
        screen.getByRole('heading', { name: mockProgram.name })
      ).toBeInTheDocument()
      // Description component should handle null gracefully
      expect(screen.queryByText(/null/)).not.toBeInTheDocument()
    })
  })

  describe('PWA features', () => {
    beforeEach(() => {
      vi.mocked(useProgramWithCalculationsAndCache).mockReturnValue({
        program: mockProgram,
        progress: mockProgress,
        stats: mockStats,
        isLoading: false,
        isRefreshing: false,
        error: null,
        refetch: vi.fn(),
        hasPartialDataError: false,
      } as UseProgramWithCalculationsAndCacheReturn)
    })

    it('should handle safe area insets', () => {
      renderWithProviders(<ProgramPage />)

      // pb-safe-bottom class was removed when converting to floating button
      // The floating button handles safe area automatically
      const ctaContainer = screen.getByTestId('floating-cta-container')
      expect(ctaContainer).toBeInTheDocument()
    })

    it('should have proper scroll behavior', () => {
      renderWithProviders(<ProgramPage />)

      const scrollContainer = screen.getByTestId('scroll-container')
      expect(scrollContainer).toHaveClass('overflow-y-auto')

      // Check that the inner div has padding for floating CTA
      const innerDiv = scrollContainer.querySelector('.pb-24')
      expect(innerDiv).toBeInTheDocument()
    })
  })

  describe('Data prefetching', () => {
    it('should start prefetching workout data when progress is above 0%', () => {
      vi.mocked(useProgramWithCalculationsAndCache).mockReturnValue({
        program: mockProgram,
        progress: mockProgress,
        stats: mockStats,
        isLoading: false,
        isRefreshing: false,
        error: null,
        refetch: vi.fn(),
        hasPartialDataError: false,
      } as UseProgramWithCalculationsAndCacheReturn)

      renderWithProviders(<ProgramPage />)

      // Should prefetch workout route
      expect(mockRouter.prefetch).toHaveBeenCalledWith('/workout')
    })
  })

  describe('Performance', () => {
    it('should not re-render unnecessarily', () => {
      const { rerender } = renderWithProviders(<ProgramPage />)

      const initialRenderCount = 1
      let renderCount = initialRenderCount

      // Track renders
      function TrackedPage() {
        renderCount++
        return <ProgramPage />
      }

      rerender(<TrackedPage />)
      rerender(<TrackedPage />)

      // Should only render once per rerender
      expect(renderCount).toBe(initialRenderCount + 2)
    })
  })
})
