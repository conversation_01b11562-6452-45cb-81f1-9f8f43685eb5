'use client'

import React from 'react'
import { <PERSON><PERSON>, Theme<PERSON><PERSON>ider, useTheme } from '@/design-system'

function ThemeSwitcher() {
  const { theme, setTheme } = useTheme()

  return (
    <div className="flex flex-col sm:flex-row gap-2 p-4 bg-bg-secondary rounded-lg">
      <button
        onClick={() => setTheme('subtle-depth')}
        className={`px-4 py-2 rounded ${
          theme === 'subtle-depth'
            ? 'bg-brand-primary text-text-inverse'
            : 'bg-bg-tertiary text-text-primary'
        }`}
      >
        Subtle Depth
      </button>
      <button
        onClick={() => setTheme('flat-bold')}
        className={`px-4 py-2 ${
          theme === 'flat-bold'
            ? 'bg-brand-primary text-text-inverse'
            : 'bg-bg-tertiary text-text-primary'
        }`}
      >
        Flat Bold
      </button>
      <button
        onClick={() => setTheme('glassmorphism')}
        className={`px-4 py-2 rounded-2xl ${
          theme === 'glassmorphism'
            ? 'bg-brand-primary text-text-inverse'
            : 'bg-bg-tertiary text-text-primary'
        }`}
      >
        Glassmorphism
      </button>
      <button
        onClick={() => setTheme('ultra-minimal')}
        className={`px-4 py-2 ${
          theme === 'ultra-minimal'
            ? 'bg-brand-primary text-text-inverse'
            : 'bg-bg-tertiary text-text-primary'
        }`}
      >
        Ultra-Minimal
      </button>
    </div>
  )
}

function DesignSystemShowcase() {
  const { theme } = useTheme()

  return (
    <div className="min-h-screen bg-bg-primary text-text-primary transition-colors duration-300">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <header className="mb-12">
          <h1 className="text-4xl sm:text-5xl font-heading font-bold mb-4">
            Dr. Muscle X Design System
          </h1>
          <p className="text-lg text-text-secondary">
            Premium, cutting-edge design system for the world&apos;s fastest AI
            personal trainer
          </p>
          <p className="text-sm text-text-tertiary mt-2">
            Current theme:{' '}
            <span className="text-brand-primary font-medium">{theme}</span>
          </p>
        </header>

        {/* Theme Switcher */}
        <section className="mb-12">
          <h2 className="text-2xl font-heading font-semibold mb-4">
            Theme Variations
          </h2>
          <ThemeSwitcher />
        </section>

        {/* Typography */}
        <section className="mb-12">
          <h2 className="text-2xl font-heading font-semibold mb-4">
            Typography
          </h2>
          <div className="space-y-4 p-6 bg-bg-secondary rounded-lg">
            <h1 className="text-4xl font-heading">
              Heading 1 - Build Muscle Fast
            </h1>
            <h2 className="text-3xl font-heading">
              Heading 2 - AI-Powered Training
            </h2>
            <h3 className="text-2xl font-heading">
              Heading 3 - Progressive Overload
            </h3>
            <p className="text-base">
              Body text - Dr. Muscle X uses cutting-edge AI to create
              personalized workout plans that adapt to your progress in
              real-time.
            </p>
            <p className="text-sm text-text-secondary">
              Secondary text - Trusted by over 100,000 users worldwide
            </p>
            <p className="text-xs text-text-tertiary">
              Tertiary text - © 2024 Dr. Muscle X. All rights reserved.
            </p>
          </div>
        </section>

        {/* Colors */}
        <section className="mb-12">
          <h2 className="text-2xl font-heading font-semibold mb-4">
            Color Palette
          </h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
            <div className="p-4 bg-brand-primary text-text-inverse rounded-lg">
              <p className="font-medium">Brand Primary</p>
            </div>
            <div className="p-4 bg-brand-secondary text-text-inverse rounded-lg">
              <p className="font-medium">Brand Secondary</p>
            </div>
            <div className="p-4 bg-brand-accent text-text-inverse rounded-lg">
              <p className="font-medium">Brand Accent</p>
            </div>
            <div className="p-4 bg-bg-primary text-text-primary border border-text-tertiary rounded-lg">
              <p className="font-medium">BG Primary</p>
            </div>
            <div className="p-4 bg-bg-secondary text-text-primary rounded-lg">
              <p className="font-medium">BG Secondary</p>
            </div>
            <div className="p-4 bg-bg-tertiary text-text-primary rounded-lg">
              <p className="font-medium">BG Tertiary</p>
            </div>
          </div>
        </section>

        {/* Buttons */}
        <section className="mb-12">
          <h2 className="text-2xl font-heading font-semibold mb-4">Buttons</h2>

          {/* Button Sizes */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-3">Sizes</h3>
            <div className="flex flex-wrap gap-4">
              <Button size="sm">Small (48px)</Button>
              <Button size="md">Medium (56px)</Button>
              <Button size="lg">Large (64px)</Button>
            </div>
          </div>

          {/* Button Variants */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-3">Variants</h3>
            <div className="flex flex-wrap gap-4">
              <Button variant="primary">Start Workout</Button>
              <Button variant="secondary">View Progress</Button>
              <Button variant="ghost">Skip Exercise</Button>
            </div>
          </div>

          {/* Button States */}
          <div>
            <h3 className="text-lg font-medium mb-3">States</h3>
            <div className="flex flex-wrap gap-4">
              <Button>Normal</Button>
              <Button disabled>Disabled</Button>
            </div>
          </div>
        </section>

        {/* Shadows & Effects */}
        <section className="mb-12">
          <h2 className="text-2xl font-heading font-semibold mb-4">
            Shadows & Effects
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div className="p-6 bg-bg-secondary shadow-theme-sm rounded-lg">
              <p className="font-medium">Small Shadow</p>
            </div>
            <div className="p-6 bg-bg-secondary shadow-theme-md rounded-lg">
              <p className="font-medium">Medium Shadow</p>
            </div>
            <div className="p-6 bg-bg-secondary shadow-theme-lg rounded-lg">
              <p className="font-medium">Large Shadow</p>
            </div>
            <div className="p-6 bg-bg-secondary shadow-theme-xl rounded-lg">
              <p className="font-medium">Extra Large Shadow</p>
            </div>
          </div>
        </section>

        {/* Mobile Touch Targets */}
        <section className="mb-12">
          <h2 className="text-2xl font-heading font-semibold mb-4">
            Mobile Touch Targets
          </h2>
          <div className="p-6 bg-bg-secondary rounded-lg">
            <p className="text-sm text-text-secondary mb-4">
              All interactive elements meet minimum 44px touch target
              requirements
            </p>
            <div className="space-y-4">
              <button className="w-full h-11 bg-brand-primary text-text-inverse rounded-lg">
                44px Height Touch Target
              </button>
              <button className="w-full h-14 bg-brand-primary text-text-inverse rounded-lg">
                56px Height (Comfortable)
              </button>
              <button className="w-full h-16 bg-brand-primary text-text-inverse rounded-lg">
                64px Height (Large - Active Workouts)
              </button>
            </div>
          </div>
        </section>

        {/* Theme Description */}
        <section className="mb-12">
          <h2 className="text-2xl font-heading font-semibold mb-4">
            Theme Philosophy
          </h2>
          <div className="p-6 bg-bg-secondary rounded-lg space-y-4">
            {theme === 'subtle-depth' && (
              <>
                <h3 className="text-xl font-heading font-medium text-brand-primary">
                  Subtle Depth - Premium Sophistication
                </h3>
                <p className="text-text-secondary">
                  Rich, layered interfaces with micro-shadows and gentle
                  gradients creating visual hierarchy. Deep charcoal backgrounds
                  with golden accents evoke luxury and refinement.
                </p>
              </>
            )}
            {theme === 'flat-bold' && (
              <>
                <h3 className="text-xl font-heading font-medium text-brand-primary">
                  Flat Bold - Power & Precision
                </h3>
                <p className="text-text-secondary">
                  High-impact color blocks with sharp edges and no gradients.
                  Bold typography with maximum contrast. Electric green accents
                  on pure black/white for no-nonsense, powerful interfaces.
                </p>
              </>
            )}
            {theme === 'glassmorphism' && (
              <>
                <h3 className="text-xl font-heading font-medium text-brand-primary">
                  Glassmorphism - Future Tech
                </h3>
                <p className="text-text-secondary">
                  Frosted glass effects with layered transparency and
                  aurora-like gradient meshes. Glowing accents and smooth
                  transitions create a cutting-edge, futuristic aesthetic.
                </p>
              </>
            )}
            {theme === 'ultra-minimal' && (
              <>
                <h3 className="text-xl font-heading font-medium text-brand-primary">
                  Ultra-Minimal - Pure Focus
                </h3>
                <p className="text-text-secondary">
                  Maximum negative space as a luxury element with hairline
                  borders and typography-driven hierarchy. Single accent color
                  with black/white base where the interface disappears and
                  content dominates.
                </p>
              </>
            )}
          </div>
        </section>
      </div>
    </div>
  )
}

export default function DesignSystemPage() {
  return (
    <ThemeProvider>
      <DesignSystemShowcase />
    </ThemeProvider>
  )
}
