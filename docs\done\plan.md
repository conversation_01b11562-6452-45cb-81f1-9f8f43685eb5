# Dr. Muscle X - Implementation Prompt Plan

> Step-by-step prompts for building the ultra-fast TypeScript/Next.js web app using TDD methodology

## Overview

This document contains a series of discrete, iterative prompts designed for LLM-driven code generation. Each prompt builds on the previous ones and follows strict Test-Driven Development principles. The prompts are sized to be manageable, safe, and focused on incremental progress.

**Important**: Mark each prompt as "Completed" after successful implementation and testing.

## Prerequisites for LLM Execution

Before starting, ensure the LLM has access to:

- `web-spec.md` - The complete specification
- `api-models-reference.md` - API data models
- `testing-patterns-reference.md` - TDD/BDD patterns
- Production DrMuscle API endpoint documentation

## Implementation Prompts

---

### **Prompt 1: Mobile-First PWA Foundation Setup**

**Status**: ✅ Completed (Jun 30, 2025)  
**Actual Time**: ~40 minutes (including npm install fixes)  
**Dependencies**: None

```text
Set up a new Next.js 14 TypeScript PWA project optimized for mobile-first development. Your task:

1. Create a new Next.js 14 project with App Router using `create-next-app`
2. Configure TypeScript in strict mode with zero tolerance for `any` types
3. Install and configure the core dependencies:
   - Tailwind CSS 3.0+ with mobile-first breakpoints
   - Zustand for state management
   - React Query/TanStack Query for API state
   - Axios for HTTP client
   - Vitest + React Testing Library for testing
   - ESLint + Prettier with Airbnb configuration
   - Husky for pre-commit hooks

4. **PWA Setup (CRITICAL FOR MOBILE USERS):**
   - Install and configure `next-pwa` with Workbox
   - Create Web App Manifest (`public/manifest.json`) with proper mobile icons
   - Configure Service Worker for offline workout caching
   - Set up background sync for workout data
   - Configure push notifications infrastructure
   - Add PWA meta tags for mobile installation

5. Set up the project structure exactly as specified:
```

src/
├── app/ # Next.js App Router
├── components/ # React components  
 ├── lib/ # Utilities
├── hooks/ # Custom hooks
├── types/ # TypeScript types
├── api/ # API client
├── stores/ # Zustand stores
└── styles/ # Global styles
public/
├── manifest.json # PWA manifest
├── sw.js # Service worker
└── icons/ # PWA icons (various sizes)

```

6. Configure mobile-first performance optimizations:
- Bundle analyzer with mobile-specific targets
- Next.js optimizations for mobile devices
- Tailwind CSS with mobile-first breakpoints
- Aggressive code splitting for mobile bandwidth

7. Write your first test: A basic "app renders without crashing" test
8. Ensure the development server starts without errors
9. Test PWA installation on mobile device
10. Create initial documentation in `README.md` with setup instructions

Requirements:
- All dependencies must have locked versions for reproducibility
- ESLint must pass with zero warnings
- TypeScript must compile with zero errors
- The test must pass
- Bundle size must be under 150KB for initial JS (mobile bandwidth)
- PWA installation must work on mobile devices
- Service Worker must register successfully

Test the setup by running `npm run dev`, `npm run test`, `npm run build`, and PWA installation on mobile.
```

---

### **Prompt 2: TypeScript API Types Generation**

**Status**: ✅ Completed (Jun 30, 2025)  
**Actual Time**: ~15 minutes  
**Dependencies**: Prompt 1

```text
Create TypeScript interfaces for the DrMuscle API based on the provided API models reference. Your task:

1. Create `src/types/api.ts` with all the interfaces from `api-models-reference.md`:
   - WorkoutTemplateGroupModel
   - WorkoutTemplateModel
   - ExerciseModel
   - RecommendationModel
   - LoginModel, LoginSuccessResult, RegisterModel
   - SetLog, NewExerciceLogModel
   - MultiUnityWeight, ApiResponse<T>

2. Create `src/types/app.ts` for application-specific types:
   - UI state types
   - Form data types
   - Component prop types
   - Store state types

3. Create utility types for common patterns:
   - API response handlers
   - Error types
   - Loading states
   - Form validation types

4. Write comprehensive tests for type safety:
   - Type validation tests
   - API response parsing tests
   - Ensure all interfaces match the C# models exactly

5. Add JSDoc comments for all interfaces with usage examples

Requirements:
- All types must be exported with clear naming
- No `any` types allowed - use proper generics
- Include runtime type guards where needed
- All types must have corresponding tests
- Types must be compatible with React Query patterns

Test by creating mock data objects that satisfy all interfaces without TypeScript errors.
```

---

### **Prompt 3: Authentication Store and API Client**

**Status**: ✅ Completed (Jun 30, 2025)  
**Actual Time**: ~30 minutes (including TypeScript fixes)  
**Dependencies**: Prompt 2

```text
Implement the authentication system using TDD. Reference the testing patterns from `testing-patterns-reference.md` for BDD scenarios. Your task:

1. **First, write failing tests** following the watch app patterns:
   - Test authentication store initialization
   - Test login flow with email/password
   - Test token storage and retrieval
   - Test automatic token refresh
   - Test logout functionality
   - Test auth header injection in API calls

2. **Create `src/stores/authStore.ts`** using Zustand:
   - State: `{ user, token, refreshToken, isAuthenticated, isLoading, error }`
   - Actions: `login`, `logout`, `refreshToken`, `setError`, `clearError`
   - Persistence: Secure token storage (localStorage with encryption)

3. **Create `src/api/client.ts`** with Axios:
   - Base configuration with production API URL
   - Request interceptor for auth headers
   - Response interceptor for token refresh
   - Error handling for 401/403 responses
   - Retry logic with exponential backoff

4. **Create `src/api/auth.ts`** with methods:
   - `login(email, password)` → LoginSuccessResult
   - `register(userData)` → LoginSuccessResult
   - `refreshToken()` → new token
   - `logout()` → void

5. **Implement using the Given/When/Then patterns**:
```

**Scenario:** Successful Email/Password Login
Given the user provides valid credentials
When they call the login function
Then tokens are stored securely
And the auth state shows authenticated
And subsequent API calls include auth headers

```

Requirements:
- Follow TDD strictly: failing tests first, then implementation
- All auth scenarios from testing-patterns-reference.md must be covered
- Use React Query for API state management in auth calls
- Implement proper TypeScript typing throughout
- Handle all error cases with user-friendly messages
- Secure token storage with rotation

Run tests to ensure 100% pass rate before proceeding.
```

---

### **Prompt 4: Mobile-First Login UI Component**

**Status**: ✅ Completed (Jun 30, 2025)  
**Actual Time**: ~25 minutes  
**Dependencies**: Prompt 3

```text
Create the mobile-first login form UI component with TDD. Your task:

1. **Write comprehensive tests first** following the BDD patterns:
   - Render login form correctly on mobile viewports
   - Handle mobile keyboard interactions (email/password)
   - Form validation with mobile-friendly error display
   - Submit form with touch interactions
   - Display error messages with mobile-appropriate sizing
   - Show loading state during authentication with haptic feedback
   - Handle API errors gracefully with mobile UX patterns
   - Redirect on successful login

2. **Create `src/components/LoginForm.tsx`** (MOBILE-FIRST):
   - Email input optimized for mobile keyboards (type="email")
   - Password input with show/hide toggle for mobile
   - Large touch-friendly inputs (minimum 44px height)
   - Real-time validation with mobile-sized error messages
   - Submit button with loading state and haptic feedback
   - Error display optimized for mobile screens
   - Tailwind CSS mobile-first styling
   - Touch accessibility features

3. **Create `src/app/login/page.tsx`**:
   - Full-screen mobile login layout
   - PWA-compatible layout (status bar safe areas)
   - Integration with LoginForm component
   - Redirect logic for authenticated users
   - Mobile SEO optimization with proper meta tags
   - Splash screen integration

4. **Mobile form validation requirements**:
   - Email: Valid email format, required, mobile keyboard optimization
   - Password: Minimum 8 characters, required, show/hide toggle
   - Show specific field errors below inputs with mobile-appropriate spacing
   - Disable submit button when invalid with visual feedback
   - Auto-focus management for mobile keyboards

5. **Mobile UI/UX specifications**:
   - Mobile-first design with large touch targets (44px minimum)
   - iOS/Android native-like styling patterns
   - Loading states with smooth mobile transitions
   - Error states with mobile-appropriate messaging
   - Biometric authentication integration where available
   - Keyboard avoidance for input fields

**Test scenarios to implement**:
```

**Scenario:** Valid Form Submission
Given the user enters valid email and password
When they click the login button
Then the loading state is shown
And the login API is called with correct credentials
And on success, user is redirected to workout screen

**Scenario:** Form Validation Errors
Given the user enters an invalid email
When they try to submit the form
Then an error message is shown below the email field
And the submit button remains disabled

```

Requirements:
- All tests must pass before implementation
- Component must be fully typed with TypeScript
- Use React Hook Form for form management
- Implement optimistic UI updates
- Error boundaries for crash protection
- Performance: component should render in < 50ms

Test the component in isolation and integration with the auth store.
```

---

### **Prompt 5: Workout Data Management**

**Status**: Pending  
**Estimated Time**: 20-25 minutes  
**Dependencies**: Prompt 4

```text
Implement workout data fetching and management using TDD patterns. Your task:

1. **Write failing tests first** for all workout scenarios:
   - Fetch today's workout successfully
   - Handle empty workout state
   - Handle API errors during fetch
   - Cache workout data appropriately
   - Update workout state during execution
   - Save workout progress locally

2. **Create `src/stores/workoutStore.ts`** using Zustand:
   - State: `{ currentWorkout, exercises, currentExerciseIndex, currentSetIndex, workoutSession, isLoading, error }`
   - Actions: `startWorkout`, `nextSet`, `nextExercise`, `saveSet`, `completeWorkout`, `updateCurrentSet`
   - Session management for workout persistence

3. **Create `src/api/workouts.ts`** with methods:
   - `getTodaysWorkout()` → WorkoutTemplateGroupModel[]
   - `getWorkoutDetails(id)` → WorkoutTemplateModel
   - `getExerciseRecommendation(exerciseId)` → RecommendationModel
   - `saveWorkoutSet(setData)` → success response
   - `completeWorkout(workoutData)` → completion response

4. **Create `src/hooks/useWorkout.ts`** custom hook:
   - Integrate React Query with workout store
   - Provide optimistic updates for set logging
   - Handle offline/online state management
   - Implement automatic data refresh

5. **Implement core workout logic**:
   - Exercise progression (warmup → work sets)
   - Set counting and progression
   - RIR capture for first work set
   - Rest timer management
   - Workout completion tracking

**Key test scenarios**:
```

**Scenario:** Fetch Today's Workout Successfully
Given the user is authenticated
When the workout hook is initialized
Then it fetches today's workout from the API
And stores the workout data in the store
And sets the current exercise to the first exercise

**Scenario:** Progress Through Workout Sets
Given a workout is loaded with multiple exercises
When the user completes a set
Then the current set index increments
And if it's the last set, current exercise index increments
And the workout progress is saved locally

```

Requirements:
- Follow TDD: tests first, then implementation
- Use React Query for server state management
- Implement optimistic updates for better UX
- Handle offline scenarios with local storage
- Type everything with proper interfaces
- Error handling for all API failure modes
- Performance: minimize re-renders with proper memoization

Test with various workout scenarios including edge cases (empty workouts, API failures, network issues).
```

---

### **Prompt 6: Mobile-Optimized Today's Workout Screen**

**Status**: Pending  
**Estimated Time**: 20-25 minutes  
**Dependencies**: Prompt 5

```text
Create the mobile-optimized workout screen that displays today's workout, mirroring the watch app UX for mobile. Your task:

1. **Write comprehensive tests** for the mobile workout screen:
   - Display workout name and exercises list on mobile viewports
   - Show large "Start Workout" button when workout available
   - Handle empty workout state with mobile-appropriate messaging
   - Show loading state during data fetch with mobile spinners
   - Handle error states with mobile-friendly retry options
   - Navigate to exercise execution on "Start Workout" tap
   - Test swipe gestures for exercise preview
   - Test touch interactions and haptic feedback

2. **Create `src/components/WorkoutScreen.tsx`** (MOBILE-OPTIMIZED):
   - Display today's workout name prominently (large mobile typography)
   - Show exercise list with mobile card design (large touch targets)
   - "Start Workout" button: 100% width, minimum 56px height, thumb-friendly
   - Empty state optimized for mobile with engaging illustrations
   - Error state with large retry button and mobile-appropriate messaging
   - Swipe gestures for exercise preview navigation
   - Pull-to-refresh functionality for workout updates

3. **Create `src/app/workout/page.tsx`**:
   - Full-screen mobile workout layout (no wasted space)
   - PWA-compatible layout with safe areas
   - Integration with workout hooks and store
   - Authentication guard (redirect if not logged in)
   - Mobile SEO optimization and meta tags
   - Preload critical workout data for instant display

4. **Mobile-Optimized UI Components to create**:
   - `ExercisePreviewCard`: Mobile card design, large touch targets, swipe-enabled
   - `WorkoutHeader`: Mobile-first header with workout name and progress
   - `EmptyWorkoutState`: Mobile-engaging empty state with call-to-action
   - `ErrorState`: Mobile-friendly error with large retry button
   - `PullToRefresh`: Custom pull-to-refresh component

5. **Mobile UX Requirements (enhanced watch app patterns)**:
   - Thumb-friendly interface with all controls in easy reach
   - Large, easily tappable "Start Workout" button (full-width)
   - Exercise cards with large text and clear visual hierarchy
   - Smooth mobile transitions and micro-interactions
   - Haptic feedback for button taps and successful actions
   - Swipe gestures for secondary actions
   - Background preloading for instant navigation

**Test scenarios**:
```

**Scenario:** Display Today's Workout Successfully
Given the user has a scheduled workout for today
When they navigate to the workout screen
Then the workout name is displayed prominently
And the exercise list shows each exercise with target reps/sets
And the "Start Workout" button is enabled and visible

**Scenario:** Handle Empty Workout State
Given the user has no scheduled workout for today
When they navigate to the workout screen
Then a friendly empty state message is shown
And suggestions for creating a workout are provided
And the "Start Workout" button is hidden

```

Requirements:
- Follow TDD pattern with tests first
- Use Tailwind CSS for styling with mobile-first approach
- Implement proper loading and error boundaries
- Use React Query for data fetching with proper caching
- Ensure accessibility with proper ARIA labels
- Performance: screen should load in < 1 second
- Integrate seamlessly with authentication flow

Test thoroughly with different workout states and network conditions.
```

---

### **Prompt 7: Exercise Execution Screen**

**Status**: Pending  
**Estimated Time**: 25-30 minutes  
**Dependencies**: Prompt 6

```text
Implement the core exercise execution screen where users perform sets. This is the heart of the app. Your task:

1. **Write failing tests first** for all exercise execution scenarios:
   - Display exercise name, target reps, target weight
   - Allow editing reps and weight values
   - Save set data to API and local store
   - Handle RIR capture for first work set
   - Progress to next set/exercise automatically
   - Handle final set completion
   - Update performance percentage calculations

2. **Create `src/components/SetScreen.tsx`**:
   - Exercise name display (prominent)
   - Target vs actual reps/weight inputs
   - Editable input fields (tap to edit, mirroring watch UX)
   - Save set button with progress indication
   - Set counter (e.g., "Set 2 of 4")
   - Performance percentage display
   - Next set/exercise navigation

3. **Create `src/components/SetInputs.tsx`**:
   - Reps input with number picker
   - Weight input with decimal support
   - Unit display (lbs/kg based on user preference)
   - Input validation and formatting
   - Touch-friendly interface for mobile

4. **Create `src/app/workout/exercise/page.tsx`**:
   - Exercise execution page layout
   - URL parameters for exercise/set tracking
   - Navigation guards and error handling
   - Back navigation to workout screen

5. **Core functionality to implement**:
   - Set data tracking and persistence
   - Performance calculation (% change from previous)
   - Rest timer integration (prepare for next prompt)
   - Exercise progression logic
   - Local storage backup for offline scenarios

**Critical test scenarios from watch app patterns**:
```

**Scenario:** Display Set Details Correctly
Given the user is on Set 2 of Bench Press with target 8 reps, 105 lbs
When the Set Screen appears
Then the exercise name "Bench Press" is displayed
And the target reps "8" are shown
And the target weight "105" is displayed
And the weight unit matches user preference
And the set counter shows "Set 2 of 4"

**Scenario:** Save Set Data Successfully
Given the user has entered 10 reps and 100 lbs
When they tap the "Save Set" button
Then the set data is saved to the API
And the local store is updated
And the performance percentage is calculated
And the UI transitions to the next set or RIR capture

```

Requirements:
- Strict TDD: comprehensive tests before implementation
- Mirror watch app UX exactly for consistency
- Use optimistic updates for perceived performance
- Handle all error cases gracefully
- Type everything with proper interfaces
- Performance: transitions under 100ms
- Offline-first with sync when online

This is the most critical screen - test exhaustively with various exercise types and edge cases.
```

---

### **Prompt 8: RIR (Reps in Reserve) Capture System**

**Status**: Pending  
**Estimated Time**: 15-20 minutes  
**Dependencies**: Prompt 7

```text
Implement the RIR capture system that appears after the first work set of each exercise. Your task:

1. **Write failing tests** for RIR scenarios:
   - Show RIR picker after first work set only
   - Display 5 descriptive RIR options
   - Handle RIR selection and save with set data
   - Skip RIR for subsequent sets of same exercise
   - Handle dismissal without selection (cancel)
   - Integrate RIR data with performance calculations

2. **Create `src/components/RIRPicker.tsx`**:
   - Modal/overlay presentation
   - 5 descriptive options from watch app:
     * "Very hard (0 left)"
     * "Could do 1-2 more"
     * "Could do 3-4 more"
     * "Could do 5-6 more"
     * "Could do 7+ more"
   - Large, touch-friendly selection buttons
   - Cancel option to dismiss without saving
   - Smooth animations and transitions

3. **Create `src/hooks/useRIR.ts`**:
   - Logic to determine when RIR should be captured
   - RIR value mapping (descriptive → numeric)
   - Integration with set saving logic
   - RIR data persistence

4. **Integration with SetScreen**:
   - Trigger RIR picker after first work set save
   - Pass RIR data to API when saving set
   - Update UI flow for RIR → rest timer → next set
   - Handle RIR in performance calculations

5. **Data flow implementation**:
   - Detect first work set vs subsequent sets
   - Save set data with RIR value
   - Update workout store with RIR tracking
   - API integration for RIR data submission

**Key test scenarios from watch app**:
```

**Scenario:** Display RIR Picker After First Work Set
Given the user just completed the first work set of an exercise
When they tap "Save Set"
Then the RIR picker modal appears
And 5 descriptive options are displayed
And each option has a large, tappable target

**Scenario:** Save Set with RIR Data
Given the RIR picker is displayed
When the user selects "Could do 1-2 more"
Then the modal is dismissed
And the set is saved with RIR value 1-2
And the workout progresses to rest timer
And subsequent sets of this exercise skip RIR

```

Requirements:
- Follow watch app UX exactly
- Tests first, then implementation
- Smooth modal animations and interactions
- Proper TypeScript typing for RIR data
- Integration with existing set saving flow
- Error handling for API failures
- Performance: modal should appear instantly

Test with various exercise scenarios and ensure RIR only appears for first work sets.
```

---

### **Prompt 9: Rest Timer System**

**Status**: Pending  
**Estimated Time**: 20-25 minutes  
**Dependencies**: Prompt 8

```text
Implement the rest timer that appears between sets, providing smooth workout flow. Your task:

1. **Write comprehensive tests** for timer functionality:
   - Start timer automatically after set save/RIR
   - Display countdown with minutes:seconds format
   - Allow timer skip/override
   - Show different rest times for warmup vs work sets
   - Handle timer completion with audio/visual feedback
   - Pause/resume timer functionality
   - Handle app backgrounding/foregrounding

2. **Create `src/components/RestTimer.tsx`**:
   - Large, easy-to-read countdown display
   - Progress ring or bar visualization
   - "Skip Rest" button for user control
   - Pause/resume functionality
   - Visual feedback when timer completes
   - Next exercise/set information preview

3. **Create `src/hooks/useTimer.ts`**:
   - Timer logic with requestAnimationFrame for accuracy
   - Background timer handling with Web Workers
   - Timer state management (running, paused, completed)
   - Integration with workout store for rest duration
   - Audio notifications (optional for MVP)

4. **Create `src/components/TimerScreen.tsx`**:
   - Full screen timer display
   - Exercise context (what's next)
   - Timer controls and skip option
   - Smooth transitions to next set screen
   - Handle timer completion automatically

5. **Rest timer logic**:
   - Warmup sets: 60-90 seconds
   - Work sets: 120-180 seconds (based on exercise)
   - Between exercises: 60 seconds + transition
   - User can customize default times
   - Smart recommendations based on exercise intensity

**Critical test scenarios**:
```

**Scenario:** Start Rest Timer After Set Save
Given the user just saved a work set
When the set save completes successfully
Then the rest timer starts with appropriate duration
And the countdown displays in MM:SS format
And a "Skip Rest" button is available
And the next exercise/set is previewed

**Scenario:** Timer Completion and Auto-Progress
Given the rest timer is running
When the timer reaches 00:00
Then a completion notification is shown
And the screen automatically transitions to the next set
And the timer state is reset for the next rest period

```

Requirements:
- Accurate timing with smooth UI updates
- Handle browser tab switching and backgrounding
- Use Web Workers for background timer execution
- Implement proper timer cleanup to prevent memory leaks
- Audio feedback for timer completion (subtle)
- Accessibility for screen readers
- Performance: 60fps smooth countdown animation

Test timer accuracy, background handling, and user interaction flows thoroughly.
```

---

### **Prompt 10: Workout Completion Flow**

**Status**: Pending  
**Estimated Time**: 15-20 minutes  
**Dependencies**: Prompt 9

```text
Implement the workout completion flow that saves the full workout and provides user feedback. Your task:

1. **Write failing tests** for completion scenarios:
   - Detect workout completion (all exercises done)
   - Save complete workout data to API
   - Display workout summary with statistics
   - Handle save failures with retry options
   - Reset workout state for next session
   - Navigate back to main screen
   - Offline completion with sync later

2. **Create `src/components/WorkoutComplete.tsx`**:
   - Completion celebration UI
   - Workout summary statistics:
     * Total time, exercises completed, sets performed
     * Performance improvements, personal records
     * Volume comparison to previous workouts
   - "Finish Workout" confirmation button
   - Option to add notes or feedback
   - Return to workout screen button

3. **Create `src/api/workoutCompletion.ts`**:
   - `completeWorkout(workoutData)` API call
   - Batch save all sets and exercise data
   - Handle partial completions (incomplete workouts)
   - Retry logic for failed submissions
   - Offline queue for completion data

4. **Workout completion logic**:
   - Determine when all exercises are complete
   - Calculate workout statistics and metrics
   - Prepare complete workout data for API
   - Handle partial workout scenarios
   - Clean up workout state after completion

5. **Data persistence and sync**:
   - Save completed workout to local storage
   - Sync with API when connection available
   - Handle sync conflicts and resolution
   - Update user progress and statistics

**Key test scenarios**:
```

**Scenario:** Complete Workout Successfully
Given the user has finished all exercises in their workout
When they complete the final set
Then the workout completion screen appears
And workout statistics are calculated and displayed
And the complete workout data is saved to the API
And the user sees a success confirmation

**Scenario:** Handle Workout Save Failure
Given the user completes their workout
When the API save fails due to network issues
Then the workout is saved locally
And a retry option is provided
And the user sees appropriate feedback about offline save
And sync will retry when connection is restored

```

Requirements:
- Comprehensive error handling for save failures
- Offline-first completion with background sync
- Accurate statistics calculation
- Celebratory UX for completed workouts
- Proper cleanup of workout state
- Performance tracking and metrics
- Data integrity checks before save

Test completion flows thoroughly including network failures and edge cases.
```

---

### **Prompt 11: Navigation and Routing**

**Status**: Pending  
**Estimated Time**: 10-15 minutes  
**Dependencies**: Prompt 10

```text
Implement Next.js App Router navigation and route protection for the complete workout flow. Your task:

1. **Write tests** for navigation scenarios:
   - Protected route authentication guards
   - Proper URL structure and parameters
   - Navigation state preservation during workout
   - Back button handling and workout interruption
   - Deep linking to specific exercises/sets
   - Route transitions and loading states

2. **Set up App Router structure**:
```

src/app/
├── page.tsx # Landing/redirect page
├── login/page.tsx # Login page
├── workout/
│ ├── page.tsx # Today's workout
│ ├── exercise/
│ │ └── [id]/page.tsx # Exercise execution
│ └── complete/page.tsx # Workout completion
└── layout.tsx # Root layout with auth

```

3. **Create `src/components/AuthGuard.tsx`**:
- Check authentication state
- Redirect unauthenticated users to login
- Preserve intended destination for post-login redirect
- Handle token expiration gracefully

4. **Create route-specific loading and error pages**:
- `src/app/workout/loading.tsx`
- `src/app/workout/error.tsx`
- `src/app/exercise/[id]/loading.tsx`
- Handle navigation errors and timeouts

5. **Navigation flow implementation**:
- Login → Workout → Exercise → Complete → Workout
- URL state management for workout progress
- Browser history handling
- Prevent accidental navigation during workouts

**Test scenarios**:
```

**Scenario:** Protected Route Access
Given an unauthenticated user tries to access /workout
When they navigate to the workout page
Then they are redirected to /login
And the intended destination is saved
And after login, they are redirected to /workout

**Scenario:** Workout Progress Navigation
Given a user is in the middle of a workout
When they are on exercise 2, set 3
Then the URL reflects the current progress
And browser refresh maintains the workout state
And back button shows appropriate warnings

```

Requirements:
- Use Next.js App Router properly
- Implement authentication guards on all protected routes
- Handle URL state and deep linking
- Preserve workout progress across navigation
- Smooth page transitions with loading states
- Error boundaries for navigation failures

Test navigation flows thoroughly including edge cases and browser behavior.
```

---

### **Prompt 12: Error Handling and Edge Cases**

**Status**: Pending  
**Estimated Time**: 15-20 minutes  
**Dependencies**: Prompt 11

```text
Implement comprehensive error handling and edge case management throughout the app. Your task:

1. **Write tests** for error scenarios:
   - Network failures during workout
   - API timeouts and rate limiting
   - Invalid data responses
   - Authentication token expiration mid-workout
   - Browser storage limitations
   - Concurrent workout sessions
   - App backgrounding/foregrounding

2. **Create `src/components/ErrorBoundary.tsx`**:
   - React error boundary for crash recovery
   - User-friendly error messages
   - Error reporting to logging service
   - Fallback UI for broken components
   - Recovery options where possible

3. **Create `src/hooks/useErrorHandler.ts`**:
   - Centralized error handling logic
   - Error categorization (network, auth, validation, etc.)
   - User notification system
   - Retry mechanisms with exponential backoff
   - Error recovery strategies

4. **Implement offline handling**:
   - Network status detection
   - Offline queue for API requests
   - Local storage management and limits
   - Sync conflict resolution
   - User feedback for offline state

5. **Edge case handling**:
   - Empty workout data
   - Malformed API responses
   - Browser storage quota exceeded
   - Multiple tabs/windows open
   - Workout interruption scenarios
   - Timer accuracy in background

**Critical error scenarios to test**:
```

**Scenario:** Network Failure During Set Save
Given the user completes a set
When they tap "Save Set" and the network fails
Then the set is queued locally for retry
And the user sees appropriate feedback
And the app continues functioning offline
And sync retries when connection is restored

**Scenario:** Authentication Expiration Mid-Workout
Given the user is mid-workout
When their auth token expires
Then the workout data is preserved locally
And they are prompted to re-authenticate
And after login, they can continue their workout
And no workout data is lost

```

Requirements:
- Graceful degradation for all failure modes
- User-friendly error messages (no technical jargon)
- Automatic retry with intelligent backoff
- Offline-first architecture with sync
- Error logging for debugging
- Recovery options for users
- Data preservation during errors

Test extensively with simulated network failures, invalid responses, and edge cases.
```

---

### **Prompt 13: Performance Optimization**

**Status**: Pending  
**Estimated Time**: 20-25 minutes  
**Dependencies**: Prompt 12

```text
Optimize the app for ultra-fast performance to meet the "<1 second load, <100ms transitions" requirements. Your task:

1. **Write performance tests**:
   - Page load time measurement
   - Bundle size analysis
   - Runtime performance profiling
   - Memory usage tracking
   - API response time monitoring
   - Component render optimization

2. **Bundle optimization**:
   - Code splitting for routes and components
   - Tree shaking unused code
   - Dynamic imports for non-critical features
   - Webpack/Next.js optimization configuration
   - Asset optimization (images, fonts, etc.)

3. **React optimization**:
   - Implement React.memo for pure components
   - useMemo and useCallback for expensive operations
   - Optimize re-renders with proper dependency arrays
   - Virtual scrolling for large lists (if needed)
   - Concurrent features for better UX

4. **Create `src/lib/performance.ts`**:
   - Performance monitoring utilities
   - Bundle analyzer integration
   - Core Web Vitals tracking
   - Custom performance metrics
   - Performance regression detection

5. **API and data optimization**:
   - React Query cache optimization
   - Request deduplication
   - Prefetching critical data
   - Optimistic updates for immediate feedback
   - Background data sync

**Performance targets to test**:
```

**Target:** Page Load Performance
Given a user navigates to the workout page
When the page loads
Then First Contentful Paint occurs within 800ms
And Largest Contentful Paint occurs within 1.2s
And Time to Interactive is under 1.5s
And the JavaScript bundle is under 150KB gzipped

**Target:** Transition Performance
Given a user taps "Save Set"
When the action is triggered
Then the UI responds within 50ms
And the transition to rest timer is under 100ms
And no jank or stuttering occurs

```

Requirements:
- Meet all performance targets from the specification
- Use Lighthouse CI for automated performance testing
- Implement Core Web Vitals monitoring
- Bundle size under 200KB (preferably 150KB)
- Zero layout shifts during normal operation
- 60fps smooth animations and transitions
- Memory usage optimization

Test performance on various devices and network conditions to ensure targets are met.
```

---

### **Prompt 14: Final Testing and Quality Assurance**

**Status**: Pending  
**Estimated Time**: 25-30 minutes  
**Dependencies**: Prompt 13

```text
Implement comprehensive end-to-end testing and quality assurance for the complete application. Your task:

1. **Create comprehensive E2E tests** with Playwright:
   - Complete workout flow from login to completion
   - Error scenarios and recovery
   - Cross-browser compatibility (Chrome, Safari, Firefox)
   - Mobile responsiveness testing
   - Performance testing integration
   - Accessibility compliance testing

2. **Create `tests/e2e/workoutFlow.spec.ts`**:
   - Full user journey: Login → Workout → Sets → RIR → Timer → Complete
   - Error injection and recovery testing
   - Offline/online transition testing
   - Multiple workout scenarios
   - Performance assertions

3. **Create `tests/e2e/accessibility.spec.ts`**:
   - Keyboard navigation testing
   - Screen reader compatibility
   - ARIA label verification
   - Color contrast validation
   - Focus management testing

4. **Set up test automation**:
   - CI/CD pipeline integration
   - Automated test running on PR/merge
   - Performance regression detection
   - Cross-browser testing matrix
   - Mobile device simulation

5. **Quality checklist verification**:
   - All BDD scenarios pass
   - 90%+ test coverage on business logic
   - Performance targets met
   - Accessibility requirements satisfied
   - Error handling comprehensive
   - Documentation complete

**Critical E2E test scenarios**:
```

**Scenario:** Complete Workout Flow E2E
Given a new user opens the web app
When they log in with valid credentials
And start today's workout
And complete all exercises with sets and RIR
And finish the workout
Then their workout is saved successfully
And they see completion statistics
And they can start a new session

**Scenario:** Error Recovery E2E
Given a user is mid-workout
When a network error occurs during set save
And they continue working out offline
And the network reconnects
Then all their data is synced successfully
And no workout data is lost

```

Requirements:
- Test coverage must be 90%+ on critical paths
- All performance targets must be verified
- Cross-browser compatibility confirmed
- Mobile responsiveness validated
- Accessibility compliance checked
- Error scenarios thoroughly tested
- Documentation updated and complete

This is the final quality gate - ensure everything meets the ultra-fast, ultra-reliable requirements before considering the MVP complete.
```

---

## Post-Implementation Checklist

After completing all prompts, verify:

- [ ] **Performance**: Page loads < 1s, transitions < 100ms
- [ ] **Functionality**: Complete workout flow works end-to-end
- [ ] **Quality**: 90%+ test coverage, all scenarios pass
- [ ] **Compatibility**: Works on Chrome, Safari, Firefox (mobile + desktop)
- [ ] **Accessibility**: Basic keyboard navigation and screen reader support
- [ ] **Error Handling**: Graceful failure and recovery in all scenarios
- [ ] **Documentation**: README, API docs, deployment guide complete

## Deployment and Launch

Once all prompts are completed:

1. **Staging Deployment**: Deploy to Vercel staging environment
2. **Performance Testing**: Run Lighthouse CI and verify all targets
3. **User Testing**: Internal team testing with real workout data
4. **Beta Launch**: Soft launch to 50-100 existing users
5. **Monitoring**: Set up error tracking and performance monitoring
6. **Feedback Loop**: Collect user feedback and iterate

---

**Success Criteria**: A user can complete a full workout without bugs, and the experience is measurably faster than the current mobile app.

_"The robots LOVE TDD. Seriously. They eat it up."_ - Harper Reed
