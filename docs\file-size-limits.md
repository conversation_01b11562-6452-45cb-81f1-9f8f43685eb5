# File Size Limits

## Overview

To maintain code readability and modularity, we enforce a maximum line limit of **225 lines** per file for all TypeScript and JavaScript files in the codebase.

## Enforcement Points

### 1. Pre-commit Hook (Immediate Feedback)
- Runs automatically when you commit files
- Only checks staged files
- Prevents commits with oversized files
- Can be bypassed with `git commit --no-verify` (use sparingly)

### 2. CI/CD Pipeline (Quality Gate)
- Runs on all pull requests and pushes
- Checks entire codebase
- Blocks merging if files exceed limit
- Cannot be bypassed

### 3. Local Development (Manual Check)
- Run `npm run check:file-sizes` to check all files
- Integrated into `npm run test:dev` script
- Provides immediate feedback during development

## Commands

```bash
# Check all files in the codebase
npm run check:file-sizes

# Run full development test suite (includes file size check)
npm run test:dev

# Check only staged files (used by pre-commit hook)
node scripts/check-file-sizes-staged.js
```

## Exclusions

The following files are excluded from size checks:
- Test files (`*.test.*`, `*.spec.*`)
- Type definition files (`*.d.ts`)
- Generated files

## Dealing with Large Files

When you encounter a file exceeding 225 lines:

1. **Analyze the file structure** - Identify logical boundaries
2. **Extract reusable logic** - Move utilities to separate files
3. **Split by responsibility** - Create focused modules
4. **Use composition** - Break large components into smaller ones

### Example Refactoring

```typescript
// Before: LargeComponent.tsx (400+ lines)
export function LargeComponent() {
  // Complex state logic
  // Multiple helper functions
  // Large render method
}

// After: Split into multiple files
// LargeComponent.tsx (< 225 lines)
export function LargeComponent() {
  const logic = useComponentLogic();
  return <ComponentUI {...logic} />;
}

// useComponentLogic.ts
export function useComponentLogic() {
  // Extracted state and logic
}

// ComponentUI.tsx
export function ComponentUI(props) {
  // Focused on presentation
}
```

## Current Status

Run `npm run check:file-sizes` to see files that need refactoring. As of implementation, 38 files exceed the limit and should be addressed in future refactoring efforts.