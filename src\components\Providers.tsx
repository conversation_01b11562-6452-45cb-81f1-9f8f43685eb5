'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useState, useEffect } from 'react'
import { AuthGuard } from './AuthGuard'
import { ErrorBoundary } from './ErrorBoundary'
import { useAuthStore } from '@/stores/authStore'
import { SyncManager } from '@/utils/syncManager'
import { logEnvValidation } from '../utils/validateEnv'
import { initDebugCommands } from '@/utils/debugCommands'
import { useAuthTokenRestore } from '@/hooks/useAuthTokenRestore'
import { ThemeProvider } from '@/providers/theme-provider'
// Import for side effects - clears workout cache if quota exceeded
import '@/utils/clearWorkoutCache'

interface ProvidersProps {
  children: React.ReactNode
}

export function Providers({ children }: ProvidersProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            gcTime: 5 * 60 * 1000, // 5 minutes
            retry: 1,
          },
        },
      })
  )

  // Restore auth token before allowing any API calls
  const { isRestoringToken } = useAuthTokenRestore()

  // Ensure auth store hydration on client side and initialize sync manager
  useEffect(() => {
    // Clear workout cache if quota exceeded (this runs immediately on import)
    // The clearWorkoutCache module handles this automatically

    // Validate environment configuration in development
    if (process.env.NODE_ENV === 'development') {
      logEnvValidation()
    }

    // Initialize debug commands
    initDebugCommands()

    useAuthStore.persist.rehydrate()

    // Initialize sync manager for failed request retries
    SyncManager.init()
  }, [])

  // Show loading spinner while restoring auth token
  // This prevents API calls from being made before Authorization header is set
  if (isRestoringToken) {
    return (
      <div className="flex items-center justify-center min-h-[100dvh]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600" />
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <QueryClientProvider client={queryClient}>
          <AuthGuard
            excludePaths={['/', '/login', '/register', '/about', '/privacy']}
          >
            {children}
          </AuthGuard>
        </QueryClientProvider>
      </ThemeProvider>
    </ErrorBoundary>
  )
}
