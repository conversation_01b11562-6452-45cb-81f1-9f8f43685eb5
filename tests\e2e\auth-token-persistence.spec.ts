/* eslint-disable no-console */
import { test, expect } from '@playwright/test'

test.describe('Authentication Token Persistence', () => {
  test('should persist auth token after login and use it for API calls', async ({
    page,
  }) => {
    // Enable request interception to monitor API calls
    const apiCalls: { url: string; headers: Record<string, string> }[] = []

    await page.route('**/*', async (route) => {
      const request = route.request()
      const url = request.url()

      // Log all API calls to Dr. Muscle backend
      if (url.includes('drmuscle.azurewebsites.net')) {
        apiCalls.push({
          url,
          headers: await request.allHeaders(),
        })
        console.log('API Call:', {
          method: request.method(),
          url,
          headers: await request.allHeaders(),
        })
      }

      await route.continue()
    })

    // Go to login page
    await page.goto('/login')
    await expect(page).toHaveTitle(/Dr\. Muscle X/)

    // Fill in login form
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('testpassword')

    // Monitor console for debugging
    page.on('console', (msg) => {
      console.log('Browser console:', msg.type(), msg.text())
    })

    // Click login button
    await page.getByRole('button', { name: /login/i }).click()

    // Wait for navigation or error
    await Promise.race([
      page.waitForURL('/workout', { timeout: 10000 }),
      page.waitForSelector('[role="alert"]', { timeout: 10000 }),
    ])

    // Check if login was successful
    const error = page.locator('[role="alert"]')
    if (await error.isVisible()) {
      const errorText = await error.textContent()
      console.log('Login error:', errorText)
      throw new Error(`Login failed: ${errorText}`)
    }

    // Check localStorage for auth data
    const authData = await page.evaluate(() => {
      return {
        localStorage: Object.keys(localStorage).reduce(
          (acc, key) => {
            acc[key] = localStorage.getItem(key)
            return acc
          },
          {} as Record<string, string | null>
        ),
        sessionStorage: Object.keys(sessionStorage).reduce(
          (acc, key) => {
            acc[key] = sessionStorage.getItem(key)
            return acc
          },
          {} as Record<string, string | null>
        ),
      }
    })

    console.log('Storage after login:', authData)

    // Check if auth token was stored
    const authStore = authData.localStorage['auth-store']
    if (authStore) {
      const parsed = JSON.parse(authStore)
      console.log('Auth store state:', parsed.state)

      if (!parsed.state?.user?.access_token) {
        console.error('No access token found in auth store!')
      }
    } else {
      console.error('No auth-store found in localStorage!')
    }

    // Wait for workout page to load
    await page.waitForLoadState('networkidle')

    // Check API calls made after login
    const tokenCalls = apiCalls.filter((call) => call.url.includes('/token'))
    const workoutCalls = apiCalls.filter((call) =>
      call.url.includes('/api/Workout')
    )

    console.log('Token API calls:', tokenCalls.length)
    console.log('Workout API calls:', workoutCalls.length)

    // Verify workout calls have Authorization header
    workoutCalls.forEach((call) => {
      console.log('Workout call headers:', call.headers)
      if (!call.headers.authorization && !call.headers.Authorization) {
        console.error('Workout API call missing Authorization header!')
      }
    })

    // Check if workout content loads
    const workoutError = page.locator('[data-testid="error-message"]')
    if (await workoutError.isVisible()) {
      const errorText = await workoutError.textContent()
      console.log('Workout loading error:', errorText)

      // Take screenshot for debugging
      await page.screenshot({ path: 'workout-auth-error.png', fullPage: true })

      // Check network tab
      const failedRequests = apiCalls.filter(
        (call) =>
          call.url.includes('/api/Workout') &&
          !call.headers.authorization &&
          !call.headers.Authorization
      )

      if (failedRequests.length > 0) {
        console.error(
          'Found workout API calls without auth headers:',
          failedRequests
        )
      }

      throw new Error(`Workout loading failed: ${errorText}`)
    }

    // Verify workout content is displayed
    await expect(page.locator('[data-testid="workout-content"]')).toBeVisible()
  })

  test('should check axios interceptor configuration', async ({ page }) => {
    // Navigate to login page to load the app
    await page.goto('/login')

    // Check axios configuration
    const axiosConfig = await page.evaluate(() => {
      // This will check if axios interceptors are set up
      return new Promise((resolve) => {
        setTimeout(() => {
          // Try to access window.axios if exposed for debugging
          const result = {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            hasAxios: typeof (window as any).axios !== 'undefined',
            hasInterceptors: false,
            interceptorCount: 0,
          }

          if (result.hasAxios) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const { axios } = window as any
            result.hasInterceptors =
              axios.interceptors?.request?.handlers?.length > 0
            result.interceptorCount =
              axios.interceptors?.request?.handlers?.length || 0
          }

          resolve(result)
        }, 1000)
      })
    })

    console.log('Axios configuration check:', axiosConfig)
  })

  test('should manually test auth token flow', async ({ request }) => {
    const apiUrl = 'https://drmuscle.azurewebsites.net'

    // Step 1: Login via API directly
    console.log('Step 1: Direct API login')
    const loginResponse = await request.post(`${apiUrl}/token`, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: 'grant_type=password&username=<EMAIL>&password=testpassword',
    })

    console.log('Login response status:', loginResponse.status())
    const loginData = await loginResponse.json().catch(() => null)
    console.log('Login response data:', loginData)

    if (loginData?.access_token) {
      // Step 2: Test workout API with token
      console.log('Step 2: Testing workout API with token')
      const workoutResponse = await request.post(
        `${apiUrl}/api/Workout/GetUserWorkoutTemplateGroup`,
        {
          headers: {
            Authorization: `Bearer ${loginData.access_token}`,
            'Content-Type': 'application/json',
          },
          data: {},
        }
      )

      console.log('Workout API response status:', workoutResponse.status())
      console.log('Workout API headers:', workoutResponse.headers())

      if (workoutResponse.status() === 401) {
        console.error('Token was not accepted by workout API!')
      }
    }
  })
})
