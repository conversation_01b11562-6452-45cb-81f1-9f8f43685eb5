I'll analyze the MAUI mobile app to understand how exercise steps are saved during workouts. Let me search for the relevant API calls and local handling.

Let me search for more specific code examples to provide complete implementation details.

Based on my analysis of the MAUI mobile app, here's the complete sequence of API calls for saving exercise steps during workouts, with endpoints and code examples:

## API Calls for Saving Exercise Steps (In Order)

### 1. **Save Individual Sets During Exercise**

**Endpoint:** `POST /api/Exercise/AddWorkoutLogSerieNew`  
**Called:** After each set completion  
**Local Handling:**

```csharp
// Create set model
WorkoutLogSerieModel serieModel = new WorkoutLogSerieModel()
{
    Exercice = new ExerciceModel() { Id = exerciseId },
    Reps = currentReps,
    Weight = new MultiUnityWeight(currentWeight, "kg"),
    IsWarmups = isWarmup,
    RIR = rirValue,
    UserId = userId,
    LogDate = DateTime.Now
};

// Store locally first
workoutLogSerieModel.Add(serieModel);

// Save to API
BooleanModel result = await DrMuscleRestClient.Instance.AddWorkoutLogSerie(serieModel);
```

### 2. **Batch Save Sets (Alternative to #1)**

**Endpoint:** `POST /api/Exercise/AddWorkoutLogSerieListNew`  
**Called:** After completing all sets for an exercise  
**Local Handling:**

```csharp
List<WorkoutLogSerieModel> allSets = new List<WorkoutLogSerieModel>();
// Collect all sets for the exercise
foreach (var set in exerciseSets)
{
    allSets.Add(new WorkoutLogSerieModel()
    {
        Id = set.Id,
        Reps = set.Reps,
        Weight = set.Weight,
        Exercice = exerciseModel
    });
}

// Batch save
await DrMuscleRestClient.Instance.AddWorkoutLogSerieListwithoutLoader(allSets);
```

### 3. **Update Exercise Light Session Status**

**Endpoint:** `POST /api/Exercise/AddUpdateExerciseUserLightSession`  
**Called:** After completing an exercise  
**Local Handling:**

```csharp
await DrMuscleRestClient.Instance.AddUpdateExerciseUserLightSession(new LightSessionModel()
{
    ExerciseId = exerciseId,
    IsLightSession = isLightSession,
    MaxChallengeDate = maxDate
});
```

### 4. **Complete/Save Entire Workout**

**Endpoint:** `POST /api/Workout/SaveWorkoutV3Pro`  
**Called:** When finishing the workout  
**Local Handling:**

```csharp
// Determine workout ID based on template structure
long workoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id;
long templateId = CurrentLog.Instance.CurrentWorkoutTemplateGroup.Id;

var saveModel = new SaveWorkoutModel()
{
    WorkoutId = workoutId,
    WorkoutTemplateId = templateId
};

BooleanModel success = await DrMuscleRestClient.Instance.SaveWorkoutV3(saveModel);
```

### 5. **Get Workout Summary Info**

**Endpoint:** `POST /api/Workout/SaveGetWorkoutInfoPro`  
**Called:** After saving workout to get summary  
**Local Handling:**

```csharp
// Get workout summary and recommendations
GetUserProgramInfoResponseModel workoutInfo =
    await DrMuscleRestClient.Instance.SaveGetWorkoutInfo(new SaveWorkoutModel()
    {
        WorkoutId = workoutId,
        WorkoutTemplateId = templateId
    });

// Process response
var totalSets = workoutInfo.TotalSets;
var totalWeight = workoutInfo.TotalWeight;
var nextWorkoutDate = workoutInfo.NextWorkoutDate;
```

## Local State Management

**CurrentLog Singleton:**

```csharp
public class CurrentLog
{
    public static CurrentLog Instance { get; } = new CurrentLog();

    // Stores sets by exercise ID
    public Dictionary<long, List<WorkoutLogSerieModel>> WorkoutLogSeriesByExercise;

    // Current workout template
    public GetUserWorkoutTemplateResponseModel CurrentWorkoutTemplate;

    // AI recommendations per exercise
    public Dictionary<long, RecommendationModel> RecommendationsByExercise;
}
```

**Local Database Updates:**

```csharp
// Track workout progress
LocalDBManager.Instance.SetDBSetting("WorkoutInProgress", "true");
LocalDBManager.Instance.SetDBSetting("WorkoutInProgressTime", DateTime.Now.ToString());

// Track completed exercises
LocalDBManager.Instance.SetDBSetting($"Exercises{DateTime.Now.Date}", exerciseCount);

// Track completed sets
LocalDBManager.Instance.SetDBSetting($"Sets{DateTime.Now.Date}", setCount);

// Save last workout info
LocalDBManager.Instance.SetDBSetting("last_workout_label", workoutLabel);
LocalDBManager.Instance.SetDBSetting("lastDoneProgram", templateId);
```

## Complete Flow Summary

1. **Start Workout** → Initialize CurrentLog.Instance
2. **During Exercise:**
   - User completes set → Create WorkoutLogSerieModel
   - Save to local collection
   - Call AddWorkoutLogSerieNew API
3. **After Each Exercise:**
   - Call AddWorkoutLogSerieListNew (if batch mode)
   - Update exercise light session status
   - Clear exercise-specific data
4. **Finish Workout:**
   - Call SaveWorkoutV3Pro
   - Call SaveGetWorkoutInfoPro for summary
   - Update local database
   - Clear workout state

This architecture ensures data persistence through local storage first, then syncs with the API, providing resilience against connectivity issues.
