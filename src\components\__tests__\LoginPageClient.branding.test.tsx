import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { LoginPageClient } from '../LoginPageClient'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

vi.mock('../LoginForm', () => ({
  LoginForm: () => <div data-testid="login-form">Login Form</div>,
}))

vi.mock('../auth/QuickSuccessScreen', () => ({
  QuickSuccessScreen: () => (
    <div data-testid="success-screen">Success Screen</div>
  ),
}))

vi.mock('../Logo', () => ({
  Logo: () => <div data-testid="logo">Logo Component</div>,
}))

describe('LoginPageClient - Branding Update', () => {
  it('should display Logo component with app title', () => {
    render(<LoginPageClient />)

    // Logo component should be rendered
    expect(screen.getByTestId('logo')).toBeInTheDocument()

    // App title should exist as h1
    const title = screen.getByRole('heading', {
      level: 1,
      name: 'Dr. Muscle X',
    })
    expect(title).toBeInTheDocument()
    expect(title).toHaveClass('font-heading')
    expect(title).toHaveClass('font-bold')
  })

  it('should display new tagline', () => {
    render(<LoginPageClient />)

    // New tagline should be displayed
    expect(
      screen.getByText("World's Fastest AI Personal Trainer")
    ).toBeInTheDocument()

    // Old tagline should not exist
    expect(
      screen.queryByText('Login to continue your workout')
    ).not.toBeInTheDocument()
  })

  it('should apply heading font to tagline', () => {
    render(<LoginPageClient />)

    const tagline = screen.getByText("World's Fastest AI Personal Trainer")
    expect(tagline).toHaveClass('font-heading')
  })

  it('should maintain proper mobile-first layout', () => {
    render(<LoginPageClient />)

    // Check main container has proper mobile classes
    const mainContent = screen.getByRole('main')
    expect(mainContent).toHaveClass('flex-1')
    expect(mainContent).toHaveClass('flex')
    expect(mainContent).toHaveClass('items-center')
    expect(mainContent).toHaveClass('justify-center')
    expect(mainContent).toHaveClass('px-4')
  })

  it('should have proper spacing between logo and tagline', () => {
    render(<LoginPageClient />)

    // The header div containing logo, title, and tagline
    const headerDiv =
      screen.getByTestId('logo').parentElement?.parentElement?.parentElement
    expect(headerDiv).toHaveClass('mb-8')
  })

  it('should center align logo and tagline', () => {
    render(<LoginPageClient />)

    // The header div containing logo, title, and tagline
    const headerDiv =
      screen.getByTestId('logo').parentElement?.parentElement?.parentElement
    expect(headerDiv).toHaveClass('text-center')
  })

  it('should maintain responsive max-width', () => {
    render(<LoginPageClient />)

    const contentWrapper = screen.getByRole('main').querySelector('.max-w-md')
    expect(contentWrapper).toBeInTheDocument()
    expect(contentWrapper).toHaveClass('w-full')
  })

  it('should keep PWA safe areas intact', () => {
    render(<LoginPageClient />)

    // Check safe area elements exist
    expect(document.querySelector('.safe-area-top')).toBeInTheDocument()
    expect(document.querySelector('.safe-area-bottom')).toBeInTheDocument()
  })

  it('should maintain login form functionality', () => {
    render(<LoginPageClient />)

    // Login form should still be rendered
    expect(screen.getByTestId('login-form')).toBeInTheDocument()
  })

  it('should apply appropriate font sizes for mobile', () => {
    render(<LoginPageClient />)

    const tagline = screen.getByText("World's Fastest AI Personal Trainer")
    expect(tagline).toHaveClass('text-lg')
  })

  it('should display proper visual hierarchy: logo -> title -> tagline', () => {
    render(<LoginPageClient />)

    // Get all elements in order
    const logo = screen.getByTestId('logo')
    const title = screen.getByRole('heading', {
      level: 1,
      name: 'Dr. Muscle X',
    })
    const tagline = screen.getByText("World's Fastest AI Personal Trainer")

    // Verify they all exist
    expect(logo).toBeInTheDocument()
    expect(title).toBeInTheDocument()
    expect(tagline).toBeInTheDocument()

    // Title should have larger font size than tagline
    expect(title).toHaveClass('text-3xl')
    expect(tagline).toHaveClass('text-lg')
  })
})
