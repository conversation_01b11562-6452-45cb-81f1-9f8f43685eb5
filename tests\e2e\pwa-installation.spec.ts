import { test, expect, Page, BrowserContext } from '@playwright/test'

test.describe('PWA Installation Tests', () => {
  let page: Page
  let context: BrowserContext

  test.beforeEach(async ({ page: p, context: c }) => {
    page = p
    context = c

    // Grant permissions for PWA features
    await context.grantPermissions(['notifications'])

    // Enable service worker
    await page.goto('/')
  })

  test('should have valid web app manifest', async () => {
    const response = await page.goto('/manifest.json')
    expect(response?.status()).toBe(200)

    const manifest = await response?.json()

    // Verify required manifest properties
    expect(manifest).toHaveProperty('name', 'Dr. Muscle X')
    expect(manifest).toHaveProperty('short_name')
    expect(manifest).toHaveProperty('start_url')
    expect(manifest).toHaveProperty('display', 'standalone')
    expect(manifest).toHaveProperty('theme_color')
    expect(manifest).toHaveProperty('background_color')
    expect(manifest).toHaveProperty('icons')

    // Verify icons
    expect(manifest.icons.length).toBeGreaterThan(0)
    const has192Icon = manifest.icons.some((icon: any) =>
      icon.sizes.includes('192x192')
    )
    const has512Icon = manifest.icons.some((icon: any) =>
      icon.sizes.includes('512x512')
    )
    expect(has192Icon).toBe(true)
    expect(has512Icon).toBe(true)
  })

  test('should register service worker successfully', async () => {
    await page.goto('/')

    // Wait for service worker registration
    const swRegistered = await page.evaluate(async () => {
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations()
        return registrations.length > 0
      }
      return false
    })

    expect(swRegistered).toBe(true)

    // Check service worker state
    const swState = await page.evaluate(async () => {
      const registrations = await navigator.serviceWorker.getRegistrations()
      if (registrations.length > 0) {
        const sw =
          registrations[0].active ||
          registrations[0].installing ||
          registrations[0].waiting
        return sw?.state
      }
      return null
    })

    expect(['activated', 'activating', 'installed']).toContain(swState)
  })

  test('should show install prompt on supported browsers', async () => {
    // Mock the beforeinstallprompt event
    await page.evaluateOnNewDocument(() => {
      let deferredPrompt: any

      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault()
        deferredPrompt = e
        // Trigger custom event to notify app
        window.dispatchEvent(new CustomEvent('pwa-install-available'))
      })(
        // Expose for testing
        window as any
      ).triggerInstallPrompt = () => {
        if (deferredPrompt) {
          deferredPrompt.prompt()
          return deferredPrompt.userChoice
        }
        return Promise.resolve({ outcome: 'dismissed' })
      }
    })

    await page.goto('/')

    // Simulate beforeinstallprompt event
    await page.evaluate(() => {
      const event = new Event('beforeinstallprompt')
      ;(event as any).prompt = () => {}
      ;(event as any).userChoice = Promise.resolve({ outcome: 'accepted' })
      window.dispatchEvent(event)
    })

    // Check if install button appears
    const installButton = page.locator('[data-testid="pwa-install-button"]')
    const isVisible = await installButton
      .isVisible({ timeout: 5000 })
      .catch(() => false)

    if (isVisible) {
      // Click install button
      await installButton.click()

      // Verify install prompt was triggered
      const result = await page.evaluate(() =>
        (window as any).triggerInstallPrompt()
      )
      expect(['accepted', 'dismissed']).toContain(result.outcome)
    }
  })

  test('should work in standalone mode when installed', async () => {
    // Simulate standalone mode
    await page.evaluateOnNewDocument(() => {
      Object.defineProperty(window.navigator, 'standalone', {
        get: () => true,
      })

      // Also check for display-mode media query
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: (query: string) => ({
          matches: query === '(display-mode: standalone)',
          media: query,
          onchange: null,
          addEventListener: () => {},
          removeEventListener: () => {},
          dispatchEvent: () => true,
        }),
      })
    })

    await page.goto('/')

    // Check if app detects standalone mode
    const isStandalone = await page.evaluate(() => {
      return (
        (window.navigator as any).standalone ||
        window.matchMedia('(display-mode: standalone)').matches
      )
    })

    expect(isStandalone).toBe(true)

    // UI should adapt for standalone mode (no browser chrome)
    const hasStandaloneUI = await page
      .locator('[data-testid="standalone-header"]')
      .isVisible()
      .catch(() => false)

    // App might show different UI in standalone mode
    if (hasStandaloneUI) {
      expect(hasStandaloneUI).toBe(true)
    }
  })
})
