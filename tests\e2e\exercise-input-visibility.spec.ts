import { test, expect } from '@playwright/test'
import { login } from './helpers'

test.describe('Exercise Input Visibility', () => {
  test.beforeEach(async ({ page }) => {
    // Login with test user
    await login(page, '<EMAIL>', 'Dr123456')
  })

  test('input values should be visible with theme-aware colors', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')

    // Wait for workout to load
    await page.waitForSelector('[data-testid="start-workout-button"]', {
      timeout: 10000,
    })

    // Start workout
    await page.click('[data-testid="start-workout-button"]')

    // Wait for exercise list to load
    await page.waitForSelector('[data-testid^="exercise-"]', { timeout: 10000 })

    // Click on first exercise
    const firstExercise = page.locator('[data-testid^="exercise-"]').first()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Check that input labels are visible
    const repsLabel = page.locator('label:has-text("Reps")')
    await expect(repsLabel).toBeVisible()
    await expect(repsLabel).toHaveCSS('color', /.+/) // Should have a color value

    const weightLabel = page.locator(
      'label:has-text("Weight"), label:has-text("Additional Weight")'
    )
    await expect(weightLabel).toBeVisible()
    await expect(weightLabel).toHaveCSS('color', /.+/) // Should have a color value

    // Check that input fields have proper text color
    const repsInput = page.locator('#reps-input')
    await expect(repsInput).toBeVisible()
    const repsColor = await repsInput.evaluate(
      (el) => window.getComputedStyle(el).color
    )
    expect(repsColor).not.toBe('rgb(255, 255, 255)') // Not white
    expect(repsColor).not.toBe('') // Has a color value

    const weightInput = page.locator('#weight-input')
    await expect(weightInput).toBeVisible()
    const weightColor = await weightInput.evaluate(
      (el) => window.getComputedStyle(el).color
    )
    expect(weightColor).not.toBe('rgb(255, 255, 255)') // Not white
    expect(weightColor).not.toBe('') // Has a color value

    // Type values and verify they are visible
    await repsInput.fill('10')
    await expect(repsInput).toHaveValue('10')

    await weightInput.fill('100')
    await expect(weightInput).toHaveValue('100')

    // Verify values are visible by checking contrast
    const repsContrast = await page.evaluate(() => {
      const input = document.querySelector('#reps-input') as HTMLInputElement
      const computedStyle = window.getComputedStyle(input)
      const bgColor = computedStyle.backgroundColor
      const textColor = computedStyle.color
      return { bgColor, textColor }
    })

    // Ensure text and background are different colors
    expect(repsContrast.textColor).not.toBe(repsContrast.bgColor)
  })

  test('inputs should be visible in all themes', async ({ page }) => {
    const themes = [
      'subtle-depth',
      'flat-bold',
      'glassmorphism',
      'ultra-minimal',
    ]

    // Navigate to an exercise page directly
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="start-workout-button"]')
    await page.click('[data-testid="start-workout-button"]')
    await page.waitForSelector('[data-testid^="exercise-"]')
    await page.locator('[data-testid^="exercise-"]').first().click()
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Test each theme sequentially
    await Promise.all(
      themes.map(async (theme) => {
        // Change theme
        await page.evaluate((themeName) => {
          document.documentElement.setAttribute('data-theme', themeName)
          localStorage.setItem('dr-muscle-theme', themeName)
        }, theme)

        // Wait for theme to apply
        await page.waitForTimeout(100)

        // Verify inputs have proper contrast
        const contrast = await page.evaluate(() => {
          const reps = document.querySelector('#reps-input') as HTMLInputElement
          const weight = document.querySelector(
            '#weight-input'
          ) as HTMLInputElement

          const repsStyle = window.getComputedStyle(reps)
          const weightStyle = window.getComputedStyle(weight)

          return {
            reps: { color: repsStyle.color, bg: repsStyle.backgroundColor },
            weight: {
              color: weightStyle.color,
              bg: weightStyle.backgroundColor,
            },
          }
        })

        // Ensure text is visible (not same as background)
        expect(contrast.reps.color).not.toBe(contrast.reps.bg)
        expect(contrast.weight.color).not.toBe(contrast.weight.bg)
      })
    )
  })
})
