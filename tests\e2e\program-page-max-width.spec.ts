import { test, expect } from '@playwright/test'

test.describe('Program Page - Max Width Consistency', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.addInitScript(() => {
      window.localStorage.setItem(
        'auth-storage',
        JSON.stringify({
          state: {
            token: 'test-token',
            user: { id: 1, email: '<EMAIL>' },
            isAuthenticated: true,
          },
        })
      )
    })
  })

  test('should use max-w-lg container on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Navigate to program page
    await page.goto('/program')

    // Wait for content to load
    await page.waitForSelector('[data-testid="program-overview-page"]')

    // Check for max-w-lg container
    const maxWidthContainer = await page.locator('.max-w-lg').first()
    await expect(maxWidthContainer).toBeVisible()

    // Verify it has mx-auto for centering
    await expect(maxWidthContainer).toHaveClass(/mx-auto/)

    // Check that stat cards are within the max-width container
    const statsContainer = await page.locator('.py-6').first()
    const parentWithMaxWidth = await statsContainer.evaluateHandle((el) =>
      el?.closest('.max-w-lg')
    )
    expect(await parentWithMaxWidth.evaluate((el) => el !== null)).toBe(true)
  })

  test('should have consistent max-width with workout overview page', async ({
    page,
  }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Check program page
    await page.goto('/program')
    await page.waitForSelector('[data-testid="program-overview-page"]')
    const programMaxWidth = await page.locator('.max-w-lg').first()
    await expect(programMaxWidth).toBeVisible()

    // Check workout overview page
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="workout-overview-page"]')
    const workoutMaxWidth = await page.locator('.max-w-lg').first()
    await expect(workoutMaxWidth).toBeVisible()

    // Both should have the same max-width class
    await expect(programMaxWidth).toHaveClass(/max-w-lg/)
    await expect(workoutMaxWidth).toHaveClass(/max-w-lg/)
  })

  test('should center content properly on desktop viewport', async ({
    page,
  }) => {
    // Set desktop viewport
    await page.setViewportSize({ width: 1280, height: 720 })

    // Navigate to program page
    await page.goto('/program')

    // Wait for content to load
    await page.waitForSelector('[data-testid="program-overview-page"]')

    // Check that max-w-lg container is centered with mx-auto
    const maxWidthContainer = await page.locator('.max-w-lg.mx-auto').first()
    await expect(maxWidthContainer).toBeVisible()

    // Verify the container doesn't span full width on desktop
    const containerBox = await maxWidthContainer.boundingBox()
    expect(containerBox?.width).toBeLessThan(600) // max-w-lg is ~512px
  })
})
