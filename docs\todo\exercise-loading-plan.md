# Revised Workout Recommendation Loading Flow for Web App

Overview

The mobile app uses a two-phase loading strategy:

Home Page: Loads basic workout program information without exercise recommendations

Workout Page: Loads complete exercise recommendations for all exercises in the workout, handling null recommendations gracefully for exercises without history
API Endpoints

Here are the 8 main API endpoints you'll need:

1. Get User Workout Program (Home Page)

POST /api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo
Body: TimeZoneInfo object
Returns: WorkoutLogAverage with program info 2. Get Recommendation for Normal Sets

POST /api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew
Body: GetRecommendationForExerciseModel
Returns: RecommendationModel or null (if no history) 3. Get Recommendation for Rest-Pause Sets

POST /api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew
Body: GetRecommendationForExerciseModel
Returns: RecommendationModel or null (if no history) 4. Get Customized Current Workout

POST /api/Workout/GetUserCustomizedCurrentWorkout
Body: Workout ID (long)
Returns: WorkoutTemplateModel with exercises 5. Save Workout Info

POST /api/Exercise/SaveGetWorkoutInfo
Body: SaveWorkoutModel { WorkoutId, WorkoutTemplateId }
Returns: Updated workout info 6. Get Exercise History

POST /api/Exercise/GetExerciseWorkoutHistory
Body: Exercise ID (long)
Returns: List<HistoryModel> 7. Get Workout History

POST /api/Exercise/GetWorkoutHistory
Body: null
Returns: List<HistoryModel> 8. Get User Workout Log Date

POST /api/Exercise/GetUserWorkoutLogDate
Body: null
Returns: List of workout dates
Data Models
GetRecommendationForExerciseModel
typescript

interface GetRecommendationForExerciseModel {
Username: string
ExerciseId: number
WorkoutId?: number
IsQuickMode?: boolean | null // Can be true, false, or null
LightSessionDays?: number | null // Only set if > 9 days since last workout
SwapedExId?: number | null
IsStrengthPhashe: boolean // Note the typo - API expects this spelling
IsFreePlan: boolean
IsFirstWorkoutOfStrengthPhase: boolean
VersionNo: number
}
API Response Structure
typescript

interface ApiResponse<T> {
StatusCode: number
ErrorMessage: string | null
Result: T | null // Can be null even with 200 status!
}
RecommendationModel
typescript

interface RecommendationModel {
Series: number // Number of sets
Reps: number // Number of reps
Weight: MultiUnityWeight // Weight recommendation
OneRMProgress: number
RecommendationInKg: number
OneRMPercentage: number
WarmUpsList: WarmUp[] // Warmup sets
NbPauses: number // Rest time in seconds (not RpRest)
IsBodyweight: boolean
IsNormalSets: boolean
IsDeload: boolean
IsBackOffSet: boolean
BackOffSetWeight?: MultiUnityWeight
IsPyramid: boolean
IsReversePyramid: boolean
MinReps: number
MaxReps: number
RIR?: number // Reps in Reserve
LastLogDate?: string // ISO date string
// Equipment flags
isPlateAvailable: boolean
isDumbbellAvailable: boolean
isPulleyAvailable: boolean
isBandsAvailable: boolean
}

interface MultiUnityWeight {
Kg: number
Lb: number
Unity: number // 0 for kg, 1 for lb
Entered: number
IsRound: boolean
}
Implementation Flow
Step 1: Home Page Loading
javascript

// On home page load
async function loadHomePageData() {
// Create proper timezone object
const timeZone = {
Id: Intl.DateTimeFormat().resolvedOptions().timeZone || "UTC",
BaseUtcOffset: "00:00:00",
DaylightName: "UTC",
DisplayName: "(UTC) Coordinated Universal Time",
StandardName: "UTC",
SupportsDaylightSavingTime: false
}

// Fetch basic workout program info
const response = await fetch(
'/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
{
method: 'POST',
headers: {
'Content-Type': 'application/json',
'Authorization': `Bearer ${authToken}`
},
body: JSON.stringify(timeZone),
}
)

const apiResponse = await response.json()

if (apiResponse.StatusCode === 200 && apiResponse.Result) {
const programData = apiResponse.Result

    // Store program info
    const nextWorkout = programData.GetUserProgramInfoResponseModel?.NextWorkoutTemplate
    const recommendedProgram = programData.GetUserProgramInfoResponseModel?.RecommendedProgram

    // Display workout name and program on home page
    displayWorkoutInfo(nextWorkout, recommendedProgram)

}

// DO NOT load exercise recommendations here
}
Step 2: Start Workout - Navigate to Workout Page
javascript

async function startWorkout(workoutId) {
// First get the customized workout with exercises
const response = await fetch(
'/api/Workout/GetUserCustomizedCurrentWorkout',
{
method: 'POST',
headers: {
'Content-Type': 'application/json',
'Authorization': `Bearer ${authToken}`
},
body: JSON.stringify(workoutId),
}
)

const apiResponse = await response.json()

if (apiResponse.StatusCode === 200 && apiResponse.Result) {
const workout = apiResponse.Result
// Navigate to workout page with exercises
navigateToWorkoutPage(workout)
}
}
Step 3: Workout Page - Load All Exercise Recommendations
javascript

async function loadWorkoutPage(workout) {
const exercises = workout.Exercises

// Load recommendations for ALL exercises in parallel
const recommendationPromises = exercises.map((exercise) =>
loadExerciseRecommendation(exercise, workout.Id)
)

// Wait for all recommendations to load
const recommendations = await Promise.all(recommendationPromises)

// Store recommendations in memory, handling nulls
exercises.forEach((exercise, index) => {
const apiResponse = recommendations[index]

    if (apiResponse.StatusCode === 200) {
      // Result can be null even with 200 status - this is normal!
      exercise.recommendation = apiResponse.Result
    } else {
      exercise.recommendation = null
    }

})

// Display first exercise
displayExercise(exercises[0])
}

async function loadExerciseRecommendation(exercise, workoutId) {
// Calculate parameters
const daysSinceLastWorkout = calculateDaysSinceLastWorkout()

const requestBody = {
Username: userEmail,
ExerciseId: exercise.Id,
WorkoutId: workoutId,
IsQuickMode: getUserSetting('QuickMode') === 'true' ? true :
getUserSetting('QuickMode') === 'false' ? false : null,
LightSessionDays: daysSinceLastWorkout > 9 ? daysSinceLastWorkout : null,
SwapedExId: null,
IsStrengthPhashe: calculateIsStrengthPhase(), // Keep the typo!
IsFreePlan: calculateIsFreePlan(),
IsFirstWorkoutOfStrengthPhase: calculateIsFirstStrengthPhase(),
VersionNo: 1,
}

// Determine which endpoint to use
const setStyle = getUserSetting('SetStyle') || 'Normal'
const useNormalEndpoint =
setStyle === 'Normal' ||
exercise.Id === 16508 ||
exercise.BodyPartId === 12 ||
exercise.IsFlexibility

const endpoint = useNormalEndpoint
? '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'
: '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew'

const response = await fetch(endpoint, {
method: 'POST',
headers: {
'Content-Type': 'application/json',
'Authorization': `Bearer ${authToken}`
},
body: JSON.stringify(requestBody),
})

return await response.json()
}
Step 4: Display Exercise with Recommendations (Handling Nulls)
javascript

function displayExercise(exercise) {
const recommendation = exercise.recommendation

if (recommendation) {
// We have history - use API recommendations
displayRecommendedSets(exercise, recommendation)
} else {
// No history - show default/manual entry UI
displayDefaultSets(exercise)
}
}

function displayRecommendedSets(exercise, recommendation) {
// Display warmup sets if any
if (recommendation.WarmUpsList && recommendation.WarmUpsList.length > 0) {
recommendation.WarmUpsList.forEach((warmup, index) => {
displaySet({
setNumber: `W${index + 1}`,
reps: warmup.WarmUpReps,
weight: warmup.WarmUpWeightSet,
isWarmup: true,
})
})
}

// Display working sets
for (let i = 0; i < recommendation.Series; i++) {
displaySet({
setNumber: i + 1,
reps: recommendation.Reps,
weight: recommendation.Weight,
isWarmup: false,
restTime: recommendation.NbPauses, // Use NbPauses, not RpRest
})
}

// Show last performed date if available
if (recommendation.LastLogDate) {
displayLastPerformed(recommendation.LastLogDate)
}
}

function displayDefaultSets(exercise) {
// No recommendation - provide sensible defaults
const sets = []

if (exercise.IsBodyweight) {
// Bodyweight exercise - no weight needed
for (let i = 1; i <= 3; i++) {
sets.push({
setNumber: i,
reps: exercise.RepsMinValue || 8,
weight: { Kg: 0, Lb: 0, Unity: 0, Entered: 0 },
isWarmup: false,
restTime: 60
})
}
} else {
// Weighted exercise - prompt for starting weight
showManualWeightEntry(exercise, (startingWeight) => {
// Add warmup if weight > 20kg
if (startingWeight.Kg > 20) {
sets.push({
setNumber: 'W1',
reps: 5,
weight: {
Kg: startingWeight.Kg _ 0.5,
Lb: startingWeight.Lb _ 0.5,
Unity: startingWeight.Unity,
Entered: startingWeight.Entered \* 0.5
},
isWarmup: true
})
}

      // Add working sets
      for (let i = 1; i <= 3; i++) {
        sets.push({
          setNumber: i,
          reps: exercise.RepsMinValue || 8,
          weight: startingWeight,
          isWarmup: false,
          restTime: 90
        })
      }

      displaySets(sets)
    })
    return

}

displaySets(sets)
showFirstTimeExerciseMessage(exercise)
}

function showFirstTimeExerciseMessage(exercise) {
displayMessage(`First time performing ${exercise.Label}! Start conservatively and focus on proper form. The app will learn from you.`)
}
Step 5: Calculate User Settings
javascript

function calculateIsFreePlan() {
if (!isUserOnFreePlan()) return false

const dailyReset = localStorage.getItem('DailyReset')
return dailyReset && parseInt(dailyReset) > 0
}

function calculateIsStrengthPhase() {
const strengthPhaseSetting = getUserSetting('StrengthPhase')
if (strengthPhaseSetting === 'false') return false

const programName = getCurrentProgramName()
if (programName.toLowerCase().includes('bodyweight') ||
programName.toLowerCase().includes('bands')) {
return false
}

// Implement full calculation based on program progress
const { isStrengthPhase } = calculateStrengthPhaseStatus()
return isStrengthPhase
}

function calculateDaysSinceLastWorkout() {
const lastWorkoutDate = getLastWorkoutDate()
if (!lastWorkoutDate) return null

const daysSince = Math.floor((Date.now() - lastWorkoutDate) / (1000 _ 60 _ 60 \* 24))
return daysSince
}
Step 6: Caching Strategy with Null Handling
javascript

// Cache recommendations including nulls
function cacheRecommendations(workoutId, exercises) {
const cacheData = exercises.map(ex => ({
exerciseId: ex.Id,
recommendation: ex.recommendation // Can be null
}))

const cacheKey = `workout_${workoutId}_${new Date().toDateString()}`
localStorage.setItem(cacheKey, JSON.stringify(cacheData))
}

// Load from cache
async function loadWorkoutWithCache(workout) {
const cacheKey = `workout_${workout.Id}_${new Date().toDateString()}`
const cached = localStorage.getItem(cacheKey)

if (cached && !isWorkoutInProgress()) {
const cacheData = JSON.parse(cached)

    // Apply cached recommendations (including nulls)
    workout.Exercises.forEach(exercise => {
      const cachedRec = cacheData.find(c => c.exerciseId === exercise.Id)
      if (cachedRec) {
        exercise.recommendation = cachedRec.recommendation
      }
    })

    return workout

}

// Load fresh recommendations
return await loadWorkoutPage(workout)
}

Key Implementation Changes
Handle Null Recommendations: Accept Result: null as valid - it means no workout history exists for that exercise.

Default UI for New Exercises: When recommendations are null, show manual weight/rep entry UI.

Correct API Endpoints: Use /api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo and /api/Workout/GetUserCustomizedCurrentWorkout.

Parameter Typo: Keep IsStrengthPhashe (not IsStrengthPhase) - the API expects this typo.

Null vs False: IsQuickMode can be true, false, or null - these are three different states.

Rest Time Field: Use NbPauses not RpRest for rest time in seconds.

Light Sessions: Only set LightSessionDays when > 9 days since last workout.

Equipment Defaults: For new exercises, consider equipment availability when suggesting starting weights.

This implementation ensures the web app matches the mobile app's behavior while gracefully handling exercises without workout history.
