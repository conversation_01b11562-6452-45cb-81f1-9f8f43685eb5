import { renderHook } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useWorkoutRecommendations } from '../useWorkoutRecommendations'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'

vi.mock('@/stores/workoutStore')
vi.mock('@/stores/authStore')
vi.mock('@/utils/logger', () => ({
  logger: {
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}))

describe('useWorkoutRecommendations - Infinite Loop Prevention', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    vi.mocked(useWorkoutStore).mockReturnValue({
      getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
      setCachedExerciseRecommendation: vi.fn(),
      currentWorkout: { Id: 123 },
    } as any)

    vi.mocked(useAuthStore).mockReturnValue({
      user: { Email: '<EMAIL>' },
      isRestoringToken: false,
    } as any)
  })

  it('should not trigger infinite loops when dependencies change', async () => {
    let renderCount = 0

    const { rerender } = renderHook(() => {
      renderCount++
      return useWorkoutRecommendations()
    })

    // Initial render
    expect(renderCount).toBe(1)

    // Simulate dependency changes
    for (let i = 0; i < 5; i++) {
      vi.mocked(useWorkoutStore).mockReturnValue({
        getCachedExerciseRecommendation: vi.fn().mockReturnValue(null),
        setCachedExerciseRecommendation: vi.fn(),
        currentWorkout: { Id: 123 + i }, // Changing workout ID
      } as any)

      rerender()
    }

    // Should have rendered 6 times total (1 initial + 5 rerenders)
    expect(renderCount).toBe(6)

    // Not 100+ times like in the bug
    expect(renderCount).toBeLessThan(10)
  })

  it('should not call logger excessively', async () => {
    const mockLogger = await import('@/utils/logger').then((m) => m.logger)

    const { result } = renderHook(() => useWorkoutRecommendations())

    // Call loadRecommendation
    await result.current.loadRecommendation(1, 'Test Exercise')

    // Logger should be called but not excessively
    const totalLogCalls =
      (mockLogger.log as any).mock.calls.length +
      (mockLogger.warn as any).mock.calls.length +
      (mockLogger.error as any).mock.calls.length

    expect(totalLogCalls).toBeLessThan(10)
  })
})
