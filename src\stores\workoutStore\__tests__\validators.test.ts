import { describe, it, expect, vi } from 'vitest'
import { isValidWorkoutTemplate } from '../validators'

// Mock logger to avoid console output in tests
vi.mock('@/utils/logger', () => ({
  logger: {
    error: vi.fn(),
    log: vi.fn(),
    warn: vi.fn(),
  },
}))

describe('isValidWorkoutTemplate', () => {
  it('should accept workout template with Exercises field', () => {
    const workout = {
      Id: 123,
      Label: 'Test Workout',
      Exercises: [
        {
          Id: 1,
          Label: 'Bench Press',
        },
      ],
      UserId: 'user123',
      IsSystemExercise: false,
      WorkoutSettingsModel: {},
    }

    expect(isValidWorkoutTemplate(workout)).toBe(true)
  })

  it('should accept workout template with Exercices field (French spelling)', () => {
    const workout = {
      Id: 123,
      Label: 'Test Workout',
      Exercices: [
        {
          Id: 1,
          Label: 'Bench Press',
        },
      ],
      UserId: 'user123',
      IsSystemExercise: false,
      WorkoutSettingsModel: {},
    }

    expect(isValidWorkoutTemplate(workout)).toBe(true)
  })

  it('should accept workout template with string Id', () => {
    const workout = {
      Id: '123',
      Label: 'Test Workout',
      Exercises: [],
      UserId: 'user123',
      IsSystemExercise: false,
      WorkoutSettingsModel: {},
    }

    expect(isValidWorkoutTemplate(workout)).toBe(true)
  })

  it('should reject workout template without Id', () => {
    const workout = {
      Label: 'Test Workout',
      Exercises: [],
    }

    expect(isValidWorkoutTemplate(workout)).toBe(false)
  })

  it('should reject workout template without Label', () => {
    const workout = {
      Id: 123,
      Exercises: [],
    }

    expect(isValidWorkoutTemplate(workout)).toBe(false)
  })

  it('should reject workout template without exercises field', () => {
    const workout = {
      Id: 123,
      Label: 'Test Workout',
    }

    expect(isValidWorkoutTemplate(workout)).toBe(false)
  })

  it('should reject workout template with non-array exercises', () => {
    const workout = {
      Id: 123,
      Label: 'Test Workout',
      Exercises: 'not an array',
    }

    expect(isValidWorkoutTemplate(workout)).toBe(false)
  })

  it('should handle null input', () => {
    expect(isValidWorkoutTemplate(null)).toBe(false)
  })

  it('should handle undefined input', () => {
    expect(isValidWorkoutTemplate(undefined)).toBe(false)
  })

  it('should handle non-object input', () => {
    expect(isValidWorkoutTemplate('string')).toBe(false)
    expect(isValidWorkoutTemplate(123)).toBe(false)
    expect(isValidWorkoutTemplate(true)).toBe(false)
  })
})
