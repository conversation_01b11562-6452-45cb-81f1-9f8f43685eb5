import { useEffect, useCallback, useState, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  getUserWorkoutProgramInfo,
  getTodaysWorkout,
  getUserWorkout,
} from '@/services/api/workout'
import { useWorkoutStore } from '@/stores/workoutStore/index'
import { useAuthStore } from '@/stores/authStore'
import { ExerciseLoadingStateManager } from '@/utils/exerciseLoadingState'
import { SetLoader } from '@/api/setLoader'
import type {
  WorkoutTemplateGroupModel,
  WorkoutTemplateModel,
  ExerciseModel,
  ExerciseWorkSetsModel,
} from '@/types'
import type { GetUserWorkoutProgramTimeZoneInfoResponse } from '@/services/api/workout'
import {
  updateExerciseSets,
  markExerciseLoading,
  markExerciseError,
} from '@/types/workout'
import { extractExercisesFromWorkoutGroups } from './useWorkoutDataLoader.helpers'

// Create singleton instances for progressive loading
const exerciseLoadingStateManager = new ExerciseLoadingStateManager()
const setLoader = new SetLoader()

export function useWorkoutDataLoader() {
  const { isAuthenticated } = useAuthStore()
  const {
    setCachedUserProgramInfo,
    setCachedUserWorkouts,
    setCachedTodaysWorkout,
    hasHydrated,
  } = useWorkoutStore()

  // Only enable queries when authenticated and hydrated
  const canMakeApiCalls = isAuthenticated && hasHydrated

  // Optimistic data loading state
  const [optimisticData, setOptimisticData] = useState<{
    userProgramInfo: GetUserWorkoutProgramTimeZoneInfoResponse | null
    userWorkouts: WorkoutTemplateModel[] | null
    todaysWorkout: WorkoutTemplateGroupModel[] | null
    exercises: ExerciseModel[] | null
  }>({
    userProgramInfo: null,
    userWorkouts: null,
    todaysWorkout: null,
    exercises: null,
  })

  // User program info query - only enabled when ready to make API calls
  const userProgramInfoQuery = useQuery({
    queryKey: ['userProgramInfo'],
    queryFn: getUserWorkoutProgramInfo,
    enabled: canMakeApiCalls,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // User workouts query - only enabled when ready to make API calls
  const userWorkoutsQuery = useQuery({
    queryKey: ['userWorkouts'],
    queryFn: () => getUserWorkout(),
    enabled: canMakeApiCalls,
    staleTime: 5 * 60 * 1000,
  })

  // Today's workout query - only enabled when ready to make API calls
  const todaysWorkoutQuery = useQuery({
    queryKey: ['todaysWorkout'],
    queryFn: () => getTodaysWorkout(),
    enabled: canMakeApiCalls,
    staleTime: 5 * 60 * 1000,
  })

  // Update caches when data is fetched
  useEffect(() => {
    if (userProgramInfoQuery.data) {
      setCachedUserProgramInfo(userProgramInfoQuery.data)
      setOptimisticData((prev) => ({
        ...prev,
        userProgramInfo: userProgramInfoQuery.data,
      }))
    }
  }, [userProgramInfoQuery.data, setCachedUserProgramInfo])

  useEffect(() => {
    if (userWorkoutsQuery.data) {
      setCachedUserWorkouts(userWorkoutsQuery.data)
      setOptimisticData((prev) => ({
        ...prev,
        userWorkouts: userWorkoutsQuery.data,
      }))
    }
  }, [userWorkoutsQuery.data, setCachedUserWorkouts])

  useEffect(() => {
    if (todaysWorkoutQuery.data) {
      setCachedTodaysWorkout(todaysWorkoutQuery.data)
      const exercises = extractExercisesFromWorkoutGroups(
        todaysWorkoutQuery.data
      )
      setOptimisticData((prev) => ({
        ...prev,
        todaysWorkout: todaysWorkoutQuery.data,
        exercises,
      }))
    }
  }, [todaysWorkoutQuery.data, setCachedTodaysWorkout])

  const loadExerciseSets = useCallback(
    async (
      exerciseIds: number[],
      _exerciseWorkSetsModels: ExerciseWorkSetsModel[],
      setExerciseWorkSetsModels: React.Dispatch<
        React.SetStateAction<ExerciseWorkSetsModel[]>
      >
    ) => {
      if (exerciseIds.length === 0) return

      // Mark exercises as loading
      exerciseIds.forEach((id) => {
        exerciseLoadingStateManager.startLoading(id)
      })

      // Update UI to show loading state
      setExerciseWorkSetsModels((prev: ExerciseWorkSetsModel[]) =>
        prev.map((exercise: ExerciseWorkSetsModel) => {
          if (exerciseIds.includes(exercise.Id)) {
            return markExerciseLoading(exercise)
          }
          return exercise
        })
      )

      // Load sets in parallel for all exercises
      const results = await setLoader.batchLoadExerciseSets(exerciseIds)

      // Process results and update state
      setExerciseWorkSetsModels((prev: ExerciseWorkSetsModel[]) =>
        prev.map((exercise: ExerciseWorkSetsModel) => {
          const result = results[exercise.Id]
          if (!result) return exercise

          if (result.success) {
            exerciseLoadingStateManager.completeLoading(exercise.Id)
            return updateExerciseSets(exercise, result.sets)
          } else {
            exerciseLoadingStateManager.failLoading(
              exercise.Id,
              result.error || 'Failed to load sets'
            )
            return markExerciseError(
              exercise,
              result.error || 'Failed to load sets'
            )
          }
        })
      )
    },
    []
  )

  const refetchAll = useCallback(() => {
    return Promise.all([
      userProgramInfoQuery.refetch(),
      userWorkoutsQuery.refetch(),
      todaysWorkoutQuery.refetch(),
    ])
  }, [userProgramInfoQuery, userWorkoutsQuery, todaysWorkoutQuery])

  const isLoading =
    userProgramInfoQuery.isLoading ||
    userWorkoutsQuery.isLoading ||
    todaysWorkoutQuery.isLoading

  return useMemo(
    () => ({
      optimisticData,
      userProgramInfoQuery,
      userWorkoutsQuery,
      todaysWorkoutQuery,
      loadExerciseSets,
      refetchAll,
      isLoading,
    }),
    [
      optimisticData,
      userProgramInfoQuery,
      userWorkoutsQuery,
      todaysWorkoutQuery,
      loadExerciseSets,
      refetchAll,
      isLoading,
    ]
  )
}
