import React, { useState, useEffect } from 'react'

export interface ProgramDescriptionProps {
  /** Program description text */
  description: string
  /** Maximum number of lines to show when collapsed */
  maxLines?: number
  /** Loading state */
  isLoading?: boolean
  /** Additional CSS classes */
  className?: string
}

export function ProgramDescription({
  description,
  maxLines = 3,
  isLoading = false,
  className = '',
}: ProgramDescriptionProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [showButton, setShowButton] = useState(false)

  // Check for reduced motion preference
  const prefersReducedMotion =
    typeof window !== 'undefined' &&
    window.matchMedia('(prefers-reduced-motion: reduce)').matches

  // Determine if content needs truncation
  // In a real app, we'd measure the actual height, but for simplicity we'll use character count
  useEffect(() => {
    // Rough estimate: ~50 chars per line on mobile
    const estimatedLines = Math.ceil(description.length / 50)
    setShowButton(estimatedLines > maxLines)
  }, [description, maxLines])

  if (isLoading) {
    return (
      <div
        data-testid="description-skeleton"
        className={`space-y-2 ${className}`}
      >
        <div className="h-4 bg-bg-tertiary rounded-theme animate-pulse" />
        <div className="h-4 bg-bg-tertiary rounded-theme animate-pulse w-5/6" />
        <div className="h-4 bg-bg-tertiary rounded-theme animate-pulse w-4/6" />
      </div>
    )
  }

  // Generate line-clamp class based on maxLines
  const getLineClampClass = () => {
    if (isExpanded || !showButton) return ''
    switch (maxLines) {
      case 1:
        return 'line-clamp-1'
      case 2:
        return 'line-clamp-2'
      case 3:
        return 'line-clamp-3'
      case 4:
        return 'line-clamp-4'
      case 5:
        return 'line-clamp-5'
      case 6:
        return 'line-clamp-6'
      default:
        return 'line-clamp-3'
    }
  }

  return (
    <div
      data-testid="program-description-wrapper"
      className={`space-y-2 ${className}`}
    >
      <p
        data-testid="program-description"
        className={`
          text-base leading-relaxed text-text-secondary
          transition-all ease-in-out
          ${prefersReducedMotion ? 'duration-0' : 'duration-300'}
          ${getLineClampClass()}
        `}
      >
        {description}
      </p>

      {showButton && (
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          aria-expanded={isExpanded}
          className={`
            text-brand-primary font-medium text-base
            min-h-[44px] px-2 -ml-2
            flex items-center
            hover:underline focus:underline
            focus:outline-none focus:ring-2 focus:ring-brand-primary focus:ring-offset-2 focus:ring-offset-bg-primary
            transition-colors
          `}
        >
          {isExpanded ? 'Show less' : 'Read more'}
        </button>
      )}
    </div>
  )
}
