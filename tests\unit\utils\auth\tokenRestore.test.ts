import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { restoreAuthToken } from '@/utils/auth/tokenRestore'
import { setAuthToken } from '@/api/client'

// Mock the API client
vi.mock('@/api/client', () => ({
  setAuthToken: vi.fn(),
}))

// Mock fetch
global.fetch = vi.fn()

describe('restoreAuthToken', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should restore auth token when authenticated', async () => {
    const mockToken = 'test-auth-token-12345'

    vi.mocked(global.fetch).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        token: mockToken,
        authenticated: true,
      }),
    } as Response)

    const result = await restoreAuthToken()

    expect(result).toBe(true)
    expect(global.fetch).toHaveBeenCalledWith('/api/auth/token', {
      credentials: 'include',
    })
    expect(setAuthToken).toHaveBeenCalledWith(mockToken)
  })

  it('should return false when not authenticated', async () => {
    vi.mocked(global.fetch).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        token: null,
        authenticated: false,
      }),
    } as Response)

    const result = await restoreAuthToken()

    expect(result).toBe(false)
    expect(setAuthToken).not.toHaveBeenCalled()
  })

  it('should handle fetch errors gracefully', async () => {
    vi.mocked(global.fetch).mockRejectedValueOnce(new Error('Network error'))

    const result = await restoreAuthToken()

    expect(result).toBe(false)
    expect(setAuthToken).not.toHaveBeenCalled()
  })

  it('should handle non-ok responses', async () => {
    vi.mocked(global.fetch).mockResolvedValueOnce({
      ok: false,
      status: 500,
    } as Response)

    const result = await restoreAuthToken()

    expect(result).toBe(false)
    expect(setAuthToken).not.toHaveBeenCalled()
  })

  it('should not run on server side', async () => {
    // Simulate server environment
    const originalWindow = global.window
    // @ts-expect-error - Intentionally deleting window for server-side test
    delete global.window

    const result = await restoreAuthToken()

    expect(result).toBe(false)
    expect(global.fetch).not.toHaveBeenCalled()

    // Restore window
    global.window = originalWindow
  })
})
