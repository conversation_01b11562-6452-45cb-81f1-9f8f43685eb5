/**
 * Workout store refactored into modules
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { WorkoutState } from './types'
import type { RecommendationModel } from '@/types'
import { initialState } from './constants'
import { createCacheActions } from './cacheActions'
import { createLoadingActions } from './loadingActions'
import { logger } from '@/utils/logger'

export { type WorkoutState, type CacheStats, type CacheHealth } from './types'

export const useWorkoutStore = create<WorkoutState>()(
  // Disable devtools to reduce console noise
  // devtools(
  persist(
    (set, get) => {
      // Create cache actions
      const cacheActions = createCacheActions(set, get)

      // Create loading actions
      const loadingActions = createLoadingActions(set, get)

      return {
        ...initialState,

        setWorkout: (workout) => {
          set({
            currentWorkout: workout,
            exercises: workout.Exercises || [],
            currentExerciseIndex: 0,
            currentSetIndex: 0,
            error: null,
          })
        },

        startWorkout: () => {
          const { currentWorkout } = get()
          if (!currentWorkout) return

          set({
            workoutSession: {
              id: `session-${Date.now()}`,
              startTime: new Date(),
              exercises: [],
              exerciseRIRStatus: {},
            },
          })
        },

        nextSet: () => {
          set((state) => ({
            currentSetIndex: state.currentSetIndex + 1,
            currentSetData: {},
          }))
        },

        setCurrentSetIndex: (setIndex: number) => {
          set({
            currentSetIndex: setIndex,
            currentSetData: {},
          })
        },

        nextExercise: () => {
          const { currentExerciseIndex, exercises } = get()
          if (currentExerciseIndex < exercises.length - 1) {
            set({
              currentExerciseIndex: currentExerciseIndex + 1,
              currentSetIndex: 0,
              currentSetData: {},
            })
          }
        },

        setCurrentExerciseById: (exerciseId: number) => {
          const { exercises, currentExerciseIndex } = get()
          const exerciseIndex = exercises.findIndex(
            (ex) => ex.Id === exerciseId
          )
          if (exerciseIndex !== -1) {
            // Only reset set index if we're changing to a different exercise
            if (exerciseIndex !== currentExerciseIndex) {
              set({
                currentExerciseIndex: exerciseIndex,
                currentSetIndex: 0,
                currentSetData: {},
              })
            }
            // If it's the same exercise, don't change the set index
          }
        },

        saveSet: (setData) => {
          const { workoutSession } = get()
          if (!workoutSession) return

          const exerciseId = setData.ExerciseId
          if (!exerciseId) return // Exit if no exercise ID

          const existingExercise = workoutSession.exercises.find(
            (e) => e.exerciseId === exerciseId
          )

          const newSet = {
            setNumber: 1, // Default set number, should be calculated based on existing sets
            reps: setData.Reps,
            weight: setData.Weight,
            rir: setData.RIR,
            isWarmup: setData.IsWarmups,
            timestamp: new Date(),
          }

          if (existingExercise) {
            existingExercise.sets.push(newSet)
          } else {
            workoutSession.exercises.push({
              exerciseId,
              name: 'Exercise', // Default name, should be retrieved from exercise data
              sets: [newSet],
            })
          }

          set({ workoutSession: { ...workoutSession } })
        },

        completeWorkout: () => {
          const { workoutSession } = get()
          if (!workoutSession) return

          set({
            workoutSession: {
              ...workoutSession,
              endTime: new Date(),
            },
          })
        },

        updateCurrentSet: (data) => {
          set((state) => ({
            currentSetData: {
              ...state.currentSetData,
              ...data,
            },
          }))
        },

        setLoading: (loading) => {
          set({ isLoading: loading })
        },

        setError: (error) => {
          // Set error without clearing cache
          set({ error, isLoading: false })
        },

        resetWorkout: () => {
          // Reset workout state but preserve cache
          const {
            cachedData,
            cacheStats,
            hasHydrated,
            cacheVersion,
            currentProgram,
            exerciseRecommendations,
          } = get()
          set({
            ...initialState,
            cachedData,
            cacheStats,
            hasHydrated,
            cacheVersion,
            currentProgram,
            exerciseRecommendations,
          })
        },

        clearCache: () => {
          // User-initiated cache clear only
          set({
            cachedData: initialState.cachedData,
            cacheStats: initialState.cacheStats,
          })
        },

        // Loading state management
        clearLoadingState: (exerciseId: number) => {
          set((state) => {
            const newLoadingStates = new Map(state.loadingStates)
            newLoadingStates.delete(exerciseId)
            return { loadingStates: newLoadingStates }
          })
        },

        clearAllLoadingStates: () => {
          set({ loadingStates: new Map() })
        },

        // Getters
        getCurrentExercise: () => {
          const { exercises, currentExerciseIndex } = get()
          return exercises[currentExerciseIndex] || null
        },

        getCurrentSet: () => {
          const { currentSetData } = get()
          return Object.keys(currentSetData).length > 0 ? currentSetData : null
        },

        isWorkoutComplete: () => {
          const { workoutSession } = get()
          return workoutSession?.endTime !== undefined
        },

        getWorkoutDuration: () => {
          const { workoutSession } = get()
          if (!workoutSession || !workoutSession.endTime) return 0

          return (
            workoutSession.endTime.getTime() -
            workoutSession.startTime.getTime()
          )
        },

        getNextExercise: () => {
          const { exercises, currentExerciseIndex } = get()
          if (currentExerciseIndex < exercises.length - 1) {
            return exercises[currentExerciseIndex + 1] || null
          }
          return null
        },

        getRestDuration: () => {
          // Simplified rest duration logic
          return 120 // Default 2 minutes
        },

        getExerciseProgress: () => {
          const {
            currentSetIndex,
            workoutSession,
            exercises,
            currentExerciseIndex,
          } = get()
          const currentExercise = exercises[currentExerciseIndex]

          if (!currentExercise) return null

          // Simplified exercise progress
          const totalSets = 4 // Default sets
          const completedSets = currentSetIndex
          const isFirstWorkSet = currentSetIndex === 0
          const currentSetIsWarmup = false // No warmup detection

          // Check if RIR has been captured for this exercise
          const hasRIR =
            workoutSession?.exerciseRIRStatus?.[currentExercise.Id] || false

          return {
            totalSets,
            completedSets,
            isFirstWorkSet,
            currentSetIsWarmup,
            hasRIR,
          }
        },

        updateSetRIR: (exerciseId) => {
          const { workoutSession } = get()
          if (!workoutSession) return

          // Mark that RIR has been captured for this exercise
          const updatedSession = {
            ...workoutSession,
            exerciseRIRStatus: {
              ...workoutSession.exerciseRIRStatus,
              [exerciseId]: true,
            },
          }

          set({ workoutSession: updatedSession })
        },

        // Add all cache actions
        ...cacheActions,

        // Add all loading actions
        ...loadingActions,
      }
    },
    {
      name: 'drmuscle-workout',
      version: 2, // Increment to clear old cache with "Exercices" format
      migrate: (persistedState: unknown, version: number) => {
        // When version changes, return initial state to clear old cache
        if (version !== 2) {
          logger.log('[WorkoutStore] Cache version mismatch, clearing old data')
          return initialState
        }
        return persistedState
      },
      partialize: (state: WorkoutState) => ({
        workoutSession: state.workoutSession,
        // Limit cachedData to exclude large userWorkouts array
        cachedData: {
          ...state.cachedData,
          userWorkouts: null, // Don't persist the 547 workout templates
          // Only keep the first 10 exercise recommendations to save space
          exerciseRecommendations: Object.fromEntries(
            Object.entries(state.cachedData.exerciseRecommendations).slice(
              0,
              10
            )
          ),
        },
        cacheVersion: state.cacheVersion,
        currentProgram: state.currentProgram,
        // Convert Maps to objects for serialization
        exerciseRecommendations: Object.fromEntries(
          Array.from(state.exerciseRecommendations.entries()).slice(0, 10)
        ),
      }),
      onRehydrateStorage: () => (state: WorkoutState | undefined) => {
        if (state) {
          // Track hydration time
          const hydrationStart = performance.now()

          // Restore Maps from persisted objects
          if (
            state.exerciseRecommendations &&
            typeof state.exerciseRecommendations === 'object' &&
            !(state.exerciseRecommendations instanceof Map)
          ) {
            state.exerciseRecommendations = new Map(
              Object.entries(
                state.exerciseRecommendations as Record<
                  string,
                  RecommendationModel
                >
              )
            )
          }

          // Initialize missing Maps
          if (!state.loadingStates || !(state.loadingStates instanceof Map)) {
            state.loadingStates = new Map()
          }
          if (!state.errors || !(state.errors instanceof Map)) {
            state.errors = new Map()
          }

          state.setHasHydrated(true)
          // Clean expired data on hydration
          state.clearExpiredCache()
          // Update hydration time
          state.cacheStats.hydrationTime = performance.now() - hydrationStart
        }
      },
    }
  )
  // )  // Commented out devtools closing
)
