# OAuth Authentication Implementation Plan

## Project Overview

**Goal**: Enable Google and Apple OAuth authentication in the Dr. Muscle X web application, integrating with the existing authentication system.

**Current State**:

- OAuth buttons exist but are disabled with placeholder functionality
- Existing credentials available from mobile app configuration
- Working authentication system with email/password login
- API endpoints ready: `/api/Account/RegisterWithApple` exists

**Target Architecture**:

- Client-side OAuth flows using official SDKs
- Token validation and user creation/login via existing Dr. Muscle API
- Seamless integration with current auth store and routing
- Progressive enhancement - email login continues to work

---

## Phase 1: Foundation Setup

### Step 1: Dependencies and Environment Setup

**Size**: Small | **Risk**: Low | **Duration**: 30 mins

```
Implement OAuth foundation by installing required dependencies and setting up environment configuration.

CONTEXT:
- The project currently has disabled OAuth buttons in LoginForm.tsx
- Google Web Client ID and Apple configuration are available from mobile app
- Need to add OAuth SDKs and environment variables

REQUIREMENTS:
1. Add environment variables for OAuth configuration
2. Install OAuth SDKs (no additional dependencies needed - using CDN approach)
3. Create environment configuration file
4. Verify environment setup in development

IMPLEMENTATION:
1. Create `.env.local` file with OAuth credentials:
   - NEXT_PUBLIC_GOOGLE_CLIENT_ID=************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com
   - NEXT_PUBLIC_APPLE_CLIENT_ID=com.drmaxmuscle.max
   - NEXT_PUBLIC_APPLE_TEAM_ID=7AAXZ47995

2. Create `src/config/oauth.ts` with OAuth configuration constants
3. Add TypeScript global declarations for OAuth SDKs
4. Create simple environment validation utility

TESTING:
- Verify environment variables are accessible
- Confirm no build errors
- Check TypeScript compilation

ACCEPTANCE CRITERIA:
- Environment variables are properly configured
- OAuth configuration is type-safe and accessible
- No compilation errors
- Development server starts successfully
```

### Step 2: OAuth Types and Interfaces

**Size**: Small | **Risk**: Low | **Duration**: 45 mins

```
Create comprehensive TypeScript types and interfaces for OAuth functionality.

CONTEXT:
- Building on Step 1's environment configuration
- Need type safety for OAuth responses and callbacks
- Integration with existing auth types in src/types/

REQUIREMENTS:
1. Create OAuth-specific TypeScript interfaces
2. Define callback function types
3. Create union types for OAuth providers
4. Integrate with existing auth types

IMPLEMENTATION:
1. Extend `src/types/index.ts` with OAuth types:
   - OAuthProvider union type ('google' | 'apple')
   - GoogleCredentialResponse interface
   - AppleSignInResponse interface
   - OAuthUserData interface
   - OAuthSuccessCallback and OAuthErrorCallback types

2. Create `src/types/oauth.ts` for OAuth-specific types:
   - OAuth SDK window globals
   - Configuration interfaces
   - State management types

3. Update existing LoginSuccessResult to handle OAuth tokens
4. Create utility types for OAuth integration

TESTING:
- TypeScript compilation with strict mode
- Import/export verification
- Type checking with existing code

ACCEPTANCE CRITERIA:
- All OAuth types are properly defined
- No TypeScript compilation errors
- Types integrate cleanly with existing auth types
- IntelliSense support for OAuth APIs
```

---

## Phase 2: Core OAuth Implementation

### Step 3: Google OAuth Core Implementation

**Size**: Medium | **Risk**: Medium | **Duration**: 2 hours

```
Implement Google OAuth authentication using Google Identity Services SDK.

CONTEXT:
- Building on Steps 1-2 with environment and types configured
- Using Google Identity Services (accounts.google.com/gsi/client)
- Web Client ID: ************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com

REQUIREMENTS:
1. Implement Google OAuth SDK loading and initialization
2. Handle Google ID token and user data extraction
3. Implement One Tap and popup fallback flows
4. Add comprehensive error handling

IMPLEMENTATION:
1. Create `src/utils/oauth/googleOAuth.ts`:
   - GoogleOAuthHelper class with static methods
   - SDK loading with script injection
   - Initialize Google Identity Services
   - Handle credential response and JWT decoding
   - Implement popup fallback for One Tap failures

2. Key methods:
   - `initialize()`: Load and configure Google SDK
   - `signIn(onSuccess, onError)`: Initiate OAuth flow
   - `decodeJWT(token)`: Extract user data from ID token
   - `getUserInfo(accessToken)`: Fallback user data fetch

3. Error handling for:
   - SDK loading failures
   - Network errors
   - User cancellation
   - Invalid tokens
   - CORS issues

TESTING:
1. Unit tests for GoogleOAuthHelper class
2. Mock Google SDK responses
3. Test JWT decoding with sample tokens
4. Error scenario testing
5. Integration test with mock callbacks

ACCEPTANCE CRITERIA:
- Google OAuth flow completes successfully
- User data is correctly extracted from ID token
- Error handling covers all failure modes
- Unit tests achieve >90% coverage
- No memory leaks from SDK loading
```

### Step 4: Apple OAuth Core Implementation

**Size**: Medium | **Risk**: Medium | **Duration**: 2 hours

```
Implement Apple Sign-In authentication using Apple's Sign In with Apple JS SDK.

CONTEXT:
- Building on Steps 1-3 with Google OAuth working
- Using Apple Sign In JS (appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js)
- Apple configuration: Team ID 7AAXZ47995, Bundle ID com.drmaxmuscle.max

REQUIREMENTS:
1. Implement Apple OAuth SDK loading and initialization
2. Handle Apple ID token and user data extraction
3. Implement popup-based authentication flow
4. Handle first-time vs. returning user scenarios

IMPLEMENTATION:
1. Create `src/utils/oauth/appleOAuth.ts`:
   - AppleOAuthHelper class with static methods
   - SDK loading with script injection
   - Initialize Apple ID authentication
   - Handle authorization response and ID token
   - Extract user data with fallback for returning users

2. Key methods:
   - `initialize()`: Load and configure Apple SDK
   - `signIn(onSuccess, onError)`: Initiate OAuth flow
   - `extractUserData(response)`: Parse Apple response
   - Handle user object availability (first-time only)

3. Apple-specific considerations:
   - User name only provided on first authorization
   - ID token contains basic user info
   - Popup-based flow with redirect URI
   - State parameter for CSRF protection

TESTING:
1. Unit tests for AppleOAuthHelper class
2. Mock Apple SDK responses
3. Test with and without user object
4. Error scenario testing
5. Integration test with mock callbacks

ACCEPTANCE CRITERIA:
- Apple OAuth flow completes successfully
- User data is correctly extracted from responses
- Handles both first-time and returning users
- Error handling covers all failure modes
- Unit tests achieve >90% coverage
```

### Step 5: OAuth Integration Layer

**Size**: Small | **Risk**: Low | **Duration**: 1 hour

```
Create unified OAuth integration layer that bridges OAuth providers with Dr. Muscle authentication.

CONTEXT:
- Building on Steps 3-4 with both OAuth providers implemented
- Need to integrate with existing auth system from src/api/auth.ts
- Prepare for backend API integration

REQUIREMENTS:
1. Create unified OAuth interface
2. Implement common success/error handling
3. Add performance monitoring integration
4. Prepare for backend API calls

IMPLEMENTATION:
1. Create `src/utils/oauth/oauthIntegration.ts`:
   - OAuthIntegration class for common operations
   - Unified success handler for both providers
   - Error normalization and reporting
   - Performance monitoring integration

2. Key methods:
   - `handleOAuthSuccess(userData, provider)`: Process successful OAuth
   - `normalizeError(error, provider)`: Standardize error messages
   - `validateUserData(userData)`: Ensure required fields present
   - `markPerformance(provider, action)`: Track OAuth performance

3. Integration points:
   - PerformanceMonitor for OAuth timing
   - Logger for OAuth events
   - Error reporting for OAuth failures

TESTING:
1. Unit tests for integration layer
2. Mock OAuth provider responses
3. Test error normalization
4. Performance monitoring verification

ACCEPTANCE CRITERIA:
- Unified interface for both OAuth providers
- Consistent error handling and reporting
- Performance monitoring integrated
- Ready for backend API integration
```

---

## Phase 3: UI Integration

### Step 6: Update LoginForm OAuth Buttons

**Size**: Medium | **Risk**: Low | **Duration**: 1.5 hours

```
Replace disabled OAuth button placeholders with functional OAuth implementations.

CONTEXT:
- Building on Steps 1-5 with complete OAuth implementation
- Current LoginForm has disabled buttons with TODO comments
- Need to integrate with existing form state and error handling

REQUIREMENTS:
1. Replace disabled OAuth buttons with functional implementations
2. Add loading states for OAuth operations
3. Integrate with existing error handling system
4. Maintain accessibility and haptic feedback

IMPLEMENTATION:
1. Update `src/components/LoginForm.tsx`:
   - Import OAuth helper classes
   - Add OAuth loading state management
   - Replace disabled button handlers with OAuth flows
   - Integrate with existing socialError state

2. Button implementation:
   - Google button: Call GoogleOAuthHelper.signIn()
   - Apple button: Call AppleOAuthHelper.signIn()
   - Loading states with spinners
   - Disabled state during OAuth operations

3. State management:
   - oauthLoading: 'google' | 'apple' | null
   - Disable form during OAuth operations
   - Clear social errors on new attempts
   - Haptic feedback for OAuth events

4. Error handling:
   - Display OAuth errors in existing error UI
   - Differentiate OAuth vs form validation errors
   - Clear errors after timeout
   - Graceful degradation for unsupported browsers

TESTING:
1. Unit tests for button interactions
2. Mock OAuth helper responses
3. Test loading states and error handling
4. Accessibility testing
5. Visual regression tests

ACCEPTANCE CRITERIA:
- OAuth buttons are functional and responsive
- Loading states provide clear user feedback
- Errors are displayed appropriately
- Accessibility standards maintained
- No visual regressions in form layout
```

### Step 7: OAuth Success Flow Integration

**Size**: Small | **Risk**: Medium | **Duration**: 1 hour

```
Implement OAuth success handling that integrates with existing authentication flow.

CONTEXT:
- Building on Step 6 with functional OAuth buttons
- Need to bridge OAuth success with existing login success flow
- Current login flow: login() -> auth store -> redirect

REQUIREMENTS:
1. Handle OAuth success in LoginForm
2. Integrate with existing auth store and routing
3. Maintain consistent success flow with email login
4. Add proper performance tracking

IMPLEMENTATION:
1. Update LoginForm OAuth success handlers:
   - Call OAuthIntegration.handleOAuthSuccess()
   - Trigger existing onSuccess callback or router.push()
   - Add performance markers for OAuth completion
   - Maintain consistent user experience

2. Success flow:
   - OAuth provider returns user data
   - Extract email, name, and provider info
   - For now: Show descriptive error about backend integration needed
   - Future: Call backend OAuth endpoint for token exchange

3. Prepare for backend integration:
   - Structure OAuth data for API calls
   - Plan token exchange implementation
   - Design error handling for backend failures

TESTING:
1. Integration tests for OAuth success flow
2. Test routing and callback behavior
3. Performance monitoring verification
4. User experience testing

ACCEPTANCE CRITERIA:
- OAuth success triggers appropriate navigation
- Performance is tracked correctly
- Integration points are prepared for backend
- User experience is consistent with email login
```

---

## Phase 4: Backend Integration

### Step 8: OAuth API Integration

**Size**: Large | **Risk**: High | **Duration**: 3 hours

```
Implement backend API integration for OAuth authentication using existing Dr. Muscle endpoints.

CONTEXT:
- Building on Steps 1-7 with complete frontend OAuth implementation
- Backend has `/api/Account/RegisterWithApple` endpoint available
- Need to integrate with existing token-based authentication system

REQUIREMENTS:
1. Implement OAuth token validation and user creation/login
2. Integrate with existing auth API structure
3. Handle both new user registration and existing user login
4. Maintain compatibility with existing auth store

IMPLEMENTATION:
1. Extend `src/api/auth.ts` with OAuth methods:
   - `oauthLogin(provider, idToken, userData)`: OAuth authentication
   - `validateOAuthToken(provider, token)`: Token validation
   - Handle both Google and Apple OAuth flows

2. OAuth API integration:
   - For Apple: Use existing `/api/Account/RegisterWithApple` endpoint
   - For Google: Implement similar endpoint pattern
   - Send ID token and user data to backend
   - Receive standard LoginSuccessResult response

3. Update OAuthIntegration class:
   - Replace placeholder with actual API calls
   - Handle network errors and retry logic
   - Integrate with existing auth store via useAuth hook

4. Error handling:
   - Network failures
   - Invalid tokens
   - Server errors
   - User already exists scenarios

TESTING:
1. Integration tests with mock API responses
2. Test OAuth token validation
3. Test new user registration flow
4. Test existing user login flow
5. Error scenario testing
6. End-to-end OAuth flow testing

ACCEPTANCE CRITERIA:
- OAuth tokens are properly validated by backend
- New users can register via OAuth
- Existing users can login via OAuth
- Standard auth flow maintained after OAuth
- Comprehensive error handling for all scenarios
```

### Step 9: Auth Store OAuth Integration

**Size**: Small | **Risk**: Low | **Duration**: 45 mins

```
Update authentication store to properly handle OAuth-based authentication.

CONTEXT:
- Building on Step 8 with backend OAuth integration
- Need to ensure auth store handles OAuth tokens correctly
- Existing auth store in src/stores/authStore.ts

REQUIREMENTS:
1. Ensure OAuth login populates auth store correctly
2. Add OAuth provider tracking (optional)
3. Handle OAuth token refresh if needed
4. Maintain existing logout functionality

IMPLEMENTATION:
1. Review and test authStore OAuth compatibility:
   - Verify setAuth() works with OAuth LoginSuccessResult
   - Ensure token storage and retrieval works
   - Test auth state persistence

2. Optional enhancements:
   - Track OAuth provider in user profile
   - Add OAuth-specific token handling
   - Enhanced logout for OAuth users

3. Integration testing:
   - OAuth login -> auth store population
   - Page refresh -> auth state persistence
   - Logout -> proper state clearing

TESTING:
1. Unit tests for auth store OAuth integration
2. Integration tests for OAuth auth flow
3. Persistence testing across page refreshes
4. Logout flow testing

ACCEPTANCE CRITERIA:
- OAuth authentication populates auth store correctly
- Auth state persists across page refreshes
- Logout clears OAuth auth state properly
- No regressions in existing auth functionality
```

---

## Phase 5: Testing and Polish

### Step 10: Comprehensive Testing Suite

**Size**: Large | **Risk**: Medium | **Duration**: 2.5 hours

```
Implement comprehensive testing for the complete OAuth implementation.

CONTEXT:
- Building on Steps 1-9 with complete OAuth functionality
- Need comprehensive test coverage for production readiness
- Existing test infrastructure in place

REQUIREMENTS:
1. Unit tests for all OAuth utilities
2. Integration tests for complete OAuth flows
3. E2E tests for user experience
4. Error scenario and edge case testing

IMPLEMENTATION:
1. Unit Tests:
   - OAuth helper classes (Google and Apple)
   - OAuth integration layer
   - Type safety and error handling
   - Performance monitoring integration

2. Integration Tests:
   - Complete OAuth flows with mocked SDKs
   - API integration with mocked backend
   - Auth store integration
   - Error propagation and handling

3. E2E Tests:
   - Add OAuth tests to existing Playwright suite
   - Test complete user journeys
   - Cross-browser compatibility
   - Mobile device testing

4. Test Infrastructure:
   - Mock OAuth SDK responses
   - Test environment configuration
   - Performance benchmarking
   - Accessibility testing

TESTING AREAS:
- OAuth SDK loading and initialization
- User data extraction and validation
- Error handling and recovery
- Performance and memory usage
- Accessibility compliance
- Security considerations

ACCEPTANCE CRITERIA:
- Unit test coverage >90% for OAuth code
- Integration tests cover all OAuth scenarios
- E2E tests verify complete user journeys
- All tests pass in CI/CD pipeline
- Performance benchmarks meet targets
```

### Step 11: Accessibility and Performance Optimization

**Size**: Medium | **Risk**: Low | **Duration**: 1.5 hours

```
Ensure OAuth implementation meets accessibility standards and performance requirements.

CONTEXT:
- Building on Step 10 with comprehensive testing
- Need to meet production standards for accessibility and performance
- Existing performance monitoring infrastructure

REQUIREMENTS:
1. Accessibility compliance for OAuth buttons and flows
2. Performance optimization for OAuth SDK loading
3. Error message accessibility
4. Mobile-specific optimizations

IMPLEMENTATION:
1. Accessibility improvements:
   - ARIA labels for OAuth buttons
   - Screen reader announcements for OAuth states
   - Keyboard navigation for OAuth flows
   - High contrast mode support

2. Performance optimizations:
   - Lazy loading of OAuth SDKs
   - Preconnect hints for OAuth domains
   - Error boundary for OAuth failures
   - Memory leak prevention

3. Mobile optimizations:
   - Touch target sizing
   - Haptic feedback enhancement
   - Viewport handling for OAuth popups
   - Network-aware loading

TESTING:
1. Accessibility audit with automated tools
2. Screen reader testing
3. Performance benchmarking
4. Mobile device testing
5. Network throttling tests

ACCEPTANCE CRITERIA:
- WCAG 2.1 AA compliance maintained
- OAuth button interactions are accessible
- Performance metrics meet or exceed targets
- Mobile experience is optimized
- No accessibility regressions
```

### Step 12: Production Preparation and Documentation

**Size**: Small | **Risk**: Low | **Duration**: 1 hour

```
Prepare OAuth implementation for production deployment with proper documentation.

CONTEXT:
- Building on Steps 1-11 with complete, tested OAuth implementation
- Need production-ready configuration and documentation
- Integration with existing deployment pipeline

REQUIREMENTS:
1. Production environment configuration
2. Security hardening and review
3. Documentation for OAuth flows
4. Monitoring and alerting setup

IMPLEMENTATION:
1. Production configuration:
   - Environment variables for production
   - OAuth domain verification
   - CORS configuration review
   - Security headers validation

2. Documentation:
   - OAuth flow documentation
   - Troubleshooting guide
   - API integration guide
   - User experience guidelines

3. Monitoring setup:
   - OAuth success/failure metrics
   - Performance monitoring
   - Error alerting
   - User adoption tracking

DELIVERABLES:
1. Production deployment checklist
2. OAuth implementation documentation
3. Monitoring dashboard configuration
4. Security review report

ACCEPTANCE CRITERIA:
- Production environment is properly configured
- Documentation is complete and accurate
- Monitoring and alerting are operational
- Security review passes all requirements
- Deployment pipeline includes OAuth testing
```

---

## Implementation Timeline

**Total Estimated Duration**: 16-20 hours across 12 steps

### Phase 1 (Foundation): ~1.5 hours

- Step 1: Dependencies and Environment (30 mins)
- Step 2: OAuth Types and Interfaces (45 mins)

### Phase 2 (Core OAuth): ~5 hours

- Step 3: Google OAuth Implementation (2 hours)
- Step 4: Apple OAuth Implementation (2 hours)
- Step 5: OAuth Integration Layer (1 hour)

### Phase 3 (UI Integration): ~2.5 hours

- Step 6: Update LoginForm Buttons (1.5 hours)
- Step 7: OAuth Success Flow (1 hour)

### Phase 4 (Backend): ~4 hours

- Step 8: OAuth API Integration (3 hours)
- Step 9: Auth Store Integration (45 mins)

### Phase 5 (Testing & Polish): ~4 hours

- Step 10: Comprehensive Testing (2.5 hours)
- Step 11: Accessibility & Performance (1.5 hours)
- Step 12: Production Preparation (1 hour)

## Risk Assessment

**High Risk Steps**:

- Step 8 (OAuth API Integration) - Backend integration complexity

**Medium Risk Steps**:

- Step 3 (Google OAuth) - External SDK dependency
- Step 4 (Apple OAuth) - External SDK dependency
- Step 7 (OAuth Success Flow) - Integration with existing auth flow

**Low Risk Steps**:

- Steps 1, 2, 5, 6, 9, 11, 12 - Mostly frontend code with clear requirements

## Success Criteria

1. **Functional**: Users can sign in with Google and Apple OAuth
2. **Performance**: OAuth flows complete within 3 seconds
3. **Accessibility**: WCAG 2.1 AA compliance maintained
4. **Security**: Proper token validation and CSRF protection
5. **Testing**: >90% test coverage for OAuth functionality
6. **Integration**: Seamless integration with existing auth system
7. **Production Ready**: Deployed successfully without regressions

## Rollback Plan

Each step builds incrementally, allowing for easy rollback:

- Steps 1-7: Frontend-only changes, easily revertible
- Step 8: Backend integration can be feature-flagged
- Steps 9-12: Enhancement steps that don't affect core functionality

If issues arise, OAuth buttons can be disabled while maintaining existing email login functionality.
