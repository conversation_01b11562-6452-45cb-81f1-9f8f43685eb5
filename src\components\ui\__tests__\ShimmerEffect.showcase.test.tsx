import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { ShimmerEffect, ShimmerOverlay } from '../ShimmerEffect'

describe('ShimmerEffect Showcase', () => {
  it('should render basic shimmer effect', () => {
    const { container } = render(
      <div className="p-8 bg-gray-100">
        <h3 className="text-lg font-semibold mb-4">Basic Shimmer Effect</h3>
        <div className="w-64 h-16 bg-gray-300 rounded">
          <ShimmerEffect />
        </div>
      </div>
    )

    expect(container.querySelector('.shimmer-wrapper')).toBeInTheDocument()
  })

  it('should render multiple shimmer effects with staggered delays', () => {
    const { container } = render(
      <div className="p-8 bg-gray-100 space-y-4">
        <h3 className="text-lg font-semibold mb-4">
          Staggered Shimmer Effects
        </h3>
        {[0, 100, 200].map((delay) => (
          <div key={`delay-${delay}`} className="w-48 h-12 bg-gray-300 rounded">
            <ShimmerEffect delay={delay} />
          </div>
        ))}
      </div>
    )

    const shimmers = container.querySelectorAll('.shimmer-wrapper')
    expect(shimmers).toHaveLength(3)
  })

  it('should render shimmer overlay on text', () => {
    const { container } = render(
      <div className="p-8">
        <h3 className="text-lg font-semibold mb-4">Shimmer Overlay on Text</h3>
        <div className="relative inline-block">
          <span className="text-4xl font-bold text-gray-800">12,345</span>
          <ShimmerOverlay show />
        </div>
      </div>
    )

    expect(
      container.querySelector('[data-testid="shimmer-overlay"]')
    ).toBeInTheDocument()
  })

  it('should render shimmer with different sizes', () => {
    const sizes = [
      { width: '100px', height: '20px', label: 'Small' },
      { width: '200px', height: '40px', label: 'Medium' },
      { width: '300px', height: '60px', label: 'Large' },
    ]

    const { container } = render(
      <div className="p-8 bg-gray-100 space-y-4">
        <h3 className="text-lg font-semibold mb-4">Different Sizes</h3>
        {sizes.map((size) => (
          <div key={size.label}>
            <p className="text-sm text-gray-600 mb-1">{size.label}</p>
            <div
              className="bg-gray-300 rounded"
              style={{ width: size.width, height: size.height }}
            >
              <ShimmerEffect />
            </div>
          </div>
        ))}
      </div>
    )

    const shimmers = container.querySelectorAll('.shimmer-wrapper')
    expect(shimmers).toHaveLength(3)
  })

  it('should render shimmer on dark background', () => {
    const { container } = render(
      <div className="p-8 bg-gray-900">
        <h3 className="text-lg font-semibold mb-4 text-white">
          Dark Mode Shimmer
        </h3>
        <div className="w-64 h-16 bg-gray-700 rounded">
          <ShimmerEffect />
        </div>
      </div>
    )

    expect(container.querySelector('.shimmer-wrapper')).toBeInTheDocument()
  })

  it('should render shimmer for stat cards', () => {
    const stats = [
      { label: 'Week Streak', value: '0' },
      { label: 'Workouts', value: '0' },
      { label: 'Volume', value: '0' },
    ]

    const { container } = render(
      <div className="p-8">
        <h3 className="text-lg font-semibold mb-4">Stat Cards with Shimmer</h3>
        <div className="grid grid-cols-3 gap-4">
          {stats.map((stat, index) => (
            <div key={stat.label} className="bg-white rounded-lg p-4 shadow-sm">
              <div className="relative">
                <div className="text-3xl font-bold text-gray-900 text-center">
                  {stat.value}
                </div>
                <ShimmerOverlay show delay={index * 100} />
              </div>
              <p className="text-sm text-gray-600 text-center mt-2">
                {stat.label}
              </p>
            </div>
          ))}
        </div>
      </div>
    )

    const overlays = container.querySelectorAll(
      '[data-testid="shimmer-overlay"]'
    )
    expect(overlays).toHaveLength(3)
  })
})
