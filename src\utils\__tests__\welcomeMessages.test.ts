import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  getWelcomeMessage,
  getMotivationalMessage,
  getUserStats,
} from '../welcomeMessages'

describe('welcomeMessages', () => {
  beforeEach(() => {
    // Mock Math.random for predictable tests
    vi.spyOn(Math, 'random').mockReturnValue(0.5)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getWelcomeMessage', () => {
    it('should return personalized greeting with name', () => {
      const message = getWelcomeMessage('John')
      expect(message).toContain('<PERSON>')
    })

    it('should return generic greeting without name', () => {
      const message = getWelcomeMessage()
      expect(message).not.toContain('undefined')
      expect(message).not.toContain('null')
    })

    it('should return morning greeting for morning hours', () => {
      const mockDate = new Date('2024-01-01 09:00:00')
      vi.spyOn(global, 'Date').mockImplementation(() => mockDate)

      const message = getWelcomeMessage('Sarah')
      expect(message).toMatch(/good morning|morning/i)
    })

    it('should return afternoon greeting for afternoon hours', () => {
      const mockDate = new Date('2024-01-01 14:00:00')
      vi.spyOn(global, 'Date').mockImplementation(() => mockDate)

      const message = getWelcomeMessage('Mike')
      expect(message).toMatch(/good afternoon|afternoon/i)
    })

    it('should return evening greeting for evening hours', () => {
      const mockDate = new Date('2024-01-01 20:00:00')
      vi.spyOn(global, 'Date').mockImplementation(() => mockDate)

      const message = getWelcomeMessage('Lisa')
      expect(message).toMatch(/good evening|evening/i)
    })

    it('should handle edge case hours correctly', () => {
      // Late night (2 AM)
      const lateNight = new Date('2024-01-01 02:00:00')
      vi.spyOn(global, 'Date').mockImplementation(() => lateNight)

      const message1 = getWelcomeMessage()
      expect(message1).toBeTruthy()

      // Early morning (5 AM)
      const earlyMorning = new Date('2024-01-01 05:00:00')
      vi.spyOn(global, 'Date').mockImplementation(() => earlyMorning)

      const message2 = getWelcomeMessage()
      expect(message2).toBeTruthy()
    })

    it('should vary messages with different random values', () => {
      const messages = new Set()

      for (let i = 0; i < 10; i++) {
        vi.spyOn(Math, 'random').mockReturnValue(i / 10)
        messages.add(getWelcomeMessage('Test'))
      }

      // Should have at least 2 different messages
      expect(messages.size).toBeGreaterThan(1)
    })
  })

  describe('getMotivationalMessage', () => {
    it('should return message based on streak', () => {
      const messageNoStreak = getMotivationalMessage({ streak: 0 })
      const messageSmallStreak = getMotivationalMessage({ streak: 3 })
      const messageBigStreak = getMotivationalMessage({ streak: 30 })

      expect(messageNoStreak).toBeTruthy()
      expect(messageSmallStreak).toBeTruthy()
      expect(messageBigStreak).toBeTruthy()
    })

    it('should include streak number in message for streaks > 0', () => {
      const message = getMotivationalMessage({ streak: 7 })
      expect(message).toContain('7')
    })

    it('should handle first workout specially', () => {
      const message = getMotivationalMessage({ totalWorkouts: 1 })
      expect(message).toMatch(/first|begin|start/i)
    })

    it('should recognize milestone workouts', () => {
      const message50 = getMotivationalMessage({ totalWorkouts: 50 })
      const message100 = getMotivationalMessage({ totalWorkouts: 100 })
      const message500 = getMotivationalMessage({ totalWorkouts: 500 })

      expect(message50).toMatch(/50|fifty|milestone/i)
      expect(message100).toMatch(/100|hundred|incredible/i)
      expect(message500).toMatch(/500|legend|amazing/i)
    })

    it('should handle recent workout (today)', () => {
      const today = new Date()
      const message = getMotivationalMessage({ lastWorkoutDate: today })
      expect(message).toMatch(/back|another|again/i)
    })

    it('should handle workout after break', () => {
      const lastWeek = new Date()
      lastWeek.setDate(lastWeek.getDate() - 8) // More than 7 days ago

      const message = getMotivationalMessage({ lastWorkoutDate: lastWeek })
      expect(message).toMatch(/back|return|ready/i)
    })

    it('should provide default message with no stats', () => {
      const message = getMotivationalMessage({})
      expect(message).toBeTruthy()
      expect(message.length).toBeGreaterThan(0)
    })

    it('should vary messages for same stats', () => {
      const messages = new Set()
      const stats = { streak: 5, totalWorkouts: 20 }

      for (let i = 0; i < 10; i++) {
        vi.spyOn(Math, 'random').mockReturnValue(i / 10)
        messages.add(getMotivationalMessage(stats))
      }

      // Should have some variety
      expect(messages.size).toBeGreaterThan(1)
    })
  })

  describe('getUserStats', () => {
    it('should format user stats correctly', () => {
      const stats = getUserStats({
        streak: 7,
        totalWorkouts: 50,
        currentLevel: 'Intermediate',
        lastWorkoutDate: new Date('2024-01-01'),
      })

      expect(stats).toHaveProperty('streakText')
      expect(stats).toHaveProperty('workoutCountText')
      expect(stats).toHaveProperty('levelText')
      expect(stats).toHaveProperty('timeSinceLastWorkout')
    })

    it('should handle singular vs plural correctly', () => {
      const stats1 = getUserStats({ streak: 1 })
      expect(stats1.streakText).toBe('1 day streak')

      const stats2 = getUserStats({ streak: 5 })
      expect(stats2.streakText).toBe('5 day streak')
    })

    it('should format workout count with commas', () => {
      const stats = getUserStats({ totalWorkouts: 1234 })
      expect(stats.workoutCountText).toBe('1,234 workouts')
    })

    it('should calculate time since last workout', () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)

      const stats = getUserStats({ lastWorkoutDate: yesterday })
      expect(stats.timeSinceLastWorkout).toMatch(/1 day|yesterday/i)
    })

    it('should handle missing data gracefully', () => {
      const stats = getUserStats({})

      expect(stats.streakText).toBe('New journey')
      expect(stats.workoutCountText).toBe('First workout')
      expect(stats.levelText).toBe('Ready to start')
      expect(stats.timeSinceLastWorkout).toBe('Welcome!')
    })

    it('should format level text properly', () => {
      const stats = getUserStats({ currentLevel: 'Advanced' })
      expect(stats.levelText).toBe('Advanced athlete')
    })
  })
})
