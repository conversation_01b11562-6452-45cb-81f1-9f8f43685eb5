import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { WorkoutOverview } from '../WorkoutOverview'
import * as workoutHook from '@/hooks/useWorkout'
import type { UseWorkoutReturn } from '@/components/workout/__tests__/WorkoutOverview.types'
import {
  createMockWorkoutTemplateGroup,
  createMockExerciseWorkSetsModel,
} from '@/components/workout/__tests__/WorkoutOverview.test.helpers'

// Mock the hook
vi.mock('@/hooks/useWorkout')

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    refresh: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
  }),
  usePathname: () => '/workout',
}))

describe('WorkoutOverview - Finish Button', () => {
  it('should show "Finish and save workout" when workout has saved sets', () => {
    const mockExercises = [
      createMockExerciseWorkSetsModel({ Id: 1, Label: 'Squat' }),
      createMockExerciseWorkSetsModel({ Id: 2, Label: 'Bench Press' }),
    ]
    const mockWorkout = [
      createMockWorkoutTemplateGroup({
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout A',
            Exercises: [
              { Id: 1, Label: 'Squat', IsBodyweight: false },
              { Id: 2, Label: 'Bench Press', IsBodyweight: false },
            ],
          },
        ],
      }),
    ]

    // Mock useWorkout with active session and saved sets
    vi.mocked(workoutHook.useWorkout).mockReturnValue({
      todaysWorkout: mockWorkout,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: { ProgramName: 'Test Program' },
      exercises: mockExercises,
      exerciseWorkSetsModels: mockExercises,
      expectedExerciseCount: mockExercises.length,
      hasInitialData: true,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: {
        id: 'test-session',
        startTime: new Date(),
        exercises: [
          {
            exerciseId: 1,
            name: 'Squat',
            sets: [
              {
                setNumber: 1,
                reps: 10,
                weight: { Lb: 135, Kg: 61.2 },
                rir: 2,
                isWarmup: false,
                timestamp: new Date(),
              },
            ],
          },
        ],
      },
      finishWorkout: vi.fn(),
      isLoading: false,
      loadExerciseRecommendation: vi.fn(),
    } as unknown as UseWorkoutReturn)

    render(<WorkoutOverview />)

    // Check that the button shows "Finish and save workout"
    const button = screen.getByRole('button', {
      name: /Finish and save workout/i,
    })
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Finish and save workout')
  })

  it('should show "Continue Workout" when workout has no saved sets', () => {
    const mockExercises = [
      createMockExerciseWorkSetsModel({ Id: 1, Label: 'Squat' }),
      createMockExerciseWorkSetsModel({ Id: 2, Label: 'Bench Press' }),
    ]
    const mockWorkout = [
      createMockWorkoutTemplateGroup({
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout A',
            Exercises: [
              { Id: 1, Label: 'Squat', IsBodyweight: false },
              { Id: 2, Label: 'Bench Press', IsBodyweight: false },
            ],
          },
        ],
      }),
    ]

    // Mock useWorkout with active session but no saved sets
    vi.mocked(workoutHook.useWorkout).mockReturnValue({
      todaysWorkout: mockWorkout,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: null,
      exercises: mockExercises,
      exerciseWorkSetsModels: mockExercises,
      expectedExerciseCount: mockExercises.length,
      hasInitialData: true,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: {
        id: 'test-session',
        startTime: new Date(),
        exercises: [], // No saved sets
      },
      finishWorkout: vi.fn(),
      isLoading: false,
      loadExerciseRecommendation: vi.fn(),
    } as unknown as UseWorkoutReturn)

    render(<WorkoutOverview />)

    // Check that the button shows "Continue Workout"
    const button = screen.getByRole('button', {
      name: /Continue your current workout/i,
    })
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Continue Workout')
  })

  it('should show "Start Workout" when no workout session is active', () => {
    const mockExercises = [
      createMockExerciseWorkSetsModel({ Id: 1, Label: 'Squat' }),
      createMockExerciseWorkSetsModel({ Id: 2, Label: 'Bench Press' }),
    ]
    const mockWorkout = [
      createMockWorkoutTemplateGroup({
        WorkoutTemplates: [
          {
            Id: 1,
            Label: 'Workout A',
            Exercises: [
              { Id: 1, Label: 'Squat', IsBodyweight: false },
              { Id: 2, Label: 'Bench Press', IsBodyweight: false },
            ],
          },
        ],
      }),
    ]

    // Mock useWorkout with no active session
    vi.mocked(workoutHook.useWorkout).mockReturnValue({
      todaysWorkout: mockWorkout,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: null,
      exercises: mockExercises,
      exerciseWorkSetsModels: mockExercises,
      expectedExerciseCount: mockExercises.length,
      hasInitialData: true,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null, // No active session
      finishWorkout: vi.fn(),
      isLoading: false,
      loadExerciseRecommendation: vi.fn(),
    } as unknown as UseWorkoutReturn)

    render(<WorkoutOverview />)

    // Check that the button shows "Start Workout"
    const button = screen.getByRole('button', {
      name: /Start a new workout session/i,
    })
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Start Workout')
  })
})
