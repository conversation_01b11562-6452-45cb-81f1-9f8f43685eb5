'use client'

import { useEffect } from 'react'
import { monitorFontLoading } from '@/utils/fontLoader'

/**
 * Font Loading Monitor Component
 * Monitors font loading performance and prevents FOUT/FOIT
 */
export function FontLoadingMonitor() {
  useEffect(() => {
    // Only monitor in production for performance metrics
    if (process.env.NODE_ENV === 'production') {
      monitorFontLoading().catch((error) => {
        // Only log font loading errors in development
        if (process.env.NODE_ENV === 'development') {
          console.error('Font loading error:', error)
        }
      })
    }

    // Add font-loading class to document
    document.documentElement.classList.add('fonts-loading')

    // Wait for fonts to be ready
    if (document.fonts) {
      document.fonts.ready
        .then(() => {
          document.documentElement.classList.remove('fonts-loading')
          document.documentElement.classList.add('fonts-loaded')
        })
        .catch(() => {
          // Even on error, remove loading class to show content
          document.documentElement.classList.remove('fonts-loading')
          document.documentElement.classList.add('fonts-failed')
        })
    } else {
      // No fonts API support, remove loading class immediately
      document.documentElement.classList.remove('fonts-loading')
    }
  }, [])

  return null
}
