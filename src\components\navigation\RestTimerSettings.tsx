'use client'

import React, { useState, useRef, useEffect } from 'react'
import { KebabMenuIcon } from '@/components/icons/KebabMenuIcon'

export function RestTimerSettings() {
  // Load preferences from localStorage
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [vibrationEnabled, setVibrationEnabled] = useState(true)
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const savedSoundPref = localStorage.getItem('soundEnabled')
    if (savedSoundPref !== null) {
      setSoundEnabled(savedSoundPref === 'true')
    }

    const savedVibrationPref = localStorage.getItem('vibrationEnabled')
    if (savedVibrationPref !== null) {
      setVibrationEnabled(savedVibrationPref === 'true')
    }
  }, [])

  const onSoundToggle = () => {
    const newValue = !soundEnabled
    setSoundEnabled(newValue)
    localStorage.setItem('soundEnabled', newValue.toString())
    // Dispatch storage event for same-tab sync
    window.dispatchEvent(new Event('storage'))
  }

  const onVibrationToggle = () => {
    const newValue = !vibrationEnabled
    setVibrationEnabled(newValue)
    localStorage.setItem('vibrationEnabled', newValue.toString())
    // Dispatch storage event for same-tab sync
    window.dispatchEvent(new Event('storage'))
  }

  // Close menu on outside click
  useEffect(() => {
    if (!isOpen) return

    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isOpen])

  // Close menu on escape key
  useEffect(() => {
    if (!isOpen) return

    function handleEscape(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen])

  return (
    <>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 -mr-2 rounded-lg hover:bg-gray-100 transition-colors"
        aria-label="Timer settings"
      >
        <KebabMenuIcon size={20} className="text-gray-600" />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
            aria-hidden="true"
          />

          {/* Menu */}
          <div
            ref={menuRef}
            className="fixed top-14 right-4 z-50 w-64 bg-white dark:bg-gray-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-800 overflow-hidden animate-in slide-in-from-top-2 duration-200"
            role="dialog"
            aria-label="Timer settings menu"
          >
            <div className="py-2">
              {/* Sound Toggle */}
              <button
                onClick={onSoundToggle}
                className="w-full flex items-center justify-between px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"
                    />
                  </svg>
                  Sound
                </div>
                <div
                  className={`w-12 h-6 rounded-full p-1 transition-colors ${
                    soundEnabled ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                >
                  <div
                    className={`w-4 h-4 bg-white rounded-full transition-transform ${
                      soundEnabled ? 'translate-x-6' : 'translate-x-0'
                    }`}
                  />
                </div>
              </button>

              {/* Vibration Toggle */}
              <button
                onClick={onVibrationToggle}
                className="w-full flex items-center justify-between px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Vibration
                </div>
                <div
                  className={`w-12 h-6 rounded-full p-1 transition-colors ${
                    vibrationEnabled ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                >
                  <div
                    className={`w-4 h-4 bg-white rounded-full transition-transform ${
                      vibrationEnabled ? 'translate-x-6' : 'translate-x-0'
                    }`}
                  />
                </div>
              </button>
            </div>
          </div>
        </>
      )}
    </>
  )
}
