import { useEffect, useRef } from 'react'
import { trackComponentPerformance } from '@/utils/programPerformance'

/**
 * Hook to track component performance metrics
 * Measures mount time and render duration
 */
export function useComponentPerformance(componentName: string) {
  const tracker = useRef(trackComponentPerformance(componentName))
  const isFirstRender = useRef(true)

  useEffect(() => {
    // Track mount on first render
    if (isFirstRender.current) {
      tracker.current.markMount()
      isFirstRender.current = false
    }
  }, [])

  // Track render start
  useEffect(() => {
    tracker.current.markRenderStart()
  })

  // Track render end
  useEffect(() => {
    const duration = tracker.current.markRenderEnd()

    if (process.env.NODE_ENV === 'development' && duration > 50) {
      console.warn(`${componentName} render took ${duration}ms`)
    }
  })

  return {
    trackFetchStart: () => tracker.current.markFetchStart(),
    trackFetchEnd: () => tracker.current.markFetchEnd(),
  }
}
