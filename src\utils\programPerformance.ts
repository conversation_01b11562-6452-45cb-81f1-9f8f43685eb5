/**
 * Performance monitoring utilities for Program Overview Page
 * Tracks page load time, component renders, and memory usage
 */

import {
  ProgramPerformanceMarks,
  type PerformanceMetrics,
} from './programPerformance/constants'
import { getPerformanceMetrics } from './programPerformance/metrics'
import { sendMetricsToAnalytics } from './programPerformance/analytics'

export { ProgramPerformanceMarks } from './programPerformance/constants'
export type { PerformanceMetrics } from './programPerformance/constants'

/**
 * Measure program page load time
 */
export const measureProgramPageLoad = {
  start: () => {
    if (typeof performance === 'undefined') return
    performance.mark(ProgramPerformanceMarks.PROGRAM_PAGE_START)
  },

  end: (): number => {
    if (typeof performance === 'undefined') return 0

    try {
      performance.mark(ProgramPerformanceMarks.PROGRAM_PAGE_END)
      performance.measure(
        'program-page-load',
        ProgramPerformanceMarks.PROGRAM_PAGE_START,
        ProgramPerformanceMarks.PROGRAM_PAGE_END
      )

      const measure = performance.getEntriesByName('program-page-load')[0]
      return measure?.duration || 0
    } catch {
      // Handle missing start mark
      return 0
    }
  },
}

/**
 * Track individual component performance
 */
export function trackComponentPerformance(componentName: string) {
  const prefix = `program-component-${componentName}`
  let fetchStartTime = 0

  return {
    markMount: () => {
      if (typeof performance === 'undefined') return
      performance.mark(`${prefix}-mount`)
    },

    markRenderStart: () => {
      if (typeof performance === 'undefined') return
      performance.mark(`${prefix}-render-start`)
    },

    markRenderEnd: (): number => {
      if (typeof performance === 'undefined') return 0

      try {
        performance.mark(`${prefix}-render-end`)
        performance.measure(
          `${prefix}-render`,
          `${prefix}-render-start`,
          `${prefix}-render-end`
        )

        const measure = performance.getEntriesByName(`${prefix}-render`)[0]
        return measure?.duration || 0
      } catch {
        return 0
      }
    },

    markFetchStart: () => {
      if (typeof performance === 'undefined') return
      fetchStartTime = performance.now()
    },

    markFetchEnd: (): number => {
      if (typeof performance === 'undefined') return 0
      if (fetchStartTime === 0) return 0

      const duration = performance.now() - fetchStartTime
      fetchStartTime = 0
      return duration
    },
  }
}

/**
 * Get structured performance metrics
 */
export { getPerformanceMetrics }

/**
 * Report performance metrics
 */
export function reportProgramMetrics(): void {
  const metrics = getPerformanceMetrics()
  sendMetricsToAnalytics(metrics)
}

/**
 * Clear all performance marks and measures
 */
export function clearPerformanceMarks(): void {
  if (typeof performance === 'undefined') return

  performance.clearMarks()
  performance.clearMeasures()
}

/**
 * Use Performance Observer for more detailed monitoring
 */
export function observeProgramPerformance(
  callback?: (metrics: PerformanceMetrics) => void
): () => void {
  if (typeof PerformanceObserver === 'undefined') {
    return () => {} // No-op cleanup
  }

  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries()

    entries.forEach((entry) => {
      if (entry.entryType === 'measure' && entry.name.includes('program-')) {
        // Performance tracked via observer
      }
    })

    // Call callback with current metrics
    if (callback) {
      callback(getPerformanceMetrics())
    }
  })

  try {
    observer.observe({ entryTypes: ['measure', 'mark'] })
  } catch (error) {
    // Performance Observer not supported
  }

  // Return cleanup function
  return () => observer.disconnect()
}
