import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  userInfoPerformance,
  trackLoginSuccess,
  trackUserInfoFetch,
  trackUserInfoFirstByte,
  trackUserInfoComplete,
  trackStatsFetch,
  trackStatsComplete,
  trackMetricLoaded,
  trackCacheOperation,
  trackPageReady,
  getUserInfoMetrics,
  reportUserInfoPerformance,
  UserInfoPerformanceMarks,
} from '../userInfoPerformance'

describe('UserInfo Performance Tracking', () => {
  beforeEach(() => {
    // Reset performance tracking
    userInfoPerformance.reset()

    // Mock performance API
    vi.spyOn(performance, 'mark').mockImplementation(() => {})
    vi.spyOn(performance, 'measure').mockImplementation(() => {})
    vi.spyOn(performance, 'getEntriesByName').mockReturnValue([
      { duration: 100 } as PerformanceEntry,
    ])

    // Mock sessionStorage
    const sessionStorageMock = {
      setItem: vi.fn(),
      getItem: vi.fn(),
    }
    Object.defineProperty(window, 'sessionStorage', {
      value: sessionStorageMock,
      writable: true,
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Tracking', () => {
    it('should track login success', () => {
      trackLoginSuccess()

      expect(performance.mark).toHaveBeenCalledWith(
        UserInfoPerformanceMarks.LOGIN_SUCCESS
      )
    })

    it('should track UserInfo fetch start', () => {
      trackUserInfoFetch()

      expect(performance.mark).toHaveBeenCalledWith(
        UserInfoPerformanceMarks.USERINFO_FETCH_START
      )
    })

    it('should track UserInfo first byte', () => {
      trackUserInfoFirstByte()

      expect(performance.mark).toHaveBeenCalledWith(
        UserInfoPerformanceMarks.USERINFO_FIRST_BYTE
      )
      expect(performance.measure).toHaveBeenCalledWith(
        'login-to-first-byte',
        UserInfoPerformanceMarks.LOGIN_SUCCESS,
        UserInfoPerformanceMarks.USERINFO_FIRST_BYTE
      )
    })

    it('should track UserInfo complete', () => {
      trackUserInfoComplete()

      expect(performance.mark).toHaveBeenCalledWith(
        UserInfoPerformanceMarks.USERINFO_FETCH_END
      )
      expect(performance.measure).toHaveBeenCalledWith(
        'userinfo-load-time',
        UserInfoPerformanceMarks.USERINFO_FETCH_START,
        UserInfoPerformanceMarks.USERINFO_FETCH_END
      )
    })
  })

  describe('Stats Tracking', () => {
    it('should track stats fetch start', () => {
      trackStatsFetch()

      expect(performance.mark).toHaveBeenCalledWith(
        UserInfoPerformanceMarks.STATS_FETCH_START
      )
    })

    it('should track stats complete', () => {
      trackStatsComplete()

      expect(performance.mark).toHaveBeenCalledWith(
        UserInfoPerformanceMarks.STATS_FETCH_END
      )
      expect(performance.measure).toHaveBeenCalledWith(
        'stats-load-time',
        UserInfoPerformanceMarks.STATS_FETCH_START,
        UserInfoPerformanceMarks.STATS_FETCH_END
      )
    })
  })

  describe('Metric Loading', () => {
    it('should track individual metric loading', () => {
      trackMetricLoaded('streak')

      expect(performance.mark).toHaveBeenCalledWith(
        UserInfoPerformanceMarks.METRIC_STREAK_LOADED
      )
      expect(performance.measure).toHaveBeenCalledWith(
        'metric-streak-load',
        UserInfoPerformanceMarks.STATS_FETCH_START,
        UserInfoPerformanceMarks.METRIC_STREAK_LOADED
      )
    })

    it('should mark all metrics loaded when all are complete', () => {
      trackMetricLoaded('streak')
      trackMetricLoaded('workouts')
      trackMetricLoaded('volume')

      expect(performance.mark).toHaveBeenCalledWith(
        UserInfoPerformanceMarks.ALL_METRICS_LOADED
      )
    })
  })

  describe('Cache Operations', () => {
    it('should track cache hit', () => {
      trackCacheOperation('hit', 5)

      expect(performance.mark).toHaveBeenCalledWith(
        UserInfoPerformanceMarks.USERINFO_CACHE_HIT
      )

      const metrics = getUserInfoMetrics()
      expect(metrics.cacheMetrics.hits).toBe(1)
      expect(metrics.cacheMetrics.readLatency).toBe(5)
    })

    it('should track cache miss', () => {
      trackCacheOperation('miss')

      expect(performance.mark).toHaveBeenCalledWith(
        UserInfoPerformanceMarks.USERINFO_CACHE_MISS
      )

      const metrics = getUserInfoMetrics()
      expect(metrics.cacheMetrics.misses).toBe(1)
    })

    it('should calculate cache hit rate', () => {
      trackCacheOperation('hit')
      trackCacheOperation('hit')
      trackCacheOperation('miss')

      const metrics = getUserInfoMetrics()
      expect(metrics.cacheMetrics.hitRate).toBeCloseTo(66.67, 1)
    })
  })

  describe('Retry Tracking', () => {
    it('should track UserInfo retries', () => {
      trackUserInfoFetch(true)
      trackUserInfoFetch(true)

      const metrics = getUserInfoMetrics()
      expect(metrics.retryMetrics.userInfoRetries).toBe(2)
      expect(metrics.retryMetrics.totalRetries).toBe(2)
    })

    it('should track stats retries', () => {
      trackStatsFetch(true)

      const metrics = getUserInfoMetrics()
      expect(metrics.retryMetrics.statsRetries).toBe(1)
      expect(metrics.retryMetrics.totalRetries).toBe(1)
    })

    it('should track combined retries', () => {
      trackUserInfoFetch(true)
      trackStatsFetch(true)
      trackStatsFetch(true)

      const metrics = getUserInfoMetrics()
      expect(metrics.retryMetrics.userInfoRetries).toBe(1)
      expect(metrics.retryMetrics.statsRetries).toBe(2)
      expect(metrics.retryMetrics.totalRetries).toBe(3)
    })
  })

  describe('Page Ready', () => {
    it('should track overall page ready time', () => {
      trackPageReady()

      expect(performance.mark).toHaveBeenCalledWith(
        UserInfoPerformanceMarks.PROGRAM_PAGE_READY
      )
      expect(performance.measure).toHaveBeenCalledWith(
        'overall-page-ready',
        UserInfoPerformanceMarks.LOGIN_SUCCESS,
        UserInfoPerformanceMarks.PROGRAM_PAGE_READY
      )
    })
  })

  describe('Metrics Collection', () => {
    it('should collect all timing metrics', () => {
      // Simulate full flow
      trackLoginSuccess()
      trackUserInfoFetch()
      trackUserInfoFirstByte()
      trackUserInfoComplete()
      trackStatsFetch()
      trackStatsComplete()
      trackMetricLoaded('streak')
      trackMetricLoaded('workouts')
      trackMetricLoaded('volume')
      trackPageReady()

      const metrics = getUserInfoMetrics()

      expect(metrics.loginToFirstByte).toBe(100)
      expect(metrics.userInfoLoadTime).toBe(100)
      expect(metrics.statsLoadTime).toBe(100)
      expect(metrics.overallPageReady).toBe(100)
      expect(metrics.metricLoadTimes.streak).toBe(100)
      expect(metrics.metricLoadTimes.workouts).toBe(100)
      expect(metrics.metricLoadTimes.volume).toBe(100)
    })

    it('should handle missing performance marks gracefully', () => {
      vi.spyOn(performance, 'measure').mockImplementation(() => {
        throw new Error('Mark not found')
      })

      trackUserInfoFirstByte()

      const metrics = getUserInfoMetrics()
      expect(metrics.loginToFirstByte).toBeNull()
    })
  })

  describe('Reporting', () => {
    it('should report to console in development', () => {
      const originalEnv = process.env.NODE_ENV
      vi.stubEnv('NODE_ENV', 'development')

      const consoleSpy = vi.spyOn(console, 'group').mockImplementation(() => {})
      const logSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      const groupEndSpy = vi
        .spyOn(console, 'groupEnd')
        .mockImplementation(() => {})

      trackCacheOperation('hit')
      trackCacheOperation('miss')
      reportUserInfoPerformance()

      expect(consoleSpy).toHaveBeenCalledWith('[UserInfo Performance]')
      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('Cache Hit Rate:'),
        '50.0',
        '%'
      )
      expect(groupEndSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
      logSpy.mockRestore()
      groupEndSpy.mockRestore()
      vi.stubEnv('NODE_ENV', originalEnv)
    })

    it('should not report to console in production', () => {
      const originalEnv = process.env.NODE_ENV
      vi.stubEnv('NODE_ENV', 'production')

      const consoleSpy = vi.spyOn(console, 'group').mockImplementation(() => {})

      reportUserInfoPerformance()

      expect(consoleSpy).not.toHaveBeenCalled()

      consoleSpy.mockRestore()
      vi.stubEnv('NODE_ENV', originalEnv)
    })

    it('should send to analytics in production', () => {
      const originalEnv = process.env.NODE_ENV
      vi.stubEnv('NODE_ENV', 'production')

      const gtagMock = vi.fn()
      ;(window as { gtag?: typeof gtagMock }).gtag = gtagMock

      trackLoginSuccess()
      trackUserInfoFirstByte()
      trackPageReady()
      trackCacheOperation('hit')
      trackCacheOperation('hit')
      trackCacheOperation('miss')

      reportUserInfoPerformance()

      expect(gtagMock).toHaveBeenCalledWith('event', 'timing_complete', {
        name: 'userinfo_first_byte',
        value: 100,
        event_category: 'userinfo_performance',
      })

      expect(gtagMock).toHaveBeenCalledWith('event', 'cache_performance', {
        value: 67,
        event_category: 'userinfo_performance',
        event_label: 'cache_hit_rate',
      })

      delete (window as { gtag?: unknown }).gtag
      vi.stubEnv('NODE_ENV', originalEnv)
    })
  })

  describe('Session Management', () => {
    it('should generate unique session IDs', () => {
      const metrics1 = getUserInfoMetrics()
      userInfoPerformance.reset()
      const metrics2 = getUserInfoMetrics()

      expect(metrics1.analytics.sessionId).not.toBe(
        metrics2.analytics.sessionId
      )
    })

    it('should reset all metrics on reset', () => {
      trackCacheOperation('hit')
      trackUserInfoFetch(true)

      userInfoPerformance.reset()

      const metrics = getUserInfoMetrics()
      expect(metrics.cacheMetrics.hits).toBe(0)
      expect(metrics.retryMetrics.userInfoRetries).toBe(0)
    })
  })

  describe('Edge Cases', () => {
    it('should handle undefined performance API', () => {
      const originalPerformance = global.performance
      ;(global as { performance?: unknown }).performance = undefined

      expect(() => trackLoginSuccess()).not.toThrow()
      expect(() => trackUserInfoFirstByte()).not.toThrow()
      ;(global as { performance?: unknown }).performance = originalPerformance
    })

    it('should handle performance.measure errors', () => {
      vi.spyOn(performance, 'measure').mockImplementation(() => {
        throw new Error('Measurement failed')
      })

      expect(() => trackUserInfoFirstByte()).not.toThrow()

      const metrics = getUserInfoMetrics()
      expect(metrics.loginToFirstByte).toBeNull()
    })

    it('should handle sessionStorage errors', () => {
      const setItemMock = vi.fn().mockImplementation(() => {
        throw new Error('Storage full')
      })
      window.sessionStorage.setItem = setItemMock

      expect(() =>
        userInfoPerformance.mark('test-mark', { data: 'test' })
      ).not.toThrow()
    })
  })
})
