# Testing Guidelines for LLM Development

**CRITICAL: This document is the testing contract between the codebase and LLMs. All code changes MUST follow these guidelines before committing.**

## 🚨 MANDATORY Pre-Commit Testing Requirements

### The Golden Rule
**NO CODE COMMITS WITHOUT TESTS**. If you modify code, you MUST:

1. Run ALL test commands and ensure they pass
2. Write tests for new features BEFORE implementation (TDD)
3. Update existing tests when modifying functionality
4. Test in browser for ANY UI changes

### Required Test Commands (Run ALL Before Commit)
```bash
npm run typecheck  # TypeScript validation - ZERO errors allowed
npm run lint       # ESLint checks - ZERO warnings allowed
npm run test       # Unit/integration tests - 100% must pass
npm run build      # Production build - MUST succeed
```

### Browser Testing Triggers
You MUST test in browser if you:
- Modified ANY `.tsx` or `.jsx` file
- Changed auth/login/user flows
- Updated API calls or data fetching
- Modified UI components or styles
- Touched user-facing functionality

## 📱 Mobile-First Testing Requirements

### Viewport Testing
```typescript
// ALWAYS test with mobile viewport first
describe('Component Mobile Test', () => {
  beforeEach(() => {
    // Set mobile viewport
    cy.viewport(375, 667) // iPhone SE
    // or for Playwright:
    await page.setViewportSize({ width: 375, height: 667 })
  })

  it('should have 44px minimum touch targets', () => {
    const button = screen.getByRole('button')
    const { height, width } = button.getBoundingClientRect()
    expect(Math.min(height, width)).toBeGreaterThanOrEqual(44)
  })
})
```

### Touch Event Testing
```typescript
// Test touch interactions, not just clicks
it('should handle touch events', async () => {
  const button = screen.getByRole('button')
  
  // Simulate touch
  fireEvent.touchStart(button)
  fireEvent.touchEnd(button)
  
  // For swipe gestures
  fireEvent.touchStart(element, { touches: [{ clientX: 100, clientY: 100 }] })
  fireEvent.touchMove(element, { touches: [{ clientX: 50, clientY: 100 }] })
  fireEvent.touchEnd(element)
})
```

## 🧪 Test Writing Patterns

### 1. Component Testing Pattern
```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

describe('ComponentName', () => {
  // Setup
  const user = userEvent.setup()
  const mockProps = { /* default props */ }
  
  beforeEach(() => {
    vi.clearAllMocks()
  })

  // Rendering tests
  it('should render with required elements', () => {
    render(<Component {...mockProps} />)
    expect(screen.getByRole('button')).toBeInTheDocument()
  })

  // Interaction tests
  it('should handle user interactions', async () => {
    const onAction = vi.fn()
    render(<Component {...mockProps} onAction={onAction} />)
    
    await user.click(screen.getByRole('button'))
    expect(onAction).toHaveBeenCalledOnce()
  })

  // Mobile-specific tests
  it('should work on mobile viewport', () => {
    render(<Component {...mockProps} />)
    const touchTarget = screen.getByRole('button')
    const rect = touchTarget.getBoundingClientRect()
    expect(rect.height).toBeGreaterThanOrEqual(44)
  })

  // Loading states
  it('should show loading state', () => {
    render(<Component {...mockProps} isLoading />)
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  // Error states
  it('should show error state', () => {
    render(<Component {...mockProps} error="Something went wrong" />)
    expect(screen.getByText('Something went wrong')).toBeInTheDocument()
  })
})
```

### 2. Hook Testing Pattern
```typescript
import { renderHook, act } from '@testing-library/react'
import { describe, it, expect } from 'vitest'

describe('useCustomHook', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useCustomHook())
    expect(result.current.value).toBe(initialValue)
  })

  it('should update state correctly', async () => {
    const { result } = renderHook(() => useCustomHook())
    
    await act(async () => {
      result.current.updateValue('new value')
    })
    
    expect(result.current.value).toBe('new value')
  })

  it('should handle errors gracefully', async () => {
    const { result } = renderHook(() => useCustomHook())
    
    await act(async () => {
      result.current.triggerError()
    })
    
    expect(result.current.error).toBeTruthy()
    expect(result.current.isLoading).toBe(false)
  })
})
```

### 3. API Testing Pattern
```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest'
import axios from 'axios'
import MockAdapter from 'axios-mock-adapter'

describe('API Service', () => {
  let mock: MockAdapter

  beforeEach(() => {
    mock = new MockAdapter(axios)
  })

  afterEach(() => {
    mock.restore()
  })

  it('should fetch data successfully', async () => {
    const mockData = { id: 1, name: 'Test' }
    mock.onGet('/api/endpoint').reply(200, mockData)

    const result = await apiService.getData()
    expect(result).toEqual(mockData)
  })

  it('should handle network errors', async () => {
    mock.onGet('/api/endpoint').networkError()

    await expect(apiService.getData()).rejects.toThrow('Network Error')
  })

  it('should retry on failure', async () => {
    mock.onGet('/api/endpoint')
      .replyOnce(500)
      .onGet('/api/endpoint')
      .reply(200, { success: true })

    const result = await apiService.getDataWithRetry()
    expect(result).toEqual({ success: true })
    expect(mock.history.get.length).toBe(2)
  })
})
```

### 4. Store Testing Pattern (Zustand)
```typescript
import { describe, it, expect, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useStore } from '@/stores/store'

describe('Store', () => {
  beforeEach(() => {
    // Reset store to initial state
    useStore.setState(initialState)
  })

  it('should update state correctly', () => {
    const { result } = renderHook(() => useStore())

    act(() => {
      result.current.updateValue('new value')
    })

    expect(result.current.value).toBe('new value')
  })

  it('should handle async actions', async () => {
    const { result } = renderHook(() => useStore())

    await act(async () => {
      await result.current.fetchData()
    })

    expect(result.current.isLoading).toBe(false)
    expect(result.current.data).toBeDefined()
  })
})
```

## 🔥 PWA Testing Requirements

### Service Worker Testing
```typescript
describe('PWA Functionality', () => {
  it('should register service worker', async () => {
    // Check if service worker is registered
    const registration = await navigator.serviceWorker.getRegistration()
    expect(registration).toBeDefined()
  })

  it('should work offline', async () => {
    // Simulate offline
    await page.setOffline(true)
    
    // Navigate to cached page
    await page.goto('/cached-route')
    
    // Should show offline fallback or cached content
    await expect(page.locator('text="Offline"')).toBeVisible()
  })

  it('should handle app installation', async () => {
    // Test beforeinstallprompt event
    const installPrompt = new Event('beforeinstallprompt')
    window.dispatchEvent(installPrompt)
    
    expect(screen.getByText('Install App')).toBeVisible()
  })
})
```

### Offline Testing
```typescript
it('should show offline indicator', async () => {
  // Set offline
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: false
  })

  render(<App />)
  
  expect(screen.getByText('Offline Mode')).toBeInTheDocument()
})
```

## 📊 Performance Testing Requirements

### Performance Budgets
```typescript
describe('Performance', () => {
  it('should meet performance budgets', async () => {
    const metrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0]
      return {
        lcp: navigation.loadEventEnd,
        fcp: navigation.responseEnd,
        tti: navigation.domInteractive
      }
    })

    expect(metrics.lcp).toBeLessThan(1000) // < 1s LCP
    expect(metrics.fcp).toBeLessThan(500)  // < 500ms FCP
    expect(metrics.tti).toBeLessThan(1500) // < 1.5s TTI
  })

  it('should have acceptable bundle size', () => {
    const stats = require('./.next/build-stats.json')
    const mainBundle = stats.bundles.find(b => b.name === 'main')
    
    expect(mainBundle.size).toBeLessThan(150 * 1024) // < 150KB
  })
})
```

### Memory Leak Testing
```typescript
it('should not have memory leaks', async () => {
  const initialMemory = await page.evaluate(() => performance.memory.usedJSHeapSize)
  
  // Perform actions that might leak memory
  for (let i = 0; i < 10; i++) {
    await page.click('[data-testid="open-modal"]')
    await page.click('[data-testid="close-modal"]')
  }
  
  // Force garbage collection
  await page.evaluate(() => {
    if (global.gc) global.gc()
  })
  
  const finalMemory = await page.evaluate(() => performance.memory.usedJSHeapSize)
  const memoryIncrease = finalMemory - initialMemory
  
  expect(memoryIncrease).toBeLessThan(5 * 1024 * 1024) // < 5MB increase
})
```

## ✅ Step-by-Step Feature Addition Checklist

### Before Writing Code
- [ ] Create test file for the new feature
- [ ] Write failing tests that define expected behavior
- [ ] Run tests to ensure they fail (Red phase of TDD)

### During Development
- [ ] Implement minimum code to make tests pass (Green phase)
- [ ] Refactor code while keeping tests green
- [ ] Add edge case tests
- [ ] Add mobile viewport tests
- [ ] Add performance tests if applicable

### Before Committing
- [ ] Run `npm run typecheck` - MUST pass with 0 errors
- [ ] Run `npm run lint` - MUST pass with 0 warnings
- [ ] Run `npm run test` - ALL tests MUST pass
- [ ] Run `npm run build` - Build MUST succeed
- [ ] Test manually in browser with mobile viewport
- [ ] Check bundle size impact with `npm run analyze`
- [ ] Test offline functionality if PWA-related
- [ ] Update documentation if needed

### Example: Adding a New Button Component
```bash
# 1. Create test file first
touch src/components/ui/__tests__/NewButton.test.tsx

# 2. Write tests
# 3. Create component file
touch src/components/ui/NewButton.tsx

# 4. Implement component
# 5. Run all checks
npm run typecheck
npm run lint
npm run test
npm run build

# 6. Manual browser testing
# - Test on mobile viewport (375x667)
# - Test touch interactions
# - Test keyboard navigation
# - Test with screen reader

# 7. Commit only after ALL checks pass
```

## ⚠️ Common Testing Pitfalls and Solutions

### 1. Forgetting Mobile Viewport
**Problem**: Tests pass but component breaks on mobile
**Solution**: Always set mobile viewport in tests
```typescript
beforeEach(() => {
  // Set mobile viewport
  window.innerWidth = 375
  window.innerHeight = 667
  window.dispatchEvent(new Event('resize'))
})
```

### 2. Not Testing Loading States
**Problem**: UI flashes or jumps during loading
**Solution**: Always test loading, error, and success states
```typescript
it('should show proper loading sequence', async () => {
  const { rerender } = render(<Component isLoading={true} />)
  expect(screen.getByTestId('skeleton')).toBeInTheDocument()
  
  rerender(<Component isLoading={false} data={mockData} />)
  expect(screen.queryByTestId('skeleton')).not.toBeInTheDocument()
  expect(screen.getByText(mockData.title)).toBeInTheDocument()
})
```

### 3. Ignoring Touch Events
**Problem**: Component only works with mouse, not touch
**Solution**: Test both mouse and touch interactions
```typescript
it('should handle both click and touch', async () => {
  const onAction = vi.fn()
  render(<Button onAction={onAction} />)
  
  // Test click
  await user.click(screen.getByRole('button'))
  expect(onAction).toHaveBeenCalledTimes(1)
  
  // Test touch
  fireEvent.touchEnd(screen.getByRole('button'))
  expect(onAction).toHaveBeenCalledTimes(2)
})
```

### 4. Missing Error Boundaries
**Problem**: One component error crashes entire app
**Solution**: Test error boundaries
```typescript
it('should catch and display errors gracefully', () => {
  const ThrowError = () => {
    throw new Error('Test error')
  }
  
  render(
    <ErrorBoundary>
      <ThrowError />
    </ErrorBoundary>
  )
  
  expect(screen.getByText(/something went wrong/i)).toBeInTheDocument()
})
```

### 5. Race Conditions in Async Tests
**Problem**: Tests randomly fail due to timing
**Solution**: Use proper async utilities
```typescript
// Bad
it('should load data', () => {
  render(<Component />)
  setTimeout(() => {
    expect(screen.getByText('Data')).toBeInTheDocument()
  }, 1000)
})

// Good
it('should load data', async () => {
  render(<Component />)
  await waitFor(() => {
    expect(screen.getByText('Data')).toBeInTheDocument()
  })
})
```

### 6. Not Testing Accessibility
**Problem**: Component not usable with keyboard/screen reader
**Solution**: Include a11y tests
```typescript
it('should be keyboard navigable', async () => {
  render(<Form />)
  const firstInput = screen.getByLabelText('Name')
  const submitButton = screen.getByRole('button', { name: 'Submit' })
  
  // Tab navigation
  firstInput.focus()
  await user.tab()
  expect(document.activeElement).toBe(submitButton)
})

it('should have proper ARIA labels', () => {
  render(<Component />)
  expect(screen.getByRole('button', { name: 'Close dialog' })).toBeInTheDocument()
})
```

### 7. Forgetting to Mock External Dependencies
**Problem**: Tests fail due to network requests
**Solution**: Mock all external dependencies
```typescript
// Mock fetch
global.fetch = vi.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve(mockData),
  })
)

// Mock timers
vi.useFakeTimers()

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  clear: vi.fn()
}
global.localStorage = localStorageMock
```

## 🎯 Testing Philosophy

1. **Test Behavior, Not Implementation**: Focus on what the user sees and does
2. **Mobile-First Always**: Start with mobile viewport and scale up
3. **Real User Scenarios**: Test actual user workflows, not isolated units
4. **Performance is a Feature**: Include performance tests for critical paths
5. **Accessibility is Required**: Every component must be keyboard/screen reader accessible

## 📋 Quick Reference

### Must-Have Tests for Every Component
- [ ] Renders without crashing
- [ ] Shows loading state
- [ ] Shows error state
- [ ] Shows success/data state
- [ ] Works on mobile viewport (375x667)
- [ ] Has 44px minimum touch targets
- [ ] Handles touch events
- [ ] Is keyboard navigable
- [ ] Has proper ARIA labels
- [ ] Cleans up on unmount

### Must-Have Tests for Every API Integration
- [ ] Successful response handling
- [ ] Error response handling
- [ ] Network error handling
- [ ] Loading state during request
- [ ] Retry logic (if applicable)
- [ ] Token refresh (for auth endpoints)
- [ ] Offline mode behavior

### Must-Have Tests for Every Page
- [ ] Mobile viewport rendering
- [ ] Loading states for all data
- [ ] Error boundaries work
- [ ] Navigation works
- [ ] Back button behavior
- [ ] PWA features (if applicable)
- [ ] SEO meta tags present
- [ ] Performance budgets met

## 🚀 Remember

**Testing is not optional. It's part of the feature.**

Every line of code you write should be tested. If it's not tested, it's not done.