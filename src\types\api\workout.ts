/**
 * Workout-related API Types
 *
 * These TypeScript interfaces map to workout models from the DrMuscle production API.
 */

import type { ExerciseModel } from './exercise'

/**
 * Settings model for workout templates
 * @todo Define based on actual API structure
 */
export interface WorkoutTemplateSettingsModel {
  // To be defined based on actual API
  restTime?: number
  setsPerExercise?: number
  repsRange?: string
  [key: string]: unknown
}

/**
 * Represents a group of related workouts (e.g., a training program)
 * @example
 * const program: WorkoutTemplateGroupModel = {
 *   Id: 1,
 *   Label: "Beginner Program",
 *   WorkoutTemplates: [...],
 *   // ... other fields
 * }
 */
export interface WorkoutTemplateGroupModel {
  Id: number
  IsFeaturedProgram: boolean
  UserId: string
  Label: string
  WorkoutTemplates: WorkoutTemplateModel[]
  IsSystemExercise: boolean
  RequiredWorkoutToLevelUp: number
  Level?: number
  RemainingToLevelUp?: number
  NextProgramId?: number
  ProgramId: number
}

/**
 * Represents a single workout session with exercises
 * @example
 * const workout: WorkoutTemplateModel = {
 *   Id: 1,
 *   Label: "Push Day",
 *   Exercises: [...],
 *   // ... other fields
 * }
 */
export interface WorkoutTemplateModel {
  Id: number
  UserId: string
  Label: string
  Exercises: ExerciseModel[]
  IsSystemExercise: boolean
  WorkoutSettingsModel: WorkoutTemplateSettingsModel
}

/**
 * Request model for getting exercise recommendations
 */
export interface GetRecommendationForExerciseModel {
  Username: string
  ExerciceId: number
  IsNormalSets?: boolean
  Set?: SetModel[]
  Increments?: number
  IsBodyweight?: boolean
  EquipmentId?: number
  IsFromServer?: boolean
  RepsMinValue?: number
  RepsMaxValue?: number
  IsPlate?: boolean
  BodypartId?: number
  IsTimeBased?: boolean
  IsUnilateral?: boolean
  Days?: number
  IsQuickMode?: boolean
  WeightInKG?: number
  IsEasy?: boolean
  IsMedium?: boolean
  IsReversePyramid?: boolean
  IsNewStarted?: boolean
  IsLightSession?: boolean
  ReprangeType?: string
  Weight?: MultiUnityWeight
  Reps?: number
  IsBackOffSet?: boolean
  IsFromPreworkout?: boolean
  BackOffSetWeight?: MultiUnityWeight
  SetStyle?: string
  IsMaxChallenge?: boolean
  IsFromChallenge?: boolean
  IsRIREnabled?: boolean
  RIR?: number
  Plate?: string
  IsDeload?: boolean
  IsFromSaveWorkout?: boolean
  Version?: string
}

/**
 * Set model for recommendation requests
 * @todo Define based on actual API structure
 */
export interface SetModel {
  // To be defined based on actual API
  reps?: number
  weight?: MultiUnityWeight
  isWarmup?: boolean
  [key: string]: unknown
}

/**
 * Handles weight in different units (lbs/kg)
 * @example
 * const weight: MultiUnityWeight = {
 *   Lb: 135,
 *   Kg: 61.23
 * }
 */
export interface MultiUnityWeight {
  Lb: number
  Kg: number
}

/**
 * Weight model alias for component usage
 */
export type WeightModel = MultiUnityWeight

/**
 * Warm-up set configuration
 */
export interface WarmUps {
  WarmUpReps: number
  WarmUpWeightSet: MultiUnityWeight
}

/**
 * Simplified structure for creating new exercise logs
 */
export interface NewExerciceLogModel {
  ExerciceId: number
  Weight1?: MultiUnityWeight
  Weight2?: MultiUnityWeight
  Weight3?: MultiUnityWeight
  Weight4?: MultiUnityWeight
  Reps1?: string
  Reps2?: string
  Reps3?: string
  Reps4?: string
  Username: string
  RIR?: number
  VersionNo: number
}

/**
 * Response model for workout statistics and history
 */
export interface GetUserWorkoutLogAverageResponse {
  Sets: GetUserWorkoutLogDate[]
  Histograms: HistogramModel[]
  AvgWorkoutTime?: TimeSpan
  LastWorkoutDate?: Date
  GetUserProgramInfoResponseModel?: GetUserProgramInfoResponseModel
  NewRecords?: NewRecordModel[]
  ConsecutiveWeeks?: number | ConsecutiveWeeksModel[] // Can be number or array
  LevelUpDetails?: LevelInfo
  HistoryExerciseModel?: HistoryExerciseModel[]
  AllTimeHistoryExerciseModel?: HistoryExerciseModel[]
  HistoryModel?: HistoryModel[]
  TotalWorkoutCompleted?: number
  LastWorkoutDateStr?: string
  LastConsecutiveWeek?: Date
  // V2 API fields
  WorkoutCount?: number // This is the correct field from V2 API
  ConsecutiveWeeksModel?: {
    ConsecutiveWeeks: number
  }
  Averages?: unknown // For total lbs lifted calculation
  LastConsecutiveWorkoutDays?: number
  SetsDate?: string[] // Array of workout dates returned by V2 API
}

/**
 * Workout log entry for a specific date
 */
export interface GetUserWorkoutLogDate {
  Date: string
  Sets: WorkoutLogSerieModel[]
  OneRMAverage?: OneRMAverage
  IsBodyweight?: boolean
}

/**
 * For logging completed sets during workout execution
 */
export interface WorkoutLogSerieModel {
  Id?: number
  UserId?: string
  ExerciseId?: number
  Exercice?: ExerciseModel
  Reps: number
  Weight: MultiUnityWeight
  RIR?: number
  IsWarmups: boolean
  IsNext: boolean
  IsFinished: boolean
  IsEditing?: boolean
  SetNo?: string
  RepsValue?: string
  WeightValue?: string
}

export interface GetUserProgramInfoResponseModel {
  RecommendedProgram: {
    Id: number
    Label: string
    RemainingToLevelUp: number
    IconUrl?: string
  }
  NextWorkoutTemplate: {
    Id: number
    Label: string
    IsSystemExercise: boolean
    ScheduledDate?: string
    UserId?: string
    Exercices?: ExerciseModel[]
    WorkoutSettingsModel?: Record<string, unknown>
  }
  WorkoutTemplates?: WorkoutTemplateModel[]
}

export interface NewRecordModel {
  // To be defined based on actual API
  exerciseId?: number
  exerciseName?: string
  recordType?: string
  value?: number
  date?: string
  [key: string]: unknown
}

export interface LevelInfo {
  // To be defined based on actual API
  currentLevel?: number
  nextLevel?: number
  progress?: number
  [key: string]: unknown
}

export interface HistoryExerciseModel {
  TotalWorkoutCompleted: number
  TotalWeight: {
    Kg: number
    Lb: number
  }
  ConsecutiveWeeks?: number
  // Additional fields that may be present
  exerciseId?: number
  exerciseName?: string
  sets?: WorkoutLogSerieModel[]
  date?: string
  [key: string]: unknown
}

export interface HistoryModel {
  // To be defined based on actual API
  [key: string]: unknown
}

export interface OneRMAverage {
  // To be defined based on actual API
  average?: number
  unit?: string
  [key: string]: unknown
}

export interface ConsecutiveWeeksModel {
  ConsecutiveWeeks: number
  // Other fields may exist
  [key: string]: unknown
}

export interface HistogramModel {
  // To be defined based on actual API
  [key: string]: unknown
}

export interface TimeSpan {
  // To be defined based on actual API
  hours?: number
  minutes?: number
  seconds?: number
  totalSeconds?: number
}

export interface TimeZoneInfoModel {
  TimeZoneId: string
  Offset: number
  IsDaylightSaving: boolean
}
