import { renderHook, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { useRouter } from 'next/navigation'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useNavigationGuard } from '../useNavigationGuard'

// Mock dependencies
vi.mock('next/navigation')
vi.mock('@/stores/workoutStore')

describe('useNavigationGuard', () => {
  const mockPush = vi.fn()
  const mockRouter = { push: mockPush }
  const mockWorkoutSession = {
    id: '123',
    startTime: new Date().toISOString(),
    endTime: null,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    vi.mocked(useWorkoutStore).mockReturnValue({
      workoutSession: null,
    } as any)

    // Mock window.confirm
    global.confirm = vi.fn()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('navigation guard event handling', () => {
    it('should not add beforeunload listener (prevents bfcache)', () => {
      const addEventListenerSpy = vi.spyOn(window, 'addEventListener')

      renderHook(() => useNavigationGuard())

      // Verify no beforeunload listener is added
      const beforeunloadCalls = addEventListenerSpy.mock.calls.filter(
        (call) => call[0] === 'beforeunload'
      )
      expect(beforeunloadCalls).toHaveLength(0)
    })

    it('should handle page navigation with active workout session', () => {
      vi.mocked(useWorkoutStore).mockReturnValue({
        workoutSession: mockWorkoutSession,
      } as any)

      const { result } = renderHook(() => useNavigationGuard())

      // Navigation guard should still work through navigateWithConfirmation
      vi.mocked(global.confirm).mockReturnValue(false)

      act(() => {
        result.current.navigateWithConfirmation('/test')
      })

      expect(global.confirm).toHaveBeenCalled()
      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should clean up any event listeners on unmount', () => {
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener')

      const { unmount } = renderHook(() => useNavigationGuard())
      unmount()

      // If we add any future event listeners, they should be cleaned up
      // Currently expecting no cleanup since we're not adding beforeunload
      const beforeunloadRemoveCalls = removeEventListenerSpy.mock.calls.filter(
        (call) => call[0] === 'beforeunload'
      )
      expect(beforeunloadRemoveCalls).toHaveLength(0)
    })
  })

  describe('navigateWithConfirmation', () => {
    it('should navigate directly when no active workout', () => {
      const { result } = renderHook(() => useNavigationGuard())

      act(() => {
        result.current.navigateWithConfirmation('/test-url')
      })

      expect(global.confirm).not.toHaveBeenCalled()
      expect(mockPush).toHaveBeenCalledWith('/test-url')
    })

    it('should navigate directly when workout is completed', () => {
      vi.mocked(useWorkoutStore).mockReturnValue({
        workoutSession: {
          ...mockWorkoutSession,
          endTime: new Date().toISOString(),
        },
      } as any)

      const { result } = renderHook(() => useNavigationGuard())

      act(() => {
        result.current.navigateWithConfirmation('/test-url')
      })

      expect(global.confirm).not.toHaveBeenCalled()
      expect(mockPush).toHaveBeenCalledWith('/test-url')
    })

    it('should show confirmation and navigate when confirmed', () => {
      vi.mocked(useWorkoutStore).mockReturnValue({
        workoutSession: mockWorkoutSession,
      } as any)
      vi.mocked(global.confirm).mockReturnValue(true)

      const { result } = renderHook(() => useNavigationGuard())

      act(() => {
        result.current.navigateWithConfirmation('/test-url')
      })

      expect(global.confirm).toHaveBeenCalledWith(
        'You have an active workout. Are you sure you want to leave? Your progress will be saved.'
      )
      expect(mockPush).toHaveBeenCalledWith('/test-url')
    })

    it('should show confirmation and not navigate when cancelled', () => {
      vi.mocked(useWorkoutStore).mockReturnValue({
        workoutSession: mockWorkoutSession,
      } as any)
      vi.mocked(global.confirm).mockReturnValue(false)

      const { result } = renderHook(() => useNavigationGuard())

      act(() => {
        result.current.navigateWithConfirmation('/test-url')
      })

      expect(global.confirm).toHaveBeenCalledWith(
        'You have an active workout. Are you sure you want to leave? Your progress will be saved.'
      )
      expect(mockPush).not.toHaveBeenCalled()
    })
  })

  describe('bfcache compatibility', () => {
    it('should not use events that prevent bfcache', () => {
      const addEventListenerSpy = vi.spyOn(window, 'addEventListener')

      renderHook(() => useNavigationGuard())

      // Check that we're not using unload event (which prevents bfcache)
      const unloadCalls = addEventListenerSpy.mock.calls.filter(
        (call) => call[0] === 'unload'
      )
      expect(unloadCalls).toHaveLength(0)

      // Check that we're not using beforeunload event (which prevents bfcache)
      const beforeunloadCalls = addEventListenerSpy.mock.calls.filter(
        (call) => call[0] === 'beforeunload'
      )
      expect(beforeunloadCalls).toHaveLength(0)
    })
  })
})
