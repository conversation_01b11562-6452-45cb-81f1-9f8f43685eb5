'use client'

import { useState, useEffect, useRef, useCallback } from 'react'

interface ExerciseTimerProps {
  restDuration?: number // in seconds
  onRestComplete?: () => void
  workoutStartTime?: Date
  isExercising?: boolean
  soundEnabled?: boolean
}

export function ExerciseTimer({
  restDuration = 90,
  onRestComplete,
  workoutStartTime,
  isExercising = false,
  soundEnabled = false,
}: ExerciseTimerProps) {
  const [timeRemaining, setTimeRemaining] = useState(restDuration)
  const [isRunning, setIsRunning] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [exerciseTime, setExerciseTime] = useState(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const wakeLockRef = useRef<WakeLockSentinel | null>(null)

  // Format time display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Calculate total workout time
  const getTotalWorkoutTime = () => {
    if (!workoutStartTime) return '0:00'
    const elapsed = Math.floor((Date.now() - workoutStartTime.getTime()) / 1000)
    return formatTime(elapsed)
  }

  // Request wake lock to prevent screen sleep
  const requestWakeLock = async () => {
    if ('wakeLock' in navigator) {
      try {
        wakeLockRef.current = await navigator.wakeLock.request('screen')
      } catch (err) {
        console.error('Wake lock request failed:', err)
      }
    }
  }

  // Release wake lock
  const releaseWakeLock = () => {
    if (wakeLockRef.current) {
      wakeLockRef.current.release()
      wakeLockRef.current = null
    }
  }

  // Play sound notification
  const playSound = useCallback(() => {
    if (soundEnabled && typeof Audio !== 'undefined') {
      const audio = new Audio('/sounds/timer-complete.mp3')
      audio.play().catch((err) => console.error('Failed to play sound:', err))
    }
  }, [soundEnabled])

  // Start timer
  const startTimer = () => {
    setIsRunning(true)
    setIsPaused(false)
    requestWakeLock()
  }

  // Pause timer
  const pauseTimer = () => {
    setIsPaused(true)
    setIsRunning(false)
  }

  // Resume timer
  const resumeTimer = () => {
    setIsRunning(true)
    setIsPaused(false)
  }

  // Reset timer
  const resetTimer = () => {
    setIsRunning(false)
    setIsPaused(false)
    setTimeRemaining(restDuration)
    releaseWakeLock()
  }

  // Skip rest
  const skipRest = () => {
    setIsRunning(false)
    setTimeRemaining(0)
    releaseWakeLock()
    onRestComplete?.()
  }

  // Timer effect for rest countdown
  useEffect(() => {
    if (isRunning && !isExercising && timeRemaining > 0) {
      intervalRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            setIsRunning(false)
            playSound()
            releaseWakeLock()
            onRestComplete?.()
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } else if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isRunning, isExercising, timeRemaining, onRestComplete, playSound])

  // Timer effect for exercise count up
  useEffect(() => {
    if (isExercising) {
      const interval = setInterval(() => {
        setExerciseTime((prev) => prev + 1)
      }, 1000)

      return () => clearInterval(interval)
    }
    setExerciseTime(0)
    return undefined
  }, [isExercising])

  // Clean up wake lock on unmount
  useEffect(() => {
    return () => {
      releaseWakeLock()
    }
  }, [])

  const isNearEnd = timeRemaining <= 3 && timeRemaining > 0
  const progressPercentage =
    ((restDuration - timeRemaining) / restDuration) * 100

  if (isExercising) {
    return (
      <div className="rounded-lg bg-white p-4 shadow-sm">
        <div className="text-center">
          <p className="text-sm text-gray-600">Exercise Time</p>
          <p className="text-3xl font-bold text-gray-900">
            {formatTime(exerciseTime)}
          </p>
          {workoutStartTime && (
            <p className="mt-2 text-sm text-gray-500">
              Total Time: {getTotalWorkoutTime()}
            </p>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="rounded-lg bg-white p-4 shadow-sm">
      <div className="mb-4 text-center">
        <p className="text-sm text-gray-600">Rest Time</p>
        <div
          role="timer"
          aria-live="polite"
          aria-label={`Rest time ${formatTime(timeRemaining)} remaining`}
          className={`text-3xl font-bold ${
            isNearEnd ? 'text-red-600' : 'text-gray-900'
          }`}
        >
          {formatTime(timeRemaining)}
        </div>
      </div>

      {/* Progress bar */}
      {isRunning && (
        <div className="mb-4">
          <div className="h-2 w-full rounded-full bg-gray-200">
            <div
              role="progressbar"
              aria-valuenow={timeRemaining}
              aria-valuemin={0}
              aria-valuemax={restDuration}
              className="h-full rounded-full bg-blue-600 transition-all duration-1000"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>
      )}

      {/* Control buttons */}
      <div className="flex gap-2">
        {!isRunning && !isPaused && (
          <>
            <button
              onClick={startTimer}
              className="flex-1 rounded-lg bg-blue-600 py-3 text-white hover:bg-blue-700"
              style={{ minHeight: '44px' }}
            >
              Start
            </button>
            <button
              onClick={skipRest}
              className="rounded-lg border border-gray-300 px-4 py-3 text-gray-700 hover:bg-gray-50"
              style={{ minHeight: '44px' }}
            >
              Skip Rest
            </button>
          </>
        )}

        {isRunning && (
          <>
            <button
              onClick={pauseTimer}
              className="flex-1 rounded-lg bg-yellow-600 py-3 text-white hover:bg-yellow-700"
              style={{ minHeight: '44px' }}
            >
              Pause
            </button>
            <button
              onClick={resetTimer}
              className="rounded-lg border border-gray-300 px-4 py-3 text-gray-700 hover:bg-gray-50"
              style={{ minHeight: '44px' }}
            >
              Reset
            </button>
          </>
        )}

        {isPaused && (
          <>
            <button
              onClick={resumeTimer}
              className="flex-1 rounded-lg bg-green-600 py-3 text-white hover:bg-green-700"
              style={{ minHeight: '44px' }}
            >
              Resume
            </button>
            <button
              onClick={resetTimer}
              className="rounded-lg border border-gray-300 px-4 py-3 text-gray-700 hover:bg-gray-50"
              style={{ minHeight: '44px' }}
            >
              Reset
            </button>
          </>
        )}
      </div>

      {workoutStartTime && (
        <p className="mt-4 text-center text-sm text-gray-500">
          Total Time: {getTotalWorkoutTime()}
        </p>
      )}
    </div>
  )
}
