import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { WorkoutOverview } from '../WorkoutOverview'
import * as useWorkoutModule from '@/hooks/useWorkout'

// Mock next/navigation
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock pull to refresh hook
vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isPulling: false,
    isRefreshing: false,
  }),
}))

describe('WorkoutOverview - Blank Page Issue', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should continue showing "No Workout Available" message and not go blank', async () => {
    // Given - API returns no workout data
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue({
      todaysWorkout: null,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: {
        hasData: false,
      },
      exercises: [],
      exerciseWorkSetsModels: [],
      expectedExerciseCount: 0,
      hasInitialData: true,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null,
      finishWorkout: vi.fn(),
      isLoading: false,
    } as any)

    // When - Component renders
    const { container } = render(<WorkoutOverview />)

    // Then - Should show "No Workout Available" message
    expect(screen.getByText('No Workout Available')).toBeInTheDocument()
    expect(
      screen.getByText(
        /It looks like you don't have a workout program assigned yet/
      )
    ).toBeInTheDocument()

    // Wait for potential navigation or unmounting (simulate 10 seconds)
    await waitFor(
      () => {
        // Component should still be mounted and showing the message
        expect(container.querySelector('div')).toBeInTheDocument()
        expect(screen.getByText('No Workout Available')).toBeInTheDocument()
      },
      { timeout: 10000 }
    )

    // No navigation should have occurred
    expect(mockPush).not.toHaveBeenCalled()
  })

  it('should handle empty workout array (length 0) without going blank', async () => {
    // Given - API returns empty workout array
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue({
      todaysWorkout: [],
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: {
        hasData: false,
      },
      exercises: [],
      exerciseWorkSetsModels: [],
      expectedExerciseCount: 0,
      hasInitialData: true,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null,
      finishWorkout: vi.fn(),
      isLoading: false,
    } as any)

    // When - Component renders
    render(<WorkoutOverview />)

    // Then - Should show "No Workout Available" message
    expect(screen.getByText('No Workout Available')).toBeInTheDocument()

    // Wait to ensure no blank page
    await waitFor(() => {
      expect(screen.getByText('No Workout Available')).toBeInTheDocument()
    })
  })
})
