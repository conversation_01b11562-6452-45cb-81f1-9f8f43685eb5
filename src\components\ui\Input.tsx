'use client'

import React, { forwardRef, InputHTMLAttributes } from 'react'
import { cn } from '@/lib/utils'

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  fullWidth?: boolean
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ label, error, fullWidth = false, className, ...props }, ref) => {
    return (
      <div className={cn('flex flex-col gap-1', fullWidth && 'w-full')}>
        {label && (
          <label className="text-sm font-medium text-text-secondary">
            {label}
          </label>
        )}
        <input
          ref={ref}
          className={cn(
            // Base styles
            'h-12 px-4', // 48px height for comfortable touch
            'bg-bg-tertiary',
            'border border-brand-primary/20',
            'text-text-primary placeholder-text-tertiary',
            'rounded-theme',
            'transition-all duration-200',
            // Focus states
            'focus:outline-none',
            'focus:border-brand-primary',
            'focus:ring-2 focus:ring-brand-primary/20',
            // Error states
            error && [
              'border-red-500',
              'focus:border-red-500',
              'focus:ring-red-500/20',
            ],
            // Disabled state
            'disabled:opacity-50 disabled:cursor-not-allowed',
            // Full width
            fullWidth && 'w-full',
            className
          )}
          {...props}
        />
        {error && <span className="text-sm text-red-500">{error}</span>}
      </div>
    )
  }
)

Input.displayName = 'Input'
