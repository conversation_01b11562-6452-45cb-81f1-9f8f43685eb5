'use client'

import { useCallback, useEffect, useState } from 'react'
import { apiClient } from '@/api/client'
import { useAuthStore } from '@/stores/authStore'

export default function TestAuthPage() {
  const [testResults, setTestResults] = useState<string[]>([])
  const authState = useAuthStore()

  const addResult = useCallback((result: string) => {
    setTestResults((prev) => [...prev, result])
  }, [])

  const testAuthHeader = useCallback(async () => {
    addResult('Starting auth header test...')

    // Check if we have a token
    if (!authState.token) {
      addResult('❌ No token in auth store')
      return
    }

    addResult(`✅ Token found: ${authState.token.substring(0, 20)}...`)

    try {
      // Test if interceptor adds auth header by making a mock request
      try {
        // Use the apiClient to create a request (but don't send it)
        await apiClient.get('/WorkoutLogGroups', {
          adapter: (config) => {
            // Check if Authorization header was added
            if (config.headers['Authorization']) {
              addResult(
                `✅ Authorization header added: ${config.headers['Authorization']}`
              )
            } else {
              addResult('❌ Authorization header not added')
            }
            // Return a mock response to prevent actual network request
            return Promise.resolve({
              data: {},
              status: 200,
              statusText: 'OK',
              headers: {},
              config,
            })
          },
        })
      } catch (err) {
        addResult(`❌ Error during interceptor test: ${err}`)
      }
    } catch (error) {
      addResult(`❌ Error testing auth header: ${error}`)
    }
  }, [authState.token, addResult])

  useEffect(() => {
    // Run test on mount if authenticated
    if (authState.isAuthenticated) {
      testAuthHeader()
    }
  }, [authState.isAuthenticated, testAuthHeader])

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Auth Header Test</h1>

      <div className="mb-4 p-4 bg-gray-100 rounded">
        <p>
          <strong>Auth State:</strong>
        </p>
        <p>Authenticated: {authState.isAuthenticated ? '✅' : '❌'}</p>
        <p>User: {authState.user?.email || 'None'}</p>
        <p>
          Token:{' '}
          {authState.token ? `${authState.token.substring(0, 20)}...` : 'None'}
        </p>
      </div>

      <div className="mb-4">
        <button
          onClick={testAuthHeader}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Test Auth Header
        </button>
      </div>

      <div className="p-4 bg-gray-50 rounded">
        <h2 className="font-bold mb-2">Test Results:</h2>
        {testResults.map((result) => (
          <div key={`${Date.now()}-${result}`} className="font-mono text-sm">
            {result}
          </div>
        ))}
      </div>
    </div>
  )
}
