'use client'

import React, { useEffect, useState } from 'react'
import type { ExerciseWorkSetsModel } from '@/types'
import { SetList } from './SetList'
import { ExerciseItemSkeleton } from '@/components/ui/Skeletons'

interface ExerciseCardProps {
  exercise: ExerciseWorkSetsModel
  onExerciseClick: (exerciseId: number) => void
  onRetry: (exerciseId: number) => void
  isCurrentExercise?: boolean
}

export function ExerciseCard({
  exercise,
  onExerciseClick,
  onRetry,
  isCurrentExercise = false,
}: ExerciseCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // Auto-expand current exercise
  useEffect(() => {
    if (isCurrentExercise) {
      setIsExpanded(true)
    }
  }, [isCurrentExercise])

  const handleCardClick = () => {
    onExerciseClick(exercise.Id)
  }

  const handleRetryClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onRetry(exercise.Id)
  }

  const toggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsExpanded(!isExpanded)
  }

  // Show skeleton if loading sets
  if (exercise.isLoadingSets && exercise.sets.length === 0) {
    return <ExerciseItemSkeleton />
  }

  return (
    <div
      className={`rounded-theme bg-gradient-overlay-subtle bg-bg-secondary p-4 shadow-theme-md hover:shadow-theme-lg transition-all duration-300 border border-brand-primary/5 hover:border-brand-primary/20 ${
        isCurrentExercise ? 'ring-2 ring-brand-primary shadow-theme-lg' : ''
      } ${exercise.IsFinished ? 'opacity-60' : ''}`}
      onClick={handleCardClick}
      role="button"
      tabIndex={0}
      aria-label={`Exercise: ${exercise.Label}`}
      aria-expanded={isExpanded}
      data-testid="exercise-card"
    >
      {/* Exercise Header */}
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="font-heading font-medium text-text-primary tracking-luxury text-shadow-sm">
            {exercise.Label}
            {exercise.IsFinished && (
              <span className="ml-2 text-sm text-brand-primary text-shadow-gold">
                ✓
              </span>
            )}
          </h3>

          {/* Quick Stats */}
          <div className="mt-1 text-sm text-text-secondary">
            {exercise.sets.length > 0 && (
              <span>{exercise.sets.length} sets</span>
            )}
            {exercise.IsBodyweight && (
              <span className="ml-2">• Bodyweight</span>
            )}
            {exercise.IsNextExercise && (
              <span className="ml-2 text-brand-primary">• Next</span>
            )}
          </div>
        </div>

        {/* Expand/Collapse Button */}
        {exercise.sets.length > 0 && (
          <button
            onClick={toggleExpand}
            className="ml-2 p-2 text-text-tertiary hover:text-text-secondary"
            aria-label={isExpanded ? 'Collapse sets' : 'Expand sets'}
          >
            <svg
              className={`h-5 w-5 transform transition-transform ${
                isExpanded ? 'rotate-180' : ''
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
        )}
      </div>

      {/* Error State */}
      {exercise.setsError && (
        <div className="mt-3 rounded-md bg-red-50 p-3">
          <div className="flex items-center justify-between">
            <p className="text-sm text-red-600">{exercise.setsError}</p>
            <button
              onClick={handleRetryClick}
              className="ml-2 text-sm font-medium text-red-600 hover:text-red-500"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Sets List (Expandable) */}
      {isExpanded && (
        <div className="mt-4 animate-fadeIn">
          <SetList sets={exercise.sets} isLoading={exercise.isLoadingSets} />
        </div>
      )}

      {/* Loading Indicator for Sets */}
      {exercise.isLoadingSets && exercise.sets.length === 0 && (
        <div className="mt-3 text-center text-sm text-text-secondary">
          Loading sets...
        </div>
      )}
    </div>
  )
}
