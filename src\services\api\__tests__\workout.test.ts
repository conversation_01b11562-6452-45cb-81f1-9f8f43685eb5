import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import {
  getUserWorkoutProgramInfo,
  getWorkoutDetails,
  getExerciseRecommendation,
  generateRecommendationCacheKey,
} from '../workout'
import { apiClient } from '@/api/client'
import { getCurrentUserEmail } from '@/lib/auth-utils'

// Mock the API client
vi.mock('@/api/client', () => ({
  apiClient: {
    post: vi.fn(),
    get: vi.fn(),
  },
}))

// Mock auth utils
vi.mock('@/lib/auth-utils', () => ({
  getCurrentUserEmail: vi.fn(),
}))

// Mock Intl.DateTimeFormat
const mockTimeZone = 'America/New_York'
global.Intl.DateTimeFormat = vi.fn(() => ({
  resolvedOptions: () => ({ timeZone: mockTimeZone }),
})) as any

describe('Workout API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getUserWorkoutProgramInfo', () => {
    it('should fetch user workout program info with timezone', async () => {
      const mockResponse = {
        data: {
          GetUserProgramInfoResponseModel: {
            NextWorkoutTemplate: {
              Id: 12345,
              Label: 'Push Day',
              IsSystemExercise: true,
              Exercises: null,
            },
            RecommendedProgram: {
              Id: 100,
              Label: 'Beginner Program',
            },
          },
          LastWorkoutDate: '2025-01-30T10:00:00Z',
          LastConsecutiveWorkoutDays: 5,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getUserWorkoutProgramInfo()

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
        {
          TimeZoneId: mockTimeZone,
          Offset: new Date().getTimezoneOffset() / -60,
          IsDaylightSaving: false,
        }
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle wrapped API response', async () => {
      const mockData = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: {
            Id: 12345,
            Label: 'Push Day',
            IsSystemExercise: true,
            Exercises: null,
          },
        },
      }

      const wrappedResponse = {
        data: {
          StatusCode: 200,
          Result: mockData,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(wrappedResponse)

      const result = await getUserWorkoutProgramInfo()
      expect(result).toEqual(mockData)
    })

    it('should handle root-level data with StatusCode', async () => {
      const mockData = {
        StatusCode: 200,
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: {
            Id: 12345,
            Label: 'Push Day',
            IsSystemExercise: true,
            Exercises: null,
          },
        },
        LastWorkoutDate: '2025-01-30T10:00:00Z',
        LastConsecutiveWorkoutDays: 5,
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce({ data: mockData })

      const result = await getUserWorkoutProgramInfo()

      // Should return data without StatusCode
      expect(result).toEqual({
        GetUserProgramInfoResponseModel:
          mockData.GetUserProgramInfoResponseModel,
        LastWorkoutDate: mockData.LastWorkoutDate,
        LastConsecutiveWorkoutDays: mockData.LastConsecutiveWorkoutDays,
      })
    })

    it('should handle Data wrapper response', async () => {
      const mockData = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: {
            Id: 12345,
            Label: 'Push Day',
          },
        },
      }

      const wrappedResponse = {
        data: {
          StatusCode: 200,
          Data: mockData,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(wrappedResponse)

      const result = await getUserWorkoutProgramInfo()
      expect(result).toEqual(mockData)
    })

    it('should throw error on API failure', async () => {
      const mockError = new Error('Network error')
      vi.mocked(apiClient.post).mockRejectedValueOnce(mockError)

      await expect(getUserWorkoutProgramInfo()).rejects.toThrow('Network error')
    })

    it('should return Result even when API returns hasData: false', async () => {
      const mockResult = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: {
            Id: 12345,
            Label: 'Push Day',
            IsSystemExercise: true,
            Exercises: null,
          },
        },
      }

      const mockResponse = {
        data: {
          statusCode: 200,
          hasResult: true,
          hasData: false,
          dataKeys: ['GetUserProgramInfoResponseModel', 'StatusCode', 'Result'],
          StatusCode: 200,
          Result: mockResult,
          ErrorMessage: null,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getUserWorkoutProgramInfo()
      expect(result).toEqual(mockResult)
    })

    it('should handle camelCase API response properties', async () => {
      const mockResult = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: {
            Id: 12345,
            Label: 'Push Day',
            IsSystemExercise: true,
            Exercises: null,
          },
        },
      }

      // This matches the exact console output structure
      const mockResponse = {
        data: {
          statusCode: 200,
          hasResult: true,
          hasData: false,
          result: mockResult, // camelCase result
          errorMessage: null,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getUserWorkoutProgramInfo()
      expect(result).toEqual(mockResult)
    })

    it('should return null when API returns StatusCode 200 with no data', async () => {
      const mockResponse = {
        data: {
          StatusCode: 200,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getUserWorkoutProgramInfo()
      expect(result).toBeNull()
    })
  })

  describe('getWorkoutDetails', () => {
    it('should fetch workout details by ID', async () => {
      const workoutId = 12345
      const mockResponse = {
        data: {
          Id: workoutId,
          Label: 'Push Day',
          Exercises: [
            {
              Id: 1001,
              Label: 'Bench Press',
              BodyPartId: 2,
              EquipmentId: 1,
              IsBodyweight: false,
              VideoUrl: 'https://example.com/video',
              RecoModel: null,
            },
          ],
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getWorkoutDetails(workoutId)

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/Workout/GetUserCustomizedCurrentWorkout',
        workoutId
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle wrapped API response for workout details', async () => {
      const workoutId = 12345
      const mockData = {
        Id: workoutId,
        Label: 'Push Day',
        Exercises: [],
      }

      const wrappedResponse = {
        data: {
          StatusCode: 200,
          Result: mockData,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(wrappedResponse)

      const result = await getWorkoutDetails(workoutId)
      expect(result).toEqual(mockData)
    })

    it('should throw error on workout details failure', async () => {
      const mockError = new Error('Not found')
      vi.mocked(apiClient.post).mockRejectedValueOnce(mockError)

      await expect(getWorkoutDetails(123)).rejects.toThrow('Not found')
    })
  })

  describe('getExerciseRecommendation', () => {
    it('should fetch exercise recommendation with all required parameters', async () => {
      const request = {
        Username: '<EMAIL>',
        ExerciseId: 1001,
        WorkoutId: 12345,
        IsQuickMode: false,
        LightSessionDays: null,
        SwapedExId: undefined,
        IsStrengthPhashe: false, // Note the typo
        IsFreePlan: false,
        IsFirstWorkoutOfStrengthPhase: false,
        VersionNo: 1,
        SetStyle: 'Normal',
        IsFlexibility: false,
      }

      const mockResponse = {
        data: {
          Series: 3,
          Reps: 8,
          Weight: { Kg: 80, Lb: 176, Unity: 0, Entered: 80, IsRound: true },
          OneRMProgress: 0.85,
          RecommendationInKg: 80,
          OneRMPercentage: 85,
          WarmUpsList: [
            {
              WarmUpReps: 10,
              WarmUpWeightSet: {
                Kg: 40,
                Lb: 88,
                Unity: 0,
                Entered: 40,
                IsRound: true,
              },
            },
          ],
          NbPauses: 90, // Rest time in seconds
          IsBodyweight: false,
          IsNormalSets: true,
          IsDeload: false,
          IsBackOffSet: false,
          IsPyramid: false,
          IsReversePyramid: false,
          MinReps: 6,
          MaxReps: 12,
          RIR: 2,
          LastLogDate: '2025-01-25T10:00:00Z',
          isPlateAvailable: true,
          isDumbbellAvailable: true,
          isPulleyAvailable: false,
          isBandsAvailable: false,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getExerciseRecommendation(request)

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
        request
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should use RestPause endpoint for RestPause set style', async () => {
      const request = {
        Username: '<EMAIL>',
        ExerciseId: 1001,
        WorkoutId: 12345,
        IsQuickMode: null,
        LightSessionDays: null,
        SwapedExId: undefined,
        IsStrengthPhashe: false,
        IsFreePlan: false,
        IsFirstWorkoutOfStrengthPhase: false,
        VersionNo: 1,
        SetStyle: 'RestPause',
        IsFlexibility: false,
      }

      const mockResponse = {
        data: {
          StatusCode: 200,
          Result: {
            Series: 1,
            Reps: 15,
            Weight: { Kg: 60, Lb: 132, Unity: 0, Entered: 60, IsRound: true },
            NbPauses: 15,
            IsNormalSets: false,
          },
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      await getExerciseRecommendation(request)

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew',
        request
      )
    })

    it('should handle null recommendations (no exercise history)', async () => {
      const request = {
        Username: '<EMAIL>',
        ExerciseId: 999,
        WorkoutId: 12345,
        IsQuickMode: false,
        LightSessionDays: null,
        SwapedExId: undefined,
        IsStrengthPhashe: false,
        IsFreePlan: false,
        IsFirstWorkoutOfStrengthPhase: false,
        VersionNo: 1,
        SetStyle: 'Normal',
        IsFlexibility: false,
      }

      const mockResponse = {
        data: {
          StatusCode: 200,
          Result: null, // No history for this exercise
          ErrorMessage: null,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getExerciseRecommendation(request)

      expect(result).toBeNull()
    })

    it('should get username from auth state if not provided', async () => {
      // Mock getCurrentUserEmail to return the email
      vi.mocked(getCurrentUserEmail).mockReturnValue('<EMAIL>')

      const request = {
        Username: '', // Empty username
        ExerciseId: 1001,
        WorkoutId: 12345,
        SetStyle: 'Normal',
      }

      const mockResponse = {
        data: {
          Weight: { Kg: 80, Lb: 176 },
          Reps: 8,
          Sets: 3,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      await getExerciseRecommendation(request)

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
        expect.objectContaining({
          Username: '<EMAIL>',
        })
      )
    })

    it('should pass all required parameters even when not provided in request', async () => {
      const request = {
        Username: '<EMAIL>',
        ExerciseId: 1001,
        WorkoutId: 12345,
        SetStyle: 'Normal',
        // Missing required parameters
      }

      const mockResponse = {
        data: {
          Series: 3,
          Reps: 8,
          Weight: { Kg: 80, Lb: 176, Unity: 0, Entered: 80, IsRound: true },
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      await getExerciseRecommendation(request)

      // Should add default values for missing required parameters
      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
        expect.objectContaining({
          Username: '<EMAIL>',
          ExerciseId: 1001,
          WorkoutId: 12345,
          IsQuickMode: undefined,
          LightSessionDays: undefined,
          SwapedExId: undefined,
          IsStrengthPhashe: undefined, // Note the typo
          IsFreePlan: undefined,
          IsFirstWorkoutOfStrengthPhase: undefined,
          VersionNo: undefined,
          SetStyle: 'Normal',
          IsFlexibility: undefined,
        })
      )
    })

    it('should return null on recommendation failure', async () => {
      const request = {
        Username: '<EMAIL>',
        ExerciseId: 1001,
        WorkoutId: 12345,
      }

      const mockError = new Error('Server error')
      vi.mocked(apiClient.post).mockRejectedValueOnce(mockError)

      const result = await getExerciseRecommendation(request)
      expect(result).toBeNull()
    })

    it.skip('should throw error if no username available', async () => {
      const request = {
        Username: '',
        ExerciseId: 1001,
        WorkoutId: 12345,
      }

      // No auth state in localStorage
      localStorage.clear()

      await expect(getExerciseRecommendation(request)).rejects.toThrow(
        'Username is required for exercise recommendations'
      )
    })
  })

  describe('generateRecommendationCacheKey', () => {
    it('should generate correct cache key', () => {
      const key = generateRecommendationCacheKey('user123', 456, 789)
      expect(key).toBe('user123-456-789')
    })
  })
})
