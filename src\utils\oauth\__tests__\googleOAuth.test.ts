/**
 * Unit tests for GoogleOAuthHelper
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { GoogleOAuthHelper } from '../googleOAuth'
import type { GoogleCredentialResponse } from '../../../types/oauth'

// Type for Google Sign-In API
interface MockGoogleAccounts {
  accounts: {
    id: {
      initialize: ReturnType<typeof vi.fn>
      prompt: ReturnType<typeof vi.fn>
      renderButton: ReturnType<typeof vi.fn>
      disableAutoSelect: ReturnType<typeof vi.fn>
      cancel: ReturnType<typeof vi.fn>
      revoke: ReturnType<typeof vi.fn>
    }
  }
}

// Type for window with google property
interface WindowWithGoogle extends Window {
  google?: MockGoogleAccounts
}

// Type for GoogleOAuthHelper private properties
interface GoogleOAuthHelperPrivate {
  isInitialized: boolean
  isLoading: boolean
  loadPromise: Promise<void> | null
  loadGoogleSDK: () => Promise<void>
}

// Mock performance API
vi.mock('../../performance', () => ({
  PerformanceMonitor: {
    mark: vi.fn(),
    measure: vi.fn(),
  },
}))

describe('GoogleOAuthHelper', () => {
  let mockGoogle: MockGoogleAccounts
  let scriptElement: HTMLScriptElement | null = null

  beforeEach(() => {
    // Clear DOM
    document.head.innerHTML = ''

    // Reset window.google
    delete (window as WindowWithGoogle).google

    // Reset GoogleOAuthHelper internal state
    const helperPrivate =
      GoogleOAuthHelper as unknown as GoogleOAuthHelperPrivate
    helperPrivate.isInitialized = false
    helperPrivate.isLoading = false
    helperPrivate.loadPromise = null

    // Mock document.createElement to capture script element
    const originalCreateElement = document.createElement.bind(document)
    vi.spyOn(document, 'createElement').mockImplementation(
      (tagName: string) => {
        const element = originalCreateElement(tagName)
        if (tagName === 'script') {
          scriptElement = element as HTMLScriptElement
        }
        return element
      }
    )

    // Setup mock Google object
    mockGoogle = {
      accounts: {
        id: {
          initialize: vi.fn(),
          prompt: vi.fn(),
          renderButton: vi.fn(),
          disableAutoSelect: vi.fn(),
          cancel: vi.fn(),
          revoke: vi.fn(),
        },
      },
    }
  })

  afterEach(() => {
    vi.clearAllMocks()
    scriptElement = null
  })

  describe('SDK Loading', () => {
    it('should load Google SDK script', async () => {
      const loadPromise = (
        GoogleOAuthHelper as unknown as GoogleOAuthHelperPrivate
      ).loadGoogleSDK()

      // Should append script to head
      expect(document.head.querySelector('script')).toBeTruthy()
      expect(scriptElement?.src).toBe('https://accounts.google.com/gsi/client')
      expect(scriptElement?.async).toBe(true)
      expect(scriptElement?.defer).toBe(true)

      // Simulate successful load
      ;(window as WindowWithGoogle).google = mockGoogle
      scriptElement?.onload?.(new Event('load'))

      await loadPromise
      expect(
        (GoogleOAuthHelper as unknown as GoogleOAuthHelperPrivate).isLoading
      ).toBe(false)
    })

    it('should handle SDK load failure', async () => {
      const loadPromise = (
        GoogleOAuthHelper as unknown as GoogleOAuthHelperPrivate
      ).loadGoogleSDK()

      // Simulate load error
      scriptElement?.onerror?.(new Event('error'))

      await expect(loadPromise).rejects.toThrow(
        'Failed to load Google Sign-In SDK'
      )
      expect(
        (GoogleOAuthHelper as unknown as GoogleOAuthHelperPrivate).isLoading
      ).toBe(false)
      expect(
        (GoogleOAuthHelper as unknown as GoogleOAuthHelperPrivate).loadPromise
      ).toBeNull()
    })

    it('should return existing promise if SDK is already loading', async () => {
      // Start loading SDK through two simultaneous calls
      const initPromise1 = GoogleOAuthHelper.initialize(vi.fn(), vi.fn())
      const initPromise2 = GoogleOAuthHelper.initialize(vi.fn(), vi.fn())

      // Only one script element should be created
      expect(document.head.querySelectorAll('script').length).toBe(1)

      // Complete the load
      ;(window as WindowWithGoogle).google = mockGoogle
      scriptElement?.onload?.(new Event('load'))

      await Promise.all([initPromise1, initPromise2])

      // Both should have succeeded
      expect(
        (GoogleOAuthHelper as unknown as GoogleOAuthHelperPrivate).isInitialized
      ).toBe(true)
    })

    it('should return immediately if SDK already loaded', async () => {
      ;(window as WindowWithGoogle).google = mockGoogle

      const loadPromise = (
        GoogleOAuthHelper as unknown as GoogleOAuthHelperPrivate
      ).loadGoogleSDK()
      await loadPromise

      // Should not create new script element
      expect(document.createElement).not.toHaveBeenCalledWith('script')
    })
  })

  describe('initialize', () => {
    it('should initialize Google Sign-In with correct config', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      // Pre-load SDK
      ;(window as WindowWithGoogle).google = mockGoogle

      await GoogleOAuthHelper.initialize(onSuccess, onError, {
        autoSelect: true,
        cancelOnTapOutside: false,
        context: 'signup',
        nonce: 'test-nonce',
      })

      expect(mockGoogle.accounts.id.initialize).toHaveBeenCalledWith({
        client_id:
          '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com',
        callback: expect.any(Function),
        auto_select: true,
        cancel_on_tap_outside: false,
        context: 'signup',
        nonce: 'test-nonce',
      })

      expect(
        (GoogleOAuthHelper as unknown as GoogleOAuthHelperPrivate).isInitialized
      ).toBe(true)
      expect(onError).not.toHaveBeenCalled()
    })

    it('should use default config values', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      ;(window as WindowWithGoogle).google = mockGoogle

      await GoogleOAuthHelper.initialize(onSuccess, onError)

      expect(mockGoogle.accounts.id.initialize).toHaveBeenCalledWith({
        client_id:
          '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com',
        callback: expect.any(Function),
        auto_select: false,
        cancel_on_tap_outside: true,
        context: 'signin',
        nonce: undefined,
      })
    })

    it('should handle initialization error', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      // Simulate SDK load failure
      const loadPromise = GoogleOAuthHelper.initialize(onSuccess, onError)

      // Trigger the script error
      scriptElement?.onerror?.(new Event('error'))

      await expect(loadPromise).rejects.toThrow(
        'Failed to load Google Sign-In SDK'
      )

      expect(onError).toHaveBeenCalledWith({
        code: 'provider_error',
        message: 'Failed to initialize Google Sign-In',
        provider: 'google',
        providerError: expect.any(Error),
        details: {
          originalError: 'Failed to load Google Sign-In SDK',
        },
      })
    })
  })

  describe('signIn', () => {
    beforeEach(() => {
      ;(window as WindowWithGoogle).google = mockGoogle
    })

    it('should initiate One Tap sign-in', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      // Pre-initialize
      await GoogleOAuthHelper.initialize(onSuccess, onError)

      await GoogleOAuthHelper.signIn(onSuccess, onError)

      expect(mockGoogle.accounts.id.prompt).toHaveBeenCalledWith(
        expect.any(Function)
      )
    })

    it('should handle One Tap not displayed with error reasons', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      await GoogleOAuthHelper.initialize(onSuccess, onError)

      mockGoogle.accounts.id.prompt.mockImplementation(
        (
          callback: (notification: {
            isNotDisplayed: () => boolean
            isSkippedMoment: () => boolean
            getNotDisplayedReason?: () => string
          }) => void
        ) => {
          const notification = {
            isNotDisplayed: () => true,
            isSkippedMoment: () => false,
            getNotDisplayedReason: () => 'invalid_client',
          }
          callback(notification)
        }
      )

      await GoogleOAuthHelper.signIn(onSuccess, onError)

      expect(onError).toHaveBeenCalledWith({
        code: 'provider_error',
        message: 'Google Sign-In failed: invalid client',
        provider: 'google',
        details: { reason: 'invalid_client' },
      })
    })

    it('should silently handle One Tap skip for user reasons', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      await GoogleOAuthHelper.initialize(onSuccess, onError)

      mockGoogle.accounts.id.prompt.mockImplementation(
        (
          callback: (notification: {
            isNotDisplayed: () => boolean
            isSkippedMoment: () => boolean
            getSkippedReason?: () => string
          }) => void
        ) => {
          const notification = {
            isNotDisplayed: () => false,
            isSkippedMoment: () => true,
            getSkippedReason: () => 'user_cancel',
          }
          callback(notification)
        }
      )

      await GoogleOAuthHelper.signIn(onSuccess, onError)

      expect(onError).not.toHaveBeenCalled()
    })

    it('should handle sign-in without One Tap', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      await GoogleOAuthHelper.initialize(onSuccess, onError)
      await GoogleOAuthHelper.signIn(onSuccess, onError, false)

      expect(mockGoogle.accounts.id.prompt).not.toHaveBeenCalled()
    })

    it('should auto-initialize if not already initialized', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      await GoogleOAuthHelper.signIn(onSuccess, onError)

      expect(mockGoogle.accounts.id.initialize).toHaveBeenCalled()
      expect(
        (GoogleOAuthHelper as unknown as GoogleOAuthHelperPrivate).isInitialized
      ).toBe(true)
    })
  })

  describe('handleCredentialResponse', () => {
    const validToken =
      'eyJhbGciOiJSUzI1NiIsImtpZCI6InRlc3QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.signature'

    it('should handle valid credential response', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      ;(window as WindowWithGoogle).google = mockGoogle

      await GoogleOAuthHelper.initialize(onSuccess, onError)

      // Get the callback function that was passed to initialize
      const initCall = mockGoogle.accounts.id.initialize.mock.calls[0][0]
      const { callback } = initCall

      const response: GoogleCredentialResponse = {
        credential: validToken,
        select_by: 'auto',
        clientId:
          '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com',
      }

      callback(response)

      expect(onSuccess).toHaveBeenCalledWith({
        provider: 'google',
        providerId: '**********',
        email: '<EMAIL>',
        emailVerified: true,
        name: 'Test User',
        firstName: 'Test',
        lastName: 'User',
        pictureUrl: 'https://example.com/picture.jpg',
        rawToken: validToken,
        tokenPayload: expect.objectContaining({
          sub: '**********',
          email: '<EMAIL>',
          email_verified: true,
        }),
        metadata: {
          selectBy: 'auto',
          clientId:
            '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com',
        },
      })
    })

    it('should handle invalid token', async () => {
      const onSuccess = vi.fn()
      const onError = vi.fn()

      ;(window as WindowWithGoogle).google = mockGoogle

      await GoogleOAuthHelper.initialize(onSuccess, onError)

      const initCall = mockGoogle.accounts.id.initialize.mock.calls[0][0]
      const { callback } = initCall

      const response: GoogleCredentialResponse = {
        credential: 'invalid-token',
        select_by: 'auto',
      }

      callback(response)

      expect(onError).toHaveBeenCalledWith({
        code: 'invalid_token',
        message: 'Failed to process Google credential',
        provider: 'google',
        providerError: expect.any(Error),
        details: {
          originalError: expect.any(String),
        },
      })
    })
  })

  describe('decodeJWT', () => {
    it('should decode valid JWT token', () => {
      const validToken =
        'eyJhbGciOiJSUzI1NiIsImtpZCI6InRlc3QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.signature'

      const payload = GoogleOAuthHelper.decodeJWT(validToken)

      expect(payload).toEqual({
        iss: 'https://accounts.google.com',
        aud: '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com',
        sub: '**********',
        email: '<EMAIL>',
        email_verified: true,
        name: 'Test User',
        given_name: 'Test',
        family_name: 'User',
        picture: 'https://example.com/picture.jpg',
        iat: **********,
        exp: **********,
      })
    })

    it('should return null for invalid JWT format', () => {
      expect(GoogleOAuthHelper.decodeJWT('invalid')).toBeNull()
      expect(GoogleOAuthHelper.decodeJWT('part1.part2')).toBeNull()
      expect(GoogleOAuthHelper.decodeJWT('part1.part2.part3.part4')).toBeNull()
    })

    it('should return null for invalid base64', () => {
      const invalidBase64Token = 'header.!!!invalid-base64!!!.signature'
      expect(GoogleOAuthHelper.decodeJWT(invalidBase64Token)).toBeNull()
    })

    it('should return null for invalid JSON payload', () => {
      // This creates a token with invalid JSON in payload
      const invalidJsonToken = `header.${btoa('not valid json')}.signature`
      expect(GoogleOAuthHelper.decodeJWT(invalidJsonToken)).toBeNull()
    })

    it('should return null for wrong issuer', () => {
      const wrongIssuerToken = `header.${btoa(
        JSON.stringify({
          iss: 'https://wrong-issuer.com',
          aud: '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com',
          sub: '123',
          email: '<EMAIL>',
          exp: **********,
        })
      )}.signature`

      expect(GoogleOAuthHelper.decodeJWT(wrongIssuerToken)).toBeNull()
    })

    it('should return null for wrong audience', () => {
      const wrongAudienceToken = `header.${btoa(
        JSON.stringify({
          iss: 'https://accounts.google.com',
          aud: 'wrong-client-id',
          sub: '123',
          email: '<EMAIL>',
          exp: **********,
        })
      )}.signature`

      expect(GoogleOAuthHelper.decodeJWT(wrongAudienceToken)).toBeNull()
    })

    it('should return null for expired token', () => {
      const expiredToken = `header.${btoa(
        JSON.stringify({
          iss: 'https://accounts.google.com',
          aud: '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com',
          sub: '123',
          email: '<EMAIL>',
          exp: Math.floor(Date.now() / 1000) - 3600, // Expired 1 hour ago
        })
      )}.signature`

      expect(GoogleOAuthHelper.decodeJWT(expiredToken)).toBeNull()
    })

    it('should accept alternative issuer format', () => {
      const altIssuerToken = `header.${btoa(
        JSON.stringify({
          iss: 'accounts.google.com', // Without https://
          aud: '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com',
          sub: '123',
          email: '<EMAIL>',
          exp: **********,
        })
      )}.signature`

      const payload = GoogleOAuthHelper.decodeJWT(altIssuerToken)
      expect(payload).toBeTruthy()
      expect(payload?.iss).toBe('accounts.google.com')
    })
  })

  describe('getUserInfo', () => {
    it('should fetch user info with access token', async () => {
      const mockUserInfo = {
        id: '**********',
        email: '<EMAIL>',
        verified_email: true,
        name: 'Test User',
        given_name: 'Test',
        family_name: 'User',
        picture: 'https://example.com/picture.jpg',
      }

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: async () => mockUserInfo,
      })

      const userInfo = await GoogleOAuthHelper.getUserInfo('test-access-token')

      expect(fetch).toHaveBeenCalledWith(
        'https://www.googleapis.com/oauth2/v1/userinfo?access_token=test-access-token'
      )
      expect(userInfo).toEqual(mockUserInfo)
    })

    it('should return null on fetch error', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 401,
      })

      const userInfo = await GoogleOAuthHelper.getUserInfo('invalid-token')
      expect(userInfo).toBeNull()
    })

    it('should return null on network error', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))

      const userInfo = await GoogleOAuthHelper.getUserInfo('test-token')
      expect(userInfo).toBeNull()
    })
  })

  describe('renderButton', () => {
    beforeEach(() => {
      ;(window as WindowWithGoogle).google = mockGoogle
    })

    it('should render button with default config', () => {
      const element = document.createElement('div')

      GoogleOAuthHelper.renderButton(element)

      expect(mockGoogle.accounts.id.renderButton).toHaveBeenCalledWith(
        element,
        {
          type: 'standard',
          theme: 'outline',
          size: 'large',
          text: 'signin_with',
          shape: 'rectangular',
        }
      )
    })

    it('should render button with custom config', () => {
      const element = document.createElement('div')

      GoogleOAuthHelper.renderButton(element, {
        theme: 'filled_blue',
        size: 'medium',
        shape: 'pill',
        text: 'continue_with',
      })

      expect(mockGoogle.accounts.id.renderButton).toHaveBeenCalledWith(
        element,
        {
          type: 'standard',
          theme: 'filled_blue',
          size: 'medium',
          text: 'continue_with',
          shape: 'pill',
        }
      )
    })

    it('should throw error if SDK not initialized', () => {
      delete (window as WindowWithGoogle).google

      const element = document.createElement('div')

      expect(() => GoogleOAuthHelper.renderButton(element)).toThrow(
        'Google Sign-In SDK not initialized'
      )
    })
  })

  describe('Utility methods', () => {
    beforeEach(() => {
      ;(window as WindowWithGoogle).google = mockGoogle
    })

    it('should cancel One Tap flow', () => {
      GoogleOAuthHelper.cancel()
      expect(mockGoogle.accounts.id.cancel).toHaveBeenCalled()
    })

    it('should disable auto-select', () => {
      GoogleOAuthHelper.disableAutoSelect()
      expect(mockGoogle.accounts.id.disableAutoSelect).toHaveBeenCalled()
    })

    it('should revoke authorization', () => {
      const callback = vi.fn()
      GoogleOAuthHelper.revoke('<EMAIL>', callback)
      expect(mockGoogle.accounts.id.revoke).toHaveBeenCalledWith(
        '<EMAIL>',
        callback
      )
    })

    it('should check if Google Sign-In is available', () => {
      expect(GoogleOAuthHelper.isAvailable()).toBe(true)

      delete (window as WindowWithGoogle).google
      expect(GoogleOAuthHelper.isAvailable()).toBe(false)
    })

    it('should check if Google Sign-In is ready', async () => {
      expect(GoogleOAuthHelper.isReady()).toBe(false)

      await GoogleOAuthHelper.initialize(vi.fn(), vi.fn())
      expect(GoogleOAuthHelper.isReady()).toBe(true)

      delete (window as WindowWithGoogle).google
      expect(GoogleOAuthHelper.isReady()).toBe(false)
    })
  })

  describe('Edge cases and error handling', () => {
    it('should handle multiple simultaneous initialization calls', async () => {
      ;(window as WindowWithGoogle).google = mockGoogle

      const onSuccess1 = vi.fn()
      const onError1 = vi.fn()
      const onSuccess2 = vi.fn()
      const onError2 = vi.fn()

      await Promise.all([
        GoogleOAuthHelper.initialize(onSuccess1, onError1),
        GoogleOAuthHelper.initialize(onSuccess2, onError2),
      ])

      // Should only initialize once
      expect(mockGoogle.accounts.id.initialize).toHaveBeenCalledTimes(2)
      expect(
        (GoogleOAuthHelper as unknown as GoogleOAuthHelperPrivate).isInitialized
      ).toBe(true)
    })

    it('should handle SDK methods when window.google is undefined', () => {
      delete (window as WindowWithGoogle).google

      expect(() => GoogleOAuthHelper.cancel()).not.toThrow()
      expect(() => GoogleOAuthHelper.disableAutoSelect()).not.toThrow()
      expect(() => GoogleOAuthHelper.revoke('<EMAIL>')).not.toThrow()
    })
  })
})
