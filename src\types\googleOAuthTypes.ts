/**
 * Google OAuth Authentication Types for Dr. Muscle X
 */

/**
 * Google OAuth credential response structure
 * This extends the global google.accounts.id.CredentialResponse
 */
export interface GoogleCredentialResponse {
  /** JWT credential token containing user information */
  credential: string
  /** How the credential was selected */
  select_by:
    | 'auto'
    | 'user'
    | 'user_1tap'
    | 'user_2tap'
    | 'btn'
    | 'btn_confirm'
    | 'btn_add_session'
    | 'btn_confirm_add_session'
  /** Google client ID */
  clientId?: string
}

/**
 * Decoded Google JWT token payload
 */
export interface GoogleTokenPayload {
  /** Unique Google user ID */
  sub: string
  /** User's email address */
  email: string
  /** Whether email is verified */
  email_verified: boolean
  /** User's full name */
  name?: string
  /** User's given/first name */
  given_name?: string
  /** User's family/last name */
  family_name?: string
  /** Profile picture URL */
  picture?: string
  /** Issued at timestamp */
  iat: number
  /** Expiration timestamp */
  exp: number
  /** Issuer (google) */
  iss: string
  /** Audience (client ID) */
  aud: string
  /** Nonce for security */
  nonce?: string
}

/**
 * Google OAuth configuration
 */
export interface GoogleOAuthConfig {
  clientId: string
  /** OAuth scopes to request */
  scope?: string
  /** Prompt behavior */
  prompt?: 'none' | 'consent' | 'select_account'
  /** Auto-select account if possible */
  autoSelect?: boolean
  /** Cancel sign-in on tap outside */
  cancelOnTapOutside?: boolean
  /** Context for the sign-in */
  context?: 'signin' | 'signup' | 'use'
  /** UX mode */
  uxMode?: 'popup' | 'redirect'
  /** Nonce for security */
  nonce?: string
}

/**
 * Type guard for Google credential response
 */
export function isGoogleCredentialResponse(
  response: unknown
): response is GoogleCredentialResponse {
  return (
    typeof response === 'object' &&
    response !== null &&
    'credential' in response &&
    typeof (response as GoogleCredentialResponse).credential === 'string'
  )
}
