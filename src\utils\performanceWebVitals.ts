/**
 * Performance observer for Core Web Vitals
 */
export function observeWebVitals(
  callback: (metric: { name: string; value: number; rating: string }) => void
): void {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
    return
  }

  try {
    // Observe Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      if (lastEntry) {
        callback({
          name: 'LCP',
          value: lastEntry.startTime,
          rating: lastEntry.startTime < 2500 ? 'good' : 'needs-improvement',
        })
      }
    })
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

    // Observe First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        const eventTiming = entry as unknown as {
          processingStart?: number
          startTime: number
        }
        if (eventTiming.processingStart) {
          callback({
            name: 'FID',
            value: eventTiming.processingStart - eventTiming.startTime,
            rating:
              eventTiming.processingStart - eventTiming.startTime < 100
                ? 'good'
                : 'needs-improvement',
          })
        }
      })
    })
    fidObserver.observe({ entryTypes: ['first-input'] })

    // Observe Cumulative Layout Shift
    let clsValue = 0
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        const layoutShift = entry as unknown as {
          hadRecentInput?: boolean
          value?: number
        }
        if (!layoutShift.hadRecentInput && layoutShift.value) {
          clsValue += layoutShift.value
        }
      })
      callback({
        name: 'CLS',
        value: clsValue,
        rating: clsValue < 0.1 ? 'good' : 'needs-improvement',
      })
    })
    clsObserver.observe({ entryTypes: ['layout-shift'] })
  } catch (error) {
    // Silently fail if observer is not supported
  }
}
