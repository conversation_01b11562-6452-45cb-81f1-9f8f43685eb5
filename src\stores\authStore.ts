import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { LoginSuccessResult, LoginSuccessResultAlt } from '@/types'
import type {
  AuthState,
  User,
  CachedUserInfo,
  UserInfoCache,
} from './authStore/types'
import {
  CACHE_VERSION,
  USER_INFO_CACHE_TTL,
  SESSION_TIMEOUT,
  initialState,
} from './authStore/constants'
import { setToken, clearToken, updateTokenInClient } from '@/utils/tokenManager'

export type {
  User,
  CachedUserInfo,
  UserInfoCache,
  AuthState,
} from './authStore/types'

export const useAuthStore = create<AuthState>()(
  // Disable devtools to reduce console noise
  // devtools(
  persist(
    (set, get) => ({
      ...initialState,

      setAuth: async (
        data: LoginSuccessResult | LoginSuccessResultAlt,
        originalEmail?: string
      ) => {
        let user: User
        let token: string
        let refreshToken: string

        // Handle different response formats
        if ('UserData' in data) {
          // Alternative format (used in tests)
          user = {
            email: data.UserData.Email,
            name: data.UserData.Name,
          }
          token = data.UserToken
          refreshToken = data.RefreshToken
        } else {
          // Standard OAuth format
          // Note: Dr. Muscle API sometimes doesn't return userName field
          // In that case, use the original email from login credentials
          const email = data.userName || originalEmail
          if (!email) {
            throw new Error(
              'No email available from login response or credentials'
            )
          }

          user = {
            email,
          }
          token = data.access_token
          // Dr. Muscle API doesn't return a separate refresh token
          // The access token is used for both access and refresh
          refreshToken = data.access_token
        }

        // Set the Authorization header in the API client
        // This is critical for Dr. Muscle API authentication
        setToken(token)
        updateTokenInClient(token)

        // Exchange tokens for httpOnly cookies
        try {
          const response = await fetch('/api/auth/exchange', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ token, refreshToken }),
            credentials: 'include', // Ensure cookies are sent/received
          })

          if (!response.ok) {
            console.warn(
              '[Auth] Failed to exchange tokens for cookies, continuing with login'
            )
          }
        } catch (error) {
          // Continue to set state even if cookie exchange fails
          console.warn(
            '[Auth] Cookie exchange failed, continuing with login:',
            error
          )
        }

        set({
          user,
          token,
          refreshToken,
          isAuthenticated: true,
          error: null,
          lastActivity: Date.now(),
        })
      },

      logout: async () => {
        // Clear Authorization header from API client
        clearToken()
        updateTokenInClient(null)

        // Clear auth cookies
        try {
          await fetch('/api/auth/exchange', {
            method: 'DELETE',
            credentials: 'include', // Ensure cookies are sent/received
          })
        } catch (error) {
          // Continue even if cookie clear fails
          console.warn('[Auth] Failed to clear cookies during logout:', error)
        }

        // Clear unified program data cache to prevent stale data on next login
        // Note: Cache will be cleared on next app load via cache invalidation

        set({
          ...initialState,
          hasHydrated: true, // Keep hydrated state
          cacheVersion: CACHE_VERSION, // Keep current cache version
        })
      },

      updateTokens: async (token: string, refreshToken: string) => {
        // Update Authorization header in API client
        setToken(token)
        updateTokenInClient(token)

        set({
          token,
          refreshToken,
        })
      },

      updateUser: (userData: Partial<User>) => {
        set((state) => ({
          user: state.user ? { ...state.user, ...userData } : null,
        }))
      },

      setUser: (user: User) => {
        set({ user })
      },

      setError: (error: string) => {
        set({
          error,
          isLoading: false,
        })
      },

      clearError: () => {
        set({ error: null })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      setHasHydrated: (hasHydrated: boolean) => {
        set({ hasHydrated })
      },

      updateLastActivity: () => {
        set({ lastActivity: Date.now() })
      },

      checkSessionTimeout: () => {
        const state = get()
        if (!state.isAuthenticated) return

        const now = Date.now()
        const timeSinceLastActivity = now - state.lastActivity

        if (timeSinceLastActivity > SESSION_TIMEOUT) {
          // Session expired due to inactivity
          state.logout()
        }
      },

      // Cache Actions
      setCachedUserInfo: (data: CachedUserInfo) => {
        const cache: UserInfoCache = {
          data,
          timestamp: Date.now(),
          version: CACHE_VERSION,
        }
        set({ cachedUserInfo: cache })
      },

      getCachedUserInfo: () => {
        const state = get()

        // Check cache version
        if (state.cachedUserInfo?.version !== CACHE_VERSION) {
          return null
        }

        // Check if cache is stale
        if (state.isCacheStale()) {
          return null
        }

        const result = state.cachedUserInfo?.data || null

        return result
      },

      isCacheStale: () => {
        const state = get()

        if (!state.cachedUserInfo) {
          return true
        }

        const now = Date.now()
        const cacheAge = now - state.cachedUserInfo.timestamp

        return cacheAge > USER_INFO_CACHE_TTL
      },

      clearUserInfoCache: () => {
        set({ cachedUserInfo: null })
      },
    }),
    {
      name: 'drmuscle-auth',
      partialize: (state) => ({
        user: state.user,
        // Do not persist tokens to localStorage for security
        // Tokens are stored in httpOnly cookies instead
        isAuthenticated: state.isAuthenticated,
        cachedUserInfo: state.cachedUserInfo,
        cacheVersion: state.cacheVersion,
      }),
      onRehydrateStorage: () => (state) => {
        state?.setHasHydrated(true)

        // If we have a persisted token, set it in the API client
        if (state?.token) {
          setToken(state.token)
          updateTokenInClient(state.token)
        }
      },
    }
  )
  // )  // Commented out devtools closing
)
