import type { QueueItem, QueueStats } from './queueTypes'
import { RetryManager } from './retryManager'
import { QueueItemManager } from './queueItemManager'

export class RequestQueue {
  private static instance: RequestQueue

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private queue: QueueItem<any>[] = []

  private processing = false

  private concurrentRequests = 3

  private activeRequests = 0

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private requestMap = new Map<string, QueueItem<any>>()

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {}

  static getInstance(): RequestQueue {
    if (!RequestQueue.instance) {
      RequestQueue.instance = new RequestQueue()
    }
    return RequestQueue.instance
  }

  /**
   * Add a request to the queue
   * @param request Function that returns a promise
   * @param priority Higher priority executes first (default 0)
   * @param key Optional key for deduplication
   */
  async add<T>(
    request: () => Promise<T>,
    priority = 0,
    key?: string
  ): Promise<T> {
    // Check for duplicate request
    if (key && this.requestMap.has(key)) {
      const existing = this.requestMap.get(key)!
      return new Promise((resolve, reject) => {
        // Piggyback on existing request
        const originalResolve = existing.resolve
        const originalReject = existing.reject
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        existing.resolve = (value: any) => {
          originalResolve(value)
          resolve(value)
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        existing.reject = (error: any) => {
          originalReject(error)
          reject(error)
        }
      })
    }

    return new Promise((resolve, reject) => {
      const item = QueueItemManager.createItem(
        request,
        priority,
        key,
        resolve,
        reject
      )

      if (key) {
        this.requestMap.set(key, item)
      }

      // Insert based on priority (higher priority first)
      QueueItemManager.insertByPriority(this.queue, item)

      this.process()
    })
  }

  /**
   * Set the maximum number of concurrent requests
   */
  setConcurrency(limit: number): void {
    this.concurrentRequests = Math.max(1, limit)
    this.process()
  }

  /**
   * Clear all pending requests
   */
  clear(): void {
    // Abort all pending requests
    this.queue.forEach((item) => {
      item.abortController.abort()
      item.reject(new Error('Request queue cleared'))
    })

    this.queue = []
    this.requestMap.clear()
  }

  /**
   * Cancel a specific request by key
   */
  cancel(key: string): boolean {
    const item = this.requestMap.get(key)
    if (!item) return false

    // Remove from queue
    const index = this.queue.findIndex((q) => q.id === key)
    if (index !== -1) {
      this.queue.splice(index, 1)
    }

    // Abort the request
    item.abortController.abort()
    item.reject(new Error('Request cancelled'))

    this.requestMap.delete(key)
    return true
  }

  /**
   * Get queue statistics
   */
  getStats(): QueueStats {
    return {
      pending: this.queue.length,
      active: this.activeRequests,
      total: this.queue.length + this.activeRequests,
      concurrency: this.concurrentRequests,
    }
  }

  /**
   * Process the queue
   */
  private async process(): Promise<void> {
    if (this.processing) return
    this.processing = true

    while (
      this.queue.length > 0 &&
      this.activeRequests < this.concurrentRequests
    ) {
      const item = this.queue.shift()!
      this.activeRequests++

      // Execute request
      this.executeRequest(item)
    }

    this.processing = false
  }

  /**
   * Execute a single request with retry logic
   */
  private async executeRequest<T>(item: QueueItem<T>): Promise<void> {
    const maxRetries = 3
    const baseDelay = 1000

    try {
      // Check if aborted before starting
      if (item.abortController.signal.aborted) {
        throw new Error('Request aborted')
      }

      const result = await RetryManager.withAbort(
        item.request(),
        item.abortController.signal
      )
      item.resolve(result)

      // Clean up
      if (item.id && this.requestMap.has(item.id)) {
        this.requestMap.delete(item.id)
      }
    } catch (error) {
      // Check if it's a retryable error
      if (
        item.retryCount! < maxRetries &&
        !item.abortController.signal.aborted &&
        RetryManager.isRetryableError(error)
      ) {
        item.retryCount!++

        // Calculate delay with exponential backoff and jitter
        const delay = RetryManager.calculateBackoff(item.retryCount!, baseDelay)

        // Re-queue with same priority after delay
        setTimeout(() => {
          // Insert at front of same priority group
          QueueItemManager.insertByPriority(this.queue, item)
          this.process()
        }, delay)
      } else {
        // Final failure
        item.reject(error)

        // Clean up
        if (item.id && this.requestMap.has(item.id)) {
          this.requestMap.delete(item.id)
        }
      }
    } finally {
      this.activeRequests--
      // Process next item
      setTimeout(() => this.process(), 0)
    }
  }
}

// Export singleton instance
export const requestQueue = RequestQueue.getInstance()
