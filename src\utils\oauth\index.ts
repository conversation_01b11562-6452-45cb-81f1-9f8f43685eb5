/**
 * OAuth utilities export
 *
 * Central export point for all OAuth-related functionality
 */

// Main OAuth integration layer
export { OAuthIntegration, oauthIntegration } from './oauthIntegration'

// Provider-specific helpers
export { GoogleOAuthHelper } from './googleOAuth'
export { AppleOAuthHelper } from './appleOAuth'
export { FirebaseOAuthHelper } from './firebaseOAuth'

// New modular components
export { OAuthErrorHandler, ErrorCodes } from './errorHandler'
export { OAuthPerformanceTracker } from './performanceTracker'
export { OAuthValidator } from './validator'
export { OAuthProviderHandlers } from './providerHandlers'

// Re-export OAuth types for convenience
export type {
  OAuthProvider,
  OAuthUserData,
  OAuthError,
  OAuthSuccessCallback,
  OAuthErrorCallback,
  OAuthLoginRequest,
  OAuthLoginResponse,
  OAuthConfig,
  OAuthButtonConfig,
  OAuthState,
  UseOAuthReturn,
  GoogleCredentialResponse,
  GoogleTokenPayload,
  AppleSignInResponse,
  AppleTokenPayload,
} from '../../types/oauth'
