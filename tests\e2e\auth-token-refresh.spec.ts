import { test, expect, Page } from '@playwright/test'

const TEST_USER = {
  email: '<EMAIL>',
  password: 'Dr123456',
}

test.describe('Token Refresh Mechanism', () => {
  let page: Page

  test.beforeEach(async ({ page: p }) => {
    page = p
    await page.goto('/')
  })

  test('should automatically refresh expired token', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Mock token expiration
    await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        // Set token to expire in 5 seconds
        parsed.state.tokenExpiry = Date.now() + 5000
        localStorage.setItem('auth-storage', JSON.stringify(parsed))
      }
    })

    // Intercept refresh token request
    let refreshCalled = false
    await page.route('**/api/token/refresh', (route) => {
      refreshCalled = true
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          token: 'new-access-token',
          refreshToken: 'new-refresh-token',
          expiresIn: 3600,
        }),
      })
    })

    // Wait for token to expire
    await page.waitForTimeout(6000)

    // Make an API call that requires auth
    await page.goto('/workout')

    // Should have refreshed token
    expect(refreshCalled).toBe(true)
    await expect(page).toHaveURL('/workout')

    // Verify new token is stored
    const newToken = await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      return authState ? JSON.parse(authState).state.token : null
    })
    expect(newToken).toBe('new-access-token')
  })

  test('should refresh token before expiration', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Set token to expire in 10 minutes (should trigger pre-emptive refresh)
    await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        parsed.state.tokenExpiry = Date.now() + 10 * 60 * 1000 // 10 minutes
        localStorage.setItem('auth-storage', JSON.stringify(parsed))
      }
    })

    let refreshCalled = false
    await page.route('**/api/token/refresh', (route) => {
      refreshCalled = true
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          token: 'refreshed-token',
          refreshToken: 'new-refresh-token',
          expiresIn: 3600,
        }),
      })
    })

    // Navigate to trigger background refresh
    await page.goto('/workout')

    // Should trigger refresh within 5 seconds
    await page.waitForTimeout(5000)
    expect(refreshCalled).toBe(true)
  })

  test('should handle refresh token failure', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Expire token immediately
    await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        parsed.state.tokenExpiry = Date.now() - 1000
        localStorage.setItem('auth-storage', JSON.stringify(parsed))
      }
    })

    // Make refresh fail
    await page.route('**/api/token/refresh', (route) => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Refresh token expired' }),
      })
    })

    // Try to access protected route
    await page.goto('/workout')

    // Should redirect to login
    await expect(page).toHaveURL('/login')
    await expect(
      page.locator('text=Session expired. Please sign in again.')
    ).toBeVisible()
  })

  test('should queue requests during token refresh', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Expire token
    await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        parsed.state.tokenExpiry = Date.now() - 1000
        localStorage.setItem('auth-storage', JSON.stringify(parsed))
      }
    })

    const apiCalls: string[] = []
    await page.route('**/api/**', (route) => {
      apiCalls.push(route.request().url())
      if (route.request().url().includes('/token/refresh')) {
        // Delay refresh response
        setTimeout(() => {
          route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({
              token: 'new-token',
              refreshToken: 'new-refresh',
              expiresIn: 3600,
            }),
          })
        }, 2000)
      } else {
        route.continue()
      }
    })

    // Make multiple API calls simultaneously
    await Promise.all([
      page.evaluate(() => fetch('/api/workouts')),
      page.evaluate(() => fetch('/api/userStats')),
      page.evaluate(() => fetch('/api/program')),
    ])

    // Should see refresh called first
    expect(apiCalls[0]).toContain('/token/refresh')

    // Other calls should be made after refresh
    expect(apiCalls.length).toBeGreaterThanOrEqual(4)
  })

  test('should handle concurrent refresh attempts', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Expire token
    await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        parsed.state.tokenExpiry = Date.now() - 1000
        localStorage.setItem('auth-storage', JSON.stringify(parsed))
      }
    })

    let refreshCount = 0
    await page.route('**/api/token/refresh', (route) => {
      refreshCount++
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          token: `token-${refreshCount}`,
          refreshToken: 'refresh-token',
          expiresIn: 3600,
        }),
      })
    })

    // Trigger multiple components trying to refresh simultaneously
    await page.evaluate(() => {
      // Simulate multiple components checking auth
      for (let i = 0; i < 5; i++) {
        fetch('/api/userStats', {
          headers: { Authorization: 'Bearer expired-token' },
        })
      }
    })

    await page.waitForTimeout(2000)

    // Should only refresh once
    expect(refreshCount).toBe(1)
  })

  test('should handle refresh token rotation', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    const refreshTokens: string[] = []

    // Capture initial refresh token
    const initialRefreshToken = await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      return authState ? JSON.parse(authState).state.refreshToken : null
    })
    refreshTokens.push(initialRefreshToken)

    // Setup refresh endpoint to rotate tokens
    await page.route('**/api/token/refresh', (route) => {
      const newRefreshToken = `refresh-${Date.now()}`
      refreshTokens.push(newRefreshToken)
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          token: 'new-access-token',
          refreshToken: newRefreshToken,
          expiresIn: 3600,
        }),
      })
    })

    // Trigger refresh
    await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        parsed.state.tokenExpiry = Date.now() - 1000
        localStorage.setItem('auth-storage', JSON.stringify(parsed))
      }
    })

    await page.goto('/workout')
    await page.waitForTimeout(1000)

    // Verify refresh token was rotated
    const currentRefreshToken = await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      return authState ? JSON.parse(authState).state.refreshToken : null
    })

    expect(currentRefreshToken).not.toBe(initialRefreshToken)
    expect(refreshTokens).toContain(currentRefreshToken)
  })

  test('should clear auth on invalid refresh token', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Setup refresh to fail with invalid token
    await page.route('**/api/token/refresh', (route) => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Invalid refresh token',
          code: 'INVALID_REFRESH_TOKEN',
        }),
      })
    })

    // Expire access token
    await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        parsed.state.tokenExpiry = Date.now() - 1000
        localStorage.setItem('auth-storage', JSON.stringify(parsed))
      }
    })

    // Try to access protected route
    await page.goto('/workout')

    // Should clear auth and redirect to login
    await expect(page).toHaveURL('/login')

    const authState = await page.evaluate(() =>
      localStorage.getItem('auth-storage')
    )
    expect(authState).toBeNull()
  })

  test('should handle refresh during OAuth sign in', async () => {
    // Start with an expired session
    await page.evaluate(() => {
      const expiredAuth = {
        state: {
          token: 'expired-token',
          refreshToken: 'old-refresh',
          tokenExpiry: Date.now() - 1000,
          user: { id: 'old-user' },
        },
      }
      localStorage.setItem('auth-storage', JSON.stringify(expiredAuth))
    })

    await page.goto('/login')

    // Setup OAuth response
    await page.route('**/api/oauth/**', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          token: 'oauth-token',
          refreshToken: 'oauth-refresh',
          user: { id: 'oauth-user', email: '<EMAIL>' },
        }),
      })
    })

    // Simulate OAuth callback
    await page.evaluate(() => {
      window.postMessage(
        {
          type: 'oauth-success',
          provider: 'google',
          data: { userId: 'google-123', email: '<EMAIL>' },
        },
        '*'
      )
    })

    await page.waitForURL('/program')

    // Verify old session was replaced
    const newAuth = await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      return authState ? JSON.parse(authState).state : null
    })

    expect(newAuth.token).toBe('oauth-token')
    expect(newAuth.user.id).toBe('oauth-user')
  })

  test('should handle token refresh with clock skew', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Simulate clock skew - server time is 5 minutes ahead
    await page.route('**/api/token/refresh', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          token: 'new-token',
          refreshToken: 'new-refresh',
          expiresIn: 3600,
          serverTime: Date.now() + 5 * 60 * 1000, // 5 minutes ahead
        }),
      })
    })

    // Trigger refresh
    await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        parsed.state.tokenExpiry = Date.now() + 30 * 60 * 1000 // 30 minutes
        localStorage.setItem('auth-storage', JSON.stringify(parsed))
      }
    })

    await page.goto('/workout')
    await page.waitForTimeout(2000)

    // Verify token expiry was adjusted for clock skew
    const adjustedExpiry = await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        return parsed.state.tokenExpiry
      }
      return null
    })

    // Should have adjusted for server time difference
    expect(adjustedExpiry).toBeLessThan(Date.now() + 60 * 60 * 1000)
  })
})
