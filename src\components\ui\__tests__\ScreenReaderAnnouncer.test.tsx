import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, act, renderHook } from '@testing-library/react'
import {
  ScreenReaderAnnouncer,
  useScreenReaderAnnouncer,
} from '../ScreenReaderAnnouncer'

describe('ScreenReaderAnnouncer', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.runOnlyPendingTimers()
    vi.useRealTimers()
  })

  describe('Component', () => {
    it('should render with correct ARIA attributes', () => {
      render(<ScreenReaderAnnouncer message="Test announcement" />)

      const announcer = screen.getByRole('status')
      expect(announcer).toHaveAttribute('aria-live', 'polite')
      expect(announcer).toHaveAttribute('aria-atomic', 'true')
      expect(announcer).toHaveClass('sr-only')
      expect(announcer).toHaveTextContent('Test announcement')
    })

    it('should use assertive priority when specified', () => {
      render(
        <ScreenReaderAnnouncer message="Urgent message" priority="assertive" />
      )

      const announcer = screen.getByRole('status')
      expect(announcer).toHaveAttribute('aria-live', 'assertive')
    })

    it('should clear message after default delay', () => {
      render(<ScreenReaderAnnouncer message="Temporary message" />)

      const announcer = screen.getByRole('status')
      expect(announcer).toHaveTextContent('Temporary message')

      act(() => {
        vi.advanceTimersByTime(5000)
      })

      expect(announcer).toHaveTextContent('')
    })

    it('should clear message after custom delay', () => {
      render(
        <ScreenReaderAnnouncer message="Quick message" clearAfter={1000} />
      )

      const announcer = screen.getByRole('status')
      expect(announcer).toHaveTextContent('Quick message')

      act(() => {
        vi.advanceTimersByTime(1000)
      })

      expect(announcer).toHaveTextContent('')
    })

    it('should not clear message when clearAfter is 0', () => {
      render(
        <ScreenReaderAnnouncer message="Persistent message" clearAfter={0} />
      )

      const announcer = screen.getByRole('status')
      expect(announcer).toHaveTextContent('Persistent message')

      act(() => {
        vi.advanceTimersByTime(10000)
      })

      expect(announcer).toHaveTextContent('Persistent message')
    })

    it('should update message when prop changes', () => {
      const { rerender } = render(
        <ScreenReaderAnnouncer message="First message" />
      )

      const announcer = screen.getByRole('status')
      expect(announcer).toHaveTextContent('First message')

      rerender(<ScreenReaderAnnouncer message="Second message" />)

      expect(announcer).toHaveTextContent('Second message')
    })

    it('should cancel previous timeout when message changes', () => {
      const { rerender } = render(
        <ScreenReaderAnnouncer message="First message" clearAfter={2000} />
      )

      const announcer = screen.getByRole('status')

      act(() => {
        vi.advanceTimersByTime(1000)
      })

      rerender(
        <ScreenReaderAnnouncer message="Second message" clearAfter={2000} />
      )

      act(() => {
        vi.advanceTimersByTime(1500)
      })

      // Should still show second message
      expect(announcer).toHaveTextContent('Second message')

      act(() => {
        vi.advanceTimersByTime(500)
      })

      // Now it should be cleared
      expect(announcer).toHaveTextContent('')
    })

    it('should not render when enabled is false', () => {
      render(<ScreenReaderAnnouncer message="Hidden message" enabled={false} />)

      expect(screen.queryByRole('status')).not.toBeInTheDocument()
    })

    it('should handle empty message', () => {
      render(<ScreenReaderAnnouncer message="" />)

      const announcer = screen.getByRole('status')
      expect(announcer).toHaveTextContent('')
    })
  })

  describe('useScreenReaderAnnouncer hook', () => {
    it('should provide announce function', () => {
      const { result } = renderHook(() => useScreenReaderAnnouncer())

      expect(result.current.announce).toBeDefined()
      expect(typeof result.current.announce).toBe('function')
    })

    it('should update announcement when announce is called', () => {
      const { result } = renderHook(() => useScreenReaderAnnouncer())

      expect(result.current.announcement).toBe('')

      act(() => {
        result.current.announce('New announcement')
      })

      expect(result.current.announcement).toBe('New announcement')
    })

    it('should clear announcement after default delay', () => {
      const { result } = renderHook(() => useScreenReaderAnnouncer())

      act(() => {
        result.current.announce('Temporary')
      })

      expect(result.current.announcement).toBe('Temporary')

      act(() => {
        vi.advanceTimersByTime(5000)
      })

      expect(result.current.announcement).toBe('')
    })

    it('should clear announcement after custom delay', () => {
      const { result } = renderHook(() => useScreenReaderAnnouncer())

      act(() => {
        result.current.announce('Quick', { clearAfter: 1000 })
      })

      expect(result.current.announcement).toBe('Quick')

      act(() => {
        vi.advanceTimersByTime(1000)
      })

      expect(result.current.announcement).toBe('')
    })

    it('should not clear when clearAfter is 0', () => {
      const { result } = renderHook(() => useScreenReaderAnnouncer())

      act(() => {
        result.current.announce('Persistent', { clearAfter: 0 })
      })

      act(() => {
        vi.advanceTimersByTime(10000)
      })

      expect(result.current.announcement).toBe('Persistent')
    })

    it('should cancel previous timeout when announcing new message', () => {
      const { result } = renderHook(() => useScreenReaderAnnouncer())

      act(() => {
        result.current.announce('First', { clearAfter: 2000 })
      })

      act(() => {
        vi.advanceTimersByTime(1000)
      })

      act(() => {
        result.current.announce('Second', { clearAfter: 2000 })
      })

      act(() => {
        vi.advanceTimersByTime(1500)
      })

      expect(result.current.announcement).toBe('Second')
    })

    it('should clean up timeout on unmount', () => {
      const { result, unmount } = renderHook(() => useScreenReaderAnnouncer())

      act(() => {
        result.current.announce('Message')
      })

      unmount()

      // Should not throw or cause issues
      act(() => {
        vi.advanceTimersByTime(5000)
      })
    })
  })

  describe('Integration', () => {
    it('should work with hook and component together', () => {
      function TestComponent() {
        const { announcement, announce } = useScreenReaderAnnouncer()

        return (
          <>
            <button onClick={() => announce('Button clicked')}>Click me</button>
            <ScreenReaderAnnouncer message={announcement} />
          </>
        )
      }

      render(<TestComponent />)

      const button = screen.getByRole('button')
      const announcer = screen.getByRole('status')

      expect(announcer).toHaveTextContent('')

      act(() => {
        button.click()
      })

      expect(announcer).toHaveTextContent('Button clicked')

      act(() => {
        vi.advanceTimersByTime(5000)
      })

      expect(announcer).toHaveTextContent('')
    })
  })
})
