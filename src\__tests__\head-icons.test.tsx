import { describe, it, expect } from 'vitest'

// Mock the layout to avoid CSS import issues in tests
const metadata = {
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/icons/icon-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/icons/icon-512x512.png', sizes: '512x512', type: 'image/png' },
    ],
    apple: '/apple-touch-icon.png',
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Dr. Muscle X',
  },
  manifest: '/manifest.json',
}

describe('Document Head Icons', () => {
  it('should have apple-touch-icon configured', () => {
    expect(metadata.icons?.apple).toBe('/apple-touch-icon.png')
  })

  it('should have favicon icons configured', () => {
    const icons = metadata.icons?.icon
    expect(Array.isArray(icons)).toBe(true)

    if (Array.isArray(icons)) {
      // Check for 16x16 favicon
      expect(icons).toContainEqual({
        url: '/favicon-16x16.png',
        sizes: '16x16',
        type: 'image/png',
      })

      // Check for 32x32 favicon
      expect(icons).toContainEqual({
        url: '/favicon-32x32.png',
        sizes: '32x32',
        type: 'image/png',
      })

      // Check for larger PWA icons
      expect(icons).toContainEqual({
        url: '/icons/icon-192x192.png',
        sizes: '192x192',
        type: 'image/png',
      })

      expect(icons).toContainEqual({
        url: '/icons/icon-512x512.png',
        sizes: '512x512',
        type: 'image/png',
      })
    }
  })

  it('should have PWA app configuration', () => {
    expect(metadata.appleWebApp).toEqual({
      capable: true,
      statusBarStyle: 'default',
      title: 'Dr. Muscle X',
    })
  })

  it('should have manifest linked', () => {
    expect(metadata.manifest).toBe('/manifest.json')
  })
})
