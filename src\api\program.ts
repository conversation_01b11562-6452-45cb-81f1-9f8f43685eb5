import type { ProgramModel, ProgramProgress, ProgramStats } from '@/types'
import { categorizeError, retryWithBackoff } from '@/lib/errorUtils'
import { retryApiCall, STATS_RETRY_OPTIONS } from '@/utils/apiRetry'
import { userInfoPerformance } from '@/utils/userInfoPerformance'
import { logger } from '@/utils/logger'
import {
  fetchUnifiedProgramData,
  clearUnifiedProgramDataCache,
} from './unifiedProgramData'
import {
  getProgramDescription,
  getProgramCategory,
  calculateStartDate,
  calculateTotalVolume,
} from './programHelpers'

// Re-export the clear cache function for backward compatibility
export { clearUnifiedProgramDataCache as clearProgramDataCache }

/**
 * Program API methods
 * Extracts and transforms program-specific data from existing endpoints
 */
export const programApi = {
  /**
   * Get the current user's program information
   * Transforms data from the existing getUserProgramInfo endpoint
   */
  async getUserProgram(): Promise<ProgramModel | null> {
    return retryWithBackoff(
      async () => {
        const data = await fetchUnifiedProgramData()
        if (!data) {
          return null
        }

        // Debug logging in development only
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          logger.log('[Program API] V2 API data:', {
            WorkoutCount: data.WorkoutCount,
            TotalWorkoutCompleted: data.TotalWorkoutCompleted,
            LastWorkoutDateStr: data.LastWorkoutDateStr,
            ConsecutiveWeeks: data.ConsecutiveWeeks,
            ConsecutiveWeeksModel: data.ConsecutiveWeeksModel,
            FullResponse: data,
          })
        }

        const programInfo = data.GetUserProgramInfoResponseModel

        if (!programInfo?.RecommendedProgram) {
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            logger.error('[Program API] No program info:', programInfo)
          }
          return null
        }

        const { RecommendedProgram } = programInfo

        // Use WorkoutCount from V2 API, fallback to TotalWorkoutCompleted for compatibility
        const completed = data.WorkoutCount || data.TotalWorkoutCompleted || 0

        const remaining = RecommendedProgram.RemainingToLevelUp || 0
        const totalWorkouts = completed + remaining

        // Get exercise count from next workout
        const exerciseCount = programInfo.NextWorkoutTemplate?.Exercices?.length

        // Transform API response to ProgramModel
        return {
          id: RecommendedProgram.Id,
          name: RecommendedProgram.Label,
          description: getProgramDescription(RecommendedProgram.Label),
          category: getProgramCategory(RecommendedProgram.Label),
          totalDays: 90, // Default program length
          currentDay: completed,
          workoutsCompleted: completed,
          startDate: calculateStartDate(completed, data.LastWorkoutDateStr),
          totalWorkouts,
          imageUrl: RecommendedProgram.IconUrl,
          nextWorkoutExerciseCount: exerciseCount,
        }
      },
      {
        maxRetries: 3,
        initialDelay: 1000,
        maxDelay: 5000,
        backoffMultiplier: 2,
      },
      (attempt, error) => {
        logger.warn(
          `Retry attempt ${attempt} for getUserProgram:`,
          error.message
        )
      }
    ).catch((error) => {
      logger.error('Failed to get user program after retries:', error)
      throw categorizeError(error)
    })
  },

  /**
   * Get program progress for the current user
   * Calculates progress based on workout history
   */
  async getProgramProgress(): Promise<ProgramProgress | null> {
    return retryWithBackoff(
      async () => {
        const data = await fetchUnifiedProgramData()
        if (!data) {
          return null
        }
        const programInfo = data.GetUserProgramInfoResponseModel

        if (!programInfo?.RecommendedProgram) {
          return null
        }

        // Use WorkoutCount from V2 API, fallback to TotalWorkoutCompleted for compatibility
        const completed = data.WorkoutCount || data.TotalWorkoutCompleted || 0

        const remaining = programInfo.RecommendedProgram.RemainingToLevelUp || 0
        const totalWorkouts = completed + remaining

        // Calculate current week (assuming 3 workouts per week)
        const currentWeek = Math.ceil(completed / 3) || 1
        const workoutsThisWeek = completed % 3 || 3

        return {
          percentage:
            totalWorkouts > 0
              ? Math.round((completed / totalWorkouts) * 100)
              : 0,
          daysCompleted: completed,
          totalWorkouts,
          currentWeek,
          workoutsThisWeek,
          remainingWorkouts: remaining,
        }
      },
      { maxRetries: 2, initialDelay: 500, maxDelay: 3000, backoffMultiplier: 2 }
    ).catch((error) => {
      logger.error('Failed to get program progress:', error)
      throw categorizeError(error)
    })
  },

  /**
   * Get program statistics for the current user
   * Aggregates workout history data
   */
  async getProgramStats(): Promise<ProgramStats> {
    // Start performance timing
    userInfoPerformance.trackStatsFetch()

    try {
      const result = await retryApiCall(
        async () => {
          const data = await fetchUnifiedProgramData()
          if (!data) {
            throw new Error('Failed to fetch program data')
          }

          // Debug logging to understand the data structure
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            logger.log('[Program API] Stats V2 data:', {
              WorkoutCount: data.WorkoutCount,
              TotalWorkoutCompleted: data.TotalWorkoutCompleted,
              ConsecutiveWeeks: data.ConsecutiveWeeks,
              ConsecutiveWeeksModel: data.ConsecutiveWeeksModel,
              LastWorkoutDateStr: data.LastWorkoutDateStr,
              NewRecords: data.NewRecords?.length,
              AvgWorkoutTime: data.AvgWorkoutTime,
              Sets: data.Sets?.length,
              Averages: data.Averages,
              HistoryExerciseModel: data.HistoryExerciseModel,
              AllTimeHistoryExerciseModel: data.AllTimeHistoryExerciseModel,
              FullStatsResponse: data,
            })
          }

          // Calculate average workout time in minutes
          let avgTime = 0
          if (data.AvgWorkoutTime) {
            const time = data.AvgWorkoutTime
            avgTime =
              (time.hours || 0) * 60 +
              (time.minutes || 0) +
              (time.seconds || 0) / 60
          }

          // Calculate total volume from sets
          // Check various possible fields for total weight/volume
          let totalVolume = 0

          // First check HistoryExerciseModel.TotalWeight as backend suggests
          if (data.HistoryExerciseModel) {
            // HistoryExerciseModel might be an array or object
            const historyModel = Array.isArray(data.HistoryExerciseModel)
              ? data.HistoryExerciseModel[0]
              : data.HistoryExerciseModel

            if (historyModel && historyModel.TotalWeight) {
              totalVolume = Math.round(historyModel.TotalWeight.Lb)
              if (process.env.NODE_ENV === 'development') {
                // eslint-disable-next-line no-console
                logger.log(
                  '[Program API] Found volume in HistoryExerciseModel.TotalWeight:',
                  historyModel.TotalWeight
                )
              }
            }
          }

          // If not found, check if Averages contains total weight data
          if (
            totalVolume === 0 &&
            data.Averages &&
            typeof data.Averages === 'object'
          ) {
            const averages = data.Averages as Record<string, unknown>

            // Try different possible field names for total volume
            const possibleVolumeFields = [
              'TotalWeight',
              'TotalVolume',
              'TotalLbsLifted',
              'TotalWeightLifted',
              'TotalLb',
            ]

            // Use array method instead of for loop to satisfy ESLint
            possibleVolumeFields.some((field) => {
              if (averages[field] !== undefined && averages[field] !== null) {
                const value = Number(averages[field])
                if (!Number.isNaN(value) && value > 0) {
                  totalVolume = Math.round(value)
                  if (process.env.NODE_ENV === 'development') {
                    // eslint-disable-next-line no-console
                    logger.log(
                      `[Program API] Found volume in Averages field '${field}':`,
                      value
                    )
                  }
                  return true // Stop iteration
                }
              }
              return false // Continue iteration
            })
          }

          // If still no volume, try to calculate from sets
          if (totalVolume === 0 && data.Sets && data.Sets.length > 0) {
            totalVolume = calculateTotalVolume(data.Sets)
          }

          // Debug volume calculation
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            logger.log('[Program API] Volume calculation:', {
              HistoryExerciseModel: data.HistoryExerciseModel,
              Averages: data.Averages,
              AveragesType: typeof data.Averages,
              SetsCount: data.Sets?.length,
              CalculatedVolume: totalVolume,
            })
          }

          // Use WorkoutCount from V2 API
          const totalWorkoutsCompleted =
            data.WorkoutCount || data.TotalWorkoutCompleted || 0

          // ConsecutiveWeeks handling - check different possible formats
          let consecutiveWeeks = 0

          // First check if it's an array with objects containing ConsecutiveWeeks property
          if (
            Array.isArray(data.ConsecutiveWeeks) &&
            data.ConsecutiveWeeks.length > 0
          ) {
            const firstWeek = data.ConsecutiveWeeks[0]
            if (
              firstWeek &&
              typeof firstWeek === 'object' &&
              'ConsecutiveWeeks' in firstWeek
            ) {
              consecutiveWeeks = Number(firstWeek.ConsecutiveWeeks) || 0
            }
          }
          // Then check if it's a direct number
          else if (typeof data.ConsecutiveWeeks === 'number') {
            consecutiveWeeks = data.ConsecutiveWeeks
          }
          // Also check ConsecutiveWeeksModel if present
          else if (data.ConsecutiveWeeksModel?.ConsecutiveWeeks) {
            consecutiveWeeks = data.ConsecutiveWeeksModel.ConsecutiveWeeks
          }

          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            logger.log('[Program API] ConsecutiveWeeks extraction:', {
              rawConsecutiveWeeks: data.ConsecutiveWeeks,
              isArray: Array.isArray(data.ConsecutiveWeeks),
              consecutiveWeeksModel: data.ConsecutiveWeeksModel,
              extractedValue: consecutiveWeeks,
            })
          }

          // Calculate recovery days
          let recoveryDays = 0
          let coachRecommendation: 'Train' | 'Rest' = 'Train'

          if (data.LastWorkoutDateStr) {
            const lastWorkoutDate = new Date(data.LastWorkoutDateStr)
            const currentDate = new Date()
            const timeDiff = currentDate.getTime() - lastWorkoutDate.getTime()
            recoveryDays = Math.floor(timeDiff / (1000 * 60 * 60 * 24))

            // Coach recommends rest if less than 1 day recovery
            coachRecommendation = recoveryDays >= 1 ? 'Train' : 'Rest'
          }

          const statsResult = {
            averageWorkoutTime: Math.round(avgTime * 10) / 10, // Round to 1 decimal
            totalVolume,
            personalRecords: data.NewRecords?.length || 0,
            consecutiveWeeks:
              typeof consecutiveWeeks === 'number' ? consecutiveWeeks : 0,
            lastWorkoutDate: data.LastWorkoutDateStr,
            totalWorkoutsCompleted,
            recoveryDays,
            coachRecommendation,
            // Body weight would need to come from user profile - not available in this endpoint
            bodyWeight: undefined,
          }

          // Debug final stats result
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            logger.log('[Program API] Final stats result:', statsResult)
          }

          return statsResult
        }, // Close async arrow function
        STATS_RETRY_OPTIONS,
        10000 // 10 second timeout
      )

      // Track successful API call
      userInfoPerformance.trackStatsComplete()

      return result
    } catch (error) {
      // Track failed API call on retry
      // Note: Retry tracking happens inside trackStatsFetch when isRetry=true

      logger.error('Failed to get program stats:', error)
      // Return default stats on error (graceful degradation)
      return {
        averageWorkoutTime: 0,
        totalVolume: 0,
        personalRecords: 0,
        consecutiveWeeks: 0,
        totalWorkoutsCompleted: 0,
      }
    }
  },
}
