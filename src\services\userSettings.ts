/**
 * User Settings Service
 * Provides user preferences and settings for workout recommendations
 */

import { userProfileApi } from '@/api/userProfile'
import { logger } from '@/utils/logger'

export interface UserSettings {
  isQuickMode: boolean | null // Can be true/false/null like mobile app
  isStrengthPhase: boolean
  isFreePlan: boolean
  isFirstWorkoutOfStrengthPhase: boolean
  lightSessionDays: number | null
}

interface StrengthPhaseCalculation {
  isStrengthPhase: boolean
  isFirstStrengthPhase: boolean
}

/**
 * Calculate strength phase status based on mobile app logic
 */
function calculateStrengthPhase(
  programName: string = '',
  userAge: number = 30,
  remainingWorkouts: number = 50,
  totalWorkouts: number = 100
): StrengthPhaseCalculation {
  // Skip if bodyweight/bands program or strength phase disabled
  if (
    programName.toLowerCase().includes('bodyweight') ||
    programName.toLowerCase().includes('bands')
  ) {
    return { isStrengthPhase: false, isFirstStrengthPhase: false }
  }

  // Calculate days based on program type (following mobile app logic)
  let xDays = 3 // default
  if (
    programName.includes('Split') ||
    programName.includes('Upper') ||
    programName.includes('Lower')
  ) {
    if (userAge < 30) {
      xDays = 5
    } else if (userAge < 50) {
      xDays = 4
    } else {
      xDays = 3
    }
  } else if (programName.includes('full-body') || programName.includes('PL')) {
    if (userAge < 30) {
      xDays = 4
    } else if (userAge < 50) {
      xDays = 3
    } else {
      xDays = 2
    }
  } else if (programName.includes('PPL')) {
    xDays = 6
  }

  const requiredWorkoutsForStrength = xDays * 3
  const completedWorkouts = totalWorkouts - remainingWorkouts
  const strengthPhaseThreshold = totalWorkouts - requiredWorkoutsForStrength

  return {
    isStrengthPhase: completedWorkouts >= strengthPhaseThreshold,
    isFirstStrengthPhase: completedWorkouts === strengthPhaseThreshold,
  }
}

/**
 * Check if user is on free plan (following mobile app logic)
 */
function calculateIsFreePlan(): boolean {
  try {
    // Check for DailyReset in localStorage (mobile app pattern)
    const dailyReset = localStorage.getItem('DailyReset')
    return dailyReset ? parseInt(dailyReset) > 0 : false
  } catch {
    return false // Default to not free plan if localStorage unavailable
  }
}

/**
 * Calculate light session days based on last workout date
 */
function calculateLightSessionDays(): number | null {
  try {
    // Try to get last workout date from localStorage or API
    const lastWorkoutDate = localStorage.getItem('lastWorkoutDate')
    if (!lastWorkoutDate) return null

    const daysSince = Math.floor(
      (Date.now() - new Date(lastWorkoutDate).getTime()) / (1000 * 60 * 60 * 24)
    )

    // Only set light session if > 9 days (mobile app logic)
    return daysSince > 9 ? Math.min(daysSince, 50) : null
  } catch {
    return null
  }
}

/**
 * Get user settings for workout recommendations
 * This combines data from user profile API and calculated values
 */
export async function getUserSettings(): Promise<UserSettings> {
  try {
    // Try to get user profile data
    await userProfileApi.getUserInfo()
    // TODO: Use userInfo?.Result to get actual user preferences when available

    // Calculate strength phase (would need actual program data)
    const strengthPhase = calculateStrengthPhase()

    // Get user preferences from localStorage or defaults
    const quickModePreference = localStorage.getItem('quickMode')
    let isQuickMode: boolean | null = null
    if (quickModePreference === 'true') {
      isQuickMode = true
    } else if (quickModePreference === 'false') {
      isQuickMode = false
    } else {
      isQuickMode = null
    }

    const settings: UserSettings = {
      isQuickMode,
      isStrengthPhase: strengthPhase.isStrengthPhase,
      isFreePlan: calculateIsFreePlan(),
      isFirstWorkoutOfStrengthPhase: strengthPhase.isFirstStrengthPhase,
      lightSessionDays: calculateLightSessionDays(),
    }

    logger.debug('[UserSettings] Retrieved user settings:', settings)
    return settings
  } catch (error) {
    logger.error(
      '[UserSettings] Failed to get user settings, using defaults:',
      error
    )

    // Return safe defaults if API call fails
    return {
      isQuickMode: null, // null like mobile app
      isStrengthPhase: false,
      isFreePlan: false,
      isFirstWorkoutOfStrengthPhase: false,
      lightSessionDays: null,
    }
  }
}

/**
 * Get user settings synchronously from cache/localStorage
 * This is a fallback for when async settings aren't available
 */
export function getUserSettingsSync(): UserSettings {
  const quickModePreference = localStorage.getItem('quickMode')
  let isQuickMode: boolean | null = null
  if (quickModePreference === 'true') {
    isQuickMode = true
  } else if (quickModePreference === 'false') {
    isQuickMode = false
  } else {
    isQuickMode = null
  }

  return {
    isQuickMode,
    isStrengthPhase: false, // Conservative default
    isFreePlan: calculateIsFreePlan(),
    isFirstWorkoutOfStrengthPhase: false,
    lightSessionDays: calculateLightSessionDays(),
  }
}

/**
 * Determine if this is the first workout of a strength phase
 * This is a placeholder implementation that should be enhanced
 */
export function isFirstWorkoutOfStrengthPhase(): boolean {
  // TODO: Implement logic to determine if this is the first workout
  // of a strength phase based on:
  // - User's workout history
  // - Program progression
  // - Phase transitions

  // For now, return false
  return false
}
