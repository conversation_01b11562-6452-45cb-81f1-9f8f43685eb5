# Workout Loading Strategy Implementation Plan

## Overview

This document provides a detailed implementation plan for optimizing the workout loading strategy in the Dr. Muscle web app. It includes the current mobile app implementation as a reference and proposes enhancements for the web version.

## Current Mobile App Implementation

### 1. Data Loading Flow

#### Home Page (MainAIPage)

1. **Initial Load**: Calls `GetUserWorkoutProgramTimeZoneInfo` API
   - Returns basic program info and next workout template (without exercises)
   - Stores in `UserWorkoutContexts`

2. **Start Workout Click**:
   - Checks if exercises are loaded (`NextWorkoutTemplate.Exercises == null`)
   - If not loaded, calls `GetUserCustomizedCurrentWorkout(workoutId)`
   - Navigates to workout page

#### Workout Page (KenkoChooseYourWorkoutExercisePage)

1. **Page Load**:
   - Verifies exercises are loaded, fetches if missing
   - Calls `UpdateExerciseList()` to populate UI
   - Shows all exercises (collapsed state, no recommendations)

2. **Exercise Interaction**:
   - User taps exercise → `CellHeaderTapped()`
   - Checks if `exercise.RecoModel == null`
   - If null, calls `FetchReco()` to get recommendations
   - Loads sets, weights, reps based on recommendation

### 2. API Endpoints and Payloads

#### GetUserWorkoutProgramTimeZoneInfo

**Request:**

```json
{
  "timeZoneInfo": "America/New_York"
}
```

**Response:**

```json
{
  "GetUserProgramInfoResponseModel": {
    "NextWorkoutTemplate": {
      "Id": 12345,
      "Label": "Push Day",
      "IsSystemExercise": true,
      "Exercises": null // Not loaded initially
    },
    "RecommendedProgram": {
      "Id": 100,
      "Label": "Beginner Program"
    }
  },
  "LastWorkoutDate": "2025-01-30T10:00:00Z",
  "LastConsecutiveWorkoutDays": 5
}
```

#### GetUserCustomizedCurrentWorkout

**Request:**

```
GET /api/Workout/GetUserCustomizedCurrentWorkout/{workoutId}
```

**Response:**

```json
{
  "Id": 12345,
  "Label": "Push Day",
  "Exercises": [
    {
      "Id": 1001,
      "Label": "Bench Press",
      "BodyPartId": 2,
      "EquipmentId": 1,
      "IsBodyweight": false,
      "VideoUrl": "https://...",
      "RecoModel": null // Not loaded initially
    }
  ]
}
```

#### GetRecommendationNormalRIRForExercise

**Request:**

```json
{
  "Username": "<EMAIL>",
  "ExerciseId": 1001,
  "WorkoutId": 12345,
  "IsQuickMode": false,
  "LightSessionDays": null,
  "IsStrengthPhase": false
}
```

**Response:**

```json
{
  "Weight": { "Kg": 80, "Lb": 176 },
  "Reps": 8,
  "Sets": 3,
  "WarmUpSets": [{ "Weight": { "Kg": 40, "Lb": 88 }, "Reps": 10 }],
  "IsDeload": false,
  "FirstWorkSetWeight": { "Kg": 80, "Lb": 176 },
  "FirstWorkSetReps": 8
}
```

## Proposed Web App Implementation Strategy

### Phase 1: Replicate Mobile App Behavior

#### Goal

Achieve feature parity with the mobile app's current loading strategy.

#### Implementation Steps

```text
Step 1: Create workout data models and types
- Define TypeScript interfaces matching mobile app models
- Create Zustand store slices for workout state
- Set up API client methods for workout endpoints
```

```text
Step 2: Implement home page workout loading
- Add GetUserWorkoutProgramTimeZoneInfo API call on page load
- Store basic workout info in state
- Display next workout template on home page
```

```text
Step 3: Implement workout page with exercise list
- Create workout page component
- Add GetUserCustomizedCurrentWorkout API call
- Display exercise list (collapsed state)
- Implement exercise expansion on click
```

```text
Step 4: Implement on-demand recommendation loading
- Add GetRecommendationNormalRIRForExercise API integration
- Create FetchReco equivalent for web
- Update UI to show sets/weights/reps
- Cache recommendations in component state
```

### Phase 2: Optimize with Pre-loading Strategy

#### Goal

Enhance performance by pre-loading exercise recommendations intelligently.

#### Implementation Steps

```text
Step 5: Add first exercise pre-loading on home page
- After workout info loads, immediately fetch first exercise recommendation
- Store in cache with key: userId-exerciseId-workoutId
- Check cache expiry (2 days) before using cached data
```

```text
Step 6: Implement background pre-loading of all exercises
- After page fully renders, start background loading
- Use request queue with rate limiting
- Load exercises sequentially to avoid server overload
- Show exercises immediately, load recommendations silently
```

```text
Step 7: Add intelligent caching layer
- Implement IndexedDB or localStorage cache
- Cache key: `${userId}-${exerciseId}-${workoutId}`
- Cache expiry: 2 days or after workout completion
- Clear cache on different workout template ID
```

```text
Step 8: Implement loading state management
- Add loading states to exercise UI components
- Handle concurrent request management
- Implement request prioritization (user-clicked exercise first)
- Add retry logic with exponential backoff
```

### Phase 3: Enhanced Features

#### Goal

Add web-specific optimizations not present in mobile app.

#### Implementation Steps

```text
Step 9: Add request batching and optimization
- Batch API calls where possible
- Implement request deduplication
- Add request cancellation on navigation
- Monitor and log performance metrics
```

```text
Step 10: Implement offline support
- Use service worker for API response caching
- Enable offline workout viewing
- Queue data sync when back online
- Show offline indicators in UI
```

```text
Step 11: Add performance monitoring
- Track loading times for each phase
- Monitor cache hit rates
- Log failed requests and retry attempts
- Send telemetry to analytics
```

```text
Step 12: Final integration and testing
- Ensure all components work together
- Add comprehensive error handling
- Test edge cases (network failures, slow connections)
- Verify caching behavior
```

## Technical Implementation Details

### Cache Implementation

```typescript
interface CacheEntry {
  key: string // userId-exerciseId-workoutId
  data: RecommendationModel
  timestamp: number
  workoutTemplateId: number
}

const CACHE_EXPIRY_MS = 2 * 24 * 60 * 60 * 1000 // 2 days
```

### Request Queue Implementation

```typescript
class RequestQueue {
  private queue: Array<() => Promise<any>> = []
  private processing = false
  private concurrentRequests = 3

  async add(request: () => Promise<any>, priority = 0) {
    // Implementation details
  }
}
```

### Loading State Management

```typescript
interface ExerciseLoadingState {
  exerciseId: number
  isLoading: boolean
  error?: Error
  retryCount: number
  lastAttempt?: number
}
```

## Success Criteria

1. **Performance**: First exercise loads instantly from cache when available
2. **User Experience**: No blocking UI, smooth transitions
3. **Reliability**: Graceful handling of network failures
4. **Efficiency**: Minimal API calls through intelligent caching
5. **Compatibility**: Works across all target browsers

## Risk Mitigation

1. **Server Overload**: Rate limiting and request batching
2. **Cache Invalidation**: Clear rules for when to refresh data
3. **Memory Usage**: Limit cache size, implement LRU eviction
4. **Network Failures**: Comprehensive retry logic and offline support

## Rollout Strategy

1. **Phase 1**: Deploy basic implementation matching mobile app
2. **Phase 2**: A/B test pre-loading strategy with subset of users
3. **Phase 3**: Full rollout with monitoring and iteration

## Monitoring and Metrics

- Time to first exercise ready
- Cache hit rate
- API call volume
- Error rates and types
- User engagement metrics

## Implementation Prompts

This section contains a series of prompts for implementing the workout loading strategy in the Dr. Muscle web app. Each prompt builds on the previous one, ensuring incremental progress with proper testing at each stage.

### Context

The Dr. Muscle web app needs to implement an optimized workout loading strategy that improves upon the mobile app's current implementation. The mobile app loads workouts on-demand, but the web app will pre-load exercise recommendations for better performance.

### Prompt 1: Create TypeScript Types and Interfaces

```text
Create TypeScript types and interfaces for the workout loading system based on the Dr. Muscle API.

Create a new file `src/types/workout.ts` with the following types:
- WorkoutTemplateModel
- WorkoutTemplateGroupModel
- ExerciseModel (already exists but may need updates)
- RecommendationModel
- GetUserWorkoutProgramTimeZoneInfoResponse
- GetRecommendationForExerciseRequest

Use the following API response examples as reference:

GetUserWorkoutProgramTimeZoneInfoResponse:
{
  "GetUserProgramInfoResponseModel": {
    "NextWorkoutTemplate": {
      "Id": 12345,
      "Label": "Push Day",
      "IsSystemExercise": true,
      "Exercises": null
    },
    "RecommendedProgram": {
      "Id": 100,
      "Label": "Beginner Program"
    }
  },
  "LastWorkoutDate": "2025-01-30T10:00:00Z",
  "LastConsecutiveWorkoutDays": 5
}

RecommendationModel:
{
  "Weight": { "Kg": 80, "Lb": 176 },
  "Reps": 8,
  "Sets": 3,
  "WarmUpSets": [{ "Weight": { "Kg": 40, "Lb": 88 }, "Reps": 10 }],
  "IsDeload": false
}

Write comprehensive unit tests for type validation in `src/types/__tests__/workout.test.ts`.
```

### Prompt 2: Create Workout API Service

```text
Create API service methods for workout-related endpoints.

In `src/services/api/workout.ts`, implement:
1. getUserWorkoutProgramInfo() - calls GetUserWorkoutProgramTimeZoneInfo with timezone
2. getWorkoutDetails(workoutId: number) - calls GetUserCustomizedCurrentWorkout
3. getExerciseRecommendation(request: GetRecommendationForExerciseRequest) - calls GetRecommendationNormalRIRForExercise

Each method should:
- Use the existing API client configuration
- Include proper error handling
- Add request/response logging in development
- Handle authentication tokens properly

Include the timezone detection:
const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

Write integration tests in `src/services/api/__tests__/workout.test.ts` using MSW (Mock Service Worker) to mock API responses.
```

### Prompt 3: Create Zustand Store for Workout State

```text
Create a Zustand store slice for workout state management.

Create `src/store/workoutStore.ts` with:
1. State interface including:
   - currentWorkout: WorkoutTemplateModel | null
   - currentProgram: WorkoutTemplateGroupModel | null
   - exerciseRecommendations: Map<string, RecommendationModel>
   - loadingStates: Map<number, boolean>
   - errors: Map<number, Error>

2. Actions:
   - loadWorkoutProgram() - fetches and stores program info
   - loadWorkoutDetails(workoutId: number) - fetches full workout with exercises
   - loadExerciseRecommendation(exerciseId: number) - fetches single recommendation
   - getCacheKey(userId: string, exerciseId: number, workoutId: number) - returns cache key

3. Selectors:
   - getExerciseRecommendation(exerciseId: number)
   - isExerciseLoading(exerciseId: number)
   - getWorkoutExercises()

Write unit tests for all store actions and selectors.
```

### Prompt 4: Implement Caching Layer

```text
Implement a caching layer for exercise recommendations with 2-day expiry.

Create `src/services/cache/recommendationCache.ts`:
1. CacheEntry interface with data, timestamp, and workoutTemplateId
2. RecommendationCache class with:
   - get(key: string): CacheEntry | null
   - set(key: string, data: RecommendationModel, workoutTemplateId: number): void
   - clear(): void
   - clearByWorkout(workoutTemplateId: number): void
   - isExpired(entry: CacheEntry): boolean (2-day expiry)

Use localStorage for persistence with JSON serialization.
Add error handling for storage quota exceeded.

Update the workout store to check cache before API calls:
- In loadExerciseRecommendation, check cache first
- Save to cache after successful API response
- Clear cache when workout template ID changes

Write tests including cache expiry, storage limits, and error scenarios.
```

### Prompt 5: Create Request Queue for Rate Limiting

```text
Implement a request queue to manage API calls and prevent server overload.

Create `src/services/queue/requestQueue.ts`:
1. RequestQueue class with:
   - add(request: () => Promise<T>, priority?: number): Promise<T>
   - private process(): void
   - setConcurrency(limit: number): void
   - clear(): void

2. Features:
   - Priority queue (higher priority requests execute first)
   - Concurrent request limit (default: 3)
   - Request deduplication by key
   - Automatic retry with exponential backoff
   - Request cancellation on clear()

3. Integration with workout store:
   - Queue all recommendation requests
   - High priority for user-clicked exercises
   - Normal priority for background pre-loading

Write tests for queue behavior, priority handling, and concurrency limits.
```

### Prompt 6: Implement Home Page Workout Loading

```text
Update the home page to load workout program information.

In the home page component:
1. Use useEffect to call loadWorkoutProgram() on mount
2. Display next workout information when available
3. Show loading skeleton while fetching
4. Handle and display errors appropriately

Add these UI elements:
- Workout name and type
- Number of exercises
- Estimated duration
- "Start Workout" button

Pre-load first exercise recommendation:
- After workout program loads successfully
- Check if first exercise exists
- Silently load its recommendation in background
- No loading indicator for pre-loading

Write component tests for loading states, error handling, and user interactions.
```

### Prompt 7: Create Workout Page with Exercise List

```text
Create a workout page that displays all exercises and handles recommendation loading.

Create `src/app/workout/[id]/page.tsx`:
1. Load workout details if not already loaded
2. Display exercise list immediately
3. Show exercise name, equipment, and body part
4. Collapsed state by default (no sets visible)

For each exercise component:
- Click to expand and load recommendation
- Show loading spinner while fetching
- Display sets, reps, and weights when loaded
- Cache recommendations for future use
- Handle errors gracefully with retry option

UI requirements:
- Smooth expand/collapse animations
- Touch targets minimum 44px
- Loading states don't shift layout
- Error states with clear actions

Write tests for exercise interactions, loading states, and error scenarios.
```

### Prompt 8: Add Background Pre-loading

```text
Implement background pre-loading of all exercise recommendations.

After the workout page renders:
1. Wait for initial render completion (useEffect with empty deps)
2. Get list of exercises without recommendations
3. Queue them for background loading with normal priority
4. Load sequentially to avoid server overload
5. Update UI silently as each loads

Pre-loading strategy:
- First exercise: immediate high priority (if not cached)
- Remaining exercises: sequential with 500ms delay between requests
- Cancel pending requests if user navigates away
- Don't show loading indicators for background loads

Performance considerations:
- Use requestIdleCallback for low-priority background work
- Monitor memory usage and clear old cache entries if needed
- Track metrics: cache hit rate, loading times, errors

Write tests for background loading behavior and performance.
```

### Prompt 9: Handle Edge Cases and Loading States

```text
Implement comprehensive edge case handling for the workout loading system.

Edge cases to handle:
1. User clicks exercise before recommendation loads:
   - Cancel background load for that exercise
   - Load with high priority
   - Show loading state immediately

2. Network failure during pre-loading:
   - Silently retry with exponential backoff
   - Fall back to on-demand loading
   - Don't show errors for background failures

3. User rapidly clicks multiple exercises:
   - Cancel previous requests
   - Prioritize latest clicked exercise
   - Queue others with normal priority

4. Cache invalidation scenarios:
   - Workout completed: clear all recommendations
   - Different workout loaded: clear previous workout's cache
   - 2-day expiry: check before using cached data

Add loading state management:
- Track loading state per exercise
- Show skeletons for smooth transitions
- Prevent double-loading same exercise

Write comprehensive tests for all edge cases.
```

### Prompt 10: Add Offline Support

```text
Implement offline support using service workers and IndexedDB.

Create service worker functionality:
1. Cache API responses for offline access
2. Return cached data when offline
3. Queue mutations for sync when online
4. Show offline indicators in UI

IndexedDB implementation:
- More robust than localStorage
- Store larger amounts of data
- Better performance for complex queries
- Structured data storage

Offline features:
- View previously loaded workouts offline
- Show cached recommendations
- Indicate which data is cached vs fresh
- Sync changes when back online

UI updates:
- Offline banner when no connection
- Cached data indicators
- Sync status for pending changes
- Force refresh option

Write tests for offline scenarios and data sync.
```

### Prompt 11: Add Performance Monitoring

```text
Implement performance monitoring and analytics for the workout loading system.

Add tracking for:
1. Time to first exercise ready
2. Time to all exercises loaded
3. Cache hit rate
4. API error rate
5. Loading abandonment rate

Create `src/services/analytics/performanceTracker.ts`:
- Track key metrics with timestamps
- Calculate percentiles (p50, p95, p99)
- Send to analytics service
- Local development dashboard

Performance optimizations based on metrics:
- Adjust pre-loading strategy
- Tune cache expiry times
- Optimize request batching
- Identify slow API endpoints

Add user-facing performance features:
- Show loading progress
- Estimated time remaining
- Option to skip pre-loading
- Performance settings

Write tests for metric collection and reporting.
```

### Prompt 12: Final Integration and Testing

```text
Complete the integration of all workout loading components and add comprehensive testing.

Integration tasks:
1. Wire all components together
2. Ensure proper data flow between pages
3. Verify cache persistence across sessions
4. Test full user journey from home to workout completion

End-to-end tests to add:
- Complete workout flow with pre-loading
- Offline workout access
- Cache expiry and refresh
- Error recovery scenarios
- Performance under load

Code quality improvements:
- Add JSDoc comments to all public APIs
- Create usage examples in Storybook
- Document performance best practices
- Add debugging utilities

Performance validation:
- First exercise loads in <100ms (cached)
- All exercises loaded in <5s (fast connection)
- Graceful degradation on slow networks
- Memory usage stays under 50MB

Create a comprehensive test suite that validates the entire implementation meets the specified requirements.
```

## Implementation Notes

Each prompt should be implemented and tested before moving to the next. The implementation should:

1. Follow existing code patterns in the Dr. Muscle web app
2. Use TypeScript for type safety
3. Include comprehensive error handling
4. Write tests using the existing test framework
5. Follow accessibility best practices
6. Optimize for mobile-first experience

The final implementation should provide a superior user experience compared to the mobile app while maintaining reliability and performance.

This plan provides a comprehensive roadmap for implementing an optimized workout loading strategy that improves upon the mobile app's approach while maintaining compatibility and reliability.
