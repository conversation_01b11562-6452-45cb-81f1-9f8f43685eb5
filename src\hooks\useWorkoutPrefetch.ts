import { useState, useCallback, useRef } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { workoutApi } from '@/api/workouts'

interface UseWorkoutPrefetchReturn {
  progress: number
  isComplete: boolean
  error: string | null
  status: string
  startPrefetch: () => void
  reset: () => void
}

export function useWorkoutPrefetch(): UseWorkoutPrefetchReturn {
  const queryClient = useQueryClient()
  const [progress, setProgress] = useState(0)
  const [isComplete, setIsComplete] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [status, setStatus] = useState('Starting...')
  const isRunningRef = useRef(false)
  const programInfoCompletedRef = useRef(false)
  const workoutsCompletedRef = useRef(false)

  const updateProgress = useCallback(() => {
    let newProgress = 25 // Base progress for starting
    if (programInfoCompletedRef.current) newProgress += 25
    if (workoutsCompletedRef.current) newProgress += 25
    if (programInfoCompletedRef.current && workoutsCompletedRef.current) {
      newProgress = 100
    }
    setProgress(newProgress)
  }, [])

  const startPrefetch = useCallback(async () => {
    // Prevent multiple concurrent prefetches
    if (isRunningRef.current) {
      return
    }

    isRunningRef.current = true
    programInfoCompletedRef.current = false
    workoutsCompletedRef.current = false
    setProgress(25)
    setStatus('Loading workout program...')
    setError(null)

    try {
      // Start both API calls in parallel
      const promises = [
        workoutApi
          .getUserProgramInfo()
          .then((result) => {
            programInfoCompletedRef.current = true
            updateProgress()
            queryClient.setQueryData(['userProgramInfo'], result)
            setStatus('Loading exercises...')
            return { status: 'fulfilled' as const, value: result }
          })
          .catch((err) => {
            programInfoCompletedRef.current = true
            updateProgress()
            return { status: 'rejected' as const, reason: err }
          }),

        workoutApi
          .getUserWorkout()
          .then((result) => {
            workoutsCompletedRef.current = true
            updateProgress()
            queryClient.setQueryData(['userWorkout'], result)
            const exerciseCount = result?.[0]?.Exercises?.length || 0
            if (exerciseCount > 0) {
              setStatus(`Loading ${exerciseCount} exercises...`)
            }
            return { status: 'fulfilled' as const, value: result }
          })
          .catch((err) => {
            workoutsCompletedRef.current = true
            updateProgress()
            return { status: 'rejected' as const, reason: err }
          }),
      ]

      const [programInfoResult, workoutsResult] = await Promise.all(promises)

      // Check if both failed
      if (
        programInfoResult?.status === 'rejected' &&
        workoutsResult?.status === 'rejected'
      ) {
        throw new Error('Failed to load workout data')
      }

      // Complete the prefetch
      setIsComplete(true)
      setStatus('Ready!')
    } catch (err) {
      setError('Failed to load workout data')
      setStatus('Error loading workout data')
      setIsComplete(false)
    } finally {
      isRunningRef.current = false
    }
  }, [queryClient, updateProgress])

  const reset = useCallback(() => {
    setProgress(0)
    setIsComplete(false)
    setError(null)
    setStatus('Starting...')
    isRunningRef.current = false
    programInfoCompletedRef.current = false
    workoutsCompletedRef.current = false
  }, [])

  return {
    progress,
    isComplete,
    error,
    status,
    startPrefetch,
    reset,
  }
}
