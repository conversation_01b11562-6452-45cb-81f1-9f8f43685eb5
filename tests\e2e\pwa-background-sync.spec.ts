import { test, expect, Page, BrowserContext } from '@playwright/test'

test.describe('PWA Background Sync Tests', () => {
  let page: Page
  let context: BrowserContext
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p, context: c }) => {
    page = p
    context = c

    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')
  })

  test('should queue workout data for background sync when offline', async () => {
    // Navigate to workout
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Go offline
    await context.setOffline(true)

    // Register sync event listener
    const syncRequested = await page.evaluate(() => {
      return new Promise((resolve) => {
        if ('serviceWorker' in navigator && 'SyncManager' in window) {
          navigator.serviceWorker.ready.then((registration) => {
            // Listen for sync registration
            const originalSync = registration.sync.register.bind(
              registration.sync
            )
            registration.sync.register = function (tag: string) {
              resolve(tag)
              return originalSync(tag)
            }
          })
        } else {
          resolve(false)
        }
      })
    })

    // Complete a set while offline
    await page.click('[data-testid="exercise-item"]')
    await page.fill('input[name="weight"]', '100')
    await page.fill('input[name="reps"]', '10')
    await page.click('button:has-text("Save")')

    // Should show sync pending indicator
    await expect(page.locator('[data-testid="sync-pending"]')).toBeVisible()

    // If browser supports background sync
    if (syncRequested) {
      expect(syncRequested).toBe('workout-sync')
    }

    // Go back online
    await context.setOffline(false)

    // Should automatically sync
    await expect(page.locator('[data-testid="sync-complete"]')).toBeVisible({
      timeout: 5000,
    })
  })

  test('should handle background sync failures gracefully', async () => {
    // Mock failed sync
    await page.route('**/SaveWorkoutLog', (route) => {
      route.fulfill({ status: 500 })
    })

    // Navigate to workout
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Complete a set
    await page.click('[data-testid="exercise-item"]')
    await page.fill('input[name="weight"]', '100')
    await page.fill('input[name="reps"]', '10')
    await page.click('button:has-text("Save")')

    // Should show sync error
    await expect(page.locator('[data-testid="sync-error"]')).toBeVisible()

    // Should offer manual retry
    await expect(page.locator('button:has-text("Retry Sync")')).toBeVisible()
  })
})
