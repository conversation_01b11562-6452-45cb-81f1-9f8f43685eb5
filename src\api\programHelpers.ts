import type { GetUserWorkoutLogDate } from '@/types'

/**
 * Helper function to generate program description based on name
 */
export function getProgramDescription(programName: string): string {
  const descriptions = {
    Beginner: 'Perfect for those new to strength training',
    Intermediate: 'Take your training to the next level',
    Advanced: 'Push your limits with advanced techniques',
    Strength: 'Build raw strength with progressive overload',
    Hypertrophy: 'Maximize muscle growth with volume training',
    'Full Body': 'Train your entire body efficiently',
  } as const

  // Find matching description based on program name
  const keys = Object.keys(descriptions) as Array<keyof typeof descriptions>
  const matchingKey = keys.find((key) =>
    programName.toLowerCase().includes(key.toLowerCase())
  )

  if (matchingKey) {
    return descriptions[matchingKey]
  }

  // Return empty string to hide the description
  return ''
}

/**
 * Helper function to determine program category
 */
export function getProgramCategory(programName: string): string {
  // Check for explicit categories in program name
  const categories = [
    'Strength',
    'Hypertrophy',
    'Endurance',
    'Power',
    'Full Body',
    'Powerbuilding',
    'Bodybuilding',
  ]

  const foundCategory = categories.find((category) =>
    programName.toLowerCase().includes(category.toLowerCase())
  )

  if (foundCategory) {
    return foundCategory
  }

  // Custom program names get "Custom" category
  const knownPrograms = ['beginner', 'intermediate', 'advanced']
  const isKnownProgram = knownPrograms.some((known) =>
    programName.toLowerCase().includes(known)
  )

  return isKnownProgram ? 'Strength' : 'Custom'
}

/**
 * Calculate program start date based on workouts completed and last workout date
 */
export function calculateStartDate(
  workoutsCompleted: number,
  lastWorkoutDate?: string
): string {
  if (!lastWorkoutDate || workoutsCompleted === 0) {
    return new Date().toISOString()
  }

  // Estimate start date assuming 3 workouts per week
  const weeksElapsed = Math.ceil(workoutsCompleted / 3)
  const startDate = new Date(lastWorkoutDate)
  startDate.setDate(startDate.getDate() - weeksElapsed * 7)

  return startDate.toISOString()
}

/**
 * Calculate total volume from workout log dates
 */
export function calculateTotalVolume(
  workoutLogDates: GetUserWorkoutLogDate[]
): number {
  const totalVolume = workoutLogDates.reduce((total, workoutDate) => {
    if (!workoutDate.Sets) return total

    const workoutVolume = workoutDate.Sets.reduce((setTotal, set) => {
      // Skip warmup sets
      if (set.IsWarmups) return setTotal

      // Add weight * reps to total (using Lb for consistency)
      if (set.Weight && set.Reps) {
        return setTotal + set.Weight.Lb * set.Reps
      }
      return setTotal
    }, 0)

    return total + workoutVolume
  }, 0)

  return Math.round(totalVolume)
}
