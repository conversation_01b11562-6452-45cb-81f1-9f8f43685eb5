import type { WorkoutTemplateGroupModel } from '@/types'

export const WORKOUT_CACHE_KEY = 'drmuscle-workout-cache'
export const CACHE_VERSION = 1
export const MAX_CACHE_SIZE_KB = 100
export const DEFAULT_CACHE_EXPIRY_MS = 24 * 60 * 60 * 1000 // 24 hours

interface CachedWorkoutData {
  version: number
  timestamp: number
  data: WorkoutTemplateGroupModel[]
}

export const WorkoutCache = {
  /**
   * Get cached workout data
   * Returns null if cache is empty, expired, or invalid
   */
  get(): WorkoutTemplateGroupModel[] | null {
    try {
      const cached = localStorage.getItem(WORKOUT_CACHE_KEY)
      if (!cached) return null

      const parsedCache: CachedWorkoutData = JSON.parse(cached)

      // Check cache version
      if (parsedCache.version !== CACHE_VERSION) {
        this.clear()
        return null
      }

      // Check if expired
      if (this.isExpired(parsedCache.timestamp)) {
        this.clear()
        return null
      }

      return parsedCache.data
    } catch (error) {
      // Cache is corrupted or localStorage not available
      return null
    }
  },

  /**
   * Set workout data in cache
   * Will not cache if data is too large or localStorage is not available
   */
  set(data: WorkoutTemplateGroupModel[] | null): void {
    try {
      // Don't cache empty data
      if (!data || data.length === 0) return

      const cacheData: CachedWorkoutData = {
        version: CACHE_VERSION,
        timestamp: Date.now(),
        data,
      }

      // Check size before storing
      const sizeKB = this.calculateSizeKB(cacheData)
      if (sizeKB > MAX_CACHE_SIZE_KB) {
        return
      }

      localStorage.setItem(WORKOUT_CACHE_KEY, JSON.stringify(cacheData))
    } catch (error) {
      // Quota exceeded or localStorage not available
      // Fail silently - caching is optional
    }
  },

  /**
   * Clear cached workout data
   */
  clear(): void {
    try {
      localStorage.removeItem(WORKOUT_CACHE_KEY)
    } catch {
      // Fail silently
    }
  },

  /**
   * Check if cache is expired
   */
  isExpired(
    timestamp: number,
    expiryMs: number = DEFAULT_CACHE_EXPIRY_MS
  ): boolean {
    return Date.now() - timestamp > expiryMs
  },

  /**
   * Calculate size of data in KB
   */
  calculateSizeKB(data: unknown): number {
    const jsonString = JSON.stringify(data)
    return jsonString.length / 1024
  },
}
