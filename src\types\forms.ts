/**
 * Form-related type definitions
 */

/**
 * Base form data interface
 */
export interface FormData {
  [key: string]: string | number | boolean | undefined | null
}

/**
 * Login form data structure
 */
export interface LoginFormData {
  email: string
  password: string
  rememberMe?: boolean
}

/**
 * Registration form data structure
 */
export interface RegisterFormData {
  email: string
  password: string
  confirmPassword: string
  firstName?: string
  lastName?: string
  birthDate?: string
  massUnit: 'lbs' | 'kg'
  gender?: 'male' | 'female' | 'other'
  isAthlete?: boolean
  isVegan?: boolean
  bodyWeight?: number
  acceptTerms: boolean
}

/**
 * Set execution form data
 */
export interface SetFormData {
  reps: number
  weight: number
  unit: 'lbs' | 'kg'
  rir?: number
}

/**
 * Form validation error
 */
export interface ValidationError {
  field: string
  message: string
  code?: string
}
