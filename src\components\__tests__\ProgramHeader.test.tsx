import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ProgramHeader } from '../ProgramHeader'
import type { ProgramModel } from '@/types'
import React from 'react'

describe('ProgramHeader', () => {
  const mockProgram: ProgramModel = {
    id: 1,
    name: 'Beginner Strength Program',
    description: 'A comprehensive program for beginners',
    category: 'Strength',
    totalDays: 84,
    currentDay: 15,
    workoutsCompleted: 12,
    startDate: '2024-01-01',
    totalWorkouts: 36,
    imageUrl: '/images/strength-program.jpg',
  }

  describe('Basic rendering', () => {
    it('should render program name prominently', () => {
      render(<ProgramHeader program={mockProgram} />)

      const heading = screen.getByRole('heading', { name: mockProgram.name })
      expect(heading).toBeInTheDocument()
      expect(heading).toHaveClass('text-2xl') // Large mobile-first typography
    })

    it('should display category badge', () => {
      render(<ProgramHeader program={mockProgram} />)

      const badge = screen.getByText(mockProgram.category)
      expect(badge).toBeInTheDocument()
      expect(badge).toHaveClass('rounded-full') // Badge styling
    })

    it('should show loading skeleton when program is undefined', () => {
      render(<ProgramHeader program={undefined} isLoading />)

      expect(screen.getByTestId('program-header-skeleton')).toBeInTheDocument()
    })

    it('should handle missing program gracefully', () => {
      render(<ProgramHeader program={undefined} />)

      expect(screen.queryByRole('heading')).not.toBeInTheDocument()
      expect(screen.queryByText('Strength')).not.toBeInTheDocument()
    })
  })

  describe('Exercise count display', () => {
    it('should display exercise count when provided', () => {
      render(<ProgramHeader program={mockProgram} exerciseCount={8} />)

      expect(screen.getByText('8 exercises')).toBeInTheDocument()
    })

    it('should handle singular exercise count', () => {
      render(<ProgramHeader program={mockProgram} exerciseCount={1} />)

      expect(screen.getByText('1 exercise')).toBeInTheDocument()
    })

    it('should show loading skeleton for exercise count', () => {
      render(<ProgramHeader program={mockProgram} isLoadingExercises />)

      expect(screen.getByTestId('exercise-count-skeleton')).toBeInTheDocument()
    })

    it('should not show exercise count when not provided', () => {
      render(<ProgramHeader program={mockProgram} />)

      expect(screen.queryByText(/exercise/)).not.toBeInTheDocument()
    })
  })

  describe('Image removal', () => {
    it('should not render any image or gradient background', () => {
      render(<ProgramHeader program={mockProgram} />)

      // Should not find any images
      expect(screen.queryByRole('img')).not.toBeInTheDocument()
      expect(screen.queryByAltText(mockProgram.name)).not.toBeInTheDocument()

      // Should not find gradient background
      const container = screen.getByTestId('program-header')
      expect(
        container.querySelector('.bg-gradient-to-br')
      ).not.toBeInTheDocument()
      expect(
        screen.queryByTestId('program-header-fallback')
      ).not.toBeInTheDocument()
    })
  })

  describe('Long program names', () => {
    it('should handle long program names gracefully', () => {
      const longNameProgram = {
        ...mockProgram,
        name: 'Advanced Hypertrophy and Strength Building Program for Intermediate Athletes',
      }
      render(<ProgramHeader program={longNameProgram} />)

      const heading = screen.getByRole('heading')
      expect(heading).toHaveClass('break-words') // Allow word breaking
    })
  })

  describe('Category badge styling', () => {
    it('should apply different colors based on category', () => {
      const categories = [
        { category: 'Strength', expectedClass: 'bg-blue-100' },
        { category: 'Hypertrophy', expectedClass: 'bg-purple-100' },
        { category: 'Beginner', expectedClass: 'bg-green-100' },
        { category: 'Advanced', expectedClass: 'bg-red-100' },
      ]

      categories.forEach(({ category, expectedClass }) => {
        const { rerender } = render(
          <ProgramHeader program={{ ...mockProgram, category }} />
        )

        const badge = screen.getByText(category)
        expect(badge).toHaveClass(expectedClass)

        rerender(<ProgramHeader program={mockProgram} />)
      })
    })
  })

  describe('Styling and layout', () => {
    it('should have mobile-optimized spacing', () => {
      render(<ProgramHeader program={mockProgram} />)

      const container = screen.getByTestId('program-header')
      expect(container).toHaveClass('space-y-2') // Compact vertical spacing
    })

    it('should support dark mode', () => {
      render(
        <div className="dark">
          <ProgramHeader program={mockProgram} />
        </div>
      )

      const heading = screen.getByRole('heading')
      expect(heading).toHaveClass('dark:text-gray-100')
    })

    it('should accept custom className', () => {
      render(<ProgramHeader program={mockProgram} className="custom-class" />)

      const container = screen.getByTestId('program-header')
      expect(container).toHaveClass('custom-class')
    })
  })

  describe('Component order', () => {
    it('should render elements in correct order', () => {
      render(<ProgramHeader program={mockProgram} exerciseCount={8} />)

      const container = screen.getByTestId('program-header')
      const elements = container.querySelectorAll('*')

      // Check order: name, exercise count, category badge
      expect(elements[0]).toHaveTextContent(mockProgram.name)
      expect(elements[1]).toHaveTextContent('8 exercises')
      // Category badge is inside a flex container
      expect(container).toHaveTextContent(mockProgram.category)
    })
  })
})
