import { test, expect } from '@playwright/test'
import type { Page } from '@playwright/test'

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Test123!',
}

// Helper to wait for shimmer to disappear
async function waitForShimmerToDisappear(page: Page, selector: string) {
  await page.waitForFunction(
    (sel) => {
      const element = document.querySelector(sel)
      if (!element) return false
      const shimmer = element.querySelector('[data-testid="shimmer-overlay"]')
      return !shimmer || shimmer.getAttribute('data-visible') === 'false'
    },
    selector,
    { timeout: 10000 }
  )
}

test.describe('Progressive Loading on Program Page', () => {
  test.beforeEach(async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize({ width: 390, height: 844 })
    
    // Go to login page
    await page.goto('/')
    
    // Wait for login form
    await page.waitForSelector('[data-testid="login-form"]')
  })

  test('should show progressive loading with shimmer effects', async ({ page }) => {
    // Login
    await page.fill('[data-testid="email-input"]', TEST_USER.email)
    await page.fill('[data-testid="password-input"]', TEST_USER.password)
    await page.click('[data-testid="login-button"]')
    
    // Wait for success screen
    await page.waitForSelector('[data-testid="quick-success-screen"]')
    
    // Wait for navigation to program page
    await page.waitForURL(/\/program/, { timeout: 10000 })
    
    // Check that welcome header shows immediately
    const welcomeHeader = page.locator('[data-testid="welcome-header"]')
    await expect(welcomeHeader).toBeVisible()
    
    // Initially should show "Welcome back" or email
    const welcomeText = await welcomeHeader.textContent()
    expect(welcomeText).toMatch(/Welcome back/i)
    
    // Check that metrics show with shimmer initially
    const counters = page.locator('[data-testid="animated-counter"]')
    await expect(counters).toHaveCount(3)
    
    // Verify shimmer effects are visible
    const shimmers = page.locator('[data-testid="shimmer-overlay"][data-visible="true"]')
    await expect(shimmers.first()).toBeVisible()
    
    // Wait for first metric to load (shimmer disappears)
    await waitForShimmerToDisappear(page, '[data-testid="animated-counter"]:first-child')
    
    // Verify metrics animate from 0
    const firstMetricValue = await page
      .locator('[data-testid="animated-counter-value"]')
      .first()
      .textContent()
    expect(Number(firstMetricValue?.replace(/,/g, '') || '0')).toBeGreaterThanOrEqual(0)
    
    // Wait for all shimmers to disappear (max 10 seconds)
    await page.waitForFunction(
      () => {
        const shimmers = document.querySelectorAll('[data-testid="shimmer-overlay"][data-visible="true"]')
        return shimmers.length === 0
      },
      { timeout: 10000 }
    )
    
    // Verify welcome header updates with user's name (if available)
    await page.waitForTimeout(1000) // Give time for name to update
    const updatedWelcomeText = await welcomeHeader.textContent()
    // Should either show first name or still show "Welcome back"
    expect(updatedWelcomeText).toMatch(/Welcome back/i)
    
    // Verify all metrics have loaded with values
    const metricValues = await page.locator('[data-testid="animated-counter-value"]').allTextContents()
    expect(metricValues).toHaveLength(3)
    metricValues.forEach(value => {
      const numValue = Number(value.replace(/,/g, ''))
      expect(numValue).toBeGreaterThanOrEqual(0)
    })
  })

  test('should handle errors gracefully with recovery', async ({ page }) => {
    // Login
    await page.fill('[data-testid="email-input"]', TEST_USER.email)
    await page.fill('[data-testid="password-input"]', TEST_USER.password)
    
    // Intercept API calls to simulate errors
    await page.route('**/api/Account/GetUserInfoPyramid', async (route) => {
      // Fail first request
      if (!page.url().includes('retry')) {
        await route.abort('failed')
      } else {
        // Success on retry
        await route.fulfill({
          status: 200,
          body: JSON.stringify({
            StatusCode: 200,
            Result: {
              FirstName: 'Test',
              LastName: 'User',
            },
          }),
        })
      }
    })
    
    await page.click('[data-testid="login-button"]')
    
    // Wait for navigation to program page
    await page.waitForURL(/\/program/, { timeout: 10000 })
    
    // Page should still load despite initial error
    await expect(page.locator('[data-testid="program-overview-page"]')).toBeVisible()
    
    // Metrics should still show (with shimmer or values)
    const counters = page.locator('[data-testid="animated-counter"]')
    await expect(counters).toHaveCount(3)
  })

  test('should cache user info for 1 week', async ({ page, context }) => {
    // Login
    await page.fill('[data-testid="email-input"]', TEST_USER.email)
    await page.fill('[data-testid="password-input"]', TEST_USER.password)
    await page.click('[data-testid="login-button"]')
    
    // Wait for program page
    await page.waitForURL(/\/program/, { timeout: 10000 })
    
    // Wait for data to load
    await page.waitForTimeout(2000)
    
    // Get localStorage data
    const authStore = await page.evaluate(() => {
      const stored = localStorage.getItem('auth-store')
      return stored ? JSON.parse(stored) : null
    })
    
    expect(authStore).toBeTruthy()
    expect(authStore.state.cachedUserInfo).toBeTruthy()
    expect(authStore.state.userInfoCacheTimestamp).toBeTruthy()
    
    // Reload page
    await page.reload()
    
    // Welcome header should show immediately with cached data
    const welcomeHeader = page.locator('[data-testid="welcome-header"]')
    await expect(welcomeHeader).toBeVisible()
    
    // Should not show shimmer on metrics (using cached data)
    const shimmers = page.locator('[data-testid="shimmer-overlay"][data-visible="true"]')
    const shimmerCount = await shimmers.count()
    expect(shimmerCount).toBe(0)
  })

  test('should track performance metrics', async ({ page }) => {
    // Intercept console logs to capture performance metrics
    const consoleLogs: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'log' && msg.text().includes('[UserInfo Performance]')) {
        consoleLogs.push(msg.text())
      }
    })
    
    // Login
    await page.fill('[data-testid="email-input"]', TEST_USER.email)
    await page.fill('[data-testid="password-input"]', TEST_USER.password)
    await page.click('[data-testid="login-button"]')
    
    // Wait for program page
    await page.waitForURL(/\/program/, { timeout: 10000 })
    
    // Wait for performance tracking to complete
    await page.waitForTimeout(3000)
    
    // Check that performance metrics were logged (in development)
    if (process.env.NODE_ENV === 'development') {
      expect(consoleLogs.length).toBeGreaterThan(0)
      
      // Should log various performance metrics
      const allLogs = consoleLogs.join('\n')
      expect(allLogs).toContain('Login to First Byte')
      expect(allLogs).toContain('Cache Hit Rate')
    }
  })

  test('should support pull-to-refresh for user info', async ({ page }) => {
    // Login
    await page.fill('[data-testid="email-input"]', TEST_USER.email)
    await page.fill('[data-testid="password-input"]', TEST_USER.password)
    await page.click('[data-testid="login-button"]')
    
    // Wait for program page
    await page.waitForURL(/\/program/, { timeout: 10000 })
    
    // Wait for initial load
    await page.waitForTimeout(2000)
    
    // Simulate pull-to-refresh gesture
    const scrollContainer = page.locator('[data-testid="scroll-container"]')
    await scrollContainer.evaluate((element) => {
      // Dispatch touch events to simulate pull gesture
      const touchstart = new TouchEvent('touchstart', {
        touches: [new Touch({ identifier: 1, target: element, clientY: 100 })],
      })
      const touchmove = new TouchEvent('touchmove', {
        touches: [new Touch({ identifier: 1, target: element, clientY: 200 })],
      })
      const touchend = new TouchEvent('touchend', {
        touches: [],
      })
      
      element.dispatchEvent(touchstart)
      element.dispatchEvent(touchmove)
      element.dispatchEvent(touchend)
    })
    
    // Check for refresh indicator or loading state
    const refreshIndicator = page.locator('[data-testid="pull-to-refresh-indicator"]')
    await expect(refreshIndicator).toBeVisible()
    
    // Wait for refresh to complete
    await page.waitForTimeout(2000)
    
    // Success animation should show
    const successAnimation = page.locator('[data-testid="success-animation"]')
    await expect(successAnimation).toBeVisible()
  })

  test('should handle network interruptions gracefully', async ({ page }) => {
    // Login
    await page.fill('[data-testid="email-input"]', TEST_USER.email)
    await page.fill('[data-testid="password-input"]', TEST_USER.password)
    await page.click('[data-testid="login-button"]')
    
    // Wait for program page
    await page.waitForURL(/\/program/, { timeout: 10000 })
    
    // Go offline
    await page.context().setOffline(true)
    
    // Try to refresh
    await page.reload()
    
    // Page should still show with cached data
    await expect(page.locator('[data-testid="program-overview-page"]')).toBeVisible()
    
    // Should show metrics (from cache)
    const counters = page.locator('[data-testid="animated-counter"]')
    await expect(counters).toHaveCount(3)
    
    // Go back online
    await page.context().setOffline(false)
    
    // Data should automatically refresh
    await page.waitForTimeout(2000)
    
    // Verify page is still functional
    const continueButton = page.locator('button:has-text("Continue to Workout")')
    await expect(continueButton).toBeVisible()
    await expect(continueButton).toBeEnabled()
  })

  test('should announce loading states for screen readers', async ({ page }) => {
    // Enable screen reader announcements
    await page.addInitScript(() => {
      // Mock screen reader by logging announcements
      const originalSetAttribute = Element.prototype.setAttribute
      Element.prototype.setAttribute = function(name, value) {
        if (name === 'aria-live' || name === 'aria-label' || name === 'aria-busy') {
          console.log(`[A11Y] ${name}="${value}" on ${this.tagName}`)
        }
        return originalSetAttribute.call(this, name, value)
      }
    })
    
    const a11yLogs: string[] = []
    page.on('console', (msg) => {
      if (msg.text().includes('[A11Y]')) {
        a11yLogs.push(msg.text())
      }
    })
    
    // Login
    await page.fill('[data-testid="email-input"]', TEST_USER.email)
    await page.fill('[data-testid="password-input"]', TEST_USER.password)
    await page.click('[data-testid="login-button"]')
    
    // Wait for program page
    await page.waitForURL(/\/program/, { timeout: 10000 })
    
    // Wait for loading to complete
    await page.waitForTimeout(3000)
    
    // Check accessibility announcements
    expect(a11yLogs.length).toBeGreaterThan(0)
    
    // Should announce loading states
    const allA11yLogs = a11yLogs.join('\n')
    expect(allA11yLogs).toContain('aria-busy')
    expect(allA11yLogs).toContain('aria-live')
    expect(allA11yLogs).toContain('aria-label')
  })
})

test.describe('Progressive Loading Error Recovery', () => {
  test('should clear corrupted cache and retry', async ({ page }) => {
    // Inject corrupted data into localStorage
    await page.addInitScript(() => {
      localStorage.setItem('auth-store', '{"state":{corrupted json}')
    })
    
    // Navigate to login
    await page.goto('/')
    
    // Login
    await page.fill('[data-testid="email-input"]', TEST_USER.email)
    await page.fill('[data-testid="password-input"]', TEST_USER.password)
    await page.click('[data-testid="login-button"]')
    
    // Should handle corrupted cache and still navigate
    await page.waitForURL(/\/program/, { timeout: 10000 })
    
    // Page should load successfully
    await expect(page.locator('[data-testid="program-overview-page"]')).toBeVisible()
    
    // Check that cache was cleared and rebuilt
    const authStore = await page.evaluate(() => {
      const stored = localStorage.getItem('auth-store')
      return stored ? JSON.parse(stored) : null
    })
    
    expect(authStore).toBeTruthy()
    expect(authStore.state).toBeTruthy()
  })
})