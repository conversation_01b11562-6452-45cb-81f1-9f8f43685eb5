import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render } from '@testing-library/react'
import { FontLoadingMonitor } from '../FontLoadingMonitor'

// Mock fontLoader utility
vi.mock('@/utils/fontLoader', () => ({
  monitorFontLoading: vi.fn().mockResolvedValue(undefined),
}))

describe('FontLoadingMonitor', () => {
  let originalFonts: FontFaceSet | undefined

  beforeEach(() => {
    // Save original fonts API
    originalFonts = document.fonts

    // Mock document.fonts
    Object.defineProperty(document, 'fonts', {
      value: {
        ready: Promise.resolve(),
      },
      writable: true,
      configurable: true,
    })

    // Clear classes
    document.documentElement.className = ''
  })

  afterEach(() => {
    // Restore original fonts API
    if (originalFonts !== undefined) {
      Object.defineProperty(document, 'fonts', {
        value: originalFonts,
        writable: true,
        configurable: true,
      })
    }

    vi.clearAllMocks()
  })

  it('should render null (no visual output)', () => {
    const { container } = render(<FontLoadingMonitor />)
    expect(container.firstChild).toBeNull()
  })

  it('should add fonts-loading class initially', () => {
    render(<FontLoadingMonitor />)

    expect(document.documentElement.classList.contains('fonts-loading')).toBe(
      true
    )
  })

  it('should add fonts-loaded class when fonts are ready', async () => {
    const readyPromise = Promise.resolve()
    Object.defineProperty(document, 'fonts', {
      value: {
        ready: readyPromise,
      },
      configurable: true,
    })

    render(<FontLoadingMonitor />)

    // Wait for fonts.ready to resolve
    await readyPromise

    expect(document.documentElement.classList.contains('fonts-loading')).toBe(
      false
    )
    expect(document.documentElement.classList.contains('fonts-loaded')).toBe(
      true
    )
  })

  it('should add fonts-failed class on error', async () => {
    const readyPromise = Promise.reject(new Error('Font loading failed'))
    Object.defineProperty(document, 'fonts', {
      value: {
        ready: readyPromise,
      },
      configurable: true,
    })

    render(<FontLoadingMonitor />)

    // Wait for promise to settle
    await new Promise((resolve) => {
      setTimeout(resolve, 0)
    })

    expect(document.documentElement.classList.contains('fonts-loading')).toBe(
      false
    )
    expect(document.documentElement.classList.contains('fonts-failed')).toBe(
      true
    )
  })

  it('should handle browsers without fonts API', () => {
    // Remove fonts API
    delete (document as any).fonts

    render(<FontLoadingMonitor />)

    // Should remove loading class immediately
    expect(document.documentElement.classList.contains('fonts-loading')).toBe(
      false
    )
  })

  it('should only monitor fonts in production', async () => {
    const monitorMock = await import('@/utils/fontLoader')

    // Set NODE_ENV to development
    vi.stubEnv('NODE_ENV', 'development')

    render(<FontLoadingMonitor />)

    expect(monitorMock.monitorFontLoading).not.toHaveBeenCalled()

    // NODE_ENV will be restored automatically by vitest
  })

  it('should monitor fonts in production', async () => {
    const monitorMock = await import('@/utils/fontLoader')

    // Set NODE_ENV to production
    vi.stubEnv('NODE_ENV', 'production')

    render(<FontLoadingMonitor />)

    expect(monitorMock.monitorFontLoading).toHaveBeenCalled()

    // NODE_ENV will be restored automatically by vitest
  })
})
