import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { WorkoutErrorBoundary } from '../WorkoutErrorBoundary'
import React from 'react'

// Mock console methods
const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {})

// Component that throws an error
function ThrowError({ shouldThrow }: { shouldThrow: boolean }) {
  if (shouldThrow) {
    throw new Error('Test error')
  }
  return <div>No error</div>
}

// Component that throws specific error types
function ThrowSpecificError({ errorType }: { errorType: string }) {
  switch (errorType) {
    case 'network':
      throw new Error('Network request failed')
    case 'api401':
      throw new Error('API Error: 401 Unauthorized')
    case 'api403':
      throw new Error('API Error: 403 Forbidden')
    default:
      throw new Error('Generic error')
  }
}

describe('WorkoutErrorBoundary', () => {
  const mockLocation = { href: '' }

  beforeEach(() => {
    vi.clearAllMocks()
    mockConsoleError.mockClear()
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true,
    })
  })

  it('should render children when there is no error', () => {
    render(
      <WorkoutErrorBoundary>
        <div>Test content</div>
      </WorkoutErrorBoundary>
    )

    expect(screen.getByText('Test content')).toBeInTheDocument()
  })

  it('should catch and display error fallback', () => {
    render(
      <WorkoutErrorBoundary>
        <ThrowError shouldThrow />
      </WorkoutErrorBoundary>
    )

    expect(screen.getByText('Workout Error')).toBeInTheDocument()
    expect(
      screen.getByText(/Something went wrong while loading your workout/)
    ).toBeInTheDocument()
    expect(screen.getByTestId('retry-workout-button')).toBeInTheDocument()
  })

  it('should show network-specific error message', () => {
    render(
      <WorkoutErrorBoundary>
        <ThrowSpecificError errorType="network" />
      </WorkoutErrorBoundary>
    )

    expect(screen.getByText('Connection Problem')).toBeInTheDocument()
    expect(
      screen.getByText(/check your internet connection/)
    ).toBeInTheDocument()
  })

  it('should show API error message with login button', () => {
    render(
      <WorkoutErrorBoundary>
        <ThrowSpecificError errorType="api401" />
      </WorkoutErrorBoundary>
    )

    expect(
      screen.getByText(/Your session may have expired/)
    ).toBeInTheDocument()
    expect(screen.getByTestId('login-button')).toBeInTheDocument()
  })

  it('should reset error boundary state when retry is clicked', () => {
    // Use a ref to control whether to throw
    let shouldThrow = true

    function ThrowConditionally() {
      if (shouldThrow) {
        throw new Error('Test error')
      }
      return <div>No error</div>
    }

    const { rerender } = render(
      <WorkoutErrorBoundary>
        <ThrowConditionally />
      </WorkoutErrorBoundary>
    )

    // Should show error
    expect(screen.getByText('Workout Error')).toBeInTheDocument()

    // Change condition to not throw
    shouldThrow = false

    // Click retry - this should reset the error boundary
    fireEvent.click(screen.getByTestId('retry-workout-button'))

    // Force a new render with the component that won't throw
    rerender(
      <WorkoutErrorBoundary>
        <ThrowConditionally />
      </WorkoutErrorBoundary>
    )

    // Should now show the success content
    expect(screen.getByText('No error')).toBeInTheDocument()
    expect(screen.queryByText('Workout Error')).not.toBeInTheDocument()
  })

  it('should navigate to login when login button is clicked', () => {
    render(
      <WorkoutErrorBoundary>
        <ThrowSpecificError errorType="api401" />
      </WorkoutErrorBoundary>
    )

    fireEvent.click(screen.getByTestId('login-button'))

    expect(mockLocation.href).toBe('/login')
  })

  it('should navigate to home when home button is clicked', () => {
    render(
      <WorkoutErrorBoundary>
        <ThrowError shouldThrow />
      </WorkoutErrorBoundary>
    )

    fireEvent.click(screen.getByTestId('home-button'))

    expect(mockLocation.href).toBe('/')
  })

  it('should call onError callback when error occurs', () => {
    const mockOnError = vi.fn()

    render(
      <WorkoutErrorBoundary onError={mockOnError}>
        <ThrowError shouldThrow />
      </WorkoutErrorBoundary>
    )

    expect(mockOnError).toHaveBeenCalledWith(
      expect.objectContaining({
        message: 'Test error',
      }),
      expect.objectContaining({
        componentStack: expect.any(String),
      })
    )
  })

  it('should show error details in development mode', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'

    render(
      <WorkoutErrorBoundary>
        <ThrowError shouldThrow />
      </WorkoutErrorBoundary>
    )

    expect(screen.getByText('Error Details')).toBeInTheDocument()

    // Click to expand details
    fireEvent.click(screen.getByText('Error Details'))

    expect(screen.getByText(/Test error/)).toBeInTheDocument()

    process.env.NODE_ENV = originalEnv
  })

  it('should not show error details in production mode', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'production'

    render(
      <WorkoutErrorBoundary>
        <ThrowError shouldThrow />
      </WorkoutErrorBoundary>
    )

    expect(screen.queryByText('Error Details')).not.toBeInTheDocument()

    process.env.NODE_ENV = originalEnv
  })

  it('should show helpful tips section', () => {
    render(
      <WorkoutErrorBoundary>
        <ThrowError shouldThrow />
      </WorkoutErrorBoundary>
    )

    expect(screen.getByText('Tips for Workout Issues:')).toBeInTheDocument()
    expect(screen.getByText(/stable internet connection/)).toBeInTheDocument()
    expect(
      screen.getByText(/workout progress is automatically saved/)
    ).toBeInTheDocument()
  })

  it('should preserve error boundary state across component updates', () => {
    const { rerender } = render(
      <WorkoutErrorBoundary>
        <ThrowError shouldThrow />
      </WorkoutErrorBoundary>
    )

    expect(screen.getByText('Workout Error')).toBeInTheDocument()

    // Update props without fixing error
    rerender(
      <WorkoutErrorBoundary>
        <ThrowError shouldThrow />
      </WorkoutErrorBoundary>
    )

    // Error UI should still be shown
    expect(screen.getByText('Workout Error')).toBeInTheDocument()
  })

  it('should handle errors with no message gracefully', () => {
    const ErrorWithNoMessage = () => {
      throw new Error()
    }

    render(
      <WorkoutErrorBoundary>
        <ErrorWithNoMessage />
      </WorkoutErrorBoundary>
    )

    expect(screen.getByText('Workout Error')).toBeInTheDocument()
    expect(screen.getByText(/Something went wrong/)).toBeInTheDocument()
  })

  it('should handle non-Error objects being thrown', () => {
    // React converts non-Error objects to Error instances
    const ThrowString = () => {
      // eslint-disable-next-line @typescript-eslint/no-throw-literal
      throw 'String error'
    }

    // Temporarily mock console.error for this specific test
    const originalConsoleError = console.error
    console.error = vi.fn()

    render(
      <WorkoutErrorBoundary>
        <ThrowString />
      </WorkoutErrorBoundary>
    )

    // Should still render error UI
    expect(screen.getByText('Workout Error')).toBeInTheDocument()
    expect(screen.getByText(/Something went wrong/)).toBeInTheDocument()

    // Restore console.error
    console.error = originalConsoleError
  })
})
