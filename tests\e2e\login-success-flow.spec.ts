import { test, expect } from '@playwright/test'

// Use mobile viewport by default
test.use({
  viewport: { width: 390, height: 844 }, // iPhone 13
  userAgent:
    'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
})

test.describe('Login Success Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login')
  })

  test('should show success screen after login and navigate to workout', async ({
    page,
  }) => {
    // Fill in login form
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'password123')

    // Submit form
    await page.click('button[type="submit"]')

    // Should show success screen
    await expect(page.getByTestId('login-success-screen')).toBeVisible({
      timeout: 10000,
    })

    // Should show welcome message
    await expect(page.getByText('Welcome to Dr. Muscle X!')).toBeVisible()

    // Should show progress indicator
    await expect(page.getByTestId('loading-progress')).toBeVisible()

    // Should eventually navigate to workout page (after prefetch and min display time)
    await page.waitForURL('/workout', { timeout: 15000 })

    // Verify we're on workout page
    expect(page.url()).toContain('/workout')
  })

  test('should allow skipping success screen after minimum time', async ({
    page,
  }) => {
    // Fill in login form
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'password123')

    // Submit form
    await page.click('button[type="submit"]')

    // Wait for success screen
    await expect(page.getByTestId('login-success-screen')).toBeVisible()

    // Skip button should not be visible immediately
    await expect(page.getByText('Skip')).not.toBeVisible()

    // Wait for minimum display duration (2.5s by default)
    await page.waitForTimeout(2600)

    // Skip button should now be visible
    await expect(page.getByText('Skip')).toBeVisible()

    // Click skip
    await page.click('text=Skip')

    // Should navigate to workout page
    await page.waitForURL('/workout')
  })

  test('should preserve return URL through login flow', async ({ page }) => {
    // Navigate to login with return URL
    await page.goto('/login?from=/workout/exercise/1')

    // Fill in login form
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'password123')

    // Submit form
    await page.click('button[type="submit"]')

    // Should show success screen
    await expect(page.getByTestId('login-success-screen')).toBeVisible()

    // Wait for automatic navigation
    await page.waitForURL('/workout/exercise/1', { timeout: 15000 })

    // Verify we're on the correct page
    expect(page.url()).toContain('/workout/exercise/1')
  })

  test('should handle login errors without showing success screen', async ({
    page,
  }) => {
    // Fill in login form with invalid credentials
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'wrongpassword')

    // Submit form
    await page.click('button[type="submit"]')

    // Should show error message, not success screen
    await expect(page.getByRole('alert')).toBeVisible()
    await expect(page.getByTestId('login-success-screen')).not.toBeVisible()

    // Should still be on login page
    expect(page.url()).toContain('/login')
  })

  test('should show progress updates during prefetch', async ({ page }) => {
    // Fill in login form
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'password123')

    // Submit form
    await page.click('button[type="submit"]')

    // Wait for success screen
    await expect(page.getByTestId('login-success-screen')).toBeVisible()

    // Check for progress updates
    const progressElement = page.getByTestId('loading-progress-fill')

    // Initial progress should be low
    const initialWidth = await progressElement.evaluate((el) => {
      return window.getComputedStyle(el).width
    })

    // Wait a bit
    await page.waitForTimeout(1000)

    // Progress should have increased
    const laterWidth = await progressElement.evaluate((el) => {
      return window.getComputedStyle(el).width
    })

    expect(parseInt(laterWidth)).toBeGreaterThan(parseInt(initialWidth))
  })

  test('should handle network errors gracefully', async ({ page }) => {
    // Simulate offline mode after loading the page
    await page.route('**/api/**', (route) => {
      route.abort('internetdisconnected')
    })

    // Fill in login form
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'password123')

    // Submit form
    await page.click('button[type="submit"]')

    // Should show error, not success screen
    await expect(page.getByRole('alert')).toBeVisible()
    await expect(page.getByTestId('login-success-screen')).not.toBeVisible()
  })
})

test.describe('Login Success Flow - Desktop', () => {
  test.use({
    viewport: { width: 1280, height: 720 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })

  test('should work correctly on desktop viewport', async ({ page }) => {
    await page.goto('/login')

    // Fill in login form
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'password123')

    // Submit form
    await page.click('button[type="submit"]')

    // Should show success screen
    await expect(page.getByTestId('login-success-screen')).toBeVisible()

    // Should eventually navigate to workout page
    await page.waitForURL('/workout', { timeout: 15000 })
  })
})
