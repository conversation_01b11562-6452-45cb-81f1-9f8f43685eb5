'use client'

import { useState, useEffect } from 'react'

export function DebugToggle() {
  const [debugMode, setDebugMode] = useState(false)
  const [showToggle, setShowToggle] = useState(false)

  useEffect(() => {
    const isDebug = localStorage.getItem('drmuscle-debug') === 'true'
    setDebugMode(isDebug)

    // Show toggle in development or if already enabled
    setShowToggle(process.env.NODE_ENV === 'development' || isDebug)
  }, [])

  const toggleDebug = () => {
    const newValue = !debugMode
    setDebugMode(newValue)
    localStorage.setItem('drmuscle-debug', newValue.toString())

    if (newValue) {
      if (process.env.NODE_ENV !== 'production') {
        // eslint-disable-next-line no-console
        console.log('Debug mode enabled - detailed logging activated')
      }
    } else if (process.env.NODE_ENV !== 'production') {
      // eslint-disable-next-line no-console
      console.log('Debug mode disabled')
    }
  }

  if (!showToggle) return null

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <button
        onClick={toggleDebug}
        className={`px-3 py-2 rounded-lg shadow-lg text-sm font-medium transition-colors ${
          debugMode
            ? 'bg-green-500 text-white hover:bg-green-600'
            : 'bg-gray-500 text-white hover:bg-gray-600'
        }`}
        title={debugMode ? 'Debug mode is ON' : 'Debug mode is OFF'}
      >
        <div className="flex items-center gap-2">
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
            />
          </svg>
          <span>Debug: {debugMode ? 'ON' : 'OFF'}</span>
        </div>
      </button>
    </div>
  )
}
