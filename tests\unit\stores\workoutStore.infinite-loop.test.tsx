import { renderHook } from '@testing-library/react'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useEffect, useState } from 'react'

describe('getCachedExerciseRecommendation infinite loop prevention', () => {
  beforeEach(() => {
    // Reset store before each test
    useWorkoutStore.setState({
      hasHydrated: true,
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {
          123: {
            Id: 123,
            ExerciseId: 123,
            Label: 'Test Exercise',
            WarmupsCount: 2,
            Series: 3,
            Reps: 10,
            Weight: { Lb: 100, Kg: 45.4 },
            ReferenceSetHistory: null,
            OneRM: { Lb: 120, Kg: 54.4 },
            Timer: 45,
          },
        },
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: { 123: Date.now() },
        },
      },
      cacheStats: {
        hits: 0,
        misses: 0,
        hitRate: 0,
        operationCount: 0,
        totalLatency: 0,
        averageLatency: 0,
      },
    })
  })

  it('should not cause infinite re-renders when called during render', () => {
    let renderCount = 0

    function TestComponent() {
      renderCount++

      // This simulates what happens in useSetScreenLogic
      const { getCachedExerciseRecommendation } = useWorkoutStore()
      const [recommendation] = useState(() => {
        // Call during render initialization
        return getCachedExerciseRecommendation(123)
      })

      useEffect(() => {
        // Also test calling in effect
        getCachedExerciseRecommendation(123)
      }, [getCachedExerciseRecommendation])

      return <div>{recommendation?.Label}</div>
    }

    const { rerender } = renderHook(() => <TestComponent />)

    // Force a few re-renders to see if it stabilizes
    rerender()
    rerender()

    // Should have rendered a reasonable number of times, not infinitely
    expect(renderCount).toBeLessThan(10)
  })

  it('should track cache operations without causing state updates during render', () => {
    const { getCachedExerciseRecommendation } = useWorkoutStore.getState()

    // Get initial stats
    const initialStats = useWorkoutStore.getState().cacheStats

    // Call the function (simulating during render)
    getCachedExerciseRecommendation(123)

    // Stats should not be updated immediately (synchronously)
    const immediateStats = useWorkoutStore.getState().cacheStats
    expect(immediateStats).toEqual(initialStats)

    // But should be updated asynchronously
    return new Promise((resolve) => {
      setTimeout(() => {
        const finalStats = useWorkoutStore.getState().cacheStats
        expect(finalStats.hits).toBe(1)
        expect(finalStats.operationCount).toBe(1)
        resolve(undefined)
      }, 10)
    })
  })
})
