import { test, expect } from '@playwright/test'

// Test account credentials
const TEST_EMAIL = '<EMAIL>'
const TEST_PASSWORD = 'Dr123456'

test.describe('Weight Recommendations', () => {
  test.beforeEach(async ({ page }) => {
    // Go to login page
    await page.goto('http://localhost:3000/login')
    
    // Login with test account
    await page.fill('input[type="email"]', TEST_EMAIL)
    await page.fill('input[type="password"]', TEST_PASSWORD)
    await page.click('button[type="submit"]')
    
    // Wait for navigation to workout page
    await page.waitForURL('**/workout', { timeout: 30000 })
    
    // Wait for workout to load
    await page.waitForSelector('[data-testid="workout-loading"]', { state: 'hidden', timeout: 30000 })
  })

  test('should display weight recommendations for exercises', async ({ page }) => {
    // Listen to console for debugging
    page.on('console', msg => {
      if (msg.text().includes('[API]') || msg.text().includes('[ExerciseInfo]')) {
        console.log('Browser console:', msg.text())
      }
    })

    // Click start workout button
    await page.click('button:has-text("Start Workout")')
    
    // Wait for exercise screen to load
    await page.waitForURL('**/exercise/**', { timeout: 10000 })
    
    // Wait for recommendation to load
    await page.waitForSelector('[data-testid="set-screen-loading"]', { state: 'hidden', timeout: 30000 })
    
    // Check if weight recommendation is displayed
    const exerciseInfo = await page.locator('.bg-bg-secondary').first()
    const targetText = await exerciseInfo.locator('p:has-text("Target:")').textContent()
    
    console.log('Target text found:', targetText)
    
    // For non-bodyweight exercises, we should see weight
    const hasWeight = targetText?.includes('×') && targetText?.includes('lbs')
    const hasNoWeightWarning = await page.locator('text="No weight recommendation"').count() > 0
    
    // Log the exercise details
    const exerciseTitle = await page.locator('h2.text-2xl').textContent()
    console.log('Exercise:', exerciseTitle)
    console.log('Has weight:', hasWeight)
    console.log('Has no weight warning:', hasNoWeightWarning)
    
    // Either we should have a weight recommendation or a warning message
    if (!hasWeight && !hasNoWeightWarning) {
      // Take a screenshot for debugging
      await page.screenshot({ path: 'weight-recommendation-issue.png', fullPage: true })
    }
    
    // At least one should be true
    expect(hasWeight || hasNoWeightWarning).toBeTruthy()
  })

  test('should load recommendations from API with correct parameters', async ({ page }) => {
    // Intercept API calls to verify correct parameters
    await page.route('**/api/Exercise/**', async (route, request) => {
      const url = request.url()
      const postData = request.postDataJSON()
      
      console.log('API Call:', {
        url,
        method: request.method(),
        body: postData
      })
      
      // Check if it's using the correct endpoint
      if (url.includes('WithoutWarmupsNew')) {
        console.log('✅ Using correct WithoutWarmupsNew endpoint')
        
        // Verify required parameters
        expect(postData).toHaveProperty('Username')
        expect(postData).toHaveProperty('ExerciseId')
        expect(postData).toHaveProperty('WorkoutId')
        expect(postData).toHaveProperty('SetStyle')
        expect(postData).toHaveProperty('IsFlexibility')
        expect(postData).toHaveProperty('VersionNo')
        
        // Check for the typo in the API
        expect(postData).toHaveProperty('IsStrengthPhashe') // Note the typo
      }
      
      // Continue with the request
      await route.continue()
    })
    
    // Start workout
    await page.click('button:has-text("Start Workout")')
    
    // Wait for API call to complete
    await page.waitForURL('**/exercise/**', { timeout: 10000 })
    await page.waitForTimeout(2000) // Give time for API calls
  })
})