import { test, expect } from '@playwright/test'

test.describe('Navigation and Routing @critical', () => {
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456'
  }

  test.describe('Unauthenticated Routes', () => {
    test('should redirect to login when accessing protected routes @critical', async ({ page }) => {
      // Try to access protected routes without login
      const protectedRoutes = ['/program', '/workout', '/profile', '/settings']

      for (const route of protectedRoutes) {
        await page.goto(route)
        await expect(page).toHaveURL('/login')
      }
    })

    test('should allow access to public routes @critical', async ({ page }) => {
      // Public routes should be accessible
      await page.goto('/')
      await expect(page).not.toHaveURL('/login')

      await page.goto('/login')
      await expect(page).toHaveURL('/login')
    })
  })

  test.describe('Authenticated Routes', () => {
    test.beforeEach(async ({ page }) => {
      // Login before each test
      await page.goto('/login')
      await page.getByLabel('Email').fill(TEST_USER.email)
      await page.getByLabel('Password').fill(TEST_USER.password)
      await page.getByRole('button', { name: /log in/i }).click()
      await page.waitForURL('/program')
    })

    test('should navigate between main sections @critical', async ({ page }) => {
      // Program page
      await expect(page).toHaveURL('/program')
      await expect(page.locator('h1')).toContainText(/program/i)

      // Navigate to workout
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await expect(page).toHaveURL('/workout')
      await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

      // Navigate back using browser
      await page.goBack()
      await expect(page).toHaveURL('/program')
    })

    test('should handle deep linking @critical', async ({ page }) => {
      // Direct navigation to workout
      await page.goto('/workout')
      await expect(page).toHaveURL('/workout')
      await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

      // Direct navigation to specific exercise (if supported)
      const exerciseId = await page.locator('[data-testid="exercise-item"]').first().getAttribute('data-exercise-id')
      if (exerciseId) {
        await page.goto(`/workout/exercise/${exerciseId}`)
        await expect(page.locator('[data-testid="set-input"]')).toBeVisible()
      }
    })

    test('should maintain navigation state across refresh @critical', async ({ page }) => {
      // Navigate to workout
      await page.goto('/workout')
      await expect(page).toHaveURL('/workout')

      // Refresh page
      await page.reload()

      // Should still be on workout page
      await expect(page).toHaveURL('/workout')
      await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()
    })
  })

  test.describe('Navigation Guards', () => {
    test.beforeEach(async ({ page }) => {
      // Login before each test
      await page.goto('/login')
      await page.getByLabel('Email').fill(TEST_USER.email)
      await page.getByLabel('Password').fill(TEST_USER.password)
      await page.getByRole('button', { name: /log in/i }).click()
      await page.waitForURL('/program')
    })

    test('should prevent navigation with unsaved changes @critical', async ({ page }) => {
      // Navigate to exercise
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Start filling form
      await page.locator('input[name="weight"]').fill('100')

      // Set up dialog handler
      page.on('dialog', dialog => {
        expect(dialog.message()).toContain(/unsaved|leave|discard/i)
        dialog.accept() // Accept to leave
      })

      // Try to navigate away
      await page.goBack()

      // Should navigate after confirmation
      await expect(page).toHaveURL('/workout')
    })

    test('should handle navigation during data loading @critical', async ({ page }) => {
      // Slow down network
      await page.route('**/*', route => {
        setTimeout(() => route.continue(), 500)
      })

      // Start navigation
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()

      // Quickly navigate back before load completes
      await page.goBack()

      // Should handle gracefully
      await expect(page).toHaveURL('/program')
      
      // No error should be visible
      await expect(page.locator('[role="alert"][data-type="error"]')).not.toBeVisible()
    })
  })

  test.describe('Back Button Navigation', () => {
    test.beforeEach(async ({ page }) => {
      // Login before each test
      await page.goto('/login')
      await page.getByLabel('Email').fill(TEST_USER.email)
      await page.getByLabel('Password').fill(TEST_USER.password)
      await page.getByRole('button', { name: /log in/i }).click()
      await page.waitForURL('/program')
    })

    test('should handle back button correctly @critical', async ({ page }) => {
      // Create navigation history
      await page.goto('/program')
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Navigate back through history
      await page.goBack()
      await expect(page).toHaveURL('/workout')

      await page.goBack()
      await expect(page).toHaveURL('/program')

      // Forward navigation should work
      await page.goForward()
      await expect(page).toHaveURL('/workout')
    })

    test('should show in-app back button on mobile @critical', async ({ page }) => {
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Look for back button in navigation
      const backButton = page.locator('[data-testid="back-button"], [aria-label="Go back"]')
      await expect(backButton).toBeVisible()

      // Click back button
      await backButton.click()
      await expect(page).toHaveURL('/program')
    })
  })

  test.describe('Route Transitions', () => {
    test.beforeEach(async ({ page }) => {
      // Login before each test
      await page.goto('/login')
      await page.getByLabel('Email').fill(TEST_USER.email)
      await page.getByLabel('Password').fill(TEST_USER.password)
      await page.getByRole('button', { name: /log in/i }).click()
      await page.waitForURL('/program')
    })

    test('should show loading indicators during navigation @critical', async ({ page }) => {
      // Slow down network to see transitions
      await page.route('**/*', route => {
        setTimeout(() => route.continue(), 500)
      })

      // Start navigation
      const workoutButton = page.getByRole('button', { name: /start workout|today's workout/i })
      await workoutButton.click()

      // Should show loading state
      const loadingIndicator = page.locator('[data-testid="navigation-loading"], [data-testid="page-loading"]')
      await expect(loadingIndicator).toBeVisible()

      // Wait for navigation to complete
      await page.waitForURL('/workout')

      // Loading indicator should disappear
      await expect(loadingIndicator).not.toBeVisible()
    })

    test('should maintain scroll position on back navigation @critical', async ({ page }) => {
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Wait for exercises to load
      await page.waitForSelector('[data-testid="exercise-item"]')

      // Scroll down
      await page.evaluate(() => window.scrollTo(0, 500))
      const scrollBefore = await page.evaluate(() => window.scrollY)

      // Navigate to exercise
      await page.locator('[data-testid="exercise-item"]').nth(2).click()

      // Go back
      await page.goBack()

      // Check scroll position is restored
      await page.waitForTimeout(500) // Wait for scroll restoration
      const scrollAfter = await page.evaluate(() => window.scrollY)
      expect(Math.abs(scrollAfter - scrollBefore)).toBeLessThan(50)
    })
  })

  test.describe('Error Route Handling', () => {
    test('should show 404 page for invalid routes @critical', async ({ page }) => {
      await page.goto('/this-route-does-not-exist')

      // Should show 404 or redirect
      const notFoundText = page.locator('text=/not found|404|doesn.*exist/i')
      const isRedirected = page.url().includes('/login') || page.url().includes('/')

      expect(await notFoundText.isVisible() || isRedirected).toBeTruthy()
    })

    test('should recover from navigation errors @critical', async ({ page }) => {
      // Login first
      await page.goto('/login')
      await page.getByLabel('Email').fill(TEST_USER.email)
      await page.getByLabel('Password').fill(TEST_USER.password)
      await page.getByRole('button', { name: /log in/i }).click()
      await page.waitForURL('/program')

      // Cause a navigation error by going to invalid workout
      await page.goto('/workout/invalid-id')

      // Should show error or redirect
      const errorMessage = page.locator('[data-testid="error-message"]')
      const isRedirected = page.url().includes('/workout') || page.url().includes('/program')

      expect(await errorMessage.isVisible() || isRedirected).toBeTruthy()

      // Should be able to navigate normally after error
      await page.goto('/program')
      await expect(page).toHaveURL('/program')
    })
  })

  test.describe('Mobile Navigation', () => {
    test.use({
      viewport: { width: 375, height: 667 },
      hasTouch: true,
      isMobile: true,
    })

    test.beforeEach(async ({ page }) => {
      // Login before each test
      await page.goto('/login')
      await page.getByLabel('Email').fill(TEST_USER.email)
      await page.getByLabel('Password').fill(TEST_USER.password)
      await page.getByRole('button', { name: /log in/i }).click()
      await page.waitForURL('/program')
    })

    test('should handle swipe navigation @critical', async ({ page }) => {
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Simulate swipe right (back gesture)
      await page.locator('body').dispatchEvent('touchstart', {
        touches: [{ clientX: 50, clientY: 400 }],
      })
      
      await page.locator('body').dispatchEvent('touchmove', {
        touches: [{ clientX: 250, clientY: 400 }],
      })
      
      await page.locator('body').dispatchEvent('touchend')

      // Some apps might show back navigation on swipe
      // Check if navigation occurred or gesture was recognized
      const backButton = page.locator('[data-testid="back-button"]')
      const isStillOnWorkout = page.url().includes('/workout')
      
      expect(await backButton.isVisible() || !isStillOnWorkout).toBeTruthy()
    })

    test('should have mobile-optimized navigation @critical', async ({ page }) => {
      // Check for mobile navigation elements
      const mobileNav = page.locator('[data-testid="mobile-nav"], [data-testid="bottom-nav"]')
      
      if (await mobileNav.isVisible()) {
        // Verify navigation items
        const navItems = mobileNav.locator('a, button')
        const count = await navItems.count()
        expect(count).toBeGreaterThan(0)

        // Check touch targets
        for (let i = 0; i < count; i++) {
          const item = navItems.nth(i)
          if (await item.isVisible()) {
            const box = await item.boundingBox()
            expect(box?.height).toBeGreaterThanOrEqual(44)
          }
        }
      }
    })
  })
})