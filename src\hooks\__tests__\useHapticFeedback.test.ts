import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useHapticFeedback } from '../useHapticFeedback'

describe('useHapticFeedback', () => {
  let mockVibrate: vi.MockedFunction<typeof navigator.vibrate>

  beforeEach(() => {
    // Mock navigator.vibrate
    mockVibrate = vi.fn().mockReturnValue(true)
    Object.defineProperty(navigator, 'vibrate', {
      value: mockVibrate,
      writable: true,
      configurable: true,
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('should provide haptic feedback functions', () => {
    const { result } = renderHook(() => useHapticFeedback())

    expect(result.current).toHaveProperty('light')
    expect(result.current).toHaveProperty('medium')
    expect(result.current).toHaveProperty('heavy')
    expect(result.current).toHaveProperty('success')
    expect(result.current).toHaveProperty('error')
    expect(result.current).toHaveProperty('isSupported')
  })

  it('should detect vibration support', () => {
    const { result } = renderHook(() => useHapticFeedback())

    expect(result.current.isSupported).toBe(true)
  })

  it('should handle missing vibration API', () => {
    // Remove vibrate function
    delete (navigator as Record<string, unknown>).vibrate

    const { result } = renderHook(() => useHapticFeedback())

    expect(result.current.isSupported).toBe(false)

    // Should not throw when calling feedback functions
    expect(() => {
      result.current.light()
      result.current.medium()
      result.current.heavy()
    }).not.toThrow()
  })

  it('should trigger light haptic feedback', () => {
    const { result } = renderHook(() => useHapticFeedback())

    act(() => {
      result.current.light()
    })

    expect(mockVibrate).toHaveBeenCalledWith(10)
  })

  it('should trigger medium haptic feedback', () => {
    const { result } = renderHook(() => useHapticFeedback())

    act(() => {
      result.current.medium()
    })

    expect(mockVibrate).toHaveBeenCalledWith(20)
  })

  it('should trigger heavy haptic feedback', () => {
    const { result } = renderHook(() => useHapticFeedback())

    act(() => {
      result.current.heavy()
    })

    expect(mockVibrate).toHaveBeenCalledWith(30)
  })

  it('should trigger success pattern', () => {
    const { result } = renderHook(() => useHapticFeedback())

    act(() => {
      result.current.success()
    })

    expect(mockVibrate).toHaveBeenCalledWith([10, 50, 20])
  })

  it('should trigger error pattern', () => {
    const { result } = renderHook(() => useHapticFeedback())

    act(() => {
      result.current.error()
    })

    expect(mockVibrate).toHaveBeenCalledWith([30, 100, 30])
  })

  it('should handle vibrate API failures gracefully', () => {
    mockVibrate.mockReturnValue(false)

    const { result } = renderHook(() => useHapticFeedback())

    expect(() => {
      result.current.light()
    }).not.toThrow()

    expect(mockVibrate).toHaveBeenCalled()
  })

  it('should not vibrate when API throws', () => {
    mockVibrate.mockImplementation(() => {
      throw new Error('Vibration not supported')
    })

    const { result } = renderHook(() => useHapticFeedback())

    expect(() => {
      result.current.heavy()
    }).not.toThrow()
  })
})
