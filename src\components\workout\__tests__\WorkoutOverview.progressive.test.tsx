import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { WorkoutOverview } from '../WorkoutOverview'
import * as useWorkoutModule from '@/hooks/useWorkout'
import type { WorkoutTemplateModel, ExerciseModel } from '@/types'
import {
  createMockUseWorkoutReturn,
  createMockWorkoutTemplateGroup,
  createMockExerciseWorkSetsFromExercise,
  createMockExerciseWorkSetsModel,
} from './WorkoutOverview.test.helpers'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

const mockWorkoutTemplate: WorkoutTemplateModel = {
  Id: 1,
  Label: 'Upper Body Day',
  IsSystemExercise: true,
  UserId: '',
  Exercises: [
    { Id: 1, Label: 'Bench Press' } as ExerciseModel,
    { Id: 2, Label: 'Shoulder Press' } as ExerciseModel,
    { Id: 3, Label: 'Dumbbell Rows' } as ExerciseModel,
    { Id: 4, Label: 'Bicep Curls' } as ExerciseModel,
    { Id: 5, Label: 'Tricep Extensions' } as ExerciseModel,
  ],
  WorkoutSettingsModel: {},
}

describe('WorkoutOverview - Progressive Data Loading', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should show workout structure immediately with partial data', () => {
    // Create exercises in the new format
    const exercises =
      mockWorkoutTemplate.Exercises?.map((ex) =>
        createMockExerciseWorkSetsFromExercise(ex)
      ) || []

    // Mock hook to return partial data (no exercise details yet)
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(
      createMockUseWorkoutReturn({
        todaysWorkout: [
          {
            Id: 1,
            Label: 'Test Program',
            WorkoutTemplates: [mockWorkoutTemplate],
            IsFeaturedProgram: false,
            UserId: '',
            IsSystemExercise: true,
            RequiredWorkoutToLevelUp: 5,
            ProgramId: 1,
          },
        ],
        exercises, // Provide exercises in new format
        exerciseWorkSetsModels: exercises, // Add this property
        isLoadingWorkout: false,
        workoutError: null,
        startWorkout: vi.fn(),
        currentWorkout: null,
        userProgramInfo: null,
        hasInitialData: true,
        isLoadingFresh: true,
        loadingStates: {
          programInfo: false,
          userWorkout: false,
          recommendation: true, // Still loading recommendations
        },
      })
    )

    render(<WorkoutOverview />)

    // Should show all exercises immediately

    // All exercises should be visible
    expect(screen.getByText('Bench Press')).toBeInTheDocument()
    expect(screen.getByText('Shoulder Press')).toBeInTheDocument()
    expect(screen.getByText('Dumbbell Rows')).toBeInTheDocument()
    expect(screen.getByText('Bicep Curls')).toBeInTheDocument()
    expect(screen.getByText('Tricep Extensions')).toBeInTheDocument()

    // Should show loading indicator for recommendations
    expect(screen.getByTestId('recommendations-loading')).toBeInTheDocument()
  })

  it('should update UI progressively as data arrives without layout shift', async () => {
    // Create exercises in the new format
    const exercises =
      mockWorkoutTemplate.Exercises?.map((ex) =>
        createMockExerciseWorkSetsFromExercise(ex)
      ) || []

    let mockHookReturn = createMockUseWorkoutReturn({
      todaysWorkout: [
        {
          Id: 1,
          Label: 'Test Program',
          WorkoutTemplates: [mockWorkoutTemplate],
          IsFeaturedProgram: false,
          UserId: '',
          IsSystemExercise: true,
          RequiredWorkoutToLevelUp: 5,
          ProgramId: 1,
        },
      ],
      exercises,
      exerciseWorkSetsModels: exercises,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      currentWorkout: null,
      userProgramInfo: null,
      hasInitialData: true,
      isLoadingFresh: false,
      loadingStates: {
        programInfo: false,
        userWorkout: false,
        recommendation: true,
      },
    })

    const useWorkoutSpy = vi
      .spyOn(useWorkoutModule, 'useWorkout')
      .mockReturnValue(mockHookReturn)

    const { rerender } = render(<WorkoutOverview />)

    // Check initial layout
    const exerciseCards = [
      screen.getByText('Bench Press'),
      screen.getByText('Shoulder Press'),
      screen.getByText('Dumbbell Rows'),
      screen.getByText('Bicep Curls'),
      screen.getByText('Tricep Extensions'),
    ]
    expect(exerciseCards).toHaveLength(5)

    // Simulate recommendations loading complete
    mockHookReturn = {
      ...mockHookReturn,
      loadingStates: {
        programInfo: false,
        userWorkout: false,
        recommendation: false, // Recommendations loaded
      },
    }
    useWorkoutSpy.mockReturnValue(mockHookReturn)

    rerender(<WorkoutOverview />)

    // Exercise cards should still be in the same positions (no layout shift)
    const updatedCards = [
      screen.getByText('Bench Press'),
      screen.getByText('Shoulder Press'),
      screen.getByText('Dumbbell Rows'),
      screen.getByText('Bicep Curls'),
      screen.getByText('Tricep Extensions'),
    ]
    expect(updatedCards).toHaveLength(5)

    // Loading indicator should be gone
    expect(
      screen.queryByTestId('recommendations-loading')
    ).not.toBeInTheDocument()
  })

  it('should allow user interaction during progressive loading', async () => {
    const startWorkoutMock = vi.fn()
    const mockWorkoutGroup = createMockWorkoutTemplateGroup({
      WorkoutTemplates: [mockWorkoutTemplate],
    })

    // Create exercises in the new format
    const exercises =
      mockWorkoutTemplate.Exercises?.map((ex) =>
        createMockExerciseWorkSetsFromExercise(ex)
      ) || []

    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(
      createMockUseWorkoutReturn({
        todaysWorkout: [mockWorkoutGroup],
        exercises,
        exerciseWorkSetsModels: exercises,
        isLoadingWorkout: false,
        workoutError: null,
        startWorkout: startWorkoutMock,
        currentWorkout: mockWorkoutTemplate,
        userProgramInfo: null,
        hasInitialData: true,
        isLoadingFresh: true,
        loadingStates: {
          programInfo: false,
          userWorkout: false,
          recommendation: true, // Still loading
        },
      })
    )

    render(<WorkoutOverview />)
    const user = userEvent.setup()

    // User should be able to start workout even while recommendations are loading
    const startButton = screen.getByTestId('start-workout-button')
    expect(startButton).toBeEnabled()

    await user.click(startButton)

    expect(startWorkoutMock).toHaveBeenCalledTimes(1)

    // User should be able to click on exercises
    const benchPressCard = screen.getByText('Bench Press')
    await user.click(benchPressCard)

    expect(startWorkoutMock).toHaveBeenCalledTimes(2) // Called again when clicking exercise
  })

  it('should preserve scroll position when data updates', async () => {
    const { container } = render(<WorkoutOverview />)

    // Get scrollable container
    const scrollContainer = container.querySelector('.overflow-y-auto')
    expect(scrollContainer).toBeTruthy()

    // Simulate scrolling down
    const initialScrollTop = 200
    Object.defineProperty(scrollContainer, 'scrollTop', {
      writable: true,
      value: initialScrollTop,
    })

    // Create exercises in the new format
    const exercises =
      mockWorkoutTemplate.Exercises?.map((ex) =>
        createMockExerciseWorkSetsFromExercise(ex)
      ) || []

    // Trigger a re-render with updated data
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(
      createMockUseWorkoutReturn({
        todaysWorkout: [
          {
            Id: 1,
            Label: 'Test Program',
            WorkoutTemplates: [mockWorkoutTemplate],
            IsFeaturedProgram: false,
            UserId: '',
            IsSystemExercise: true,
            RequiredWorkoutToLevelUp: 5,
            ProgramId: 1,
          },
        ],
        exercises,
        exerciseWorkSetsModels: exercises,
        isLoadingWorkout: false,
        workoutError: null,
        startWorkout: vi.fn(),
        currentWorkout: null,
        userProgramInfo: null,
        hasInitialData: true,
        isLoadingFresh: false,
        loadingStates: {
          programInfo: false,
          userWorkout: false,
          recommendation: false, // Now loaded
        },
      })
    )

    // Scroll position should be preserved
    expect(scrollContainer?.scrollTop).toBe(initialScrollTop)
  })

  it('should show skeleton loaders only for missing data', () => {
    // Only first 3 exercises have loaded
    const partialExercises =
      mockWorkoutTemplate.Exercises?.slice(0, 3).map((ex) =>
        createMockExerciseWorkSetsFromExercise(ex)
      ) || []

    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(
      createMockUseWorkoutReturn({
        todaysWorkout: [
          {
            Id: 1,
            Label: 'Test Program',
            WorkoutTemplates: [mockWorkoutTemplate],
            IsFeaturedProgram: false,
            UserId: '',
            IsSystemExercise: true,
            RequiredWorkoutToLevelUp: 5,
            ProgramId: 1,
          },
        ],
        exercises: partialExercises,
        exerciseWorkSetsModels: partialExercises,
        isLoadingWorkout: true,
        workoutError: null,
        startWorkout: vi.fn(),
        currentWorkout: null,
        userProgramInfo: null,
        hasInitialData: true,
        isLoadingFresh: false,
        loadingStates: {
          programInfo: false,
          userWorkout: true, // Still loading more exercises
          recommendation: false,
        },
        expectedExerciseCount: 5, // We know there should be 5 exercises total
      })
    )

    render(<WorkoutOverview />)

    // Should show loaded exercises
    expect(screen.getByText('Bench Press')).toBeInTheDocument()
    expect(screen.getByText('Shoulder Press')).toBeInTheDocument()
    expect(screen.getByText('Dumbbell Rows')).toBeInTheDocument()

    // Should show skeleton loaders for missing exercises
    const skeletons = screen.getAllByTestId('exercise-item-skeleton')
    expect(skeletons).toHaveLength(2) // 5 total - 3 loaded = 2 skeletons
  })

  it('should maintain 60fps scrolling performance during progressive loading', () => {
    // This is more of a performance regression test
    // In real implementation, we'd use React DevTools Profiler

    // Create 20 exercises
    const manyExercises = Array.from({ length: 20 }, (_, i) =>
      createMockExerciseWorkSetsModel({
        Id: i + 1,
        Label: `Exercise ${i + 1}`,
      })
    )

    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(
      createMockUseWorkoutReturn({
        todaysWorkout: [
          {
            Id: 1,
            Label: 'Test Program',
            WorkoutTemplates: [
              {
                ...mockWorkoutTemplate,
                Exercises: Array.from(
                  { length: 20 },
                  (_, i) =>
                    ({
                      Id: i + 1,
                      Label: `Exercise ${i + 1}`,
                    }) as ExerciseModel
                ),
              },
            ],
            IsFeaturedProgram: false,
            UserId: '',
            IsSystemExercise: true,
            RequiredWorkoutToLevelUp: 5,
            ProgramId: 1,
          },
        ],
        exercises: manyExercises,
        exerciseWorkSetsModels: manyExercises,
        isLoadingWorkout: false,
        workoutError: null,
        startWorkout: vi.fn(),
        currentWorkout: null,
        userProgramInfo: null,
        hasInitialData: true,
        isLoadingFresh: true,
        loadingStates: {
          programInfo: false,
          userWorkout: false,
          recommendation: true, // Loading recommendations
        },
      })
    )

    const startTime = performance.now()
    render(<WorkoutOverview />)
    const renderTime = performance.now() - startTime

    // Initial render should be fast even with many items
    expect(renderTime).toBeLessThan(16.67) // 60fps = 16.67ms per frame

    // All exercises should be rendered
    expect(screen.getAllByText(/Exercise \d+/)).toHaveLength(20)
  })
})
