import { test, expect } from '@playwright/test'

test.describe('Login Redirect Delay', () => {
  test('should show root page animation for 0.6 seconds before redirecting to login', async ({
    page,
  }) => {
    // Given - user is not authenticated
    await page.goto('/')

    // Then - should see the root page content immediately
    await expect(page.getByText('Dr. Muscle X')).toBeVisible()
    await expect(
      page.getByText("World's Fastest AI Personal Trainer")
    ).toBeVisible()

    // And - should still be on root page (not redirected yet)
    expect(page.url()).toContain('/')
    expect(page.url()).not.toContain('/login')

    // When - wait for almost 0.6 seconds
    await page.waitForTimeout(500)

    // Then - should still be on root page
    expect(page.url()).toContain('/')
    expect(page.url()).not.toContain('/login')

    // When - wait for redirect to complete
    await page.waitForURL('**/login', { timeout: 5000 })

    // Then - should be on login page
    expect(page.url()).toContain('/login')
  })

  test('should redirect immediately to program page when authenticated', async ({
    page,
  }) => {
    // Given - user is authenticated
    await page.context().addCookies([
      {
        name: 'auth-storage',
        value: JSON.stringify({
          state: {
            user: { id: '1', email: '<EMAIL>' },
            token: 'test-token',
            isAuthenticated: true,
            isLoading: false,
            hasHydrated: true,
          },
        }),
        domain: 'localhost',
        path: '/',
      },
    ])

    // When - navigate to root
    await page.goto('/')

    // Then - should redirect to program page immediately
    await page.waitForURL('**/program', { timeout: 2000 })
    expect(page.url()).toContain('/program')
  })
})
