import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act, waitFor } from '@testing-library/react'
import { useErrorHandler } from '@/hooks/useErrorHandler'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'

// Create wrapper for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useErrorHandler', () => {
  const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {})
  
  beforeEach(() => {
    vi.clearAllMocks()
    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true
    })
  })

  afterEach(() => {
    mockConsoleError.mockRestore()
  })

  describe('Error Categorization', () => {
    it('should categorize network errors correctly', () => {
      // Given
      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      const networkError = new Error('Network request failed')
      networkError.name = 'NetworkError'

      // When
      act(() => {
        result.current.handleError(networkError)
      })

      // Then
      expect(result.current.errorState).toEqual({
        type: 'network',
        message: 'Connection issue. Please check your internet.',
        canRetry: true,
        error: networkError
      })
    })

    it('should categorize authentication errors correctly', () => {
      // Given
      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      const authError = new Error('Unauthorized')
      authError.name = 'AuthenticationError'

      // When
      act(() => {
        result.current.handleError(authError)
      })

      // Then
      expect(result.current.errorState).toEqual({
        type: 'auth',
        message: 'Session expired. Please log in again.',
        canRetry: false,
        error: authError
      })
    })

    it('should categorize validation errors correctly', () => {
      // Given
      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      const validationError = new Error('Invalid input')
      validationError.name = 'ValidationError'

      // When
      act(() => {
        result.current.handleError(validationError)
      })

      // Then
      expect(result.current.errorState).toEqual({
        type: 'validation',
        message: 'Invalid input',
        canRetry: false,
        error: validationError
      })
    })

    it('should handle unknown errors with generic message', () => {
      // Given
      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      const unknownError = new Error('Something broke')

      // When
      act(() => {
        result.current.handleError(unknownError)
      })

      // Then
      expect(result.current.errorState).toEqual({
        type: 'unknown',
        message: 'An unexpected error occurred. Please try again.',
        canRetry: true,
        error: unknownError
      })
    })
  })

  describe('Retry Mechanism', () => {
    it('should retry with exponential backoff', async () => {
      // Given
      const mockOperation = vi.fn()
        .mockRejectedValueOnce(new Error('First failure'))
        .mockRejectedValueOnce(new Error('Second failure'))
        .mockResolvedValueOnce('Success')

      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      // When
      let finalResult
      await act(async () => {
        finalResult = await result.current.retryWithBackoff(mockOperation, {
          maxRetries: 3,
          initialDelay: 10
        })
      })

      // Then
      expect(mockOperation).toHaveBeenCalledTimes(3)
      expect(finalResult).toBe('Success')
    })

    it('should stop retrying after max attempts', async () => {
      // Given
      const mockOperation = vi.fn().mockRejectedValue(new Error('Always fails'))
      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      // When/Then
      await expect(
        result.current.retryWithBackoff(mockOperation, {
          maxRetries: 2,
          initialDelay: 10
        })
      ).rejects.toThrow('Always fails')

      expect(mockOperation).toHaveBeenCalledTimes(3) // Initial + 2 retries
    })

    it('should not retry non-retryable errors', async () => {
      // Given
      const authError = new Error('Unauthorized')
      authError.name = 'AuthenticationError'
      
      const mockOperation = vi.fn().mockRejectedValue(authError)
      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      // When/Then
      await expect(
        result.current.retryWithBackoff(mockOperation)
      ).rejects.toThrow('Unauthorized')

      expect(mockOperation).toHaveBeenCalledTimes(1) // No retries
    })
  })

  describe('Offline Handling', () => {
    it('should detect offline status', () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: false })
      
      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      // When
      act(() => {
        result.current.handleError(new Error('Network request failed'))
      })

      // Then
      expect(result.current.isOffline).toBe(true)
      expect(result.current.errorState?.message).toContain('offline')
    })

    it('should queue operations when offline', async () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: false })
      
      const mockOperation = vi.fn().mockResolvedValue('Success')
      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      // When
      act(() => {
        result.current.queueForRetry('test-operation', mockOperation)
      })

      // Then
      expect(result.current.retryQueue).toHaveLength(1)
      expect(mockOperation).not.toHaveBeenCalled()
    })

    it('should process retry queue when coming online', async () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: false })
      
      const mockOperation = vi.fn().mockResolvedValue('Success')
      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      // Queue operation while offline
      act(() => {
        result.current.queueForRetry('test-operation', mockOperation)
      })

      // When - come back online
      Object.defineProperty(navigator, 'onLine', { value: true })
      
      act(() => {
        window.dispatchEvent(new Event('online'))
      })

      // Then
      await waitFor(() => {
        expect(mockOperation).toHaveBeenCalled()
        expect(result.current.retryQueue).toHaveLength(0)
      })
    })
  })

  describe('Error Recovery', () => {
    it('should clear error state', () => {
      // Given
      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      act(() => {
        result.current.handleError(new Error('Test error'))
      })

      expect(result.current.errorState).not.toBeNull()

      // When
      act(() => {
        result.current.clearError()
      })

      // Then
      expect(result.current.errorState).toBeNull()
    })

    it('should provide recovery suggestions for common errors', () => {
      // Given
      const { result } = renderHook(() => useErrorHandler(), {
        wrapper: createWrapper()
      })

      // Network error
      act(() => {
        result.current.handleError(new Error('ERR_NETWORK'))
      })
      expect(result.current.errorState?.recovery).toContain('Check your internet connection')

      // Storage quota error
      act(() => {
        result.current.handleError(new Error('QuotaExceededError'))
      })
      expect(result.current.errorState?.recovery).toContain('Clear some space')
    })
  })

  describe('Error Notifications', () => {
    it('should show toast notification for errors', () => {
      // Given
      const mockShowToast = vi.fn()
      const { result } = renderHook(() => useErrorHandler({
        showToast: mockShowToast
      }), {
        wrapper: createWrapper()
      })

      // When
      act(() => {
        result.current.handleError(new Error('Test error'))
      })

      // Then
      expect(mockShowToast).toHaveBeenCalledWith({
        type: 'error',
        message: expect.any(String),
        duration: 5000
      })
    })

    it('should not show toast for silent errors', () => {
      // Given
      const mockShowToast = vi.fn()
      const { result } = renderHook(() => useErrorHandler({
        showToast: mockShowToast
      }), {
        wrapper: createWrapper()
      })

      // When
      act(() => {
        result.current.handleError(new Error('Test error'), { silent: true })
      })

      // Then
      expect(mockShowToast).not.toHaveBeenCalled()
    })
  })

  describe('Workout Context Preservation', () => {
    it('should preserve workout data during errors', () => {
      // Given
      const workoutData = {
        exerciseId: 123,
        setNumber: 3,
        weight: 100,
        reps: 10
      }

      const { result } = renderHook(() => useErrorHandler({
        workoutContext: workoutData
      }), {
        wrapper: createWrapper()
      })

      // When
      act(() => {
        result.current.handleError(new Error('Save failed'))
      })

      // Then
      expect(result.current.errorState?.context).toEqual(workoutData)
      expect(result.current.errorState?.preservedData).toEqual(workoutData)
    })

    it('should provide workout recovery options', () => {
      // Given
      const { result } = renderHook(() => useErrorHandler({
        workoutContext: { exerciseId: 123 }
      }), {
        wrapper: createWrapper()
      })

      // When
      act(() => {
        result.current.handleError(new Error('Save failed'))
      })

      // Then
      expect(result.current.errorState?.recovery).toContain('Continue workout')
      expect(result.current.errorState?.recovery).toContain('Save locally')
    })
  })
})