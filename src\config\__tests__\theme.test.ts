import { describe, it, expect } from 'vitest'
import { themeConfig } from '../theme'

describe('Theme Configuration', () => {
  it('should have subtle-depth as the default theme', () => {
    expect(themeConfig.defaultTheme).toBe('subtle-depth')
  })

  it('should have subtle-depth theme defined', () => {
    expect(themeConfig.themes['subtle-depth']).toBeDefined()
    expect(themeConfig.themes['subtle-depth'].name).toBe('Subtle Depth')
  })

  it('should have correct subtle-depth colors', () => {
    const subtleDepthTheme = themeConfig.themes['subtle-depth']
    expect(subtleDepthTheme.colors.bg.primary).toBe('#0a0a0b')
    expect(subtleDepthTheme.colors.brand.primary).toBe('#d4af37')
    expect(subtleDepthTheme.colors.text.primary).toBe('#ffffff')
  })

  it('should have glassmorphism theme still available', () => {
    expect(themeConfig.themes.glassmorphism).toBeDefined()
    expect(themeConfig.themes.glassmorphism.name).toBe('Glassmorphism')
  })
})
