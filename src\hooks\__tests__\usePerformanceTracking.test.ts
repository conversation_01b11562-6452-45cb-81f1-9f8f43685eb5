import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook } from '@testing-library/react'
import { usePerformanceTracking } from '../usePerformanceTracking'
import { PerformanceMonitor, PerformanceMarks } from '@/utils/performance'

// Mock the performance utility
vi.mock('@/utils/performance', () => ({
  PerformanceMonitor: {
    mark: vi.fn(),
    measure: vi.fn(),
    reportKeyMetrics: vi.fn(),
  },
  PerformanceMarks: {
    LOGIN_START: 'login-start',
    LOGIN_SUCCESS: 'login-success',
    SUCCESS_SCREEN_START: 'success-screen-start',
    SUCCESS_SCREEN_COMPLETE: 'success-screen-complete',
    DATA_FETCH_START: 'data-fetch-start',
    DATA_FETCH_COMPLETE: 'data-fetch-complete',
    WORKOUT_PAGE_INTERACTIVE: 'workout-page-interactive',
  },
}))

describe('usePerformanceTracking', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should mark login start', () => {
    const { result } = renderHook(() => usePerformanceTracking())

    result.current.markLoginStart()

    expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
      PerformanceMarks.LOGIN_START
    )
  })

  it('should mark login success', () => {
    const { result } = renderHook(() => usePerformanceTracking())

    result.current.markLoginSuccess()

    expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
      PerformanceMarks.LOGIN_SUCCESS
    )
  })

  it('should mark success screen start', () => {
    const { result } = renderHook(() => usePerformanceTracking())

    result.current.markSuccessScreenStart()

    expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
      PerformanceMarks.SUCCESS_SCREEN_START
    )
  })

  it('should mark success screen complete', () => {
    const { result } = renderHook(() => usePerformanceTracking())

    result.current.markSuccessScreenComplete()

    expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
      PerformanceMarks.SUCCESS_SCREEN_COMPLETE
    )
  })

  it('should mark data fetch start', () => {
    const { result } = renderHook(() => usePerformanceTracking())

    result.current.markDataFetchStart()

    expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
      PerformanceMarks.DATA_FETCH_START
    )
  })

  it('should mark data fetch complete', () => {
    const { result } = renderHook(() => usePerformanceTracking())

    result.current.markDataFetchComplete()

    expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
      PerformanceMarks.DATA_FETCH_COMPLETE
    )
  })

  it('should mark workout page interactive', () => {
    const { result } = renderHook(() => usePerformanceTracking())

    result.current.markWorkoutPageInteractive()

    expect(PerformanceMonitor.mark).toHaveBeenCalledWith(
      PerformanceMarks.WORKOUT_PAGE_INTERACTIVE
    )
  })

  it('should report metrics', () => {
    const mockMetrics = {
      loginToInteractive: 2500,
      successScreenDuration: 2000,
    }
    vi.mocked(PerformanceMonitor.reportKeyMetrics).mockReturnValue(mockMetrics)

    const { result } = renderHook(() => usePerformanceTracking())

    const metrics = result.current.reportMetrics()

    expect(PerformanceMonitor.reportKeyMetrics).toHaveBeenCalled()
    expect(metrics).toEqual(mockMetrics)
  })

  it('should provide all functions', () => {
    const { result } = renderHook(() => usePerformanceTracking())

    expect(result.current).toHaveProperty('markLoginStart')
    expect(result.current).toHaveProperty('markLoginSuccess')
    expect(result.current).toHaveProperty('markSuccessScreenStart')
    expect(result.current).toHaveProperty('markSuccessScreenComplete')
    expect(result.current).toHaveProperty('markDataFetchStart')
    expect(result.current).toHaveProperty('markDataFetchComplete')
    expect(result.current).toHaveProperty('markWorkoutPageInteractive')
    expect(result.current).toHaveProperty('reportMetrics')
  })
})
