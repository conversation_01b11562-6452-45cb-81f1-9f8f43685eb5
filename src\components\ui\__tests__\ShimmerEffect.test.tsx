import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ShimmerEffect, ShimmerOverlay } from '../ShimmerEffect'

describe('ShimmerEffect', () => {
  it('should render with default props', () => {
    render(<ShimmerEffect />)

    const shimmer = screen.getByRole('status', { name: 'Loading' })
    expect(shimmer).toBeInTheDocument()
    expect(shimmer).toHaveClass('shimmer-wrapper')
  })

  it('should apply custom dimensions', () => {
    render(<ShimmerEffect width="200px" height="50px" />)

    const shimmer = screen.getByRole('status')
    expect(shimmer).toHaveStyle({
      width: '200px',
      height: '50px',
    })
  })

  it('should apply custom className', () => {
    render(<ShimmerEffect className="custom-shimmer" />)

    const shimmer = screen.getByRole('status')
    expect(shimmer).toHaveClass('shimmer-wrapper', 'custom-shimmer')
  })

  it('should use custom aria label', () => {
    render(<ShimmerEffect ariaLabel="Loading stats" />)

    const shimmer = screen.getByRole('status', { name: 'Loading stats' })
    expect(shimmer).toBeInTheDocument()
  })

  it('should have shimmer effect element', () => {
    const { container } = render(<ShimmerEffect />)

    const shimmerEffect = container.querySelector('.shimmer-effect')
    expect(shimmerEffect).toBeInTheDocument()
    expect(shimmerEffect).toHaveStyle({
      position: 'absolute',
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
    })
  })
})

describe('ShimmerOverlay', () => {
  it('should not render when show is false', () => {
    const { container } = render(<ShimmerOverlay show={false} />)

    expect(container.firstChild).toBeNull()
  })

  it('should render when show is true', () => {
    render(<ShimmerOverlay show />)

    const overlay = screen.getByTestId('shimmer-overlay')
    expect(overlay).toBeInTheDocument()
    expect(overlay).toHaveClass('opacity-100')
  })

  it('should apply custom duration and delay', () => {
    render(<ShimmerOverlay show duration={2} delay={100} />)

    const shimmer = screen.getByRole('status', { name: 'Loading value' })
    expect(shimmer).toBeInTheDocument()
  })

  it('should apply custom className', () => {
    render(<ShimmerOverlay show className="custom-overlay" />)

    const overlay = screen.getByTestId('shimmer-overlay')
    expect(overlay).toHaveClass('custom-overlay')
  })

  it('should have pointer-events-none class', () => {
    render(<ShimmerOverlay show />)

    const overlay = screen.getByTestId('shimmer-overlay')
    expect(overlay).toHaveClass('pointer-events-none')
  })

  it('should have absolute positioning', () => {
    render(<ShimmerOverlay show />)

    const overlay = screen.getByTestId('shimmer-overlay')
    expect(overlay).toHaveClass('absolute', 'inset-0')
  })
})
