import { describe, it, expect, beforeEach } from 'vitest'
import { apiClient, setAuthToken, clearAuthToken } from '@/api/client'

describe('Authorization Header Integration', () => {
  beforeEach(() => {
    clearAuthToken()
  })

  it('should set Authorization header when setAuthToken is called', () => {
    // Given a token
    const token = 'test-bearer-token'

    // When setAuthToken is called
    setAuthToken(token)

    // Then the Authorization header should be set with Bearer prefix
    expect(apiClient.defaults.headers.common['Authorization']).toBe(`Bearer ${token}`)
  })

  it('should clear Authorization header when clearAuthToken is called', () => {
    // Given a token is set
    setAuthToken('test-token')

    // When clearAuthToken is called
    clearAuthToken()

    // Then the Authorization header should be undefined
    expect(apiClient.defaults.headers.common['Authorization']).toBeUndefined()
  })

  it('should include Authorization header in request interceptor', async () => {
    // Given a token is set
    const token = 'test-api-token'
    setAuthToken(token)

    // Create a mock config that will be passed through the interceptor
    const mockConfig = {
      method: 'get',
      url: '/test-endpoint',
      headers: {}
    }

    // Get the request interceptor (first interceptor added)
    const requestInterceptor = apiClient.interceptors.request.handlers[0]

    // When the interceptor processes the config
    const processedConfig = await requestInterceptor.fulfilled(mockConfig)

    // Then the Authorization header should be present in defaults
    expect(apiClient.defaults.headers.common['Authorization']).toBe(`Bearer ${token}`)
    
    // The interceptor itself doesn't add the header, axios merges defaults
    // So we verify the default is set correctly
    expect(processedConfig).toBe(mockConfig) // Config passes through unchanged
  })
})