import { test, expect, Page } from '@playwright/test'

test.describe('API Error Handling - Network and Recovery Tests', () => {
  let page: Page
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p }) => {
    page = p
  })

  test('should implement exponential backoff for retries', async () => {
    const timestamps: number[] = []
    let attemptCount = 0

    await page.route('**/api/**', (route) => {
      timestamps.push(Date.now())
      attemptCount++

      if (attemptCount < 3) {
        route.fulfill({ status: 503 })
      } else {
        route.fulfill({ status: 200, json: { success: true } })
      }
    })

    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    // Wait for retries to complete
    await page.waitForTimeout(5000)

    // Verify exponential backoff
    if (timestamps.length >= 3) {
      const delay1 = timestamps[1] - timestamps[0]
      const delay2 = timestamps[2] - timestamps[1]
      expect(delay2).toBeGreaterThan(delay1 * 1.5) // Exponential increase
    }
  })

  test('should queue failed requests for retry when online', async () => {
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Block save requests initially
    let blocked = true
    await page.route('**/SaveWorkoutLog', (route) => {
      if (blocked) {
        route.abort('internetdisconnected')
      } else {
        route.fulfill({ status: 200, json: { success: true } })
      }
    })

    // Try to save while "offline"
    await page.goto('/workout')
    await page.click('[data-testid="exercise-item"]')
    await page.fill('input[name="weight"]', '100')
    await page.fill('input[name="reps"]', '10')
    await page.click('button:has-text("Save")')

    // Should show queued indicator
    await expect(page.locator('[data-testid="sync-queued"]')).toBeVisible()

    // "Come back online"
    blocked = false

    // Trigger sync
    await page.click('[data-testid="sync-now"]')

    // Should sync successfully
    await expect(page.locator('[data-testid="sync-success"]')).toBeVisible()
  })

  test('should provide user-friendly error messages', async () => {
    const errorScenarios = [
      { status: 400, expected: 'Please check your input and try again' },
      { status: 401, expected: 'Please log in again' },
      { status: 403, expected: "You don't have permission" },
      { status: 404, expected: 'The requested data was not found' },
      { status: 500, expected: 'Something went wrong on our end' },
      { status: 503, expected: 'Service temporarily unavailable' },
    ]

    // Test each error scenario separately to avoid await in loop
    await Promise.all(
      errorScenarios.map(async (scenario) => {
        const testPage = await page.context().newPage()

        await testPage.route('**/api/**', (route) => {
          route.fulfill({
            status: scenario.status,
            json: { error: 'test_error' },
          })
        })

        await testPage.goto('/login')
        await testPage.fill('input[type="email"]', TEST_USER.email)
        await testPage.fill('input[type="password"]', TEST_USER.password)
        await testPage.click('button[type="submit"]')

        await expect(testPage.locator('[role="alert"]')).toContainText(
          new RegExp(scenario.expected, 'i')
        )

        await testPage.close()
      })
    )
  })

  test('should handle network errors differently from API errors', async () => {
    await page.goto('/login')

    // Network error (no response)
    await page.route('**/api/token', (route) => route.abort('failed'))

    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    // Should show network-specific error
    await expect(page.locator('[role="alert"]')).toContainText(
      /network error|connection failed/i
    )

    // Should offer offline mode if available
    const offlineButton = page.locator('button:has-text("Continue Offline")')
    if (await offlineButton.isVisible()) {
      await expect(offlineButton).toBeEnabled()
    }
  })

  test('should handle recommendation service unavailable', async () => {
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    await page.route('**/GetRecommendation*', (route) => {
      route.fulfill({
        status: 503,
        json: {
          error: 'service_unavailable',
          error_description:
            'Recommendation service is temporarily unavailable',
        },
      })
    })

    await page.goto('/workout')
    await page.click('[data-testid="exercise-item"]')

    // Should fallback to last known values or manual input
    await expect(page.locator('text=Recommendation unavailable')).toBeVisible()
    await expect(page.locator('input[name="weight"]')).toBeEnabled()
  })

  test('should handle missing recommendations gracefully', async () => {
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    await page.route('**/GetRecommendation*', (route) => {
      route.fulfill({
        status: 404,
        json: {
          error: 'no_recommendations',
          error_description: 'No recommendations available for this exercise',
        },
      })
    })

    await page.goto('/workout')
    await page.click('[data-testid="exercise-item"]')

    // Should show default values or manual input
    await expect(page.locator('text=Enter weight manually')).toBeVisible()
    await expect(page.locator('input[name="weight"]')).toBeEnabled()
  })
})
