import { describe, it, expect, beforeEach } from 'vitest'
import { RequestQueue } from '../requestQueue'

describe('RequestQueue', () => {
  let queue: RequestQueue

  beforeEach(() => {
    queue = RequestQueue.getInstance()
    queue.clear()
    queue.setConcurrency(3) // Reset to default
  })

  describe('add', () => {
    it('should execute a simple request', async () => {
      const result = await queue.add(() => Promise.resolve('test'))
      expect(result).toBe('test')
    })

    it('should handle request failures', async () => {
      await expect(
        queue.add(() => Promise.reject(new Error('Test error')))
      ).rejects.toThrow('Test error')
    })

    it('should respect priority order', async () => {
      const results: number[] = []

      // Set concurrency to 1 to ensure sequential processing
      queue.setConcurrency(1)

      // Add requests with different priorities
      const promises = [
        queue.add(
          () =>
            new Promise((resolve) =>
              setTimeout(() => {
                results.push(1)
                resolve(1)
              }, 10)
            ),
          0
        ), // Low priority
        queue.add(
          () =>
            new Promise((resolve) =>
              setTimeout(() => {
                results.push(2)
                resolve(2)
              }, 10)
            ),
          10
        ), // High priority
        queue.add(
          () =>
            new Promise((resolve) =>
              setTimeout(() => {
                results.push(3)
                resolve(3)
              }, 10)
            ),
          5
        ), // Medium priority
      ]

      await Promise.all(promises)

      // Should execute in priority order: 2, 3, 1
      expect(results).toEqual([2, 3, 1])
    })

    it('should deduplicate requests with same key', async () => {
      let callCount = 0
      const request = () =>
        new Promise<number>((resolve) => {
          callCount++
          setTimeout(() => resolve(42), 10)
        })

      // Add same request with same key multiple times
      const promises = [
        queue.add(request, 0, 'duplicate-key'),
        queue.add(request, 0, 'duplicate-key'),
        queue.add(request, 0, 'duplicate-key'),
      ]

      const results = await Promise.all(promises)

      // Should only execute once
      expect(callCount).toBe(1)
      expect(results).toEqual([42, 42, 42])
    })
  })

  describe('concurrency', () => {
    it('should respect concurrency limit', async () => {
      queue.setConcurrency(2)

      let activeCount = 0
      let maxActive = 0

      const createRequest = () => () =>
        new Promise<void>((resolve) => {
          activeCount++
          maxActive = Math.max(maxActive, activeCount)
          setTimeout(() => {
            activeCount--
            resolve()
          }, 50)
        })

      // Add 5 requests
      const promises = Array(5)
        .fill(null)
        .map(() => queue.add(createRequest()))

      await Promise.all(promises)

      // Max active should not exceed concurrency limit
      expect(maxActive).toBeLessThanOrEqual(2)
    })

    it('should update concurrency dynamically', async () => {
      queue.setConcurrency(1)

      const results: number[] = []
      const promises: Promise<void>[] = []

      // Add initial requests
      for (let i = 0; i < 3; i++) {
        promises.push(
          queue.add(
            () =>
              new Promise<void>((resolve) => {
                setTimeout(() => {
                  results.push(i)
                  resolve()
                }, 100)
              })
          )
        )
      }

      // Increase concurrency after a delay
      setTimeout(() => {
        queue.setConcurrency(3)
      }, 50)

      await Promise.all(promises)

      // Should process faster with increased concurrency
      expect(results.length).toBe(3)
    })
  })

  describe('retry logic', () => {
    it('should retry failed requests', async () => {
      let attempts = 0
      const request = () => {
        attempts++
        if (attempts < 3) {
          return Promise.reject(new Error('Network error'))
        }
        return Promise.resolve('success')
      }

      const result = await queue.add(request)

      expect(attempts).toBe(3)
      expect(result).toBe('success')
    })

    it('should not retry non-retryable errors', async () => {
      let attempts = 0
      const request = () => {
        attempts++
        return Promise.reject(new Error('Invalid input'))
      }

      await expect(queue.add(request)).rejects.toThrow('Invalid input')
      expect(attempts).toBe(1)
    })

    it('should apply exponential backoff', async () => {
      const timestamps: number[] = []
      let attempts = 0

      const request = () => {
        timestamps.push(Date.now())
        attempts++
        if (attempts < 3) {
          const error = new Error('Network error') as Error & { status: number }
          error.status = 500
          return Promise.reject(error)
        }
        return Promise.resolve('success')
      }

      await queue.add(request)

      // Check delays between attempts (should increase)
      const delay1 = timestamps[1] - timestamps[0]
      const delay2 = timestamps[2] - timestamps[1]

      expect(delay1).toBeGreaterThan(800) // ~1000ms with jitter
      expect(delay2).toBeGreaterThan(delay1) // Should increase
    })
  })

  describe('cancel and clear', () => {
    it('should cancel specific request', async () => {
      const request = () =>
        new Promise((resolve) => {
          setTimeout(() => resolve('completed'), 1000)
        })

      const promise = queue.add(request, 0, 'cancel-me')

      // Cancel after a short delay
      setTimeout(() => {
        const cancelled = queue.cancel('cancel-me')
        expect(cancelled).toBe(true)
      }, 10)

      await expect(promise).rejects.toThrow('Request cancelled')
    })

    it('should clear all pending requests', async () => {
      const promises = Array(5)
        .fill(null)
        .map((_, i) =>
          queue.add(
            () =>
              new Promise((resolve) => {
                setTimeout(() => resolve(i), 1000)
              })
          )
        )

      // Clear after a short delay
      setTimeout(() => queue.clear(), 10)

      // All should be rejected
      const results = await Promise.allSettled(promises)
      results.forEach((result) => {
        expect(result.status).toBe('rejected')
        if (result.status === 'rejected') {
          expect(result.reason.message).toBe('Request queue cleared')
        }
      })
    })
  })

  describe('getStats', () => {
    it('should return correct statistics', async () => {
      queue.setConcurrency(2)

      // Add some requests
      const promises = Array(5)
        .fill(null)
        .map(() =>
          queue.add(
            () =>
              new Promise((resolve) => {
                setTimeout(() => resolve(null), 100)
              })
          )
        )

      // Check stats while processing
      const stats = queue.getStats()
      expect(stats.concurrency).toBe(2)
      expect(stats.total).toBeGreaterThan(0)
      expect(stats.active).toBeLessThanOrEqual(2)
      expect(stats.pending + stats.active).toBe(stats.total)

      await Promise.all(promises)

      // Check stats after completion
      const finalStats = queue.getStats()
      expect(finalStats.pending).toBe(0)
      expect(finalStats.active).toBe(0)
    })
  })

  describe('error handling', () => {
    it('should handle abort errors', async () => {
      const request = () =>
        new Promise((resolve) => {
          setTimeout(() => resolve('completed'), 1000)
        })

      const promise = queue.add(request, 0, 'abort-test')

      // Clear queue (which aborts all requests)
      setTimeout(() => queue.clear(), 10)

      await expect(promise).rejects.toThrow()
    })

    it('should identify retryable errors correctly', async () => {
      const errors = [
        { error: new TypeError('Failed to fetch'), shouldRetry: true },
        {
          error: Object.assign(new Error('Server error'), { status: 500 }),
          shouldRetry: true,
        },
        {
          error: Object.assign(new Error('Too many requests'), { status: 429 }),
          shouldRetry: true,
        },
        { error: new Error('network timeout'), shouldRetry: true },
        { error: new Error('ECONNRESET'), shouldRetry: true },
        {
          error: Object.assign(new Error('Bad request'), { status: 400 }),
          shouldRetry: false,
        },
        { error: new Error('Invalid input'), shouldRetry: false },
      ]

      // Test each error type separately to avoid await in loop
      await Promise.all(
        errors.map(async ({ error, shouldRetry }) => {
          let attempts = 0
          const request = () => {
            attempts++
            return Promise.reject(error)
          }

          try {
            await queue.add(request)
          } catch (e) {
            // Expected to fail
          }

          if (shouldRetry) {
            expect(attempts).toBeGreaterThan(1)
          } else {
            expect(attempts).toBe(1)
          }
        })
      )
    })
  })
})
