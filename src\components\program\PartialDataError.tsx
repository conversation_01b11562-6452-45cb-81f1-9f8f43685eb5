'use client'

import React from 'react'

export interface PartialDataErrorProps {
  failedData: string[]
  onRetry?: () => void
  className?: string
}

export function PartialDataError({
  failedData,
  onRetry,
  className = '',
}: PartialDataErrorProps) {
  return (
    <div
      className={`bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 ${className}`}
    >
      <div className="flex items-start space-x-3">
        <svg
          className="w-5 h-5 text-yellow-600 dark:text-yellow-500 mt-0.5 flex-shrink-0"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
          />
        </svg>
        <div className="flex-1">
          <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
            Some data couldn't be loaded
          </h3>
          <div className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
            <p>The following information is temporarily unavailable:</p>
            <ul className="list-disc list-inside mt-1">
              {failedData.map((item) => (
                <li key={item}>{item}</li>
              ))}
            </ul>
          </div>
          {onRetry && (
            <button
              onClick={onRetry}
              className="mt-3 text-sm font-medium text-yellow-600 dark:text-yellow-400 hover:text-yellow-500 dark:hover:text-yellow-300"
            >
              Try loading again
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
