import React from 'react'

interface ChevronLeftIconProps {
  className?: string
  onClick?: () => void
  size?: number
}

export function ChevronLeftIcon({
  className = '',
  onClick,
  size = 24,
}: ChevronLeftIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      onClick={onClick}
      aria-hidden="true"
    >
      <path d="M15 18l-6-6 6-6" />
    </svg>
  )
}
