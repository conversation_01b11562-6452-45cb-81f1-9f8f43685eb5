/**
 * Enhanced error handling utilities for API requests
 */

export interface EnhancedError extends Error {
  code?: string
  statusCode?: number
  retryAfter?: number
  isRetryable?: boolean
}

/**
 * Categorize errors by type for better handling
 */
export function categorizeError(error: unknown): EnhancedError {
  if (error instanceof Error) {
    const enhancedError = error as EnhancedError

    // Network timeout
    if (error.message.includes('timeout') || error.name === 'AbortError') {
      enhancedError.name = 'TimeoutError'
      enhancedError.code = 'TIMEOUT'
      enhancedError.isRetryable = true
      return enhancedError
    }

    // Network error
    if (error.message.includes('network') || error.message.includes('fetch')) {
      enhancedError.name = 'NetworkError'
      enhancedError.code = 'NETWORK_ERROR'
      enhancedError.isRetryable = true
      return enhancedError
    }

    // Authentication error
    if (error.message.includes('401') || error.message.includes('auth')) {
      enhancedError.name = 'AuthError'
      enhancedError.code = 'UNAUTHORIZED'
      enhancedError.statusCode = 401
      enhancedError.isRetryable = false
      return enhancedError
    }

    // Expired access
    if (error.message.includes('expired')) {
      enhancedError.name = 'ExpiredError'
      enhancedError.code = 'EXPIRED'
      enhancedError.isRetryable = false
      return enhancedError
    }

    // Rate limiting
    if (error.message.includes('429') || error.message.includes('rate limit')) {
      enhancedError.name = 'RateLimitError'
      enhancedError.code = 'RATE_LIMITED'
      enhancedError.statusCode = 429
      enhancedError.isRetryable = true
      enhancedError.retryAfter = 60 // Default 60 seconds
      return enhancedError
    }

    return enhancedError
  }

  // Convert non-Error objects to Error
  const newError = new Error(String(error)) as EnhancedError
  newError.name = 'UnknownError'
  newError.code = 'UNKNOWN'
  return newError
}

/**
 * Exponential backoff retry configuration
 */
export interface RetryConfig {
  maxRetries: number
  initialDelay: number
  maxDelay: number
  backoffMultiplier: number
}

export const defaultRetryConfig: RetryConfig = {
  maxRetries: 3,
  initialDelay: 1000, // 1 second
  maxDelay: 30000, // 30 seconds
  backoffMultiplier: 2,
}

/**
 * Calculate delay for exponential backoff
 */
export function calculateBackoffDelay(
  attempt: number,
  config: RetryConfig = defaultRetryConfig
): number {
  const delay = Math.min(
    config.initialDelay * Math.pow(config.backoffMultiplier, attempt - 1),
    config.maxDelay
  )
  // Add jitter to prevent thundering herd
  const jitter = Math.random() * 0.3 * delay
  return Math.floor(delay + jitter)
}

/**
 * Retry wrapper for async functions with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  config: RetryConfig = defaultRetryConfig,
  onRetry?: (attempt: number, error: Error) => void
): Promise<T> {
  let lastError: EnhancedError

  const attemptWithDelay = async (attempt: number): Promise<T> => {
    try {
      return await fn()
    } catch (error) {
      lastError = categorizeError(error)

      // Don't retry non-retryable errors
      if (!lastError.isRetryable) {
        throw lastError
      }

      // Don't retry if this is the last attempt
      if (attempt === config.maxRetries) {
        throw lastError
      }

      // Calculate delay
      const delay = calculateBackoffDelay(attempt, config)

      // Call retry callback if provided
      if (onRetry) {
        onRetry(attempt, lastError)
      }

      // Wait before retrying
      await new Promise<void>((resolve) => {
        setTimeout(resolve, delay)
      })

      // Recursive call for next attempt
      return attemptWithDelay(attempt + 1)
    }
  }

  return attemptWithDelay(1)
}

/**
 * Create a timeout promise that rejects after specified time
 */
export function createTimeoutPromise(
  ms: number,
  operation: string
): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => {
      const error = new Error(
        `${operation} timed out after ${ms}ms`
      ) as EnhancedError
      error.name = 'TimeoutError'
      error.code = 'TIMEOUT'
      error.isRetryable = true
      reject(error)
    }, ms)
  })
}

/**
 * Wrap a promise with a timeout
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  operation = 'Operation'
): Promise<T> {
  return Promise.race([promise, createTimeoutPromise(timeoutMs, operation)])
}
