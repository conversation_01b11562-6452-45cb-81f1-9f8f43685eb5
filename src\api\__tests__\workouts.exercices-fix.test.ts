import { describe, it, expect, vi, beforeEach } from 'vitest'
import { workoutApi } from '../workouts'
import { apiClient } from '../client'

vi.mock('../client')

describe('workoutApi - Exercices to Exercises fix', () => {
  const mockApiClient = vi.mocked(apiClient)

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getUserWorkout', () => {
    it('should convert Exercices to Exercises in wrapped response', async () => {
      const mockResponse = {
        data: {
          StatusCode: 200,
          Result: [
            {
              Id: 1,
              Label: 'Test Workout',
              Exercices: [
                { Id: 1, Label: 'Bench Press' },
                { Id: 2, Label: 'Squat' },
              ],
            },
          ],
        },
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const result = await workoutApi.getUserWorkout()

      expect(result).toHaveLength(1)
      expect(result[0]).toHaveProperty('Exercises')
      expect(result[0]).not.toHaveProperty('Exercices')
      expect(result[0].Exercises).toHaveLength(2)
      expect(result[0].Exercises[0].Label).toBe('Bench Press')
    })

    it('should convert Exercices to Exercises in Data response', async () => {
      const mockResponse = {
        data: {
          Data: [
            {
              Id: 1,
              Label: 'Test Workout',
              Exercices: [
                { Id: 1, Label: 'Bench Press' },
                { Id: 2, Label: 'Squat' },
              ],
            },
          ],
        },
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const result = await workoutApi.getUserWorkout()

      expect(result).toHaveLength(1)
      expect(result[0]).toHaveProperty('Exercises')
      expect(result[0]).not.toHaveProperty('Exercices')
      expect(result[0].Exercises).toHaveLength(2)
    })

    it('should convert Exercices to Exercises in direct array response', async () => {
      const mockResponse = {
        data: [
          {
            Id: 1,
            Label: 'Test Workout',
            Exercices: [
              { Id: 1, Label: 'Bench Press' },
              { Id: 2, Label: 'Squat' },
            ],
          },
        ],
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const result = await workoutApi.getUserWorkout()

      expect(result).toHaveLength(1)
      expect(result[0]).toHaveProperty('Exercises')
      expect(result[0]).not.toHaveProperty('Exercices')
      expect(result[0].Exercises).toHaveLength(2)
    })

    it('should leave workouts unchanged if they already have Exercises', async () => {
      const mockResponse = {
        data: {
          StatusCode: 200,
          Result: [
            {
              Id: 1,
              Label: 'Test Workout',
              Exercises: [
                { Id: 1, Label: 'Bench Press' },
                { Id: 2, Label: 'Squat' },
              ],
            },
          ],
        },
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const result = await workoutApi.getUserWorkout()

      expect(result).toHaveLength(1)
      expect(result[0]).toHaveProperty('Exercises')
      expect(result[0].Exercises).toHaveLength(2)
    })

    it('should handle single workout object response', async () => {
      const mockResponse = {
        data: {
          Id: 1,
          Label: 'Test Workout',
          Exercices: [
            { Id: 1, Label: 'Bench Press' },
            { Id: 2, Label: 'Squat' },
          ],
        },
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const result = await workoutApi.getUserWorkout()

      expect(result).toHaveLength(1)
      expect(result[0]).toHaveProperty('Exercises')
      expect(result[0]).not.toHaveProperty('Exercices')
      expect(result[0].Exercises).toHaveLength(2)
    })
  })
})
