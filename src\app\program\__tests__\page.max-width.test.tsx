import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render } from '@testing-library/react'
import ProgramPage from '../page'

// Mock the imports
vi.mock('@/components/AuthGuard', () => ({
  AuthGuard: ({ children }: { children: React.ReactNode }) => children,
}))

vi.mock('@/components/program/ProgramErrorBoundary', () => ({
  ProgramErrorBoundary: ({ children }: { children: React.ReactNode }) =>
    children,
}))

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
  }),
}))

vi.mock('@/hooks/useProgramWithCalculationsAndCache', () => ({
  useProgramWithCalculationsAndCache: () => ({
    program: {
      id: '1',
      name: 'Test Program',
      description: 'Test description',
    },
    progress: { percentage: 50 },
    isLoading: false,
    isRefreshing: false,
    error: null,
    refetch: vi.fn(),
    hasPartialDataError: false,
  }),
}))

vi.mock('@/hooks/useUserStats', () => ({
  useUserStats: () => ({
    stats: {
      weekStreak: 4,
      workoutsCompleted: 12,
      lbsLifted: 5000,
    },
    isLoading: false,
    error: null,
    refetch: vi.fn(),
  }),
}))

vi.mock('@/hooks/useUserInfo', () => ({
  useUserInfo: () => ({
    refetch: vi.fn(),
  }),
}))

vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    isRefreshing: false,
    pullDistance: 0,
    isPulling: false,
  }),
}))

vi.mock('@/utils/haptic', () => ({
  useHaptic: () => ({
    trigger: vi.fn(),
    withHandler: (fn: () => void) => fn,
  }),
}))

vi.mock('@/components/ui/ScreenReaderAnnouncer', () => ({
  ScreenReaderAnnouncer: () => null,
  useScreenReaderAnnouncer: () => ({
    announcement: '',
    announce: vi.fn(),
  }),
}))

describe('ProgramPage - Max Width Consistency', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should use max-w-lg container for consistent width with other pages', () => {
    const { container } = render(<ProgramPage />)

    // Look for the main content container with max-w-lg
    const maxWidthContainer = container.querySelector('.max-w-lg')

    expect(maxWidthContainer).toBeInTheDocument()
    expect(maxWidthContainer).toHaveClass('mx-auto')

    // Verify it's applied to the main content area, not just nested components
    const scrollContainer = container.querySelector(
      '[data-testid="scroll-container"]'
    )
    const contentWithMaxWidth = scrollContainer?.querySelector('.max-w-lg')

    expect(contentWithMaxWidth).toBeInTheDocument()
  })

  it('should apply max-w-lg to the stat cards container', () => {
    const { container } = render(<ProgramPage />)

    // Find the ProgramStats component area by its py-6 class (we removed px-4)
    const statsContainer = container.querySelector('.py-6')

    // Verify the parent container has max-w-lg constraint
    const parentWithMaxWidth = statsContainer?.closest('.max-w-lg')

    expect(parentWithMaxWidth).toBeInTheDocument()
  })
})
