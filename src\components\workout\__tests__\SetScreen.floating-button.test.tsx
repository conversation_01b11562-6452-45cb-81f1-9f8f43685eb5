import { render, screen } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { SetScreen } from '../SetScreen'
import { NavigationProvider } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'

// Mock the navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
}))

// Mock the hook
vi.mock('@/hooks/useSetScreenLogic')

// Type the mock correctly for vitest

describe('SetScreen - Floating Save Button', () => {
  const mockHandleSaveSet = vi.fn()
  const mockSetSetData = vi.fn()
  const mockHandleRIRSelect = vi.fn()
  const mockHandleRIRCancel = vi.fn()
  const mockRefetchRecommendation = vi.fn()
  const mockPerformancePercentage = vi.fn().mockReturnValue(85)

  const defaultMockReturn = {
    currentExercise: {
      Id: 1,
      Label: 'Bench Press',
      IsBodyweight: false,
      IsTimeBased: false,
    },
    exercises: [],
    currentExerciseIndex: 0,
    isWarmup: false,
    totalSets: 3,
    currentSetIndex: 0,
    setData: { reps: 10, weight: 100, duration: 0 },
    isSaving: false,
    saveError: null,
    showRIRPicker: false,
    showComplete: false,
    showExerciseComplete: false,
    isTransitioning: false,
    showSetSaved: false,
    recommendation: null,
    isLoading: false,
    error: null,
    isLastExercise: false,
    setSetData: mockSetSetData,
    handleSaveSet: mockHandleSaveSet,
    handleRIRSelect: mockHandleRIRSelect,
    handleRIRCancel: mockHandleRIRCancel,
    refetchRecommendation: mockRefetchRecommendation,
    performancePercentage: mockPerformancePercentage,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useSetScreenLogic).mockReturnValue(defaultMockReturn)
  })

  it('should render save button in a floating container', () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Check for floating container
    const floatingContainer = screen.getByTestId('floating-save-button')
    expect(floatingContainer).toBeInTheDocument()
    expect(floatingContainer).toHaveClass('fixed')
    expect(floatingContainer).toHaveClass('bottom-6')
    expect(floatingContainer).toHaveClass('left-0')
    expect(floatingContainer).toHaveClass('right-0')
    expect(floatingContainer).toHaveClass('z-50')
  })

  it('should have proper padding for floating effect', () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    const floatingContainer = screen.getByTestId('floating-save-button')
    expect(floatingContainer).toHaveClass('px-4')
  })

  it('should center the button with max width', () => {
    const { container } = render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    const innerWrapper = container.querySelector('.max-w-lg.mx-auto')
    expect(innerWrapper).toBeInTheDocument()
  })

  it('should maintain button functionality', () => {
    render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    const saveButton = screen.getByRole('button', { name: /save set/i })
    expect(saveButton).toBeInTheDocument()

    saveButton.click()
    expect(mockHandleSaveSet).toHaveBeenCalledTimes(1)
  })

  it('should show proper button states', () => {
    // Test saving state
    vi.mocked(useSetScreenLogic).mockReturnValue({
      ...defaultMockReturn,
      isSaving: true,
    })

    const { rerender } = render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    expect(
      screen.getByRole('button', { name: /saving.../i })
    ).toBeInTheDocument()

    // Test transitioning state
    vi.mocked(useSetScreenLogic).mockReturnValue({
      ...defaultMockReturn,
      isTransitioning: true,
    })

    rerender(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    expect(
      screen.getByRole('button', { name: /loading next set.../i })
    ).toBeInTheDocument()
  })

  it('should add bottom padding to content area to prevent overlap', () => {
    const { container } = render(
      <NavigationProvider>
        <SetScreen exerciseId={1} />
      </NavigationProvider>
    )

    // Check that the main content has bottom padding
    const mainContent = container.querySelector('.pb-24')
    expect(mainContent).toBeInTheDocument()
  })
})
