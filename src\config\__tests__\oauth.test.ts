import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  getGoogleOAuth,
  getAppleOAuth,
  getOAuthConfig,
  oauthRedirectUris,
} from '../oauth'

describe('OAuth Configuration', () => {
  const originalEnv = process.env

  beforeEach(() => {
    vi.resetModules()
    process.env = { ...originalEnv }
  })

  describe('Google OAuth', () => {
    it('should return Google client ID from environment', () => {
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID = 'test-google-client-id'
      const googleOAuth = getGoogleOAuth()
      expect(googleOAuth.clientId).toBe('test-google-client-id')
    })

    it('should return correct scopes', () => {
      const googleOAuth = getGoogleOAuth()
      expect(googleOAuth.scopes).toEqual(['profile', 'email'])
    })

    it('should detect when Google OAuth is configured', () => {
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID = 'test-google-client-id'
      const googleOAuth = getGoogleOAuth()
      expect(googleOAuth.isConfigured()).toBe(true)
    })

    it('should detect when Google OAuth is not configured', () => {
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID = ''
      const googleOAuth = getGoogleOAuth()
      expect(googleOAuth.isConfigured()).toBe(false)
    })
  })

  describe('Apple OAuth', () => {
    it('should return Apple configuration from environment', () => {
      process.env.NEXT_PUBLIC_APPLE_TEAM_ID = 'test-team-id'
      process.env.NEXT_PUBLIC_APPLE_BUNDLE_ID = 'test-bundle-id'

      const appleOAuth = getAppleOAuth()
      expect(appleOAuth.teamId).toBe('test-team-id')
      expect(appleOAuth.bundleId).toBe('test-bundle-id')
    })

    it('should return correct scopes', () => {
      const appleOAuth = getAppleOAuth()
      expect(appleOAuth.scopes).toEqual(['name', 'email'])
    })

    it('should detect when Apple OAuth is configured', () => {
      process.env.NEXT_PUBLIC_APPLE_TEAM_ID = 'test-team-id'
      process.env.NEXT_PUBLIC_APPLE_BUNDLE_ID = 'test-bundle-id'
      const appleOAuth = getAppleOAuth()
      expect(appleOAuth.isConfigured()).toBe(true)
    })

    it('should detect when Apple OAuth is not configured', () => {
      process.env.NEXT_PUBLIC_APPLE_TEAM_ID = ''
      process.env.NEXT_PUBLIC_APPLE_BUNDLE_ID = ''
      const appleOAuth = getAppleOAuth()
      expect(appleOAuth.isConfigured()).toBe(false)
    })

    it('should require both team ID and bundle ID', () => {
      process.env.NEXT_PUBLIC_APPLE_TEAM_ID = 'test-team-id'
      process.env.NEXT_PUBLIC_APPLE_BUNDLE_ID = ''
      const appleOAuth = getAppleOAuth()
      expect(appleOAuth.isConfigured()).toBe(false)
    })
  })

  describe('Combined OAuth Config', () => {
    it('should detect when any provider is configured', () => {
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID = 'test-google-client-id'
      process.env.NEXT_PUBLIC_APPLE_TEAM_ID = ''
      process.env.NEXT_PUBLIC_APPLE_BUNDLE_ID = ''

      const oauthConfig = getOAuthConfig()
      expect(oauthConfig.hasAnyProvider()).toBe(true)
    })

    it('should detect when no providers are configured', () => {
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID = ''
      process.env.NEXT_PUBLIC_APPLE_TEAM_ID = ''
      process.env.NEXT_PUBLIC_APPLE_BUNDLE_ID = ''

      const oauthConfig = getOAuthConfig()
      expect(oauthConfig.hasAnyProvider()).toBe(false)
    })
  })

  describe('OAuth Redirect URIs', () => {
    it('should generate correct redirect URI for server-side rendering', () => {
      const redirectUri = oauthRedirectUris.getRedirectUri()
      expect(redirectUri).toBe('http://localhost:3000/auth/callback')
    })

    it('should generate Google-specific redirect URI', () => {
      const googleUri = oauthRedirectUris.google()
      expect(googleUri).toBe('http://localhost:3000/auth/callback/google')
    })

    it('should generate Apple-specific redirect URI', () => {
      const appleUri = oauthRedirectUris.apple()
      expect(appleUri).toBe('http://localhost:3000/auth/callback/apple')
    })
  })
})
