import { describe, it, expect } from 'vitest'
import {
  formatWeight,
  calculatePercentageChange,
  roundWeight,
} from '../weightUtils'
import type { MultiUnityWeight } from '@/types'

describe('weightUtils', () => {
  describe('formatWeight', () => {
    it('should format weight in pounds', () => {
      const weight: MultiUnityWeight = { Lb: 100, Kg: 45.36 }
      expect(formatWeight(weight, 'lbs')).toBe('100 lbs')
    })

    it('should format weight in kilograms', () => {
      const weight: MultiUnityWeight = { Lb: 100, Kg: 45.36 }
      expect(formatWeight(weight, 'kg')).toBe('45.4 kg')
    })

    it('should handle decimal values', () => {
      const weight: MultiUnityWeight = { Lb: 102.5, Kg: 46.5 }
      expect(formatWeight(weight, 'lbs')).toBe('102.5 lbs')
    })

    it('should handle null weight', () => {
      expect(formatWeight(null as any)).toBe('0 lbs')
    })
  })

  describe('calculatePercentageChange', () => {
    it('should calculate positive percentage change', () => {
      expect(calculatePercentageChange(110, 100)).toBe(10)
    })

    it('should calculate negative percentage change', () => {
      expect(calculatePercentageChange(90, 100)).toBe(-10)
    })

    it('should handle zero previous value', () => {
      expect(calculatePercentageChange(100, 0)).toBeNull()
    })

    it('should round to 1 decimal place', () => {
      expect(calculatePercentageChange(105, 100)).toBe(5)
      expect(calculatePercentageChange(105.5, 100)).toBe(5.5)
    })
  })

  describe('roundWeight', () => {
    it('should round floating-point precision errors', () => {
      expect(roundWeight(35.00000000000004)).toBe(35)
      expect(roundWeight(45.00000000000001)).toBe(45)
      expect(roundWeight(99.99999999999999)).toBe(100)
    })

    it('should preserve valid decimal values', () => {
      expect(roundWeight(35.5)).toBe(35.5)
      expect(roundWeight(45.25)).toBe(45.25)
      expect(roundWeight(102.75)).toBe(102.75)
    })

    it('should round to 2 decimal places', () => {
      expect(roundWeight(35.123456)).toBe(35.12)
      expect(roundWeight(45.999)).toBe(46)
      expect(roundWeight(102.005)).toBe(102.01)
    })

    it('should handle whole numbers', () => {
      expect(roundWeight(35)).toBe(35)
      expect(roundWeight(100)).toBe(100)
      expect(roundWeight(0)).toBe(0)
    })
  })
})
