/**
 * UI component type definitions
 */

import type React from 'react'

/**
 * UI state management
 */
export interface UIState {
  isMobileMenuOpen: boolean
  isRIRModalOpen: boolean
  activeModal: string | null
  toastMessage: string | null
  isKeyboardVisible: boolean
}

/**
 * Loading state for async operations
 */
export interface LoadingState {
  isLoading: boolean
  loadingMessage?: string
  progress?: number
}

/**
 * Error state for UI components
 */
export interface ErrorState {
  hasError: boolean
  errorMessage: string
  errorCode?: string
  errorDetails?: Record<string, unknown>
}

/**
 * Common component props
 */
export interface ComponentProps {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  onClick?: () => void
  disabled?: boolean
  'aria-label'?: string
  'data-testid'?: string
  [key: string]: unknown
}

/**
 * Touch event handler props
 */
export interface TouchHandlers {
  onTouchStart?: (e: React.TouchEvent) => void
  onTouchMove?: (e: React.TouchEvent) => void
  onTouchEnd?: (e: React.TouchEvent) => void
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
}

/**
 * Mobile-optimized button props
 */
export interface MobileButtonProps extends ComponentProps {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost'
  size?: 'small' | 'medium' | 'large' | 'full'
  hapticFeedback?: boolean
  loading?: boolean
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
}

/**
 * Modal configuration
 */
export interface ModalConfig {
  id: string
  title?: string
  content: React.ReactNode
  onClose?: () => void
  closeOnOverlayClick?: boolean
  showCloseButton?: boolean
  fullScreen?: boolean
  animation?: 'slide' | 'fade' | 'scale'
}

/**
 * Toast notification configuration
 */
export interface ToastConfig {
  id: string
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
  dismissible?: boolean
}

/**
 * RIR (Reps in Reserve) options
 */
export type RIROption = {
  value: number
  label: string
  description: string
}

/**
 * Rest timer state
 */
export interface RestTimerState {
  isActive: boolean
  duration: number
  remaining: number
  exerciseName?: string
  setNumber?: number
}
