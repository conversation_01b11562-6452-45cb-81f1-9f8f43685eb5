'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { LoginForm } from './LoginForm'
import { QuickSuccessScreen } from './auth/QuickSuccessScreen'
import { useLoginPrefetch } from '@/hooks/useLoginPrefetch'

interface LoginPageClientProps {
  returnUrl?: string
}

export function LoginPageClient({
  returnUrl = '/program',
}: LoginPageClientProps) {
  const router = useRouter()
  const [showSuccessScreen, setShowSuccessScreen] = useState(false)
  const [isAnimated, setIsAnimated] = useState(false)
  const { startPrefetch } = useLoginPrefetch()

  useEffect(() => {
    // Trigger animations after mount
    const timer = setTimeout(() => setIsAnimated(true), 10)
    return () => clearTimeout(timer)
  }, [])

  const handleLoginSuccess = () => {
    // Show success screen with prefetching
    setShowSuccessScreen(true)
    // Start prefetching program and workout data immediately
    startPrefetch()
  }

  const handleSuccessComplete = () => {
    // Navigate to the return URL
    router.push(returnUrl)
  }

  if (showSuccessScreen) {
    return <QuickSuccessScreen onComplete={handleSuccessComplete} />
  }

  // Check for reduced motion preference
  const prefersReducedMotion =
    typeof window !== 'undefined' &&
    window.matchMedia &&
    window.matchMedia('(prefers-reduced-motion: reduce)')?.matches === true

  // Helper function to get animation classes without nested ternaries
  const getAnimationClasses = (
    animated: string,
    staticClass: string = 'opacity-100'
  ) => {
    if (isAnimated && !prefersReducedMotion) {
      return animated
    }
    if (prefersReducedMotion) {
      return staticClass
    }
    return staticClass === 'opacity-100' ? 'opacity-0' : staticClass
  }

  return (
    <div className="min-h-[100dvh] bg-bg-primary flex flex-col landscape:min-h-0 landscape:h-[100dvh]">
      {/* PWA Safe Area Top */}
      <div className="safe-area-top bg-bg-primary" />

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-8 landscape:py-4 landscape:overflow-y-auto">
        <div className="w-full max-w-md lg:max-w-lg">
          {/* Header with fade-in animation */}
          <div
            className={`text-center mb-8 transition-all duration-700 ease-out ${getAnimationClasses(
              'opacity-100 translate-y-0',
              'opacity-0 -translate-y-4'
            )}`}
          >
            <h1 className="font-heading text-3xl sm:text-4xl lg:text-5xl text-text-primary font-bold mb-2 heading-luxury-lg text-gradient-gold">
              Dr. Muscle X
            </h1>
            <p className="font-heading text-lg sm:text-xl lg:text-2xl text-text-secondary font-medium tracking-luxury text-shadow-sm">
              World's Fastest AI Personal Trainer
            </p>
          </div>

          {/* Login Form with slide-up animation */}
          <div
            className={`bg-gradient-premium rounded-theme shadow-theme-lg p-6 sm:p-8 transition-all duration-700 ease-out ${getAnimationClasses(
              'opacity-100 translate-y-0',
              'opacity-0 translate-y-8'
            )}`}
            style={{
              transitionDelay: prefersReducedMotion ? '0ms' : '200ms',
            }}
          >
            <LoginForm onSuccess={handleLoginSuccess} />
          </div>

          {/* Footer Links with fade-in animation */}
          <div
            className={`mt-6 text-center transition-all duration-700 ease-out ${getAnimationClasses(
              'opacity-100',
              'opacity-0'
            )}`}
            style={{
              transitionDelay: prefersReducedMotion ? '0ms' : '400ms',
            }}
          >
            <p className="text-sm sm:text-base text-text-secondary">
              Don't have an account?{' '}
              <a
                href="/register"
                className="text-brand-primary hover:text-brand-secondary font-medium transition-colors duration-200"
              >
                Sign up
              </a>
            </p>
          </div>
        </div>
      </main>

      {/* PWA Safe Area Bottom */}
      <div className="safe-area-bottom bg-gray-50" />
    </div>
  )
}
