/**
 * Constants and configurations for progressive loading monitoring
 */

export interface PerformanceThresholds {
  loginToFirstByte: number // milliseconds
  userInfoLoadTime: number
  statsLoadTime: number
  overallPageReady: number
  cacheHitRate: number // percentage
}

export const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  loginToFirstByte: 2000, // 2 seconds
  userInfoLoadTime: 1500, // 1.5 seconds
  statsLoadTime: 2500, // 2.5 seconds
  overallPageReady: 4000, // 4 seconds
  cacheHitRate: 70, // 70%
}
