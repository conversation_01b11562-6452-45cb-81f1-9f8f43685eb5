# Cache Implementation Documentation

## Overview

The Dr. Muscle X webapp implements a comprehensive caching system to eliminate skeleton loading and provide instant data display for returning users. The cache is built on top of Zustand's persist middleware and integrates with React Query for background synchronization.

## Key Features

### 1. Zero Skeleton Loading

- Cached data displays instantly when available
- Skeleton loading only appears on first visit or when cache is empty
- Seamless transitions when fresh data arrives

### 2. Cache Management

- **Automatic Expiration**: 24h for main data, 1h for exercise recommendations
- **Size Limits**: Maximum 50 exercise recommendations cached
- **Version Management**: Automatic cache clearing on version mismatch
- **Selective Clearing**: Clear expired data without affecting fresh cache

### 3. Performance Monitoring

- Real-time cache hit/miss tracking
- Average latency monitoring (target < 1ms)
- Cache size tracking
- Health monitoring with automatic warnings

### 4. Background Synchronization

- Stale data displays immediately while fresh data loads
- Silent cache updates without UI disruption
- Offline support with local persistence

## Implementation Details

### Cache Structure

```typescript
interface CachedAPIData {
  userProgramInfo: GetUserProgramInfoResponseModel | null
  userWorkouts: WorkoutTemplateModel[] | null
  todaysWorkout: WorkoutTemplateGroupModel[] | null
  exerciseRecommendations: Record<number, RecommendationModel | null>
  lastUpdated: {
    userProgramInfo: number
    userWorkouts: number
    todaysWorkout: number
    exerciseRecommendations: Record<number, number>
  }
}
```

### Cache Expiration

```typescript
const CACHE_EXPIRY = {
  userProgramInfo: 24 * 60 * 60 * 1000, // 24 hours
  userWorkouts: 24 * 60 * 60 * 1000, // 24 hours
  todaysWorkout: 24 * 60 * 60 * 1000, // 24 hours
  exerciseRecommendation: 60 * 60 * 1000, // 1 hour
}
```

### Performance Metrics

```typescript
interface CacheStats {
  hits: number // Successful cache reads
  misses: number // Cache misses
  hitRate: number // Calculated hit/miss ratio
  operationCount: number // Total cache operations
  averageLatency: number // Average operation time (ms)
  totalLatency: number // Total time spent in cache operations
  totalSize: number // Cache size in bytes
  itemCount: number // Number of cached items
  oldestDataAge: number // Age of oldest cached data (ms)
  freshDataCount: number // Count of non-stale items
  staleDataCount: number // Count of stale items
  hydrationTime: number // Time to restore from localStorage (ms)
}
```

## Usage

### Basic Cache Operations

```typescript
// Store data in cache
store.setCachedUserProgramInfo(data)
store.setCachedUserWorkouts(workouts)
store.setCachedExerciseRecommendation(exerciseId, recommendation)

// Retrieve from cache (returns null if not hydrated or missing)
const programInfo = store.getCachedUserProgramInfo()
const workouts = store.getCachedUserWorkouts()
const recommendation = store.getCachedExerciseRecommendation(exerciseId)

// Check if cache is stale
const isStale = store.isCacheStale('userProgramInfo')
const isRecStale = store.isCacheStale('exerciseRecommendation', exerciseId)
```

### Cache Monitoring

```typescript
// Get cache statistics
const stats = store.getCacheStats()
console.log(`Cache hit rate: ${(stats.hitRate * 100).toFixed(1)}%`)
console.log(`Average latency: ${stats.averageLatency.toFixed(2)}ms`)

// Check cache health
const health = store.getCacheHealth()
if (!health.isHealthy) {
  console.warn('Cache issues:', health.warnings)
}

// Debug cache contents (development only)
store.logCacheContents()
```

### Cache Management

```typescript
// Clear expired data
store.clearExpiredCache()

// Reset cache statistics
store.resetCacheStats()

// Clear all cache data
store.clearAllCache()

// Get cache size
const sizeInBytes = store.getCacheSize()
```

## Integration with React Query

The cache integrates seamlessly with React Query in the `useWorkout` hook:

```typescript
// Cache-first approach
const cachedData = store.getCachedUserProgramInfo()
const isStale = store.isCacheStale('userProgramInfo')

// Use React Query for background updates
const { data } = useQuery({
  queryKey: ['userProgramInfo'],
  queryFn: workoutApi.getUserProgramInfo,
  enabled: isAuthenticated && isStale,
  staleTime: 24 * 60 * 60 * 1000,
})

// Update cache when fresh data arrives
useEffect(() => {
  if (data) {
    store.setCachedUserProgramInfo(data)
  }
}, [data])
```

## Performance Monitoring Integration

The cache reports metrics to the PerformanceMonitor every 30 seconds:

```typescript
useEffect(() => {
  if (!hasHydrated) return

  const interval = setInterval(() => {
    const stats = getCacheStats()
    PerformanceMonitor.trackCacheMetrics(stats)
  }, 30000)

  return () => clearInterval(interval)
}, [hasHydrated])
```

## Testing

### Unit Tests

- Cache operation tests in `workoutStore.cache.test.ts`
- Performance monitoring tests in `workoutStore.cache-monitoring.test.ts`

### Integration Tests

- Complete user journey tests in `workoutStore.integration.test.ts`
- Performance integration in `workoutStore.performance-integration.test.ts`

### E2E Tests

- Full cache flow in `workoutStore.e2e.test.tsx`

## Best Practices

1. **Always check hydration state** before reading from cache
2. **Handle cache misses gracefully** - show loading state when no cached data
3. **Monitor cache health** in production for performance insights
4. **Set appropriate expiration times** based on data freshness requirements
5. **Use background sync** for seamless updates without UI disruption

## Troubleshooting

### Common Issues

1. **Cache not persisting**: Ensure localStorage is available and not full
2. **Stale data showing**: Check expiration times and staleness detection
3. **Poor hit rate**: Monitor usage patterns and adjust cache strategy
4. **Performance issues**: Check cache size and operation latency

### Debug Tools

```typescript
// Check what's in the cache
store.logCacheContents()

// Monitor cache performance
const stats = store.getCacheStats()
console.log('Cache stats:', stats)

// Check cache health
const health = store.getCacheHealth()
console.log('Cache health:', health)
```

## Future Improvements

1. **Compression**: Add LZ-String compression for large cache data
2. **IndexedDB**: Migrate to IndexedDB for larger storage capacity
3. **Partial Updates**: Support incremental cache updates
4. **Smart Prefetching**: Predictive caching based on user patterns
5. **Cache Warming**: Preload common data on app initialization
