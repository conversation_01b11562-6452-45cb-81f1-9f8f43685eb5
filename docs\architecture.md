# Dr. Muscle X - Architecture Documentation

## 🎯 Quick Start

**Dr. Muscle X**: Lightning-fast mobile-first PWA replacing slow MAUI app.

**Key Metrics:**

- Load Time: < 1s (LCP)
- Bundle Size: < 150KB initial JS
- Touch Response: < 50ms
- Test Coverage: 90%+

**Production:**

- URL: https://x.dr-muscle.com/
- API: https://drmuscle.azurewebsites.net
- GitHub: carl<PERSON><PERSON> (id: 39870118)

## 🔧 Tech Stack

| Technology   | Version | Purpose                   | Config File            |
| ------------ | ------- | ------------------------- | ---------------------- |
| Next.js      | 15.3.4  | Framework with App Router | `next.config.js`       |
| React        | 19.1.0  | UI Library                | -                      |
| TypeScript   | 5.8.3   | Type Safety               | `tsconfig.json`        |
| Tailwind CSS | 3.4.17  | Styling                   | `tailwind.config.ts`   |
| Zustand      | 5.0.6   | State Management          | `/src/stores/`         |
| React Query  | 5.81.5  | Server State              | `/src/hooks/`          |
| Axios        | 1.10.0  | HTTP Client               | `/src/api/client.ts`   |
| next-pwa     | 5.6.0   | PWA Features              | `next.config.js`       |
| Vitest       | 3.2.4   | Unit Testing              | `vitest.config.mjs`    |
| Playwright   | 1.53.2  | E2E Testing               | `playwright.config.ts` |

## 📁 Project Structure

```
src/
├── app/                 # Next.js App Router pages
├── components/          # Reusable React components
├── hooks/               # Custom React hooks
├── stores/              # Zustand state stores
├── api/                 # API integration layer
├── types/               # TypeScript definitions
├── services/            # Business logic
├── design-system/       # Theme system & tokens
└── utils/               # Utility functions
```

## 🎨 Design System & Theme

### Theme Architecture

- **Multiple Variants**: subtle-depth (default), flat-bold, glassmorphism, ultra-minimal
- **CSS Variables**: Dynamic theme switching via CSS custom properties
- **Tailwind Integration**: Custom classes like `bg-background`, `shadow-theme-md`

### Component Patterns

```typescript
// Theme-aware components
className="bg-background text-text-primary rounded-theme shadow-theme-md"

// Floating action buttons
<FloatingCTAButton onClick={start} icon={<StartIcon />} label="Start" />
```

## 📱 PWA Features

### Service Worker

- **Caching Strategy**: NetworkFirst for API, CacheFirst for assets
- **Offline Support**: Full workout functionality offline
- **Background Sync**: Queue and sync when reconnected
- **Installation**: Custom install flow with `usePWAInstall` hook

### Offline Queue

```typescript
// Automatic queue management
if (!navigator.onLine) {
  await offlineQueue.add(request)
} else {
  await processRequest(request)
}
```

## 🔐 Authentication

### Token Management

- JWT with refresh token rotation
- Automatic token refresh on 401
- Secure storage in Zustand with persistence
- OAuth support (Google One-Tap, Apple Sign-In)

### OAuth Flow

```typescript
const { signIn } = useOAuth()
await signIn('google', { auto_select: true })
await signIn('apple', { clientId: 'com.drmaxmuscle.web' })
```

## 🏗️ Core Patterns

### State Management Strategy

1. **Server State**: React Query with 24h cache
2. **Client State**: Zustand with localStorage
3. **Offline State**: Queue system with IndexedDB
4. **Component State**: useState/useReducer

### API Integration

```typescript
Component → Custom Hook → React Query → Axios → API
              ↓                ↓
         Error Boundary   Offline Queue
```

### Performance Optimizations

1. **Progressive Loading**: Static → Skeleton → Data
2. **Parallel Requests**: Batch API calls
3. **Smart Caching**: Program (24h), Recommendations (1h)
4. **Code Splitting**: Route-based lazy loading
5. **Image Optimization**: Next.js Image component

## 🔄 Key Workflows

### Workout Flow

```
Program → Today's Workout → Exercise → Set Logging → Rest Timer → Complete
              ↓                ↓           ↓
        Recommendations    Save Queue  Background Sync
```

### Error Recovery

- Network errors → Offline queue
- Auth errors → Token refresh → Retry
- API errors → Exponential backoff
- UI errors → Error boundaries

## 💾 Data Management

### Cache Strategy

| Data Type       | Duration   | Storage   |
| --------------- | ---------- | --------- |
| User Profile    | Session    | Memory    |
| Program Data    | 24 hours   | IndexedDB |
| Workout Data    | 24 hours   | IndexedDB |
| Recommendations | 1 hour     | Memory    |
| Completed Sets  | Until sync | Queue     |

### Zustand Stores

- **authStore**: User, tokens, login/logout
- **workoutStore**: Current workout, recommendations, progress
- **programStore**: Program data, stats, calculations

## 🎨 UI/UX Patterns

### Mobile-First Design

- 44px minimum touch targets
- Haptic feedback on interactions
- Bottom-fixed CTAs for thumb reach
- Swipe gestures for navigation

### Loading States

- Skeleton screens for lists
- Progress rings for workouts
- Shimmer effects for cards
- Instant static UI rendering

## 📊 Performance Monitoring

### Metrics Tracking

```typescript
usePerformanceTracking({
  webVitals: true,
  customMetrics: ['api-latency', 'cache-hit-rate'],
  thresholds: { LCP: 1000, FID: 100, CLS: 0.1 },
})
```

### Analysis Tools

- `npm run analyze` - Bundle size analysis
- `npm run lighthouse:mobile` - Performance audit
- Custom performance marks throughout app

## 🧪 Testing Strategy

### Coverage Requirements

- Business Logic: 95%+
- UI Components: 85%+
- API Integration: 90%+
- E2E Critical Paths: 100%

### Test Types

- **Unit**: Vitest for components/hooks
- **Integration**: API and store tests
- **E2E**: Playwright for user journeys
- **Performance**: Load and runtime tests

## 🚀 Development

### Key Commands

```bash
npm run dev          # Development server
npm run typecheck    # TypeScript checks
npm run test         # All tests
npm run test:e2e     # E2E tests
npm run build        # Production build
npm run analyze      # Bundle analysis
```

### CI/CD Pipeline

```
Push → GitHub Actions → Tests → Build → Deploy (Vercel)
           ↓
      Security Scan → Performance Check
```

## 📋 Quick Reference

### Important Files

- `CLAUDE.md` - AI workflow guide
- `src/api/client.ts` - API config
- `src/stores/` - State management
- `src/design-system/` - Theme system
- `worker/index.js` - Service worker

### Environment Variables

```env
NEXT_PUBLIC_API_BASE_URL=https://drmuscle.azurewebsites.net
NEXT_PUBLIC_GOOGLE_CLIENT_ID=************-...
NEXT_PUBLIC_APPLE_TEAM_ID=7AAXZ47995
```

### Common Patterns

```typescript
// Performance tracking
performance.mark('workout-start')

// Haptic feedback
navigator.vibrate?.(10)

// Theme switching
setTheme('subtle-depth')

// Offline check
if (!navigator.onLine) {
}
```
