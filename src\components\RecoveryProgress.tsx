import React from 'react'

interface RecoveryProgressProps {
  percentage: number
  size?: number
  strokeWidth?: number
  className?: string
}

export function RecoveryProgress({
  percentage,
  size = 80,
  strokeWidth = 8,
  className = '',
}: RecoveryProgressProps) {
  // Ensure percentage is valid
  const validPercentage = Number.isFinite(percentage) ? percentage : 0

  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const offset = circumference - (validPercentage / 100) * circumference

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('[RecoveryProgress] SVG calculations:', {
      inputPercentage: percentage,
      validPercentage,
      size,
      strokeWidth,
      radius,
      circumference,
      offset,
      progressLength: (validPercentage / 100) * circumference,
    })
  }

  // Determine color based on percentage
  const getColor = () => {
    if (validPercentage < 50) return '#EF4444' // red-500
    if (validPercentage < 75) return '#F59E0B' // amber-500
    return '#10B981' // green-500
  }

  return (
    <svg
      width={size}
      height={size}
      className={`transform -rotate-90 ${className}`}
    >
      {/* Background circle */}
      <circle
        cx={size / 2}
        cy={size / 2}
        r={radius}
        stroke="currentColor"
        strokeWidth={strokeWidth}
        fill="none"
        className="text-gray-200 dark:text-gray-700"
      />
      {/* Progress circle */}
      <circle
        cx={size / 2}
        cy={size / 2}
        r={radius}
        stroke={getColor()}
        strokeWidth={strokeWidth}
        fill="none"
        strokeDasharray={circumference}
        strokeDashoffset={offset}
        strokeLinecap="round"
        className="transition-all duration-300 ease-in-out"
      />
      {/* Percentage text */}
      <text
        x="50%"
        y="50%"
        dominantBaseline="middle"
        textAnchor="middle"
        className="fill-current text-gray-900 dark:text-gray-100 text-lg font-semibold"
        transform={`rotate(90 ${size / 2} ${size / 2})`}
      >
        {validPercentage}%
      </text>
    </svg>
  )
}
