import { test, expect } from '@playwright/test'

test.describe('Rest Timer Theme', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication state
    await page.addInitScript(() => {
      localStorage.setItem(
        'dr-muscle-auth-storage',
        JSON.stringify({
          state: {
            token: 'mock-token',
            user: {
              Email: '<EMAIL>',
              FirstName: 'Test',
              LastName: 'User',
            },
          },
        })
      )
    })
  })

  test('should apply theme colors to rest timer page', async ({ page }) => {
    // Navigate to rest timer
    await page.goto('/workout/rest-timer?between-sets=true')

    // Check background color
    const mainDiv = page.locator('.min-h-\\[100dvh\\]')
    await expect(mainDiv).toHaveClass(/bg-bg-primary/)

    // Check bottom bar styling
    const bottomBar = page.locator('.fixed.bottom-0')
    await expect(bottomBar).toHaveClass(/bg-bg-secondary/)
    await expect(bottomBar).toHaveClass(/border-border-primary/)

    // Check skip button styling
    const skipButton = page.getByRole('button', { name: /skip/i })
    await expect(skipButton).toHaveClass(/bg-brand-primary/)
    await expect(skipButton).toHaveClass(/text-text-inverse/)

    // Check rest title color
    const restTitle = page.getByText('Rest', { exact: true })
    await expect(restTitle).toHaveClass(/text-text-primary/)

    // Check progress ring colors
    const svgCircles = page.locator('svg circle')
    await expect(svgCircles.first()).toHaveClass(/text-text-tertiary/)
    await expect(svgCircles.nth(1)).toHaveClass(/text-brand-primary/)

    // Check timer text color
    const timer = page.getByRole('timer')
    await expect(timer).toHaveClass(/text-text-primary/)
  })

  test('should use correct theme colors (gold for subtle-depth)', async ({
    page,
  }) => {
    await page.goto('/workout/rest-timer?between-sets=true')

    // Get computed styles for brand primary (should be gold #d4af37)
    const skipButton = page.getByRole('button', { name: /skip/i })
    const brandColor = await skipButton.evaluate((el) => {
      return window.getComputedStyle(el).backgroundColor
    })

    // Convert hex to rgb for comparison (gold #d4af37 = rgb(212, 175, 55))
    expect(brandColor).toBe('rgb(212, 175, 55)')

    // Check background is dark theme color
    const mainDiv = page.locator('.min-h-\\[100dvh\\]')
    const bgColor = await mainDiv.evaluate((el) => {
      return window.getComputedStyle(el).backgroundColor
    })

    // Dark background #0a0a0b = rgb(10, 10, 11)
    expect(bgColor).toBe('rgb(10, 10, 11)')
  })

  test('should handle theme switching on rest timer page', async ({ page }) => {
    await page.goto('/workout/rest-timer?between-sets=true')

    // Change theme to flat-bold
    await page.evaluate(() => {
      document.documentElement.setAttribute('data-theme', 'flat-bold')
    })

    // Wait for theme to apply
    await page.waitForTimeout(100)

    // Check that brand color changed (flat-bold uses green #00ff88)
    const skipButton = page.getByRole('button', { name: /skip/i })
    const brandColor = await skipButton.evaluate((el) => {
      return window.getComputedStyle(el).backgroundColor
    })

    // Green #00ff88 = rgb(0, 255, 136)
    expect(brandColor).toBe('rgb(0, 255, 136)')
  })
})
