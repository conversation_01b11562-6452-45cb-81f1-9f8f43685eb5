/**
 * Environment validation utility
 *
 * This module provides utilities to validate that required environment
 * variables are properly configured for the Dr. Muscle X web app.
 */

/**
 * Environment variable configuration
 */
interface EnvConfig {
  /**
   * Variable name
   */
  name: string

  /**
   * Whether the variable is required
   */
  required: boolean

  /**
   * Default value if not provided
   */
  defaultValue?: string

  /**
   * Custom validation function
   */
  validate?: (value: string) => boolean
}

/**
 * Environment variables configuration
 */
const envConfigs: EnvConfig[] = [
  {
    name: 'NEXT_PUBLIC_API_URL',
    required: true,
    validate: (value) => {
      try {
        const url = new URL(value)
        return Boolean(url)
      } catch {
        return false
      }
    },
  },
  {
    name: 'NEXT_PUBLIC_APP_ENV',
    required: true,
    defaultValue: 'development',
    validate: (value) =>
      ['development', 'staging', 'production'].includes(value),
  },
  {
    name: 'NEXT_PUBLIC_GOOGLE_CLIENT_ID',
    required: false,
    validate: (value) => value.length > 0,
  },
  {
    name: 'NEXT_PUBLIC_APPLE_TEAM_ID',
    required: false,
    validate: (value) => value.length > 0,
  },
  {
    name: 'NEXT_PUBLIC_APPLE_BUNDLE_ID',
    required: false,
    validate: (value) => value.includes('.'),
  },
]

/**
 * Validation result
 */
interface ValidationResult {
  /**
   * Whether validation passed
   */
  valid: boolean

  /**
   * List of errors
   */
  errors: string[]

  /**
   * List of warnings
   */
  warnings: string[]
}

/**
 * Validate environment variables
 *
 * @returns Validation result with errors and warnings
 */
export function validateEnv(): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  envConfigs.forEach((config) => {
    const value = process.env[config.name] || config.defaultValue

    if (config.required && !value) {
      errors.push(`Missing required environment variable: ${config.name}`)
      return
    }

    if (value && config.validate && !config.validate(value)) {
      errors.push(`Invalid value for environment variable: ${config.name}`)
    }

    if (!config.required && !value) {
      warnings.push(`Optional environment variable not set: ${config.name}`)
    }
  })

  // OAuth specific validation
  const googleClientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID
  const appleTeamId = process.env.NEXT_PUBLIC_APPLE_TEAM_ID
  const appleBundleId = process.env.NEXT_PUBLIC_APPLE_BUNDLE_ID

  if (!googleClientId && !appleTeamId) {
    warnings.push(
      'No OAuth providers configured. At least one OAuth provider (Google or Apple) should be configured.'
    )
  }

  if (appleTeamId && !appleBundleId) {
    errors.push('Apple Team ID is configured but Bundle ID is missing')
  }

  if (!appleTeamId && appleBundleId) {
    errors.push('Apple Bundle ID is configured but Team ID is missing')
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  }
}

/**
 * Get environment variable value with type safety
 *
 * @param name - Environment variable name
 * @param defaultValue - Default value if not set
 * @returns Environment variable value
 */
export function getEnvVar(name: string, defaultValue?: string): string {
  const value = process.env[name]

  if (!value && !defaultValue) {
    throw new Error(`Environment variable ${name} is not set`)
  }

  return value || defaultValue || ''
}

/**
 * Check if running in production environment
 */
export function isProduction(): boolean {
  return process.env.NEXT_PUBLIC_APP_ENV === 'production'
}

/**
 * Check if running in development environment
 */
export function isDevelopment(): boolean {
  return process.env.NEXT_PUBLIC_APP_ENV === 'development'
}

/**
 * Check if running in staging environment
 */
export function isStaging(): boolean {
  return process.env.NEXT_PUBLIC_APP_ENV === 'staging'
}

/**
 * Log environment validation results
 */
export function logEnvValidation(): void {
  const result = validateEnv()

  if (!result.valid) {
    console.error('Environment validation failed:')
    result.errors.forEach((error) => console.error(`  ❌ ${error}`))
  }

  if (result.warnings.length > 0) {
    console.warn('Environment validation warnings:')
    result.warnings.forEach((warning) => console.warn(`  ⚠️  ${warning}`))
  }

  if (result.valid && result.warnings.length === 0) {
    // eslint-disable-next-line no-console
    console.log('✅ Environment validation passed')
  }
}
