/**
 * Program-specific types for the Dr. Muscle X PWA
 * These types are used for the Program Overview page and related features
 */

/**
 * Represents a training program with metadata
 * Extracted and enhanced from GetUserProgramInfoResponseModel
 */
export interface ProgramModel {
  /** Unique identifier for the program */
  id: number
  /** Display name of the program */
  name: string
  /** Detailed description of the program */
  description: string
  /** Category/type of program (e.g., "Strength", "Hypertrophy", "Endurance") */
  category: string
  /** Total number of days in the program */
  totalDays: number
  /** Current day in the program */
  currentDay: number
  /** Number of workouts completed */
  workoutsCompleted: number
  /** ISO date string when user started the program */
  startDate: string
  /** Total number of workouts in the program */
  totalWorkouts: number
  /** Optional URL for program image/icon */
  imageUrl?: string
  /** Number of exercises in the next workout */
  nextWorkoutExerciseCount?: number
}

/**
 * Tracks user's progress through a program
 * Calculated from workout history and program structure
 */
export interface ProgramProgress {
  /** Percentage of program completed (0-100) */
  percentage: number
  /** Number of days completed in the program */
  daysCompleted: number
  /** Total number of workouts in the program */
  totalWorkouts: number
  /** Current week number in the program */
  currentWeek: number
  /** Number of workouts completed this week */
  workoutsThisWeek: number
  /** Number of workouts remaining to complete program */
  remainingWorkouts: number
}

/**
 * Statistics about user's performance in the program
 * Aggregated from workout history and logs
 */
export interface ProgramStats {
  /** Average workout duration in minutes */
  averageWorkoutTime: number
  /** Total weight lifted across all workouts (in user's preferred unit) */
  totalVolume: number
  /** Number of personal records set during program */
  personalRecords: number
  /** Number of consecutive weeks with workouts */
  consecutiveWeeks: number
  /** ISO date string of last completed workout */
  lastWorkoutDate?: string
  /** Total number of workouts completed (all time) */
  totalWorkoutsCompleted: number
  /** User's current body weight */
  bodyWeight?: number
  /** Number of recovery days since last workout */
  recoveryDays?: number
  /** Coach recommendation: Train or Rest */
  coachRecommendation?: 'Train' | 'Rest'
}

/**
 * Type guard to check if an object is a valid ProgramModel
 */
export function isProgramModel(obj: unknown): obj is ProgramModel {
  if (!obj || typeof obj !== 'object') return false

  const p = obj as Record<string, unknown>

  return (
    typeof p.id === 'number' &&
    typeof p.name === 'string' &&
    typeof p.description === 'string' &&
    typeof p.category === 'string' &&
    typeof p.totalDays === 'number' &&
    typeof p.currentDay === 'number' &&
    typeof p.workoutsCompleted === 'number' &&
    typeof p.startDate === 'string' &&
    typeof p.totalWorkouts === 'number' &&
    (p.imageUrl === undefined || typeof p.imageUrl === 'string')
  )
}

/**
 * Type guard to check if an object is a valid ProgramProgress
 */
export function isProgramProgress(obj: unknown): obj is ProgramProgress {
  if (!obj || typeof obj !== 'object') return false

  const p = obj as Record<string, unknown>

  return (
    typeof p.percentage === 'number' &&
    typeof p.daysCompleted === 'number' &&
    typeof p.totalWorkouts === 'number' &&
    typeof p.currentWeek === 'number' &&
    typeof p.workoutsThisWeek === 'number' &&
    typeof p.remainingWorkouts === 'number'
  )
}

/**
 * Type guard to check if an object is a valid ProgramStats
 */
export function isProgramStats(obj: unknown): obj is ProgramStats {
  if (!obj || typeof obj !== 'object') return false

  const s = obj as Record<string, unknown>

  return (
    typeof s.averageWorkoutTime === 'number' &&
    typeof s.totalVolume === 'number' &&
    typeof s.personalRecords === 'number' &&
    typeof s.consecutiveWeeks === 'number' &&
    typeof s.totalWorkoutsCompleted === 'number' &&
    (s.lastWorkoutDate === undefined ||
      typeof s.lastWorkoutDate === 'string') &&
    (s.bodyWeight === undefined || typeof s.bodyWeight === 'number') &&
    (s.recoveryDays === undefined || typeof s.recoveryDays === 'number') &&
    (s.coachRecommendation === undefined ||
      s.coachRecommendation === 'Train' ||
      s.coachRecommendation === 'Rest')
  )
}
