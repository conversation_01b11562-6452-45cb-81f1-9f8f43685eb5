/**
 * Google OAuth Configuration for Dr<PERSON> Muscle X
 */

export const GoogleOAuthConfig = {
  /** Google Sign-In SDK URL */
  GOOGLE_SDK_URL: 'https://accounts.google.com/gsi/client',

  /** Google OAuth Client ID */
  GOOGLE_CLIENT_ID:
    '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com',

  /** Default configuration options */
  DEFAULT_CONFIG: {
    autoSelect: false,
    cancelOnTapOutside: true,
    context: 'signin' as const,
  },

  /** SDK load timeout in milliseconds */
  SDK_LOAD_TIMEOUT: 10000,

  /** Retry configuration */
  RETRY_CONFIG: {
    maxAttempts: 3,
    initialDelay: 1000,
    maxDelay: 5000,
    backoffMultiplier: 2,
  },

  /** Performance tracking names */
  PERFORMANCE_MARKS: {
    SDK_LOAD_START: 'google-oauth-sdk-load-start',
    SDK_LOAD_COMPLETE: 'google-oauth-sdk-load-complete',
    INIT_START: 'google-oauth-init-start',
    INIT_COMPLETE: 'google-oauth-init-complete',
    SIGN_IN_START: 'google-oauth-sign-in-start',
    SIGN_IN_COMPLETE: 'google-oauth-sign-in-complete',
  },

  /** Performance measure names */
  PERFORMANCE_MEASURES: {
    SDK_LOAD: 'google-oauth-sdk-load',
    INIT: 'google-oauth-init',
    SIGN_IN: 'google-oauth-sign-in',
  },
} as const
