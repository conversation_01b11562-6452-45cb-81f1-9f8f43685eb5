'use client'

interface SetProgressInfoProps {
  isWarmup: boolean
  currentSetIndex: number
  totalSets: number
  showSetSaved: boolean
}

export function SetProgressInfo({
  isWarmup,
  currentSetIndex,
  totalSets,
  showSetSaved,
}: SetProgressInfoProps) {
  return (
    <div className="bg-bg-secondary border-b border-brand-primary/10">
      <div className="px-4 py-3">
        <p className="text-center text-text-secondary font-medium">
          {isWarmup
            ? `Warmup Set ${currentSetIndex + 1}`
            : `Set ${currentSetIndex + 1} of ${totalSets}`}
        </p>
        {showSetSaved && (
          <p className="text-center text-success text-sm mt-1 animate-pulse">
            ✓ Set saved
          </p>
        )}
      </div>
    </div>
  )
}
