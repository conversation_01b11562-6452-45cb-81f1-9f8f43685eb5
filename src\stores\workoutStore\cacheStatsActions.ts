/**
 * Cache statistics and health actions for the workout store
 */

import type { WorkoutState, CacheStats, CacheHealth } from './types'
import { initialCacheStats } from './constants'

export const createCacheStatsActions = (
  set: (
    partial:
      | Partial<WorkoutState>
      | ((state: WorkoutState) => Partial<WorkoutState>)
  ) => void,
  get: () => WorkoutState
) => ({
  getCacheStats: (): CacheStats => {
    const { cachedData, cacheStats } = get()
    const now = Date.now()

    // Calculate total size
    let totalSize = 0
    try {
      totalSize = JSON.stringify(cachedData).length
    } catch {
      totalSize = 0
    }

    // Count items
    let itemCount = 0
    if (cachedData.userProgramInfo) itemCount++
    if (cachedData.userWorkouts) itemCount++
    if (cachedData.todaysWorkout) itemCount++
    itemCount += Object.keys(cachedData.exerciseRecommendations).length

    // Find oldest data
    let oldestTimestamp = now
    if (cachedData.lastUpdated.userProgramInfo > 0) {
      oldestTimestamp = Math.min(
        oldestTimestamp,
        cachedData.lastUpdated.userProgramInfo
      )
    }
    if (cachedData.lastUpdated.userWorkouts > 0) {
      oldestTimestamp = Math.min(
        oldestTimestamp,
        cachedData.lastUpdated.userWorkouts
      )
    }
    if (cachedData.lastUpdated.todaysWorkout > 0) {
      oldestTimestamp = Math.min(
        oldestTimestamp,
        cachedData.lastUpdated.todaysWorkout
      )
    }
    Object.values(cachedData.lastUpdated.exerciseRecommendations).forEach(
      (timestamp) => {
        if (timestamp > 0) {
          oldestTimestamp = Math.min(oldestTimestamp, timestamp)
        }
      }
    )

    const oldestDataAge = oldestTimestamp < now ? now - oldestTimestamp : 0

    // Count fresh vs stale data
    let freshDataCount = 0
    let staleDataCount = 0

    const { isCacheStale } = get()

    if (cachedData.userProgramInfo) {
      if (isCacheStale('userProgramInfo')) {
        staleDataCount++
      } else {
        freshDataCount++
      }
    }

    if (cachedData.userWorkouts) {
      if (isCacheStale('userWorkouts')) {
        staleDataCount++
      } else {
        freshDataCount++
      }
    }

    if (cachedData.todaysWorkout) {
      if (isCacheStale('todaysWorkout')) {
        staleDataCount++
      } else {
        freshDataCount++
      }
    }

    Object.keys(cachedData.exerciseRecommendations).forEach((exerciseIdStr) => {
      const exerciseId = Number(exerciseIdStr)
      if (isCacheStale('exerciseRecommendation', exerciseId)) {
        staleDataCount++
      } else {
        freshDataCount++
      }
    })

    // Calculate derived metrics
    const hitRate =
      cacheStats.operationCount > 0
        ? cacheStats.hits / cacheStats.operationCount
        : 0
    const averageLatency =
      cacheStats.operationCount > 0
        ? cacheStats.totalLatency / cacheStats.operationCount
        : 0

    return {
      hits: cacheStats.hits,
      misses: cacheStats.misses,
      hitRate,
      operationCount: cacheStats.operationCount,
      averageLatency,
      totalLatency: cacheStats.totalLatency,
      totalSize,
      itemCount,
      oldestDataAge,
      freshDataCount,
      staleDataCount,
      hydrationTime: cacheStats.hydrationTime,
    }
  },

  resetCacheStats: () => {
    set({ cacheStats: initialCacheStats })
  },

  logCacheContents: () => {
    if (process.env.NODE_ENV === 'development') {
      const stats = get().getCacheStats()
      const { cachedData } = get()
      // eslint-disable-next-line no-console
      console.log('Cache Stats:', stats)
      // eslint-disable-next-line no-console
      console.log('Cache Contents:', cachedData)
    }
  },

  getCacheHealth: (): CacheHealth => {
    const stats = get().getCacheStats()
    const warnings: string[] = []

    // Check hit rate
    if (stats.operationCount > 10 && stats.hitRate < 0.5) {
      warnings.push('Low cache hit rate')
    }

    // Check cache size
    if (stats.totalSize > 200 * 1024) {
      warnings.push('Cache size exceeds recommended limit')
    }

    // Check staleness
    if (stats.itemCount > 0 && stats.staleDataCount > stats.freshDataCount) {
      warnings.push('Majority of cache is stale')
    }

    // Check average latency
    if (stats.averageLatency > 1) {
      warnings.push('Cache operations are slow')
    }

    return {
      isHealthy: warnings.length === 0,
      warnings,
    }
  },
})
