// Disable service worker logs in production
self.__WB_DISABLE_DEV_LOGS = true

// Listen to message event from client
self.addEventListener('message', (event) => {
  if (event.data && event.data.action === 'SKIP_WAITING') {
    self.skipWaiting()
  }
})

// Custom fetch handler to exclude RSC requests
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // Skip RSC requests - let them go through network directly
  if (url.search.includes('_rsc=')) {
    return
  }
  
  // Skip Next.js internal requests
  if (url.pathname.includes('__nextjs')) {
    return
  }
  
  // Let workbox handle other requests
})