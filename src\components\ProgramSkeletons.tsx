import React from 'react'

interface SkeletonProps {
  className?: string
}

interface ProgressRingSkeletonProps extends SkeletonProps {
  size?: 'sm' | 'md' | 'lg'
}

interface ProgramDescriptionSkeletonProps extends SkeletonProps {
  lines?: number
}

// Helper to check for reduced motion preference
function getPrefersReducedMotion() {
  if (typeof window === 'undefined') return false
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

export function ProgramHeaderSkeleton({ className = '' }: SkeletonProps) {
  const prefersReducedMotion = getPrefersReducedMotion()
  const animateClass = prefersReducedMotion ? '' : 'animate-pulse'

  return (
    <div
      data-testid="program-header-skeleton"
      className={`${animateClass} ${className}`}
    >
      <div className="space-y-3">
        {/* Title skeleton */}
        <div
          data-testid="header-title-skeleton"
          className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-3/4"
        />
        {/* Category badge skeleton */}
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-full w-32" />
      </div>
    </div>
  )
}

export function ProgressRingSkeleton({
  size = 'md',
  className = '',
}: ProgressRingSkeletonProps) {
  const prefersReducedMotion = getPrefersReducedMotion()
  const animateClass = prefersReducedMotion ? '' : 'animate-pulse'

  const sizeClasses = {
    sm: 'w-24 h-24',
    md: 'w-32 h-32',
    lg: 'w-40 h-40',
  }

  const innerSizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-20 h-20',
    lg: 'w-28 h-28',
  }

  return (
    <div className="flex justify-center">
      <div
        data-testid="progress-ring-skeleton"
        className={`${sizeClasses[size]} bg-gray-200 dark:bg-gray-700 rounded-full ${animateClass} relative flex items-center justify-center ${className}`}
      >
        {/* Inner circle to create ring effect */}
        <div
          data-testid="progress-ring-inner-skeleton"
          className={`${innerSizeClasses[size]} bg-gray-50 dark:bg-gray-800 rounded-full`}
        />
      </div>
    </div>
  )
}

export function ProgramStatsSkeleton({ className = '' }: SkeletonProps) {
  const prefersReducedMotion = getPrefersReducedMotion()
  const animateClass = prefersReducedMotion ? '' : 'animate-pulse'

  return (
    <div
      data-testid="program-stats-skeleton"
      className={`grid grid-cols-2 md:grid-cols-4 gap-4 ${className}`}
    >
      {[1, 2, 3, 4].map((index) => (
        <div
          key={index}
          data-testid="stat-card-skeleton"
          className={`bg-white dark:bg-gray-800 rounded-lg p-4 min-h-[100px] ${animateClass}`}
        >
          <div className="flex items-start justify-between">
            <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full" />
          </div>
          <div className="mt-3 space-y-2">
            <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded" />
            <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded" />
          </div>
        </div>
      ))}
    </div>
  )
}

export function ProgramDescriptionSkeleton({
  lines = 3,
  className = '',
}: ProgramDescriptionSkeletonProps) {
  const prefersReducedMotion = getPrefersReducedMotion()
  const animateClass = prefersReducedMotion ? '' : 'animate-pulse'

  // Generate different widths for realism
  const lineWidths = ['w-full', 'w-5/6', 'w-4/6', 'w-full', 'w-3/4']

  return (
    <div
      data-testid="program-description-skeleton"
      className={`space-y-2 ${className}`}
    >
      {Array.from({ length: lines }).map((_, index) => (
        <div
          // eslint-disable-next-line react/no-array-index-key
          key={`description-line-${index}`}
          data-testid="description-line-skeleton"
          className={`h-4 bg-gray-200 dark:bg-gray-700 rounded ${animateClass} ${lineWidths[index % lineWidths.length]}`}
        />
      ))}
    </div>
  )
}

export function ProgramOverviewSkeleton({ className = '' }: SkeletonProps) {
  const prefersReducedMotion = getPrefersReducedMotion()
  const animateClass = prefersReducedMotion ? '' : 'animate-pulse'

  return (
    <div
      data-testid="program-overview-skeleton"
      className={`min-h-[100dvh] flex flex-col space-y-6 p-4 pb-safe-bottom ${className}`}
    >
      {/* Header */}
      <ProgramHeaderSkeleton />

      {/* Progress Ring */}
      <div className="py-4">
        <ProgressRingSkeleton size="lg" />
      </div>

      {/* Stats Grid */}
      <ProgramStatsSkeleton />

      {/* Description */}
      <ProgramDescriptionSkeleton lines={4} />

      {/* Spacer to push button to bottom */}
      <div className="flex-1" />

      {/* CTA Button skeleton */}
      <div
        data-testid="cta-button-skeleton"
        className={`h-14 bg-gray-200 dark:bg-gray-700 rounded-lg w-full ${animateClass}`}
      />
    </div>
  )
}
