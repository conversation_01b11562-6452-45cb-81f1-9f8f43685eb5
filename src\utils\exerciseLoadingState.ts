/**
 * Exercise Loading State Manager
 *
 * Tracks loading states for individual exercises to support
 * progressive loading of sets. This prevents redundant API calls
 * and provides granular loading feedback.
 */

export interface ExerciseLoadingState {
  exerciseId: number
  isLoading: boolean
  error: string | null
  errorDetails?: Record<string, unknown>
  startTime: number
  endTime?: number
}

export class ExerciseLoadingStateManager {
  private states: Map<number, ExerciseLoadingState>

  private cleanupThreshold = 5 * 60 * 1000 // 5 minutes

  private lastCleanup = Date.now()

  constructor() {
    this.states = new Map()
  }

  /**
   * Start loading for an exercise
   */
  startLoading(exerciseId: number): void {
    this.maybeCleanup()

    this.states.set(exerciseId, {
      exerciseId,
      isLoading: true,
      error: null,
      errorDetails: undefined,
      startTime: Date.now(),
      endTime: undefined,
    })
  }

  /**
   * Mark loading as complete for an exercise
   */
  completeLoading(exerciseId: number): void {
    const state = this.states.get(exerciseId)
    if (state) {
      this.states.set(exerciseId, {
        ...state,
        isLoading: false,
        error: null,
        errorDetails: undefined,
        endTime: Date.now(),
      })
    }
  }

  /**
   * Mark loading as failed for an exercise
   */
  failLoading(
    exerciseId: number,
    error: string,
    errorDetails?: Record<string, unknown>
  ): void {
    const state = this.states.get(exerciseId)
    if (state) {
      this.states.set(exerciseId, {
        ...state,
        isLoading: false,
        error,
        errorDetails,
        endTime: Date.now(),
      })
    }
  }

  /**
   * Get the loading state for an exercise
   */
  getLoadingState(exerciseId: number): ExerciseLoadingState | null {
    return this.states.get(exerciseId) || null
  }

  /**
   * Check if an exercise is currently loading
   */
  isLoading(exerciseId: number): boolean {
    const state = this.states.get(exerciseId)
    return state?.isLoading || false
  }

  /**
   * Check if an exercise has an error
   */
  hasError(exerciseId: number): boolean {
    const state = this.states.get(exerciseId)
    return state?.error !== null && state?.error !== undefined
  }

  /**
   * Check if an exercise has completed successfully
   */
  isComplete(exerciseId: number): boolean {
    const state = this.states.get(exerciseId)
    return (
      state !== undefined &&
      !state.isLoading &&
      state.error === null &&
      state.endTime !== undefined
    )
  }

  /**
   * Get all currently loading exercise IDs
   */
  getAllLoadingExercises(): number[] {
    const loading: number[] = []
    this.states.forEach((state, id) => {
      if (state.isLoading) {
        loading.push(id)
      }
    })
    return loading
  }

  /**
   * Get the count of tracked exercises
   */
  getTrackedCount(): number {
    return this.states.size
  }

  /**
   * Clear state for a specific exercise
   */
  clearState(exerciseId: number): void {
    this.states.delete(exerciseId)
  }

  /**
   * Clear all states
   */
  clearAll(): void {
    this.states.clear()
  }

  /**
   * Clean up old states to prevent memory leaks
   */
  cleanup(): void {
    const now = Date.now()
    const toDelete: number[] = []

    this.states.forEach((state, id) => {
      // Don't clean up active loading states
      if (state.isLoading) {
        return
      }

      // Clean up old completed or failed states
      const age = now - (state.endTime || state.startTime)
      if (age > this.cleanupThreshold) {
        toDelete.push(id)
      }
    })

    toDelete.forEach((id) => this.states.delete(id))
    this.lastCleanup = now
  }

  /**
   * Maybe trigger cleanup if enough time has passed
   */
  private maybeCleanup(): void {
    const now = Date.now()
    if (now - this.lastCleanup > this.cleanupThreshold) {
      this.cleanup()
    }
  }
}
