/**
 * Cache performance tracking utilities
 */
export class CachePerformanceTracker {
  /**
   * Track cache performance metrics
   */
  static trackCacheMetrics(cacheStats: {
    hits: number
    misses: number
    hitRate: number
    averageLatency: number
    totalSize: number
    itemCount: number
  }): void {
    // Store cache metrics in sessionStorage for later reporting
    if (typeof window !== 'undefined' && window.sessionStorage) {
      try {
        window.sessionStorage.setItem(
          'dr-muscle-cache-metrics',
          JSON.stringify(cacheStats)
        )
      } catch (error) {
        // Silently fail if storage is full
      }
    }
  }

  /**
   * Retrieve cache metrics from sessionStorage
   */
  static getCacheMetrics(): {
    hitRate: number
    averageLatency: number
    totalSize: number
    itemCount: number
  } | null {
    if (typeof window !== 'undefined' && window.sessionStorage) {
      try {
        const cacheMetricsStr = window.sessionStorage.getItem(
          'dr-muscle-cache-metrics'
        )
        if (cacheMetricsStr) {
          const cacheStats = JSON.parse(cacheMetricsStr)
          return {
            hitRate: cacheStats.hitRate,
            averageLatency: cacheStats.averageLatency,
            totalSize: cacheStats.totalSize,
            itemCount: cacheStats.itemCount,
          }
        }
      } catch (error) {
        // Ignore parse errors
      }
    }
    return null
  }
}
