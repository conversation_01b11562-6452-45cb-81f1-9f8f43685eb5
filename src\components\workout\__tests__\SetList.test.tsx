import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import React from 'react'
import { SetList } from '../SetList'
import type { WorkoutLogSerieModel } from '@/types'

describe('SetList', () => {
  const mockSets: WorkoutLogSerieModel[] = [
    {
      ExerciseId: 1,
      Weight: { Value: 45, Unit: 'lbs' },
      Reps: 10,
      IsWarmups: true,
      IsNext: false,
      IsFinished: true,
    },
    {
      ExerciseId: 1,
      Weight: { Value: 95, Unit: 'lbs' },
      Reps: 8,
      IsWarmups: true,
      IsNext: false,
      IsFinished: true,
    },
    {
      ExerciseId: 1,
      Weight: { Value: 135, Unit: 'lbs' },
      Reps: 10,
      IsWarmups: false,
      IsNext: false,
      IsFinished: true,
    },
    {
      ExerciseId: 1,
      Weight: { Value: 135, Unit: 'lbs' },
      Reps: 8,
      IsWarmups: false,
      IsNext: true,
      IsFinished: false,
      RIR: 2,
    },
  ]

  const defaultProps = {
    sets: mockSets,
    isLoading: false,
    exerciseId: 1,
    onSetClick: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('should render all sets with proper formatting', () => {
      render(<SetList {...defaultProps} />)

      // Warmup sets
      expect(screen.getByText('45 lbs × 10')).toBeInTheDocument()
      expect(screen.getByText('95 lbs × 8')).toBeInTheDocument()

      // Working sets
      expect(screen.getByText('135 lbs × 10')).toBeInTheDocument()
      expect(screen.getByText('135 lbs × 8')).toBeInTheDocument()
    })

    it('should show warmup indicators', () => {
      render(<SetList {...defaultProps} />)

      // Should have 2 warmup indicators
      const warmupIndicators = screen.getAllByText('W')
      expect(warmupIndicators).toHaveLength(2)
    })

    it('should show set numbers for working sets', () => {
      render(<SetList {...defaultProps} />)

      // Should have numbered working sets
      expect(screen.getByText('1')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument()
    })

    it('should display RIR when available', () => {
      render(<SetList {...defaultProps} />)

      expect(screen.getByText('RIR: 2')).toBeInTheDocument()
    })
  })

  describe('Status Indicators', () => {
    it('should show completed checkmark for finished sets', () => {
      const { container } = render(<SetList {...defaultProps} />)

      // First 3 sets are finished
      const checkmarks = container.querySelectorAll('svg')
      expect(checkmarks.length).toBeGreaterThanOrEqual(3)
    })

    it('should highlight next set', () => {
      render(<SetList {...defaultProps} />)

      expect(screen.getByText('NEXT')).toBeInTheDocument()
    })
  })

  describe('Empty State', () => {
    it('should show empty message when no sets', () => {
      render(<SetList {...defaultProps} sets={[]} />)

      expect(screen.getByText('No sets recorded yet')).toBeInTheDocument()
    })
  })

  describe('Loading State', () => {
    it('should show loading skeletons', () => {
      const { container } = render(<SetList {...defaultProps} isLoading />)

      const skeletons = container.querySelectorAll('.animate-pulse')
      expect(skeletons).toHaveLength(3)
    })

    it('should show both sets and skeletons when loading more', () => {
      render(<SetList {...defaultProps} isLoading />)

      // Should show existing sets
      expect(screen.getByText('135 lbs × 10')).toBeInTheDocument()

      // And loading skeletons
      const { container } = render(<SetList {...defaultProps} isLoading />)
      expect(container.querySelector('.animate-pulse')).toBeInTheDocument()
    })
  })

  describe('Interactions', () => {
    it('should call onSetClick with correct index', () => {
      render(<SetList {...defaultProps} />)

      // Click first working set
      fireEvent.click(screen.getByLabelText('Set 1: 10 reps at 135 lbs'))
      expect(defaultProps.onSetClick).toHaveBeenCalledWith(0)
    })

    it('should handle click on warmup sets', () => {
      render(<SetList {...defaultProps} />)

      // Click first warmup set
      fireEvent.click(screen.getByLabelText('Set 1: 10 reps at 45 lbs'))
      expect(defaultProps.onSetClick).toHaveBeenCalledWith(0)
    })
  })

  describe('Touch Targets', () => {
    it('should have minimum height for touch targets', () => {
      render(<SetList {...defaultProps} />)

      const buttons = screen.getAllByRole('button')
      buttons.forEach((button) => {
        expect(button).toHaveClass('min-h-[44px]')
      })
    })
  })

  describe('Visual Styling', () => {
    it('should apply warmup styling', () => {
      render(<SetList {...defaultProps} />)

      const warmupButton = screen.getByLabelText('Set 1: 10 reps at 45 lbs')
      expect(warmupButton).toHaveClass('border-orange-200', 'bg-orange-50')
    })

    it('should apply working set styling', () => {
      render(<SetList {...defaultProps} />)

      const workingButton = screen.getByLabelText('Set 1: 10 reps at 135 lbs')
      expect(workingButton).toHaveClass('border-gray-200', 'bg-white')
    })

    it('should show ring for next set', () => {
      render(<SetList {...defaultProps} />)

      const nextButton = screen.getByLabelText('Set 2: 8 reps at 135 lbs')
      expect(nextButton).toHaveClass('ring-2', 'ring-blue-500')
    })
  })

  describe('Different Weight Units', () => {
    it('should display kg units correctly', () => {
      const kgSets: WorkoutLogSerieModel[] = [
        {
          ExerciseId: 1,
          Weight: { Value: 60, Unit: 'kg' },
          Reps: 10,
          IsWarmups: false,
          IsNext: false,
          IsFinished: true,
        },
      ]

      render(<SetList {...defaultProps} sets={kgSets} />)
      expect(screen.getByText('60 kg × 10')).toBeInTheDocument()
    })
  })
})
