import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { WorkoutOverview } from '../WorkoutOverview'
import * as useWorkoutModule from '@/hooks/useWorkout'
import { createMockUseWorkoutReturn } from './WorkoutOverview.test.helpers'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

describe('WorkoutOverview - Skeleton Loading', () => {
  it('should show skeletons while loading workout data', () => {
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(
      createMockUseWorkoutReturn({
        todaysWorkout: null,
        isLoadingWorkout: true,
        workoutError: null,
      })
    )

    render(<WorkoutOverview />)

    // Should show skeleton loaders instead of spinner
    expect(screen.getByTestId('workout-card-skeleton')).toBeInTheDocument()

    // Should show multiple exercise skeletons
    const exerciseSkeletons = screen.getAllByTestId('exercise-item-skeleton')
    expect(exerciseSkeletons).toHaveLength(5) // Default to 5 exercise skeletons

    // Should not show the old loading spinner
    expect(screen.queryByTestId('navigation-loading')).not.toBeInTheDocument()
  })

  it('should maintain layout consistency during loading', () => {
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(
      createMockUseWorkoutReturn({
        todaysWorkout: null,
        isLoadingWorkout: true,
        workoutError: null,
      })
    )

    const { container } = render(<WorkoutOverview />)

    // Should have the same layout structure as loaded state
    expect(container.querySelector('.h-full.bg-bg-primary')).toBeTruthy()
    expect(container.querySelector('.p-4')).toBeTruthy()
    expect(container.querySelector('.mx-auto.max-w-lg')).toBeTruthy()

    // Should show start button even during loading (disabled)
    const startButton = screen.getByTestId('start-workout-button')
    expect(startButton).toBeInTheDocument()
    expect(startButton).toBeDisabled()
  })

  it('should have proper accessibility attributes on skeletons', () => {
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(
      createMockUseWorkoutReturn({
        todaysWorkout: null,
        isLoadingWorkout: true,
        workoutError: null,
      })
    )

    render(<WorkoutOverview />)

    // Check workout card skeleton
    const workoutSkeleton = screen.getByTestId('workout-card-skeleton')
    expect(workoutSkeleton).toHaveAttribute('role', 'status')
    expect(workoutSkeleton).toHaveAttribute('aria-busy', 'true')

    // Check exercise skeletons
    const exerciseSkeletons = screen.getAllByTestId('exercise-item-skeleton')
    exerciseSkeletons.forEach((skeleton) => {
      expect(skeleton).toHaveAttribute('role', 'status')
      expect(skeleton).toHaveAttribute('aria-busy', 'true')
    })
  })

  it('should show title skeleton while loading', () => {
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(
      createMockUseWorkoutReturn({
        todaysWorkout: null,
        isLoadingWorkout: true,
        workoutError: null,
      })
    )

    render(<WorkoutOverview />)

    // Should show title skeleton
    const titleSkeleton = screen.getByTestId('workout-title-skeleton')
    expect(titleSkeleton).toBeInTheDocument()
    expect(titleSkeleton).toHaveClass(
      'h-8',
      'w-48',
      'bg-bg-tertiary',
      'rounded',
      'animate-pulse'
    )
  })
})
