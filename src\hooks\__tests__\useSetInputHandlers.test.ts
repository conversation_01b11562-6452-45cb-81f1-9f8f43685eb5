import { renderHook, act } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { useSetInputHandlers } from '../useSetInputHandlers'

describe('useSetInputHandlers', () => {
  const defaultProps = {
    reps: 10,
    weight: 100,
    duration: 30,
    unit: 'lbs' as const,
    onChange: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('reps increment/decrement', () => {
    it('should increment reps by 1', () => {
      const { result } = renderHook(() => useSetInputHandlers(defaultProps))

      act(() => {
        result.current.incrementReps()
      })

      expect(defaultProps.onChange).toHaveBeenCalledWith({
        reps: 11,
        weight: 100,
      })
    })

    it('should decrement reps by 1', () => {
      const { result } = renderHook(() => useSetInputHandlers(defaultProps))

      act(() => {
        result.current.decrementReps()
      })

      expect(defaultProps.onChange).toHaveBeenCalledWith({
        reps: 9,
        weight: 100,
      })
    })

    it('should not decrement reps below 1', () => {
      const { result } = renderHook(() =>
        useSetInputHandlers({ ...defaultProps, reps: 1 })
      )

      act(() => {
        result.current.decrementReps()
      })

      expect(defaultProps.onChange).toHaveBeenCalledWith({
        reps: 1,
        weight: 100,
      })
    })

    it('should not increment reps above 100', () => {
      const { result } = renderHook(() =>
        useSetInputHandlers({ ...defaultProps, reps: 100 })
      )

      act(() => {
        result.current.incrementReps()
      })

      expect(defaultProps.onChange).toHaveBeenCalledWith({
        reps: 100,
        weight: 100,
      })
    })

    it('should work with time-based exercises', () => {
      const { result } = renderHook(() =>
        useSetInputHandlers({ ...defaultProps, isTimeBased: true })
      )

      act(() => {
        result.current.incrementReps()
      })

      // For time-based exercises, reps shouldn't be included
      expect(defaultProps.onChange).not.toHaveBeenCalled()
    })
  })

  describe('weight increment/decrement', () => {
    it('should increment weight by 5 for lbs', () => {
      const { result } = renderHook(() => useSetInputHandlers(defaultProps))

      act(() => {
        result.current.incrementWeight()
      })

      expect(defaultProps.onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 105,
      })
    })

    it('should increment weight by 2.5 for kg', () => {
      const { result } = renderHook(() =>
        useSetInputHandlers({ ...defaultProps, unit: 'kg' })
      )

      act(() => {
        result.current.incrementWeight()
      })

      expect(defaultProps.onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 102.5,
      })
    })

    it('should not increment weight above 1000', () => {
      const { result } = renderHook(() =>
        useSetInputHandlers({ ...defaultProps, weight: 998 })
      )

      act(() => {
        result.current.incrementWeight()
      })

      expect(defaultProps.onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 1000,
      })
    })

    it('should not decrement weight below 0', () => {
      const { result } = renderHook(() =>
        useSetInputHandlers({ ...defaultProps, weight: 2 })
      )

      act(() => {
        result.current.decrementWeight()
      })

      expect(defaultProps.onChange).toHaveBeenCalledWith({
        reps: 10,
        weight: 0,
      })
    })
  })
})
