import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  PerformanceMonitor,
  PerformanceMarks,
  measureDuration,
  logPerformanceMetrics,
} from '../performance'

describe('Performance Monitoring', () => {
  let mockPerformance: {
    mark: vi.MockedFunction<Performance['mark']>
    measure: vi.MockedFunction<Performance['measure']>
    getEntriesByType: vi.MockedFunction<Performance['getEntriesByType']>
    getEntriesByName: vi.MockedFunction<Performance['getEntriesByName']>
    clearMarks: vi.MockedFunction<Performance['clearMarks']>
    clearMeasures: vi.MockedFunction<Performance['clearMeasures']>
    now?: vi.MockedFunction<() => number>
  }
  let consoleLogSpy: vi.SpyInstance

  beforeEach(() => {
    // Mock performance API
    mockPerformance = {
      mark: vi.fn(),
      measure: vi.fn(),
      getEntriesByType: vi.fn(() => []),
      getEntriesByName: vi.fn(() => []),
      clearMarks: vi.fn(),
      clearMeasures: vi.fn(),
    }
    ;(global as typeof globalThis).performance = mockPerformance as Performance

    // Mock console.log
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation()
  })

  afterEach(() => {
    vi.clearAllMocks()
    // Reset NODE_ENV
    process.env.NODE_ENV = 'test'
  })

  describe('PerformanceMonitor', () => {
    it('should mark performance events', () => {
      PerformanceMonitor.mark(PerformanceMarks.LOGIN_START)

      expect(mockPerformance.mark).toHaveBeenCalledWith(
        PerformanceMarks.LOGIN_START
      )
    })

    it('should measure duration between marks', () => {
      const startMark = PerformanceMarks.LOGIN_START
      const endMark = PerformanceMarks.LOGIN_SUCCESS

      // Mock getEntriesByName to return a measure
      mockPerformance.getEntriesByName.mockReturnValue([{ duration: 1500 }])

      PerformanceMonitor.mark(startMark)
      PerformanceMonitor.mark(endMark)
      const duration = PerformanceMonitor.measure(
        'login-duration',
        startMark,
        endMark
      )

      expect(mockPerformance.measure).toHaveBeenCalledWith(
        'login-duration',
        startMark,
        endMark
      )
      expect(duration).toBe(1500)
    })

    it('should return null if measure fails', () => {
      mockPerformance.measure.mockImplementation(() => {
        throw new Error('Performance API not supported')
      })

      const duration = PerformanceMonitor.measure(
        'test-measure',
        PerformanceMarks.LOGIN_START,
        PerformanceMarks.LOGIN_SUCCESS
      )

      expect(duration).toBeNull()
    })

    it('should track API call duration', async () => {
      const mockApiCall = vi.fn().mockResolvedValue({ data: 'test' })

      // Mock measure to return duration
      mockPerformance.getEntriesByName.mockReturnValue([{ duration: 150 }])

      const result = await PerformanceMonitor.trackApiCall(
        'getUserWorkout',
        mockApiCall
      )

      expect(result).toEqual({ data: 'test' })
      expect(mockPerformance.mark).toHaveBeenCalledWith(
        'api-getUserWorkout-start'
      )
      expect(mockPerformance.mark).toHaveBeenCalledWith(
        'api-getUserWorkout-end'
      )
      expect(mockPerformance.measure).toHaveBeenCalledWith(
        'api-getUserWorkout',
        'api-getUserWorkout-start',
        'api-getUserWorkout-end'
      )
    })

    it('should clear all marks and measures', () => {
      PerformanceMonitor.clear()

      expect(mockPerformance.clearMarks).toHaveBeenCalled()
      expect(mockPerformance.clearMeasures).toHaveBeenCalled()
    })

    it('should get all performance entries', () => {
      const mockEntries = [
        { name: 'test-mark', startTime: 100 },
        { name: 'test-measure', duration: 50 },
      ]
      mockPerformance.getEntriesByType.mockReturnValue(mockEntries)

      const entries = PerformanceMonitor.getEntries()

      expect(entries).toEqual(mockEntries)
    })

    it('should log metrics in development mode', () => {
      process.env.NODE_ENV = 'development'

      const metrics = {
        loginToInteractive: 2500,
        apiCalls: {
          getUserProgramInfo: 300,
          getUserWorkout: 250,
        },
      }

      PerformanceMonitor.logMetrics(metrics)

      expect(consoleLogSpy).toHaveBeenCalledWith('[Performance]', metrics)
    })

    it('should not log metrics in production mode', () => {
      process.env.NODE_ENV = 'production'

      const metrics = { test: 'data' }

      PerformanceMonitor.logMetrics(metrics)

      expect(consoleLogSpy).not.toHaveBeenCalled()
    })
  })

  describe('measureDuration', () => {
    it('should measure execution time of a function', () => {
      // Mock performance.now()
      let callCount = 0
      mockPerformance.now = vi.fn(() => {
        callCount++
        return callCount === 1 ? 1000 : 1050 // 50ms duration
      })

      const testFn = vi.fn(() => 'result')
      const { result, duration } = measureDuration('test-operation', testFn)

      expect(result).toBe('result')
      expect(duration).toBe(50)
      expect(testFn).toHaveBeenCalled()
    })

    it('should measure execution time of async function', async () => {
      // Mock performance.now()
      let callCount = 0
      mockPerformance.now = vi.fn(() => {
        callCount++
        return callCount === 1 ? 2000 : 2000.5 // 0.5ms duration for sync measurement
      })

      const testFn = vi.fn().mockResolvedValue('async result')
      const measureResult = measureDuration('async-operation', testFn)

      // Since measureDuration doesn't await async functions,
      // we need to await the result separately
      const actualResult = await measureResult.result
      const { duration } = measureResult

      expect(actualResult).toBe('async result')
      expect(duration).toBe(0.5) // Sync measurement only
      expect(testFn).toHaveBeenCalled()
    })
  })

  describe('logPerformanceMetrics', () => {
    it('should log performance metrics in development', () => {
      process.env.NODE_ENV = 'development'

      // Mock performance entries
      mockPerformance.getEntriesByType.mockImplementation((type: string) => {
        if (type === 'measure') {
          return [
            { name: 'login-to-interactive', duration: 2500 },
            { name: 'api-getUserWorkout', duration: 300 },
          ]
        }
        return []
      })

      logPerformanceMetrics()

      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('=== Performance Metrics ===')
      )
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('login-to-interactive: 2500.00ms')
      )
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('api-getUserWorkout: 300.00ms')
      )
    })

    it('should not log in production', () => {
      process.env.NODE_ENV = 'production'

      logPerformanceMetrics()

      expect(consoleLogSpy).not.toHaveBeenCalled()
    })
  })

  describe('Performance Observer', () => {
    it('should handle missing Performance API gracefully', () => {
      ;(global as typeof globalThis).performance =
        undefined as unknown as Performance

      expect(() => {
        PerformanceMonitor.mark(PerformanceMarks.LOGIN_START)
      }).not.toThrow()

      expect(() => {
        PerformanceMonitor.measure('test', 'start', 'end')
      }).not.toThrow()
    })
  })
})
