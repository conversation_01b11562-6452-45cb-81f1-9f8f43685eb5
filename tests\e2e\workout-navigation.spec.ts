import { test, expect } from '@playwright/test'

// Test credentials from docs
const TEST_EMAIL = '<EMAIL>'
const TEST_PASSWORD = 'Dr123456'

test.describe('Workout Navigation', () => {
  test('should navigate from program page to workout page without showing "No workout available"', async ({
    page,
  }) => {
    // Start at login page
    await page.goto('/login')

    // Login
    await page.fill('input[type="email"]', TEST_EMAIL)
    await page.fill('input[type="password"]', TEST_PASSWORD)
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('/program', { timeout: 10000 })

    // Wait for program page to load - look for any stats to be visible
    await expect(page.locator('text="Weeks streak"').first()).toBeVisible({
      timeout: 15000,
    })

    // Click "Continue to Workout" button
    await page.click('button:has-text("Continue to Workout")')

    // Wait for navigation to workout page
    await page.waitForURL('/workout', { timeout: 10000 })

    // Verify workout page loads without "No workout available" message
    await expect(page.locator('text="No workout available"')).not.toBeVisible({
      timeout: 5000,
    })

    // Verify workout content is displayed
    const workoutTitle = page.locator('h1').first()
    await expect(workoutTitle).toBeVisible({ timeout: 10000 })

    // Verify exercises are shown
    const exerciseCount = page.locator('text=/\\d+ exercises/')
    await expect(exerciseCount).toBeVisible({ timeout: 10000 })

    // Verify Start Workout button is visible
    await expect(page.locator('button:has-text("Start Workout")')).toBeVisible({
      timeout: 10000,
    })
  })

  test('should handle offline mode gracefully when navigating to workout', async ({
    page,
    context,
  }) => {
    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_EMAIL)
    await page.fill('input[type="password"]', TEST_PASSWORD)
    await page.click('button[type="submit"]')

    // Wait for program page
    await page.waitForURL('/program', { timeout: 10000 })
    await expect(page.locator('text="Weeks streak"').first()).toBeVisible({
      timeout: 15000,
    })

    // Set offline mode
    await context.setOffline(true)

    // Try to navigate to workout
    await page.click('button:has-text("Continue to Workout")')

    // Should still navigate and show cached data or offline indicator
    await page.waitForURL('/workout', { timeout: 10000 })

    // Should not show "No workout available" but may show offline indicator
    await expect(page.locator('text="No workout available"')).not.toBeVisible({
      timeout: 5000,
    })

    // Verify offline mode indicator if present
    const offlineIndicator = page.locator('text="Offline Mode"')
    const hasOfflineIndicator = await offlineIndicator
      .isVisible()
      .catch(() => false)

    if (hasOfflineIndicator) {
      expect(hasOfflineIndicator).toBe(true)
    }
  })
})
