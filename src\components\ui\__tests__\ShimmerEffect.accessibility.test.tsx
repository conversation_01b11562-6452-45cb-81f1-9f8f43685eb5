import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ShimmerEffect, ShimmerOverlay } from '../ShimmerEffect'

describe('ShimmerEffect - Accessibility', () => {
  describe('ARIA attributes', () => {
    it('should have proper role and aria attributes', () => {
      render(<ShimmerEffect />)

      const shimmer = screen.getByRole('status')
      expect(shimmer).toHaveAttribute('aria-label', 'Loading')
      expect(shimmer).toHaveAttribute('aria-busy', 'true')
    })

    it('should use custom aria label', () => {
      render(<ShimmerEffect ariaLabel="Loading user statistics" />)

      const shimmer = screen.getByRole('status')
      expect(shimmer).toHaveAttribute('aria-label', 'Loading user statistics')
    })

    it('should include screen reader only text', () => {
      render(<ShimmerEffect ariaLabel="Loading content" />)

      const srText = screen.getByText('Loading content')
      expect(srText).toHaveClass('sr-only')
    })

    it('should hide visual effect from screen readers', () => {
      const { container } = render(<ShimmerEffect />)

      const visualEffect = container.querySelector('.shimmer-effect')
      expect(visualEffect).toHaveAttribute('aria-hidden', 'true')
    })
  })

  describe('ShimmerOverlay accessibility', () => {
    it('should be hidden from screen readers', () => {
      render(<ShimmerOverlay show />)

      const overlay = screen.getByTestId('shimmer-overlay')
      expect(overlay).toHaveAttribute('aria-hidden', 'true')
    })

    it('should pass custom aria label to inner shimmer', () => {
      render(<ShimmerOverlay show ariaLabel="Loading metrics" />)

      // The shimmer is inside an aria-hidden container, so we need to check with hidden: true
      const shimmer = screen.getByRole('status', { hidden: true })
      expect(shimmer).toHaveAttribute('aria-label', 'Loading metrics')
    })

    it('should not render when show is false', () => {
      render(<ShimmerOverlay show={false} />)

      expect(screen.queryByTestId('shimmer-overlay')).not.toBeInTheDocument()
    })
  })

  describe('Reduced motion preference', () => {
    it('should disable animation for reduced motion preference', () => {
      const mockMatchMedia = vi.fn().mockReturnValue({
        matches: true,
        media: '(prefers-reduced-motion: reduce)',
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      })
      vi.stubGlobal('matchMedia', mockMatchMedia)

      const { container } = render(<ShimmerEffect />)

      // Check that CSS includes reduced motion styles
      const style = container.querySelector('style')
      expect(style?.textContent).toContain(
        '@media (prefers-reduced-motion: reduce)'
      )
      expect(style?.textContent).toContain('animation: none !important')
    })
  })

  describe('Focus management', () => {
    it('should not be focusable', () => {
      const { container } = render(<ShimmerEffect />)

      const focusableElements = container.querySelectorAll(
        'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
      )
      expect(focusableElements).toHaveLength(0)
    })

    it('overlay should have pointer-events-none', () => {
      render(<ShimmerOverlay show />)

      const overlay = screen.getByTestId('shimmer-overlay')
      expect(overlay).toHaveClass('pointer-events-none')
    })
  })
})
