type ConnectionQuality = 'fast' | 'slow' | 'offline'
type NetworkStatusCallback = (isOnline: boolean) => void

export class NetworkStatusManager {
  private subscribers: Set<NetworkStatusCallback> = new Set()

  private _isOnline: boolean = navigator.onLine

  constructor() {
    this.setupEventListeners()
  }

  get isOnline(): boolean {
    return this._isOnline
  }

  private setupEventListeners() {
    window.addEventListener('online', this.handleOnline)
    window.addEventListener('offline', this.handleOffline)
  }

  private handleOnline = () => {
    this._isOnline = true
    this.notifySubscribers(true)
  }

  private handleOffline = () => {
    this._isOnline = false
    this.notifySubscribers(false)
  }

  private notifySubscribers(isOnline: boolean) {
    this.subscribers.forEach((callback) => {
      try {
        callback(isOnline)
      } catch (error) {
        console.warn('Error in network status callback:', error)
      }
    })
  }

  subscribe(callback: NetworkStatusCallback): () => void {
    this.subscribers.add(callback)

    // Return unsubscribe function
    return () => {
      this.subscribers.delete(callback)
    }
  }

  async checkConnectionQuality(
    testFunction?: () => Promise<unknown>
  ): Promise<ConnectionQuality> {
    if (!this._isOnline) {
      return 'offline'
    }

    const startTime = Date.now()

    try {
      if (testFunction) {
        await testFunction()
      } else {
        // Default test: fetch a small resource
        await fetch('/favicon.ico', {
          method: 'HEAD',
          cache: 'no-cache',
        })
      }

      const duration = Date.now() - startTime

      // Consider connection slow if it takes more than 2 seconds
      return duration > 2000 ? 'slow' : 'fast'
    } catch (error) {
      return 'offline'
    }
  }

  destroy() {
    window.removeEventListener('online', this.handleOnline)
    window.removeEventListener('offline', this.handleOffline)
    this.subscribers.clear()
  }
}
