import type {
  GetUserWorkoutLogAverageResponse,
  WorkoutTemplateModel,
  ExerciseModel,
} from '@/types'

export function createMockProgramInfo(
  overrides?: Partial<GetUserWorkoutLogAverageResponse>
): GetUserWorkoutLogAverageResponse {
  return {
    Sets: [],
    Histograms: [],
    GetUserProgramInfoResponseModel: {
      RecommendedProgram: {
        Id: 1,
        Label: 'Test Program',
        RemainingToLevelUp: 0,
      },
      NextWorkoutTemplate: {
        Id: 1,
        Label: 'Workout A',
        IsSystemExercise: true,
      },
    },
    ...overrides,
  } as GetUserWorkoutLogAverageResponse
}

export function createMockWorkouts(
  exercises?: ExerciseModel[]
): WorkoutTemplateModel[] {
  return [
    {
      Id: 1,
      Label: 'Workout A',
      Exercises: exercises || [
        {
          Id: 1,
          Label: 'Bench Press',
          IsSystemExercise: true,
          IsSwapTarget: false,
          IsFinished: false,
          BodyPartId: 1,
          IsUnilateral: false,
          IsTimeBased: false,
          IsEasy: false,
          IsMedium: false,
          IsBodyweight: false,
          VideoUrl: '',
          IsNextExercise: false,
          IsPlate: true,
          IsWeighted: true,
          IsPyramid: false,
          IsNormalSets: true,
          IsBodypartPriority: false,
          IsFlexibility: false,
          IsOneHanded: false,
          LocalVideo: '',
          IsAssisted: false,
        },
      ],
      UserId: 'test',
      IsSystemExercise: true,
      WorkoutSettingsModel: {},
    },
  ]
}
