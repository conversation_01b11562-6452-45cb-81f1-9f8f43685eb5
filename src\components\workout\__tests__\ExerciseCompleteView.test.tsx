import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { ExerciseCompleteView } from '../ExerciseCompleteView'

describe('ExerciseCompleteView', () => {
  it('should render with theme-aware colors', () => {
    render(
      <ExerciseCompleteView
        exerciseLabel="Bench Press"
        isLastExercise={false}
        nextExerciseLabel="Squats"
      />
    )

    // Check that the SVG icon uses theme brand color
    const svg = screen.getByRole('img', { hidden: true })
    expect(svg).toHaveClass('text-brand-primary')

    // Check that the heading uses theme text color
    const heading = screen.getByText('Great job!')
    expect(heading).toHaveClass('text-text-primary')

    // Check that the exercise complete text uses theme secondary color
    const completeText = screen.getByText(/Bench Press complete/)
    expect(completeText).toHaveClass('text-text-secondary')

    // Check that the next exercise text uses theme tertiary color
    const nextText = screen.getByText(/Moving to Squats/)
    expect(nextText).toHaveClass('text-text-tertiary')
  })

  it('should render correctly for last exercise', () => {
    render(<ExerciseCompleteView exerciseLabel="Deadlifts" isLastExercise />)

    const finalText = screen.getByText('Final exercise done!')
    expect(finalText).toHaveClass('text-text-tertiary')
  })

  it('should handle missing exercise labels', () => {
    render(<ExerciseCompleteView isLastExercise={false} />)

    expect(screen.getByText(/Exercise complete/)).toBeInTheDocument()
    expect(screen.getByText(/Moving to next exercise/)).toBeInTheDocument()
  })
})
