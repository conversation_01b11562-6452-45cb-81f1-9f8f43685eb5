import { describe, it, expect } from 'vitest'
import fs from 'fs'
import path from 'path'

describe('Service Worker RSC Configuration', () => {
  it('should have exclude configuration for RSC requests in next.config.js', () => {
    // Read the next.config.js file content
    const configPath = path.join(process.cwd(), 'next.config.js')
    const configContent = fs.readFileSync(configPath, 'utf-8')

    // Verify the exclude configuration exists
    expect(configContent).toContain('exclude: [')
    expect(configContent).toContain("request.url.includes('_rsc=')")
    expect(configContent).toContain(
      '// Exclude RSC requests from service worker caching'
    )
  })

  it('should have exclude configuration for Next.js internal requests', () => {
    // Read the next.config.js file content
    const configPath = path.join(process.cwd(), 'next.config.js')
    const configContent = fs.readFileSync(configPath, 'utf-8')

    // Verify Next.js internal request exclusion
    expect(configContent).toContain("request.url.includes('__nextjs')")
    expect(configContent).toContain('// Also exclude Next.js internal requests')
  })

  it('should test the exclude function logic', () => {
    // Test the exclude function logic independently
    const excludeFunctions = [
      ({ request }: { request: { url: string } }) =>
        request.url.includes('_rsc='),
      ({ request }: { request: { url: string } }) =>
        request.url.includes('__nextjs'),
    ]

    // Test RSC request is excluded
    const rscRequest = {
      url: 'https://x.dr-muscle.com/workout/exercise/30431?_rsc=x9ipq',
    }
    const isRscExcluded = excludeFunctions.some((fn) =>
      fn({ request: rscRequest })
    )
    expect(isRscExcluded).toBe(true)

    // Test Next.js internal request is excluded
    const nextjsRequest = {
      url: 'https://x.dr-muscle.com/_next/__nextjs_original-stack-frame',
    }
    const isNextjsExcluded = excludeFunctions.some((fn) =>
      fn({ request: nextjsRequest })
    )
    expect(isNextjsExcluded).toBe(true)

    // Test normal API request is NOT excluded
    const apiRequest = {
      url: 'https://api.drmuscle.com/api/Workout/GetUserWorkout',
    }
    const isApiExcluded = excludeFunctions.some((fn) =>
      fn({ request: apiRequest })
    )
    expect(isApiExcluded).toBe(false)
  })
})
