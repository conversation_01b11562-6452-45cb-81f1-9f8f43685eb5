# Console Errors Infinite Loop Fix

## Problem Description

When opening an exercise page (e.g., `/workout/exercise/27474`), console errors were piling up rapidly, reaching 1000+ errors and continuing to grow. The server logs showed infinite repeated calls to `/api/auth/token`.

## Root Cause Analysis

The issue was caused by **multiple instances** of the `useAuthTokenRestore` hook being called simultaneously:

1. **Providers.tsx** (line 35) - Main app provider (✅ correct usage)
2. **useWorkoutRecommendations.ts** (line 29) - Used in exercise pages (❌ duplicate)
3. **useWorkoutDataLoader.ts** (line 33) - Used in workout pages (❌ duplicate)

Each hook instance was independently calling `restoreAuthToken()`, which makes a fetch request to `/api/auth/token`. Since these hooks were all running on the same page, they created an infinite loop of API calls.

### Dependency Chain Issues

Additionally, there were React dependency issues causing infinite re-renders:

1. **useWorkout.ts** - `recommendations` object in dependency array caused infinite preloading
2. **useWorkoutRecommendations.ts** - `canMakeApiCalls` variable changed on every render
3. **ExercisePageClient.tsx** - Missing dependencies in useEffect hooks

## Solution Implemented

### 1. Remove Duplicate Auth Token Restoration

**Files Modified:**

- `src/hooks/useWorkoutRecommendations.ts`
- `src/hooks/useWorkoutDataLoader.ts`

**Changes:**

- Removed `import { useAuthTokenRestore } from '@/hooks/useAuthTokenRestore'`
- Removed `const { isRestoringToken } = useAuthTokenRestore()`
- Updated auth state checks to only use `isAuthenticated && hasHydrated`

### 2. Fix Infinite Re-render Issues

**useWorkout.ts:**

```typescript
// BEFORE (caused infinite loop)
}, [state.currentWorkout?.Id, state.exercises.length, recommendations])

// AFTER (fixed)
}, [state.currentWorkout?.Id, state.exercises.length])
// Removed 'recommendations' to prevent infinite loops
```

**useWorkoutRecommendations.ts:**

```typescript
// BEFORE (changed on every render)
const canMakeApiCalls = isAuthenticated && hasHydrated && !isRestoringToken

// AFTER (stable function using refs)
const canMakeApiCalls = () =>
  authStateRef.current.isAuthenticated && authStateRef.current.hasHydrated
```

**ExercisePageClient.tsx:**

- Added `useRef` to track loading initiation and prevent duplicate calls
- Reduced console logging frequency to prevent spam
- Added proper error handling guards

### 3. Optimize Console Logging

- Guarded all console.error calls with development environment checks
- Reduced logging frequency in render effects
- Added loading state tracking to prevent duplicate logs

## Results

### Before Fix:

- 1000+ console errors and growing
- Infinite `/api/auth/token` API calls (50+ calls in seconds)
- Page performance degradation
- Server resource waste

### After Fix (Partial):

- ✅ Console errors significantly reduced
- ✅ `/api/auth/token` calls reduced from 50+ per second to 1-2 per page navigation
- ✅ API calls now complete in 6-29ms (vs 100ms+ before)
- ✅ Proper error handling implemented
- ⚠️ Still some duplicate calls during page navigation (Next.js layout re-rendering issue)

## Key Principles Applied

1. **Single Responsibility**: Auth token restoration should only happen once at the app level
2. **Stable Dependencies**: Use refs for values that shouldn't trigger re-renders
3. **Proper Error Handling**: Guard console logs and handle errors gracefully
4. **Performance Optimization**: Prevent unnecessary API calls and re-renders

## Testing

Verified the fix by:

1. Opening exercise page `/workout/exercise/27474`
2. Monitoring server logs for API calls
3. Checking browser console for errors
4. Confirming normal page functionality

## Prevention

To prevent similar issues in the future:

1. Only call `useAuthTokenRestore` in `Providers.tsx`
2. Use refs for values that shouldn't trigger re-renders
3. Be careful with dependency arrays in useEffect hooks
4. Always guard console logs with environment checks
5. Test for infinite loops during development

## Additional Improvements Made

### 4. Singleton Pattern for Token Restoration

**File:** `src/utils/auth/tokenRestore.ts`

Added comprehensive singleton pattern to prevent multiple simultaneous token restoration attempts:

```typescript
// Global state tracking
let isRestoring = false
let restorationPromise: Promise<boolean> | null = null

// SessionStorage persistence across page navigations
function hasRestoredSuccessfully(): boolean {
  return sessionStorage.getItem('dr-muscle-token-restored') === 'true'
}

// Check if token already exists in API client
if (hasAuthToken()) {
  return true // Skip restoration
}
```

This reduces API calls from 50+ per second to 1-2 per page navigation.

## Current Status

The infinite loop issue has been **significantly improved** but not completely eliminated:

- **Before**: 1000+ console errors, 50+ API calls per second
- **After**: Minimal console errors, 1-2 API calls per page navigation
- **Remaining**: Some duplicate calls during Next.js page navigation (layout re-rendering)

The remaining calls are much faster (6-29ms vs 100ms+) and don't cause the exponential growth of errors.

## Files Changed

- `src/hooks/useWorkout.ts`
- `src/hooks/useWorkoutRecommendations.ts`
- `src/hooks/useWorkoutDataLoader.ts`
- `src/app/workout/exercise/[id]/ExercisePageClient.tsx`
- `src/utils/auth/tokenRestore.ts`
- `src/hooks/useAuthTokenRestore.ts`
