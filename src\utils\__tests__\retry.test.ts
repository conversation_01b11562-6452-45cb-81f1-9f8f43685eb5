import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  retryWithBackoff,
  retryWithDetails,
  calculateBackoff,
  createRetryFn,
  isRetryableError,
} from '../retry'

describe('retry', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.restoreAllMocks()
    vi.useRealTimers()
  })

  describe('retryWithBackoff', () => {
    it('should return successful result without retry', async () => {
      const fn = vi.fn().mockResolvedValue('success')

      const result = await retryWithBackoff(fn)

      expect(result).toBe('success')
      expect(fn).toHaveBeenCalledTimes(1)
    })

    it('should retry on failure with exponential backoff', async () => {
      const fn = vi
        .fn()
        .mockRejectedValueOnce(new Error('Fail 1'))
        .mockRejectedValueOnce(new Error('Fail 2'))
        .mockResolvedValue('success')

      const promise = retryWithBackoff(fn, {
        maxRetries: 3,
        baseDelay: 100,
        jitter: 0, // Disable jitter for predictable timing
      })

      // First attempt fails immediately
      await vi.advanceTimersByTimeAsync(0)
      expect(fn).toHaveBeenCalledTimes(1)

      // Wait for first retry delay (100ms)
      await vi.advanceTimersByTimeAsync(100)
      expect(fn).toHaveBeenCalledTimes(2)

      // Wait for second retry delay (200ms)
      await vi.advanceTimersByTimeAsync(200)
      expect(fn).toHaveBeenCalledTimes(3)

      const result = await promise
      expect(result).toBe('success')
    })

    it('should enforce max retry limit', async () => {
      const fn = vi.fn().mockRejectedValue(new Error('Always fails'))

      const promise = retryWithBackoff(fn, {
        maxRetries: 2,
        baseDelay: 10,
        jitter: 0,
      })

      // Advance through all retries
      await vi.advanceTimersByTimeAsync(0) // Initial attempt
      await vi.advanceTimersByTimeAsync(10) // First retry
      await vi.advanceTimersByTimeAsync(20) // Second retry

      await expect(promise).rejects.toThrow('Always fails')
      expect(fn).toHaveBeenCalledTimes(3) // Initial + 2 retries
    })

    it('should apply jitter to prevent thundering herd', async () => {
      const fn = vi
        .fn()
        .mockRejectedValueOnce(new Error('Fail'))
        .mockResolvedValue('success')

      // Mock Math.random to return predictable values
      const mockRandom = vi.spyOn(Math, 'random')
      mockRandom.mockReturnValue(0.5) // This will produce no jitter (0.5 * 2 - 1 = 0)

      const promise = retryWithBackoff(fn, {
        maxRetries: 1,
        baseDelay: 100,
        jitter: 0.2,
      })

      await vi.advanceTimersByTimeAsync(0)
      await vi.advanceTimersByTimeAsync(100) // Base delay with no jitter

      const result = await promise
      expect(result).toBe('success')
      expect(mockRandom).toHaveBeenCalled()
    })

    it('should handle abort signal', async () => {
      const fn = vi.fn().mockResolvedValue('success')
      const controller = new AbortController()

      // Abort before starting
      controller.abort()

      const promise = retryWithBackoff(fn, {
        signal: controller.signal,
      })

      await expect(promise).rejects.toThrow('Operation aborted')
      expect(fn).toHaveBeenCalledTimes(0)
    })

    it('should abort during retry delay', async () => {
      const fn = vi
        .fn()
        .mockRejectedValueOnce(new Error('Fail'))
        .mockResolvedValue('success')

      const controller = new AbortController()

      const promise = retryWithBackoff(fn, {
        maxRetries: 1,
        baseDelay: 100,
        signal: controller.signal,
        jitter: 0,
      })

      // First attempt fails
      await vi.advanceTimersByTimeAsync(0)
      expect(fn).toHaveBeenCalledTimes(1)

      // Abort during the retry delay
      controller.abort()
      await vi.advanceTimersByTimeAsync(50)

      await expect(promise).rejects.toThrow('Sleep aborted')
      expect(fn).toHaveBeenCalledTimes(1) // No second attempt
    })

    it('should call onRetry callback', async () => {
      const fn = vi
        .fn()
        .mockRejectedValueOnce(new Error('Fail 1'))
        .mockRejectedValueOnce(new Error('Fail 2'))
        .mockResolvedValue('success')

      const onRetry = vi.fn()

      const promise = retryWithBackoff(fn, {
        maxRetries: 2,
        baseDelay: 10,
        onRetry,
        jitter: 0,
      })

      await vi.advanceTimersByTimeAsync(0)
      await vi.advanceTimersByTimeAsync(10)
      await vi.advanceTimersByTimeAsync(20)

      await promise

      expect(onRetry).toHaveBeenCalledTimes(2)
      expect(onRetry).toHaveBeenCalledWith(new Error('Fail 1'), 1)
      expect(onRetry).toHaveBeenCalledWith(new Error('Fail 2'), 2)
    })

    it('should handle non-Error exceptions', async () => {
      const fn = vi.fn().mockRejectedValue('string error')

      const promise = retryWithBackoff(fn, { maxRetries: 0 })

      await expect(promise).rejects.toThrow('string error')
    })

    it('should handle zero delay', async () => {
      const fn = vi
        .fn()
        .mockRejectedValueOnce(new Error('Fail'))
        .mockResolvedValue('success')

      const promise = retryWithBackoff(fn, {
        maxRetries: 1,
        baseDelay: 0,
        jitter: 0,
      })

      // Both attempts should happen immediately
      await vi.advanceTimersByTimeAsync(0)

      const result = await promise
      expect(result).toBe('success')
      expect(fn).toHaveBeenCalledTimes(2)
    })

    it('should respect maxDelay limit', async () => {
      const fn = vi.fn().mockRejectedValue(new Error('Always fails'))

      // Start the retry operation but don't await it
      retryWithBackoff(fn, {
        maxRetries: 5,
        baseDelay: 1000,
        maxDelay: 2000,
        factor: 10, // Would result in huge delays without maxDelay
        jitter: 0,
      }).catch(() => {
        // Expected to fail, ignore the error
      })

      // Advance through retries
      await vi.advanceTimersByTimeAsync(0) // Initial attempt
      await vi.advanceTimersByTimeAsync(1000) // First retry (1000ms)
      await vi.advanceTimersByTimeAsync(2000) // Second retry (capped at 2000ms)
      await vi.advanceTimersByTimeAsync(2000) // Third retry (capped at 2000ms)

      expect(fn).toHaveBeenCalledTimes(4)
    })
  })

  describe('retryWithDetails', () => {
    it('should return detailed success result', async () => {
      const fn = vi.fn().mockResolvedValue('data')

      const result = await retryWithDetails(fn)

      expect(result).toEqual({
        success: true,
        data: 'data',
        attempts: 1,
      })
    })

    it('should return detailed failure result', async () => {
      const error = new Error('Failed')
      const fn = vi.fn().mockRejectedValue(error)

      const promise = retryWithDetails(fn, {
        maxRetries: 2,
        baseDelay: 10,
        jitter: 0,
      })

      // Advance through all retries
      await vi.advanceTimersByTimeAsync(0) // Initial attempt
      await vi.advanceTimersByTimeAsync(10) // First retry
      await vi.advanceTimersByTimeAsync(20) // Second retry

      const result = await promise

      expect(result).toEqual({
        success: false,
        error,
        attempts: 3, // Initial + 2 retries
      })
    })

    it('should count attempts correctly with retries', async () => {
      const fn = vi
        .fn()
        .mockRejectedValueOnce(new Error('Fail 1'))
        .mockRejectedValueOnce(new Error('Fail 2'))
        .mockResolvedValue('success')

      const promise = retryWithDetails(fn, {
        maxRetries: 2,
        baseDelay: 10,
        jitter: 0,
      })

      await vi.advanceTimersByTimeAsync(0)
      await vi.advanceTimersByTimeAsync(10)
      await vi.advanceTimersByTimeAsync(20)

      const result = await promise

      expect(result).toEqual({
        success: true,
        data: 'success',
        attempts: 3,
      })
    })

    it('should handle non-Error exceptions in detailed result', async () => {
      const fn = vi.fn().mockRejectedValue('string error')

      const result = await retryWithDetails(fn, { maxRetries: 0 })

      expect(result).toEqual({
        success: false,
        error: new Error('string error'),
        attempts: 1,
      })
    })
  })

  describe('calculateBackoff', () => {
    it('should calculate exponential backoff', () => {
      const options = {
        baseDelay: 100,
        maxDelay: 10000,
        factor: 2,
        jitter: 0,
      }

      expect(calculateBackoff(0, options)).toBe(100) // 100 * 2^0 = 100
      expect(calculateBackoff(1, options)).toBe(200) // 100 * 2^1 = 200
      expect(calculateBackoff(2, options)).toBe(400) // 100 * 2^2 = 400
      expect(calculateBackoff(3, options)).toBe(800) // 100 * 2^3 = 800
    })

    it('should respect maxDelay', () => {
      const options = {
        baseDelay: 1000,
        maxDelay: 3000,
        factor: 2,
        jitter: 0,
      }

      expect(calculateBackoff(0, options)).toBe(1000)
      expect(calculateBackoff(1, options)).toBe(2000)
      expect(calculateBackoff(2, options)).toBe(3000) // Capped at maxDelay
      expect(calculateBackoff(3, options)).toBe(3000) // Still capped
    })

    it('should apply positive jitter', () => {
      const mockRandom = vi.spyOn(Math, 'random')
      mockRandom.mockReturnValue(1) // Max positive jitter

      const options = {
        baseDelay: 1000,
        maxDelay: 10000,
        factor: 2,
        jitter: 0.2,
      }

      // With max positive jitter: 1000 + (1000 * 0.2) = 1200
      expect(calculateBackoff(0, options)).toBe(1200)
    })

    it('should apply negative jitter', () => {
      const mockRandom = vi.spyOn(Math, 'random')
      mockRandom.mockReturnValue(0) // Max negative jitter

      const options = {
        baseDelay: 1000,
        maxDelay: 10000,
        factor: 2,
        jitter: 0.2,
      }

      // With max negative jitter: 1000 - (1000 * 0.2) = 800
      expect(calculateBackoff(0, options)).toBe(800)
    })

    it('should never return negative delay', () => {
      const mockRandom = vi.spyOn(Math, 'random')
      mockRandom.mockReturnValue(0) // Max negative jitter

      const options = {
        baseDelay: 100,
        maxDelay: 10000,
        factor: 2,
        jitter: 2, // 200% jitter could make delay negative
      }

      // Even with extreme negative jitter, should return 0, not negative
      expect(calculateBackoff(0, options)).toBe(0)
    })
  })

  describe('createRetryFn', () => {
    it('should create retry function with preset options', async () => {
      const retry = createRetryFn({
        maxRetries: 1,
        baseDelay: 50,
        jitter: 0,
      })

      const fn = vi
        .fn()
        .mockRejectedValueOnce(new Error('Fail'))
        .mockResolvedValue('success')

      const promise = retry(fn)

      await vi.advanceTimersByTimeAsync(0)
      await vi.advanceTimersByTimeAsync(50)

      const result = await promise
      expect(result).toBe('success')
      expect(fn).toHaveBeenCalledTimes(2)
    })

    it('should allow overriding preset options', async () => {
      const retry = createRetryFn({
        maxRetries: 5,
        baseDelay: 1000,
      })

      const fn = vi.fn().mockResolvedValue('success')

      // Override with different options
      const result = await retry(fn, {
        maxRetries: 0, // No retries
      })

      expect(result).toBe('success')
      expect(fn).toHaveBeenCalledTimes(1)
    })

    it('should work with no preset options', async () => {
      const retry = createRetryFn()
      const fn = vi.fn().mockResolvedValue('success')

      const result = await retry(fn)

      expect(result).toBe('success')
      expect(fn).toHaveBeenCalledTimes(1)
    })
  })

  describe('isRetryableError', () => {
    it('should return false for non-Error values', () => {
      expect(isRetryableError(null)).toBe(false)
      expect(isRetryableError(undefined)).toBe(false)
      expect(isRetryableError('string')).toBe(false)
      expect(isRetryableError(123)).toBe(false)
      expect(isRetryableError({})).toBe(false)
    })

    it('should identify network errors as retryable', () => {
      expect(isRetryableError(new Error('Network request failed'))).toBe(true)
      expect(isRetryableError(new Error('NETWORK_ERROR'))).toBe(true)
      expect(
        isRetryableError(new Error('Connection failed: network down'))
      ).toBe(true)
    })

    it('should identify timeout errors as retryable', () => {
      expect(isRetryableError(new Error('Request timeout'))).toBe(true)
      expect(isRetryableError(new Error('TIMEOUT_ERROR'))).toBe(true)
      expect(isRetryableError(new Error('Connection timeout exceeded'))).toBe(
        true
      )
    })

    it('should identify retryable HTTP status codes', () => {
      // 5xx errors
      expect(
        isRetryableError(
          Object.assign(new Error('Server Error'), { status: 500 })
        )
      ).toBe(true)
      expect(
        isRetryableError(
          Object.assign(new Error('Bad Gateway'), { status: 502 })
        )
      ).toBe(true)
      expect(
        isRetryableError(
          Object.assign(new Error('Service Unavailable'), { status: 503 })
        )
      ).toBe(true)

      // Specific 4xx errors
      expect(
        isRetryableError(
          Object.assign(new Error('Too Many Requests'), { status: 429 })
        )
      ).toBe(true)
      expect(
        isRetryableError(
          Object.assign(new Error('Request Timeout'), { status: 408 })
        )
      ).toBe(true)
    })

    it('should identify non-retryable HTTP status codes', () => {
      expect(
        isRetryableError(
          Object.assign(new Error('Bad Request'), { status: 400 })
        )
      ).toBe(false)
      expect(
        isRetryableError(
          Object.assign(new Error('Unauthorized'), { status: 401 })
        )
      ).toBe(false)
      expect(
        isRetryableError(Object.assign(new Error('Not Found'), { status: 404 }))
      ).toBe(false)
    })

    it('should return false for generic errors', () => {
      expect(isRetryableError(new Error('Generic error'))).toBe(false)
      expect(isRetryableError(new Error('Something went wrong'))).toBe(false)
    })

    it('should handle case-insensitive matching', () => {
      expect(isRetryableError(new Error('NETWORK ERROR'))).toBe(true)
      expect(isRetryableError(new Error('Network Error'))).toBe(true)
      expect(isRetryableError(new Error('nEtWoRk eRrOr'))).toBe(true)
    })
  })

  describe('edge cases', () => {
    it('should handle immediate abort before first attempt', async () => {
      const fn = vi.fn().mockResolvedValue('success')
      const controller = new AbortController()
      controller.abort()

      await expect(
        retryWithBackoff(fn, { signal: controller.signal })
      ).rejects.toThrow('Operation aborted')

      expect(fn).not.toHaveBeenCalled()
    })

    it('should handle zero retries', async () => {
      const fn = vi.fn().mockRejectedValue(new Error('Fail'))

      await expect(retryWithBackoff(fn, { maxRetries: 0 })).rejects.toThrow(
        'Fail'
      )

      expect(fn).toHaveBeenCalledTimes(1)
    })

    it('should handle very large delays', async () => {
      const fn = vi
        .fn()
        .mockRejectedValueOnce(new Error('Fail'))
        .mockResolvedValue('success')

      const promise = retryWithBackoff(fn, {
        maxRetries: 1,
        baseDelay: Number.MAX_SAFE_INTEGER,
        maxDelay: 1000, // Should cap the delay
        jitter: 0,
      })

      await vi.advanceTimersByTimeAsync(0)
      await vi.advanceTimersByTimeAsync(1000)

      const result = await promise
      expect(result).toBe('success')
    })

    it('should handle concurrent retries', async () => {
      const fn1 = vi
        .fn()
        .mockRejectedValueOnce(new Error('Fail'))
        .mockResolvedValue('success1')

      const fn2 = vi
        .fn()
        .mockRejectedValueOnce(new Error('Fail'))
        .mockResolvedValue('success2')

      const promise1 = retryWithBackoff(fn1, {
        maxRetries: 1,
        baseDelay: 100,
        jitter: 0,
      })

      const promise2 = retryWithBackoff(fn2, {
        maxRetries: 1,
        baseDelay: 50,
        jitter: 0,
      })

      // Advance time to handle both retries
      await vi.advanceTimersByTimeAsync(0)
      await vi.advanceTimersByTimeAsync(50) // fn2 retries
      await vi.advanceTimersByTimeAsync(50) // fn1 retries

      const [result1, result2] = await Promise.all([promise1, promise2])

      expect(result1).toBe('success1')
      expect(result2).toBe('success2')
      expect(fn1).toHaveBeenCalledTimes(2)
      expect(fn2).toHaveBeenCalledTimes(2)
    })
  })
})
