/**
 * Performance checking and alerting utilities
 */

import { getUserInfoMetrics, userInfoPerformance } from '../userInfoPerformance'
import { logger } from '../logger'
import type { PerformanceThresholds } from './constants'

export function checkPerformanceViolations(
  thresholds: PerformanceThresholds
): string[] {
  const metrics = getUserInfoMetrics()
  const violations: string[] = []

  // Check login to first byte
  if (
    metrics.loginToFirstByte !== null &&
    metrics.loginToFirstByte > thresholds.loginToFirstByte
  ) {
    violations.push(
      `Login to first byte took ${metrics.loginToFirstByte.toFixed(0)}ms (threshold: ${
        thresholds.loginToFirstByte
      }ms)`
    )
  }

  // Check UserInfo load time
  if (
    metrics.userInfoLoadTime !== null &&
    metrics.userInfoLoadTime > thresholds.userInfoLoadTime
  ) {
    violations.push(
      `UserInfo load took ${metrics.userInfoLoadTime.toFixed(0)}ms (threshold: ${
        thresholds.userInfoLoadTime
      }ms)`
    )
  }

  // Check stats load time
  if (
    metrics.statsLoadTime !== null &&
    metrics.statsLoadTime > thresholds.statsLoadTime
  ) {
    violations.push(
      `Stats load took ${metrics.statsLoadTime.toFixed(0)}ms (threshold: ${
        thresholds.statsLoadTime
      }ms)`
    )
  }

  // Check overall page ready time
  if (
    metrics.overallPageReady !== null &&
    metrics.overallPageReady > thresholds.overallPageReady
  ) {
    violations.push(
      `Overall page ready took ${metrics.overallPageReady.toFixed(0)}ms (threshold: ${
        thresholds.overallPageReady
      }ms)`
    )
  }

  // Check cache hit rate
  if (
    metrics.cacheMetrics.hitRate > 0 && // Only check if we have cache data
    metrics.cacheMetrics.hitRate < thresholds.cacheHitRate
  ) {
    violations.push(
      `Cache hit rate is ${metrics.cacheMetrics.hitRate.toFixed(1)}% (threshold: ${
        thresholds.cacheHitRate
      }%)`
    )
  }

  return violations
}

export function sendPerformanceAlert(
  violations: string[],
  metrics: ReturnType<typeof userInfoPerformance.getMetrics>,
  alertsSent: Set<string>
): void {
  const alertKey = violations.join('|')

  // Don't send duplicate alerts
  if (alertsSent.has(alertKey)) return

  alertsSent.add(alertKey)

  // Send to Google Analytics
  if (typeof window !== 'undefined' && 'gtag' in window) {
    const { gtag } = window as unknown as {
      gtag: (
        command: string,
        eventName: string,
        parameters: Record<string, unknown>
      ) => void
    }

    gtag('event', 'performance_violation', {
      event_category: 'progressive_loading',
      event_label: violations[0], // Send first violation as label
      value: violations.length,
      custom_parameter_1: JSON.stringify(violations),
      session_id: metrics.analytics.sessionId,
    })
  }

  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    logger.error('[Performance Alert]', violations)
  }
}
