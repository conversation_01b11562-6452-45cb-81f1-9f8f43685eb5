import { useAuthStore } from '@/stores/authStore'
import { logger } from '@/utils/logger'
import { SYNC_EVENTS, SyncEventManager } from './syncEvents'
import { SyncQueueManager } from './syncQueue'
import { SyncRequestProcessor } from './syncRequestProcessor'
import type { FailedRequest } from './syncQueue'

// Re-export for backward compatibility
export { SYNC_EVENTS }

/**
 * Manages syncing of failed requests and offline data
 */
export class SyncManager {
  private static isRetrying = false

  private static eventTarget = new EventTarget()

  /**
   * Subscribe to sync events
   */
  static on(eventType: string, handler: (event: CustomEvent) => void) {
    this.eventTarget.addEventListener(eventType, handler as EventListener)
  }

  /**
   * Unsubscribe from sync events
   */
  static off(eventType: string, handler: (event: CustomEvent) => void) {
    this.eventTarget.removeEventListener(eventType, handler as EventListener)
  }

  /**
   * Retry all failed requests
   */
  static async retryFailedRequests(): Promise<void> {
    // Check if user is authenticated
    const authState = useAuthStore.getState()
    if (!authState.isAuthenticated || this.isRetrying || !navigator.onLine)
      return

    this.isRetrying = true

    try {
      // Get failed requests
      const failedRequests: FailedRequest[] = JSON.parse(
        localStorage.getItem('drmuscle-failed-requests') || '[]'
      )

      if (failedRequests.length === 0) return

      // Retrying failed requests
      logger.log(
        '[SyncManager] Retrying failed requests:',
        failedRequests.length
      )

      // Process batch of requests
      const { successfulIds, failedRequests: remainingRequests } =
        await SyncRequestProcessor.processBatch(failedRequests)

      // Update storage
      localStorage.setItem(
        'drmuscle-failed-requests',
        JSON.stringify(remainingRequests)
      )

      logger.log('[SyncManager] Retry complete:', {
        total: failedRequests.length,
        successful: successfulIds.length,
        remaining: remainingRequests.length,
      })

      // Emit event if any requests were successful
      if (successfulIds.length > 0) {
        SyncEventManager.emit(SYNC_EVENTS.SYNC_COMPLETED, {
          syncedCount: successfulIds.length,
          remainingCount: remainingRequests.length,
        })
      }

      // Always emit queue updated event
      SyncEventManager.emit(SYNC_EVENTS.QUEUE_UPDATED, {
        pendingCount: SyncQueueManager.getPendingCount(),
      })

      // Retry complete
    } finally {
      this.isRetrying = false
    }
  }

  /**
   * Process offline queue
   */
  static async processOfflineQueue(): Promise<void> {
    // Check if user is authenticated
    const authState = useAuthStore.getState()
    if (!authState.isAuthenticated || !navigator.onLine) return

    try {
      const offlineQueue: FailedRequest[] = JSON.parse(
        localStorage.getItem('drmuscle-offline-queue') || '[]'
      )

      if (offlineQueue.length === 0) return

      // Processing offline requests
      logger.log('[SyncManager] Processing offline queue:', offlineQueue.length)

      // Process batch of requests
      const result = await SyncRequestProcessor.processBatch(offlineQueue)
      const { successfulIds } = result
      let { failedRequests: remainingRequests } = result

      // Move any new failures to failed requests queue
      const newFailures = remainingRequests.filter(
        (req) =>
          req.error &&
          !offlineQueue.find((orig) => orig.id === req.id && orig.error)
      )
      if (newFailures.length > 0) {
        const failedRequests = SyncQueueManager.getFailedRequests()
        failedRequests.push(...newFailures)
        SyncQueueManager.setFailedRequests(failedRequests)
        // Remove new failures from remaining offline queue
        remainingRequests = remainingRequests.filter(
          (req) => !newFailures.find((f) => f.id === req.id)
        )
      }

      localStorage.setItem(
        'drmuscle-offline-queue',
        JSON.stringify(remainingRequests)
      )

      // Emit event if any requests were successful
      if (successfulIds.length > 0) {
        SyncEventManager.emit(SYNC_EVENTS.SYNC_COMPLETED, {
          syncedCount: successfulIds.length,
          remainingCount: remainingRequests.length,
        })
      }

      // Always emit queue updated event
      SyncEventManager.emit(SYNC_EVENTS.QUEUE_UPDATED, {
        pendingCount: SyncQueueManager.getPendingCount(),
      })
    } catch (error) {
      // Error processing offline queue
      SyncEventManager.emit(SYNC_EVENTS.SYNC_FAILED, { error })
    }
  }

  /**
   * Initialize sync manager
   */
  static init(): void {
    // Listen for online event
    window.addEventListener('online', () => {
      // Network connection restored
      // Delay to ensure network is stable
      setTimeout(() => {
        this.processOfflineQueue()
        this.retryFailedRequests()
      }, 2000)
    })

    // Retry periodically when online
    setInterval(() => {
      if (navigator.onLine) {
        this.retryFailedRequests()
      }
    }, 60000) // Every minute

    // Process on page load if online and authenticated
    const authState = useAuthStore.getState()
    if (navigator.onLine && authState.isAuthenticated) {
      setTimeout(() => {
        this.processOfflineQueue()
        this.retryFailedRequests()
      }, 5000)
    }
  }

  /**
   * Get count of pending sync items
   */
  static getPendingCount(): number {
    return SyncQueueManager.getPendingCount()
  }

  /**
   * Add item to sync queue (called when save fails)
   */
  static addToQueue(request: Omit<FailedRequest, 'id'>): void {
    SyncQueueManager.addToQueue(request)

    // Emit queue updated event
    SyncEventManager.emit(SYNC_EVENTS.QUEUE_UPDATED, {
      pendingCount: SyncQueueManager.getPendingCount(),
    })
  }

  /**
   * Clear all sync data (use with caution)
   */
  static clearAll(): void {
    SyncQueueManager.clearAll()

    // Emit queue cleared event
    SyncEventManager.emit(SYNC_EVENTS.QUEUE_UPDATED, {
      pendingCount: 0,
    })
  }
}
