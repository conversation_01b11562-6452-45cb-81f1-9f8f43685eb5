'use client'

import { useState } from 'react'
import {
  getUserWorkoutProgramInfo,
  getUserWorkout,
} from '@/services/api/workout'
import type { GetUserWorkoutProgramTimeZoneInfoResponse } from '@/services/api/workout'
import type { WorkoutTemplateModel } from '@/types/api'

export default function TestApiPage() {
  const [loading, setLoading] = useState(false)
  const [programInfoResult, setProgramInfoResult] =
    useState<GetUserWorkoutProgramTimeZoneInfoResponse | null>(null)
  const [userWorkoutResult, setUserWorkoutResult] = useState<
    WorkoutTemplateModel[] | null
  >(null)
  const [error, setError] = useState<string | null>(null)

  const testApis = async () => {
    setLoading(true)
    setError(null)
    setProgramInfoResult(null)
    setUserWorkoutResult(null)

    try {
      // Test getUserWorkoutProgramInfo
      // eslint-disable-next-line no-console
      console.log('Testing getUserWorkoutProgramInfo...')
      const programInfo = await getUserWorkoutProgramInfo()
      // eslint-disable-next-line no-console
      console.log('Program info result:', programInfo)
      setProgramInfoResult(programInfo)

      // Test getUserWorkout
      // eslint-disable-next-line no-console
      console.log('Testing getUserWorkout...')
      const userWorkout = await getUserWorkout()
      // eslint-disable-next-line no-console
      console.log('User workout result:', userWorkout)
      setUserWorkoutResult(userWorkout)
    } catch (err) {
      console.error('API test error:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-[100dvh] bg-gray-50 p-4">
      <div className="mx-auto max-w-4xl">
        <h1 className="text-2xl font-bold mb-6">API Test Page</h1>

        <button
          onClick={testApis}
          disabled={loading}
          className="mb-6 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400"
        >
          {loading ? 'Testing...' : 'Test APIs'}
        </button>

        {error && (
          <div className="mb-6 p-4 bg-red-100 border border-red-400 rounded-lg">
            <h2 className="font-bold text-red-700 mb-2">Error</h2>
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {programInfoResult && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-3">
              getUserWorkoutProgramInfo Result:
            </h2>
            <pre className="p-4 bg-gray-100 rounded-lg overflow-auto text-xs">
              {JSON.stringify(programInfoResult, null, 2)}
            </pre>
          </div>
        )}

        {userWorkoutResult && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-3">
              getUserWorkout Result:
            </h2>
            <pre className="p-4 bg-gray-100 rounded-lg overflow-auto text-xs">
              {JSON.stringify(userWorkoutResult, null, 2)}
            </pre>
          </div>
        )}

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold mb-2">Instructions:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>Click "Test APIs" to fetch data from both workout endpoints</li>
            <li>Check the browser console for detailed logs</li>
            <li>The results will show the raw API responses</li>
            <li>This helps debug what data structure the API is returning</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
