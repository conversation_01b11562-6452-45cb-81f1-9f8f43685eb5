import { test, expect } from '@playwright/test'

test.describe('Offline Page Theme', () => {
  test.beforeEach(async ({ page, context }) => {
    // Set theme to subtle-depth
    await context.addCookies([
      {
        name: 'theme',
        value: 'subtle-depth',
        domain: 'localhost',
        path: '/',
      },
    ])

    // Navigate to offline page
    await page.goto('/offline')
  })

  test('should use theme colors instead of hardcoded colors', async ({
    page,
  }) => {
    // Check description text uses theme color
    const description = page.locator(
      'text=Please check your internet connection'
    )
    await expect(description).toHaveClass(/text-text-secondary/)

    // Check button uses theme colors
    const button = page.getByRole('button', { name: 'Try Again' })
    await expect(button).toHaveClass(/bg-brand-primary/)
    await expect(button).toHaveClass(/text-text-inverse/)
    await expect(button).toHaveClass(/hover:bg-brand-primary\/90/)
    await expect(button).toHaveClass(/rounded-theme/)
  })

  test('should adapt colors based on theme', async ({ page, context }) => {
    // Check initial theme (subtle-depth) - button should have gold color
    const button = page.getByRole('button', { name: 'Try Again' })
    const subtleDepthBg = await button.evaluate(
      (el) => window.getComputedStyle(el).backgroundColor
    )
    expect(subtleDepthBg).toBe('rgb(255, 193, 7)') // Gold color

    // Switch to flat-bold theme
    await context.clearCookies()
    await context.addCookies([
      {
        name: 'theme',
        value: 'flat-bold',
        domain: 'localhost',
        path: '/',
      },
    ])
    await page.reload()

    // Check button now has green color
    const flatBoldBg = await button.evaluate(
      (el) => window.getComputedStyle(el).backgroundColor
    )
    expect(flatBoldBg).toBe('rgb(34, 197, 94)') // Green color
  })
})
