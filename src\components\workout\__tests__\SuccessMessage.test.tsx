import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { SuccessMessage } from '../SuccessMessage'
import { useSuccessAnimation } from '@/hooks/useSuccessAnimation'

// Mock the animation hook
vi.mock('@/hooks/useSuccessAnimation')

describe('SuccessMessage', () => {
  beforeEach(() => {
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'idle',
      isAnimating: false,
      prefersReducedMotion: false,
      start: vi.fn(),
      reset: vi.fn(),
    })
  })

  it('should render title', () => {
    render(<SuccessMessage title="Welcome back!" />)

    expect(screen.getByText('Welcome back!')).toBeInTheDocument()
  })

  it('should render message', () => {
    render(
      <SuccessMessage title="Success" message="Your workout is loading..." />
    )

    expect(screen.getByText('Your workout is loading...')).toBeInTheDocument()
  })

  it('should render both title and message', () => {
    render(
      <SuccessMessage
        title="Welcome to Dr. Muscle X"
        message="The world's fastest AI personal trainer"
      />
    )

    expect(screen.getByText('Welcome to Dr. Muscle X')).toBeInTheDocument()
    expect(
      screen.getByText("The world's fastest AI personal trainer")
    ).toBeInTheDocument()
  })

  it('should apply custom className', () => {
    render(<SuccessMessage title="Test" className="custom-class" />)

    const container = screen.getByTestId('success-message')
    expect(container).toHaveClass('custom-class')
  })

  it('should apply animation classes based on state', () => {
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'active',
      isAnimating: true,
      prefersReducedMotion: false,
      start: vi.fn(),
      reset: vi.fn(),
    })

    render(<SuccessMessage title="Animated" />)

    const title = screen.getByTestId('success-title')
    expect(title).toHaveClass('animate-slide-up')
  })

  it('should apply staggered animation to message', () => {
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'active',
      isAnimating: true,
      prefersReducedMotion: false,
      start: vi.fn(),
      reset: vi.fn(),
    })

    render(<SuccessMessage title="Title" message="Message" />)

    const message = screen.getByTestId('success-text')
    expect(message).toHaveClass('animate-slide-up')
    expect(message).toHaveStyle({ animationDelay: '100ms' })
  })

  it('should start animation on mount when autoPlay is true', () => {
    const mockStart = vi.fn()
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'idle',
      isAnimating: false,
      prefersReducedMotion: false,
      start: mockStart,
      reset: vi.fn(),
    })

    render(<SuccessMessage title="Auto" autoPlay />)

    expect(mockStart).toHaveBeenCalledTimes(1)
  })

  it('should not start animation when autoPlay is false', () => {
    const mockStart = vi.fn()
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'idle',
      isAnimating: false,
      prefersReducedMotion: false,
      start: mockStart,
      reset: vi.fn(),
    })

    render(<SuccessMessage title="Manual" autoPlay={false} />)

    expect(mockStart).not.toHaveBeenCalled()
  })

  it('should handle animation completion callback', () => {
    const onComplete = vi.fn()

    vi.mocked(useSuccessAnimation).mockImplementation((options) => {
      // Trigger callback
      if (options?.onComplete) {
        options.onComplete()
      }

      return {
        state: 'complete',
        isAnimating: false,
        prefersReducedMotion: false,
        start: vi.fn(),
        reset: vi.fn(),
      }
    })

    render(<SuccessMessage title="Done" onAnimationComplete={onComplete} />)

    expect(onComplete).toHaveBeenCalledTimes(1)
  })

  it('should skip animations when reduced motion is preferred', () => {
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'active',
      isAnimating: true,
      prefersReducedMotion: true,
      start: vi.fn(),
      reset: vi.fn(),
    })

    render(<SuccessMessage title="No animation" />)

    const title = screen.getByTestId('success-title')
    expect(title).not.toHaveClass('animate-slide-up')
  })

  it('should have proper text size hierarchy', () => {
    render(<SuccessMessage title="Big Title" message="Smaller message" />)

    const title = screen.getByTestId('success-title')
    const message = screen.getByTestId('success-text')

    expect(title).toHaveClass('text-2xl', 'font-bold')
    expect(message).toHaveClass('text-lg')
  })

  it('should center align text', () => {
    render(<SuccessMessage title="Centered" />)

    const container = screen.getByTestId('success-message')
    expect(container).toHaveClass('text-center')
  })

  it('should have proper spacing', () => {
    render(<SuccessMessage title="Title" message="Message" />)

    const container = screen.getByTestId('success-message')
    expect(container).toHaveClass('space-y-4')
  })

  it('should apply visibility based on animation state', () => {
    // Test idle state
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'idle',
      isAnimating: false,
      prefersReducedMotion: false,
      start: vi.fn(),
      reset: vi.fn(),
    })

    const { rerender } = render(<SuccessMessage title="Hidden" />)
    let title = screen.getByTestId('success-title')
    expect(title).toHaveClass('opacity-0')

    // Test active state
    vi.mocked(useSuccessAnimation).mockReturnValue({
      state: 'active',
      isAnimating: true,
      prefersReducedMotion: false,
      start: vi.fn(),
      reset: vi.fn(),
    })

    rerender(<SuccessMessage title="Visible" />)
    title = screen.getByTestId('success-title')
    expect(title).not.toHaveClass('opacity-0')
  })

  it('should apply dark mode styles', () => {
    render(<SuccessMessage title="Dark mode" />)

    const title = screen.getByTestId('success-title')
    expect(title).toHaveClass('text-gray-900', 'dark:text-white')
  })
})
