/**
 * Haptic feedback utilities for mobile interactions
 */

// Types of haptic feedback
export type HapticStyle = 'light' | 'medium' | 'heavy' | 'soft' | 'rigid'

/**
 * Check if the browser supports haptic feedback
 */
export function isHapticSupported(): boolean {
  return 'vibrate' in navigator
}

/**
 * Trigger haptic feedback
 * @param style - The style of haptic feedback
 * @param fallbackDuration - Fallback duration for browsers that only support basic vibration
 */
export function triggerHaptic(
  style: HapticStyle = 'light',
  fallbackDuration = 10
): void {
  if (!isHapticSupported()) {
    return
  }

  try {
    // Map styles to vibration patterns
    const patterns: Record<HapticStyle, number | number[]> = {
      light: 10,
      medium: 20,
      heavy: 30,
      soft: [10, 10, 10],
      rigid: [20, 10, 20],
    }

    const pattern = patterns[style] || fallbackDuration
    navigator.vibrate(pattern)
  } catch (error) {
    // Silently fail if vibration is not allowed
    // Haptic feedback failed - fail silently
  }
}

/**
 * Hook for adding haptic feedback to click handlers
 */
export function withHaptic<T extends (...args: unknown[]) => unknown>(
  handler: T,
  style: HapticStyle = 'light'
): T {
  return ((...args: Parameters<T>) => {
    triggerHaptic(style)
    return handler(...args)
  }) as T
}

/**
 * React hook for haptic feedback
 */
export function useHaptic(style: HapticStyle = 'light') {
  return {
    trigger: () => triggerHaptic(style),
    withHandler: <T extends (...args: unknown[]) => unknown>(handler: T) =>
      withHaptic(handler, style),
    isSupported: isHapticSupported(),
  }
}
