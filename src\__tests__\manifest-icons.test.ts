import { describe, it, expect } from 'vitest'
import manifestJson from '../../public/manifest.json'

describe('PWA Manifest Icons', () => {
  it('should have all required icon sizes', () => {
    const requiredSizes = [
      '72x72',
      '96x96',
      '128x128',
      '144x144',
      '152x152',
      '192x192',
      '384x384',
      '512x512',
    ]
    const iconSizes = manifestJson.icons.map((icon) => icon.sizes)

    requiredSizes.forEach((size) => {
      expect(iconSizes).toContain(size)
    })
  })

  it('should use PNG format for all icons', () => {
    manifestJson.icons.forEach((icon) => {
      expect(icon.src).toMatch(/\.png$/)
      expect(icon.type).toBe('image/png')
    })
  })

  it('should have both any and maskable purpose icons', () => {
    const purposes = manifestJson.icons.map((icon) => icon.purpose)

    expect(purposes).toContain('any')
    expect(purposes).toContain('maskable')
  })

  it('should have correct paths for regular icons', () => {
    const regularIcons = manifestJson.icons.filter(
      (icon) => icon.purpose === 'any'
    )

    regularIcons.forEach((icon) => {
      const size = icon.sizes
      expect(icon.src).toBe(`/icons/icon-${size}.png`)
    })
  })

  it('should have correct paths for maskable icons', () => {
    const maskableIcons = manifestJson.icons.filter(
      (icon) => icon.purpose === 'maskable'
    )

    maskableIcons.forEach((icon) => {
      const size = icon.sizes
      expect(icon.src).toBe(`/icons/icon-maskable-${size}.png`)
    })
  })

  it('should have at least one icon 192x192 or larger', () => {
    const largeIcons = manifestJson.icons.filter((icon) => {
      const size = parseInt(icon.sizes.split('x')[0] || '0')
      return size >= 192
    })

    expect(largeIcons.length).toBeGreaterThan(0)
  })

  it('should have valid manifest structure', () => {
    expect(manifestJson).toHaveProperty('name')
    expect(manifestJson).toHaveProperty('short_name')
    expect(manifestJson).toHaveProperty('icons')
    expect(manifestJson).toHaveProperty('start_url')
    expect(manifestJson).toHaveProperty('display')
    expect(manifestJson).toHaveProperty('theme_color')
    expect(manifestJson).toHaveProperty('background_color')
  })

  it('should have maskable icons with safe zone padding', () => {
    const maskableIcons = manifestJson.icons.filter(
      (icon) => icon.purpose === 'maskable'
    )

    expect(maskableIcons.length).toBeGreaterThan(0)
    maskableIcons.forEach((icon) => {
      expect(icon.src).toContain('maskable')
    })
  })
})
