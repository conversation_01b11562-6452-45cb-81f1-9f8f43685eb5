import { apiClient } from './client'
import { logger } from '@/utils/logger'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import type {
  WorkoutTemplateModel,
  RecommendationModel,
  TimeZoneInfoModel,
} from '@/types'
import type {
  GetRecommendationForExerciseRequest,
  GetUserWorkoutProgramTimeZoneInfoResponse,
} from './workout/types'

export type {
  GetRecommendationForExerciseRequest,
  GetUserWorkoutProgramTimeZoneInfoResponse,
} from './workout/types'

/**
 * Service methods for interacting with the Dr. Muscle Workout API
 * Follows the progressive loading pattern: program -> workout -> exercises -> recommendations
 */
export const workoutService = {
  /**
   * Get user's workout program info including next workout template
   * Called when user lands on Program Overview page
   * @returns User's program info with next workout template (exercises not loaded)
   */
  async getUserWorkoutProgramInfo(): Promise<GetUserWorkoutProgramTimeZoneInfoResponse | null> {
    try {
      // Detect user's timezone
      const timeZoneInfo: TimeZoneInfoModel = {
        TimeZoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
        Offset: new Date().getTimezoneOffset() / -60,
        IsDaylightSaving: false, // Simplified - could be enhanced
      }

      if (process.env.NODE_ENV === 'development') {
        logger.log(
          '[Workout Service] Getting user workout program info with timezone:',
          timeZoneInfo
        )
      }

      const response = await apiClient.post(
        '/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
        timeZoneInfo
      )

      // Handle wrapped response format
      if (response.data?.StatusCode === 200 && response.data?.Result) {
        if (process.env.NODE_ENV === 'development') {
          logger.log(
            '[Workout Service] Program info loaded:',
            response.data.Result
          )
        }
        return response.data.Result as GetUserWorkoutProgramTimeZoneInfoResponse
      }

      // Direct response format
      const data = response.data as GetUserWorkoutProgramTimeZoneInfoResponse

      if (process.env.NODE_ENV === 'development') {
        logger.log('[Workout Service] Program info loaded:', data)
      }

      return data
    } catch (error) {
      logger.error(
        '[Workout Service] Failed to get user workout program info:',
        error
      )
      throw error
    }
  },

  /**
   * Get detailed workout information including exercises
   * Called when user clicks "Start Workout" or navigates to workout page
   * @param workoutId The workout ID to fetch
   * @returns Complete workout template with exercises (but no recommendations yet)
   */
  async getWorkoutDetails(
    workoutId: number
  ): Promise<WorkoutTemplateModel | null> {
    try {
      if (process.env.NODE_ENV === 'development') {
        logger.log(
          '[Workout Service] Getting workout details for ID:',
          workoutId
        )
      }

      const response = await apiClient.get(
        `/api/Workout/GetUserCustomizedCurrentWorkout/${workoutId}`
      )

      // Handle wrapped response format
      if (response.data?.StatusCode === 200 && response.data?.Result) {
        const result = response.data.Result as WorkoutTemplateModel
        if (process.env.NODE_ENV === 'development') {
          logger.log('[Workout Service] Workout details loaded:', result)
        }
        return result
      }

      // Direct response format
      const data = response.data as WorkoutTemplateModel

      if (process.env.NODE_ENV === 'development') {
        logger.log('[Workout Service] Workout details loaded:', data)
        logger.log(
          `[Workout Service] Loaded ${data.Exercises?.length || 0} exercises`
        )
      }

      return data
    } catch (error) {
      logger.error(
        `[Workout Service] Failed to get workout details for ID ${workoutId}:`,
        error
      )
      throw error
    }
  },

  /**
   * Get recommendation for a specific exercise
   * Called for each exercise when user navigates to exercise screen
   * @param request Exercise recommendation request parameters
   * @returns Recommendation with sets, reps, weight for the exercise
   */
  async getExerciseRecommendation(
    request: GetRecommendationForExerciseRequest
  ): Promise<RecommendationModel | null> {
    try {
      const username = request.Username || getCurrentUserEmail()
      if (!username) {
        logger.error('[Workout Service] No username for recommendation request')
        return null
      }

      const requestData = {
        ...request,
        Username: username,
      }

      if (process.env.NODE_ENV === 'development') {
        logger.log(
          `[Workout Service] Getting recommendation for exercise ${request.ExerciseId}:`,
          requestData
        )
      }

      const response = await apiClient.post(
        '/api/Recommendations/GetRecommendationForExercise',
        requestData
      )

      // Handle wrapped response format
      if (response.data?.StatusCode === 200 && response.data?.Result) {
        const result = response.data.Result as RecommendationModel
        if (process.env.NODE_ENV === 'development' && result) {
          logger.log('[Workout Service] Recommendation loaded:', {
            exerciseId: request.ExerciseId,
            weight: result.Weight,
            reps: result.Reps,
            sets: result.Series,
          })
        }
        return result
      }

      // Handle direct response or Data wrapper
      const recommendation = response.data?.Data || response.data

      if (process.env.NODE_ENV === 'development' && recommendation) {
        logger.log('[Workout Service] Recommendation loaded:', {
          exerciseId: request.ExerciseId,
          weight: recommendation.Weight,
          reps: recommendation.Reps,
          sets: recommendation.Series,
        })
      }

      return recommendation as RecommendationModel
    } catch (error) {
      logger.error(
        `[Workout Service] Failed to get recommendation for exercise ${request.ExerciseId}:`,
        error
      )

      // Return null for graceful degradation
      return null
    }
  },

  /**
   * Helper method to build cache key for recommendations
   * Format: userId-exerciseId-workoutId
   * @param userId User identifier
   * @param exerciseId Exercise ID
   * @param workoutId Workout ID
   * @returns Cache key string
   */
  getCacheKey(userId: string, exerciseId: number, workoutId: number): string {
    return `${userId}-${exerciseId}-${workoutId}`
  },
}
