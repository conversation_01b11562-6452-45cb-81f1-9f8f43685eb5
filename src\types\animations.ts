/**
 * Animation state types for success screen animations
 */
export type AnimationState =
  | 'idle'
  | 'entering'
  | 'active'
  | 'exiting'
  | 'complete'

/**
 * Animation timing configuration
 */
export interface AnimationTiming {
  /** Duration in milliseconds */
  duration: number
  /** Delay before animation starts in milliseconds */
  delay?: number
  /** CSS easing function or cubic-bezier */
  easing?: string
}

/**
 * Success animation options
 */
export interface SuccessAnimationOptions {
  /** Whether to skip animations for reduced motion */
  respectReducedMotion?: boolean
  /** Callback when animation completes */
  onComplete?: () => void
  /** Custom timing overrides */
  timing?: Partial<AnimationTiming>
}

/**
 * Animation hook return type
 */
export interface UseSuccessAnimationReturn {
  /** Current animation state */
  state: AnimationState
  /** Whether animation is currently running */
  isAnimating: boolean
  /** Whether user prefers reduced motion */
  prefersReducedMotion: boolean
  /** Start the animation sequence */
  start: () => void
  /** Reset animation to initial state */
  reset: () => void
}

/**
 * Common animation durations in milliseconds
 */
export const ANIMATION_DURATION = {
  /** Quick fade/scale animations */
  QUICK: 200,
  /** Standard animations */
  NORMAL: 300,
  /** Slower emphasis animations */
  SLOW: 500,
  /** Checkmark draw animation */
  DRAW: 800,
  /** Bounce effect duration */
  BOUNCE: 600,
  /** Complete sequence duration */
  TOTAL: 2000,
} as const

/**
 * CSS easing functions
 */
export const ANIMATION_EASING = {
  /** Default ease for most animations */
  DEFAULT: 'cubic-bezier(0.4, 0, 0.2, 1)',
  /** Bounce effect for emphasis */
  BOUNCE: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  /** Smooth deceleration */
  EASE_OUT: 'cubic-bezier(0, 0, 0.2, 1)',
  /** Smooth acceleration */
  EASE_IN: 'cubic-bezier(0.4, 0, 1, 1)',
} as const
