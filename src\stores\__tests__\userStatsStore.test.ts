import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { act } from '@testing-library/react'
import { useUserStatsStore, selectUserStats } from '../userStatsStore'
import { UserStats } from '@/types/userStats'
import * as userStatsApi from '@/api/userStats'

// Mock the API
vi.mock('@/api/userStats', () => ({
  fetchUserStats: vi.fn(),
}))

// Mock logger
vi.mock('@/utils/logger', () => ({
  logger: {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}))

describe('userStatsStore', () => {
  const mockStats: UserStats = {
    weekStreak: 5,
    workoutsCompleted: 25,
    lbsLifted: 10000,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Reset store to initial state
    act(() => {
      useUserStatsStore.setState({
        stats: null,
        isLoading: false,
        error: null,
        lastFetchTime: null,
      })
    })
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('initial state', () => {
    it('should have correct initial values', () => {
      const state = useUserStatsStore.getState()
      expect(state.stats).toBeNull()
      expect(state.isLoading).toBe(false)
      expect(state.error).toBeNull()
      expect(state.lastFetchTime).toBeNull()
    })
  })

  describe('fetchStats', () => {
    it('should fetch stats successfully', async () => {
      vi.mocked(userStatsApi.fetchUserStats).mockResolvedValueOnce(mockStats)

      await act(async () => {
        await useUserStatsStore.getState().fetchStats()
      })

      const state = useUserStatsStore.getState()
      expect(state.stats).toEqual(mockStats)
      expect(state.isLoading).toBe(false)
      expect(state.error).toBeNull()
      expect(state.lastFetchTime).toBeTruthy()
    })

    it('should handle fetch errors', async () => {
      const mockError = new Error('Network error')
      vi.mocked(userStatsApi.fetchUserStats).mockRejectedValueOnce(mockError)

      await act(async () => {
        await useUserStatsStore.getState().fetchStats()
      })

      const state = useUserStatsStore.getState()
      expect(state.stats).toBeNull()
      expect(state.isLoading).toBe(false)
      expect(state.error).toBe('Network error')
    })

    it('should skip fetch if already loading', async () => {
      // Set loading state
      act(() => {
        useUserStatsStore.setState({ isLoading: true })
      })

      await act(async () => {
        await useUserStatsStore.getState().fetchStats()
      })

      // API should not be called
      expect(userStatsApi.fetchUserStats).not.toHaveBeenCalled()
    })

    it('should skip fetch if data is fresh', async () => {
      // Set fresh data
      act(() => {
        useUserStatsStore.setState({
          stats: mockStats,
          lastFetchTime: Date.now(),
        })
      })

      await act(async () => {
        await useUserStatsStore.getState().fetchStats()
      })

      // API should not be called
      expect(userStatsApi.fetchUserStats).not.toHaveBeenCalled()
    })

    it('should fetch if data is stale', async () => {
      vi.useFakeTimers()
      const now = Date.now()
      vi.setSystemTime(now)

      // Set stale data (2 hours old)
      act(() => {
        useUserStatsStore.setState({
          stats: mockStats,
          lastFetchTime: now - 2 * 60 * 60 * 1000,
        })
      })

      vi.mocked(userStatsApi.fetchUserStats).mockResolvedValueOnce(mockStats)

      await act(async () => {
        await useUserStatsStore.getState().fetchStats()
      })

      // API should be called
      expect(userStatsApi.fetchUserStats).toHaveBeenCalled()
    })
  })

  describe('setStats', () => {
    it('should set stats directly', () => {
      act(() => {
        useUserStatsStore.getState().setStats(mockStats)
      })

      const state = useUserStatsStore.getState()
      expect(state.stats).toEqual(mockStats)
      expect(state.lastFetchTime).toBeTruthy()
      expect(state.error).toBeNull()
    })
  })

  describe('clearStats', () => {
    it('should clear all stats data', () => {
      // First set some data
      act(() => {
        useUserStatsStore.setState({
          stats: mockStats,
          lastFetchTime: Date.now(),
          error: 'Some error',
        })
      })

      // Then clear
      act(() => {
        useUserStatsStore.getState().clearStats()
      })

      const state = useUserStatsStore.getState()
      expect(state.stats).toBeNull()
      expect(state.lastFetchTime).toBeNull()
      expect(state.error).toBeNull()
    })
  })

  describe('clearError', () => {
    it('should clear error only', () => {
      act(() => {
        useUserStatsStore.setState({
          stats: mockStats,
          error: 'Some error',
        })
      })

      act(() => {
        useUserStatsStore.getState().clearError()
      })

      const state = useUserStatsStore.getState()
      expect(state.error).toBeNull()
      expect(state.stats).toEqual(mockStats)
    })
  })

  describe('isStale', () => {
    it('should return true if no lastFetchTime', () => {
      expect(useUserStatsStore.getState().isStale()).toBe(true)
    })

    it('should return false if data is fresh', () => {
      act(() => {
        useUserStatsStore.setState({ lastFetchTime: Date.now() })
      })

      expect(useUserStatsStore.getState().isStale()).toBe(false)
    })

    it('should return true if data is older than 1 hour', () => {
      vi.useFakeTimers()
      const now = Date.now()
      vi.setSystemTime(now)

      act(() => {
        useUserStatsStore.setState({
          lastFetchTime: now - 61 * 60 * 1000, // 61 minutes ago
        })
      })

      expect(useUserStatsStore.getState().isStale()).toBe(true)
    })
  })

  describe('hasData', () => {
    it('should return false if stats is null', () => {
      expect(useUserStatsStore.getState().hasData()).toBe(false)
    })

    it('should return true if stats exists', () => {
      act(() => {
        useUserStatsStore.setState({ stats: mockStats })
      })

      expect(useUserStatsStore.getState().hasData()).toBe(true)
    })
  })

  describe('selectors', () => {
    it('selectUserStats should return default stats when null', () => {
      const state = useUserStatsStore.getState()
      const stats = selectUserStats(state)

      expect(stats).toEqual({
        weekStreak: 0,
        workoutsCompleted: 0,
        lbsLifted: 0,
      })
    })

    it('selectUserStats should return actual stats when available', () => {
      act(() => {
        useUserStatsStore.setState({ stats: mockStats })
      })

      const state = useUserStatsStore.getState()
      const stats = selectUserStats(state)

      expect(stats).toEqual(mockStats)
    })
  })

  describe('persistence', () => {
    it('should persist only stats and lastFetchTime', () => {
      // Initialize localStorage
      if (!global.localStorage) {
        global.localStorage = {
          getItem: vi.fn(),
          setItem: vi.fn(),
          removeItem: vi.fn(),
          clear: vi.fn(),
          length: 0,
          key: vi.fn(),
        }
      }

      act(() => {
        useUserStatsStore.setState({
          stats: mockStats,
          isLoading: true,
          error: 'Some error',
          lastFetchTime: 123456789,
        })
      })

      // Since we're using a mock localStorage in tests, we need to check the store state directly
      const state = useUserStatsStore.getState()
      expect(state.stats).toEqual(mockStats)
      expect(state.lastFetchTime).toBe(123456789)

      // In a real environment, persistence would save only stats and lastFetchTime
      // but we can't test localStorage directly in this test environment
    })
  })
})
