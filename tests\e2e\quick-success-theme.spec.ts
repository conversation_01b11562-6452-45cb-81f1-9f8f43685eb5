import { test, expect } from '@playwright/test'

test.describe('QuickSuccessScreen Theme Integration', () => {
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test('should use theme brand color for success checkmark', async ({
    page,
    context,
  }) => {
    // Set theme in localStorage
    await context.addInitScript(() => {
      localStorage.setItem('dr-muscle-x-theme', 'subtle-depth')
    })

    // Navigate to login page
    await page.goto('/login')

    // Fill in login form
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)

    // Click login button
    await page.getByRole('button', { name: /log in/i }).click()

    // Wait for quick success screen
    await page.waitForSelector('[data-testid="quick-success-screen"]')

    // Check that the SVG elements use CSS variable for color
    const circleStroke = await page.$eval(
      '[data-testid="success-circle"]',
      (el) => el.getAttribute('stroke')
    )
    const checkmarkStroke = await page.$eval(
      '[data-testid="success-checkmark"]',
      (el) => el.getAttribute('stroke')
    )

    // Should use CSS variable instead of hardcoded color
    expect(circleStroke).toBe('var(--color-brand-primary)')
    expect(checkmarkStroke).toBe('var(--color-brand-primary)')

    // Verify the actual computed color is gold (subtle-depth theme)
    const computedCircleColor = await page.$eval(
      '[data-testid="success-circle"]',
      (el) => window.getComputedStyle(el).stroke
    )
    const computedCheckmarkColor = await page.$eval(
      '[data-testid="success-checkmark"]',
      (el) => window.getComputedStyle(el).stroke
    )

    // Subtle-depth theme uses gold color
    expect(computedCircleColor).toBe('rgb(212, 175, 55)') // #d4af37 in RGB
    expect(computedCheckmarkColor).toBe('rgb(212, 175, 55)')
  })

  test('should adapt checkmark color to different themes', async ({
    page,
    context,
  }) => {
    // Set theme in localStorage
    await context.addInitScript(() => {
      localStorage.setItem('dr-muscle-x-theme', 'flat-bold')
    })

    // Navigate to login page
    await page.goto('/login')

    // Fill in login form
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)

    // Click login button
    await page.getByRole('button', { name: /log in/i }).click()

    // Wait for quick success screen
    await page.waitForSelector('[data-testid="quick-success-screen"]')

    // Verify the computed color is green (flat-bold theme)
    const computedCircleColor = await page.$eval(
      '[data-testid="success-circle"]',
      (el) => window.getComputedStyle(el).stroke
    )
    const computedCheckmarkColor = await page.$eval(
      '[data-testid="success-checkmark"]',
      (el) => window.getComputedStyle(el).stroke
    )

    // Flat-bold theme uses green color
    expect(computedCircleColor).toBe('rgb(0, 255, 136)') // #00ff88 in RGB
    expect(computedCheckmarkColor).toBe('rgb(0, 255, 136)')
  })
})
