import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  measureProgramPageLoad,
  trackComponentPerformance,
  reportProgramMetrics,
  ProgramPerformanceMarks,
  clearPerformanceMarks,
  getPerformanceMetrics,
} from '../programPerformance'

// Mock performance API
const mockPerformance = {
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByType: vi.fn(() => []),
  getEntriesByName: vi.fn(() => []),
  clearMarks: vi.fn(),
  clearMeasures: vi.fn(),
  now: vi.fn(() => 1000),
}

// Replace global performance
const originalPerformance = global.performance
beforeEach(() => {
  global.performance = mockPerformance as any
  vi.clearAllMocks()
})

afterEach(() => {
  global.performance = originalPerformance
})

describe('Program Performance Monitoring', () => {
  describe('measureProgramPageLoad', () => {
    it('should mark program page start and end', () => {
      measureProgramPageLoad.start()
      expect(mockPerformance.mark).toHaveBeenCalledWith(
        ProgramPerformanceMarks.PROGRAM_PAGE_START
      )

      measureProgramPageLoad.end()
      expect(mockPerformance.mark).toHaveBeenCalledWith(
        ProgramPerformanceMarks.PROGRAM_PAGE_END
      )
    })

    it('should measure time between start and end', () => {
      measureProgramPageLoad.start()
      measureProgramPageLoad.end()

      expect(mockPerformance.measure).toHaveBeenCalledWith(
        'program-page-load',
        ProgramPerformanceMarks.PROGRAM_PAGE_START,
        ProgramPerformanceMarks.PROGRAM_PAGE_END
      )
    })

    it('should return measured duration', () => {
      mockPerformance.getEntriesByName.mockReturnValueOnce([
        { duration: 1234.56 },
      ])

      measureProgramPageLoad.start()
      const duration = measureProgramPageLoad.end()

      expect(duration).toBe(1234.56)
    })

    it('should handle missing start mark gracefully', () => {
      const duration = measureProgramPageLoad.end()
      expect(duration).toBe(0)
    })
  })

  describe('trackComponentPerformance', () => {
    it('should track component mount time', () => {
      const tracker = trackComponentPerformance('ProgramHeader')

      tracker.markMount()
      expect(mockPerformance.mark).toHaveBeenCalledWith(
        'program-component-ProgramHeader-mount'
      )
    })

    it('should track component render time', () => {
      const tracker = trackComponentPerformance('AnimatedCounter')

      tracker.markRenderStart()
      expect(mockPerformance.mark).toHaveBeenCalledWith(
        'program-component-AnimatedCounter-render-start'
      )

      tracker.markRenderEnd()
      expect(mockPerformance.mark).toHaveBeenCalledWith(
        'program-component-AnimatedCounter-render-end'
      )
    })

    it('should measure render duration', () => {
      mockPerformance.getEntriesByName.mockReturnValueOnce([{ duration: 25.5 }])

      const tracker = trackComponentPerformance('ProgramStats')
      tracker.markRenderStart()
      const duration = tracker.markRenderEnd()

      expect(duration).toBe(25.5)
      expect(mockPerformance.measure).toHaveBeenCalledWith(
        'program-component-ProgramStats-render',
        'program-component-ProgramStats-render-start',
        'program-component-ProgramStats-render-end'
      )
    })

    it('should track data fetch duration', () => {
      mockPerformance.now.mockReturnValueOnce(1000).mockReturnValueOnce(1500)

      const tracker = trackComponentPerformance('ProgramData')
      tracker.markFetchStart()
      const duration = tracker.markFetchEnd()

      expect(duration).toBe(500)
    })
  })

  describe('reportProgramMetrics', () => {
    it('should collect all performance metrics', () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'development'

      const mockEntries = [
        { name: 'program-page-load', duration: 1500 },
        { name: 'program-component-Header-render', duration: 50 },
        { name: 'program-data-fetch', duration: 300 },
      ]
      mockPerformance.getEntriesByType.mockReturnValueOnce(mockEntries)

      const consoleLogSpy = vi.spyOn(console, 'log').mockImplementation()

      reportProgramMetrics()

      expect(consoleLogSpy).toHaveBeenCalledWith(
        'Program Page Performance Metrics:',
        expect.objectContaining({
          pageLoad: 1500,
          componentRenders: expect.any(Object),
          dataFetch: 300,
        })
      )

      consoleLogSpy.mockRestore()
      process.env.NODE_ENV = originalEnv
    })

    it('should only report in development mode', () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'

      const consoleLogSpy = vi.spyOn(console, 'log').mockImplementation()

      reportProgramMetrics()

      expect(consoleLogSpy).not.toHaveBeenCalled()

      consoleLogSpy.mockRestore()
      process.env.NODE_ENV = originalEnv
    })

    it('should send metrics to analytics in production', () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'

      // Mock analytics
      global.gtag = vi.fn()

      mockPerformance.getEntriesByType.mockReturnValueOnce([
        { name: 'program-page-load', duration: 1200 },
      ])

      reportProgramMetrics()

      expect(global.gtag).toHaveBeenCalledWith('event', 'timing_complete', {
        name: 'program_page_load',
        value: 1200,
        event_category: 'performance',
      })

      delete global.gtag
      process.env.NODE_ENV = originalEnv
    })
  })

  describe('getPerformanceMetrics', () => {
    it('should return structured metrics object', () => {
      mockPerformance.getEntriesByType.mockReturnValueOnce([
        { name: 'program-page-load', duration: 1000 },
        { name: 'program-component-A-render', duration: 10 },
        { name: 'program-component-B-render', duration: 20 },
        { name: 'program-data-fetch', duration: 200 },
      ])

      const metrics = getPerformanceMetrics()

      expect(metrics).toEqual({
        pageLoad: 1000,
        componentRenders: {
          A: 10,
          B: 20,
        },
        dataFetch: 200,
        totalRenderTime: 30,
      })
    })

    it('should handle missing metrics gracefully', () => {
      mockPerformance.getEntriesByType.mockReturnValueOnce([])

      const metrics = getPerformanceMetrics()

      expect(metrics).toEqual({
        pageLoad: 0,
        componentRenders: {},
        dataFetch: 0,
        totalRenderTime: 0,
      })
    })
  })

  describe('clearPerformanceMarks', () => {
    it('should clear all program-related marks and measures', () => {
      clearPerformanceMarks()

      expect(mockPerformance.clearMarks).toHaveBeenCalled()
      expect(mockPerformance.clearMeasures).toHaveBeenCalled()
    })
  })

  describe('Performance targets', () => {
    it('should warn if page load exceeds 1 second', () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'development'

      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation()

      mockPerformance.getEntriesByType.mockReturnValueOnce([
        { name: 'program-page-load', duration: 1500 },
      ])

      reportProgramMetrics()

      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'Performance warning: Page load time (1500ms) exceeds target (1000ms)'
      )

      consoleWarnSpy.mockRestore()
      process.env.NODE_ENV = originalEnv
    })

    it('should warn if component render exceeds 50ms', () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'development'

      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation()

      mockPerformance.getEntriesByType.mockReturnValueOnce([
        { name: 'program-component-Heavy-render', duration: 75 },
      ])

      reportProgramMetrics()

      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'Performance warning: Component Heavy render time (75ms) exceeds target (50ms)'
      )

      consoleWarnSpy.mockRestore()
      process.env.NODE_ENV = originalEnv
    })
  })

  describe('Memory monitoring', () => {
    it('should track memory usage if available', () => {
      const mockMemory = {
        usedJSHeapSize: 10485760, // 10MB
        totalJSHeapSize: 20971520, // 20MB
        jsHeapSizeLimit: 2147483648, // 2GB
      }

      global.performance.memory = mockMemory as any

      const metrics = getPerformanceMetrics()

      expect(metrics.memory).toEqual({
        used: 10,
        total: 20,
        limit: 2048,
        percentage: 50,
      })
    })
  })
})
