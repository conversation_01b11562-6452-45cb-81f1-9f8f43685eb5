/**
 * Custom event types for sync status updates
 */
export const SYNC_EVENTS = {
  QUEUE_UPDATED: 'sync:queue-updated',
  SYNC_STARTED: 'sync:started',
  SYNC_COMPLETED: 'sync:completed',
  SYNC_FAILED: 'sync:failed',
} as const

/**
 * Event management for sync operations
 */
export class SyncEventManager {
  private static eventTarget = new EventTarget()

  /**
   * Emit a sync event
   */
  static emit(eventType: string, detail?: unknown) {
    this.eventTarget.dispatchEvent(new CustomEvent(eventType, { detail }))
  }

  /**
   * Subscribe to sync events
   */
  static on(eventType: string, handler: (event: CustomEvent) => void) {
    this.eventTarget.addEventListener(eventType, handler as EventListener)
  }

  /**
   * Unsubscribe from sync events
   */
  static off(eventType: string, handler: (event: CustomEvent) => void) {
    this.eventTarget.removeEventListener(eventType, handler as EventListener)
  }
}
