import { render, screen } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { TimerScreen } from '../TimerScreen'
import { useWorkout } from '@/hooks/useWorkout'
import { useRouter } from 'next/navigation'

vi.mock('@/hooks/useWorkout')
vi.mock('next/navigation')

// Type the mocks correctly for vitest

describe('TimerScreen - Floating Skip Button', () => {
  const mockPush = vi.fn()

  const defaultMockReturn = {
    currentExercise: { Id: 1, Label: 'Bench Press' },
    getNextExercise: vi.fn().mockReturnValue({ Id: 2, Label: 'Squats' }),
    currentSetIndex: 0,
    totalSets: 3,
    isLastSet: false,
    isLastExercise: false,
    nextSet: vi.fn(),
    getRestDuration: vi.fn().mockReturnValue(90),
    exercises: [{ Id: 1 }, { Id: 2 }],
  } as any

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue({ push: mockPush } as any)
    vi.mocked(useWorkout).mockReturnValue(defaultMockReturn)
  })

  it('should render skip button in a floating container', () => {
    render(
      <TimerScreen
        soundEnabled={false}
        vibrationEnabled={false}
        isBetweenSets
      />
    )

    // Check for floating container
    const floatingContainer = screen.getByTestId('floating-skip-button')
    expect(floatingContainer).toBeInTheDocument()
    expect(floatingContainer).toHaveClass('fixed')
    expect(floatingContainer).toHaveClass('bottom-6')
    expect(floatingContainer).toHaveClass('left-0')
    expect(floatingContainer).toHaveClass('right-0')
    expect(floatingContainer).toHaveClass('z-50')
  })

  it('should have proper padding and styling for floating effect', () => {
    render(
      <TimerScreen
        soundEnabled={false}
        vibrationEnabled={false}
        isBetweenSets
      />
    )

    const floatingContainer = screen.getByTestId('floating-skip-button')
    expect(floatingContainer).toHaveClass('px-4')

    // Should not have bottom-0 class (attached to bottom)
    expect(floatingContainer).not.toHaveClass('bottom-0')

    // Should not have border-t (no top border for floating design)
    expect(floatingContainer).not.toHaveClass('border-t')
  })

  it('should center the button with max width', () => {
    const { container } = render(
      <TimerScreen
        soundEnabled={false}
        vibrationEnabled={false}
        isBetweenSets
      />
    )

    const innerWrapper = container.querySelector('.max-w-lg.mx-auto')
    expect(innerWrapper).toBeInTheDocument()
  })

  it('should apply proper button styling', () => {
    render(
      <TimerScreen
        soundEnabled={false}
        vibrationEnabled={false}
        isBetweenSets
      />
    )

    const skipButton = screen.getByRole('button', { name: /skip/i })
    expect(skipButton).toHaveClass('w-full')
    expect(skipButton).toHaveClass('py-4')
    expect(skipButton).toHaveClass('rounded-full')
    expect(skipButton).toHaveClass('shadow-theme-xl')
  })

  it('should maintain skip functionality', () => {
    render(
      <TimerScreen
        soundEnabled={false}
        vibrationEnabled={false}
        isBetweenSets
      />
    )

    const skipButton = screen.getByRole('button', { name: /skip/i })
    skipButton.click()

    // Should navigate back to current exercise
    expect(mockPush).toHaveBeenCalledWith('/workout/exercise/1')
  })

  it('should have consistent height with other floating buttons', () => {
    render(
      <TimerScreen
        soundEnabled={false}
        vibrationEnabled={false}
        isBetweenSets
      />
    )

    const skipButton = screen.getByRole('button', { name: /skip/i })
    expect(skipButton).toHaveClass('min-h-[56px]')
  })
})
