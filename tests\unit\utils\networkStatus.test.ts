import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import { NetworkStatusManager } from '@/lib/networkStatus'

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true,
})

describe('NetworkStatusManager', () => {
  let networkManager: NetworkStatusManager
  
  beforeEach(() => {
    networkManager = new NetworkStatusManager()
    vi.clearAllMocks()
  })

  afterEach(() => {
    networkManager.destroy()
  })

  describe('Network Detection', () => {
    it('should detect initial online status', () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: true })

      // When
      const manager = new NetworkStatusManager()

      // Then
      expect(manager.isOnline).toBe(true)
    })

    it('should detect initial offline status', () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: false })

      // When
      const manager = new NetworkStatusManager()

      // Then
      expect(manager.isOnline).toBe(false)
    })

    it('should listen for online/offline events', () => {
      // Given
      const onStatusChange = vi.fn()
      networkManager.subscribe(onStatusChange)

      // When - simulate going offline
      Object.defineProperty(navigator, 'onLine', { value: false })
      window.dispatchEvent(new Event('offline'))

      // Then
      expect(onStatusChange).toHaveBeenCalledWith(false)
      expect(networkManager.isOnline).toBe(false)
    })

    it('should detect network reconnection', () => {
      // Given
      const onStatusChange = vi.fn()
      networkManager.subscribe(onStatusChange)
      Object.defineProperty(navigator, 'onLine', { value: false })

      // When - simulate coming back online
      Object.defineProperty(navigator, 'onLine', { value: true })
      window.dispatchEvent(new Event('online'))

      // Then
      expect(onStatusChange).toHaveBeenCalledWith(true)
      expect(networkManager.isOnline).toBe(true)
    })
  })

  describe('Connection Quality', () => {
    it('should detect slow connection', async () => {
      // Given
      const slowResponse = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 5000))
      )

      // When
      const quality = await networkManager.checkConnectionQuality(slowResponse)

      // Then
      expect(quality).toBe('slow')
    })

    it('should detect fast connection', async () => {
      // Given
      const fastResponse = vi.fn().mockResolvedValue({ ok: true })

      // When
      const quality = await networkManager.checkConnectionQuality(fastResponse)

      // Then
      expect(quality).toBe('fast')
    })

    it('should handle connection test failures', async () => {
      // Given
      const failedResponse = vi.fn().mockRejectedValue(new Error('Network Error'))

      // When
      const quality = await networkManager.checkConnectionQuality(failedResponse)

      // Then
      expect(quality).toBe('offline')
    })
  })

  describe('Subscription Management', () => {
    it('should allow multiple subscribers', () => {
      // Given
      const subscriber1 = vi.fn()
      const subscriber2 = vi.fn()
      
      networkManager.subscribe(subscriber1)
      networkManager.subscribe(subscriber2)

      // When
      Object.defineProperty(navigator, 'onLine', { value: false })
      window.dispatchEvent(new Event('offline'))

      // Then
      expect(subscriber1).toHaveBeenCalledWith(false)
      expect(subscriber2).toHaveBeenCalledWith(false)
    })

    it('should allow unsubscribing', () => {
      // Given
      const subscriber = vi.fn()
      const unsubscribe = networkManager.subscribe(subscriber)

      // When
      unsubscribe()
      Object.defineProperty(navigator, 'onLine', { value: false })
      window.dispatchEvent(new Event('offline'))

      // Then
      expect(subscriber).not.toHaveBeenCalled()
    })
  })
})

describe('useNetworkStatus', () => {
  beforeEach(() => {
    Object.defineProperty(navigator, 'onLine', { value: true })
  })

  describe('Status Tracking', () => {
    it('should return current network status', () => {
      // Given/When
      const { result } = renderHook(() => useNetworkStatus())

      // Then
      expect(result.current.isOnline).toBe(true)
      expect(result.current.isOffline).toBe(false)
    })

    it('should update status when network changes', () => {
      // Given
      const { result } = renderHook(() => useNetworkStatus())

      // When
      act(() => {
        Object.defineProperty(navigator, 'onLine', { value: false })
        window.dispatchEvent(new Event('offline'))
      })

      // Then
      expect(result.current.isOnline).toBe(false)
      expect(result.current.isOffline).toBe(true)
    })

    it('should track connection quality', async () => {
      // Given
      const { result } = renderHook(() => useNetworkStatus())

      // When
      await act(async () => {
        await result.current.checkConnectionQuality()
      })

      // Then
      expect(result.current.connectionQuality).toMatch(/fast|slow|offline/)
    })
  })

  describe('Event Callbacks', () => {
    it('should call onOnline callback when coming online', () => {
      // Given
      const onOnline = vi.fn()
      renderHook(() => useNetworkStatus({ onOnline }))

      // When
      act(() => {
        Object.defineProperty(navigator, 'onLine', { value: true })
        window.dispatchEvent(new Event('online'))
      })

      // Then
      expect(onOnline).toHaveBeenCalled()
    })

    it('should call onOffline callback when going offline', () => {
      // Given
      const onOffline = vi.fn()
      renderHook(() => useNetworkStatus({ onOffline }))

      // When
      act(() => {
        Object.defineProperty(navigator, 'onLine', { value: false })
        window.dispatchEvent(new Event('offline'))
      })

      // Then
      expect(onOffline).toHaveBeenCalled()
    })
  })

  describe('Automatic Sync', () => {
    it('should trigger sync when coming back online', () => {
      // Given
      const onSync = vi.fn()
      renderHook(() => useNetworkStatus({ onSync }))

      // When - go offline then online
      act(() => {
        Object.defineProperty(navigator, 'onLine', { value: false })
        window.dispatchEvent(new Event('offline'))
      })

      act(() => {
        Object.defineProperty(navigator, 'onLine', { value: true })
        window.dispatchEvent(new Event('online'))
      })

      // Then
      expect(onSync).toHaveBeenCalled()
    })
  })
}) 