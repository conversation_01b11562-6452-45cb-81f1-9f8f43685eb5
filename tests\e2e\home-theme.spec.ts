import { test, expect } from '@playwright/test'

test.describe('Home Page Theme Styling', () => {
  test('should apply theme brand color to title and theme text color to subtitle', async ({
    page,
  }) => {
    // Given: Navigate to home page
    await page.goto('/')

    // When: Page loads
    // Then: Title should have gold color (brand primary) and subtitle should have theme text color
    const title = page.locator('h1:has-text("Dr. Muscle X")')
    const subtitle = page.locator("text=World's Fastest AI Personal Trainer")

    // Check that title has brand color class
    await expect(title).toHaveClass(/text-brand-primary/)

    // Check that subtitle has theme text color class
    await expect(subtitle).toHaveClass(/text-text-secondary/)

    // Verify the actual color values from CSS variables
    const titleColor = await title.evaluate((el) => {
      return window.getComputedStyle(el).color
    })
    const subtitleColor = await subtitle.evaluate((el) => {
      return window.getComputedStyle(el).color
    })

    // Gold color for brand primary in subtle-depth theme
    expect(titleColor).toBe('rgb(212, 175, 55)') // #d4af37

    // Secondary text color in subtle-depth theme
    expect(subtitleColor).toBe('rgb(184, 184, 188)') // #b8b8bc
  })

  test('should show theme-styled content briefly before redirect', async ({
    page,
  }) => {
    // Given: Navigate to home page
    await page.goto('/')

    // When: Page loads
    // Then: Content should be visible with theme styling
    const container = page.locator('main')
    await expect(container).toBeVisible()

    // Check background color matches theme
    await container.evaluate((el) => {
      return window.getComputedStyle(el).backgroundColor
    })

    // Should inherit from body which has theme background
    // Note: The actual background color is set on body element
    const bodyBgColor = await page.evaluate(() => {
      return window.getComputedStyle(document.body).backgroundColor
    })

    // Theme background color for subtle-depth
    expect(bodyBgColor).toBe('rgb(10, 10, 11)') // #0a0a0b
  })
})
