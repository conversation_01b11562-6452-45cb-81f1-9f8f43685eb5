import { useProgramStore } from './programStore'

/**
 * Selector hooks for convenient access to program cache
 */
export const useProgramCache = () => {
  const store = useProgramStore()
  return {
    program: store.getCachedProgram(),
    progress: store.getCachedProgress(),
    stats: store.getCachedStats(),
    isProgramStale: store.isProgramCacheStale(),
    isProgressStale: store.isProgressCacheStale(),
    isStatsStale: store.isStatsCacheStale(),
    hasHydrated: store.hasHydrated,
  }
}
