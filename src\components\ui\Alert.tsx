'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface AlertProps {
  variant?: 'error' | 'warning' | 'success' | 'info'
  title?: string
  children: React.ReactNode
  className?: string
  action?: {
    label: string
    onClick: () => void
  }
}

const variantStyles = {
  error: {
    container: 'bg-error/10 border-error/20 text-error',
    icon: 'text-error',
    action: 'text-error/80 hover:text-error',
  },
  warning: {
    container: 'bg-warning/10 border-warning/20 text-warning',
    icon: 'text-warning',
    action: 'text-warning/80 hover:text-warning',
  },
  success: {
    container: 'bg-success/10 border-success/20 text-success',
    icon: 'text-success',
    action: 'text-success/80 hover:text-success',
  },
  info: {
    container: 'bg-info/10 border-info/20 text-info',
    icon: 'text-info',
    action: 'text-info/80 hover:text-info',
  },
}

const icons = {
  error: (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  ),
  warning: (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
      />
    </svg>
  ),
  success: (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  ),
  info: (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  ),
}

export function Alert({
  variant = 'info',
  title,
  children,
  className,
  action,
}: AlertProps) {
  const styles = variantStyles[variant]

  return (
    <div
      className={cn('p-4 border rounded-theme', styles.container, className)}
      role="alert"
    >
      <div className="flex gap-3">
        <div className={cn('flex-shrink-0', styles.icon)}>{icons[variant]}</div>
        <div className="flex-1">
          {title && <h3 className="font-medium mb-1">{title}</h3>}
          <div className="text-sm">{children}</div>
          {action && (
            <button
              onClick={action.onClick}
              className={cn(
                'mt-2 text-sm underline transition-colors',
                styles.action
              )}
            >
              {action.label}
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
