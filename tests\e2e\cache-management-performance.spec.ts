import { test, expect, Page } from '@playwright/test'

test.describe('Cache Performance Management Tests', () => {
  let page: Page
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p }) => {
    page = p
    // Clear all caches before each test
    await page.goto('/')
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })
  })

  test('should implement cache memory limits', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Monitor memory usage
    const initialMemory = await page.evaluate(() => {
      if ('memory' in performance) {
        return (performance as any).memory.usedJSHeapSize
      }
      return 0
    })

    // Load large amount of data
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Check memory after loading
    const afterLoadMemory = await page.evaluate(() => {
      if ('memory' in performance) {
        return (performance as any).memory.usedJSHeapSize
      }
      return 0
    })

    // Memory increase should be reasonable (less than 10MB for workout data)
    const memoryIncrease = afterLoadMemory - initialMemory
    expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024) // 10MB
  })

  test('should implement LRU eviction for cache entries', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Create multiple cache entries with timestamps
    await page.evaluate(() => {
      const cacheEntries = [
        { key: 'cache-1', lastAccessed: Date.now() - 3600000 }, // 1 hour ago
        { key: 'cache-2', lastAccessed: Date.now() - 7200000 }, // 2 hours ago
        { key: 'cache-3', lastAccessed: Date.now() - 1800000 }, // 30 min ago
      ]

      cacheEntries.forEach((entry) => {
        localStorage.setItem(
          entry.key,
          JSON.stringify({
            data: 'x'.repeat(1024 * 100), // 100KB each
            lastAccessed: entry.lastAccessed,
          })
        )
      })
    })

    // Trigger cache cleanup by adding new data
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Check which entries remain
    const remainingEntries = await page.evaluate(() => {
      return {
        cache1: !!localStorage.getItem('cache-1'),
        cache2: !!localStorage.getItem('cache-2'),
        cache3: !!localStorage.getItem('cache-3'),
      }
    })

    // Oldest entry (cache-2) should be evicted first if needed
    if (!remainingEntries.cache1 || !remainingEntries.cache3) {
      expect(remainingEntries.cache2).toBe(false)
    }
  })
})
