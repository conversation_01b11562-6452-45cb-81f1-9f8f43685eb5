import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { useState } from 'react'
import { useRouter, usePathname, useSearchParams } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { createMockUseWorkoutReturn } from './WorkoutNavigation.test.helpers'

// Mock components we'll test
function WorkoutPage() {
  const router = useRouter()
  const { startWorkout, isLoadingWorkout } = useWorkout()
  const [isNavigating, setIsNavigating] = useState(false)

  const handleStartWorkout = async () => {
    setIsNavigating(true)
    startWorkout()
    await router.push('/workout/exercise/1')
    setIsNavigating(false)
  }

  return (
    <div>
      <h1>Today's Workout</h1>
      <button onClick={handleStartWorkout}>Start Workout</button>
      {(isLoadingWorkout || isNavigating) && (
        <div data-testid="navigation-loading">Loading...</div>
      )}
    </div>
  )
}

function ExercisePage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { currentSetIndex, totalSets, nextSet, isLastExercise, error } =
    useWorkout()

  // Handle invalid exercise ID
  if (error === 'Exercise not found') {
    router.replace('/workout')
    return null
  }

  const handleNextSet = () => {
    if (currentSetIndex < totalSets - 1) {
      nextSet()
    } else if (!isLastExercise) {
      router.push(`/workout/exercise/${parseInt(params.id) + 1}`)
    } else {
      router.push('/workout/complete')
    }
  }

  return (
    <div>
      <h1>Exercise {params.id}</h1>
      <p>
        Set {currentSetIndex + 1} of {totalSets}
      </p>
      <button onClick={handleNextSet}>Next</button>
    </div>
  )
}

// Mock dependencies
vi.mock('next/navigation')
vi.mock('@/hooks/useWorkout')

describe('Workout Navigation Flow', () => {
  const mockPush = vi.fn()
  const mockBack = vi.fn()
  const mockReplace = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    vi.mocked(useRouter).mockReturnValue({
      push: mockPush,
      back: mockBack,
      replace: mockReplace,
      forward: vi.fn(),
      refresh: vi.fn(),
      prefetch: vi.fn(),
    } as ReturnType<typeof useRouter>)

    vi.mocked(usePathname).mockReturnValue('/workout')
    vi.mocked(useSearchParams).mockReturnValue(new URLSearchParams())
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Workout Navigation Flow', () => {
    it('should navigate from workout to first exercise', () => {
      // Given
      vi.mocked(useWorkout).mockReturnValue(
        createMockUseWorkoutReturn({
          startWorkout: vi.fn(),
          currentSetIndex: 0,
          totalSets: 4,
          isLastExercise: false,
        })
      )

      render(<WorkoutPage />)

      // When
      const startButton = screen.getByText('Start Workout')
      fireEvent.click(startButton)

      // Then
      expect(mockPush).toHaveBeenCalledWith('/workout/exercise/1')
    })

    it('should navigate between exercises', () => {
      // Given
      vi.mocked(useWorkout).mockReturnValue(
        createMockUseWorkoutReturn({
          currentSetIndex: 3, // Last set
          totalSets: 4,
          nextSet: vi.fn(),
          isLastExercise: false,
        })
      )

      render(<ExercisePage params={{ id: '1' }} />)

      // When
      const nextButton = screen.getByText('Next')
      fireEvent.click(nextButton)

      // Then
      expect(mockPush).toHaveBeenCalledWith('/workout/exercise/2')
    })

    it('should navigate to completion after last exercise', () => {
      // Given
      vi.mocked(useWorkout).mockReturnValue(
        createMockUseWorkoutReturn({
          currentSetIndex: 3, // Last set
          totalSets: 4,
          nextSet: vi.fn(),
          isLastExercise: true,
        })
      )

      render(<ExercisePage params={{ id: '3' }} />)

      // When
      const nextButton = screen.getByText('Next')
      fireEvent.click(nextButton)

      // Then
      expect(mockPush).toHaveBeenCalledWith('/workout/complete')
    })
  })

  describe('URL State Management', () => {
    it('should reflect current exercise in URL', () => {
      // Given
      vi.mocked(usePathname).mockReturnValue('/workout/exercise/2')
      vi.mocked(useWorkout).mockReturnValue(
        createMockUseWorkoutReturn({
          currentSetIndex: 1,
          totalSets: 4,
          nextSet: vi.fn(),
          isLastExercise: false,
        })
      )

      // When
      render(<ExercisePage params={{ id: '2' }} />)

      // Then
      expect(screen.getByText('Exercise 2')).toBeInTheDocument()
      expect(screen.getByText('Set 2 of 4')).toBeInTheDocument()
    })

    it('should preserve workout state on page refresh', async () => {
      // Given - simulate page refresh with state preservation

      vi.mocked(useWorkout).mockReturnValue(
        createMockUseWorkoutReturn({
          currentSetIndex: 2,
          totalSets: 4,
          nextSet: vi.fn(),
          isLastExercise: false,
        })
      )

      // When
      render(<ExercisePage params={{ id: '2' }} />)

      // Then
      expect(screen.getByText('Set 3 of 4')).toBeInTheDocument()
    })
  })

  describe('Back Button Handling', () => {
    it('should show warning when trying to leave during workout', () => {
      // Given
      let eventPrevented = false
      const handleBeforeUnload = (e: BeforeUnloadEvent) => {
        e.preventDefault()
        e.returnValue = ''
        eventPrevented = true
        return ''
      }

      // When
      window.addEventListener('beforeunload', handleBeforeUnload)
      const event = new Event('beforeunload', {
        cancelable: true,
      }) as BeforeUnloadEvent
      window.dispatchEvent(event)

      // Then
      expect(eventPrevented).toBe(true)

      // Cleanup
      window.removeEventListener('beforeunload', handleBeforeUnload)
    })

    it('should allow navigation if user confirms', () => {
      // Given
      const mockConfirm = vi.spyOn(window, 'confirm').mockReturnValue(true)

      vi.mocked(useWorkout).mockReturnValue(
        createMockUseWorkoutReturn({
          currentSetIndex: 2,
          totalSets: 4,
          nextSet: vi.fn(),
          isLastExercise: false,
        })
      )

      render(<ExercisePage params={{ id: '2' }} />)

      // When - simulate back navigation
      mockBack()

      // Then
      expect(mockBack).toHaveBeenCalled()

      // Cleanup
      mockConfirm.mockRestore()
    })
  })

  describe('Deep Linking', () => {
    it('should support deep linking to specific exercise', () => {
      // Given
      vi.mocked(usePathname).mockReturnValue('/workout/exercise/3')
      vi.mocked(useWorkout).mockReturnValue(
        createMockUseWorkoutReturn({
          currentSetIndex: 0,
          totalSets: 4,
          nextSet: vi.fn(),
          isLastExercise: false,
        })
      )

      // When
      render(<ExercisePage params={{ id: '3' }} />)

      // Then
      expect(screen.getByText('Exercise 3')).toBeInTheDocument()
    })

    it('should handle invalid exercise ID in URL', () => {
      // Given
      vi.mocked(useWorkout).mockReturnValue(
        createMockUseWorkoutReturn({
          currentSetIndex: 0,
          totalSets: 0,
          nextSet: vi.fn(),
          isLastExercise: false,
          error: 'Exercise not found',
        })
      )

      // When
      render(<ExercisePage params={{ id: '999' }} />)

      // Then
      expect(mockReplace).toHaveBeenCalledWith('/workout')
    })
  })

  describe('Route Transitions', () => {
    it('should show loading state during navigation', async () => {
      // Given
      let resolveNavigation: () => void
      const navigationPromise = new Promise<void>((resolve) => {
        resolveNavigation = resolve
      })

      mockPush.mockImplementation(() => navigationPromise)

      vi.mocked(useWorkout).mockReturnValue(
        createMockUseWorkoutReturn({
          startWorkout: vi.fn(),
          isLoading: true,
        })
      )

      // When
      render(<WorkoutPage />)
      const startButton = screen.getByText('Start Workout')
      fireEvent.click(startButton)

      // Then - should show loading state
      await waitFor(() => {
        expect(screen.getByTestId('navigation-loading')).toBeInTheDocument()
      })

      // Complete navigation
      resolveNavigation!()

      // Loading should disappear
      await waitFor(() => {
        expect(
          screen.queryByTestId('navigation-loading')
        ).not.toBeInTheDocument()
      })
    })
  })
})
