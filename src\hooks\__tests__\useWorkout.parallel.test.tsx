import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useWorkout } from '../useWorkout'
import * as workoutApiModule from '@/api/workouts'
import type {
  MockProgramInfoResponse,
  MockUserWorkoutResponse,
} from './useWorkout.types'

// Mock the API
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserProgramInfo: vi.fn(),
    getUserWorkout: vi.fn(),
    getExerciseRecommendation: vi.fn(),
    saveWorkoutSet: vi.fn(),
    completeWorkout: vi.fn(),
  },
}))

// Mock auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: vi.fn(() => ({
    isAuthenticated: true,
  })),
}))

// Mock workout store
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(() => ({
    currentWorkout: null,
    currentSetIndex: 0,
    currentExerciseIndex: 0,
    exercises: [],
    workoutSession: null,
    isLoading: false,
    error: null,
    setWorkout: vi.fn(),
    startWorkout: vi.fn(),
    nextSet: vi.fn(),
    nextExercise: vi.fn(),
    saveSet: vi.fn(),
    completeWorkout: vi.fn(),
    setError: vi.fn(),
    getCurrentExercise: vi.fn(),
    getNextExercise: vi.fn(),
    getRestDuration: vi.fn(),
  })),
}))

describe('useWorkout - Parallel API Calls', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })
    vi.clearAllMocks()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  it('should fetch getUserProgramInfo and getUserWorkout in parallel', async () => {
    const programInfoPromise = new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          GetUserProgramInfoResponseModel: {
            RecommendedProgram: { Id: 1, Label: 'Test Program' },
            NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
            WorkoutTemplates: [],
          },
        })
      }, 100)
    })

    const userWorkoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            Id: 1,
            Label: 'Test Workout',
            Exercises: [{ Id: 1, Label: 'Bench Press' }],
          },
        ])
      }, 100)
    })

    const getUserProgramInfoSpy = vi
      .spyOn(workoutApiModule.workoutApi, 'getUserProgramInfo')
      .mockReturnValue(programInfoPromise as Promise<MockProgramInfoResponse>)

    const getUserWorkoutSpy = vi
      .spyOn(workoutApiModule.workoutApi, 'getUserWorkout')
      .mockReturnValue(userWorkoutPromise as Promise<MockUserWorkoutResponse>)

    const startTime = Date.now()

    renderHook(() => useWorkout(), { wrapper })

    // Both API calls should start immediately
    expect(getUserProgramInfoSpy).toHaveBeenCalledTimes(1)
    expect(getUserWorkoutSpy).toHaveBeenCalledTimes(1)

    // Wait for both to complete
    await waitFor(() => {
      expect(getUserProgramInfoSpy).toHaveBeenCalledTimes(1)
      expect(getUserWorkoutSpy).toHaveBeenCalledTimes(1)
    })

    const endTime = Date.now()
    const duration = endTime - startTime

    // Both calls take 100ms each, but running in parallel should complete in ~100ms, not 200ms
    expect(duration).toBeLessThan(150) // Allow some overhead
  })

  it('should handle partial failures gracefully', async () => {
    const programInfoPromise = Promise.reject(new Error('Program info failed'))
    const userWorkoutPromise = Promise.resolve([
      {
        Id: 1,
        Label: 'Test Workout',
        Exercises: [{ Id: 1, Label: 'Bench Press' }],
      },
    ])

    vi.spyOn(workoutApiModule.workoutApi, 'getUserProgramInfo').mockReturnValue(
      programInfoPromise as Promise<MockProgramInfoResponse>
    )
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockReturnValue(
      userWorkoutPromise as Promise<MockUserWorkoutResponse>
    )

    const { result } = renderHook(() => useWorkout(), { wrapper })

    await waitFor(() => {
      // Should have error from program info
      expect(result.current.workoutError).toBeTruthy()
      // But should still attempt to get user workout
      expect(workoutApiModule.workoutApi.getUserWorkout).toHaveBeenCalled()
    })
  })

  it('should track loading progress granularly', async () => {
    const programInfoPromise = new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          GetUserProgramInfoResponseModel: {
            RecommendedProgram: { Id: 1, Label: 'Test Program' },
            NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
          },
        })
      }, 50)
    })

    const userWorkoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        resolve([{ Id: 1, Label: 'Test Workout', Exercises: [] }])
      }, 100)
    })

    vi.spyOn(workoutApiModule.workoutApi, 'getUserProgramInfo').mockReturnValue(
      programInfoPromise as Promise<MockProgramInfoResponse>
    )
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockReturnValue(
      userWorkoutPromise as Promise<MockUserWorkoutResponse>
    )

    const { result } = renderHook(() => useWorkout(), { wrapper })

    // Initially loading
    expect(result.current.isLoadingWorkout).toBe(true)

    // After 60ms, program info should be done but user workout still loading
    await new Promise((resolve) => {
      setTimeout(resolve, 60)
    })

    // Still loading because one query is pending
    expect(result.current.isLoadingWorkout).toBe(true)

    // After 110ms, both should be done
    await new Promise((resolve) => {
      setTimeout(resolve, 50)
    })

    await waitFor(() => {
      expect(result.current.isLoadingWorkout).toBe(false)
    })
  })

  it('should merge data correctly when both calls succeed', async () => {
    const programInfo = {
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: {
          Id: 1,
          Label: 'Test Program',
          RemainingToLevelUp: 5,
        },
        NextWorkoutTemplate: {
          Id: 2,
          Label: 'Upper Body',
          IsSystemExercise: true,
          Exercises: [],
        },
        WorkoutTemplates: [],
      },
    }

    const userWorkouts = [
      {
        Id: 2,
        Label: 'Upper Body',
        Exercises: [
          { Id: 1, Label: 'Bench Press' },
          { Id: 2, Label: 'Shoulder Press' },
        ],
      },
    ]

    vi.spyOn(
      workoutApiModule.workoutApi,
      'getUserProgramInfo'
    ).mockResolvedValue(programInfo as MockProgramInfoResponse)
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockResolvedValue(
      userWorkouts as MockUserWorkoutResponse
    )

    const { result } = renderHook(() => useWorkout(), { wrapper })

    await waitFor(() => {
      expect(result.current.todaysWorkout).toBeTruthy()
    })

    // Check merged data
    expect(result.current.todaysWorkout).toHaveLength(1)
    expect(result.current.todaysWorkout![0].Label).toBe('Test Program')
    expect(result.current.todaysWorkout![0].WorkoutTemplates).toHaveLength(1)
    expect(
      result.current.todaysWorkout![0].WorkoutTemplates[0].Exercises
    ).toHaveLength(2)
    expect(result.current.todaysWorkout![0].RequiredWorkoutToLevelUp).toBe(5)
  })

  it('should not wait for getUserProgramInfo before starting getUserWorkout', async () => {
    let programInfoStarted = false
    let userWorkoutStarted = false
    let programInfoResolved = false

    const programInfoPromise = new Promise((resolve) => {
      programInfoStarted = true
      setTimeout(() => {
        programInfoResolved = true
        resolve({
          GetUserProgramInfoResponseModel: {
            RecommendedProgram: { Id: 1, Label: 'Test Program' },
            NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
          },
        })
      }, 100)
    })

    const userWorkoutPromise = new Promise((resolve) => {
      userWorkoutStarted = true
      // This should start immediately, not wait for programInfo
      expect(programInfoResolved).toBe(false)
      resolve([{ Id: 1, Label: 'Test Workout', Exercises: [] }])
    })

    vi.spyOn(workoutApiModule.workoutApi, 'getUserProgramInfo').mockReturnValue(
      programInfoPromise as Promise<MockProgramInfoResponse>
    )
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockReturnValue(
      userWorkoutPromise as Promise<MockUserWorkoutResponse>
    )

    renderHook(() => useWorkout(), { wrapper })

    // Both should start immediately
    expect(programInfoStarted).toBe(true)
    expect(userWorkoutStarted).toBe(true)
  })

  it('should provide loading states for each API call separately', async () => {
    const programInfoPromise = new Promise((resolve) => {
      setTimeout(() => resolve({ GetUserProgramInfoResponseModel: {} }), 50)
    })

    const userWorkoutPromise = new Promise((resolve) => {
      setTimeout(() => resolve([]), 100)
    })

    vi.spyOn(workoutApiModule.workoutApi, 'getUserProgramInfo').mockReturnValue(
      programInfoPromise as Promise<MockProgramInfoResponse>
    )
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockReturnValue(
      userWorkoutPromise as Promise<MockUserWorkoutResponse>
    )

    const { result } = renderHook(() => useWorkout(), { wrapper })

    // Check for new granular loading states
    expect(result.current.loadingStates).toBeDefined()
    expect(result.current.loadingStates.programInfo).toBe(true)
    expect(result.current.loadingStates.userWorkout).toBe(true)

    // After program info completes
    await new Promise((resolve) => {
      setTimeout(resolve, 60)
    })

    await waitFor(() => {
      expect(result.current.loadingStates.programInfo).toBe(false)
      expect(result.current.loadingStates.userWorkout).toBe(true)
    })

    // After user workout completes
    await new Promise((resolve) => {
      setTimeout(resolve, 50)
    })

    await waitFor(() => {
      expect(result.current.loadingStates.programInfo).toBe(false)
      expect(result.current.loadingStates.userWorkout).toBe(false)
    })
  })
})
