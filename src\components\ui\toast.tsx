interface ToastOptions {
  title?: string
  description: string
  variant?: 'default' | 'subtle' | 'error'
  duration?: number
}

// Simple toast implementation for error handling
// In production, you might want to use a library like react-hot-toast
let toastContainer: HTMLDivElement | null = null

function ensureToastContainer() {
  if (!toastContainer && typeof document !== 'undefined') {
    toastContainer = document.createElement('div')
    toastContainer.id = 'toast-container'
    toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2'
    document.body.appendChild(toastContainer)
  }
  return toastContainer
}

export function toast(options: ToastOptions) {
  const container = ensureToastContainer()
  if (!container) return

  const toastEl = document.createElement('div')
  let variantClasses = ''
  if (options.variant === 'error') {
    variantClasses =
      'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
  } else if (options.variant === 'subtle') {
    variantClasses =
      'bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
  } else {
    variantClasses =
      'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
  }

  toastEl.className = `max-w-sm p-4 rounded-lg shadow-lg transform transition-all duration-300 ${variantClasses}`
  toastEl.style.transform = 'translateX(400px)'

  // Create elements safely to prevent XSS
  if (options.title) {
    const titleEl = document.createElement('h4')
    titleEl.className = 'font-medium text-gray-900 dark:text-gray-100 mb-1'
    titleEl.textContent = options.title
    toastEl.appendChild(titleEl)
  }

  const descriptionEl = document.createElement('p')
  descriptionEl.className = 'text-sm text-gray-600 dark:text-gray-400'
  descriptionEl.textContent = options.description
  toastEl.appendChild(descriptionEl)

  container.appendChild(toastEl)

  // Animate in
  requestAnimationFrame(() => {
    toastEl.style.transform = 'translateX(0)'
  })

  // Remove after duration
  const duration = options.duration || 4000
  setTimeout(() => {
    toastEl.style.transform = 'translateX(400px)'
    setTimeout(() => {
      container.removeChild(toastEl)
    }, 300)
  }, duration)
}
