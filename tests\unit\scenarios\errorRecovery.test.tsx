import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { SetScreen } from '@/components/workout/SetScreen'
import { useWorkout } from '@/hooks/useWorkout'
import { apiClient } from '@/api/client'
import { offlineQueue } from '@/services/offlineQueue'

// Mock dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('@/api/client')
vi.mock('@/services/offlineQueue', () => ({
  offlineQueue: {
    addToQueue: vi.fn(),
    processQueue: vi.fn(),
  },
}))

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return function ({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

describe('Error Recovery Scenarios', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    vi.clearAllMocks()
    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true,
    })
  })

  describe('Network Failure During Set Save', () => {
    it('should queue set data locally when network fails', async () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: false })

      vi.mocked(useWorkout).mockReturnValue({
        currentExercise: {
          Id: 123,
          Name: 'Bench Press',
          Sets: [{ SetNumber: 1, Reps: 10, Weight: 100 }],
        },
        currentSetIndex: 0,
        saveSet: vi.fn().mockRejectedValue(new Error('Network error')),
        isLoadingSet: false,
      } as any)

      vi.mocked(offlineQueue.addToQueue).mockReturnValue('queue-123')

      render(<SetScreen exerciseId={123} />, { wrapper: createWrapper() })

      // When - user saves set while offline
      const saveButton = screen.getByRole('button', { name: /save set/i })
      await user.click(saveButton)

      // Then
      await waitFor(() => {
        expect(offlineQueue.addToQueue).toHaveBeenCalledWith(
          expect.objectContaining({
            url: expect.stringContaining('/Set/SaveUserSet'),
            method: 'post',
          })
        )
      })
    })

    it('should retry queued requests when connection restored', async () => {
      // Given - start offline
      Object.defineProperty(navigator, 'onLine', { value: false })

      const mockProcessQueue = vi.fn().mockResolvedValue(undefined)
      vi.mocked(offlineQueue).processQueue = mockProcessQueue

      // When - come back online
      Object.defineProperty(navigator, 'onLine', { value: true })
      window.dispatchEvent(new Event('online'))

      // Then
      await waitFor(() => {
        expect(mockProcessQueue).toHaveBeenCalled()
      })
    })
  })

  describe('Authentication Expiration Mid-Workout', () => {
    it('should preserve workout data when auth expires', async () => {
      // Given
      const mockWorkoutData = {
        exerciseId: 123,
        setNumber: 2,
        weight: 100,
        reps: 10,
      }

      vi.mocked(useWorkout).mockReturnValue({
        currentExercise: { Id: 123, Name: 'Bench Press' },
        currentSetIndex: 1,
        saveSet: vi.fn().mockRejectedValue({
          response: { status: 401 },
          message: 'Unauthorized',
        }),
        workoutSession: mockWorkoutData,
      } as any)

      render(<SetScreen exerciseId={123} />, { wrapper: createWrapper() })

      // When - save fails with 401
      const saveButton = screen.getByRole('button', { name: /save set/i })
      await user.click(saveButton)

      // Then - workout data should be preserved
      expect(localStorage.getItem('preserved_workout')).toBeTruthy()
    })

    it('should restore workout after successful re-authentication', async () => {
      // Given - preserved workout data
      const preservedData = {
        exerciseId: 123,
        setNumber: 2,
        timestamp: Date.now(),
      }
      localStorage.setItem('preserved_workout', JSON.stringify(preservedData))

      // When - user logs back in
      vi.mocked(useWorkout).mockReturnValue({
        restoreWorkout: vi.fn(),
      } as any)

      // Then - workout should be restored
      const { restoreWorkout } = useWorkout()
      restoreWorkout({})

      expect(restoreWorkout).toHaveBeenCalledWith({})
    })
  })

  describe('API Response Failures', () => {
    it('should handle malformed API responses gracefully', async () => {
      // Given
      vi.mocked(apiClient.get).mockResolvedValue({
        data: 'invalid json string instead of object',
      })

      vi.mocked(useWorkout).mockReturnValue({
        currentWorkout: null,
        error: 'Invalid response format',
      } as any)

      // When
      render(<SetScreen exerciseId={123} />, { wrapper: createWrapper() })

      // Then
      await waitFor(() => {
        expect(screen.getByText(/error loading workout/i)).toBeInTheDocument()
      })
    })

    it('should handle API rate limiting', async () => {
      // Given
      vi.mocked(apiClient.post).mockRejectedValue({
        response: {
          status: 429,
          headers: { 'retry-after': '60' },
        },
      })

      vi.mocked(useWorkout).mockReturnValue({
        saveSet: vi.fn().mockRejectedValue(new Error('Rate limited')),
        error: 'Too many requests. Please wait.',
      } as any)

      render(<SetScreen exerciseId={123} />, { wrapper: createWrapper() })

      // When
      const saveButton = screen.getByRole('button', { name: /save set/i })
      await user.click(saveButton)

      // Then
      await waitFor(() => {
        expect(screen.getByText(/too many requests/i)).toBeInTheDocument()
      })
    })
  })
})
