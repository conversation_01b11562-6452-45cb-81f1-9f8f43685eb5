import { apiClient } from './client'
import type {
  GetUserWorkoutLogAverageResponse,
  TimeZoneInfoModel,
} from '@/types'
import { withTimeout } from '@/lib/errorUtils'
import { logger } from '@/utils/logger'

// Singleton instance to manage unified data fetching
class UnifiedProgramDataManager {
  private pendingRequest: Promise<GetUserWorkoutLogAverageResponse | null> | null =
    null

  private lastFetch: number = 0

  private cachedData: GetUserWorkoutLogAverageResponse | null = null

  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  /**
   * Fetch unified program data from the API
   * This ensures only one request is made even if multiple components request data simultaneously
   */
  async fetchData(): Promise<GetUserWorkoutLogAverageResponse | null> {
    // Check if we have fresh cached data
    if (this.cachedData && Date.now() - this.lastFetch < this.CACHE_DURATION) {
      logger.log('[UnifiedProgramData] Returning cached data')
      return this.cachedData
    }

    // If a request is already in progress, return the same promise
    if (this.pendingRequest) {
      logger.log('[UnifiedProgramData] Returning pending request')
      return this.pendingRequest
    }

    // Create a new request
    logger.log('[UnifiedProgramData] Making new API request')
    this.pendingRequest = this.makeRequest()

    try {
      const result = await this.pendingRequest
      if (result) {
        this.cachedData = result
        this.lastFetch = Date.now()
      }
      return result
    } finally {
      // Clear pending request after completion
      this.pendingRequest = null
    }
  }

  // eslint-disable-next-line class-methods-use-this
  private async makeRequest(): Promise<GetUserWorkoutLogAverageResponse | null> {
    try {
      const timeZoneInfo: TimeZoneInfoModel = {
        TimeZoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
        Offset: new Date().getTimezoneOffset() / -60,
        IsDaylightSaving: false,
      }

      const response = await withTimeout(
        apiClient.post<{
          StatusCode: number
          Result?: GetUserWorkoutLogAverageResponse
        }>(
          '/api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2',
          timeZoneInfo
        ),
        30000, // 30 second timeout
        'Loading program data'
      )

      if (response.data.StatusCode !== 200 || !response.data.Result) {
        logger.error('[UnifiedProgramData] Invalid response:', response.data)
        return null
      }

      logger.log('[UnifiedProgramData] API request successful')
      return response.data.Result
    } catch (error) {
      logger.error('[UnifiedProgramData] API request failed:', error)
      throw error
    }
  }

  /**
   * Clear the cache (useful for logout or manual refresh)
   */
  clearCache(): void {
    this.cachedData = null
    this.lastFetch = 0
    this.pendingRequest = null
    logger.log('[UnifiedProgramData] Cache cleared')
  }

  /**
   * Check if cache is stale
   */
  isCacheStale(): boolean {
    return (
      !this.cachedData || Date.now() - this.lastFetch >= this.CACHE_DURATION
    )
  }
}

// Export singleton instance
export const unifiedProgramDataManager = new UnifiedProgramDataManager()

// Export convenience functions
export const fetchUnifiedProgramData = () =>
  unifiedProgramDataManager.fetchData()
export const clearUnifiedProgramDataCache = () =>
  unifiedProgramDataManager.clearCache()
export const isUnifiedDataCacheStale = () =>
  unifiedProgramDataManager.isCacheStale()
