import { render } from '@testing-library/react'
import { StreakIcon } from '../StreakIcon'

describe('StreakIcon', () => {
  it('renders without errors', () => {
    const { container } = render(<StreakIcon />)
    const svg = container.querySelector('svg')
    expect(svg).toBeInTheDocument()
  })

  it('applies custom className', () => {
    const { container } = render(<StreakIcon className="text-red-500" />)
    const svg = container.querySelector('svg')
    expect(svg).toHaveClass('text-red-500')
  })

  it('renders with default size', () => {
    const { container } = render(<StreakIcon />)
    const svg = container.querySelector('svg')
    expect(svg).toHaveAttribute('width', '24')
    expect(svg).toHaveAttribute('height', '24')
  })

  it('renders with custom size', () => {
    const { container } = render(<StreakIcon size={32} />)
    const svg = container.querySelector('svg')
    expect(svg).toHaveAttribute('width', '32')
    expect(svg).toHaveAttribute('height', '32')
  })

  it('maintains SVG structure', () => {
    const { container } = render(<StreakIcon />)
    const paths = container.querySelectorAll('path')
    expect(paths).toHaveLength(2)
  })
})
