import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactNode } from 'react'
import {
  useProgramWithCache,
  useProgramProgressWithCache,
  useProgramDataWithCache,
  useProgramPrefetch,
} from '@/hooks/useProgramWithCache'
import { useProgramStore } from '@/stores/programStore'
import { useAuthStore } from '@/stores/authStore'
import * as programApiModule from '@/api/program'
import type { ProgramModel, ProgramProgress, ProgramStats } from '@/types'

// Mock the API module
vi.mock('@/api/program', () => ({
  programApi: {
    getUserProgram: vi.fn(),
    getProgramProgress: vi.fn(),
    getProgramStats: vi.fn(),
  },
}))

// Mock data
const mockProgram: ProgramModel = {
  id: 1,
  name: 'Test Program',
  description: 'Test Description',
  category: 'Strength',
  totalDays: 90,
  currentDay: 10,
  workoutsCompleted: 10,
  startDate: '2024-01-01T00:00:00Z',
  totalWorkouts: 36,
  imageUrl: 'https://example.com/image.jpg',
}

const mockProgress: ProgramProgress = {
  percentage: 28,
  daysCompleted: 10,
  totalWorkouts: 36,
  currentWeek: 4,
  workoutsThisWeek: 1,
  remainingWorkouts: 26,
}

const mockStats: ProgramStats = {
  averageWorkoutTime: 45.5,
  totalVolume: 50000,
  personalRecords: 5,
  consecutiveWeeks: 3,
  lastWorkoutDate: '2024-01-10T00:00:00Z',
  totalWorkoutsCompleted: 10,
}

// Create a wrapper component for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return function ({ children }: { children: ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

describe('useProgramWithCache', () => {
  beforeEach(() => {
    // Reset stores
    localStorage.clear()
    useProgramStore.setState({
      cachedData: {
        program: null,
        progress: null,
        stats: null,
        lastUpdated: {
          program: 0,
          progress: 0,
          stats: 0,
        },
      },
      hasHydrated: false,
    })
    useAuthStore.setState({ isAuthenticated: true })

    // Reset mocks
    vi.clearAllMocks()
  })

  it('should return cached data immediately when available', async () => {
    // Set up cached data
    act(() => {
      useProgramStore.getState().setCachedProgram(mockProgram)
      useProgramStore.getState().setHasHydrated(true)
    })

    const { result } = renderHook(() => useProgramWithCache(), {
      wrapper: createWrapper(),
    })

    // Should return cached data immediately
    expect(result.current.program).toEqual(mockProgram)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.hasInitialData).toBe(true)
  })

  it('should show loading state when no cache available', async () => {
    // No cached data, mark as hydrated
    act(() => {
      useProgramStore.getState().setHasHydrated(true)
    })

    const mockApi = programApiModule.programApi.getUserProgram as any
    mockApi.mockImplementation(() => new Promise(() => {})) // Never resolves

    const { result } = renderHook(() => useProgramWithCache(), {
      wrapper: createWrapper(),
    })

    expect(result.current.program).toBeNull()
    expect(result.current.isLoading).toBe(true)
    expect(result.current.hasInitialData).toBe(false)
  })

  it('should fetch fresh data in background when cache is stale', async () => {
    // Set up stale cache
    const twentyFiveHoursAgo = Date.now() - 25 * 60 * 60 * 1000
    act(() => {
      useProgramStore.setState({
        cachedData: {
          program: mockProgram,
          progress: null,
          stats: null,
          lastUpdated: {
            program: twentyFiveHoursAgo,
            progress: 0,
            stats: 0,
          },
        },
        hasHydrated: true,
      })
    })

    const freshProgram = { ...mockProgram, name: 'Updated Program' }
    const mockApi = programApiModule.programApi.getUserProgram as any
    mockApi.mockResolvedValue(freshProgram)

    const { result } = renderHook(() => useProgramWithCache(), {
      wrapper: createWrapper(),
    })

    // Should show cached data immediately
    expect(result.current.program).toEqual(mockProgram)
    expect(result.current.isLoading).toBe(false)
    expect(result.current.isRefreshing).toBe(true)

    // Wait for background refresh
    await waitFor(() => {
      expect(result.current.program).toEqual(freshProgram)
      expect(result.current.isRefreshing).toBe(false)
    })
  })

  it('should not fetch when cache is fresh', async () => {
    // Set up fresh cache
    act(() => {
      useProgramStore.getState().setCachedProgram(mockProgram)
      useProgramStore.getState().setHasHydrated(true)
    })

    const mockApi = programApiModule.programApi.getUserProgram as any

    renderHook(() => useProgramWithCache(), {
      wrapper: createWrapper(),
    })

    // Should not call API
    expect(mockApi).not.toHaveBeenCalled()
  })
})

describe('useProgramProgressWithCache', () => {
  beforeEach(() => {
    localStorage.clear()
    useProgramStore.setState({
      cachedData: {
        program: null,
        progress: null,
        stats: null,
        lastUpdated: {
          program: 0,
          progress: 0,
          stats: 0,
        },
      },
      hasHydrated: false,
    })
    useAuthStore.setState({ isAuthenticated: true })
    vi.clearAllMocks()
  })

  it('should handle progress cache with 1 hour TTL', async () => {
    // Set up cache that's 30 minutes old (still fresh)
    const thirtyMinutesAgo = Date.now() - 30 * 60 * 1000
    act(() => {
      useProgramStore.setState({
        cachedData: {
          program: null,
          progress: mockProgress,
          stats: null,
          lastUpdated: {
            program: 0,
            progress: thirtyMinutesAgo,
            stats: 0,
          },
        },
        hasHydrated: true,
      })
    })

    const mockApi = programApiModule.programApi.getProgramProgress as any

    const { result } = renderHook(() => useProgramProgressWithCache(), {
      wrapper: createWrapper(),
    })

    // Should use cached data and not call API
    expect(result.current.progress).toEqual(mockProgress)
    expect(result.current.isLoading).toBe(false)
    expect(mockApi).not.toHaveBeenCalled()
  })

  it('should refresh progress when cache is over 1 hour old', async () => {
    // Set up stale cache (2 hours old)
    const twoHoursAgo = Date.now() - 2 * 60 * 60 * 1000
    act(() => {
      useProgramStore.setState({
        cachedData: {
          program: null,
          progress: mockProgress,
          stats: null,
          lastUpdated: {
            program: 0,
            progress: twoHoursAgo,
            stats: 0,
          },
        },
        hasHydrated: true,
      })
    })

    const freshProgress = { ...mockProgress, percentage: 35 }
    const mockApi = programApiModule.programApi.getProgramProgress as any
    mockApi.mockResolvedValue(freshProgress)

    const { result } = renderHook(() => useProgramProgressWithCache(), {
      wrapper: createWrapper(),
    })

    // Should show stale data with refresh indicator
    expect(result.current.progress).toEqual(mockProgress)
    expect(result.current.isRefreshing).toBe(true)

    // Wait for fresh data
    await waitFor(() => {
      expect(result.current.progress).toEqual(freshProgress)
    })
  })
})

describe('useProgramDataWithCache', () => {
  beforeEach(() => {
    localStorage.clear()
    useProgramStore.setState({
      cachedData: {
        program: null,
        progress: null,
        stats: null,
        lastUpdated: {
          program: 0,
          progress: 0,
          stats: 0,
        },
      },
      hasHydrated: false,
    })
    useAuthStore.setState({ isAuthenticated: true })
    vi.clearAllMocks()
  })

  it('should combine all program data with cache support', async () => {
    // Set up mixed cache states
    const fresh = Date.now() - 30 * 60 * 1000 // 30 minutes ago
    const stale = Date.now() - 25 * 60 * 60 * 1000 // 25 hours ago

    act(() => {
      useProgramStore.setState({
        cachedData: {
          program: mockProgram,
          progress: mockProgress,
          stats: mockStats,
          lastUpdated: {
            program: fresh, // Fresh
            progress: stale, // Stale (> 1 hour)
            stats: fresh, // Fresh
          },
        },
        hasHydrated: true,
      })
    })

    const freshProgress = { ...mockProgress, percentage: 40 }
    const mockProgressApi = programApiModule.programApi
      .getProgramProgress as any
    mockProgressApi.mockResolvedValue(freshProgress)

    const { result } = renderHook(() => useProgramDataWithCache(), {
      wrapper: createWrapper(),
    })

    // Should return all cached data
    expect(result.current.program).toEqual(mockProgram)
    expect(result.current.progress).toEqual(mockProgress)
    expect(result.current.stats).toEqual(mockStats)
    expect(result.current.hasInitialData).toBe(true)

    // Only progress should be refreshing
    expect(result.current.isRefreshingProgram).toBe(false)
    expect(result.current.isRefreshingProgress).toBe(true)
    expect(result.current.isRefreshingStats).toBe(false)

    // Wait for progress update
    await waitFor(() => {
      expect(result.current.progress).toEqual(freshProgress)
    })
  })
})

describe('useProgramPrefetch', () => {
  beforeEach(() => {
    localStorage.clear()
    useProgramStore.setState({
      cachedData: {
        program: null,
        progress: null,
        stats: null,
        lastUpdated: {
          program: 0,
          progress: 0,
          stats: 0,
        },
      },
      hasHydrated: false,
    })
    vi.clearAllMocks()
  })

  it('should prefetch all program data and update cache', async () => {
    const mockProgramApi = programApiModule.programApi.getUserProgram as any
    const mockProgressApi = programApiModule.programApi
      .getProgramProgress as any
    const mockStatsApi = programApiModule.programApi.getProgramStats as any

    mockProgramApi.mockResolvedValue(mockProgram)
    mockProgressApi.mockResolvedValue(mockProgress)
    mockStatsApi.mockResolvedValue(mockStats)

    const { result } = renderHook(() => useProgramPrefetch())

    expect(result.current.isLoading).toBe(false)
    expect(result.current.progress).toBe(0)

    // Trigger prefetch
    let prefetchResult: boolean | undefined
    await act(async () => {
      prefetchResult = await result.current.prefetchProgramData()
    })

    expect(prefetchResult).toBe(true)
    expect(result.current.progress).toBe(100)
    expect(result.current.isLoading).toBe(false)

    // Check cache was updated
    const state = useProgramStore.getState()
    expect(state.getCachedProgram()).toEqual(mockProgram)
    expect(state.getCachedProgress()).toEqual(mockProgress)
    expect(state.getCachedStats()).toEqual(mockStats)
  })

  it('should handle partial failures gracefully', async () => {
    const mockProgramApi = programApiModule.programApi.getUserProgram as any
    const mockProgressApi = programApiModule.programApi
      .getProgramProgress as any
    const mockStatsApi = programApiModule.programApi.getProgramStats as any

    mockProgramApi.mockResolvedValue(mockProgram)
    mockProgressApi.mockRejectedValue(new Error('Network error'))
    mockStatsApi.mockResolvedValue(mockStats)

    const { result } = renderHook(() => useProgramPrefetch())

    let prefetchResult: boolean | undefined
    await act(async () => {
      prefetchResult = await result.current.prefetchProgramData()
    })

    // Should return false due to failure
    expect(prefetchResult).toBe(false)

    // But should still cache successful data
    const state = useProgramStore.getState()
    expect(state.getCachedProgram()).toEqual(mockProgram)
    expect(state.getCachedProgress()).toBeNull()
    expect(state.getCachedStats()).toEqual(mockStats)
  })
})
