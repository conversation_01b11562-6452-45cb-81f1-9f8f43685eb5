/**
 * Dr. Muscle X Design System Types
 */

export type ThemeVariant =
  | 'subtle-depth'
  | 'flat-bold'
  | 'glassmorphism'
  | 'ultra-minimal'

export interface Theme {
  name: ThemeVariant
  colors: ThemeColors
  typography: ThemeTypography
  spacing: ThemeSpacing
  shadows: ThemeShadows
  animations: ThemeAnimations
  borders: ThemeBorders
  gradients?: ThemeGradients
}

export interface ThemeColors {
  // Background colors
  background: {
    primary: string
    secondary: string
    tertiary: string
    overlay: string
  }
  // Text colors
  text: {
    primary: string
    secondary: string
    tertiary: string
    inverse: string
  }
  // Brand colors
  brand: {
    primary: string
    secondary: string
    accent: string
  }
  // Status colors
  status: {
    success: string
    error: string
    warning: string
    info: string
  }
  // Interactive states
  interactive: {
    hover: string
    active: string
    disabled: string
  }
}

export interface ThemeTypography {
  fonts: {
    heading: string
    body: string
    mono: string
  }
  sizes: {
    xs: string
    sm: string
    base: string
    lg: string
    xl: string
    '2xl': string
    '3xl': string
    '4xl': string
  }
  weights: {
    light: number
    regular: number
    medium: number
    semibold: number
    bold: number
  }
  lineHeights: {
    tight: number
    normal: number
    relaxed: number
  }
  letterSpacing?: {
    tight: string
    normal: string
    wide: string
    wider: string
    widest: string
  }
  textShadows?: {
    sm: string
    md: string
    lg: string
    gold: string
  }
}

export interface ThemeSpacing {
  unit: number // Base unit in pixels
  xs: string
  sm: string
  md: string
  lg: string
  xl: string
  '2xl': string
  '3xl': string
  '4xl': string
}

export interface ThemeShadows {
  none: string
  sm: string
  md: string
  lg: string
  xl: string
  inner: string
}

export interface ThemeAnimations {
  duration: {
    fast: string
    normal: string
    slow: string
  }
  easing: {
    default: string
    smooth: string
    spring: string
    luxury?: string
  }
  keyframes?: {
    shimmer?: string
  }
}

export interface ThemeBorders {
  radius: {
    none: string
    sm: string
    md: string
    lg: string
    full: string
  }
  width: {
    hairline: string
    thin: string
    medium: string
    thick: string
  }
}

export interface DesignTokens {
  base: BaseTokens
  themes: Record<ThemeVariant, Theme>
}

export interface ThemeGradients {
  backgroundPremium: string
  metallicGold: string
  metallicSilver: string
  overlaySubtle: string
  overlayPremium: string
  shimmer: string
}

export interface BaseTokens {
  spacing: ThemeSpacing
  breakpoints: {
    sm: string
    md: string
    lg: string
    xl: string
  }
  touchTargets: {
    minimum: string
    comfortable: string
    large: string
  }
}
