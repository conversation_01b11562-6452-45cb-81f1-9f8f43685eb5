# Program Page Data Analysis

## Current Data Flow

The program page (`/src/app/program/page.tsx`) currently fetches and uses the following data:

### 1. Program Information (via `useProgramWithCalculationsAndCache`)

- **Source**: `programApi.getUserProgram()`
- **Data Used**:
  - `program.name` - Displayed in CompletedProgramState
  - `program.description` - Displayed in ProgramDescription component (lines 389-395)
  - `program.id` - Used for validation
  - Progress calculations rely on program data

### 2. Program Progress (via `useProgramWithCalculationsAndCache`)

- **Source**: `programApi.getProgramProgress()`
- **Data Used**:
  - `progress.percentage` - Used to check if program is completed (line 249)
  - Various progress metrics for calculations

### 3. Program Stats (via `useProgramWithCalculationsAndCache`)

- **Source**: `programApi.getProgramStats()`
- **Data Used**:
  - Stats are converted to display format in CompletedProgramState (lines 251-263)
  - Not directly displayed on main page

### 4. User Stats (via `useUserStats`)

- **Source**: Separate user stats API
- **Data Used**:
  - `stats.weekStreak` - Displayed in StatCard (lines 353-360)
  - `stats.workoutsCompleted` - Displayed in StatCard (lines 363-371)
  - `stats.lbsLifted` - Displayed in StatCard (lines 374-385)

## What Would Break Without Prefetching

### If we remove program information prefetch:

1. **No immediate breakage** - The page has loading states
2. **User Experience Impact**:
   - Page would show loading state on initial render
   - Data would be fetched when page loads (slight delay)
   - Special states (NoProgramState, CompletedProgramState) would only appear after data loads

### If we remove program progress prefetch:

1. **No immediate breakage** - Progress is used for calculations
2. **User Experience Impact**:
   - Cannot determine if program is completed until data loads
   - Progress-based UI decisions delayed

### If we remove program stats prefetch:

1. **No immediate breakage** - Stats are only used in CompletedProgramState
2. **Minimal Impact** - Only affects completed program view

### If we keep only user stats prefetch:

1. **StatCards would load immediately** (primary visible content)
2. **Program description would load after** (secondary content)
3. **Special states would be determined after data loads**

## Critical Observations

1. **The page already handles loading states gracefully** (line 270 comment: "Always render the main page structure immediately")
2. **User stats are the most visible data** - displayed prominently in 3 stat cards
3. **Program data is mainly used for**:
   - Validation (null checks)
   - Special state determination (completed, no program)
   - Description display (which can load later)

## Recommendation

**It appears safe to remove all prefetching except user stats** because:

1. The page is designed to handle progressive loading
2. User stats are the primary visible content
3. All components have proper loading states
4. The page structure renders immediately regardless of data availability
5. Special states (completed program, no program) can be determined after initial render

The main trade-off is a slightly delayed display of:

- Program description
- Determination of special states (completed/no program)

But the core user experience (seeing their stats) would remain fast.
