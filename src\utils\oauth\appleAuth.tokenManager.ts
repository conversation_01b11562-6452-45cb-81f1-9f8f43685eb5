/**
 * Apple OAuth Token Manager
 * Handles token parsing, validation, and storage
 */

import type { AppleTokenPayload, OAuthUserData } from '../../types/oauth'

export class AppleAuthTokenManager {
  /**
   * Parse boolean value from various formats
   */
  private static parseBoolean(value: boolean | string | undefined): boolean {
    if (typeof value === 'boolean') return value
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true' || value === '1'
    }
    return false
  }

  /**
   * Decode JWT token without verification
   * Note: This is for client-side use only. Server should verify the token.
   */
  static decodeJWT(token: string): AppleTokenPayload | null {
    try {
      const parts = token.split('.')
      if (parts.length !== 3) {
        console.error('[Apple OAuth] Invalid JWT format')
        return null
      }

      const payload = parts[1]
      if (!payload) {
        console.error('[Apple OAuth] No payload in JWT')
        return null
      }
      const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'))
      const data = JSON.parse(decoded)

      // Validate required fields
      if (!data.sub || !data.aud || !data.iat || !data.exp) {
        console.error('[Apple OAuth] JWT missing required fields:', data)
        return null
      }

      return {
        iss: data.iss,
        aud: data.aud,
        exp: data.exp,
        iat: data.iat,
        sub: data.sub, // User ID
        email: data.email,
        email_verified: data.email_verified,
        is_private_email: data.is_private_email,
        auth_time: data.auth_time,
        nonce: data.nonce,
        real_user_status: data.real_user_status,
      }
    } catch (error) {
      console.error('[Apple OAuth] Failed to decode JWT:', error)
      return null
    }
  }

  /**
   * Store user data in session storage
   */
  static storeUserData(
    userId: string,
    data: {
      firstName?: string
      lastName?: string
      email?: string
    }
  ): void {
    try {
      sessionStorage.setItem(
        `apple_user_${userId}`,
        JSON.stringify({
          ...data,
          timestamp: Date.now(),
        })
      )
    } catch (error) {
      console.error('[Apple OAuth] Failed to store user data:', error)
    }
  }

  /**
   * Get stored user data from session storage
   */
  static getStoredUserData(userId: string): {
    firstName?: string
    lastName?: string
    email?: string
  } | null {
    try {
      const stored = sessionStorage.getItem(`apple_user_${userId}`)
      if (stored) {
        const data = JSON.parse(stored)
        // Check if data is not older than 24 hours
        if (Date.now() - data.timestamp < 24 * 60 * 60 * 1000) {
          return data
        }
      }
    } catch (error) {
      console.error('[Apple OAuth] Failed to retrieve stored user data:', error)
    }
    return null
  }

  /**
   * Extract user data from Apple Sign-In response
   */
  static extractUserData(
    response: AppleSignInResponse,
    tokenPayload: AppleTokenPayload | null
  ): OAuthUserData {
    console.error('[Apple OAuth] Extracting user data from response:', {
      hasUser: !!response.user,
      hasIdToken: !!response.id_token,
      hasAuthCode: !!response.code,
      tokenPayloadEmail: tokenPayload?.email,
      userEmail: response.user?.email,
    })

    const userId = tokenPayload?.sub || ''
    let userEmail = tokenPayload?.email || response.user?.email || ''
    let firstName = response.user?.name?.firstName || ''
    let lastName = response.user?.name?.lastName || ''

    // If we don't have user data in response, check session storage
    if (!firstName && !lastName && userId) {
      const storedData = this.getStoredUserData(userId)
      if (storedData) {
        console.error('[Apple OAuth] Using stored user data for user:', userId)
        firstName = storedData.firstName || ''
        lastName = storedData.lastName || ''
        userEmail = userEmail || storedData.email || ''
      }
    }

    // Store user data if we have it
    if ((firstName || lastName || userEmail) && userId) {
      this.storeUserData(userId, {
        firstName,
        lastName,
        email: userEmail,
      })
    }

    const userData: OAuthUserData = {
      provider: 'apple',
      providerId: userId,
      email: userEmail,
      emailVerified: this.parseBoolean(tokenPayload?.email_verified) || false,
      name: [firstName, lastName].filter(Boolean).join(' ') || userEmail,
      firstName,
      lastName,
      rawToken: response.id_token || '',
      tokenPayload: tokenPayload || ({} as AppleTokenPayload),
      metadata: {
        authorizationCode: response.code || '',
        isPrivateEmail: tokenPayload?.is_private_email || false,
        raw: response,
      },
    }

    console.error('[Apple OAuth] Extracted user data:', {
      email: userData.email,
      name: userData.name,
      providerId: userData.providerId,
      hasToken: !!userData.rawToken,
      hasAuthCode: !!userData.metadata?.authorizationCode,
    })

    return userData
  }
}

// Custom type for Apple Sign-In Response with additional fields
type AppleSignInResponse = {
  authorization?: {
    code: string
    id_token: string
    state?: string
  }
  id_token?: string
  code?: string
  state?: string
  user?: {
    email?: string
    name?: {
      firstName?: string
      lastName?: string
    }
  }
}
