/**
 * Debug script to test exercise recommendation API
 * Run this in browser console after logging in
 */

async function testRecommendationAPI() {
  console.log('🔍 Testing Exercise Recommendation API...')
  
  // Test credentials
  const testEmail = '<EMAIL>'
  const testPassword = 'Dr123456'
  
  try {
    // First, let's login
    console.log('🔐 Logging in...')
    const loginResponse = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword
      })
    })
    
    if (!loginResponse.ok) {
      console.error('❌ Login failed:', await loginResponse.text())
      return
    }
    
    console.log('✅ Login successful')
    
    // Get user program info to get a workout ID
    console.log('📋 Getting user program info...')
    const programResponse = await fetch('/api/workouts/user-program-info', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (!programResponse.ok) {
      console.error('❌ Failed to get program info:', await programResponse.text())
      return
    }
    
    const programData = await programResponse.json()
    console.log('📋 Program data:', programData)
    
    const workoutId = programData?.Result?.NextWorkoutTemplate?.Id || 123
    const exercises = programData?.Result?.NextWorkoutTemplate?.Exercises || []
    
    if (exercises.length === 0) {
      console.error('❌ No exercises found in workout')
      return
    }
    
    const firstExercise = exercises[0]
    console.log('🏋️ Testing with first exercise:', firstExercise)
    
    // Test recommendation API call
    console.log('🎯 Testing recommendation API...')
    
    const requestBody = {
      Username: testEmail.replace(/\s+/g, '').toLowerCase(),
      ExerciseId: firstExercise.Id,
      WorkoutId: workoutId,
      SetStyle: firstExercise.SetStyle || 'Normal',
      IsFlexibility: firstExercise.IsFlexibility || false,
      IsQuickMode: null,
      LightSessionDays: null,
      SwapedExId: null,
      IsStrengthPhashe: false, // Note: API has typo
      IsFreePlan: false,
      IsFirstWorkoutOfStrengthPhase: false,
      VersionNo: 1,
    }
    
    console.log('📤 Request body:', JSON.stringify(requestBody, null, 2))
    
    // Determine endpoint
    const setStyle = (firstExercise.SetStyle || 'Normal').toLowerCase()
    const isRestPause = setStyle === 'restpause' || setStyle === 'rest-pause'
    const shouldUseNormal = firstExercise.IsFlexibility || !isRestPause
    
    const endpoint = shouldUseNormal
      ? '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'
      : '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew'
    
    console.log(`🚀 Using endpoint: ${endpoint}`)
    
    const recommendationResponse = await fetch(`https://drmuscle.azurewebsites.net${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('authToken') || 'NO_TOKEN'}`
      },
      body: JSON.stringify(requestBody)
    })
    
    console.log('📥 Response status:', recommendationResponse.status)
    console.log('📥 Response headers:', Object.fromEntries(recommendationResponse.headers.entries()))
    
    if (!recommendationResponse.ok) {
      const errorText = await recommendationResponse.text()
      console.error('❌ Recommendation API failed:', errorText)
      return
    }
    
    const recommendationData = await recommendationResponse.json()
    console.log('📥 Recommendation response:', recommendationData)
    
    // Check if we got weight and reps
    const recommendation = recommendationData?.Result || recommendationData
    if (recommendation) {
      console.log('🏋️ Weight:', recommendation.Weight)
      console.log('🔢 Reps:', recommendation.Reps)
      console.log('📊 Sets:', recommendation.Series)
      
      if (recommendation.Weight && recommendation.Reps) {
        console.log('✅ SUCCESS: Got weight and reps!')
      } else {
        console.log('❌ ISSUE: Missing weight or reps in response')
      }
    } else {
      console.log('❌ ISSUE: No recommendation data in response')
    }
    
  } catch (error) {
    console.error('❌ Error during test:', error)
  }
}

// Run the test
testRecommendationAPI()
