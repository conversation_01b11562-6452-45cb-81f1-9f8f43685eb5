<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug App State</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
        .info { background: #e3f2fd; color: #1565c0; }
        button { padding: 10px 20px; margin: 5px; background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #1976d2; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>DrMuscle App State Debug</h1>
    
    <div class="section">
        <h2>Authentication State</h2>
        <button onclick="checkAuthState()">Check Auth State</button>
        <button onclick="checkCookies()">Check Cookies</button>
        <div id="auth-logs"></div>
    </div>

    <div class="section">
        <h2>Local Storage</h2>
        <button onclick="checkLocalStorage()">Check Local Storage</button>
        <div id="storage-logs"></div>
    </div>

    <div class="section">
        <h2>API Test</h2>
        <button onclick="testAuthToken()">Test Auth Token</button>
        <button onclick="testWorkoutAPI()">Test Workout API</button>
        <div id="api-logs"></div>
    </div>

    <div class="section">
        <h2>Console Logs</h2>
        <button onclick="clearLogs()">Clear All Logs</button>
        <div id="console-logs"></div>
    </div>

    <script>
        function log(message, type = 'info', containerId = 'console-logs') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('auth-logs').innerHTML = '';
            document.getElementById('storage-logs').innerHTML = '';
            document.getElementById('api-logs').innerHTML = '';
            document.getElementById('console-logs').innerHTML = '';
        }

        async function checkAuthState() {
            log('🔍 Checking authentication state...', 'info', 'auth-logs');
            
            try {
                // Check if user is logged in via Next.js app
                const response = await fetch('/api/auth/token', {
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.authenticated && data.token) {
                        log(`✅ User is authenticated. Token: ${data.token.substring(0, 20)}...`, 'success', 'auth-logs');
                    } else {
                        log('❌ User is not authenticated', 'error', 'auth-logs');
                    }
                } else {
                    log(`❌ Auth check failed: ${response.status}`, 'error', 'auth-logs');
                }
            } catch (error) {
                log(`❌ Auth check error: ${error.message}`, 'error', 'auth-logs');
            }
        }

        async function checkCookies() {
            log('🍪 Checking cookies...', 'info', 'auth-logs');
            
            const cookies = document.cookie.split(';').map(c => c.trim());
            if (cookies.length === 0 || (cookies.length === 1 && cookies[0] === '')) {
                log('❌ No cookies found', 'error', 'auth-logs');
            } else {
                log(`📋 Found ${cookies.length} cookies:`, 'info', 'auth-logs');
                cookies.forEach(cookie => {
                    const [name] = cookie.split('=');
                    log(`   - ${name}`, 'info', 'auth-logs');
                });
            }
        }

        async function checkLocalStorage() {
            log('💾 Checking local storage...', 'info', 'storage-logs');
            
            try {
                const keys = Object.keys(localStorage);
                if (keys.length === 0) {
                    log('❌ No local storage items found', 'error', 'storage-logs');
                } else {
                    log(`📋 Found ${keys.length} local storage items:`, 'info', 'storage-logs');
                    keys.forEach(key => {
                        try {
                            const value = localStorage.getItem(key);
                            if (key.includes('auth') || key.includes('workout')) {
                                log(`   - ${key}: ${value ? value.substring(0, 100) + '...' : 'null'}`, 'info', 'storage-logs');
                            } else {
                                log(`   - ${key}`, 'info', 'storage-logs');
                            }
                        } catch (e) {
                            log(`   - ${key}: [Error reading value]`, 'error', 'storage-logs');
                        }
                    });
                }
            } catch (error) {
                log(`❌ Local storage error: ${error.message}`, 'error', 'storage-logs');
            }
        }

        async function testAuthToken() {
            log('🔑 Testing auth token...', 'info', 'api-logs');
            
            try {
                const response = await fetch('/api/auth/token', {
                    credentials: 'include'
                });
                
                const data = await response.json();
                log(`📤 Auth token response: ${JSON.stringify(data, null, 2)}`, 'info', 'api-logs');
                
                if (data.token) {
                    // Test if token works with Dr. Muscle API
                    const testResponse = await fetch('https://drmuscle.azurewebsites.net/api/Workout/GetUserWorkoutProgramTimeZoneInfo', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${data.token}`
                        },
                        body: JSON.stringify({
                            Username: '<EMAIL>',
                            TimeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
                        })
                    });
                    
                    if (testResponse.ok) {
                        log('✅ Token works with Dr. Muscle API', 'success', 'api-logs');
                    } else {
                        log(`❌ Token failed with Dr. Muscle API: ${testResponse.status}`, 'error', 'api-logs');
                    }
                }
            } catch (error) {
                log(`❌ Auth token test error: ${error.message}`, 'error', 'api-logs');
            }
        }

        async function testWorkoutAPI() {
            log('🏋️ Testing workout API...', 'info', 'api-logs');
            
            try {
                // Test the Next.js API route
                const response = await fetch('/api/workouts/today', {
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Workout API works. Data: ${JSON.stringify(data).substring(0, 200)}...`, 'success', 'api-logs');
                } else {
                    log(`❌ Workout API failed: ${response.status}`, 'error', 'api-logs');
                    const text = await response.text();
                    log(`   Response: ${text}`, 'error', 'api-logs');
                }
            } catch (error) {
                log(`❌ Workout API error: ${error.message}`, 'error', 'api-logs');
            }
        }

        // Auto-run basic checks on page load
        window.onload = () => {
            log('🚀 Debug page loaded. Running basic checks...', 'info');
            setTimeout(() => {
                checkAuthState();
                checkLocalStorage();
            }, 1000);
        };

        // Capture console errors
        const originalError = console.error;
        console.error = function(...args) {
            log(`🚨 Console Error: ${args.join(' ')}`, 'error');
            originalError.apply(console, args);
        };

        // Capture console warnings
        const originalWarn = console.warn;
        console.warn = function(...args) {
            log(`⚠️ Console Warning: ${args.join(' ')}`, 'info');
            originalWarn.apply(console, args);
        };
    </script>
</body>
</html>
