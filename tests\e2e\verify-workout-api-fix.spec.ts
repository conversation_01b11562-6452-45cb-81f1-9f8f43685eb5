import { test, expect } from '@playwright/test'

test.describe('Verify Workout API Fix', () => {
  test('should handle nested result.Workouts array correctly', async ({
    page,
  }) => {
    const consoleLogs: Array<{ type: string; text: string }> = []

    // Capture all console messages
    page.on('console', (msg) => {
      consoleLogs.push({
        type: msg.type(),
        text: msg.text(),
      })
    })

    // Navigate directly to login
    await page.goto('/login')

    // Fill in the login form using the test credentials
    await page.locator('#email').fill('<EMAIL>')
    await page.locator('#password').fill('Dr123456')

    // Submit the form
    await page.locator('button[type="submit"]').click()

    // Wait for either navigation or error
    try {
      await page.waitForURL('**/program', { timeout: 15000 })
      console.log('Successfully navigated to program page')
    } catch (e) {
      console.log('Failed to navigate to program page')
      // Check if we're stuck on login
      const currentUrl = page.url()
      console.log('Current URL:', currentUrl)

      // Log any errors
      const errorLogs = consoleLogs.filter((log) => log.type === 'error')
      console.log('Console errors:', errorLogs)

      throw e
    }

    // Look for the Continue to workout button
    const continueButton = page.locator(
      'button:has-text("Continue to workout")'
    )
    await expect(continueButton).toBeVisible({ timeout: 10000 })

    // Click the button
    await continueButton.click()

    // Wait for navigation to workout page
    await page.waitForURL('**/workout', { timeout: 10000 })
    console.log('Successfully navigated to workout page')

    // Wait a bit for API calls to complete
    await page.waitForTimeout(5000)

    // Check console logs for our specific error
    const errorLogs = consoleLogs.filter(
      (log) =>
        log.type === 'error' &&
        log.text.includes('GetUserWorkout: Result is not an array')
    )

    console.log('Total console logs:', consoleLogs.length)
    console.log('Error logs with GetUserWorkout:', errorLogs.length)

    if (errorLogs.length > 0) {
      console.log('Found GetUserWorkout errors:', errorLogs)
    }

    // Also check for success logs
    const successLogs = consoleLogs.filter((log) =>
      log.text.includes('GetUserWorkout: Found nested Workouts array')
    )

    if (successLogs.length > 0) {
      console.log('Found success log - API response handled correctly!')
    }

    // Verify no GetUserWorkout errors
    expect(errorLogs.length).toBe(0)

    // Check if exercises are displayed
    const exercisesText = await page
      .locator('text=/\\d+ exercises?/')
      .first()
      .textContent()
    console.log('Exercises text:', exercisesText)

    if (exercisesText) {
      const count = parseInt(exercisesText.match(/(\d+)/)?.[1] || '0')
      console.log('Exercise count:', count)
      expect(count).toBeGreaterThan(0)
    }
  })
})
