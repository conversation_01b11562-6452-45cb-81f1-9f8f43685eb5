import { test, expect } from '@playwright/test'

test.describe('Workout Cache Fix - Second Login', () => {
  test('should clear workout cache on logout to prevent blank page on second login', async ({
    page,
  }) => {
    // First login
    await page.goto('/login')
    await page.fill('[name="username"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('/program')

    // Navigate to workout page
    await page.click('button:has-text("Continue to Workout")')
    await page.waitForURL('/workout')

    // Verify workout page loads correctly
    await expect(page.locator('h1:has-text("Today\'s Workout")')).toBeVisible()

    // Logout
    await page.click('[data-testid="user-menu-button"]')
    await page.click('button:has-text("Logout")')

    // Wait for redirect to login page
    await page.waitForURL('/login')

    // Second login (same or different user)
    await page.fill('[name="username"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('/program')

    // Navigate to workout page again
    await page.click('button:has-text("Continue to Workout")')
    await page.waitForURL('/workout')

    // IMPORTANT: Verify workout page loads correctly without blank page
    await expect(page.locator('h1:has-text("Today\'s Workout")')).toBeVisible({
      timeout: 5000,
    })

    // Verify no "No workout" message appears
    await expect(
      page.locator('text="No workout scheduled for today"')
    ).not.toBeVisible()
  })

  test('should properly handle cache clearing between different users', async ({
    page,
  }) => {
    // First user login
    await page.goto('/login')
    await page.fill('[name="username"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('[type="submit"]')

    await page.waitForURL('/program')

    // Navigate to workout to ensure data is loaded
    await page.click('button:has-text("Continue to Workout")')
    await page.waitForURL('/workout')
    await expect(page.locator('h1:has-text("Today\'s Workout")')).toBeVisible()

    // Go back to program page to access user menu
    await page.goto('/program')

    // Logout
    await page.click('[data-testid="user-menu-button"]')
    await page.click('button:has-text("Logout")')
    await page.waitForURL('/login')

    // Second user login (if available, otherwise same user)
    await page.fill('[name="username"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('[type="submit"]')

    await page.waitForURL('/program')

    // Navigate to workout
    await page.click('button:has-text("Continue to Workout")')
    await page.waitForURL('/workout')

    // Verify workout loads without issues
    await expect(page.locator('h1:has-text("Today\'s Workout")')).toBeVisible()

    // Verify the workout data is fresh (not from previous session)
    const exercises = await page
      .locator('[data-testid^="exercise-card-"]')
      .count()
    expect(exercises).toBeGreaterThan(0)
  })
})
