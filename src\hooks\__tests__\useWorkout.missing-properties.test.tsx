import { renderHook } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useWorkout } from '../useWorkout'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import type { ReactNode } from 'react'

// Mock all the sub-hooks
vi.mock('../useWorkoutState')
vi.mock('../useWorkoutActions')
vi.mock('../useWorkoutSync')
vi.mock('../useWorkoutDataLoader')
vi.mock('../useWorkoutRecommendations')
vi.mock('@/stores/workoutStore/index')
vi.mock('@/stores/authStore')

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return function ({ children }: { children: ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

describe('useWorkout - Missing Properties', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should expose finishWorkout function for WorkoutComplete component', () => {
    const { result } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    expect(result.current).toHaveProperty('finishWorkout')
    expect(typeof result.current.finishWorkout).toBe('function')
  })

  it('should expose isLoadingWorkout for WorkoutOverview component', () => {
    const { result } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    expect(result.current).toHaveProperty('isLoadingWorkout')
    expect(typeof result.current.isLoadingWorkout).toBe('boolean')
  })

  it('should expose workoutError for error handling', () => {
    const { result } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    expect(result.current).toHaveProperty('workoutError')
  })

  it('should expose goToNextExercise function for WorkoutScreen', () => {
    const { result } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    expect(result.current).toHaveProperty('goToNextExercise')
    expect(typeof result.current.goToNextExercise).toBe('function')
  })

  it('should expose getRecommendation function', () => {
    const { result } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    expect(result.current).toHaveProperty('getRecommendation')
    expect(typeof result.current.getRecommendation).toBe('function')
  })

  it('should expose loadAllExerciseRecommendations function', () => {
    const { result } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    expect(result.current).toHaveProperty('loadAllExerciseRecommendations')
    expect(typeof result.current.loadAllExerciseRecommendations).toBe(
      'function'
    )
  })

  it('should expose all required properties for WorkoutOverview', () => {
    const { result } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    const requiredProps = [
      'exerciseWorkSetsModels',
      'loadingStates',
      'expectedExerciseCount',
      'hasInitialData',
      'isLoadingFresh',
      'refreshWorkout',
      'updateExerciseWorkSets',
    ]

    requiredProps.forEach((prop) => {
      expect(result.current).toHaveProperty(prop)
    })
  })
})
