import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { OfflineQueue } from '@/services/offlineQueue'
import { apiClient } from '@/api/client'
import { waitFor } from '@testing-library/react'

// Mock dependencies
vi.mock('@/api/client')

describe('OfflineQueue', () => {
  let offlineQueue: OfflineQueue
  const mockLocalStorage = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    global.localStorage = mockLocalStorage as any
    offlineQueue = OfflineQueue.getInstance()

    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true,
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Queue Management', () => {
    it('should add requests to queue when offline', () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: false })

      const request = {
        url: '/api/Set/SaveUserSet',
        method: 'POST' as const,
        data: { exerciseId: 123, weight: 100, reps: 10 },
      }

      // When
      const queueId = offlineQueue.addToQueue(request)

      // Then
      expect(queueId).toBeDefined()
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'offline_queue',
        expect.stringContaining('SaveUserSet')
      )
    })

    it('should execute request immediately when online', async () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: true })
      vi.mocked(apiClient.request).mockResolvedValue({
        data: { success: true },
      })

      const request = {
        url: '/api/Set/SaveUserSet',
        method: 'POST' as const,
        data: { exerciseId: 123, weight: 100, reps: 10 },
      }

      // When
      await offlineQueue.addToQueue(request)

      // Then
      expect(apiClient.request).toHaveBeenCalledWith(request)
      expect(mockLocalStorage.setItem).not.toHaveBeenCalled()
    })

    it('should limit queue size to prevent storage overflow', () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: false })
      const maxQueueSize = 100

      // When - add more than max items
      for (let i = 0; i < maxQueueSize + 10; i++) {
        offlineQueue.addToQueue({
          url: `/api/test/${i}`,
          method: 'POST' as const,
          data: { id: i },
        })
      }

      // Then
      const lastCall = mockLocalStorage.setItem.mock.calls.slice(-1)[0]
      const queue = lastCall ? JSON.parse(lastCall[1]) : []
      expect(queue.length).toBeLessThanOrEqual(maxQueueSize)
    })

    it('should preserve request order in queue', () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: false })

      // When
      offlineQueue.addToQueue({ url: '/api/1', method: 'POST' as const })
      offlineQueue.addToQueue({ url: '/api/2', method: 'POST' as const })
      offlineQueue.addToQueue({ url: '/api/3', method: 'POST' as const })

      // Then
      const lastCall = mockLocalStorage.setItem.mock.calls.slice(-1)[0]
      const queue = lastCall ? JSON.parse(lastCall[1]) : []
      expect(queue[0].url).toBe('/api/1')
      expect(queue[1].url).toBe('/api/2')
      expect(queue[2].url).toBe('/api/3')
    })
  })

  describe('Queue Processing', () => {
    it('should process queue when coming online', async () => {
      // Given
      const queuedRequests = [
        { id: '1', url: '/api/1', method: 'POST', timestamp: Date.now() },
        { id: '2', url: '/api/2', method: 'POST', timestamp: Date.now() },
      ]

      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(queuedRequests))
      vi.mocked(apiClient.request).mockResolvedValue({
        data: { success: true },
      })

      // When
      await offlineQueue.processQueue()

      // Then
      expect(apiClient.request).toHaveBeenCalledTimes(2)
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'offline_queue',
        '[]'
      )
    })

    it('should handle partial queue processing on failures', async () => {
      // Given
      const queuedRequests = [
        { id: '1', url: '/api/1', method: 'POST', timestamp: Date.now() },
        { id: '2', url: '/api/2', method: 'POST', timestamp: Date.now() },
        { id: '3', url: '/api/3', method: 'POST', timestamp: Date.now() },
      ]

      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(queuedRequests))

      vi.mocked(apiClient.request)
        .mockResolvedValueOnce({ data: { success: true } })
        .mockRejectedValueOnce(new Error('Failed'))
        .mockResolvedValueOnce({ data: { success: true } })

      // When
      await offlineQueue.processQueue()

      // Then
      expect(apiClient.request).toHaveBeenCalledTimes(3)

      // Check that failed request remains in queue
      const lastCall = mockLocalStorage.setItem.mock.calls.slice(-1)[0]
      const remainingQueue = lastCall ? JSON.parse(lastCall[1]) : []
      expect(remainingQueue).toHaveLength(1)
      expect(remainingQueue[0].id).toBe('2')
    })

    it('should expire old requests from queue', async () => {
      // Given
      const oldTimestamp = Date.now() - 7 * 24 * 60 * 60 * 1000 - 1 // 7 days + 1ms
      const queuedRequests = [
        { id: '1', url: '/api/1', method: 'POST', timestamp: oldTimestamp },
        { id: '2', url: '/api/2', method: 'POST', timestamp: Date.now() },
      ]

      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(queuedRequests))
      vi.mocked(apiClient.request).mockResolvedValue({
        data: { success: true },
      })

      // When
      await offlineQueue.processQueue()

      // Then
      expect(apiClient.request).toHaveBeenCalledTimes(1) // Only new request
      expect(apiClient.request).toHaveBeenCalledWith(
        expect.objectContaining({ url: '/api/2' })
      )
    })

    it('should handle concurrent queue processing', async () => {
      // Given
      const queuedRequests = [
        { id: '1', url: '/api/1', method: 'POST', timestamp: Date.now() },
      ]

      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(queuedRequests))

      // Simulate slow request
      let resolveRequest: any
      vi.mocked(apiClient.request).mockImplementation(
        () =>
          new Promise((resolve) => {
            resolveRequest = resolve
          })
      )

      // When - start multiple processes
      const process1 = offlineQueue.processQueue()
      const process2 = offlineQueue.processQueue()

      // Then - second process should exit early
      expect(offlineQueue.isProcessing).toBe(true)

      // Complete the request
      resolveRequest({ data: { success: true } })
      await process1
      await process2

      // Verify only one request was made
      expect(apiClient.request).toHaveBeenCalledTimes(1)
    })
  })

  describe('Workout-Specific Operations', () => {
    it('should prioritize workout save operations', async () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: false })

      // Add various requests
      offlineQueue.addToQueue({ url: '/api/other', method: 'GET' as const })
      offlineQueue.addToQueue({
        url: '/api/Workout/CompleteWorkout',
        method: 'POST' as const,
        data: { workoutId: 'abc' },
      })
      offlineQueue.addToQueue({ url: '/api/another', method: 'GET' as const })

      // When
      const lastCall = mockLocalStorage.setItem.mock.calls.slice(-1)[0]
      const queue = lastCall ? JSON.parse(lastCall[1]) : []

      // Then - workout operations should be first
      expect(queue[0].url).toBe('/api/Workout/CompleteWorkout')
    })

    it('should group set saves by exercise', () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: false })

      // Add multiple sets for same exercise
      offlineQueue.addToQueue({
        url: '/api/Set/SaveUserSet',
        method: 'POST' as const,
        data: { exerciseId: 123, setNumber: 1, weight: 100, reps: 10 },
      })

      offlineQueue.addToQueue({
        url: '/api/Set/SaveUserSet',
        method: 'POST' as const,
        data: { exerciseId: 123, setNumber: 2, weight: 100, reps: 8 },
      })

      // When
      const lastCall = mockLocalStorage.setItem.mock.calls.slice(-1)[0]
      const queue = lastCall ? JSON.parse(lastCall[1]) : []

      // Then - should be grouped or marked for batch processing
      expect(queue).toHaveLength(2)
      expect(queue[0].data.exerciseId).toBe(123)
      expect(queue[1].data.exerciseId).toBe(123)
    })
  })

  describe('Event Handling', () => {
    it('should auto-process queue when coming online', async () => {
      // Given
      const processQueueSpy = vi.spyOn(offlineQueue, 'processQueue')
      Object.defineProperty(navigator, 'onLine', { value: false })

      // When - simulate coming online
      Object.defineProperty(navigator, 'onLine', { value: true })
      window.dispatchEvent(new Event('online'))

      // Then
      await waitFor(() => {
        expect(processQueueSpy).toHaveBeenCalled()
      })
    })

    it('should emit events for queue status changes', () => {
      // Given
      const onQueueChange = vi.fn()
      offlineQueue.addEventListener('queuechange', onQueueChange)

      Object.defineProperty(navigator, 'onLine', { value: false })

      // When
      offlineQueue.addToQueue({ url: '/api/test', method: 'POST' as const })

      // Then
      expect(onQueueChange).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: { queueLength: 1 },
        })
      )
    })
  })

  describe('Error Recovery', () => {
    it('should handle corrupted localStorage data', async () => {
      // Given
      mockLocalStorage.getItem.mockReturnValue('invalid json{')

      // When
      await offlineQueue.processQueue()

      // Then - should clear corrupt data and continue
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'offline_queue',
        '[]'
      )
    })

    it('should handle localStorage quota exceeded', () => {
      // Given
      Object.defineProperty(navigator, 'onLine', { value: false })
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('QuotaExceededError')
      })

      // When/Then - should not throw
      expect(() => {
        offlineQueue.addToQueue({ url: '/api/test', method: 'POST' as const })
      }).not.toThrow()
    })
  })
})
