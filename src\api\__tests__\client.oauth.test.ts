import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { apiClient } from '../client'
import { useAuthStore } from '@/stores/authStore'
import type { LoginSuccessResult } from '@/types'

// Mock the auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: {
    getState: vi.fn(),
  },
}))

describe('API Client - OAuth Authorization Header', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
  })

  afterEach(() => {
    // Clear any auth state
    delete apiClient.defaults.headers.common['Authorization']
  })

  it('should add Authorization header from authStore token to all requests', async () => {
    const mockToken = 'test-oauth-token-123'

    // Mock authStore to return a token
    vi.mocked(useAuthStore.getState).mockReturnValue({
      token: mockToken,
      isAuthenticated: true,
      user: { email: '<EMAIL>' },
    } as any)

    // Create a mock adapter to intercept the request
    const mockAdapter = vi.fn((config) => {
      // Verify the Authorization header is present
      expect(config.headers['Authorization']).toBe(`Bearer ${mockToken}`)

      return Promise.resolve({
        data: { success: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config,
      })
    })

    // Replace the adapter temporarily
    const originalAdapter = apiClient.defaults.adapter
    apiClient.defaults.adapter = mockAdapter

    try {
      // Make a request
      await apiClient.get('/api/test')

      // Verify the mock was called
      expect(mockAdapter).toHaveBeenCalledTimes(1)
    } finally {
      // Restore original adapter
      apiClient.defaults.adapter = originalAdapter
    }
  })

  it('should not add Authorization header when no token in authStore', async () => {
    // Mock authStore with no token
    vi.mocked(useAuthStore.getState).mockReturnValue({
      token: null,
      isAuthenticated: false,
      user: null,
    } as any)

    // Create a mock adapter to intercept the request
    const mockAdapter = vi.fn((config) => {
      // Verify no Authorization header is present
      expect(config.headers['Authorization']).toBeUndefined()

      return Promise.resolve({
        data: { success: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config,
      })
    })

    // Replace the adapter temporarily
    const originalAdapter = apiClient.defaults.adapter
    apiClient.defaults.adapter = mockAdapter

    try {
      // Make a request
      await apiClient.get('/api/test')

      // Verify the mock was called
      expect(mockAdapter).toHaveBeenCalledTimes(1)
    } finally {
      // Restore original adapter
      apiClient.defaults.adapter = originalAdapter
    }
  })

  it('should handle OAuth login flow correctly', async () => {
    const mockOAuthResponse: LoginSuccessResult = {
      access_token: 'oauth-access-token',
      expires_in: 3600,
      userName: '<EMAIL>',
      '.issued': new Date().toISOString(),
      '.expires': new Date(Date.now() + 3600000).toISOString(),
    }

    // Initially no token
    vi.mocked(useAuthStore.getState).mockReturnValue({
      token: null,
      isAuthenticated: false,
      user: null,
    } as any)

    // After OAuth login, token should be set
    const mockAdapter = vi.fn((config) => {
      // First call is the OAuth login
      if (config.url === '/token' && config.method === 'post') {
        // After this call, the token would be set in authStore
        vi.mocked(useAuthStore.getState).mockReturnValue({
          token: mockOAuthResponse.access_token,
          isAuthenticated: true,
          user: { email: mockOAuthResponse.userName },
        } as any)

        return Promise.resolve({
          data: mockOAuthResponse,
          status: 200,
          statusText: 'OK',
          headers: {},
          config,
        })
      }

      // Subsequent API calls should have the token
      expect(config.headers['Authorization']).toBe(
        `Bearer ${mockOAuthResponse.access_token}`
      )

      return Promise.resolve({
        data: { workoutData: 'test' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config,
      })
    })

    const originalAdapter = apiClient.defaults.adapter
    apiClient.defaults.adapter = mockAdapter

    try {
      // Simulate OAuth login
      const loginResponse = await apiClient.post('/token', 'grant_type=google')
      expect(loginResponse.data).toEqual(mockOAuthResponse)

      // Make an API call that requires auth
      const apiResponse = await apiClient.get(
        '/api/WorkoutLog/GetUserWorkoutProgram'
      )
      expect(apiResponse.data).toEqual({ workoutData: 'test' })

      // Verify both calls were made
      expect(mockAdapter).toHaveBeenCalledTimes(2)
    } finally {
      apiClient.defaults.adapter = originalAdapter
    }
  })
})
