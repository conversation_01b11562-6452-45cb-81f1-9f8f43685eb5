import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  triggerHaptic,
  isHapticSupported,
  withHaptic,
  useHaptic,
} from './haptic'

describe('Haptic Feedback Utilities', () => {
  let mockVibrate: ReturnType<typeof vi.fn>

  beforeEach(() => {
    mockVibrate = vi.fn()
    // Mock navigator.vibrate
    Object.defineProperty(navigator, 'vibrate', {
      value: mockVibrate,
      writable: true,
      configurable: true,
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
    // Reset navigator.vibrate
    if ('vibrate' in navigator) {
      delete (navigator as any).vibrate
    }
  })

  describe('isHapticSupported', () => {
    it('should return true when vibrate API is available', () => {
      expect(isHapticSupported()).toBe(true)
    })

    it('should return false when vibrate API is not available', () => {
      delete (navigator as any).vibrate
      expect(isHapticSupported()).toBe(false)
    })
  })

  describe('triggerHaptic', () => {
    it('should trigger light haptic feedback', () => {
      triggerHaptic('light')
      expect(mockVibrate).toHaveBeenCalledWith(10)
    })

    it('should trigger medium haptic feedback', () => {
      triggerHaptic('medium')
      expect(mockVibrate).toHaveBeenCalledWith(20)
    })

    it('should trigger heavy haptic feedback', () => {
      triggerHaptic('heavy')
      expect(mockVibrate).toHaveBeenCalledWith(30)
    })

    it('should trigger soft haptic feedback pattern', () => {
      triggerHaptic('soft')
      expect(mockVibrate).toHaveBeenCalledWith([10, 10, 10])
    })

    it('should trigger rigid haptic feedback pattern', () => {
      triggerHaptic('rigid')
      expect(mockVibrate).toHaveBeenCalledWith([20, 10, 20])
    })

    it('should use fallback duration for unknown styles', () => {
      triggerHaptic('light', 15)
      expect(mockVibrate).toHaveBeenCalledWith(10) // light = 10
    })

    it('should not throw when vibrate API is not available', () => {
      delete (navigator as any).vibrate
      expect(() => triggerHaptic('light')).not.toThrow()
    })

    it('should handle vibrate API errors gracefully', () => {
      mockVibrate.mockImplementation(() => {
        throw new Error('Vibration not allowed')
      })
      expect(() => triggerHaptic('light')).not.toThrow()
    })
  })

  describe('withHaptic', () => {
    it('should wrap function with haptic feedback', () => {
      const mockHandler = vi.fn((x: number) => x * 2)
      const wrappedHandler = withHaptic(mockHandler, 'medium')

      const result = wrappedHandler(5)

      expect(mockVibrate).toHaveBeenCalledWith(20)
      expect(mockHandler).toHaveBeenCalledWith(5)
      expect(result).toBe(10)
    })

    it('should preserve function arguments and return value', () => {
      const mockHandler = vi.fn((a: string, b: number) => `${a}-${b}`)
      const wrappedHandler = withHaptic(mockHandler)

      const result = wrappedHandler('test', 123)

      expect(mockHandler).toHaveBeenCalledWith('test', 123)
      expect(result).toBe('test-123')
    })

    it('should use default light style when not specified', () => {
      const mockHandler = vi.fn()
      const wrappedHandler = withHaptic(mockHandler)

      wrappedHandler()

      expect(mockVibrate).toHaveBeenCalledWith(10)
    })
  })

  describe('useHaptic', () => {
    it('should provide haptic utilities', () => {
      const haptic = useHaptic('heavy')

      expect(haptic.isSupported).toBe(true)
      expect(typeof haptic.trigger).toBe('function')
      expect(typeof haptic.withHandler).toBe('function')
    })

    it('should trigger haptic with specified style', () => {
      const haptic = useHaptic('soft')
      haptic.trigger()

      expect(mockVibrate).toHaveBeenCalledWith([10, 10, 10])
    })

    it('should wrap handlers with haptic feedback', () => {
      const haptic = useHaptic('rigid')
      const mockHandler = vi.fn()
      const wrapped = haptic.withHandler(mockHandler)

      wrapped()

      expect(mockVibrate).toHaveBeenCalledWith([20, 10, 20])
      expect(mockHandler).toHaveBeenCalled()
    })

    it('should report support correctly when API is not available', () => {
      delete (navigator as any).vibrate
      const haptic = useHaptic()

      expect(haptic.isSupported).toBe(false)
    })
  })
})
