/**
 * Dr. Muscle X Application Types
 *
 * These types are used throughout the Dr. Muscle X web app for UI state,
 * form handling, and application-specific data structures.
 */

import type { WorkoutTemplateModel, ExerciseModel } from './api'

// Import types needed from split files
import type { UIState } from './ui'
import type { WorkoutSession } from './workoutSession'

// Re-export types from split files
export * from './forms'
export * from './ui'
export * from './workoutSession'

/**
 * Main application state
 */
export interface AppState {
  ui: UIState
  auth: AuthStoreState
  workout: WorkoutStoreState
}

/**
 * Base store state interface
 */
export interface StoreState {
  isLoading: boolean
  error: Error | null
}

/**
 * User information
 */
export interface User {
  id: string
  email: string
  firstName?: string
  lastName?: string
  /** Profile picture URL from OAuth provider */
  pictureUrl?: string
  /** OAuth provider used for authentication */
  authProvider?: 'email' | 'google' | 'apple'
  /** Provider-specific user ID */
  providerId?: string
}

/**
 * Authentication store state
 */
export interface AuthStoreState extends StoreState {
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  hasHydrated: boolean
  /** OAuth-specific state */
  oauthProvider?: 'google' | 'apple' | null
  oauthToken?: string | null
  /** Whether user is signing in with OAuth */
  isOAuthLoading?: boolean
}

/**
 * Workout store state
 */
export interface WorkoutStoreState extends StoreState {
  currentWorkout: WorkoutTemplateModel | null
  exercises: ExerciseModel[]
  currentExerciseIndex: number
  currentSetIndex: number
  workoutSession: WorkoutSession | null
}

/**
 * API error structure
 */
export interface APIError {
  status: number
  message: string
  code?: string
  details?: Record<string, unknown>
}

/**
 * Async state wrapper for data fetching
 * @template T The type of data being fetched
 */
export interface AsyncState<T> {
  status: 'idle' | 'pending' | 'success' | 'error'
  data: T | null
  error: Error | null
}
