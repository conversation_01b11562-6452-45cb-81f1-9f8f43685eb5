import { describe, it, expect } from 'vitest'
import { navigationConfig, getNavigationConfig } from './navigationConfig'

describe('navigationConfig', () => {
  it('has correct configuration for program page', () => {
    const config = navigationConfig['/program']

    expect(config).toBeDefined()
    expect(config?.title).toBe('Home')
    expect(config?.showBackButton).toBe(false)
    expect(config?.requiresAuth).toBe(true)
    expect(config?.rightElement).toBeDefined()
  })

  it('has correct configuration for workout page', () => {
    const config = navigationConfig['/workout']

    expect(config).toBeDefined()
    expect(config?.title).toBe('Workout')
    expect(config?.showBackButton).toBe(true)
    expect(config?.backRoute).toBe('/program')
    expect(config?.requiresAuth).toBe(true)
  })

  it('has correct configuration for exercise page', () => {
    const config = navigationConfig['/workout/exercise/[id]']

    expect(config).toBeDefined()
    expect(config?.title).toBe('') // Dynamic title
    expect(config?.showBackButton).toBe(true)
    expect(config?.backRoute).toBe('/workout')
    expect(config?.requiresAuth).toBe(true)
  })

  it('has correct configuration for rest timer', () => {
    const config = navigationConfig['/workout/rest-timer']

    expect(config).toBeDefined()
    expect(config?.title).toBe('Rest Timer')
    expect(config?.showBackButton).toBe(true)
    expect(config?.backRoute).toBeUndefined() // No hardcoded back route
    expect(config?.requiresAuth).toBe(true)
  })

  it('has correct configuration for workout complete', () => {
    const config = navigationConfig['/workout/complete']

    expect(config).toBeDefined()
    expect(config?.title).toBe('Workout summary')
    expect(config?.showBackButton).toBe(true)
    expect(config?.backRoute).toBe('/workout')
    expect(config?.requiresAuth).toBe(true)
  })
})

describe('getNavigationConfig', () => {
  it('returns exact match configuration', () => {
    const config = getNavigationConfig('/program')

    expect(config).toEqual(navigationConfig['/program'])
  })

  it('returns configuration for dynamic routes', () => {
    const config = getNavigationConfig('/workout/exercise/123')

    expect(config).toEqual(navigationConfig['/workout/exercise/[id]'])
  })

  it('returns null for unknown routes', () => {
    const config = getNavigationConfig('/unknown-route')

    expect(config).toBeNull()
  })

  it('handles multiple dynamic route segments', () => {
    const config1 = getNavigationConfig('/workout/exercise/bench-press')
    const config2 = getNavigationConfig('/workout/exercise/squats')

    expect(config1).toEqual(navigationConfig['/workout/exercise/[id]'])
    expect(config2).toEqual(navigationConfig['/workout/exercise/[id]'])
  })

  it('does not match partial routes', () => {
    const config = getNavigationConfig('/workout/exercise')

    expect(config).toBeNull()
  })
})
