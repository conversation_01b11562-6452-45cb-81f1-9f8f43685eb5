/**
 * Integration tests for useWorkoutRecommendations hook
 * Tests the API request construction with proper parameters
 */

import { renderHook, waitFor } from '@testing-library/react'
import { useWorkoutRecommendations } from '../useWorkoutRecommendations'
import { getExerciseRecommendation } from '@/services/api/workout'
import { getUserSettings } from '@/services/userSettings'
import { getCurrentUserEmail } from '@/lib/auth-utils'

// Mock dependencies
vi.mock('@/services/api/workout')
vi.mock('@/services/userSettings')
vi.mock('@/lib/auth-utils')
vi.mock('@/stores/workoutStore/index', () => ({
  useWorkoutStore: () => ({
    currentWorkout: {
      Id: 123,
      Exercises: [
        {
          Id: 27474,
          Label: 'Test Exercise',
          SetStyle: 'Normal',
          IsFlexibility: false,
        },
      ],
    },
    getCachedExerciseRecommendation: vi.fn(() => null),
    setCachedExerciseRecommendation: vi.fn(),
  }),
}))

describe('useWorkoutRecommendations Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Mock user email
    vi.mocked(getCurrentUserEmail).mockReturnValue('<EMAIL>')

    // Mock user settings
    vi.mocked(getUserSettings).mockResolvedValue({
      isQuickMode: false,
      isStrengthPhase: false,
      isFreePlan: false,
      isFirstWorkoutOfStrengthPhase: false,
      lightSessionDays: null,
    })

    // Mock API response
    vi.mocked(getExerciseRecommendation).mockResolvedValue({
      Series: 3,
      Reps: 10,
      Weight: { Kg: 50, Lb: 110 },
      WarmUpsList: [],
      RpRest: 90,
      IsBodyweight: false,
      IsNormalSets: true,
      IsDeload: false,
      IsBackOffSet: false,
      IsPyramid: false,
      IsReversePyramid: false,
      MinReps: 8,
      MaxReps: 12,
      RIR: 2,
      isPlateAvailable: true,
      isDumbbellAvailable: true,
      isPulleyAvailable: true,
      isBandsAvailable: false,
      OneRMProgress: 0,
      RecommendationInKg: 50,
      OneRMPercentage: 80,
    })
  })

  it('should call getExerciseRecommendation with all required parameters', async () => {
    const { result } = renderHook(() => useWorkoutRecommendations())

    // Call loadRecommendation
    await result.current.loadRecommendation(27474, 'Test Exercise')

    // Wait for the API call to complete
    await waitFor(() => {
      expect(getExerciseRecommendation).toHaveBeenCalledWith({
        Username: '<EMAIL>',
        ExerciseId: 27474,
        WorkoutId: 123,
        SetStyle: 'Normal',
        IsFlexibility: false,
        IsQuickMode: false,
        LightSessionDays: null,
        SwapedExId: undefined,
        IsStrengthPhashe: false, // Note: API has typo
        IsFreePlan: false,
        IsFirstWorkoutOfStrengthPhase: false,
        VersionNo: 1,
      })
    })
  })

  it('should use user settings for recommendation parameters', async () => {
    // Mock different user settings
    vi.mocked(getUserSettings).mockResolvedValue({
      isQuickMode: true,
      isStrengthPhase: true,
      isFreePlan: true,
      isFirstWorkoutOfStrengthPhase: true,
      lightSessionDays: 2,
    })

    const { result } = renderHook(() => useWorkoutRecommendations())

    // Call loadRecommendation
    await result.current.loadRecommendation(27474, 'Test Exercise')

    // Wait for the API call to complete
    await waitFor(() => {
      expect(getExerciseRecommendation).toHaveBeenCalledWith({
        Username: '<EMAIL>',
        ExerciseId: 27474,
        WorkoutId: 123,
        SetStyle: 'Normal',
        IsFlexibility: false,
        IsQuickMode: true,
        LightSessionDays: 2,
        SwapedExId: undefined,
        IsStrengthPhashe: true, // Note: API has typo
        IsFreePlan: true,
        IsFirstWorkoutOfStrengthPhase: true,
        VersionNo: 1,
      })
    })
  })
})
