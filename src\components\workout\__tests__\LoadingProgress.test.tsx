import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { LoadingProgress } from '../LoadingProgress'

describe('LoadingProgress', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render progress bar', () => {
    render(<LoadingProgress progress={0} />)

    const progressBar = screen.getByTestId('loading-progress-bar')
    expect(progressBar).toBeInTheDocument()
  })

  it('should render with correct progress width', () => {
    render(<LoadingProgress progress={50} />)

    const progressFill = screen.getByTestId('loading-progress-fill')
    expect(progressFill).toHaveStyle({ width: '50%' })
  })

  it('should handle 0% progress', () => {
    render(<LoadingProgress progress={0} />)

    const progressFill = screen.getByTestId('loading-progress-fill')
    expect(progressFill).toHaveStyle({ width: '0%' })
  })

  it('should handle 100% progress', () => {
    render(<LoadingProgress progress={100} />)

    const progressFill = screen.getByTestId('loading-progress-fill')
    expect(progressFill).toHaveStyle({ width: '100%' })
  })

  it('should clamp progress values above 100', () => {
    render(<LoadingProgress progress={150} />)

    const progressFill = screen.getByTestId('loading-progress-fill')
    expect(progressFill).toHaveStyle({ width: '100%' })
  })

  it('should clamp negative progress values', () => {
    render(<LoadingProgress progress={-50} />)

    const progressFill = screen.getByTestId('loading-progress-fill')
    expect(progressFill).toHaveStyle({ width: '0%' })
  })

  it('should render status text when provided', () => {
    render(<LoadingProgress progress={50} status="Loading workouts..." />)

    expect(screen.getByText('Loading workouts...')).toBeInTheDocument()
  })

  it('should not render status text when not provided', () => {
    render(<LoadingProgress progress={50} />)

    const statusText = screen.queryByTestId('loading-progress-status')
    expect(statusText).not.toBeInTheDocument()
  })

  it('should apply custom className', () => {
    render(<LoadingProgress progress={50} className="custom-class" />)

    const container = screen.getByTestId('loading-progress')
    expect(container).toHaveClass('custom-class')
  })

  it('should have animated progress transition', () => {
    render(<LoadingProgress progress={50} />)

    const progressFill = screen.getByTestId('loading-progress-fill')
    expect(progressFill).toHaveClass('transition-all', 'duration-300')
  })

  it('should use primary color for progress fill', () => {
    render(<LoadingProgress progress={50} />)

    const progressFill = screen.getByTestId('loading-progress-fill')
    expect(progressFill).toHaveClass('bg-blue-600')
  })

  it('should have proper height for mobile touch', () => {
    render(<LoadingProgress progress={50} />)

    const progressBar = screen.getByTestId('loading-progress-bar')
    expect(progressBar).toHaveClass('h-2')
  })

  it('should show percentage when showPercentage is true', () => {
    render(<LoadingProgress progress={75} showPercentage />)

    expect(screen.getByText('75%')).toBeInTheDocument()
  })

  it('should not show percentage by default', () => {
    render(<LoadingProgress progress={75} />)

    expect(screen.queryByText('75%')).not.toBeInTheDocument()
  })

  it('should round percentage to whole numbers', () => {
    render(<LoadingProgress progress={75.8} showPercentage />)

    expect(screen.getByText('76%')).toBeInTheDocument()
  })

  it('should apply dark mode styles', () => {
    render(<LoadingProgress progress={50} />)

    const progressBar = screen.getByTestId('loading-progress-bar')
    expect(progressBar).toHaveClass('bg-gray-200', 'dark:bg-gray-700')
  })

  it('should have rounded corners', () => {
    render(<LoadingProgress progress={50} />)

    const progressBar = screen.getByTestId('loading-progress-bar')
    const progressFill = screen.getByTestId('loading-progress-fill')

    expect(progressBar).toHaveClass('rounded-full')
    expect(progressFill).toHaveClass('rounded-full')
  })

  it('should have smooth easing function', () => {
    render(<LoadingProgress progress={50} />)

    const progressFill = screen.getByTestId('loading-progress-fill')
    expect(progressFill).toHaveClass('ease-out')
  })

  it('should apply will-change for performance', () => {
    render(<LoadingProgress progress={50} />)

    const progressFill = screen.getByTestId('loading-progress-fill')
    expect(progressFill).toHaveStyle({ willChange: 'width' })
  })

  it('should render status with proper text styling', () => {
    render(<LoadingProgress progress={50} status="Loading..." />)

    const statusText = screen.getByTestId('loading-progress-status')
    expect(statusText).toHaveClass(
      'text-sm',
      'text-gray-600',
      'dark:text-gray-400'
    )
  })
})
