import { test, expect } from '@playwright/test'

test.describe('Weight Display Precision', () => {
  test.beforeEach(async () => {
    // Mock API setup removed - test uses direct route mocking
  })

  test('should display weight without floating-point precision errors', async ({
    page,
  }) => {
    // Mock recommendation with floating-point precision error
    await page.route('**/GetRecommendation', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 123,
          ExerciseId: 456,
          Series: 3,
          Reps: 10,
          Weight: { Lb: 35.00000000000004, Kg: 15.875732 },
          WarmupsCount: 1,
          RecommendationInKg: 15.875732,
          OneRMProgress: 0,
          Notes: null,
        }),
      })
    })

    // Navigate to login page
    await page.goto('/login')

    // Login
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL(/\/program/)

    // Navigate to workout
    await page.click('text=Start Workout')
    await page.waitForURL(/\/workout/)

    // Start workout
    await page.click('text=Start Workout')

    // Navigate to first exercise
    await page.click('text=Exercise 1')
    await page.waitForURL(/\/workout\/exercise/)

    // Check that the target weight displays correctly without precision errors
    await expect(page.locator('text=Target:')).toBeVisible()

    // The target should show "35 lbs" not "35.00000000000004 lbs"
    await expect(page.locator('text=Target:')).toContainText('10 reps × 35 lbs')

    // Check the weight input field also shows clean value
    const weightInput = page.locator('input[aria-label="Weight"]')
    await expect(weightInput).toHaveValue('35')

    // Ensure no floating-point artifacts in the display
    const pageContent = await page.content()
    expect(pageContent).not.toContain('35.00000000000004')
  })

  test('should preserve valid decimal weights', async ({ page }) => {
    // Mock recommendation with valid decimal weight
    await page.route('**/GetRecommendation', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          Id: 123,
          ExerciseId: 456,
          Series: 3,
          Reps: 10,
          Weight: { Lb: 102.5, Kg: 46.5 },
          WarmupsCount: 1,
          RecommendationInKg: 46.5,
          OneRMProgress: 0,
          Notes: null,
        }),
      })
    })

    // Navigate to login page
    await page.goto('/login')

    // Login
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL(/\/program/)

    // Navigate to workout
    await page.click('text=Start Workout')
    await page.waitForURL(/\/workout/)

    // Start workout
    await page.click('text=Start Workout')

    // Navigate to first exercise
    await page.click('text=Exercise 1')
    await page.waitForURL(/\/workout\/exercise/)

    // Check that valid decimals are preserved
    await expect(page.locator('text=Target:')).toContainText(
      '10 reps × 102.5 lbs'
    )

    // Check the weight input field preserves decimal
    const weightInput = page.locator('input[aria-label="Weight"]')
    await expect(weightInput).toHaveValue('102.5')
  })
})
