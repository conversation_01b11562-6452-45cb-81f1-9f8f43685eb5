import { QueryClient } from '@tanstack/react-query'
import { vi } from 'vitest'
import { waitFor } from '@testing-library/react'

/**
 * Helper to reset all query caches in a QueryClient
 */
export function resetQueryCache(queryClient: QueryClient) {
  queryClient.clear()
  queryClient.resetQueries()
  queryClient.cancelQueries()
}

/**
 * Helper to wait for a query to be in a specific state
 */
export async function waitForQueryState(
  queryClient: QueryClient,
  queryKey: unknown[],
  predicate: (state: ReturnType<QueryClient['getQueryState']>) => boolean,
  options: { timeout?: number } = {}
) {
  const { timeout = 5000 } = options

  await waitFor(
    () => {
      const state = queryClient.getQueryState(queryKey)
      if (!predicate(state)) {
        throw new Error('Query not in expected state')
      }
    },
    { timeout }
  )
}

/**
 * Helper to wait for a query to succeed
 */
export async function waitForQuerySuccess(
  queryClient: QueryClient,
  queryKey: unknown[]
) {
  await waitForQueryState(
    queryClient,
    queryKey,
    (state) => state?.status === 'success'
  )
}

/**
 * Helper to wait for a query to fail
 */
export async function waitForQueryError(
  queryClient: QueryClient,
  queryKey: unknown[]
) {
  await waitForQueryState(
    queryClient,
    queryKey,
    (state) => state?.status === 'error'
  )
}

/**
 * Helper to set query data in tests
 */
export function setQueryData<T>(
  queryClient: QueryClient,
  queryKey: unknown[],
  data: T
) {
  queryClient.setQueryData(queryKey, data)
}

/**
 * Helper to invalidate and refetch queries
 */
export async function invalidateAndRefetch(
  queryClient: QueryClient,
  queryKey?: unknown[]
) {
  await queryClient.invalidateQueries(queryKey ? { queryKey } : undefined)
  await queryClient.refetchQueries(queryKey ? { queryKey } : undefined)
}

/**
 * Helper to mock a mutation
 */
export function createMockMutation<TData = unknown, TVariables = unknown>({
  onSuccess,
  onError,
  data,
  error,
}: {
  onSuccess?: (data: TData, variables: TVariables) => void
  onError?: (error: Error, variables: TVariables) => void
  data?: TData
  error?: Error
} = {}) {
  const mutationFn = vi.fn()

  if (error) {
    mutationFn.mockRejectedValue(error)
  } else if (data) {
    mutationFn.mockResolvedValue(data)
  }

  return {
    mutationFn,
    onSuccess: onSuccess || vi.fn(),
    onError: onError || vi.fn(),
  }
}

/**
 * Helper to wait for all pending queries to complete
 */
export async function waitForQueriesToSettle(queryClient: QueryClient) {
  const queries = queryClient.getQueryCache().getAll()
  const pendingQueries = queries.filter(
    (query) => query.state.fetchStatus === 'fetching'
  )

  if (pendingQueries.length === 0) return

  await Promise.all(
    pendingQueries.map((query) =>
      waitForQueryState(
        queryClient,
        [...query.queryKey], // Convert readonly array to mutable array
        (state) => state?.fetchStatus === 'idle'
      )
    )
  )
}

/**
 * Helper to simulate network conditions
 */
export function mockNetworkConditions({
  isOnline = true,
  latency = 0,
}: {
  isOnline?: boolean
  latency?: number
} = {}) {
  // Mock navigator.onLine
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: isOnline,
  })

  // Add artificial latency to all promises if specified
  if (latency > 0) {
    const originalResolve = Promise.resolve
    Promise.resolve = function (...args) {
      return originalResolve
        .apply(Promise, args)
        .then(
          (value) =>
            new Promise((resolve) => setTimeout(() => resolve(value), latency))
        )
    }

    // Return cleanup function
    return () => {
      Promise.resolve = originalResolve
    }
  }

  return () => {}
}

/**
 * Helper to test optimistic updates
 */
export function createOptimisticMutation<TData, TVariables>({
  mutationFn,
  optimisticData,
}: {
  mutationFn: (variables: TVariables) => Promise<TData>
  optimisticData: (variables: TVariables) => TData
}) {
  return {
    mutationFn,
    onMutate: async (variables: TVariables) => {
      return { optimisticData: optimisticData(variables) }
    },
  }
}
