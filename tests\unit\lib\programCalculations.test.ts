import { describe, expect, it, vi } from 'vitest'
import {
  calculateProgress,
  getWeekNumber,
  calculateCompletionDate,
  getWorkoutFrequency,
  getDaysRemaining,
  getMissedWorkouts,
  getWorkoutsPerWeek,
  calculateStreak,
} from '@/lib/programCalculations'
import type { ProgramModel, ProgramProgress } from '@/types/program'

describe('programCalculations', () => {
  // Mock current date for consistent testing
  const mockDate = new Date('2024-01-15T12:00:00Z')

  beforeEach(() => {
    vi.useFakeTimers()
    vi.setSystemTime(mockDate)
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('calculateProgress', () => {
    const mockProgram: ProgramModel = {
      id: '1',
      name: '12-Week Transformation',
      description: 'Build muscle and strength',
      category: 'Muscle Building',
      totalDays: 84, // 12 weeks
      currentDay: 1,
      workoutsCompleted: 0,
      startDate: '2024-01-01T00:00:00Z',
      totalWorkouts: 36, // 3 per week
      imageUrl: null,
    }

    it('should calculate 0% progress for new program', () => {
      const progress = calculateProgress(mockProgram, [])
      expect(progress.percentage).toBe(0)
      expect(progress.daysCompleted).toBe(15) // Days since start date
      expect(progress.totalWorkouts).toBe(36)
      expect(progress.currentWeek).toBe(3) // Week 3 based on elapsed time
      expect(progress.remainingWorkouts).toBe(36)
    })

    it('should calculate progress based on workouts completed', () => {
      const programWithProgress = {
        ...mockProgram,
        workoutsCompleted: 12,
      }

      const progress = calculateProgress(programWithProgress, [])
      expect(progress.percentage).toBe(33) // 12/36 = 33.33%
      expect(progress.remainingWorkouts).toBe(24) // 36 - 12
    })

    it('should calculate current week based on start date', () => {
      const progress = calculateProgress(mockProgram, [])
      expect(progress.currentWeek).toBe(3) // 15 days since Jan 1 = week 3
    })

    it('should handle completed program (100%)', () => {
      const completedProgram = {
        ...mockProgram,
        workoutsCompleted: 36,
        currentDay: 84,
      }

      const progress = calculateProgress(completedProgram, [])
      expect(progress.percentage).toBe(100)
      expect(progress.remainingWorkouts).toBe(0)
    })

    it('should handle programs with no total workouts', () => {
      const programNoTotal = {
        ...mockProgram,
        totalWorkouts: 0,
      }

      const progress = calculateProgress(programNoTotal, [])
      expect(progress.percentage).toBe(0)
    })

    it('should calculate days completed based on elapsed time', () => {
      const progress = calculateProgress(mockProgram, [])
      expect(progress.daysCompleted).toBe(15) // Jan 1 to Jan 15
    })
  })

  describe('getWeekNumber', () => {
    it('should return week 1 for first 7 days', () => {
      expect(getWeekNumber('2024-01-01', '2024-01-01')).toBe(1)
      expect(getWeekNumber('2024-01-01', '2024-01-07')).toBe(1)
    })

    it('should return correct week for later dates', () => {
      expect(getWeekNumber('2024-01-01', '2024-01-08')).toBe(2)
      expect(getWeekNumber('2024-01-01', '2024-01-15')).toBe(3)
      expect(getWeekNumber('2024-01-01', '2024-01-22')).toBe(4)
    })

    it('should handle invalid dates gracefully', () => {
      expect(getWeekNumber('invalid', '2024-01-15')).toBe(1)
      expect(getWeekNumber('2024-01-01', 'invalid')).toBe(1)
    })

    it('should handle dates in different timezones', () => {
      expect(
        getWeekNumber('2024-01-01T00:00:00-08:00', '2024-01-08T00:00:00-08:00')
      ).toBe(2)
    })
  })

  describe('calculateCompletionDate', () => {
    const mockProgram: ProgramModel = {
      id: '1',
      name: '12-Week Program',
      description: 'Test program',
      category: 'Test',
      totalDays: 84,
      currentDay: 15,
      workoutsCompleted: 6,
      startDate: '2024-01-01T00:00:00Z',
      totalWorkouts: 36,
      imageUrl: null,
    }

    const mockProgress: ProgramProgress = {
      percentage: 17,
      daysCompleted: 15,
      currentWeek: 3,
      totalWorkouts: 36,
      workoutsThisWeek: 2,
      remainingWorkouts: 30,
    }

    it('should project completion date based on current pace', () => {
      // 6 workouts in 15 days = 0.4 workouts/day
      // 30 remaining workouts / 0.4 = 75 days
      const completionDate = calculateCompletionDate(mockProgram, mockProgress)
      const expectedDate = new Date(mockDate)
      expectedDate.setDate(expectedDate.getDate() + 75)

      expect(completionDate).toBeInstanceOf(Date)
      expect(completionDate?.toDateString()).toBe(expectedDate.toDateString())
    })

    it('should return null for completed programs', () => {
      const completedProgress: ProgramProgress = {
        ...mockProgress,
        percentage: 100,
        remainingWorkouts: 0,
      }

      expect(calculateCompletionDate(mockProgram, completedProgress)).toBeNull()
    })

    it('should handle programs with no workouts completed', () => {
      const noProgressYet: ProgramProgress = {
        ...mockProgress,
        percentage: 0,
        remainingWorkouts: 36,
      }

      // Should use expected pace (3 workouts/week)
      const completionDate = calculateCompletionDate(mockProgram, noProgressYet)
      expect(completionDate).toBeInstanceOf(Date)
    })

    it('should cap projection at reasonable maximum', () => {
      const verySlowProgress: ProgramProgress = {
        ...mockProgress,
        percentage: 3, // 1/36 = ~3%
        daysCompleted: 30,
      }

      const completionDate = calculateCompletionDate(
        { ...mockProgram, workoutsCompleted: 1 },
        verySlowProgress
      )
      const maxDate = new Date(mockDate)
      maxDate.setFullYear(maxDate.getFullYear() + 1)

      expect(completionDate).toBeInstanceOf(Date)
      expect(completionDate!.getTime()).toBeLessThan(maxDate.getTime())
    })
  })

  describe('getWorkoutFrequency', () => {
    it('should calculate average workouts per week', () => {
      const workouts = [
        { date: '2024-01-01' },
        { date: '2024-01-03' },
        { date: '2024-01-05' },
        { date: '2024-01-08' },
        { date: '2024-01-10' },
        { date: '2024-01-12' },
      ]

      // 6 workouts from Jan 1 to Jan 12 = 11 days = ~1.57 weeks
      // 6 / 1.57 = ~3.82 per week
      const frequency = getWorkoutFrequency(workouts)
      expect(frequency).toBeCloseTo(3.82, 1)
    })

    it('should handle empty workout array', () => {
      expect(getWorkoutFrequency([])).toBe(0)
    })

    it('should handle single workout', () => {
      const workouts = [{ date: '2024-01-15' }]
      expect(getWorkoutFrequency(workouts)).toBe(0) // Need at least 2 workouts for frequency
    })

    it('should calculate frequency for workouts over multiple weeks', () => {
      const workouts = [
        { date: '2024-01-01' },
        { date: '2024-01-15' },
        { date: '2024-01-29' },
      ]

      // 3 workouts over 4 weeks = 0.75 per week
      const frequency = getWorkoutFrequency(workouts)
      expect(frequency).toBeCloseTo(0.75, 2)
    })
  })

  describe('getDaysRemaining', () => {
    it('should calculate days remaining in program', () => {
      const program: ProgramModel = {
        id: '1',
        name: 'Test',
        description: 'Test',
        category: 'Test',
        totalDays: 84,
        currentDay: 15,
        workoutsCompleted: 6,
        startDate: '2024-01-01T00:00:00Z',
        totalWorkouts: 36,
        imageUrl: null,
      }

      expect(getDaysRemaining(program)).toBe(69) // 84 - 15
    })

    it('should return 0 for completed programs', () => {
      const program: ProgramModel = {
        id: '1',
        name: 'Test',
        description: 'Test',
        category: 'Test',
        totalDays: 84,
        currentDay: 84,
        workoutsCompleted: 36,
        startDate: '2024-01-01T00:00:00Z',
        totalWorkouts: 36,
        imageUrl: null,
      }

      expect(getDaysRemaining(program)).toBe(0)
    })

    it('should handle invalid data gracefully', () => {
      const program: ProgramModel = {
        id: '1',
        name: 'Test',
        description: 'Test',
        category: 'Test',
        totalDays: 0,
        currentDay: 0,
        workoutsCompleted: 0,
        startDate: '2024-01-01T00:00:00Z',
        totalWorkouts: 0,
        imageUrl: null,
      }

      expect(getDaysRemaining(program)).toBe(0)
    })
  })

  describe('getMissedWorkouts', () => {
    it('should calculate missed workouts based on expected pace', () => {
      const program: ProgramModel = {
        id: '1',
        name: 'Test',
        description: 'Test',
        category: 'Test',
        totalDays: 84,
        currentDay: 21, // 3 weeks
        workoutsCompleted: 6,
        startDate: '2024-01-01T00:00:00Z',
        totalWorkouts: 36, // 3 per week expected
        imageUrl: null,
      }

      // Expected: 9 workouts in 3 weeks, completed: 6, missed: 3
      expect(getMissedWorkouts(program)).toBe(3)
    })

    it('should return 0 when ahead of schedule', () => {
      const program: ProgramModel = {
        id: '1',
        name: 'Test',
        description: 'Test',
        category: 'Test',
        totalDays: 84,
        currentDay: 7,
        workoutsCompleted: 5,
        startDate: '2024-01-08T00:00:00Z',
        totalWorkouts: 36,
        imageUrl: null,
      }

      // Expected: 3 workouts in 1 week, completed: 5, ahead of schedule
      expect(getMissedWorkouts(program)).toBe(0)
    })
  })

  describe('getWorkoutsPerWeek', () => {
    it('should calculate expected workouts per week', () => {
      const program: ProgramModel = {
        id: '1',
        name: 'Test',
        description: 'Test',
        category: 'Test',
        totalDays: 84, // 12 weeks
        currentDay: 1,
        workoutsCompleted: 0,
        startDate: '2024-01-01T00:00:00Z',
        totalWorkouts: 36,
        imageUrl: null,
      }

      expect(getWorkoutsPerWeek(program)).toBe(3) // 36 workouts / 12 weeks
    })

    it('should handle programs with irregular durations', () => {
      const program: ProgramModel = {
        id: '1',
        name: 'Test',
        description: 'Test',
        category: 'Test',
        totalDays: 30,
        currentDay: 1,
        workoutsCompleted: 0,
        startDate: '2024-01-01T00:00:00Z',
        totalWorkouts: 20,
        imageUrl: null,
      }

      expect(getWorkoutsPerWeek(program)).toBeCloseTo(4.67, 1) // 20 workouts / ~4.3 weeks
    })
  })

  describe('calculateStreak', () => {
    it('should calculate current workout streak', () => {
      const workouts = [
        { date: '2024-01-13' },
        { date: '2024-01-14' },
        { date: '2024-01-15' },
      ]

      expect(calculateStreak(workouts)).toBe(3)
    })

    it('should return 0 for no recent workouts', () => {
      const workouts = [{ date: '2024-01-01' }, { date: '2024-01-02' }]

      expect(calculateStreak(workouts)).toBe(0) // Too old
    })

    it('should handle broken streaks', () => {
      const workouts = [
        { date: '2024-01-10' },
        { date: '2024-01-11' },
        // Gap
        { date: '2024-01-14' },
        { date: '2024-01-15' },
      ]

      expect(calculateStreak(workouts)).toBe(2) // Only counting recent streak
    })

    it('should count workouts on same day as 1', () => {
      const workouts = [
        { date: '2024-01-15T08:00:00Z' },
        { date: '2024-01-15T18:00:00Z' },
      ]

      expect(calculateStreak(workouts)).toBe(1)
    })
  })
})
