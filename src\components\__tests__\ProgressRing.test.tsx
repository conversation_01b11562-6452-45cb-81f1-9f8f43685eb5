import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ProgressRing } from '../ProgressRing'
import React from 'react'

// Mock window.matchMedia for reduced motion tests
beforeEach(() => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })
})

describe('ProgressRing', () => {
  describe('Basic rendering', () => {
    it('should render progress ring with percentage', () => {
      render(<ProgressRing progress={50} />)

      // Check for SVG element
      const svg = screen.getByRole('img', { name: /progress/i })
      expect(svg).toBeInTheDocument()

      // Check for percentage text
      expect(screen.getByText('50%')).toBeInTheDocument()
    })

    it('should render with 0% progress', () => {
      render(<ProgressRing progress={0} />)
      expect(screen.getByText('0%')).toBeInTheDocument()
    })

    it('should render with 100% progress', () => {
      render(<ProgressRing progress={100} />)
      expect(screen.getByText('100%')).toBeInTheDocument()
    })

    it('should clamp progress values outside 0-100 range', () => {
      const { rerender } = render(<ProgressRing progress={-10} />)
      expect(screen.getByText('0%')).toBeInTheDocument()

      rerender(<ProgressRing progress={150} />)
      expect(screen.getByText('100%')).toBeInTheDocument()
    })
  })

  describe('Size variants', () => {
    it('should render small size', () => {
      render(<ProgressRing progress={50} size="sm" />)
      const svg = screen.getByRole('img', { name: /progress/i })
      expect(svg).toHaveClass('w-24', 'h-24')
    })

    it('should render medium size (default)', () => {
      render(<ProgressRing progress={50} size="md" />)
      const svg = screen.getByRole('img', { name: /progress/i })
      expect(svg).toHaveClass('w-32', 'h-32')
    })

    it('should render large size', () => {
      render(<ProgressRing progress={50} size="lg" />)
      const svg = screen.getByRole('img', { name: /progress/i })
      expect(svg).toHaveClass('w-40', 'h-40')
    })

    it('should accept custom numeric size', () => {
      render(<ProgressRing progress={50} size={200} />)
      const svg = screen.getByRole('img', { name: /progress/i })
      expect(svg).toHaveAttribute('width', '200')
      expect(svg).toHaveAttribute('height', '200')
    })
  })

  describe('Customization', () => {
    it('should hide percentage when showPercentage is false', () => {
      render(<ProgressRing progress={50} showPercentage={false} />)
      expect(screen.queryByText('50%')).not.toBeInTheDocument()
    })

    it('should render custom children instead of percentage', () => {
      render(
        <ProgressRing progress={50}>
          <span>Custom Text</span>
        </ProgressRing>
      )
      expect(screen.getByText('Custom Text')).toBeInTheDocument()
      expect(screen.queryByText('50%')).not.toBeInTheDocument()
    })

    it('should apply custom color', () => {
      render(<ProgressRing progress={50} color="text-blue-500" />)
      const progressPath = screen.getByTestId('progress-path')
      expect(progressPath).toHaveClass('text-blue-500')
    })

    it('should apply custom stroke width', () => {
      render(<ProgressRing progress={50} strokeWidth={8} />)
      const progressPath = screen.getByTestId('progress-path')
      expect(progressPath).toHaveAttribute('stroke-width', '8')
    })
  })

  describe('Animation', () => {
    it('should have transition class for animation', () => {
      render(<ProgressRing progress={50} />)
      const progressPath = screen.getByTestId('progress-path')
      expect(progressPath).toHaveClass('transition-all', 'duration-1000')
    })

    it('should start from 0 and animate to progress value', () => {
      const { rerender } = render(<ProgressRing progress={0} />)
      const progressPath = screen.getByTestId('progress-path')

      // Initial state
      expect(progressPath).toHaveAttribute('stroke-dashoffset')

      // Update progress
      rerender(<ProgressRing progress={75} />)

      // Should have different stroke-dashoffset for animation
      expect(progressPath).toHaveAttribute('stroke-dashoffset')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<ProgressRing progress={50} />)
      const svg = screen.getByRole('img', { name: /progress/i })

      expect(svg).toHaveAttribute('aria-label', '50% progress')
      expect(svg).toHaveAttribute('role', 'img')
    })

    it('should have descriptive aria-label with custom children', () => {
      render(
        <ProgressRing progress={75}>
          <span>3 of 4</span>
        </ProgressRing>
      )
      const svg = screen.getByRole('img')
      expect(svg).toHaveAttribute('aria-label', '75% progress')
    })

    it('should respect prefers-reduced-motion', () => {
      // Mock reduced motion preference
      window.matchMedia = vi.fn().mockImplementation((query) => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))

      render(<ProgressRing progress={50} />)
      const progressPath = screen.getByTestId('progress-path')

      // Should not have transition classes when reduced motion is preferred
      expect(progressPath).toHaveClass('duration-0')
    })
  })

  describe('Dark mode', () => {
    it('should apply dark mode styles', () => {
      render(
        <div className="dark">
          <ProgressRing progress={50} />
        </div>
      )

      const container = screen.getByTestId('progress-ring-container')
      expect(container).toHaveClass('dark:bg-gray-800')
    })
  })
})
