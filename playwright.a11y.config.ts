// eslint-disable-next-line import/no-extraneous-dependencies
import { defineConfig, devices } from '@playwright/test'

/**
 * Accessibility-specific Playwright configuration
 */
export default defineConfig({
  testDir: './tests/a11y',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI */
  retries: process.env.CI ? 1 : 0,
  /* Use single worker for consistency */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter configuration */
  reporter: [['html', { outputFolder: 'playwright-a11y-report' }], ['list']],
  /* Timeout for each test */
  timeout: 30000,
  /* Shared settings for all the projects below */
  use: {
    /* Base URL for testing */
    baseURL: process.env.CI
      ? 'http://localhost:3000'
      : 'https://x.dr-muscle.com',
    /* Collect trace when retrying the failed test */
    trace: 'on-first-retry',
    /* Screenshot on failure */
    screenshot: 'only-on-failure',
  },

  /* Configure projects for accessibility testing */
  projects: [
    /* Mobile Safari - Primary target */
    {
      name: 'Mobile Safari A11y',
      use: {
        ...devices['iPhone 13'],
        viewport: { width: 390, height: 844 },
        hasTouch: true,
        isMobile: true,
      },
    },
    /* Desktop Chrome - Secondary target */
    {
      name: 'Desktop Chrome A11y',
      use: {
        ...devices['Desktop Chrome'],
      },
    },
  ],

  /* Run local dev server before tests in CI */
  webServer: process.env.CI
    ? {
        command: 'npm run start',
        port: 3000,
        reuseExistingServer: !process.env.CI,
        timeout: 120000,
      }
    : undefined,
})
