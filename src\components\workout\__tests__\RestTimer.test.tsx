import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { RestTimer } from '../RestTimer'
import { useTimer } from '@/hooks/useTimer'

// Mock the timer hook
vi.mock('@/hooks/useTimer', () => ({
  useTimer: vi.fn(() => ({
    timeRemaining: 30,
    isRunning: true,
    progress: 66.67,
    formattedTime: '0:30',
    skip: vi.fn(),
  })),
}))

// Mock the workout store
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(() => ({
    getRestDuration: vi.fn(() => 90),
    getExerciseProgress: vi.fn(() => ({
      currentSetIsWarmup: false,
    })),
  })),
}))

describe('RestTimer', () => {
  const mockOnComplete = vi.fn()
  const mockOnSkip = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Theme Application', () => {
    it('should use theme-aware text colors for title instead of hardcoded gray-900', () => {
      render(
        <RestTimer
          onComplete={mockOnComplete}
          onSkip={mockOnSkip}
          autoStart
          soundEnabled
        />
      )

      const title = screen.getByText('Rest')
      expect(title).toHaveClass('text-text-primary')
      expect(title).not.toHaveClass('text-gray-900')
    })

    it('should use theme-aware colors for progress ring instead of hardcoded gray-200', () => {
      const { container } = render(
        <RestTimer
          onComplete={mockOnComplete}
          onSkip={mockOnSkip}
          autoStart
          soundEnabled
        />
      )

      const backgroundCircle = container.querySelector('circle.text-gray-200')
      expect(backgroundCircle).toBeNull()

      const themedBackgroundCircle = container.querySelector(
        'circle.text-text-tertiary'
      )
      expect(themedBackgroundCircle).toBeInTheDocument()
    })

    it('should use theme-aware colors for progress ring fill instead of hardcoded blue-500', () => {
      const { container } = render(
        <RestTimer
          onComplete={mockOnComplete}
          onSkip={mockOnSkip}
          autoStart
          soundEnabled
        />
      )

      const progressCircles = container.querySelectorAll('circle')
      const progressCircle = progressCircles[1] // Second circle is the progress
      expect(progressCircle).toHaveClass('text-brand-primary')
      expect(progressCircle).not.toHaveClass('text-blue-500')
    })

    describe('timer color based on remaining time', () => {
      it('should use semantic error color for time <= 5 seconds', () => {
        vi.mocked(useTimer).mockReturnValueOnce({
          timeRemaining: 5,
          isRunning: true,
          progress: 94.44,
          formattedTime: '0:05',
          skip: vi.fn(),
        } as any)

        render(
          <RestTimer
            onComplete={mockOnComplete}
            onSkip={mockOnSkip}
            autoStart
            soundEnabled
          />
        )

        const timer = screen.getByRole('timer')
        expect(timer).toHaveClass('text-error')
        expect(timer).not.toHaveClass('text-red-600')
      })

      it('should use semantic warning color for time <= 10 seconds', () => {
        vi.mocked(useTimer).mockReturnValueOnce({
          timeRemaining: 10,
          isRunning: true,
          progress: 88.89,
          formattedTime: '0:10',
          skip: vi.fn(),
        } as any)

        render(
          <RestTimer
            onComplete={mockOnComplete}
            onSkip={mockOnSkip}
            autoStart
            soundEnabled
          />
        )

        const timer = screen.getByRole('timer')
        expect(timer).toHaveClass('text-warning')
        expect(timer).not.toHaveClass('text-orange-600')
      })

      it('should use theme text color for time > 10 seconds', () => {
        vi.mocked(useTimer).mockReturnValueOnce({
          timeRemaining: 30,
          isRunning: true,
          progress: 66.67,
          formattedTime: '0:30',
          skip: vi.fn(),
        } as any)

        render(
          <RestTimer
            onComplete={mockOnComplete}
            onSkip={mockOnSkip}
            autoStart
            soundEnabled
          />
        )

        const timer = screen.getByRole('timer')
        expect(timer).toHaveClass('text-text-primary')
        expect(timer).not.toHaveClass('text-gray-900')
      })
    })
  })

  describe('Original Functionality', () => {
    it('should render rest timer with correct time', () => {
      render(
        <RestTimer
          onComplete={mockOnComplete}
          onSkip={mockOnSkip}
          autoStart
          soundEnabled
        />
      )

      expect(screen.getByText('0:30')).toBeInTheDocument()
    })

    it('should render rest type text', () => {
      render(
        <RestTimer
          onComplete={mockOnComplete}
          onSkip={mockOnSkip}
          autoStart
          soundEnabled
        />
      )

      expect(screen.getByText('Rest')).toBeInTheDocument()
    })
  })
})
