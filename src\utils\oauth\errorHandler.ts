/**
 * OAuth Error Handler
 *
 * Handles error normalization and categorization for OAuth operations
 */

import type { OAuthError, OAuthProvider } from '@/types/oauth'
import { logger } from '../logger'

/**
 * OAuth error codes
 */
export const ErrorCodes = {
  OAUTH_FAILED: 'oauth_failed',
  NETWORK_ERROR: 'network_error',
  USER_CANCELLED: 'user_cancelled',
  INVALID_TOKEN: 'invalid_token',
  PROVIDER_ERROR: 'provider_error',
  SERVER_ERROR: 'server_error',
} as const

export type ErrorCode = (typeof ErrorCodes)[keyof typeof ErrorCodes]

/**
 * OAuth Error Handler class
 */
export class OAuthErrorHandler {
  /**
   * Type guard for OAuth error
   */
  static isOAuthError(error: unknown): error is OAuthError {
    return (
      typeof error === 'object' &&
      error !== null &&
      'code' in error &&
      'message' in error &&
      'provider' in error
    )
  }

  /**
   * Normalize error messages across providers
   * @param error - Original error
   * @param provider - OAuth provider
   * @returns Normalized OAuth error
   */
  static normalizeError(error: unknown, provider: OAuthProvider): OAuthError {
    // If already an OAuth error, return as-is
    if (this.isOAuthError(error)) {
      return error
    }

    // Handle specific error cases
    let code: OAuthError['code'] = ErrorCodes.OAUTH_FAILED
    let message = 'Authentication failed'
    const details: Record<string, unknown> = {}

    if (error instanceof Error) {
      message = error.message
      details.originalError = error.message
      details.stack = error.stack

      // Categorize error by message content
      code = this.categorizeError(error.message)
      message = this.getUserFriendlyMessage(code, provider)
    } else if (typeof error === 'string') {
      message = error
      details.originalError = error
    } else {
      details.originalError = String(error)
    }

    const normalizedError: OAuthError = {
      code,
      message,
      provider,
      providerError: error,
      details,
    }

    return normalizedError
  }

  /**
   * Categorize error based on message content
   */
  private static categorizeError(message: string): ErrorCode {
    const lowerMessage = message.toLowerCase()

    if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {
      return ErrorCodes.NETWORK_ERROR
    }

    if (
      lowerMessage.includes('cancelled') ||
      lowerMessage.includes('aborted')
    ) {
      return ErrorCodes.USER_CANCELLED
    }

    if (lowerMessage.includes('token') || lowerMessage.includes('invalid')) {
      return ErrorCodes.INVALID_TOKEN
    }

    if (lowerMessage.includes('sdk') || lowerMessage.includes('initialize')) {
      return ErrorCodes.PROVIDER_ERROR
    }

    return ErrorCodes.OAUTH_FAILED
  }

  /**
   * Get user-friendly error message
   */
  private static getUserFriendlyMessage(
    code: ErrorCode,
    provider: OAuthProvider
  ): string {
    switch (code) {
      case ErrorCodes.NETWORK_ERROR:
        return 'Network connection failed. Please check your internet connection.'
      case ErrorCodes.USER_CANCELLED:
        return 'Sign-in was cancelled'
      case ErrorCodes.INVALID_TOKEN:
        return 'Invalid authentication response'
      case ErrorCodes.PROVIDER_ERROR:
        return `Failed to initialize ${provider} sign-in`
      case ErrorCodes.SERVER_ERROR:
        return 'Server error occurred. Please try again later.'
      default:
        return 'Authentication failed'
    }
  }

  /**
   * Create a unified error handler for OAuth operations
   * @param onError - Original error callback
   * @param context - Additional context for error reporting
   */
  static createErrorHandler(
    onError: (error: OAuthError) => void,
    context?: Record<string, unknown>
  ): (error: OAuthError) => void {
    return (error: OAuthError) => {
      // Log error with context
      logger.error('OAuth error occurred', {
        ...error,
        ...context,
        timestamp: new Date().toISOString(),
      })

      // Report to error tracking service (future implementation)
      // ErrorReporter.report(error, context)

      // Call original error handler
      onError(error)
    }
  }
}
