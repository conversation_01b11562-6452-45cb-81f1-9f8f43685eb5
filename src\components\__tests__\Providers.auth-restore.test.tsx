import React from 'react'
import { render, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { Providers } from '../Providers'
import { restoreAuthToken } from '@/utils/auth/tokenRestore'

// Mock dependencies
vi.mock('@/utils/auth/tokenRestore')
vi.mock('@/api/client')
vi.mock('@/utils/validateEnv', () => ({
  logEnvValidation: vi.fn(),
}))
vi.mock('@/utils/debugCommands', () => ({
  initDebugCommands: vi.fn(),
}))
vi.mock('@/utils/clearWorkoutCache', () => ({}))
vi.mock('@/utils/syncManager', () => ({
  SyncManager: {
    init: vi.fn(),
  },
}))

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
  }),
  usePathname: () => '/test',
}))

// Mock auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: Object.assign(
    vi.fn(() => ({
      isAuthenticated: true,
      isLoading: false,
      hasHydrated: true,
    })),
    {
      persist: {
        rehydrate: vi.fn(),
      },
    }
  ),
}))

describe('Providers - Auth Token Restoration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  it('should restore auth token before children can make API calls', async () => {
    // Given a successful token restoration
    vi.mocked(restoreAuthToken).mockResolvedValue(true)

    // When Providers component is rendered
    const { getByText } = render(
      <Providers>
        <div>Test Child</div>
      </Providers>
    )

    // Initially should show loading spinner
    expect(() => getByText('Test Child')).toThrow()

    // Then auth token restoration should be called
    await waitFor(() => {
      expect(restoreAuthToken).toHaveBeenCalled()
    })

    // After token is restored, child should render
    await waitFor(() => {
      expect(getByText('Test Child')).toBeInTheDocument()
    })
  })

  it('should handle auth token restoration failure gracefully', async () => {
    // Given a failed token restoration
    vi.mocked(restoreAuthToken).mockRejectedValue(new Error('Network error'))

    // Track console errors
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    // When Providers component is rendered
    const { getByText } = render(
      <Providers>
        <div>Test Child</div>
      </Providers>
    )

    // Then the child should still render after error
    await waitFor(() => {
      expect(getByText('Test Child')).toBeInTheDocument()
    })

    // And error should be logged
    expect(consoleSpy).toHaveBeenCalledWith(
      '[useAuthTokenRestore] Failed to restore token:',
      expect.any(Error)
    )

    consoleSpy.mockRestore()
  })

  it('should show loading spinner while restoring token', () => {
    // Given a slow token restoration
    vi.mocked(restoreAuthToken).mockImplementation(
      () => new Promise((resolve) => setTimeout(() => resolve(true), 1000))
    )

    // When Providers component is rendered
    const { container } = render(
      <Providers>
        <div>Test Child</div>
      </Providers>
    )

    // Then loading spinner should be shown
    const spinner = container.querySelector('.animate-spin')
    expect(spinner).toBeInTheDocument()
    expect(spinner).toHaveClass('border-blue-600')
  })
})
