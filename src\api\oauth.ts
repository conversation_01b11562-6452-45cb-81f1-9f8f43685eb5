import { apiClient } from './client'
import { logger } from '@/utils/logger'
import type { LoginSuccessResult, BooleanModel } from '@/types'

/**
 * OAuth authentication API methods
 * Handles OAuth login for Google and Apple
 */
export const oauthApi = {
  /**
   * OAuth login with Google (using legacy mobile app format)
   * @param idToken Google ID token from OAuth flow
   * @param userData User data extracted from token
   * @returns Login success result with tokens
   */
  async googleLogin(
    idToken: string,
    userData: {
      email: string
      name?: string
      bodyWeight?: string
      massUnit?: string
    }
  ): Promise<LoginSuccessResult> {
    // Use form-urlencoded format to match mobile app
    const formData = new URLSearchParams()
    formData.append('grant_type', 'google')
    formData.append('accesstoken', idToken)
    formData.append('provider', 'google')
    formData.append('email', userData.email)
    formData.append('name', userData.name || '')
    formData.append('bodyweight', userData.bodyWeight || '')
    formData.append('massunit', userData.massUnit || '')
    formData.append('userid', '') // Empty for Google

    try {
      const response = await apiClient.post<LoginSuccessResult>(
        '/token',
        formData.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      )

      // Debug logging in development
      if (process.env.NODE_ENV === 'development') {
        logger.log('[OAuth API] Google OAuth response:', {
          userName: response.data.userName,
          hasToken: !!response.data.access_token,
        })
      }

      return response.data
    } catch (error) {
      logger.error('[OAuth API] Google OAuth error:', error)
      throw error
    }
  },

  /**
   * OAuth login with Apple (using legacy mobile app format)
   * @param appleUserId Apple User ID
   * @param userData User data from Apple
   * @returns Login success result with tokens
   */
  async appleLogin(
    appleUserId: string,
    userData: {
      email?: string
      name?: string
      bodyWeight?: string
      massUnit?: string
    }
  ): Promise<LoginSuccessResult> {
    // Use form-urlencoded format to match mobile app
    const formData = new URLSearchParams()
    formData.append('grant_type', 'google') // Yes, "google" for Apple too (legacy)
    formData.append('accesstoken', '') // Empty for Apple
    formData.append('provider', 'google') // Still "google" for Apple
    formData.append('email', userData.email || '')
    formData.append('name', userData.name || '')
    formData.append('bodyweight', userData.bodyWeight || '')
    formData.append('massunit', userData.massUnit || '')
    formData.append('userid', appleUserId) // Apple User ID

    try {
      const response = await apiClient.post<LoginSuccessResult>(
        '/token',
        formData.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      )

      // Debug logging in development
      if (process.env.NODE_ENV === 'development') {
        logger.log('[OAuth API] Apple OAuth response:', {
          userName: response.data.userName,
          hasToken: !!response.data.access_token,
        })
      }

      return response.data
    } catch (error) {
      logger.error('[OAuth API] Apple OAuth error:', error)
      throw error
    }
  },

  /**
   * Check if Apple ID already exists
   * @param appleId Apple User ID to check
   * @returns Boolean result
   */
  async isAppleIdAlreadyExist(appleId: string): Promise<BooleanModel> {
    const response = await apiClient.post<BooleanModel>(
      '/api/Account/IsEmailAlreadyExistbyAppleId',
      {
        AppleId: appleId,
      }
    )
    return response.data
  },
}
