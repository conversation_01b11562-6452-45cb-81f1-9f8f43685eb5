import { test, expect } from '@playwright/test'

test.describe('<PERSON>rror <PERSON> @critical', () => {
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456'
  }

  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login')
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)
    await page.getByRole('button', { name: /log in/i }).click()
    await page.waitForURL('/program')
  })

  test.describe('Component Error Recovery', () => {
    test('should catch and display component errors gracefully @critical', async ({ page }) => {
      // Navigate to workout
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Inject error into component
      await page.evaluate(() => {
        // Force an error in React component
        const event = new Event('error')
        Object.defineProperty(event, 'error', {
          value: new Error('Test component error'),
          writable: false
        })
        window.dispatchEvent(event)
      })

      // Should show error boundary UI
      const errorBoundary = page.locator('[data-testid="error-boundary"]')
      if (await errorBoundary.isVisible({ timeout: 3000 })) {
        // Should show user-friendly message
        await expect(errorBoundary).toContainText(/something went wrong|error|problem/i)

        // Should have recovery action
        const retryButton = errorBoundary.locator('button:has-text("Try again"), button:has-text("Reload")')
        await expect(retryButton).toBeVisible()

        // Click retry
        await retryButton.click()

        // Should recover
        await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()
      }
    })

    test('should handle async errors in data loading @critical', async ({ page }) => {
      // Navigate to workout
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Intercept API calls and make them fail with error
      await page.route('**/api/workout/**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal Server Error' })
        })
      })

      // Trigger data reload
      await page.reload()

      // Should show error state
      const errorState = page.locator('[data-testid="error-state"], [data-testid="error-message"]')
      await expect(errorState).toBeVisible()
      await expect(errorState).toContainText(/error|failed|problem/i)

      // Should not crash the entire app
      await expect(page.locator('nav')).toBeVisible()
      
      // Should provide retry option
      const retryButton = page.getByRole('button', { name: /retry|try again/i })
      await expect(retryButton).toBeVisible()
    })
  })

  test.describe('Network Error Handling', () => {
    test('should handle network timeouts gracefully @critical', async ({ page }) => {
      // Navigate to workout
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Simulate network timeout
      await page.route('**/api/**', async route => {
        // Wait longer than typical timeout
        await new Promise(resolve => setTimeout(resolve, 35000))
        route.abort()
      })

      // Try to load exercise
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Should show timeout error
      const timeoutError = page.locator('[data-testid="error-message"]')
      await expect(timeoutError).toBeVisible({ timeout: 40000 })
      await expect(timeoutError).toContainText(/timeout|taking too long|slow/i)

      // App should remain functional
      await page.unroute('**/api/**')
      const backButton = page.locator('[data-testid="back-button"]')
      if (await backButton.isVisible()) {
        await backButton.click()
        await expect(page).toHaveURL('/workout')
      }
    })

    test('should handle malformed API responses @critical', async ({ page }) => {
      // Navigate to workout
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Return malformed JSON
      await page.route('**/api/workout/**', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: 'This is not valid JSON{]'
        })
      })

      // Trigger API call
      await page.reload()

      // Should handle parse error gracefully
      const errorMessage = page.locator('[data-testid="error-message"]')
      await expect(errorMessage).toBeVisible()

      // Should not show technical details to user
      const errorText = await errorMessage.textContent()
      expect(errorText).not.toContain('JSON')
      expect(errorText).not.toContain('parse')
      expect(errorText).toMatch(/error|problem|wrong/i)
    })
  })

  test.describe('Form Error Boundaries', () => {
    test('should handle form submission errors @critical', async ({ page }) => {
      // Navigate to exercise
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Fill form
      await page.locator('input[name="weight"]').fill('999999999')
      await page.locator('input[name="reps"]').fill('999999999')

      // Make save endpoint fail
      await page.route('**/SaveWorkoutLog**', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Invalid data' })
        })
      })

      // Submit form
      await page.getByRole('button', { name: /save|done/i }).click()

      // Should show error message
      const formError = page.locator('[role="alert"], [data-testid="form-error"]')
      await expect(formError).toBeVisible()

      // Form should remain functional
      await expect(page.locator('input[name="weight"]')).toBeEnabled()
      await expect(page.locator('input[name="reps"]')).toBeEnabled()

      // Data should be preserved
      await expect(page.locator('input[name="weight"]')).toHaveValue('999999999')
    })

    test('should validate input boundaries @critical', async ({ page }) => {
      // Navigate to exercise
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Test negative values
      await page.locator('input[name="weight"]').fill('-10')
      await page.locator('input[name="reps"]').fill('-5')
      await page.getByRole('button', { name: /save|done/i }).click()

      // Should show validation error
      const validationError = page.locator('[data-testid="validation-error"]')
      await expect(validationError).toBeVisible()

      // Test extremely large values
      await page.locator('input[name="weight"]').fill('999999999999999')
      await page.getByRole('button', { name: /save|done/i }).click()

      // Should show appropriate error
      await expect(validationError).toBeVisible()
    })
  })

  test.describe('Global Error Handling', () => {
    test('should catch unhandled promise rejections @critical', async ({ page }) => {
      // Listen for console errors
      const consoleErrors: string[] = []
      page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text())
        }
      })

      // Navigate to workout
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Inject unhandled promise rejection
      await page.evaluate(() => {
        Promise.reject(new Error('Unhandled test error'))
      })

      // Wait a moment
      await page.waitForTimeout(1000)

      // App should still be functional
      await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

      // Should not show error to user (handled silently)
      const userVisibleError = page.locator('[role="alert"][data-severity="error"]')
      const errorCount = await userVisibleError.count()
      expect(errorCount).toBe(0)
    })

    test('should handle memory errors gracefully @critical', async ({ page }) => {
      // Navigate to workout
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Try to allocate large amount of memory
      const memoryError = await page.evaluate(() => {
        try {
          const hugeArray = new Array(1e8).fill('x'.repeat(1000))
          return false
        } catch (e) {
          return true
        }
      })

      if (memoryError) {
        // App should still be functional after memory error
        await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()
      }
    })
  })

  test.describe('Error Logging and Reporting', () => {
    test('should not expose sensitive data in errors @critical', async ({ page }) => {
      // Set up console listener
      const consoleMessages: string[] = []
      page.on('console', msg => {
        consoleMessages.push(msg.text())
      })

      // Navigate to workout with API error
      await page.route('**/api/workout/**', route => {
        route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ 
            error: 'Unauthorized',
            token: 'secret-token-12345',
            userId: 'user-id-67890'
          })
        })
      })

      await page.getByRole('button', { name: /start workout|today's workout/i }).click()

      // Wait for error
      await page.waitForSelector('[data-testid="error-message"]')

      // Check visible error message doesn't contain sensitive data
      const errorText = await page.locator('[data-testid="error-message"]').textContent()
      expect(errorText).not.toContain('secret-token')
      expect(errorText).not.toContain('user-id')
      expect(errorText).not.toContain('12345')
      expect(errorText).not.toContain('67890')

      // Console logs should also not contain sensitive data
      const sensitiveDataInConsole = consoleMessages.some(msg => 
        msg.includes('secret-token') || msg.includes('user-id')
      )
      expect(sensitiveDataInConsole).toBeFalsy()
    })
  })

  test.describe('Recovery Actions', () => {
    test('should provide appropriate recovery options @critical', async ({ page }) => {
      // Navigate to workout
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')

      // Cause different types of errors and check recovery options
      
      // 1. Network error
      await page.route('**/api/**', route => route.abort())
      await page.reload()

      const networkError = page.locator('[data-testid="error-message"]')
      await expect(networkError).toBeVisible()
      
      // Should have retry option for network errors
      const retryButton = page.getByRole('button', { name: /retry|try again/i })
      await expect(retryButton).toBeVisible()

      // Clear route
      await page.unroute('**/api/**')

      // 2. Navigate to home option for severe errors
      await page.evaluate(() => {
        throw new Error('Severe application error')
      })

      const severeError = page.locator('[data-testid="error-boundary"]')
      if (await severeError.isVisible({ timeout: 3000 })) {
        const homeButton = page.getByRole('button', { name: /home|back to program/i })
        await expect(homeButton).toBeVisible()
      }
    })

    test('should maintain user context after error recovery @critical', async ({ page }) => {
      // Navigate to exercise
      await page.getByRole('button', { name: /start workout|today's workout/i }).click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Fill some data
      await page.locator('input[name="weight"]').fill('100')
      await page.locator('input[name="reps"]').fill('10')

      // Cause an error
      await page.route('**/api/**', route => route.abort())
      await page.getByRole('button', { name: /save|done/i }).click()

      // Error should appear
      await expect(page.locator('[role="alert"]')).toBeVisible()

      // Fix the error
      await page.unroute('**/api/**')

      // Retry
      const retryButton = page.getByRole('button', { name: /retry|try again/i })
      if (await retryButton.isVisible()) {
        await retryButton.click()
      }

      // Data should still be in form
      await expect(page.locator('input[name="weight"]')).toHaveValue('100')
      await expect(page.locator('input[name="reps"]')).toHaveValue('10')

      // User should still be logged in
      await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible()
    })
  })
})