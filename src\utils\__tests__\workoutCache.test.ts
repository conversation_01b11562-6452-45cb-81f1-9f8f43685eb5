import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  WorkoutCache,
  WORKOUT_CACHE_KEY,
  CACHE_VERSION,
  MAX_CACHE_SIZE_KB,
} from '../workoutCache'
import type { WorkoutTemplateGroupModel } from '@/types'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock as Storage

const mockWorkoutData: WorkoutTemplateGroupModel[] = [
  {
    Id: 1,
    Label: 'Test Program',
    WorkoutTemplates: [
      {
        Id: 1,
        Label: 'Upper Body',
        IsSystemExercise: true,
        UserId: '',
        Exercises: [
          { Id: 1, Label: 'Bench Press' },
          { Id: 2, Label: 'Shoulder Press' },
        ],
        WorkoutSettingsModel: {},
      },
    ],
    IsFeaturedProgram: false,
    UserId: '',
    IsSystemExercise: true,
    RequiredWorkoutToLevelUp: 5,
    ProgramId: 1,
  },
]

describe('WorkoutCache', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.clear()
  })

  describe('get', () => {
    it('should return null when cache is empty', () => {
      localStorageMock.getItem.mockReturnValue(null)

      const result = WorkoutCache.get()

      expect(result).toBeNull()
      expect(localStorageMock.getItem).toHaveBeenCalledWith(WORKOUT_CACHE_KEY)
    })

    it('should return cached data when valid', () => {
      const cachedData = {
        version: CACHE_VERSION,
        timestamp: Date.now(),
        data: mockWorkoutData,
      }
      localStorageMock.getItem.mockReturnValue(JSON.stringify(cachedData))

      const result = WorkoutCache.get()

      expect(result).toEqual(mockWorkoutData)
    })

    it('should return null when cache version is outdated', () => {
      const cachedData = {
        version: CACHE_VERSION - 1,
        timestamp: Date.now(),
        data: mockWorkoutData,
      }
      localStorageMock.getItem.mockReturnValue(JSON.stringify(cachedData))

      const result = WorkoutCache.get()

      expect(result).toBeNull()
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(
        WORKOUT_CACHE_KEY
      )
    })

    it('should return null when cache is corrupted', () => {
      localStorageMock.getItem.mockReturnValue('invalid json')

      const result = WorkoutCache.get()

      expect(result).toBeNull()
    })

    it('should return null when localStorage throws', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('Storage error')
      })

      const result = WorkoutCache.get()

      expect(result).toBeNull()
    })
  })

  describe('set', () => {
    it('should store workout data with metadata', () => {
      const beforeTime = Date.now()

      WorkoutCache.set(mockWorkoutData)

      expect(localStorageMock.setItem).toHaveBeenCalledTimes(1)
      const [key, value] = localStorageMock.setItem.mock.calls[0]
      expect(key).toBe(WORKOUT_CACHE_KEY)

      const storedData = JSON.parse(value)
      expect(storedData.version).toBe(CACHE_VERSION)
      expect(storedData.timestamp).toBeGreaterThanOrEqual(beforeTime)
      expect(storedData.timestamp).toBeLessThanOrEqual(Date.now())
      expect(storedData.data).toEqual(mockWorkoutData)
    })

    it('should not store if data exceeds size limit', () => {
      // Create large data that exceeds 100KB
      const largeWorkoutData = Array(1000)
        .fill(null)
        .map((_, i) => ({
          ...mockWorkoutData[0],
          Id: i,
          WorkoutTemplates: Array(100).fill(
            mockWorkoutData[0].WorkoutTemplates[0]
          ),
        }))

      WorkoutCache.set(largeWorkoutData)

      expect(localStorageMock.setItem).not.toHaveBeenCalled()
    })

    it('should handle localStorage quota exceeded error', () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('QuotaExceededError')
      })

      expect(() => WorkoutCache.set(mockWorkoutData)).not.toThrow()
    })

    it('should not store null or empty data', () => {
      WorkoutCache.set(null)
      expect(localStorageMock.setItem).not.toHaveBeenCalled()

      WorkoutCache.set([])
      expect(localStorageMock.setItem).not.toHaveBeenCalled()
    })
  })

  describe('clear', () => {
    it('should remove cache from localStorage', () => {
      WorkoutCache.clear()

      expect(localStorageMock.removeItem).toHaveBeenCalledWith(
        WORKOUT_CACHE_KEY
      )
    })

    it('should handle errors gracefully', () => {
      localStorageMock.removeItem.mockImplementation(() => {
        throw new Error('Storage error')
      })

      expect(() => WorkoutCache.clear()).not.toThrow()
    })
  })

  describe('isExpired', () => {
    it('should return false for recent cache', () => {
      const recentTimestamp = Date.now() - 5 * 60 * 1000 // 5 minutes ago

      expect(WorkoutCache.isExpired(recentTimestamp)).toBe(false)
    })

    it('should return true for old cache', () => {
      const oldTimestamp = Date.now() - 25 * 60 * 60 * 1000 // 25 hours ago

      expect(WorkoutCache.isExpired(oldTimestamp)).toBe(true)
    })

    it('should use custom expiry time', () => {
      const timestamp = Date.now() - 2 * 60 * 60 * 1000 // 2 hours ago
      const oneHourExpiry = 60 * 60 * 1000 // 1 hour

      expect(WorkoutCache.isExpired(timestamp, oneHourExpiry)).toBe(true)
      expect(WorkoutCache.isExpired(timestamp, 3 * 60 * 60 * 1000)).toBe(false)
    })
  })

  describe('migration', () => {
    it('should handle migration from old cache format', () => {
      // Old format without version
      const oldCachedData = {
        timestamp: Date.now(),
        data: mockWorkoutData,
      }
      localStorageMock.getItem.mockReturnValue(JSON.stringify(oldCachedData))

      const result = WorkoutCache.get()

      // Should clear old cache and return null
      expect(result).toBeNull()
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(
        WORKOUT_CACHE_KEY
      )
    })
  })

  describe('size calculation', () => {
    it('should calculate size correctly', () => {
      const testData = { test: 'data' }
      const jsonString = JSON.stringify(testData)
      const expectedSizeKB = jsonString.length / 1024

      const size = WorkoutCache.calculateSizeKB(testData)

      expect(size).toBeCloseTo(expectedSizeKB, 2)
    })

    it('should respect MAX_CACHE_SIZE_KB constant', () => {
      expect(MAX_CACHE_SIZE_KB).toBe(100)
    })
  })
})
