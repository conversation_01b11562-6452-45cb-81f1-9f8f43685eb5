import React, { useEffect, useState } from 'react'

export interface PageTransitionProps {
  /** Children to render */
  children: React.ReactNode
  /** Transition duration in milliseconds */
  duration?: number
  /** Additional CSS classes */
  className?: string
}

export function PageTransition({
  children,
  duration = 200,
  className = '',
}: PageTransitionProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Trigger fade in after mount
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, 10)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div
      className={`transition-opacity ${className}`}
      style={{
        transitionDuration: `${duration}ms`,
        opacity: isVisible ? 1 : 0,
      }}
    >
      {children}
    </div>
  )
}
