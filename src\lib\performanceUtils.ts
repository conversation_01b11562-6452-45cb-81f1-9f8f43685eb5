/**
 * Utility function to measure execution time
 */
export function measureDuration<T>(
  _name: string,
  fn: () => T
): { result: T; duration: number } {
  const start = performance.now()
  const result = fn()
  const duration = performance.now() - start

  // Performance duration tracked internally

  return { result, duration }
}

/**
 * Log all performance metrics (development only)
 */
export function logPerformanceMetrics(): void {
  if (process.env.NODE_ENV !== 'development') {
    return
  }

  const entries = performance.getEntriesByType('measure')

  if (entries.length === 0) {
    // No entries to process
  }

  // Performance metrics available via getEntries()
}

/**
 * Utility functions for component-level performance tracking
 */
export function measureComponentRender(componentName: string) {
  const startTime = performance.now()

  return () => {
    const endTime = performance.now()
    const renderTime = endTime - startTime

    if (process.env.NODE_ENV === 'development' && renderTime > 16) {
      console.warn(
        `[Performance] ${componentName} render took ${renderTime.toFixed(2)}ms`
      )
    }

    return renderTime
  }
}
