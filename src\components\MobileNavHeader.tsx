'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { UserMenu } from './UserMenu'
import { KebabMenuIcon } from './icons'

interface MobileNavHeaderProps {
  className?: string
}

export function MobileNavHeader({ className = '' }: MobileNavHeaderProps) {
  const { user, isAuthenticated } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  // Don't render if not authenticated
  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <>
      <header
        className={`fixed top-0 left-0 right-0 z-40 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 ${className}`}
      >
        <div className="flex items-center justify-between h-14 px-4">
          {/* App Name */}
          <div className="flex items-center">
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
              Dr<PERSON> <PERSON><PERSON><PERSON> X
            </h1>
          </div>

          {/* User Menu Trigger */}
          <button
            onClick={() => setIsMenuOpen(true)}
            className="flex items-center p-2 -mr-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            aria-label="Open user menu"
          >
            <KebabMenuIcon
              className="text-gray-600 dark:text-gray-400"
              size={20}
            />
          </button>
        </div>
      </header>

      {/* User Menu */}
      <UserMenu
        isOpen={isMenuOpen}
        onClose={() => setIsMenuOpen(false)}
        user={user}
      />

      {/* Spacer to prevent content from going under fixed header */}
      <div className="h-14" />
    </>
  )
}
