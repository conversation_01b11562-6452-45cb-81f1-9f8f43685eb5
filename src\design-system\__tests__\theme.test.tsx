import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, renderHook, act, screen } from '@testing-library/react'
import { ThemeProvider, useTheme } from '../theme'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  clear: vi.fn(),
}
global.localStorage = localStorageMock as any

describe('ThemeProvider', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should provide theme context to children', () => {
    function TestComponent() {
      const { theme } = useTheme()
      return <div>Current theme: {theme}</div>
    }

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    )

    expect(screen.getByText(/Current theme:/)).toBeInTheDocument()
  })

  it('should throw error when useTheme is used outside ThemeProvider', () => {
    // Suppress console.error for this test
    const originalError = console.error
    console.error = vi.fn()

    expect(() => {
      renderHook(() => useTheme())
    }).toThrow('useTheme must be used within a ThemeProvider')

    console.error = originalError
  })

  it('should default to subtle-depth theme', () => {
    const { result } = renderHook(() => useTheme(), {
      wrapper: ThemeProvider,
    })

    expect(result.current.theme).toBe('subtle-depth')
  })

  it('should allow theme switching', () => {
    const { result } = renderHook(() => useTheme(), {
      wrapper: ThemeProvider,
    })

    act(() => {
      result.current.setTheme('flat-bold')
    })

    expect(result.current.theme).toBe('flat-bold')
  })

  it('should persist theme to localStorage', () => {
    const { result } = renderHook(() => useTheme(), {
      wrapper: ThemeProvider,
    })

    act(() => {
      result.current.setTheme('glassmorphism')
    })

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'dr-muscle-x-theme',
      'glassmorphism'
    )
  })

  it('should load theme from localStorage on mount', () => {
    localStorageMock.getItem.mockReturnValue('ultra-minimal')

    const { result } = renderHook(() => useTheme(), {
      wrapper: ThemeProvider,
    })

    expect(result.current.theme).toBe('ultra-minimal')
  })

  it('should update CSS variables when theme changes', () => {
    const { result } = renderHook(() => useTheme(), {
      wrapper: ThemeProvider,
    })

    act(() => {
      result.current.setTheme('flat-bold')
    })

    // CSS variables should be updated on document root
    expect(document.documentElement.dataset.theme).toBe('flat-bold')
  })

  it('should validate theme variant before setting', () => {
    // Clear localStorage to ensure default theme
    localStorageMock.getItem.mockReturnValue(null)

    const { result } = renderHook(() => useTheme(), {
      wrapper: ThemeProvider,
    })

    const initialTheme = result.current.theme

    act(() => {
      // @ts-expect-error - Testing invalid theme
      result.current.setTheme('invalid-theme')
    })

    // Should remain on initial theme
    expect(result.current.theme).toBe(initialTheme)
  })
})
