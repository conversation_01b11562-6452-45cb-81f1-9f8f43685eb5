import { describe, it, expect } from 'vitest'
import fs from 'fs'
import path from 'path'
// Sharp is only used in tests, not in production code
// eslint-disable-next-line import/no-extraneous-dependencies
import sharp from 'sharp'

// Types for manifest
interface ManifestIcon {
  src: string
  sizes: string
  type: string
  purpose?: string
}

interface Manifest {
  name: string
  short_name: string
  icons: ManifestIcon[]
  theme_color: string
  background_color: string
  display: string
  orientation: string
}

describe('PWA Icons Cross-Platform Testing', () => {
  const iconsDir = path.join(process.cwd(), 'public', 'icons')
  const requiredSizes = [72, 96, 128, 144, 152, 192, 384, 512]

  describe('Icon File Existence', () => {
    it('should have all regular icon sizes', () => {
      requiredSizes.forEach((size) => {
        const iconPath = path.join(iconsDir, `icon-${size}x${size}.png`)
        expect(fs.existsSync(iconPath)).toBe(true)
      })
    })

    it('should have all maskable icon sizes', () => {
      requiredSizes.forEach((size) => {
        const iconPath = path.join(
          iconsDir,
          `icon-maskable-${size}x${size}.png`
        )
        expect(fs.existsSync(iconPath)).toBe(true)
      })
    })

    it('should have apple-touch-icon', () => {
      const appleTouchIcon = path.join(
        process.cwd(),
        'public',
        'apple-touch-icon.png'
      )
      expect(fs.existsSync(appleTouchIcon)).toBe(true)
    })

    it('should have favicon files', () => {
      const favicon16 = path.join(process.cwd(), 'public', 'favicon-16x16.png')
      const favicon32 = path.join(process.cwd(), 'public', 'favicon-32x32.png')

      expect(fs.existsSync(favicon16)).toBe(true)
      expect(fs.existsSync(favicon32)).toBe(true)
    })
  })

  describe('Icon Dimensions and Format', () => {
    it.each(requiredSizes)(
      'should have correct dimensions for %ipx regular icon',
      async (size) => {
        const iconPath = path.join(iconsDir, `icon-${size}x${size}.png`)
        const metadata = await sharp(iconPath).metadata()

        expect(metadata.width).toBe(size)
        expect(metadata.height).toBe(size)
        expect(metadata.format).toBe('png')
      }
    )

    it.each(requiredSizes)(
      'should have correct dimensions for %ipx maskable icon',
      async (size) => {
        const iconPath = path.join(
          iconsDir,
          `icon-maskable-${size}x${size}.png`
        )
        const metadata = await sharp(iconPath).metadata()

        expect(metadata.width).toBe(size)
        expect(metadata.height).toBe(size)
        expect(metadata.format).toBe('png')
      }
    )

    it('should have correct dimensions for apple-touch-icon', async () => {
      const iconPath = path.join(
        process.cwd(),
        'public',
        'apple-touch-icon.png'
      )
      const metadata = await sharp(iconPath).metadata()

      expect(metadata.width).toBe(180)
      expect(metadata.height).toBe(180)
      expect(metadata.format).toBe('png')
    })
  })

  describe('Maskable Icon Safe Zone', () => {
    it('should have adequate padding for maskable icons', async () => {
      // Test one maskable icon to verify padding implementation
      const iconPath = path.join(iconsDir, 'icon-maskable-192x192.png')
      const { data, info } = await sharp(iconPath)
        .raw()
        .toBuffer({ resolveWithObject: true })

      // Check that the icon has content (not all transparent)
      expect(data.length).toBeGreaterThan(0)
      expect(info.width).toBe(192)
      expect(info.height).toBe(192)

      // The safe zone should be approximately 80% of the icon
      // This is a basic check - in production you'd analyze the actual pixels
      const expectedSafeZone = Math.floor(192 * 0.8)
      expect(expectedSafeZone).toBeLessThan(192)
    })
  })

  describe('iOS Safari Requirements', () => {
    it('should have apple-touch-icon with correct properties', async () => {
      const iconPath = path.join(
        process.cwd(),
        'public',
        'apple-touch-icon.png'
      )
      const metadata = await sharp(iconPath).metadata()

      // iOS requires 180x180 for modern devices
      expect(metadata.width).toBe(180)
      expect(metadata.height).toBe(180)

      // Should be PNG format
      expect(metadata.format).toBe('png')

      // Should have proper channels (RGBA or RGB)
      expect(metadata.channels).toBeGreaterThanOrEqual(3)
    })

    it('should support iOS home screen installation', () => {
      // Check that manifest and meta tags are properly configured
      const manifestPath = path.join(process.cwd(), 'public', 'manifest.json')
      const manifest = JSON.parse(
        fs.readFileSync(manifestPath, 'utf8')
      ) as Manifest

      // iOS specific checks
      expect(manifest.display).toBe('standalone')
      expect(manifest.orientation).toBe('portrait-primary')

      // Should have at least one 180x180 or larger icon
      const largeIcons = manifest.icons.filter((icon: ManifestIcon) => {
        const size = parseInt(icon.sizes.split('x')[0])
        return size >= 180
      })
      expect(largeIcons.length).toBeGreaterThan(0)
    })
  })

  describe('Android Requirements', () => {
    it('should have correct maskable icons for Android', () => {
      const manifestPath = path.join(process.cwd(), 'public', 'manifest.json')
      const manifest = JSON.parse(
        fs.readFileSync(manifestPath, 'utf8')
      ) as Manifest

      // Android requires maskable icons
      const maskableIcons = manifest.icons.filter(
        (icon: ManifestIcon) => icon.purpose === 'maskable'
      )

      expect(maskableIcons.length).toBeGreaterThan(0)

      // Should have at least 192x192 maskable icon
      const has192Maskable = maskableIcons.some(
        (icon: ManifestIcon) => icon.sizes === '192x192'
      )
      expect(has192Maskable).toBe(true)
    })

    it('should have proper theme and background colors', () => {
      const manifestPath = path.join(process.cwd(), 'public', 'manifest.json')
      const manifest = JSON.parse(
        fs.readFileSync(manifestPath, 'utf8')
      ) as Manifest

      expect(manifest.theme_color).toBeDefined()
      expect(manifest.background_color).toBeDefined()

      // Colors should be valid hex
      expect(manifest.theme_color).toMatch(/^#[0-9A-Fa-f]{6}$/)
      expect(manifest.background_color).toMatch(/^#[0-9A-Fa-f]{6}$/)
    })
  })

  describe('Windows Tile Support', () => {
    it('should have large icons for Windows tiles', () => {
      const manifestPath = path.join(process.cwd(), 'public', 'manifest.json')
      const manifest = JSON.parse(
        fs.readFileSync(manifestPath, 'utf8')
      ) as Manifest

      // Windows tiles prefer 144x144 and larger
      const tileIcons = manifest.icons.filter((icon: ManifestIcon) => {
        const size = parseInt(icon.sizes.split('x')[0])
        return size >= 144
      })

      expect(tileIcons.length).toBeGreaterThan(0)
    })
  })

  describe('Icon Quality and Optimization', () => {
    it('should have reasonable file sizes', async () => {
      const maxSizes: Record<number, number> = {
        72: 10 * 1024, // 10KB
        96: 15 * 1024, // 15KB
        128: 20 * 1024, // 20KB
        144: 25 * 1024, // 25KB
        152: 25 * 1024, // 25KB
        192: 35 * 1024, // 35KB
        384: 80 * 1024, // 80KB
        512: 120 * 1024, // 120KB
      }

      requiredSizes.forEach((size) => {
        const iconPath = path.join(iconsDir, `icon-${size}x${size}.png`)
        const stats = fs.statSync(iconPath)

        expect(stats.size).toBeLessThan(maxSizes[size] || 200 * 1024)
      })
    })

    it('should have proper transparency support', async () => {
      // Check one icon for alpha channel
      const iconPath = path.join(iconsDir, 'icon-192x192.png')
      const metadata = await sharp(iconPath).metadata()

      // PNG should support transparency
      expect(metadata.channels).toBe(4) // RGBA
    })
  })

  describe('PWA Installation Requirements', () => {
    it('should meet minimum PWA icon requirements', () => {
      const manifestPath = path.join(process.cwd(), 'public', 'manifest.json')
      const manifest = JSON.parse(
        fs.readFileSync(manifestPath, 'utf8')
      ) as Manifest

      // PWA requires at least 192x192 and 512x512
      const has192 = manifest.icons.some(
        (icon: ManifestIcon) => icon.sizes === '192x192'
      )
      const has512 = manifest.icons.some(
        (icon: ManifestIcon) => icon.sizes === '512x512'
      )

      expect(has192).toBe(true)
      expect(has512).toBe(true)

      // All icons should have proper type
      manifest.icons.forEach((icon: ManifestIcon) => {
        expect(icon.type).toBe('image/png')
      })
    })

    it('should have both any and maskable purpose icons', () => {
      const manifestPath = path.join(process.cwd(), 'public', 'manifest.json')
      const manifest = JSON.parse(
        fs.readFileSync(manifestPath, 'utf8')
      ) as Manifest

      const purposes = new Set(
        manifest.icons.map((icon: ManifestIcon) => icon.purpose)
      )

      expect(purposes.has('any')).toBe(true)
      expect(purposes.has('maskable')).toBe(true)
    })
  })
})
