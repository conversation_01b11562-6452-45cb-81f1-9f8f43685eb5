import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import axios from 'axios'

// Mock axios
vi.mock('axios')

// Mock auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: {
    getState: vi.fn(() => ({
      token: null,
      refreshToken: null,
      updateTokens: vi.fn(),
      logout: vi.fn(),
    })),
  },
}))

// Mock global fetch
global.fetch = vi.fn()

describe('API Client', () => {
  const mockAxiosInstance = {
    interceptors: {
      request: { use: vi.fn(), eject: vi.fn() },
      response: { use: vi.fn(), eject: vi.fn() },
    },
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    request: vi.fn(),
    defaults: {
      headers: {
        common: {},
      },
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    ;(axios.create as any).mockReturnValue(mockAxiosInstance)

    // Import the module after mocks are set up
    vi.resetModules()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Client Configuration', () => {
    it('should create axios instance with correct base configuration', async () => {
      await import('@/api/client')

      expect(axios.create).toHaveBeenCalledWith({
        baseURL: expect.stringContaining('https://'),
        timeout: expect.any(Number),
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
        }),
        withCredentials: true,
      })
    })

    it('should set up request and response interceptors', async () => {
      await import('@/api/client')

      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled()
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled()
    })
  })

  describe('Auth Token Management', () => {
    it('should set auth token in headers', async () => {
      const { setAuthToken } = await import('@/api/client')
      const token = 'test-auth-token'
      setAuthToken(token)

      expect(mockAxiosInstance.defaults.headers.common['Authorization']).toBe(
        `Bearer ${token}`
      )
    })

    it('should clear auth token from headers', async () => {
      const { setAuthToken, clearAuthToken } = await import('@/api/client')
      setAuthToken('test-token')
      clearAuthToken()

      expect(
        mockAxiosInstance.defaults.headers.common['Authorization']
      ).toBeUndefined()
    })
  })

  describe('Request Interceptor', () => {
    it('should attempt to fetch auth token if not set', async () => {
      // Mock the token endpoint
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ authenticated: true, token: 'fetched-token' }),
      })

      await import('@/api/client')

      // Get the request interceptor
      const requestInterceptor =
        mockAxiosInstance.interceptors.request.use.mock.calls[0][0]

      const config = { headers: {}, withCredentials: false }
      const result = await requestInterceptor(config)

      // Should attempt to fetch token
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/token', {
        credentials: 'include',
      })

      // Should set the Authorization header
      expect(result.headers['Authorization']).toBe('Bearer fetched-token')
    })

    it('should not add auth header manually (relies on HttpOnly cookies)', async () => {
      await import('@/api/client')

      const requestInterceptor =
        mockAxiosInstance.interceptors.request.use.mock.calls[0][0]
      const config = { headers: {} }
      const result = await requestInterceptor(config)

      // Should not add Authorization header manually
      expect(result.headers.Authorization).toBeUndefined()
    })
  })

  describe('Response Interceptor', () => {
    it('should pass through successful responses', async () => {
      await import('@/api/client')

      const responseInterceptor =
        mockAxiosInstance.interceptors.response.use.mock.calls[0][0]
      const mockResponse = { data: { Result: true }, status: 200 }

      const result = await responseInterceptor(mockResponse)

      expect(result).toBe(mockResponse)
    })

    it('should handle 401 errors by redirecting to login', async () => {
      // Mock fetch for logout
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      })

      // Mock window.location.href
      delete (window as any).location
      window.location = { href: '' } as any

      await import('@/api/client')

      const errorInterceptor =
        mockAxiosInstance.interceptors.response.use.mock.calls[0][1]
      const originalRequest = {
        _retry: false,
        headers: {},
      }
      const error = {
        response: { status: 401 },
        config: originalRequest,
      }

      await expect(errorInterceptor(error)).rejects.toThrow(
        'Authentication required. Please login again.'
      )

      // Verify NO refresh token was attempted (endpoint doesn't exist)
      expect(mockAxiosInstance.post).not.toHaveBeenCalled()

      // Verify logout was called
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/auth/exchange',
        expect.objectContaining({
          method: 'DELETE',
          credentials: 'include',
        })
      )

      // Verify redirect to login
      expect(window.location.href).toBe('/login')
    })

    it('should logout on 401 if refresh fails', async () => {
      // Mock refresh token API call to fail
      mockAxiosInstance.post.mockRejectedValueOnce(new Error('Refresh failed'))

      // Mock fetch for logout
      ;(global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      })

      // Mock window.location.href
      delete (window as any).location
      window.location = { href: '' } as any

      await import('@/api/client')

      const errorInterceptor =
        mockAxiosInstance.interceptors.response.use.mock.calls[0][1]
      const error = {
        response: { status: 401 },
        config: {},
      }

      await expect(errorInterceptor(error)).rejects.toThrow(
        'Authentication required. Please login again.'
      )

      // Verify NO refresh was attempted
      expect(mockAxiosInstance.post).not.toHaveBeenCalled()

      // Verify logout was called
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/auth/exchange',
        expect.objectContaining({
          method: 'DELETE',
          credentials: 'include',
        })
      )
    })
  })

  describe('Retry Logic', () => {
    it('should retry failed requests with exponential backoff', async () => {
      await import('@/api/client')

      const errorInterceptor =
        mockAxiosInstance.interceptors.response.use.mock.calls[0][1]

      const originalRequest = {
        _retryCount: 0,
        url: '/api/test',
      }

      const error = {
        response: undefined, // Network error has no response
        config: originalRequest,
      }

      // Mock setTimeout
      vi.useFakeTimers()

      // Mock successful retry
      mockAxiosInstance.request.mockResolvedValue({ data: 'success' })

      const retryPromise = errorInterceptor(error)

      // Fast-forward time for exponential backoff
      vi.advanceTimersByTime(1000)

      await retryPromise

      expect(mockAxiosInstance.request).toHaveBeenCalled()
      expect(originalRequest._retryCount).toBe(1)

      vi.useRealTimers()
    })

    it('should not retry after max attempts', async () => {
      await import('@/api/client')

      const errorInterceptor =
        mockAxiosInstance.interceptors.response.use.mock.calls[0][1]

      const originalRequest = {
        _retryCount: 3, // Already at max
        url: '/api/test',
      }

      const error = {
        response: {
          status: 500,
          data: {},
        },
        config: originalRequest,
      }

      await expect(errorInterceptor(error)).rejects.toThrow(
        'Server error. Please try again later.'
      )
    })
  })
})
