/**
 * Main configuration module
 *
 * This module exports all configuration constants for the Dr. Muscle X web app.
 */

export {
  oauthConfig,
  googleOAuth,
  appleOAuth,
  oauthRedirectUris,
} from './oauth'

/**
 * API configuration
 */
export const apiConfig = {
  /**
   * Base URL for the Dr. Muscle API
   */
  baseUrl:
    process.env.NEXT_PUBLIC_API_URL || 'https://drmuscle.azurewebsites.net',

  /**
   * API request timeout in milliseconds
   */
  timeout: 30000,

  /**
   * Default headers for API requests
   */
  defaultHeaders: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
} as const

/**
 * App configuration
 */
export const appConfig = {
  /**
   * Current environment
   */
  env: process.env.NEXT_PUBLIC_APP_ENV || 'development',

  /**
   * App name
   */
  name: 'Dr. Muscle X',

  /**
   * App version
   */
  version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',

  /**
   * Is production environment
   */
  isProduction: process.env.NEXT_PUBLIC_APP_ENV === 'production',

  /**
   * Is development environment
   */
  isDevelopment: process.env.NEXT_PUBLIC_APP_ENV === 'development',

  /**
   * Is staging environment
   */
  isStaging: process.env.NEXT_PUBLIC_APP_ENV === 'staging',
} as const

/**
 * PWA configuration
 */
export const pwaConfig = {
  /**
   * Service worker path
   */
  swPath: '/sw.js',

  /**
   * App manifest path
   */
  manifestPath: '/manifest.json',

  /**
   * Cache name prefix
   */
  cachePrefix: 'drmuscle-x',

  /**
   * Enable PWA features
   */
  enabled: true,
} as const

/**
 * Performance configuration
 */
export const performanceConfig = {
  /**
   * Maximum bundle size in KB
   */
  maxBundleSize: 150,

  /**
   * Target LCP in milliseconds
   */
  targetLCP: 1000,

  /**
   * Target FID in milliseconds
   */
  targetFID: 50,

  /**
   * Target Time to Interactive in milliseconds
   */
  targetTTI: 1500,
} as const
