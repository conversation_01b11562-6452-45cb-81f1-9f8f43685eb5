# LLM Testing Instructions for Dr. Muscle Development

## Overview
When developing with LLM assistance, follow these testing guidelines to ensure code quality and prevent regressions.

## Testing Strategy

### 1. **Immediate Validation (After Every Change)**
Always run these commands after making changes:
```bash
npm run typecheck
npm run lint
```

### 2. **Development Testing Workflow**

#### Quick Development Cycle
For rapid iteration during development:
```bash
npm run test:dev:quick  # Runs typecheck, lint, and unit tests only
```

#### Before Committing
Always run before committing changes:
```bash
npm run test:before-commit  # Runs quick tests + critical E2E tests
```

#### Before Pushing/Creating PR
Run comprehensive tests:
```bash
npm run test:before-push  # Full test suite including all E2E tests
```

### 3. **Testing Commands Reference**

| Command | Description | When to Use | Time |
|---------|-------------|-------------|------|
| `npm run test:quick` | Type, lint, unit tests | After each code change | ~1-2 min |
| `npm run test:dev:critical` | Quick + critical E2E | Before commits | ~5-7 min |
| `npm run test:dev:full` | Full test suite | Before PRs | ~15-20 min |
| `npm run test:e2e:ui` | Interactive E2E debugging | When tests fail | Variable |

## LLM Prompt Templates

### 1. **For Feature Development**
```
I need to implement [FEATURE]. Before starting:
1. Run `npm run test:quick` to ensure clean baseline
2. After implementation, run `npm run test:dev:critical`
3. Update relevant tests for the new feature
4. Ensure all tests pass with `npm run test:before-commit`
```

### 2. **For Bug Fixes**
```
Fix [BUG DESCRIPTION]. Requirements:
1. First reproduce the bug with a failing test
2. Implement the fix
3. Run `npm run test:dev:critical` to verify fix
4. Check for regressions with `npm run test:quick`
```

### 3. **For Refactoring**
```
Refactor [COMPONENT/MODULE]. Ensure:
1. Run `npm run test:coverage` before changes
2. Maintain or improve coverage
3. Run `npm run test:dev:full` after refactoring
4. No behavior changes (all tests should pass unchanged)
```

## Testing Best Practices for LLM Development

### 1. **Always Test After AI-Generated Code**
- AI might not be aware of all project constraints
- Test immediately after generation to catch issues early
- Don't accumulate untested changes

### 2. **Include Test Requirements in Prompts**
Example prompt additions:
- "Ensure TypeScript types are correct (no 'any' types)"
- "Follow existing code patterns in the codebase"
- "Add unit tests for new functionality"
- "Verify mobile responsiveness for UI changes"

### 3. **Critical Areas Requiring Extra Testing**
When working on these areas, always run full tests:
- Authentication/Authorization
- Payment/Subscription logic
- Data synchronization
- API integrations
- Service worker/PWA functionality

### 4. **Performance Testing**
For changes affecting performance:
```bash
npm run analyze  # Check bundle size
npm run lighthouse:mobile  # Mobile performance metrics
```

## CI/CD Integration

### Pre-commit Hook
The project uses Husky for pre-commit validation:
- Automatically runs linting and formatting
- Prevents commits with errors

### PR Checks
All PRs must pass:
1. Type checking
2. Linting
3. Unit tests (with 80% coverage)
4. Critical E2E tests
5. Bundle size limits (<150KB)

### Full E2E Suite
Runs on:
- Merges to main branch
- PRs with `full-test` label

## Troubleshooting Common Issues

### 1. **TypeScript Errors**
```bash
npm run typecheck  # See all errors
# Fix each error individually
# Re-run after each fix
```

### 2. **Lint Errors**
```bash
npm run lint  # See all issues
npm run format  # Auto-fix formatting
```

### 3. **E2E Test Failures**
```bash
npm run test:e2e:ui  # Debug interactively
npm run test:e2e:debug  # Step through tests
```

### 4. **Coverage Issues**
```bash
npm run test:coverage  # Generate report
# Open coverage/index.html to see uncovered lines
```

## Quick Reference Card

```bash
# Development cycle
npm run test:quick          # After each change (1-2 min)
npm run test:dev:critical   # Before commit (5-7 min)
npm run test:dev:full      # Before PR (15-20 min)

# Debugging
npm run test:e2e:ui        # Interactive E2E
npm run typecheck          # Type errors only
npm run lint               # Code style issues

# Performance
npm run analyze            # Bundle size check
npm run lighthouse:mobile  # Performance audit
```

## Sample LLM Instruction Block

Add this to your prompts when working with Dr. Muscle codebase:

```
IMPORTANT: Follow Dr. Muscle testing requirements:
1. After making changes, run: npm run test:quick
2. Before suggesting commit, run: npm run test:before-commit
3. Ensure no TypeScript 'any' types
4. Maintain 80%+ test coverage
5. Check bundle size stays under 150KB
6. Test on mobile viewport (390x844)
7. If modifying auth/API/sync, run full E2E tests
```