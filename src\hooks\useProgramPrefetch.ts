import { useState, useCallback, useRef } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { programApi } from '@/api/program'
import { useProgramStore } from '@/stores/programStore'

interface UseProgramPrefetchReturn {
  progress: number
  isComplete: boolean
  error: string | null
  status: string
  startPrefetch: () => void
  reset: () => void
}

export function useProgramPrefetch(): UseProgramPrefetchReturn {
  const queryClient = useQueryClient()
  const { setCachedProgram, setCachedProgress, setCachedStats } =
    useProgramStore()
  const [progress, setProgress] = useState(0)
  const [isComplete, setIsComplete] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [status, setStatus] = useState('Starting...')
  const isRunningRef = useRef(false)
  const programCompletedRef = useRef(false)
  const progressCompletedRef = useRef(false)
  const statsCompletedRef = useRef(false)

  const updateProgress = useCallback(() => {
    let newProgress = 25 // Base progress for starting
    let completedCount = 0

    if (programCompletedRef.current) {
      completedCount++
    }
    if (progressCompletedRef.current) {
      completedCount++
    }
    if (statsCompletedRef.current) {
      completedCount++
    }

    // Each completed API call adds 25% (25% base + 3 * 25% = 100%)
    newProgress = 25 + completedCount * 25

    if (completedCount === 3) {
      newProgress = 100
    }

    setProgress(newProgress)
  }, [])

  const startPrefetch = useCallback(async () => {
    // Prevent multiple concurrent prefetches
    if (isRunningRef.current) {
      return
    }

    isRunningRef.current = true
    programCompletedRef.current = false
    progressCompletedRef.current = false
    statsCompletedRef.current = false
    setProgress(25)
    setStatus('Loading program information...')
    setError(null)

    try {
      // Start all API calls in parallel
      const promises = [
        programApi
          .getUserProgram()
          .then((result) => {
            programCompletedRef.current = true
            updateProgress()
            if (result) {
              queryClient.setQueryData(['program'], result)
              setCachedProgram(result) // Save to program store
              setStatus('Loading progress data...')
            }
            return { status: 'fulfilled' as const, value: result }
          })
          .catch((err) => {
            programCompletedRef.current = true
            updateProgress()
            return { status: 'rejected' as const, reason: err }
          }),

        programApi
          .getProgramProgress()
          .then((result) => {
            progressCompletedRef.current = true
            updateProgress()
            if (result) {
              queryClient.setQueryData(['programProgress'], result)
              setCachedProgress(result) // Save to program store
              setStatus('Loading statistics...')
            }
            return { status: 'fulfilled' as const, value: result }
          })
          .catch((err) => {
            progressCompletedRef.current = true
            updateProgress()
            return { status: 'rejected' as const, reason: err }
          }),

        programApi
          .getProgramStats()
          .then((result) => {
            statsCompletedRef.current = true
            updateProgress()
            queryClient.setQueryData(['programStats'], result)
            setCachedStats(result) // Save to program store
            return { status: 'fulfilled' as const, value: result }
          })
          .catch((err) => {
            statsCompletedRef.current = true
            updateProgress()
            return { status: 'rejected' as const, reason: err }
          }),
      ]

      const [programResult, progressResult, statsResult] =
        await Promise.all(promises)

      // Check if all failed
      if (
        programResult?.status === 'rejected' &&
        progressResult?.status === 'rejected' &&
        statsResult?.status === 'rejected'
      ) {
        throw new Error('Failed to load program data')
      }

      // Complete the prefetch
      setIsComplete(true)
      setStatus('Ready!')
    } catch (err) {
      setError('Failed to load program data')
      setStatus('Error loading program data')
      setIsComplete(false)
    } finally {
      isRunningRef.current = false
    }
  }, [
    queryClient,
    updateProgress,
    setCachedProgram,
    setCachedProgress,
    setCachedStats,
  ])

  const reset = useCallback(() => {
    setProgress(0)
    setIsComplete(false)
    setError(null)
    setStatus('Starting...')
    isRunningRef.current = false
    programCompletedRef.current = false
    progressCompletedRef.current = false
    statsCompletedRef.current = false
  }, [])

  return {
    progress,
    isComplete,
    error,
    status,
    startPrefetch,
    reset,
  }
}
