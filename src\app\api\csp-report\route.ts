import { NextRequest, NextResponse } from 'next/server'
import { logger } from '@/utils/logger'

export async function POST(request: NextRequest) {
  try {
    const report = await request.json()

    // Log CSP violations for monitoring
    logger.warn('CSP Violation', {
      report: report['csp-report'] || report,
      userAgent: request.headers.get('user-agent'),
      url: request.url,
    })

    // In production, you might want to send these to a monitoring service
    // For now, we just log them

    return NextResponse.json({ status: 'ok' })
  } catch (error) {
    logger.error('Error processing CSP report', error)
    return NextResponse.json({ status: 'error' }, { status: 400 })
  }
}

// CSP reports should be allowed without authentication
export const runtime = 'edge'
