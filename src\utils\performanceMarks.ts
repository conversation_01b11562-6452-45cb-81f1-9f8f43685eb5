/**
 * Performance marks for <PERSON><PERSON> <PERSON><PERSON><PERSON> X
 * Tracks key user journey metrics
 */
export enum PerformanceMarks {
  // Login flow marks
  LOGIN_START = 'dr-muscle-login-start',
  LOGIN_SUCCESS = 'dr-muscle-login-success',
  LOGIN_SCREEN_SHOWN = 'dr-muscle-login-screen-shown',

  // OAuth flow marks
  OAUTH_START = 'dr-muscle-oauth-start',
  OAUTH_SUCCESS = 'dr-muscle-oauth-success',
  OAUTH_ERROR = 'dr-muscle-oauth-error',

  // Success screen marks
  SUCCESS_SCREEN_START = 'dr-muscle-success-screen-start',
  SUCCESS_SCREEN_READY = 'dr-muscle-success-screen-ready',
  SUCCESS_SCREEN_COMPLETE = 'dr-muscle-success-screen-complete',

  // Data loading marks
  DATA_FETCH_START = 'dr-muscle-data-fetch-start',
  DATA_FETCH_COMPLETE = 'dr-muscle-data-fetch-complete',

  // Workout page marks
  WORKOUT_PAGE_START = 'dr-muscle-workout-page-start',
  WORKOUT_PAGE_INTERACTIVE = 'dr-muscle-workout-page-interactive',

  // Cache marks
  CACHE_HIT = 'dr-muscle-cache-hit',
  CACHE_MISS = 'dr-muscle-cache-miss',
}

export interface PerformanceMetrics {
  loginToInteractive?: number
  loginToSuccessScreen?: number
  successScreenDuration?: number
  dataFetchDuration?: number
  cacheHitRate?: number
  apiCalls?: Record<string, number>
  cacheMetrics?: {
    hitRate: number
    averageLatency: number
    totalSize: number
    itemCount: number
  }
  oauthMetrics?: {
    provider: string
    duration: number
    success: boolean
  }
}
