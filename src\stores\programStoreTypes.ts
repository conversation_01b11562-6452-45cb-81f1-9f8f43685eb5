/**
 * Program store state interface
 */
export interface ProgramState {
  // Cached data
  cachedData: import('./programCacheUtils').CachedProgramData

  // Hydration state
  hasHydrated: boolean

  // Loading states
  isLoadingProgram: boolean
  isLoadingProgress: boolean
  isLoadingStats: boolean

  // Background refresh states
  isRefreshingProgram: boolean
  isRefreshingProgress: boolean
  isRefreshingStats: boolean

  // Error states
  programError: Error | null
  progressError: Error | null
  statsError: Error | null
}

/**
 * Program store actions interface
 */
export interface ProgramActions {
  // Cache management
  setCachedProgram: (program: import('@/types').ProgramModel | null) => void
  setCachedProgress: (
    progress: import('@/types').ProgramProgress | null
  ) => void
  setCachedStats: (stats: import('@/types').ProgramStats | null) => void

  // Cache retrieval
  getCachedProgram: () => import('@/types').ProgramModel | null
  getCachedProgress: () => import('@/types').ProgramProgress | null
  getCachedStats: () => import('@/types').ProgramStats | null

  // Cache validation
  isProgramCacheStale: () => boolean
  isProgressCacheStale: () => boolean
  isStatsCacheStale: () => boolean
  isCacheStale: (type: 'program' | 'progress' | 'stats') => boolean

  // Cache cleanup
  clearExpiredCache: () => void
  clearAllCache: () => void

  // Loading state management
  setIsLoadingProgram: (isLoading: boolean) => void
  setIsLoadingProgress: (isLoading: boolean) => void
  setIsLoadingStats: (isLoading: boolean) => void

  // Background refresh state management
  setIsRefreshingProgram: (isRefreshing: boolean) => void
  setIsRefreshingProgress: (isRefreshing: boolean) => void
  setIsRefreshingStats: (isRefreshing: boolean) => void

  // Error management
  setProgramError: (error: Error | null) => void
  setProgressError: (error: Error | null) => void
  setStatsError: (error: Error | null) => void

  // Hydration
  setHasHydrated: (hasHydrated: boolean) => void

  // Cache utilities
  getCacheAge: (type: 'program' | 'progress' | 'stats') => number | null
  getCacheStats: () => {
    totalSize: number
    programAge: number | null
    progressAge: number | null
    statsAge: number | null
    isHydrated: boolean
  }
}

export type ProgramStore = ProgramState & ProgramActions
