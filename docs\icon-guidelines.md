# PWA Icon Guidelines

## Overview

Dr. Muscle X uses a comprehensive set of PWA icons to ensure proper display across all platforms and devices. This document outlines the icon requirements and implementation details.

## Icon Specifications

### Source Icon

- **File**: `public/app-icon.png`
- **Size**: 1024x1024px (recommended)
- **Format**: PNG with transparency
- **Design**: Clear, recognizable at small sizes

### Generated Icons

The project includes an automated icon generation script that creates all required icon sizes:

```bash
npm run generate-icons
```

This generates:

- **Regular Icons**: 72, 96, 128, 144, 152, 192, 384, 512px
- **Maskable Icons**: Same sizes with 20% padding
- **Apple Touch Icon**: 180x180px
- **Favicons**: 16x16px, 32x32px

## Platform Requirements

### iOS Safari

- **apple-touch-icon.png**: 180x180px
- **Requirements**:
  - No transparency (filled background)
  - High contrast
  - Clear at small sizes
  - Located at `/apple-touch-icon.png`

### Android Chrome

- **Maskable Icons**: Required for adaptive icons
- **Safe Zone**: Central 80% of icon (20% padding)
- **Background**: White background for consistency
- **Minimum**: 192x192px maskable icon

### Windows

- **Tile Support**: 144x144px and larger
- **Background Color**: Matches theme color
- **Format**: PNG

### Desktop Browsers

- **Favicons**: 16x16px, 32x32px
- **Format**: PNG
- **Location**: Root directory

## Design Guidelines

### Safe Zone for Maskable Icons

```
┌─────────────────────────┐
│         20% padding     │
│  ┌─────────────────┐   │
│  │                 │   │
│  │   Safe Zone     │   │
│  │     (60%)       │   │
│  │                 │   │
│  └─────────────────┘   │
│                         │
└─────────────────────────┘
```

### Best Practices

1. **Simplicity**: Icons should be simple and recognizable
2. **Contrast**: High contrast for visibility
3. **Scalability**: Design must work at 16x16px
4. **Consistency**: Maintain brand identity across sizes
5. **Testing**: Verify on actual devices

## Implementation

### Manifest Configuration

```json
{
  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "any"
    },
    {
      "src": "/icons/icon-maskable-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable"
    }
  ]
}
```

### HTML Meta Tags

```html
<link rel="apple-touch-icon" href="/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
```

## Testing

### Automated Tests

Run comprehensive icon tests:

```bash
npm test src/__tests__/pwa-icons.test.ts
```

Tests verify:

- File existence
- Correct dimensions
- Format compliance
- Platform requirements
- File size optimization

### Manual Testing

1. **iOS**: Add to home screen on iPhone/iPad
2. **Android**: Install PWA from Chrome
3. **Windows**: Pin to Start menu
4. **Desktop**: Check favicon in browser tabs

### Tools

- **PWA Builder**: https://www.pwabuilder.com/
- **Manifest Validator**: Chrome DevTools > Application
- **Real Device Testing**: BrowserStack or physical devices

## Troubleshooting

### Common Issues

1. **Blurry Icons**: Source image too small
2. **Cut-off Icons**: Not respecting safe zones
3. **Missing Icons**: Incorrect manifest paths
4. **Black Background**: Transparency issues

### Solutions

1. Use high-resolution source (1024x1024px)
2. Ensure 20% padding for maskable icons
3. Verify all paths are absolute (`/icons/...`)
4. Use proper background colors

## Maintenance

### Updating Icons

1. Replace `public/app-icon.png` with new design
2. Run `npm run generate-icons`
3. Test on all platforms
4. Commit all generated files

### Version Control

- Always commit generated icons
- Include source file in repository
- Document any design changes

## Resources

- [Adaptive Icons](https://web.dev/maskable-icon/)
- [PWA Icons Guide](https://web.dev/add-manifest/#icons)
- [Apple HIG](https://developer.apple.com/design/human-interface-guidelines/)
