'use client'

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { SyncManager, SYNC_EVENTS } from '@/utils/syncManager'
import { useAuthStore } from '@/stores/authStore'

export function SyncIndicator() {
  const [pendingCount, setPendingCount] = useState(0)
  const [isOnline, setIsOnline] = useState(true)
  const [showSuccess, setShowSuccess] = useState(false)
  const pathname = usePathname()
  const { isAuthenticated } = useAuthStore()

  // Don't show sync indicator on login page or when not authenticated
  const shouldShow = pathname !== '/login' && isAuthenticated

  useEffect(() => {
    if (!shouldShow) {
      return
    }

    // Check initial state
    setPendingCount(SyncManager.getPendingCount())
    setIsOnline(navigator.onLine)

    // Listen for sync events
    const handleQueueUpdate = (event: CustomEvent) => {
      setPendingCount(event.detail.pendingCount)
    }

    const handleSyncCompleted = (event: CustomEvent) => {
      setPendingCount(SyncManager.getPendingCount())
      // Show success feedback briefly
      if (event.detail.syncedCount > 0) {
        setShowSuccess(true)
        setTimeout(() => setShowSuccess(false), 2000)
      }
    }

    // Listen for online/offline events
    const handleOnline = () => {
      setIsOnline(true)
      // Update count when coming online
      setPendingCount(SyncManager.getPendingCount())
    }
    const handleOffline = () => setIsOnline(false)

    // Add event listeners
    SyncManager.on(SYNC_EVENTS.QUEUE_UPDATED, handleQueueUpdate)
    SyncManager.on(SYNC_EVENTS.SYNC_COMPLETED, handleSyncCompleted)
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      // Remove event listeners
      SyncManager.off(SYNC_EVENTS.QUEUE_UPDATED, handleQueueUpdate)
      SyncManager.off(SYNC_EVENTS.SYNC_COMPLETED, handleSyncCompleted)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [shouldShow])

  if (!shouldShow || (pendingCount === 0 && isOnline)) return null

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {!isOnline && (
        <div className="bg-yellow-500 text-white px-3 py-2 rounded-lg shadow-lg flex items-center gap-2 mb-2">
          <svg
            className="w-4 h-4 animate-pulse"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M18.364 5.636a9 9 0 010 12.728m0 0l-2.829-2.829m2.829 2.829L21 21M15.536 8.464a5 5 0 010 7.072m0 0l-2.829-2.829m-4.243 2.829a4.978 4.978 0 01-1.414-2.83m-1.414 5.658a9 9 0 01-2.167-9.238m7.824 2.167a1 1 0 111.414 1.414m-1.414-1.414L3 3m8.293 8.293l1.414 1.414"
            />
          </svg>
          <span className="text-sm font-medium">Offline Mode</span>
        </div>
      )}

      {showSuccess && pendingCount === 0 && (
        <div className="bg-green-500 text-white px-3 py-2 rounded-lg shadow-lg flex items-center gap-2 mb-2 transition-opacity duration-500">
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
          <span className="text-sm font-medium">All sets synced!</span>
        </div>
      )}

      {pendingCount > 0 && (
        <div className="bg-blue-500 text-white px-3 py-2 rounded-lg shadow-lg flex items-center gap-2">
          <svg
            className="w-4 h-4 animate-spin"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          <span className="text-sm font-medium">
            {pendingCount} {pendingCount === 1 ? 'set' : 'sets'} pending sync
          </span>
        </div>
      )}
    </div>
  )
}
