'use client'

import React from 'react'
import { ThemeProvider, useTheme } from '@/providers/theme-provider'
import { <PERSON><PERSON>, Card, Input, Alert, Badge } from '@/components/ui'
import { themeConfig } from '@/config/theme'

function ThemeSelector() {
  const { theme, setTheme } = useTheme()

  return (
    <div className="flex flex-wrap gap-2 p-4 bg-bg-secondary rounded-theme">
      {Object.keys(themeConfig.themes).map((themeName) => (
        <button
          key={themeName}
          onClick={() => setTheme(themeName as keyof typeof themeConfig.themes)}
          className={`px-4 py-2 rounded-theme transition-all ${
            theme === themeName
              ? 'bg-brand-primary text-text-inverse shadow-theme-md'
              : 'bg-bg-tertiary text-text-primary hover:bg-brand-primary/10'
          }`}
        >
          {
            themeConfig.themes[themeName as keyof typeof themeConfig.themes]
              .name
          }
        </button>
      ))}
    </div>
  )
}

function ThemeDemoContent() {
  const { theme } = useTheme()
  const currentTheme = themeConfig.themes[theme]

  return (
    <div className="min-h-screen bg-bg-primary text-text-primary p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-4xl font-heading font-bold mb-2">
            Theme System Demo
          </h1>
          <p className="text-text-secondary">
            Current theme:{' '}
            <span className="text-brand-primary font-semibold">
              {currentTheme.name}
            </span>
          </p>
          <p className="text-text-tertiary text-sm mt-1">
            {currentTheme.description}
          </p>
        </div>

        {/* Theme Selector */}
        <ThemeSelector />

        {/* Colors */}
        <Card>
          <h2 className="text-2xl font-heading font-semibold mb-4">Colors</h2>
          <div className="space-y-4">
            {/* Brand Colors */}
            <div>
              <h3 className="text-lg font-medium mb-2">Brand Colors</h3>
              <div className="flex gap-4">
                <div className="text-center">
                  <div className="w-20 h-20 bg-brand-primary rounded-theme shadow-theme-md mb-2" />
                  <p className="text-sm">Primary</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 bg-brand-secondary rounded-theme shadow-theme-md mb-2" />
                  <p className="text-sm">Secondary</p>
                </div>
                <div className="text-center">
                  <div className="w-20 h-20 bg-brand-accent rounded-theme shadow-theme-md mb-2" />
                  <p className="text-sm">Accent</p>
                </div>
              </div>
            </div>

            {/* Semantic Colors */}
            <div>
              <h3 className="text-lg font-medium mb-2">Semantic Colors</h3>
              <div className="flex gap-4">
                <Badge variant="success">Success</Badge>
                <Badge variant="warning">Warning</Badge>
                <Badge variant="error">Error</Badge>
                <Badge>Default</Badge>
              </div>
            </div>
          </div>
        </Card>

        {/* Typography */}
        <Card>
          <h2 className="text-2xl font-heading font-semibold mb-4">
            Typography
          </h2>
          <div className="space-y-2">
            <h1 className="text-4xl font-heading">Heading 1</h1>
            <h2 className="text-3xl font-heading">Heading 2</h2>
            <h3 className="text-2xl font-heading">Heading 3</h3>
            <p className="text-base text-text-primary">Body text primary</p>
            <p className="text-base text-text-secondary">Body text secondary</p>
            <p className="text-sm text-text-tertiary">Small tertiary text</p>
          </div>
        </Card>

        {/* Components */}
        <Card>
          <h2 className="text-2xl font-heading font-semibold mb-4">
            Components
          </h2>

          {/* Buttons */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-3">Buttons</h3>
              <div className="flex flex-wrap gap-3">
                <Button variant="primary">Primary</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="danger">Danger</Button>
                <Button variant="primary" disabled>
                  Disabled
                </Button>
              </div>
            </div>

            {/* Inputs */}
            <div>
              <h3 className="text-lg font-medium mb-3">Inputs</h3>
              <div className="space-y-3 max-w-sm">
                <Input label="Normal Input" placeholder="Enter text..." />
                <Input
                  label="Error Input"
                  error="This field is required"
                  placeholder="Enter text..."
                />
              </div>
            </div>

            {/* Alerts */}
            <div>
              <h3 className="text-lg font-medium mb-3">Alerts</h3>
              <div className="space-y-3">
                <Alert variant="info">This is an informational message.</Alert>
                <Alert variant="success" title="Success!">
                  Your workout has been saved.
                </Alert>
                <Alert
                  variant="error"
                  action={{ label: 'Retry', onClick: () => {} }}
                >
                  Failed to load data. Please try again.
                </Alert>
              </div>
            </div>
          </div>
        </Card>

        {/* Shadows */}
        <Card>
          <h2 className="text-2xl font-heading font-semibold mb-4">Shadows</h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-bg-secondary rounded-theme shadow-theme-sm">
              <p>Small Shadow</p>
            </div>
            <div className="p-4 bg-bg-secondary rounded-theme shadow-theme-md">
              <p>Medium Shadow</p>
            </div>
            <div className="p-4 bg-bg-secondary rounded-theme shadow-theme-lg">
              <p>Large Shadow</p>
            </div>
            <div className="p-4 bg-bg-secondary rounded-theme shadow-theme-xl">
              <p>Extra Large Shadow</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default function ThemeDemoPage() {
  return (
    <ThemeProvider>
      <ThemeDemoContent />
    </ThemeProvider>
  )
}
