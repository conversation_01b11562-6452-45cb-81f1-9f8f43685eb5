import React from 'react'

interface CounterLabelProps {
  label: string
  icon?: React.ReactNode
  size: 'small' | 'medium' | 'large'
}

const iconSizeClasses = {
  small: 'w-4 h-4',
  medium: 'w-5 h-5',
  large: 'w-6 h-6',
}

export function CounterLabel({ label, icon, size }: CounterLabelProps) {
  return (
    <div className="mt-2 flex items-center justify-center gap-2">
      {icon && <div className={iconSizeClasses[size]}>{icon}</div>}
      <p
        className="text-sm text-text-secondary"
        id={`counter-label-${label.toLowerCase().replace(/\s+/g, '-')}`}
      >
        {label}
      </p>
    </div>
  )
}
