import { describe, it, expect } from 'vitest';
import { execSync } from 'child_process';
import path from 'path';

describe('File Size Check Script', () => {
  it('should execute the file size check script', () => {
    const scriptPath = path.join(process.cwd(), 'scripts/check-file-sizes.js');
    let exitCode = 0;
    
    try {
      execSync(`node ${scriptPath}`, { stdio: 'pipe' });
    } catch (error: any) {
      exitCode = error.status || 1;
    }
    
    // The script should fail because we have files over 225 lines
    expect(exitCode).toBe(1);
  });

  it('should have file size check in package.json scripts', () => {
    const packageJson = require('../../../package.json');
    expect(packageJson.scripts['check:file-sizes']).toBe('node scripts/check-file-sizes.js');
  });

  it('should have file size check integrated in CI workflows', () => {
    const fs = require('fs');
    
    // Check CI workflow
    const ciWorkflow = fs.readFileSync(
      path.join(process.cwd(), '.github/workflows/ci.yml'),
      'utf-8'
    );
    expect(ciWorkflow).toContain('npm run check:file-sizes');
    
    // Check optimized CI workflow
    const ciOptimizedWorkflow = fs.readFileSync(
      path.join(process.cwd(), '.github/workflows/ci-optimized.yml'),
      'utf-8'
    );
    expect(ciOptimizedWorkflow).toContain('npm run check:file-sizes');
  });
});