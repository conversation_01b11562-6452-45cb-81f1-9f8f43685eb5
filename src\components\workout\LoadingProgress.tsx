'use client'

interface LoadingProgressProps {
  /** Progress percentage (0-100) */
  progress: number
  /** Optional status text to display */
  status?: string
  /** Show percentage text */
  showPercentage?: boolean
  /** Additional CSS classes */
  className?: string
}

export function LoadingProgress({
  progress,
  status,
  showPercentage = false,
  className = '',
}: LoadingProgressProps) {
  // Clamp progress between 0 and 100
  const clampedProgress = Math.max(0, Math.min(100, progress))
  const roundedProgress = Math.round(clampedProgress)

  return (
    <div data-testid="loading-progress" className={`w-full ${className}`}>
      {/* Progress bar container */}
      <div
        data-testid="loading-progress-bar"
        className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden"
      >
        {/* Progress fill */}
        <div
          data-testid="loading-progress-fill"
          className="h-full bg-blue-600 rounded-full transition-all duration-300 ease-out"
          style={{
            width: `${clampedProgress}%`,
            willChange: 'width',
          }}
        />
      </div>

      {/* Status text and percentage */}
      <div className="mt-2 flex items-center justify-between">
        {status && (
          <p
            data-testid="loading-progress-status"
            className="text-sm text-gray-600 dark:text-gray-400"
          >
            {status}
          </p>
        )}

        {showPercentage && (
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {roundedProgress}%
          </span>
        )}
      </div>
    </div>
  )
}
