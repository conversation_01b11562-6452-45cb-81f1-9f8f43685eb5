import { test, expect } from '@playwright/test'

test.describe('OAuth Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Mock OAuth helpers and the SDKs they depend on
    await page.addInitScript(() => {
      // Mock Google SDK
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const mockWindow = window as any
      mockWindow.google = {
        accounts: {
          id: {
            initialize: () => {},
            prompt: () => {},
            renderButton: () => {},
          },
        },
      }

      // Mock Apple SDK
      mockWindow.AppleID = {
        auth: {
          init: () => {},
          signIn: () =>
            Promise.resolve({
              authorization: {
                code: 'test-code',
                id_token: 'test-token',
              },
            }),
        },
      }

      // Mock GoogleOAuthHelper
      mockWindow.GoogleOAuthHelper = {
        isAvailable: () => true,
        isReady: () => true,
        initialize: () => Promise.resolve(),
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        signIn: async (onSuccess: any) => {
          // Simulate user interaction
          setTimeout(() => {
            onSuccess({
              provider: 'google',
              providerId: 'google-user-123',
              email: '<EMAIL>',
              name: 'Test User',
              emailVerified: true,
              rawToken: 'mock-google-id-token',
            })
          }, 100)
        },
      }

      // Mock AppleOAuthHelper
      mockWindow.AppleOAuthHelper = {
        isAvailable: () => true,
        isReady: () => true,
        initialize: () => Promise.resolve(),
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        signIn: async (onSuccess: any) => {
          // Simulate user interaction
          setTimeout(() => {
            onSuccess({
              provider: 'apple',
              providerId: 'apple-user-123',
              email: '<EMAIL>',
              name: 'Test User',
              emailVerified: true,
              rawToken: 'mock-apple-id-token',
            })
          }, 100)
        },
      }
    })

    // Navigate to login page
    await page.goto('/login')
    await expect(page).toHaveTitle(/Dr\. Muscle X/)
  })

  test('should display OAuth buttons on login page', async ({ page }) => {
    // Debug: Check what OAuth configuration looks like
    const oauthConfig = await page.evaluate(() => {
      return {
        googleHelper: !!(window as any).GoogleOAuthHelper,
        appleHelper: !!(window as any).AppleOAuthHelper,
        // Check if helpers were exported to window
        googleAvailable: (window as any).GoogleOAuthHelper?.isAvailable?.(),
        appleAvailable: (window as any).AppleOAuthHelper?.isAvailable?.(),
      }
    })
    // eslint-disable-next-line no-console
    console.log('OAuth config in page:', oauthConfig)

    // Check if OAuth buttons are visible
    const googleButton = page.getByRole('button', {
      name: /sign in with google/i,
    })
    const appleButton = page.getByRole('button', {
      name: /sign in with apple/i,
    })

    await expect(googleButton).toBeVisible()
    await expect(appleButton).toBeVisible()

    // Wait a bit for any async initialization
    await page.waitForTimeout(1000)

    // Buttons should be enabled (not disabled)
    await expect(googleButton).toBeEnabled()
    await expect(appleButton).toBeEnabled()
  })

  test('should show loading state when clicking Google sign-in', async ({
    page,
  }) => {
    const googleButton = page.getByRole('button', {
      name: /sign in with google/i,
    })

    // Click the button
    await googleButton.click()

    // Should show loading state
    await expect(page.getByText(/signing in/i)).toBeVisible()

    // Should disable the button
    await expect(googleButton).toBeDisabled()
  })

  test('should show loading state when clicking Apple sign-in', async ({
    page,
  }) => {
    const appleButton = page.getByRole('button', {
      name: /sign in with apple/i,
    })

    // Click the button
    await appleButton.click()

    // Should show loading state
    await expect(page.getByText(/signing in/i)).toBeVisible()

    // Should disable the button
    await expect(appleButton).toBeDisabled()
  })

  test('should handle OAuth errors gracefully', async ({ page }) => {
    // Mock failed OAuth response (using the /token endpoint)
    await page.route('**/token', (route) => {
      const body = route.request().postData()
      if (body && body.includes('grant_type=google')) {
        route.fulfill({
          status: 400,
          json: {
            error: 'invalid_grant',
            error_description: 'Invalid OAuth token',
          },
        })
      } else {
        route.continue()
      }
    })

    const googleButton = page.getByRole('button', {
      name: /sign in with google/i,
    })
    await googleButton.click()

    // Should show error message after backend rejects the token
    await expect(page.locator('[role="alert"]')).toContainText(/invalid/i, {
      timeout: 10000,
    })
  })

  test('should maintain regular login functionality', async ({ page }) => {
    // Regular login should still work
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.locator('input[name="password"]').fill('testpassword')

    const loginButton = page.getByRole('button', { name: /^login$/i })
    await expect(loginButton).toBeEnabled()

    // OAuth buttons should also be enabled
    await expect(
      page.getByRole('button', { name: /sign in with google/i })
    ).toBeEnabled()
    await expect(
      page.getByRole('button', { name: /sign in with apple/i })
    ).toBeEnabled()
  })

  test('should disable all buttons when logging in', async ({ page }) => {
    // Fill in regular login form
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.locator('input[name="password"]').fill('testpassword')

    // Mock login API to delay response
    await page.route('**/token', async (route) => {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      route.fulfill({
        status: 200,
        json: {
          access_token: 'test-token',
          userName: '<EMAIL>',
        },
      })
    })

    // Click login button
    await page.getByRole('button', { name: /^login$/i }).click()

    // All buttons should be disabled during login
    await expect(page.getByRole('button', { name: /^login/i })).toBeDisabled()
    await expect(
      page.getByRole('button', { name: /sign in with google/i })
    ).toBeDisabled()
    await expect(
      page.getByRole('button', { name: /sign in with apple/i })
    ).toBeDisabled()
  })
})
