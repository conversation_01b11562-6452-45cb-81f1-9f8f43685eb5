# Dr. Muscle X

[![CI/CD Pipeline](https://github.com/dr-muscle/DrMuscleWebApp/actions/workflows/ci-optimized.yml/badge.svg)](https://github.com/dr-muscle/DrMuscleWebApp/actions/workflows/ci-optimized.yml)

> **World's Fastest AI Personal Trainer PWA**  
> _Built with TypeScript + Next.js 14 + TDD_

## 🎯 Project Overview

Dr. Muscle X is a lightning-fast **mobile-first Progressive Web App** that delivers the core workout experience of the Dr. Muscle mobile app fitness platform. Built with modern web technologies and following strict Test-Driven Development (TDD) principles, this PWA targets mobile users frustrated with the current slow and buggy MAUI experience.

> **Key Insight**: 95%+ of users will access this on mobile devices, making this a mobile-first PWA rather than a responsive web app.

### Key Goals

- **Performance**: Measurably faster than the current mobile app
- **Reliability**: Zero-bug core workout completion flow
- **Quality**: 90%+ test coverage with comprehensive TDD/BDD implementation
- **User Experience**: Mirror the proven Dr. Muscle Watch app UX patterns

## 🚀 Technical Architecture

### Frontend Stack (Mobile-First PWA)

- **Framework**: Next.js 14+ with App Router
- **Language**: TypeScript (strict mode, zero `any` types)
- **Styling**: Tailwind CSS 3.0+ with mobile-first breakpoints
- **PWA Features**:
  - Service Worker for offline workout caching
  - Web App Manifest for home screen installation
  - Background sync for workout data
  - Push notifications for rest timers
- **State Management**:
  - Zustand for global state
  - TanStack Query (React Query) for API state/caching
- **HTTP Client**: Axios with request/response interceptors
- **Testing**: Vitest + React Testing Library + Playwright (Mobile E2E)
- **Code Quality**: ESLint (Airbnb config) + Prettier + Husky

### Mobile-First Performance Targets

- **Initial Load**: < 2 seconds on 3G (Largest Contentful Paint)
- **Time to Interactive**: < 3 seconds on mid-range mobile devices
- **Tap Response**: < 50ms (mobile touch sensitivity)
- **Bundle Size**: < 150KB initial JavaScript (mobile bandwidth)
- **Workout Transitions**: < 100ms between sets/exercises
- **Battery Impact**: Minimal CPU usage during rest periods
- **Test Coverage**: Minimum 90%, Target 95%

### Lightning-Fast Loading Strategy

**Core Promise**: Every page loads instantly with immediate visual feedback.

- **Instant Static Display**: All pages render immediately with static UI elements (headers, buttons, layout)
- **Progressive Data Loading**: Dynamic content loads asynchronously and displays as it becomes available
- **Skeleton Loading**: Use skeleton screens for all dynamic content areas until data is loaded
- **No Blank States**: Users never see a blank page or loading spinner as the primary content
- **Optimistic Updates**: User actions reflect immediately in the UI before server confirmation

This approach ensures users perceive the app as instantaneous, even on slow connections or older devices.

## 📋 Core Features (Mobile-First MVP)

### Progressive Web App Features

- **Home Screen Installation**: One-tap install to phone home screen
- **Offline Workouts**: Cache workouts for spotty gym WiFi
- **Background Sync**: Queue workout data when offline, sync when connected
- **App-Like Experience**: Full-screen with no browser chrome
- **Push Notifications**: Rest timer alerts and workout reminders

### Authentication System (Mobile-Optimized)

- Email/password login optimized for mobile keyboards
- Biometric authentication integration where available
- Secure token management with automatic refresh
- Auto-redirect to today's workout on successful authentication

### Workout Execution Flow (Touch-Optimized)

_Mirroring the Dr. Muscle Watch app UX with mobile enhancements:_

1. **Today's Workout Screen**: Large thumb-friendly buttons and swipe gestures
2. **Exercise Execution**: Tap-to-edit inputs with haptic feedback and number pads
3. **RIR Capture**: Mobile-optimized picker with large touch targets
4. **Rest Timer**: Full-screen countdown with vibration and background operation
5. **Workout Completion**: Smooth animations and confirmation feedback

### API Integration (Offline-First)

- Production Dr. Muscle API integration
- TypeScript interfaces generated from existing API models
- Optimistic updates with offline-first caching strategy
- Background sync for seamless online/offline transitions

## 🛠 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Modern browser (Chrome 100+, Safari 15+, Firefox 100+, Edge 100+)

### Installation

```bash
# Clone the repository
git clone https://github.com/dr-muscle/DrMuscleWebApp.git
cd DrMuscleWebApp

# Install dependencies
npm install

# Start development server
npm run dev
```
