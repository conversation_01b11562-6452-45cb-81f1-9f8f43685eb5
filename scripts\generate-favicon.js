const sharp = require('sharp')
const fs = require('fs')
const path = require('path')

async function generateFavicon() {
  const sourcePath = path.join(process.cwd(), 'public', 'app-icon.png')
  const outputPath = path.join(process.cwd(), 'public', 'favicon.ico')
  
  // Generate a 32x32 PNG first
  const png32Path = path.join(process.cwd(), 'public', 'favicon-32x32.png')
  
  await sharp(sourcePath)
    .resize(32, 32, {
      fit: 'contain',
      background: { r: 255, g: 255, b: 255, alpha: 0 }
    })
    .toFile(png32Path)
  
  console.log('✅ Generated favicon-32x32.png')
  
  // Also generate 16x16 for completeness
  const png16Path = path.join(process.cwd(), 'public', 'favicon-16x16.png')
  
  await sharp(sourcePath)
    .resize(16, 16, {
      fit: 'contain',
      background: { r: 255, g: 255, b: 255, alpha: 0 }
    })
    .toFile(png16Path)
  
  console.log('✅ Generated favicon-16x16.png')
  
  // Note: Converting to proper .ico format requires additional libraries
  // For now, we'll use the PNG versions which are widely supported
}

generateFavicon().catch(console.error)