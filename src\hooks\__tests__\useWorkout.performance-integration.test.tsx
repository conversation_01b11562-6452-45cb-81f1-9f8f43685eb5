import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, act, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useWorkout } from '../useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { PerformanceMonitor } from '@/utils/performance'
import type {
  GetUserProgramInfoResponseModel,
  WorkoutTemplateModel,
} from '@/types'

// Mock the API module
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserProgramInfo: vi.fn().mockResolvedValue(null),
    getUserWorkout: vi.fn().mockResolvedValue(null),
    getExerciseRecommendation: vi.fn().mockResolvedValue(null),
    saveWorkoutSet: vi.fn(),
    completeWorkout: vi.fn(),
  },
}))

// Mock the PerformanceMonitor
vi.mock('@/utils/performance', () => ({
  PerformanceMonitor: {
    mark: vi.fn(),
    trackCacheMetrics: vi.fn(),
  },
  PerformanceMarks: {
    CACHE_HIT: 'dr-muscle-cache-hit',
    CACHE_MISS: 'dr-muscle-cache-miss',
  },
}))

// Mock the WorkoutCache
vi.mock('@/utils/workoutCache', () => ({
  WorkoutCache: {
    get: vi.fn().mockReturnValue(null),
    set: vi.fn(),
    clear: vi.fn(),
  },
}))

// Mock data
const mockUserProgramInfo: GetUserProgramInfoResponseModel = {
  GetUserProgramInfoResponseModel: {
    UserId: 'test-user',
    WeeklyStatus: 'Week 1',
    ProgramLabel: 'Test Program',
    NbDaysInTheWeek: 5,
    NbNonTrainingDays: 2,
    MondayIsFirst: false,
    TimeLogged: '2024-01-01T10:00:00',
    NextWorkoutDayText: 'Today',
    IsInIntroWorkout: false,
    IsInFirstWeek: true,
    TodaysWorkoutId: '1234',
    TodaysWorkoutText: 'Push Day',
    RecommendedProgram: {
      Id: 1,
      Label: 'Beginner Program',
      RemainingToLevelUp: 10,
      IconUrl: 'https://example.com/icon.png',
    },
    NextWorkoutTemplate: {
      Id: 1,
      Label: 'Push Day',
      IsSystemExercise: false,
      Exercises: [
        {
          Id: 123,
          Label: 'Bench Press',
          Path: 'chest/benchpress',
          TargetWeight: { Mass: 100, MassUnit: 'lbs' },
          TargetReps: 8,
          IsWarmup: false,
          HasPastLogs: true,
        },
      ],
    },
    NextNonTrainingDay: '2024-01-03',
    MondayHere: '2024-01-01',
    NextIntensityTechnique: 'Standard',
    ServerTimeUtc: '2024-01-01T10:00:00Z',
    MaxWorkoutSets: 20,
    NbMediumSets: 5,
    NbChallenges: 3,
    WorkoutTemplates: [],
  },
}

const mockWorkout: WorkoutTemplateModel = {
  Id: 1,
  UserId: 'test-user',
  Label: 'Push Day',
  Exercises: [
    {
      Id: 123,
      Label: 'Bench Press',
      Path: 'chest/benchpress',
      TargetWeight: { Mass: 100, MassUnit: 'lbs' },
      TargetReps: 8,
      IsWarmup: false,
      HasPastLogs: true,
    },
  ],
  IsSystemExercise: false,
  WorkoutSettingsModel: {
    Id: 1,
    Pause: 120,
    Equipment: '',
    ChildWorkoutTemplateId: null,
    SetsModel: null,
    WorkoutProgramId: 1,
    IsFirstSet: false,
    IsFail: false,
    NbRepsMinimalInc: null,
    AvgDuration: null,
    IsNotRealData: false,
  },
}

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: Infinity,
      },
    },
  })

  return function ({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

describe('useWorkout - Performance Integration', () => {
  beforeEach(() => {
    // Reset all stores
    useAuthStore.setState({ isAuthenticated: true })
    useWorkoutStore.setState({
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      hasHydrated: false,
      currentWorkout: null,
      exercises: [],
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      workoutSession: null,
      isLoading: false,
      error: null,
      cacheStats: {
        hits: 0,
        misses: 0,
        operationCount: 0,
        totalLatency: 0,
        hydrationTime: 0,
      },
    })

    // Clear all mocks
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
    vi.clearAllMocks()
  })

  it('should report cache metrics immediately after hydration', async () => {
    // Given: Store is hydrated with cached data
    useWorkoutStore.setState({
      hasHydrated: true,
      cachedData: {
        userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
        userWorkouts: [mockWorkout],
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: Date.now(),
          userWorkouts: Date.now(),
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      cacheStats: {
        hits: 5,
        misses: 2,
        operationCount: 7,
        totalLatency: 3.5,
        hydrationTime: 15,
      },
    })

    // When: Hook is rendered
    renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    // Then: Cache metrics should be reported immediately
    await waitFor(() => {
      expect(PerformanceMonitor.trackCacheMetrics).toHaveBeenCalledWith({
        hits: 5,
        misses: 2,
        hitRate: 5 / 7,
        operationCount: 7,
        averageLatency: 3.5 / 7,
        totalLatency: 3.5,
        totalSize: expect.any(Number),
        itemCount: 2, // userProgramInfo + userWorkouts
        oldestDataAge: expect.any(Number),
        freshDataCount: 2,
        staleDataCount: 0,
        hydrationTime: 15,
      })
    })
  })

  it('should report cache metrics periodically', async () => {
    // Given: Store is hydrated
    useWorkoutStore.setState({
      hasHydrated: true,
      cacheStats: {
        hits: 10,
        misses: 5,
        operationCount: 15,
        totalLatency: 7.5,
        hydrationTime: 20,
      },
    })

    // When: Hook is rendered
    renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    // Clear initial call
    vi.clearAllMocks()

    // Advance timer by 30 seconds
    act(() => {
      vi.advanceTimersByTime(30000)
    })

    // Then: Cache metrics should be reported again
    await waitFor(() => {
      expect(PerformanceMonitor.trackCacheMetrics).toHaveBeenCalledTimes(1)
      expect(PerformanceMonitor.trackCacheMetrics).toHaveBeenCalledWith(
        expect.objectContaining({
          hits: 10,
          misses: 5,
          hitRate: 10 / 15,
          operationCount: 15,
          averageLatency: 7.5 / 15,
        })
      )
    })
  })

  it('should track cache operations during usage', async () => {
    // Given: Store with cached data
    useWorkoutStore.setState({
      hasHydrated: true,
      cachedData: {
        userProgramInfo: mockUserProgramInfo.GetUserProgramInfoResponseModel,
        userWorkouts: [mockWorkout],
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: Date.now(),
          userWorkouts: Date.now(),
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
    })

    // When: Hook is rendered and cache is accessed
    renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    // Access cache through the store (simulating internal operations)
    act(() => {
      const store = useWorkoutStore.getState()
      store.getCachedUserProgramInfo() // Should be a hit
      store.getCachedTodaysWorkout() // Should be a miss
    })

    // Advance timer to trigger metric reporting
    act(() => {
      vi.advanceTimersByTime(30000)
    })

    // Then: Updated metrics should be reported
    await waitFor(() => {
      const lastCall =
        PerformanceMonitor.trackCacheMetrics.mock.calls[
          PerformanceMonitor.trackCacheMetrics.mock.calls.length - 1
        ]
      expect(lastCall[0]).toMatchObject({
        hits: 1,
        misses: 1,
        hitRate: 0.5,
        operationCount: 2,
      })
    })
  })

  it('should not report metrics before hydration', () => {
    // Given: Store is not hydrated
    useWorkoutStore.setState({
      hasHydrated: false,
    })

    // When: Hook is rendered
    renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    // Then: No metrics should be reported
    expect(PerformanceMonitor.trackCacheMetrics).not.toHaveBeenCalled()

    // Even after timer advance
    act(() => {
      vi.advanceTimersByTime(30000)
    })

    expect(PerformanceMonitor.trackCacheMetrics).not.toHaveBeenCalled()
  })

  it('should clean up interval on unmount', () => {
    // Given: Store is hydrated
    useWorkoutStore.setState({
      hasHydrated: true,
    })

    // When: Hook is rendered and unmounted
    const { unmount } = renderHook(() => useWorkout(), {
      wrapper: createWrapper(),
    })

    // Clear initial call
    vi.clearAllMocks()

    // Unmount the hook
    unmount()

    // Advance timer
    act(() => {
      vi.advanceTimersByTime(30000)
    })

    // Then: No new metrics should be reported after unmount
    expect(PerformanceMonitor.trackCacheMetrics).not.toHaveBeenCalled()
  })
})
