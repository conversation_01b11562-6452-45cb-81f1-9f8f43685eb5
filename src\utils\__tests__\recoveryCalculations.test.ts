import { vi } from 'vitest'
import {
  calculateRecoveryHours,
  calculateRecoveryInfo,
  formatTimeRemaining,
  getCoachMessage,
  getProgramType,
} from '../recoveryCalculations'

describe('recoveryCalculations', () => {
  beforeEach(() => {
    // Mock current date for consistent testing
    vi.useFakeTimers()
    vi.setSystemTime(new Date('2024-01-15T12:00:00Z'))
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('calculateRecoveryHours', () => {
    it('returns 42 hours for consecutive workouts', () => {
      expect(calculateRecoveryHours('split', 25, true)).toBe(42)
      expect(calculateRecoveryHours('full-body', 25, true)).toBe(42)
      expect(calculateRecoveryHours('other', 25, true)).toBe(42)
    })

    it('returns 18 hours for split programs', () => {
      expect(calculateRecoveryHours('split', 25, false)).toBe(18)
      expect(calculateRecoveryHours('split', 35, false)).toBe(18)
    })

    it('returns 18 hours for full-body/powerlifting under 30', () => {
      expect(calculateRecoveryHours('full-body', 25, false)).toBe(18)
      expect(calculateRecoveryHours('powerlifting', 29, false)).toBe(18)
    })

    it('returns 42 hours for full-body/powerlifting 30 and over', () => {
      expect(calculateRecoveryHours('full-body', 30, false)).toBe(42)
      expect(calculateRecoveryHours('powerlifting', 35, false)).toBe(42)
      expect(calculateRecoveryHours('full-body', undefined, false)).toBe(42)
    })

    it('returns 24 hours for other programs', () => {
      expect(calculateRecoveryHours('other', 25, false)).toBe(24)
      expect(calculateRecoveryHours('other', 35, false)).toBe(24)
    })
  })

  describe('calculateRecoveryInfo', () => {
    it('calculates 0% recovery for just completed workout', () => {
      const result = calculateRecoveryInfo(
        new Date('2024-01-15T12:00:00Z'),
        'split',
        25
      )

      expect(result.percentage).toBe(0)
      expect(result.hoursElapsed).toBe(0)
      expect(result.requiredHours).toBe(18)
      expect(result.isReady).toBe(false)
      expect(result.remainingTime).toBe('18 hours')
      expect(result.message).toBe('Rest and recover')
    })

    it('calculates 50% recovery for halfway point', () => {
      const result = calculateRecoveryInfo(
        new Date('2024-01-15T03:00:00Z'), // 9 hours ago
        'split',
        25
      )

      expect(result.percentage).toBe(50)
      expect(result.hoursElapsed).toBe(9)
      expect(result.requiredHours).toBe(18)
      expect(result.isReady).toBe(false)
      expect(result.remainingTime).toBe('9 hours')
      expect(result.message).toBe('Almost ready')
    })

    it('calculates 100% recovery when fully recovered', () => {
      const result = calculateRecoveryInfo(
        new Date('2024-01-14T18:00:00Z'), // 18 hours ago
        'split',
        25
      )

      expect(result.percentage).toBe(100)
      expect(result.hoursElapsed).toBe(18)
      expect(result.requiredHours).toBe(18)
      expect(result.isReady).toBe(true)
      expect(result.remainingTime).toBe('Ready now!')
      expect(result.message).toBe("Let's train!")
    })

    it('caps percentage at 100% even if over-recovered', () => {
      const result = calculateRecoveryInfo(
        new Date('2024-01-13T12:00:00Z'), // 48 hours ago
        'split',
        25
      )

      expect(result.percentage).toBe(100)
      expect(result.hoursElapsed).toBe(48)
      expect(result.requiredHours).toBe(18)
      expect(result.isReady).toBe(true)
    })

    it('handles workouts within 24 hours correctly', () => {
      const result = calculateRecoveryInfo(
        new Date('2024-01-15T00:00:00Z'), // 12 hours ago
        'split',
        25
      )

      expect(result.hoursElapsed).toBe(12)
      expect(result.requiredHours).toBe(18) // Should use normal recovery hours
      expect(result.percentage).toBe(67) // 12/18 = 66.67, rounded to 67
    })

    it('handles string dates correctly', () => {
      const result = calculateRecoveryInfo('2024-01-15T03:00:00Z', 'split', 25)

      expect(result.percentage).toBe(50)
      expect(result.hoursElapsed).toBe(9)
    })
  })

  describe('formatTimeRemaining', () => {
    it('formats minutes correctly', () => {
      expect(formatTimeRemaining(0.5)).toBe('30 minutes')
      expect(formatTimeRemaining(0.25)).toBe('15 minutes')
      expect(formatTimeRemaining(0.0167)).toBe('1 minute')
    })

    it('formats hours correctly', () => {
      expect(formatTimeRemaining(1)).toBe('1 hour')
      expect(formatTimeRemaining(2)).toBe('2 hours')
      expect(formatTimeRemaining(1.5)).toBe('1h 30m')
      expect(formatTimeRemaining(23.75)).toBe('23h 45m')
    })

    it('formats days correctly', () => {
      expect(formatTimeRemaining(24)).toBe('1 day')
      expect(formatTimeRemaining(48)).toBe('2 days')
      expect(formatTimeRemaining(36)).toBe('1d 12h')
      expect(formatTimeRemaining(25)).toBe('1d 1h')
    })

    it('handles zero or negative values', () => {
      expect(formatTimeRemaining(0)).toBe('Ready now!')
      expect(formatTimeRemaining(-5)).toBe('Ready now!')
    })
  })

  describe('getCoachMessage', () => {
    it('returns correct messages based on percentage', () => {
      expect(getCoachMessage(0, false)).toBe('Rest and recover')
      expect(getCoachMessage(24, false)).toBe('Rest and recover')
      expect(getCoachMessage(25, false)).toBe('Keep resting')
      expect(getCoachMessage(49, false)).toBe('Keep resting')
      expect(getCoachMessage(50, false)).toBe('Almost ready')
      expect(getCoachMessage(74, false)).toBe('Almost ready')
      expect(getCoachMessage(75, false)).toBe('Nearly there!')
      expect(getCoachMessage(99, false)).toBe('Nearly there!')
      expect(getCoachMessage(100, true)).toBe("Let's train!")
    })
  })

  describe('getProgramType', () => {
    it('identifies split programs', () => {
      expect(getProgramType('Upper/Lower Split')).toBe('split')
      expect(getProgramType('Push Pull Legs')).toBe('split')
      expect(getProgramType('3-Day Split')).toBe('split')
    })

    it('identifies full-body programs', () => {
      expect(getProgramType('Full Body Workout')).toBe('full-body')
      expect(getProgramType('3x Fullbody')).toBe('full-body')
    })

    it('identifies powerlifting programs', () => {
      expect(getProgramType('Powerlifting Program')).toBe('powerlifting')
      expect(getProgramType('Strength Training')).toBe('powerlifting')
    })

    it('returns other for unidentified programs', () => {
      expect(getProgramType('Custom Workout')).toBe('other')
      expect(getProgramType('My Program')).toBe('other')
      expect(getProgramType()).toBe('other')
    })
  })
})
