import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useWorkout } from '@/hooks/useWorkout'
import { workoutApi } from '@/api/workouts'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
// import type { NewExerciceLogModel } from '@/types'
// import { mockWorkoutTemplateGroup } from '@/tests/unit/mocks/workout.mocks'

// Mock dependencies
vi.mock('@/api/workouts')
vi.mock('@/stores/workoutStore')
vi.mock('@/stores/authStore')

// Create wrapper component for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: { retry: false },
    },
  })

  return function ({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

// Using imported mock data
const mockWorkoutGroup = mockWorkoutTemplateGroup

describe('useWorkout Hook', () => {
  const mockSetWorkout = vi.fn()
  const mockStartWorkout = vi.fn()
  const mockSaveSet = vi.fn()
  const mockCompleteWorkout = vi.fn()
  const mockSetLoading = vi.fn()
  const mockSetError = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock auth store
    ;(useAuthStore as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      isAuthenticated: true,
      token: 'mock-token',
    })

    // Mock workout store
    ;(useWorkoutStore as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      currentWorkout: null,
      exercises: [],
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      workoutSession: null,
      isLoading: false,
      error: null,
      setWorkout: mockSetWorkout,
      startWorkout: mockStartWorkout,
      saveSet: mockSaveSet,
      completeWorkout: mockCompleteWorkout,
      setLoading: mockSetLoading,
      setError: mockSetError,
      getCurrentExercise: vi.fn(),
      getCurrentSet: vi.fn().mockReturnValue({ isWarmup: false }),
      getNextExercise: vi.fn().mockReturnValue(null),
      getRestDuration: vi.fn().mockReturnValue(120),
      nextSet: vi.fn(),
      nextExercise: vi.fn(),
    })
  })

  describe('Workout Fetching', () => {
    it("should fetch today's workout on mount", async () => {
      // Given
      ;(
        workoutApi.getTodaysWorkout as unknown as ReturnType<typeof vi.fn>
      ).mockResolvedValueOnce([mockWorkoutGroup])

      // When
      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Then
      await waitFor(() => {
        expect(result.current.todaysWorkout).toBeDefined()
        expect(result.current.isLoadingWorkout).toBe(false)
      })

      expect(workoutApi.getTodaysWorkout).toHaveBeenCalled()
      expect(mockSetWorkout).toHaveBeenCalledWith(
        mockWorkoutGroup.WorkoutTemplates[0]
      )
    })

    it('should handle empty workout response', async () => {
      // Given
      ;(
        workoutApi.getTodaysWorkout as unknown as ReturnType<typeof vi.fn>
      ).mockResolvedValueOnce([])

      // When
      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Then
      await waitFor(() => {
        expect(result.current.todaysWorkout).toEqual([])
        expect(result.current.isLoadingWorkout).toBe(false)
      })

      expect(mockSetWorkout).not.toHaveBeenCalled()
    })

    it('should handle fetch errors', async () => {
      // Given
      const error = new Error('Network error')
      ;(
        workoutApi.getTodaysWorkout as unknown as ReturnType<typeof vi.fn>
      ).mockRejectedValueOnce(error)

      // When
      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Then
      await waitFor(() => {
        expect(result.current.workoutError).toBeDefined()
        expect(result.current.isLoadingWorkout).toBe(false)
      })

      expect(mockSetError).toHaveBeenCalledWith('Failed to load workout')
    })
  })

  describe('Set Saving', () => {
    it('should save set with optimistic update', async () => {
      // Given
      const setData = {
        exerciseId: 1,
        weight: { Lb: 100, Kg: 45.36 },
        reps: 8,
        isWarmup: false,
        setNumber: 1,
        RIR: 2,
      }

      ;(
        workoutApi.saveWorkoutSet as unknown as ReturnType<typeof vi.fn>
      ).mockResolvedValueOnce({
        Result: true,
        Message: 'Success',
      })

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // When
      await result.current.saveSet(setData)

      // Then
      await waitFor(() => {
        expect(mockSaveSet).toHaveBeenCalled()
        expect(workoutApi.saveWorkoutSet).toHaveBeenCalledWith({
          ExerciceId: 1,
          Weight1: { Lb: 100, Kg: 45.36 },
          Reps1: '8',
          Username: '',
          RIR: 2,
          VersionNo: 1,
        })
      })
    })

    it('should handle save errors and rollback', async () => {
      // Given
      const setData = {
        exerciseId: 1,
        weight: { Lb: 100, Kg: 45.36 },
        reps: 8,
        isWarmup: false,
        setNumber: 1,
        RIR: 2,
      }

      ;(
        workoutApi.saveWorkoutSet as unknown as ReturnType<typeof vi.fn>
      ).mockRejectedValueOnce(new Error('Save failed'))

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // When/Then
      await expect(result.current.saveSet(setData)).rejects.toThrow()
    })
  })

  describe('Workout Completion', () => {
    it('should complete workout successfully', async () => {
      // Given
      const mockSession = {
        id: 'session-1',
        startTime: new Date(Date.now() - 3600000),
        endTime: new Date(),
        exercises: [
          {
            exerciseId: 1,
            sets: [
              {
                reps: 8,
                weight: 100,
                rir: 2,
                isWarmup: false,
                timestamp: new Date(),
              },
            ],
          },
        ],
      }

      ;(useWorkoutStore as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
        {
          ...useWorkoutStore(),
          workoutSession: mockSession,
          currentWorkout: mockWorkoutGroup.WorkoutTemplates[0],
        }
      )
      ;(
        workoutApi.completeWorkout as unknown as ReturnType<typeof vi.fn>
      ).mockResolvedValueOnce({
        Result: true,
        Message: 'Workout completed',
      })

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // When
      await result.current.finishWorkout()

      // Then
      await waitFor(() => {
        expect(mockCompleteWorkout).toHaveBeenCalled()
        expect(workoutApi.completeWorkout).toHaveBeenCalled()
      })
    })

    it('should handle completion errors', async () => {
      // Given
      ;(
        workoutApi.completeWorkout as unknown as ReturnType<typeof vi.fn>
      ).mockRejectedValueOnce(new Error('Completion failed'))

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // When/Then
      await expect(result.current.finishWorkout()).rejects.toThrow()
    })
  })

  describe('Exercise Recommendations', () => {
    it('should fetch exercise recommendation', async () => {
      // Given
      const exerciseId = 1
      const mockRecommendation = {
        Exercise: mockWorkoutGroup.WorkoutTemplates[0].Exercises[0],
        ShowRecommendation: true,
        Recommendation: { Mass: 105, MassUnit: 'lbs' },
      }

      ;(
        workoutApi.getExerciseRecommendation as unknown as ReturnType<
          typeof vi.fn
        >
      ).mockResolvedValueOnce(mockRecommendation)

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // When
      const recommendation = await result.current.getRecommendation(exerciseId)

      // Then
      expect(recommendation).toEqual(mockRecommendation)
      expect(workoutApi.getExerciseRecommendation).toHaveBeenCalledWith(
        exerciseId
      )
    })
  })

  describe('Offline Support', () => {
    it('should queue set saves when offline', async () => {
      // Given
      // Simulate offline
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false,
      })

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // When
      await result.current.saveSet({
        exerciseId: 1,
        weight: { Lb: 100, Kg: 45.36 },
        reps: 8,
        isWarmup: false,
        setNumber: 1,
        RIR: 2,
      })

      // Then
      expect(mockSaveSet).toHaveBeenCalled() // Optimistic update
      expect(workoutApi.saveWorkoutSet).not.toHaveBeenCalled() // API not called

      // Restore online
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: true,
      })
    })

    it('should sync queued data when back online', async () => {
      // This would require implementing a queue system
      // For now, just verify the hook handles offline state
      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      expect(result.current.isOffline).toBe(false)
    })
  })

  describe('Integration', () => {
    it('should provide complete workout interface', () => {
      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Verify all expected properties and methods
      expect(result.current).toHaveProperty('todaysWorkout')
      expect(result.current).toHaveProperty('isLoadingWorkout')
      expect(result.current).toHaveProperty('workoutError')
      expect(result.current).toHaveProperty('currentExercise')
      expect(result.current).toHaveProperty('currentSetIndex')
      expect(result.current).toHaveProperty('saveSet')
      expect(result.current).toHaveProperty('startWorkout')
      expect(result.current).toHaveProperty('finishWorkout')
      expect(result.current).toHaveProperty('nextSet')
      expect(result.current).toHaveProperty('nextExercise')
      expect(result.current).toHaveProperty('getRecommendation')
      expect(result.current).toHaveProperty('isOffline')
    })
  })
})
