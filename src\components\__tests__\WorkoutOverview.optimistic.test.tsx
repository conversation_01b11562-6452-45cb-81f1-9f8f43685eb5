import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { WorkoutOverview } from '../workout/WorkoutOverview'
import { useWorkout } from '@/hooks/useWorkout'
import type { UseWorkoutReturn } from '@/hooks/useWorkout'
import type {
  WorkoutTemplateGroupModel,
  WorkoutTemplateModel,
  GetUserProgramInfoResponseModel,
  ExerciseWorkSetsModel,
} from '@/types'

// Mock the useWorkout hook
vi.mock('@/hooks/useWorkout')

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
}))

// Mock data
const mockWorkout: WorkoutTemplateModel = {
  Id: 1,
  UserId: 'test-user',
  Label: 'Push Day',
  Exercises: [
    {
      Id: 1,
      Label: 'Bench Press',
      TargetWeight: { Mass: 100, MassUnit: 'lbs' },
      TargetReps: 8,
      IsWarmup: false,
      HasPastLogs: true,
    },
    {
      Id: 2,
      Label: 'Shoulder Press',
      TargetWeight: { Mass: 60, MassUnit: 'lbs' },
      TargetReps: 10,
      IsWarmup: false,
      HasPastLogs: true,
    },
  ],
  IsSystemExercise: false,
  WorkoutSettingsModel: {
    Id: 1,
    Pause: 120,
    Equipment: '',
    ChildWorkoutTemplateId: null,
    SetsModel: null,
    WorkoutProgramId: 1,
    IsFirstSet: false,
    IsFail: false,
    NbRepsMinimalInc: null,
    AvgDuration: null,
    IsNotRealData: false,
  },
}

const mockWorkoutGroup: WorkoutTemplateGroupModel = {
  Id: 1,
  Label: 'Beginner Program',
  WorkoutTemplates: [mockWorkout],
  IsFeaturedProgram: false,
  UserId: '',
  IsSystemExercise: true,
  RequiredWorkoutToLevelUp: 10,
  ProgramId: 1,
}

const mockUserProgramInfo: GetUserProgramInfoResponseModel = {
  UserId: 'test-user',
  WeeklyStatus: 'Week 1',
  ProgramLabel: 'Test Program',
  NbDaysInTheWeek: 5,
  NbNonTrainingDays: 2,
  MondayIsFirst: false,
  TimeLogged: '2024-01-01T10:00:00',
  NextWorkoutDayText: 'Today',
  IsInIntroWorkout: false,
  IsInFirstWeek: true,
  TodaysWorkoutId: '1234',
  TodaysWorkoutText: 'Push Day',
  RecommendedProgram: {
    Id: 1,
    Label: 'Beginner Program',
    RemainingToLevelUp: 10,
    IconUrl: 'https://example.com/icon.png',
  },
  NextWorkoutTemplate: mockWorkout,
  NextNonTrainingDay: '2024-01-03',
  MondayHere: '2024-01-01',
  NextIntensityTechnique: 'Standard',
  ServerTimeUtc: '2024-01-01T10:00:00Z',
  MaxWorkoutSets: 20,
  NbMediumSets: 5,
  NbChallenges: 3,
  WorkoutTemplates: [mockWorkout],
}

// Helper to create mock exercises in the new format
const createMockExercises = (): ExerciseWorkSetsModel[] => {
  return (
    mockWorkout.Exercises?.map((ex) => ({
      Id: ex.Id,
      Label: ex.Label,
      MediumName: ex.Label,
      MuscleGroup: 'Chest',
      LastLogDate: null,
      Url: '',
      IsBodyweight: false,
      IsEasy: false,
      IsAlternative: false,
      IsNextExercise: false,
      IsFinished: false,
      HasRecommendation: false,
      ExerciseSettingsModel: null,
      sets: [],
      isLoadingSets: false,
      setsError: null,
    })) || []
  )
}

// Helper to create a base mock return value
const createBaseMockReturn = (
  overrides: Partial<UseWorkoutReturn> = {}
): UseWorkoutReturn => ({
  todaysWorkout: null,
  userProgramInfo: null,
  exercises: [],
  currentWorkout: null,
  currentExercise: null,
  nextExercise: null,
  currentSetIndex: 0,
  totalSets: 0,
  isLastSet: false,
  isLastExercise: false,
  isWarmupSet: false,
  workoutSession: null,
  isLoading: false,
  isLoadingWorkout: false,
  hasInitialData: false,
  isLoadingFresh: false,
  loadingStates: {
    programInfo: false,
    userWorkout: false,
    recommendation: false,
  },
  workoutError: null,
  error: null,
  recommendation: null,
  refetchRecommendation: vi.fn(),
  saveSet: vi.fn(),
  startWorkout: vi.fn(),
  finishWorkout: vi.fn(),
  nextSet: vi.fn(),
  restoreWorkout: vi.fn(),
  goToNextExercise: vi.fn(),
  getRecommendation: vi.fn(),
  getRestDuration: vi.fn(),
  isOffline: false,
  expectedExerciseCount: undefined,
  refreshWorkout: vi.fn(),
  updateExerciseWorkSets: vi.fn(),
  ...overrides,
})

const mockUseWorkout = vi.mocked(useWorkout)

describe('WorkoutOverview - Optimistic Loading', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Loading State Optimization', () => {
    it('should show cached data immediately without skeleton', () => {
      // Given: Cached workout data available
      mockUseWorkout.mockReturnValue(
        createBaseMockReturn({
          todaysWorkout: [mockWorkoutGroup],
          userProgramInfo: mockUserProgramInfo,
          exercises: createMockExercises(),
          isLoadingWorkout: false,
          hasInitialData: true,
          isLoadingFresh: false,
          expectedExerciseCount: 2,
        })
      )

      // When: Component renders
      render(<WorkoutOverview />)

      // Then: Workout content shown, no skeleton
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
      expect(screen.getByText('Shoulder Press')).toBeInTheDocument()
      expect(
        screen.queryByTestId('workout-card-skeleton')
      ).not.toBeInTheDocument()
    })

    it('should show skeleton only when no data available at all', () => {
      // Given: No cached data, initial load
      mockUseWorkout.mockReturnValue(
        createBaseMockReturn({
          todaysWorkout: null,
          userProgramInfo: null,
          isLoadingWorkout: true,
          hasInitialData: false,
          isLoadingFresh: false,
          isLoading: true,
          loadingStates: {
            programInfo: true,
            userWorkout: true,
            recommendation: false,
          },
        })
      )

      // When: Component renders
      render(<WorkoutOverview />)

      // Then: Skeleton shown
      expect(screen.getByTestId('workout-card-skeleton')).toBeInTheDocument()
    })

    it.skip('should show subtle refresh indicator during background sync', () => {
      // Given: Cached data with background refresh
      mockUseWorkout.mockReturnValue(
        createBaseMockReturn({
          todaysWorkout: [mockWorkoutGroup],
          userProgramInfo: mockUserProgramInfo,
          exercises: createMockExercises(),
          isLoadingWorkout: false,
          hasInitialData: true,
          isLoadingFresh: true, // Background sync in progress
          loadingStates: {
            programInfo: true,
            userWorkout: true,
            recommendation: false,
          },
          expectedExerciseCount: 2,
        })
      )

      // When: Component renders
      render(<WorkoutOverview />)

      // Then: Content shown with refresh indicator
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
      expect(screen.getByText('Shoulder Press')).toBeInTheDocument()
      expect(
        screen.queryByTestId('workout-card-skeleton')
      ).not.toBeInTheDocument()
      expect(screen.getByText('Checking for updates...')).toBeInTheDocument()
    })

    it('should handle smooth transitions when fresh data arrives', () => {
      // Given: Initial render with cached data
      const { rerender } = render(<WorkoutOverview />)

      mockUseWorkout.mockReturnValue(
        createBaseMockReturn({
          todaysWorkout: [mockWorkoutGroup],
          userProgramInfo: mockUserProgramInfo,
          exercises: createMockExercises(),
          isLoadingWorkout: false,
          hasInitialData: true,
          isLoadingFresh: true,
          loadingStates: {
            programInfo: true,
            userWorkout: true,
            recommendation: false,
          },
          expectedExerciseCount: 2,
        })
      )

      // When: Fresh data arrives
      const freshWorkout = {
        ...mockWorkout,
        Label: 'Updated Push Day',
      }
      const freshWorkoutGroup = {
        ...mockWorkoutGroup,
        WorkoutTemplates: [freshWorkout],
      }

      mockUseWorkout.mockReturnValue(
        createBaseMockReturn({
          todaysWorkout: [freshWorkoutGroup],
          userProgramInfo: mockUserProgramInfo,
          exercises: createMockExercises(),
          isLoadingWorkout: false,
          hasInitialData: true,
          isLoadingFresh: false, // Background sync complete
          loadingStates: {
            programInfo: false,
            userWorkout: false,
            recommendation: false,
          },
          expectedExerciseCount: 2,
        })
      )

      rerender(<WorkoutOverview />)

      // Then: Updated content shown without skeleton
      expect(screen.getByText('Bench Press')).toBeInTheDocument() // Still shows exercises
      expect(screen.getByText('Shoulder Press')).toBeInTheDocument()
      expect(
        screen.queryByTestId('workout-card-skeleton')
      ).not.toBeInTheDocument()
      expect(
        screen.queryByText('Checking for updates...')
      ).not.toBeInTheDocument()
    })

    it('should show empty state when no workout data and not loading', () => {
      // Given: No workout data and not loading
      mockUseWorkout.mockReturnValue(
        createBaseMockReturn({
          todaysWorkout: null,
          userProgramInfo: null,
          isLoadingWorkout: false,
          hasInitialData: false,
          isLoadingFresh: false,
        })
      )

      // When: Component renders
      render(<WorkoutOverview />)

      // Then: Empty state shown
      expect(screen.getByText('No Workout Available')).toBeInTheDocument()
      expect(
        screen.queryByTestId('workout-card-skeleton')
      ).not.toBeInTheDocument()
    })

    it('should handle offline mode with cached data', () => {
      // Given: Offline with cached data
      mockUseWorkout.mockReturnValue(
        createBaseMockReturn({
          todaysWorkout: [mockWorkoutGroup],
          userProgramInfo: mockUserProgramInfo,
          exercises: createMockExercises(),
          isLoadingWorkout: false,
          hasInitialData: true,
          isLoadingFresh: false,
          isOffline: true, // Offline mode
          expectedExerciseCount: 2,
        })
      )

      // When: Component renders
      render(<WorkoutOverview />)

      // Then: Content shown with offline indicator
      expect(screen.getByText('Bench Press')).toBeInTheDocument()
      expect(screen.getByText('Shoulder Press')).toBeInTheDocument()
      expect(screen.getByText('Offline Mode')).toBeInTheDocument()
      expect(
        screen.queryByTestId('workout-card-skeleton')
      ).not.toBeInTheDocument()
    })
  })
})
