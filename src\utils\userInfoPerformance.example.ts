/**
 * Example usage of UserInfo Performance Tracking
 *
 * This shows how to integrate the performance tracking utilities
 * into the progressive loading flow.
 */

import {
  trackLoginSuccess,
  trackUserInfoFetch,
  trackUserInfoFirstByte,
  trackUserInfoComplete,
  trackStatsFetch,
  trackStatsComplete,
  trackMetricLoaded,
  trackCacheOperation,
  trackPageReady,
  getUserInfoMetrics,
  reportUserInfoPerformance,
} from './userInfoPerformance'

// Helper functions (simplified for example)
function checkUserInfoCache() {
  // Implementation would check authStore cache
  return null
}

async function cacheUserInfo(data: unknown) {
  // Implementation would update authStore cache
  // Would use 'data' parameter in real implementation
  // eslint-disable-next-line no-void
  void data // Acknowledge parameter without using it
  await new Promise((resolve) => setTimeout(resolve, 5))
}

async function fetchStats() {
  // Implementation would call stats API
  return {
    consecutiveWeeks: 5,
    totalWorkoutsCompleted: 42,
    totalVolume: 150000,
  }
}

// React imports for example (simplified)
declare const React: {
  useEffect: (callback: () => void, deps: unknown[]) => void
  Component: unknown
  createElement: (component: unknown) => ReactElement
  ReactElement: unknown
}
type ReactElement = unknown
function ProgramPageContent() {
  return null
}

// Example 1: Login Flow Integration
export function trackLoginFlow() {
  // In login success handler (e.g., useAuth hook)
  trackLoginSuccess()

  // Start UserInfo fetch immediately
  trackUserInfoFetch()
}

// Example 2: UserInfo API Integration
export async function fetchUserInfoWithTracking() {
  const startTime = performance.now()

  // Check cache first
  const cached = checkUserInfoCache()
  if (cached) {
    const cacheReadTime = performance.now() - startTime
    trackCacheOperation('hit', cacheReadTime)
    trackUserInfoComplete(true) // fromCache = true
    return cached
  }

  trackCacheOperation('miss')

  try {
    // Make API call
    const response = await fetch('/api/Account/GetUserInfoPyramid')

    // Track first byte
    trackUserInfoFirstByte()

    const data = await response.json()

    // Track completion
    trackUserInfoComplete(false)

    // Cache the data
    const cacheStartTime = performance.now()
    await cacheUserInfo(data)
    const cacheWriteTime = performance.now() - cacheStartTime
    trackCacheOperation('write', cacheWriteTime)

    return data
  } catch (error) {
    // Track retry if applicable
    trackUserInfoFetch(true) // isRetry = true
    throw error
  }
}

// Example 3: Stats Loading with Metric Tracking
export async function loadStatsWithTracking() {
  trackStatsFetch()

  try {
    const stats = await fetchStats()

    trackStatsComplete()

    // Track individual metrics as they're processed
    if (stats.consecutiveWeeks !== undefined) {
      trackMetricLoaded('streak')
    }

    if (stats.totalWorkoutsCompleted !== undefined) {
      trackMetricLoaded('workouts')
    }

    if (stats.totalVolume !== undefined) {
      trackMetricLoaded('volume')
    }

    return stats
  } catch (error) {
    // Track retry
    trackStatsFetch(true)
    throw error
  }
}

// Example 4: Program Page Component
export function ProgramPageWithTracking(): React.ReactElement {
  // In useEffect or component mount
  React.useEffect(() => {
    // Start loading data
    Promise.all([fetchUserInfoWithTracking(), loadStatsWithTracking()]).then(
      () => {
        // All data loaded
        trackPageReady()

        // Report metrics
        reportUserInfoPerformance()
      }
    )
  }, [])

  return React.createElement(ProgramPageContent) as ReactElement
}

// Example 5: Monitoring in Development
export function setupDevelopmentMonitoring() {
  if (process.env.NODE_ENV === 'development') {
    // Log metrics every 5 seconds during development
    setInterval(() => {
      const metrics = getUserInfoMetrics()

      if (metrics.loginToFirstByte !== null) {
        // eslint-disable-next-line no-console
        console.log('Current performance metrics:', {
          firstByte: `${metrics.loginToFirstByte.toFixed(2)}ms`,
          cacheHitRate: `${metrics.cacheMetrics.hitRate.toFixed(1)}%`,
          retries: metrics.retryMetrics.totalRetries,
        })
      }
    }, 5000)
  }
}

// Example 6: Error Boundary Integration
export class PerformanceErrorBoundary {
  componentDidCatch(error: Error) {
    // Would use 'error' parameter in real implementation
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _ = error
    // Report current metrics before error
    const metrics = getUserInfoMetrics()

    // eslint-disable-next-line no-console
    console.error('Error occurred with metrics:', {
      pageReady: metrics.overallPageReady,
      retries: metrics.retryMetrics,
      lastOperation: performance.getEntriesByType('mark').slice(-1)[0]?.name,
    })

    // Send error metrics to analytics
    reportUserInfoPerformance()
  }
}

/**
 * Performance Tracking Features:
 *
 * 1. **Comprehensive Timing**: Tracks every stage from login to page ready
 * 2. **Cache Performance**: Monitors hit rate and latency
 * 3. **Retry Tracking**: Counts API retry attempts
 * 4. **Individual Metrics**: Times each stat metric separately
 * 5. **Analytics Integration**: Sends to Google Analytics in production
 * 6. **Development Tools**: Console reporting for debugging
 * 7. **Session Management**: Unique session IDs for tracking
 */
