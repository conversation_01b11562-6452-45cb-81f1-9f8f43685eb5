I'll analyze the MAUI mobile app to identify all API calls related to the open exercise step and show how they're handled locally. Let me start by searching for the relevant code.

Let me search for more specific implementation details to provide complete code examples.

Let me search for the specific API endpoints related to open exercises.

Now let me search for the GetExerciseForOpen API endpoint:

Now let me search for more API endpoints, particularly those related to getting recommendations and saving workouts:

Let me search for the specific API client methods:

Now let me search for how open exercises are created:

Let me check the CreateNewExercise API method:

Now let me look for the AddExercisesForWorkout API:

Let me search for how exercises are added to workouts:

Let me check how local data is handled:

Now let me create a comprehensive overview of all API calls for the open exercise step in order:

Based on my analysis of the MAUI mobile app code, here are all the API calls for the open exercise step in order, with complete logic and local data handling:

## Open Exercise API Flow

### 1. Get User Exercises List

**Endpoint:** `POST /api/Exercise/GetUserExercise`

```csharp
// Request
string username = LocalDBManager.Instance.GetDBSetting("email").Value;

// API Call
GetUserExerciseResponseModel response = await DrMuscleRestClient.Instance.GetUserExercise(username);

// Local Handling
// First tries to load from local JSON file "Exercises.json"
if (exercises == null || exercises.Count == 0)
{
    // Load from embedded resource
    string jsonFileName = "Exercises.json";
    var stream = await FileSystem.OpenAppPackageFileAsync(jsonFileName);
    var json = await new StreamReader(stream).ReadToEndAsync();
    var exerciseList = JsonConvert.DeserializeObject<List<ExerciceModel>>(json);
}

// Then merge with user's custom exercises from API
exercises.AddRange(response.Exercises);

// Filter and group by body parts
var grouped = exercises.Where(x => x.BodyPartId != 1)
    .GroupBy(x => x.BodyPartId)
    .OrderBy(g => g.Key);
```

### 2. Get Custom Exercises (Optional)

**Endpoint:** `POST /api/Exercise/GetCustomExerciseForUser`

```csharp
// Request
string username = LocalDBManager.Instance.GetDBSetting("email").Value;

// API Call (without loader for better UX)
GetUserExerciseResponseModel customExercises =
    await DrMuscleRestClient.Instance.GetCustomExerciseForUser(username);

// Local Handling
customItems.AddRange(customExercises.Exercises);
```

### 3. Create New Custom Exercise

**Endpoint:** `POST /api/Exercise/AddUserExercise`

```csharp
// Request Model
AddUserExerciseModel newExercise = new AddUserExerciseModel
{
    Username = LocalDBManager.Instance.GetDBSetting("email").Value,
    Label = exerciseName, // User input
    IsBodyweight = isBodyweight, // User selection
    IsTimeBased = isTimeBased, // User selection
    BodyPartId = selectedBodyPartId, // From body part selection
    IsEasy = isEasy, // Difficulty selection
    IsMedium = isMedium,
    IsFlexibility = isFlexibility
};

// API Call
ExerciceModel createdExercise =
    await DrMuscleRestClient.Instance.CreateNewExercise(newExercise);

// Local Handling
customItems.Add(createdExercise);
CurrentLog.Instance.IsAddedNewExercise = true;
CurrentLog.Instance.CurrentExercise = createdExercise;
```

### 4. Get Exercise Recommendation

**Endpoint:** `POST /api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew` or `GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew`

```csharp
// Determine which API to call based on exercise type
bool isPyramid = LocalDBManager.Instance.GetDBSetting("IsPyramid").Value == "true";
bool isBodyweight = exercise.IsBodyweight;
string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;

// Request Model
GetRecommendationForExerciseModel recoRequest = new GetRecommendationForExerciseModel
{
    Username = LocalDBManager.Instance.GetDBSetting("email").Value,
    ExerciseId = exercise.Id,
    IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "true",
    LightSessionDays = sessionDays > 9 ? sessionDays : null,
    WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate?.Id,
    IsFreePlan = isFreePlan,
    IsStrengthPhase = isStrengthPhase,
    SwapedExId = swapedExerciseId
};

// API Call (choose based on conditions)
RecommendationModel recommendation;
if (isPyramid && isBodyweight && exercise.BodyPartId != 12)
{
    recommendation = await DrMuscleRestClient.Instance
        .GetRecommendationRestPauseRIRForExerciseWithoutDeload(recoRequest);
}
else
{
    recommendation = await DrMuscleRestClient.Instance
        .GetRecommendationNormalRIRForExerciseWithoutDeload(recoRequest);
}

// Local Caching
LocalDBManager.Instance.SetDBReco($"RReps{exercise.Id}{setStyle}",
    JsonConvert.SerializeObject(recommendation));
LocalDBManager.Instance.SetDBReco($"NbRepsGeneratedTime{exercise.Id}{setStyle}",
    DateTime.Now.ToString());
```

### 5. Setup New Exercise (First Time)

**Endpoint:** `POST /api/Exercise/AddNewExerciseLogWithMoreSet`

```csharp
// Request Model
NewExerciceLogModel setupModel = new NewExerciceLogModel
{
    Username = LocalDBManager.Instance.GetDBSetting("email").Value,
    ExerciseId = exercise.Id,
    WorkoutSetList = new List<WorkoutSet>
    {
        new WorkoutSet
        {
            Reps = maxReps, // From user input
            Weight = isBodyweight ? new MultiUnityWeight(0, "kg") : weight,
            IsWarmup = false,
            IsBodyweight = isBodyweight
        }
    },
    NewPRMessage = "",
    Version = AppThemeConstants.GetBuildVersion()
};

// API Call
await DrMuscleRestClient.Instance.AddNewExerciseLogWithMoreSet(setupModel);

// Local Handling
string setStyle = LocalDBManager.Instance.GetDBSetting("SetStyle").Value;
if (exercise.BodyPartId == 12) // Flexibility
{
    LocalDBManager.Instance.SetDBReco($"RReps{exercise.Id}{setStyle}challenge", "");
}
else
{
    LocalDBManager.Instance.SetDBReco($"RReps{exercise.Id}{setStyle}challenge", "max");
}
```

### 6. Log Individual Set

**Endpoint:** `POST /api/Exercise/AddWorkoutLogSerieNew`

```csharp
// Request Model
WorkoutLogSerieModel setLog = new WorkoutLogSerieModel
{
    Username = LocalDBManager.Instance.GetDBSetting("email").Value,
    ExerciseId = exercise.Id,
    WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id,
    Reps = completedReps,
    Weight = weight,
    IsWarmup = isWarmup,
    IsBodyweight = exercise.IsBodyweight,
    IsFinished = false,
    RIR = rirValue, // Reps in Reserve
    RPE = rpeValue, // Rate of Perceived Exertion
    SetNo = setNumber,
    SessionDate = DateTime.Now,
    Version = AppThemeConstants.GetBuildVersion()
};

// API Call
await DrMuscleRestClient.Instance.AddWorkoutLogSerie(setLog);

// Local Handling
CurrentLog.Instance.WorkoutLogSeriesList.Add(new WorkoutLogSerieModelEx
{
    Id = response.Id,
    ExerciseId = exercise.Id,
    Reps = completedReps,
    Weight = weight,
    IsWarmup = isWarmup,
    // ... other properties
});
```

### 7. Complete Workout

**Endpoint:** `POST /api/Workout/SaveWorkoutV3Pro`

```csharp
// Request Model
SaveWorkoutModel saveModel = new SaveWorkoutModel
{
    WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id,
    Username = LocalDBManager.Instance.GetDBSetting("email").Value,
    CompletedExercises = CurrentLog.Instance.WorkoutLogSeriesList
        .GroupBy(x => x.ExerciseId)
        .Select(g => g.Key)
        .ToList(),
    TotalWorkoutTime = totalTime,
    Version = AppThemeConstants.GetBuildVersion()
};

// API Call
BooleanModel result = await DrMuscleRestClient.Instance.SaveWorkoutV3(saveModel);

// Local Handling
if (result.Result)
{
    // Clear current workout data
    CurrentLog.Instance.WorkoutLogSeriesList = new ObservableCollection<WorkoutLogSerieModelEx>();
    CurrentLog.Instance.CurrentWorkoutTemplate = null;

    // Update local stats
    var workoutCount = LocalDBManager.Instance.GetDBSetting("WorkoutCount")?.Value ?? "0";
    LocalDBManager.Instance.SetDBSetting("WorkoutCount",
        (int.Parse(workoutCount) + 1).ToString());

    // Update last workout date
    LocalDBManager.Instance.SetDBSetting("LastWorkoutDate", DateTime.Now.ToString());
}
```

## Local Data Storage Strategy

The app uses extensive local caching to minimize API calls:

1. **Exercise Database**: Cached locally in JSON format
2. **Recommendations**: Cached with timestamp, refreshed after 24 hours
3. **User Settings**: Stored in local SQLite database
4. **Plate Calculator**: Cached plate configurations by equipment type
5. **Workout Progress**: Temporarily stored during workout, synced on completion

### Key Local Storage Methods:

```csharp
// Store recommendation
LocalDBManager.Instance.SetDBReco(key, JsonConvert.SerializeObject(data));

// Store settings
LocalDBManager.Instance.SetDBSetting(key, value);

// Retrieve with null check
var value = LocalDBManager.Instance.GetDBSetting(key)?.Value;

// Check cache validity
var generatedTime = LocalDBManager.Instance.GetDBReco($"NbRepsGeneratedTime{exerciseId}{setStyle}");
if (DateTime.Parse(generatedTime) < DateTime.Now.AddHours(-24))
{
    // Refresh from API
}
```

The open exercise flow in the MAUI app follows this sequence of API calls:

1. **Get User Exercises** - Loads exercise list (cached locally first)
2. **Get Custom Exercises** - Fetches user's custom exercises
3. **Create New Exercise** - When user adds a custom exercise
4. **Get Recommendation** - Fetches AI-generated workout recommendations
5. **Setup New Exercise** - Initial configuration for first-time exercises
6. **Log Individual Sets** - Records each completed set
7. **Complete Workout** - Saves the entire workout session

Each API call includes comprehensive local caching to improve performance and enable offline functionality. The app intelligently decides between normal and rest-pause recommendations based on exercise type and user settings.
