'use client'

import React from 'react'
import { useRouter } from 'next/navigation'

export interface ProgramErrorStateProps {
  error: Error | null
  onRetry?: () => void
  className?: string
}

interface ErrorConfig {
  icon: React.ReactNode
  title: string
  message: string
  actionLabel: string
  actionHandler: () => void
}

export function ProgramErrorState({
  error,
  onRetry,
  className = '',
}: ProgramErrorStateProps) {
  const router = useRouter()

  const getErrorConfig = (): ErrorConfig => {
    // Network timeout error
    if (error?.name === 'TimeoutError' || error?.message?.includes('timeout')) {
      return {
        icon: (
          <svg
            className="w-16 h-16 mx-auto text-yellow-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        ),
        title: 'Connection Timeout',
        message:
          'The request took too long. Check your internet connection and try again.',
        actionLabel: 'Try Again',
        actionHandler: onRetry || (() => window.location.reload()),
      }
    }

    // Expired program access
    if (error?.name === 'ExpiredError' || error?.message?.includes('expired')) {
      return {
        icon: (
          <svg
            className="w-16 h-16 mx-auto text-orange-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
        ),
        title: 'Program Access Expired',
        message:
          'Your access to this program has expired. Please renew to continue.',
        actionLabel: 'Renew Access',
        actionHandler: () => router.push('/programs'),
      }
    }

    // Network/connection error
    if (error?.name === 'NetworkError' || error?.message?.includes('network')) {
      return {
        icon: (
          <svg
            className="w-16 h-16 mx-auto text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M18.364 5.636a9 9 0 010 12.728m0 0l-2.829-2.829m2.829 2.829L21 21M15.536 8.464a5 5 0 010 7.072m0 0l-2.829-2.829m-4.243 2.829a4.978 4.978 0 01-1.414-2.83m-1.414 5.658a9 9 0 01-2.167-9.238m7.824 2.167a1 1 0 111.414 1.414m-1.414-1.414L3 3m8.293 8.293l1.414 1.414"
            />
          </svg>
        ),
        title: 'No Internet Connection',
        message: 'Please check your internet connection and try again.',
        actionLabel: 'Retry',
        actionHandler: onRetry || (() => window.location.reload()),
      }
    }

    // Authentication error
    if (error?.message?.includes('auth') || error?.message?.includes('401')) {
      return {
        icon: (
          <svg
            className="w-16 h-16 mx-auto text-red-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
        ),
        title: 'Authentication Required',
        message: 'Your session has expired. Please log in again.',
        actionLabel: 'Log In',
        actionHandler: () => router.push('/login'),
      }
    }

    // Default error
    return {
      icon: (
        <svg
          className="w-16 h-16 mx-auto text-red-500"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      ),
      title: 'Failed to Load Program',
      message: error?.message || 'Something went wrong. Please try again.',
      actionLabel: 'Try Again',
      actionHandler: onRetry || (() => window.location.reload()),
    }
  }

  const config = getErrorConfig()

  return (
    <div
      className={`flex flex-col items-center justify-center min-h-[100dvh] p-4 ${className}`}
    >
      <div className="text-center space-y-4 max-w-sm">
        <div className="mb-4">{config.icon}</div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          {config.title}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">{config.message}</p>
        <button
          onClick={config.actionHandler}
          className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 active:bg-primary-800 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 min-w-[150px]"
          data-testid="error-action-button"
        >
          {config.actionLabel}
        </button>
        {onRetry && config.actionHandler !== onRetry && (
          <button
            onClick={onRetry}
            className="block mx-auto text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 underline"
          >
            Try different action
          </button>
        )}
      </div>
    </div>
  )
}
