import { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { programApi } from '@/api/program'
import { useAuthStore } from '@/stores/authStore'
import { useProgramStore } from '@/stores/programStore'
import type { ProgramProgress } from '@/types'

/**
 * Cache-enhanced hook to fetch program progress data
 * Shows cached data immediately while fetching fresh data in background
 */
export function useProgramProgressWithCache() {
  const { isAuthenticated } = useAuthStore()
  const {
    getCachedProgress,
    setCachedProgress,
    isProgressCacheStale,
    setIsLoadingProgress,
    setIsRefreshingProgress,
    setProgressError,
    hasHydrated,
  } = useProgramStore()

  const [optimisticProgress, setOptimisticProgress] =
    useState<ProgramProgress | null>(null)
  const [hasInitialData, setHasInitialData] = useState(false)

  // Load cached data immediately after hydration
  useEffect(() => {
    if (hasHydrated) {
      const cached = getCachedProgress()
      if (cached) {
        setOptimisticProgress(cached)
        setHasInitialData(true)
      }
    }
  }, [hasHydrated, getCachedProgress])

  const shouldFetch =
    isAuthenticated && (isProgressCacheStale() || !optimisticProgress)
  const isBackgroundRefresh = hasInitialData && isProgressCacheStale()

  const {
    data: freshProgress,
    isLoading: isQueryLoading,
    error,
    refetch,
  } = useQuery<ProgramProgress | null>({
    queryKey: ['programProgress'],
    queryFn: async () => {
      if (!hasInitialData) {
        setIsLoadingProgress(true)
      } else {
        setIsRefreshingProgress(true)
      }

      try {
        const data = await programApi.getProgramProgress()
        return data
      } finally {
        setIsLoadingProgress(false)
        setIsRefreshingProgress(false)
      }
    },
    enabled: shouldFetch,
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 60 * 60 * 1000, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  })

  // Update cache and optimistic data when fresh data arrives
  useEffect(() => {
    if (freshProgress !== undefined) {
      setCachedProgress(freshProgress)
      setOptimisticProgress(freshProgress)
      setHasInitialData(true)
    }
  }, [freshProgress, setCachedProgress])

  // Update error state
  useEffect(() => {
    setProgressError(error instanceof Error ? error : null)
  }, [error, setProgressError])

  return {
    progress: optimisticProgress,
    isLoading: !hasInitialData && isQueryLoading,
    isRefreshing: isBackgroundRefresh && isQueryLoading,
    error,
    refetch,
    hasInitialData,
  }
}
