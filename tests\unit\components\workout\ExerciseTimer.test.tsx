import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { render, screen, fireEvent, act } from '@testing-library/react'
import { ExerciseTimer } from '@/components/workout/ExerciseTimer'

describe('ExerciseTimer', () => {
  const mockOnComplete = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.clearAllTimers()
    vi.useRealTimers()
  })

  describe('Rest Timer', () => {
    it('should display rest timer with default duration', () => {
      // When
      render(
        <ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />
      )

      // Then
      expect(screen.getByText(/rest time/i)).toBeInTheDocument()
      expect(screen.getByText(/1:30/)).toBeInTheDocument()
    })

    it('should count down when started', async () => {
      // Given
      render(
        <ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />
      )

      // When
      fireEvent.click(screen.getByRole('button', { name: /start/i }))

      // Then
      expect(screen.getByText(/1:30/)).toBeInTheDocument()

      // Advance timer by 1 second
      act(() => {
        vi.advanceTimersByTime(1000)
      })

      expect(screen.getByText(/1:29/)).toBeInTheDocument()

      // Advance to near completion
      act(() => {
        vi.advanceTimersByTime(88000) // Total 89 seconds
      })

      expect(screen.getByText(/0:01/)).toBeInTheDocument()
    })

    it('should call onComplete when timer finishes', async () => {
      // Given
      render(<ExerciseTimer restDuration={3} onRestComplete={mockOnComplete} />)

      // When
      fireEvent.click(screen.getByRole('button', { name: /start/i }))

      act(() => {
        vi.advanceTimersByTime(3000)
      })

      // Then
      expect(mockOnComplete).toHaveBeenCalled()
      expect(screen.getByText(/0:00/)).toBeInTheDocument()
    })

    it('should pause and resume timer', async () => {
      // Given
      render(
        <ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />
      )

      // Start timer
      fireEvent.click(screen.getByRole('button', { name: /start/i }))

      act(() => {
        vi.advanceTimersByTime(10000) // 10 seconds
      })

      expect(screen.getByText(/1:20/)).toBeInTheDocument()

      // Pause
      fireEvent.click(screen.getByRole('button', { name: /pause/i }))

      act(() => {
        vi.advanceTimersByTime(5000) // 5 more seconds while paused
      })

      // Should still show same time
      expect(screen.getByText(/1:20/)).toBeInTheDocument()

      // Resume
      fireEvent.click(screen.getByRole('button', { name: /resume/i }))

      act(() => {
        vi.advanceTimersByTime(10000) // 10 more seconds
      })

      expect(screen.getByText(/1:10/)).toBeInTheDocument()
    })

    it('should reset timer', async () => {
      // Given
      render(
        <ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />
      )

      // Start and advance timer
      fireEvent.click(screen.getByRole('button', { name: /start/i }))

      act(() => {
        vi.advanceTimersByTime(30000) // 30 seconds
      })

      expect(screen.getByText(/1:00/)).toBeInTheDocument()

      // Reset
      fireEvent.click(screen.getByRole('button', { name: /reset/i }))

      // Then
      expect(screen.getByText(/1:30/)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /start/i })).toBeInTheDocument()
    })

    it('should skip rest timer', async () => {
      // Given
      render(
        <ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />
      )

      // When
      fireEvent.click(screen.getByRole('button', { name: /skip rest/i }))

      // Then
      expect(mockOnComplete).toHaveBeenCalled()
    })
  })

  describe('Exercise Timer', () => {
    it('should display exercise timer when exercise starts', () => {
      // When
      render(
        <ExerciseTimer
          isExercising
          restDuration={90}
          onRestComplete={mockOnComplete}
        />
      )

      // Then
      expect(screen.getByText(/exercise time/i)).toBeInTheDocument()
      expect(screen.getByText(/0:00/)).toBeInTheDocument()
    })

    it('should count up during exercise', () => {
      // Given
      render(
        <ExerciseTimer
          isExercising
          restDuration={90}
          onRestComplete={mockOnComplete}
        />
      )

      // When
      act(() => {
        vi.advanceTimersByTime(65000) // 65 seconds
      })

      // Then
      expect(screen.getByText(/1:05/)).toBeInTheDocument()
    })

    it('should show total workout time', () => {
      // Given
      const startTime = new Date(Date.now() - 1800000) // 30 minutes ago
      render(
        <ExerciseTimer
          workoutStartTime={startTime}
          restDuration={90}
          onRestComplete={mockOnComplete}
        />
      )

      // Then
      expect(screen.getByText(/total time: 30:00/i)).toBeInTheDocument()
    })
  })

  describe('Visual Indicators', () => {
    it('should show progress bar for rest timer', async () => {
      // Given
      render(
        <ExerciseTimer restDuration={10} onRestComplete={mockOnComplete} />
      )

      // When
      fireEvent.click(screen.getByRole('button', { name: /start/i }))

      // Then
      const progressBar = screen.getByRole('progressbar')
      expect(progressBar).toBeInTheDocument()
      expect(progressBar).toHaveAttribute('aria-valuenow', '10')

      act(() => {
        vi.advanceTimersByTime(5000) // 50% complete
      })

      expect(progressBar).toHaveAttribute('aria-valuenow', '5')
    })

    it('should show visual alert near end of rest', async () => {
      // Given
      render(
        <ExerciseTimer restDuration={10} onRestComplete={mockOnComplete} />
      )

      // When
      fireEvent.click(screen.getByRole('button', { name: /start/i }))

      act(() => {
        vi.advanceTimersByTime(7000) // 3 seconds remaining
      })

      // Then
      expect(screen.getByText(/0:03/)).toHaveClass('text-red-600')
    })

    it('should play sound notification when enabled', async () => {
      // Given
      const mockPlay = vi.fn().mockResolvedValue(undefined)
      global.Audio = vi.fn().mockImplementation(() => ({
        play: mockPlay,
      }))

      render(
        <ExerciseTimer
          restDuration={3}
          onRestComplete={mockOnComplete}
          soundEnabled
        />
      )

      // When
      fireEvent.click(screen.getByRole('button', { name: /start/i }))

      act(() => {
        vi.advanceTimersByTime(3000)
      })

      // Then
      expect(mockPlay).toHaveBeenCalled()
    })
  })

  describe('Mobile Optimizations', () => {
    it('should have large touch targets', () => {
      // When
      render(
        <ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />
      )

      // Then
      const buttons = screen.getAllByRole('button')
      buttons.forEach((button) => {
        const styles = window.getComputedStyle(button)
        const height =
          parseInt(styles.height, 10) || parseInt(styles.minHeight, 10) || 0
        expect(height).toBeGreaterThanOrEqual(44) // iOS minimum touch target
      })
    })

    it('should prevent screen sleep when timer active', async () => {
      // Given
      const mockWakeLock = {
        request: vi.fn().mockResolvedValue({
          release: vi.fn(),
        }),
      }
      Object.defineProperty(navigator, 'wakeLock', {
        value: mockWakeLock,
        writable: true,
      })

      render(
        <ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />
      )

      // When
      fireEvent.click(screen.getByRole('button', { name: /start/i }))

      // Wait for async wake lock request
      await vi.waitFor(() => {
        expect(mockWakeLock.request).toHaveBeenCalledWith('screen')
      })
    })
  })

  describe('Accessibility', () => {
    it('should announce time updates to screen readers', async () => {
      // Given
      render(
        <ExerciseTimer restDuration={10} onRestComplete={mockOnComplete} />
      )

      // When
      fireEvent.click(screen.getByRole('button', { name: /start/i }))

      // Then
      const liveRegion = screen.getByRole('timer')
      expect(liveRegion).toHaveAttribute('aria-live', 'polite')
      expect(liveRegion).toHaveAttribute(
        'aria-label',
        expect.stringContaining('Rest time')
      )
    })

    it('should support keyboard controls', async () => {
      // Given
      render(
        <ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />
      )
      const startButton = screen.getByRole('button', { name: /start/i })

      // When
      startButton.focus()
      fireEvent.click(startButton) // Click instead of keyDown since React handles Enter automatically

      // Then
      expect(screen.getByRole('button', { name: /pause/i })).toBeInTheDocument()
    })
  })
})
