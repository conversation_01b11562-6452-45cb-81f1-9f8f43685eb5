import { test, expect, Page, BrowserContext } from '@playwright/test'

test.describe('Cache Synchronization Tests', () => {
  let page: Page
  let context: BrowserContext
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p, context: c }) => {
    page = p
    context = c
    // Clear all caches before each test
    await page.goto('/')
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })
  })

  test('should sync cache across multiple tabs', async () => {
    // Login in first tab
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Open second tab
    const page2 = await context.newPage()
    await page2.goto('/program')

    // Should be logged in automatically
    await expect(page2).toHaveURL('/program')

    // Update data in first tab
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
    await page.click('[data-testid="exercise-item"]')
    await page.fill('input[name="weight"]', '100')
    await page.fill('input[name="reps"]', '10')
    await page.click('button:has-text("Save")')

    // Check if second tab sees the update
    await page2.goto('/workout')
    await page2.click('[data-testid="exercise-item"]')

    // Should see the saved data (or at least not conflict)
    await expect(page2.locator('text=Last: 100')).toBeVisible({ timeout: 5000 })

    await page2.close()
  })

  test('should handle cache conflicts gracefully', async () => {
    // Login and load workout
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Simulate cache conflict by manually modifying cache
    await page.evaluate(() => {
      const cache = localStorage.getItem('drmuscle-workout')
      if (cache) {
        const parsed = JSON.parse(cache)
        parsed.state._version = 'old-version'
        parsed.state._lastUpdated = Date.now() - 10000
        localStorage.setItem('drmuscle-workout', JSON.stringify(parsed))
      }
    })

    // Make an API call that would update cache
    await page.reload()

    // Should resolve conflict and show latest data
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

    // Cache should be updated
    const cacheVersion = await page.evaluate(() => {
      const cache = localStorage.getItem('drmuscle-workout')
      return cache ? JSON.parse(cache).state._version : null
    })

    expect(cacheVersion).not.toBe('old-version')
  })

  test('should respect cache TTL and refresh stale data', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Load workout data
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

    // Manually expire cache
    await page.evaluate(() => {
      const cache = localStorage.getItem('drmuscle-workout')
      if (cache) {
        const parsed = JSON.parse(cache)
        // Set cache timestamp to 25 hours ago (past 24h TTL)
        parsed.state._lastUpdated = Date.now() - 25 * 60 * 60 * 1000
        localStorage.setItem('drmuscle-workout', JSON.stringify(parsed))
      }
    })

    // Track API calls
    let apiCallMade = false
    await page.route('**/GetWorkoutInfo', (route) => {
      apiCallMade = true
      route.continue()
    })

    // Reload page
    await page.reload()

    // Should make fresh API call due to expired cache
    await page.waitForTimeout(1000)
    expect(apiCallMade).toBe(true)
  })

  test('should invalidate cache on data mutations', async () => {
    // Login and load workout
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Complete a set
    await page.click('[data-testid="exercise-item"]')
    await page.fill('input[name="weight"]', '100')
    await page.fill('input[name="reps"]', '10')

    // Check cache before save
    const cacheBeforeSave = await page.evaluate(() => {
      const cache = localStorage.getItem('drmuscle-workout')
      return cache ? JSON.parse(cache).state._lastUpdated : null
    })

    await page.click('button:has-text("Save")')
    await page.waitForTimeout(500)

    // Check cache after save
    const cacheAfterSave = await page.evaluate(() => {
      const cache = localStorage.getItem('drmuscle-workout')
      return cache ? JSON.parse(cache).state._lastUpdated : null
    })

    // Cache should be updated with new timestamp
    expect(cacheAfterSave).toBeGreaterThan(cacheBeforeSave)
  })

  test('should implement cache versioning for migrations', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Simulate old cache version
    await page.evaluate(() => {
      const oldCacheData = {
        version: 1,
        state: {
          oldFormat: true,
          data: 'legacy data',
        },
      }
      localStorage.setItem('drmuscle-workout-v1', JSON.stringify(oldCacheData))
    })

    // Load workout (should migrate cache)
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Check if cache was migrated
    const newCache = await page.evaluate(() => {
      const cache = localStorage.getItem('drmuscle-workout')
      const oldCache = localStorage.getItem('drmuscle-workout-v1')
      return {
        hasNewCache: !!cache,
        oldCacheRemoved: !oldCache,
      }
    })

    expect(newCache.hasNewCache).toBe(true)
  })
})
