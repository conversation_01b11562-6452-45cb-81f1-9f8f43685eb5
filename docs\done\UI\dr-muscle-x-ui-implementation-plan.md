# Dr. Muscle X UI Design System - Implementation Guide

## Overview

This guide provides step-by-step implementation prompts for creating the Dr. Muscle X design system. Each prompt is designed for test-driven development and builds upon previous work.

## Prerequisites

- Next.js 14 with TypeScript
- Tailwind CSS configured
- Testing setup (<PERSON>ites<PERSON> + Playwright)

## Implementation Phases

### Phase 1: Foundation (All Variations)

#### Prompt 1.1: Design System Architecture

```
Create the foundational design system architecture for Dr. Muscle X.

Requirements:
1. Create /src/design-system/index.ts as the main export
2. Create /src/design-system/tokens/ directory for design tokens
3. Create /src/design-system/components/ directory for UI components
4. Set up a theme provider that can switch between variations
5. Create type definitions for the design system

Tests to write first:
- Theme provider correctly provides theme context
- Design tokens are properly typed and exported
- Theme switching updates CSS variables
- Components can access theme values

Implementation:
- Use React Context for theme management
- Export all tokens and components from single entry point
- Ensure tree-shaking friendly exports
```

#### Prompt 1.2: Base Design Tokens

```
Implement the base design token system that all variations will use.

Requirements:
1. Create /src/design-system/tokens/base.ts with core values
2. Define spacing scale (4px base unit)
3. Define typography scale for headers and body
4. Define breakpoints for responsive design
5. Create animation/transition values

Tests to write first:
- All spacing values are multiples of base unit
- Typography scale follows consistent ratio
- Breakpoints match mobile-first approach
- Animation durations are within performance budget

Implementation:
- Use TypeScript const assertions for type safety
- Export individual token categories
- Include JSDoc comments for token usage
```

### Phase 2: Variation-Specific Tokens

#### Prompt 2.1: Subtle Depth Tokens

```
Create design tokens for the Subtle Depth variation.

Requirements:
1. Create /src/design-system/tokens/subtle-depth.ts
2. Define color palette: Deep charcoals (#0A0A0B to #1A1A1C)
3. Define elevation system: 5 shadow levels
4. Define gradient meshes for backgrounds
5. Typography: Playfair Display for headers, SF Pro for body

Tests to write first:
- Shadow values increase progressively
- Gradients have proper color stops
- Colors meet WCAG contrast requirements
- Font families load correctly

Reference: /docs/todo/subtle-depth-ui-plan.md
```

#### Prompt 2.2: Flat Bold Tokens

```
Create design tokens for the Flat Bold variation.

Requirements:
1. Create /src/design-system/tokens/flat-bold.ts
2. Define color palette: Pure black, white, electric accent (#00FF88)
3. No shadows or gradients
4. Sharp corners (0px border radius)
5. Typography: Bebas Neue for headers, Inter for body

Tests to write first:
- No shadow or gradient values present
- All colors are solid (no alpha)
- Border radius is always 0
- High contrast ratios achieved

Reference: /docs/todo/flat-bold-ui-plan.md
```

#### Prompt 2.3: Glassmorphism Tokens

```
Create design tokens for the Glassmorphism variation.

Requirements:
1. Create /src/design-system/tokens/glassmorphism.ts
2. Define glass effects: Multiple blur levels
3. Define aurora gradient patterns
4. Define translucent color values
5. Typography: SF Pro Display with light weights

Tests to write first:
- Backdrop filter values are valid
- Glass colors have proper opacity
- Gradients create aurora effect
- Blur values don't impact performance

Reference: /docs/todo/glassmorphism-ui-plan.md
```

#### Prompt 2.4: Ultra-Minimal Tokens

```
Create design tokens for the Ultra-Minimal variation.

Requirements:
1. Create /src/design-system/tokens/ultra-minimal.ts
2. Define minimal color palette: Black, white, single accent
3. Define hairline borders (0.5px)
4. Define golden ratio spacing
5. Typography: Didot for headers, Helvetica Neue for body

Tests to write first:
- Only essential colors defined
- Spacing follows golden ratio
- Borders render at sub-pixel width
- Typography creates clear hierarchy

Reference: /docs/todo/ultra-minimal-ui-plan.md
```

### Phase 3: Core Components

#### Prompt 3.1: Button Component (All Variations)

```
Create a Button component that adapts to all design variations.

Requirements:
1. Create /src/design-system/components/Button/Button.tsx
2. Support size variants: sm (48px), md (56px), lg (64px)
3. Support variants: primary, secondary, ghost
4. Implement theme-aware styling
5. Ensure 44px minimum touch target

Tests to write first:
- Button renders correctly in each theme
- All size variants meet touch target requirements
- Click handlers fire correctly
- Disabled state prevents interaction
- Theme switching updates button appearance

Implementation:
- Use CSS-in-JS or Tailwind with theme variables
- Include haptic feedback trigger
- Test with sweaty hands scenario
```

#### Prompt 3.2: Card Component (All Variations)

```
Create a Card component that adapts to all design variations.

Requirements:
1. Create /src/design-system/components/Card/Card.tsx
2. Support elevation/depth based on theme
3. Support interactive states
4. Implement proper content padding
5. Handle nested cards

Tests to write first:
- Card renders theme-appropriate styles
- Interactive cards respond to hover/tap
- Content padding is consistent
- Nested cards maintain proper hierarchy

Implementation:
- Subtle Depth: shadows and gradients
- Flat Bold: solid borders
- Glassmorphism: backdrop blur
- Ultra-Minimal: thin borders only
```

### Phase 4: Layout Components

#### Prompt 4.1: Page Layout Component

```
Create a PageLayout component for consistent page structure.

Requirements:
1. Create /src/design-system/components/Layout/PageLayout.tsx
2. Implement centered content with luxury margins
3. Support max-width constraints
4. Responsive margin scaling
5. Theme-aware backgrounds

Tests to write first:
- Content centers properly on all viewports
- Margins scale appropriately
- Max-width constraints apply correctly
- Background adapts to theme

Implementation:
- Mobile: 24-48px margins
- Desktop: 48-96px margins
- Max-width varies by theme
```

#### Prompt 4.2: Navigation Components

```
Create navigation components for Dr. Muscle X.

Requirements:
1. Create BottomNav.tsx for mobile navigation
2. Create Header.tsx for page headers
3. Implement theme-aware styling
4. Support active states
5. Include smooth transitions

Tests to write first:
- Navigation renders in fixed position
- Active states clearly indicated
- Touch targets meet requirements
- Theme switching updates appearance
- Navigation remains visible during scroll

Implementation:
- Each theme has distinct navigation style
- Maintain usability during workouts
```

### Phase 5: Workout-Specific Components

#### Prompt 5.1: Exercise Card Component

```
Create ExerciseCard component for workout interface.

Requirements:
1. Create /src/design-system/components/Workout/ExerciseCard.tsx
2. Display exercise name and details clearly
3. Support completed/active states
4. Large, accessible touch targets
5. Theme-appropriate styling

Tests to write first:
- Exercise information displays correctly
- State changes are visually clear
- Touch targets work with shaky hands
- Component performs well in lists

Implementation:
- No muscle diagrams (ultra-minimal approach)
- Focus on typography and spacing
```

#### Prompt 5.2: Set Input Component

```
Create SetInput component for logging workout sets.

Requirements:
1. Create /src/design-system/components/Workout/SetInput.tsx
2. Large input areas (56px+ height)
3. Clear visual feedback
4. Support for reps and weight input
5. Work well with sweaty hands

Tests to write first:
- Inputs are easily tappable
- Values update correctly
- Visual feedback is immediate
- Keyboard appears appropriately
- Works in landscape orientation

Implementation:
- Theme-aware input styling
- Clear focus states
- Generous padding
```

### Phase 6: Data Visualization

#### Prompt 6.1: Chart Components

```
Create minimalist chart components for progress tracking.

Requirements:
1. Create /src/design-system/components/Charts/LineChart.tsx
2. Create /src/design-system/components/Charts/ProgressBar.tsx
3. Implement smooth animations
4. Theme-aware styling
5. Mobile-optimized rendering

Tests to write first:
- Charts render with mock data
- Animations perform at 60fps
- Touch interactions work correctly
- Charts scale responsively
- Theme switching updates appearance

Implementation:
- Minimalist line graphs
- Subtle animations
- Clear data hierarchy
```

### Phase 7: Feedback Systems

#### Prompt 7.1: Toast Notification System

```
Create a toast notification system for user feedback.

Requirements:
1. Create /src/design-system/components/Toast/Toast.tsx
2. Support success, error, info variants
3. Auto-dismiss with timer
4. Queue multiple toasts
5. Edge positioning

Tests to write first:
- Toasts appear at correct position
- Auto-dismiss works correctly
- Multiple toasts stack properly
- Animations are smooth
- Don't block workout UI

Implementation:
- Subtle edge notifications
- Theme-appropriate styling
- Clear but non-intrusive
```

### Phase 8: Integration

#### Prompt 8.1: Complete Theme Provider

```
Finalize the theme provider with all variations.

Requirements:
1. Update theme provider to support all 4 variations
2. Implement theme persistence
3. Add theme switching UI
4. Ensure smooth transitions
5. Optimize performance

Tests to write first:
- Theme switches without flickers
- Theme persists across sessions
- All components update correctly
- No performance degradation
- Fallbacks work properly

Implementation:
- Use CSS variables for instant updates
- LocalStorage for persistence
- Smooth transition effects
```

#### Prompt 8.2: Example Pages

```
Create example pages showcasing the design system.

Requirements:
1. Create /src/pages/design-system/home.tsx
2. Create /src/pages/design-system/workout.tsx
3. Create /src/pages/design-system/progress.tsx
4. Implement theme switcher
5. Show all component variations

Tests to write first:
- All components render correctly
- Theme switching works on all pages
- Performance meets targets
- Mobile experience is smooth
- Accessibility standards met

Implementation:
- Real-world usage examples
- Performance monitoring
- Cross-browser testing
```

## Testing Strategy

Each prompt should follow TDD principles:

1. Write failing tests first
2. Implement minimum code to pass
3. Refactor for clarity and performance
4. Ensure all variations work correctly
5. Test on real devices

## Success Criteria

- All touch targets ≥ 44px
- Page load < 1s on mobile
- 60fps animations
- WCAG AA compliance
- Works offline (PWA)
- Theme switching < 100ms
