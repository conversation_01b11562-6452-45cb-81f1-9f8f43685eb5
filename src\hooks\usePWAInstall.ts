'use client'

import { useState, useEffect, useCallback } from 'react'
import { triggerHaptic } from '@/utils/haptic'

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

interface PWAInstallState {
  isInstallable: boolean
  isInstalling: boolean
  isInstalled: boolean
  canInstall: boolean
  installPrompt: () => Promise<boolean>
  checkInstallStatus: () => boolean
}

export function usePWAInstall(): PWAInstallState {
  const [isInstallable, setIsInstallable] = useState(false)
  const [isInstalling, setIsInstalling] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null)

  // Check if app is already installed
  const checkInstallStatus = useCallback(() => {
    if (typeof window === 'undefined') return false

    // Check if running in standalone mode (installed PWA)
    const isStandalone =
      window.matchMedia('(display-mode: standalone)').matches ||
      ('standalone' in window.navigator &&
        (window.navigator as Navigator & { standalone?: boolean })
          .standalone) ||
      document.referrer.includes('android-app://')

    setIsInstalled(isStandalone)
    return isStandalone
  }, [])

  // Handle beforeinstallprompt event
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent the default browser install prompt
      e.preventDefault()

      // Store the event for later use
      setDeferredPrompt(e as BeforeInstallPromptEvent)
      setIsInstallable(true)
    }

    // Handle app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsInstallable(false)
      setDeferredPrompt(null)
    }

    // Check initial install status
    checkInstallStatus()

    // Listen for install prompt
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener(
        'beforeinstallprompt',
        handleBeforeInstallPrompt
      )
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [checkInstallStatus])

  // Trigger install prompt
  const installPrompt = useCallback(async (): Promise<boolean> => {
    if (!deferredPrompt) {
      return false
    }

    try {
      setIsInstalling(true)

      // Trigger haptic feedback
      triggerHaptic('medium')

      // Show the install prompt
      await deferredPrompt.prompt()

      // Wait for user choice
      const { outcome } = await deferredPrompt.userChoice

      if (outcome === 'accepted') {
        setIsInstallable(false)
        setDeferredPrompt(null)

        // Success haptic feedback
        triggerHaptic('heavy')
        return true
      } else {
        // User dismissed the prompt
        triggerHaptic('light')
        return false
      }
    } catch (error) {
      console.error('Error during PWA installation:', error)

      // Error haptic feedback
      triggerHaptic('rigid')
      return false
    } finally {
      setIsInstalling(false)
    }
  }, [deferredPrompt])

  // Determine if we can show install option
  const canInstall = isInstallable && !isInstalled && !isInstalling

  return {
    isInstallable,
    isInstalling,
    isInstalled,
    canInstall,
    installPrompt,
    checkInstallStatus,
  }
}
