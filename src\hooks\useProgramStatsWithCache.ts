import { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { programApi } from '@/api/program'
import { useAuthStore } from '@/stores/authStore'
import { useProgramStore } from '@/stores/programStore'
import type { ProgramStats } from '@/types'

/**
 * Cache-enhanced hook to fetch program statistics
 * Shows cached data immediately while fetching fresh data in background
 */
export function useProgramStatsWithCache() {
  const { isAuthenticated } = useAuthStore()
  const {
    getCachedStats,
    setCachedStats,
    isStatsCacheStale,
    setIsLoadingStats,
    setIsRefreshingStats,
    setStatsError,
    hasHydrated,
  } = useProgramStore()

  const [optimisticStats, setOptimisticStats] = useState<ProgramStats | null>(
    null
  )
  const [hasInitialData, setHasInitialData] = useState(false)

  // Load cached data immediately after hydration
  useEffect(() => {
    if (hasHydrated) {
      const cached = getCachedStats()
      if (cached) {
        setOptimisticStats(cached)
        setHasInitialData(true)
      }
    }
  }, [hasHydrated, getCachedStats])

  const shouldFetch =
    isAuthenticated && (isStatsCacheStale() || !optimisticStats)
  const isBackgroundRefresh = hasInitialData && isStatsCacheStale()

  const {
    data: freshStats,
    isLoading: isQueryLoading,
    error,
    refetch,
  } = useQuery<ProgramStats>({
    queryKey: ['programStats'],
    queryFn: async () => {
      if (!hasInitialData) {
        setIsLoadingStats(true)
      } else {
        setIsRefreshingStats(true)
      }

      try {
        const data = await programApi.getProgramStats()
        return data
      } finally {
        setIsLoadingStats(false)
        setIsRefreshingStats(false)
      }
    },
    enabled: shouldFetch,
    staleTime: 24 * 60 * 60 * 1000, // 24 hours
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  })

  // Update cache and optimistic data when fresh data arrives
  useEffect(() => {
    if (freshStats !== undefined) {
      setCachedStats(freshStats)
      setOptimisticStats(freshStats)
      setHasInitialData(true)
    }
  }, [freshStats, setCachedStats])

  // Update error state
  useEffect(() => {
    setStatsError(error instanceof Error ? error : null)
  }, [error, setStatsError])

  return {
    stats: optimisticStats,
    isLoading: !hasInitialData && isQueryLoading,
    isRefreshing: isBackgroundRefresh && isQueryLoading,
    error,
    refetch,
    hasInitialData,
  }
}
