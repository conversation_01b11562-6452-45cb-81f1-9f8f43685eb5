import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  workoutService,
  type GetRecommendationForExerciseRequest,
} from '../workout'
import { apiClient } from '../client'
import type {
  GetUserWorkoutProgramTimeZoneInfoResponse,
  WorkoutTemplateModel,
  RecommendationModel,
} from '@/types'

// Mock the API client
vi.mock('../client', () => ({
  apiClient: {
    post: vi.fn(),
    get: vi.fn(),
  },
}))

// Mock logger
vi.mock('@/utils/logger', () => ({
  logger: {
    log: vi.fn(),
    error: vi.fn(),
  },
}))

describe('workoutService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.clearAllTimers()
    localStorage.clear()
  })

  describe('getUserWorkoutProgramInfo', () => {
    it('should fetch user workout program info with timezone', async () => {
      const mockResponse: GetUserWorkoutProgramTimeZoneInfoResponse = {
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: {
            Id: 100,
            Label: 'Beginner Program',
            RemainingToLevelUp: 10,
          },
          NextWorkoutTemplate: {
            Id: 12345,
            Label: 'Push Day',
            IsSystemExercise: true,
            Exercises: [],
          },
        },
        LastWorkoutDate: '2025-01-30T10:00:00Z',
        LastConsecutiveWorkoutDays: 5,
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: {
          StatusCode: 200,
          Result: mockResponse,
        },
      })

      const result = await workoutService.getUserWorkoutProgramInfo()

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
        expect.objectContaining({
          TimeZoneId: expect.any(String),
          Offset: expect.any(Number),
          IsDaylightSaving: false,
        })
      )

      expect(result).toEqual(mockResponse)
    })

    it('should handle direct response format', async () => {
      const mockResponse: GetUserWorkoutProgramTimeZoneInfoResponse = {
        LastWorkoutDate: '2025-01-30T10:00:00Z',
        ProgramName: 'Strength Program',
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: mockResponse,
      })

      const result = await workoutService.getUserWorkoutProgramInfo()
      expect(result).toEqual(mockResponse)
    })

    it('should handle API errors', async () => {
      const error = new Error('Network error')
      vi.mocked(apiClient.post).mockRejectedValueOnce(error)

      await expect(workoutService.getUserWorkoutProgramInfo()).rejects.toThrow(
        'Network error'
      )
    })
  })

  describe('getWorkoutDetails', () => {
    it('should fetch workout details by ID', async () => {
      const mockWorkout: WorkoutTemplateModel = {
        Id: 12345,
        UserId: 'user123',
        Label: 'Push Day',
        Exercises: [
          {
            Id: 1001,
            Label: 'Bench Press',
            IsSystemExercise: true,
            IsSwapTarget: false,
            IsFinished: false,
            IsUnilateral: false,
            IsTimeBased: false,
            IsEasy: false,
            IsMedium: true,
            IsBodyweight: false,
            VideoUrl: 'https://example.com/video',
            IsNextExercise: true,
            IsPlate: true,
            IsWeighted: true,
            IsPyramid: false,
            IsNormalSets: true,
            IsBodypartPriority: false,
            IsFlexibility: false,
            IsOneHanded: false,
            LocalVideo: '',
            IsAssisted: false,
          },
        ],
        IsSystemExercise: true,
        WorkoutSettingsModel: {},
      }

      vi.mocked(apiClient.get).mockResolvedValueOnce({
        data: {
          StatusCode: 200,
          Result: mockWorkout,
        },
      })

      const result = await workoutService.getWorkoutDetails(12345)

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/Workout/GetUserCustomizedCurrentWorkout/12345'
      )
      expect(result).toEqual(mockWorkout)
    })

    it('should handle direct response format', async () => {
      const mockWorkout: WorkoutTemplateModel = {
        Id: 12345,
        UserId: 'user123',
        Label: 'Pull Day',
        Exercises: [],
        IsSystemExercise: true,
        WorkoutSettingsModel: {},
      }

      vi.mocked(apiClient.get).mockResolvedValueOnce({
        data: mockWorkout,
      })

      const result = await workoutService.getWorkoutDetails(12345)
      expect(result).toEqual(mockWorkout)
    })

    it('should handle API errors', async () => {
      const error = new Error('Workout not found')
      vi.mocked(apiClient.get).mockRejectedValueOnce(error)

      await expect(workoutService.getWorkoutDetails(12345)).rejects.toThrow(
        'Workout not found'
      )
    })
  })

  describe('getExerciseRecommendation', () => {
    const mockRequest = {
      Username: '<EMAIL>',
      ExerciseId: 1001,
      WorkoutId: 12345,
      IsQuickMode: false,
      SetStyle: 'Normal',
      IsFlexibility: false,
    }

    const mockRecommendation: RecommendationModel = {
      Series: 3,
      Reps: 8,
      Weight: { Lb: 176, Kg: 80 },
      OneRMProgress: 0.75,
      RecommendationInKg: 80,
      OneRMPercentage: 75,
      WarmUpReps1: 10,
      WarmUpReps2: 5,
      WarmUpWeightSet1: { Lb: 88, Kg: 40 },
      WarmUpWeightSet2: { Lb: 132, Kg: 60 },
      WarmUpsList: [],
      WarmupsCount: 2,
      RpRest: 60,
      NbPauses: 0,
      NbRepsPauses: 0,
      IsEasy: false,
      IsMedium: true,
      IsBodyweight: false,
      Increments: { Lb: 5, Kg: 2.5 },
      Max: { Lb: 300, Kg: 136 },
      Min: { Lb: 45, Kg: 20 },
      IsNormalSets: true,
      IsDeload: false,
      IsBackOffSet: false,
      BackOffSetWeight: { Lb: 0, Kg: 0 },
      IsMaxChallenge: false,
      IsLightSession: false,
      FirstWorkSetReps: 8,
      FirstWorkSetWeight: { Lb: 176, Kg: 80 },
      FirstWorkSet1RM: { Lb: 220, Kg: 100 },
      IsPyramid: false,
      IsReversePyramid: false,
      HistorySet: [],
      ReferenceSetHistory: {} as any,
      MinReps: 6,
      MaxReps: 10,
      isPlateAvailable: true,
      isDumbbellAvailable: false,
      isPulleyAvailable: false,
      isBandsAvailable: false,
      Speed: 1,
      IsManual: false,
      ReferenseReps: 8,
      ReferenseWeight: { Lb: 176, Kg: 80 },
      IsDropSet: false,
    }

    it('should fetch exercise recommendation', async () => {
      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: {
          StatusCode: 200,
          Result: mockRecommendation,
        },
      })

      const result = await workoutService.getExerciseRecommendation(mockRequest)

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
        mockRequest
      )
      expect(result).toEqual(mockRecommendation)
    })

    it.skip('should get username from localStorage if not provided', async () => {
      // Don't provide username in request
      const requestWithoutUsername = {
        ExerciseId: 1001,
        WorkoutId: 12345,
        IsQuickMode: false,
      } as GetRecommendationForExerciseRequest

      localStorage.setItem(
        'drmuscle-auth',
        JSON.stringify({
          state: { user: { email: '<EMAIL>' } },
        })
      )

      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: mockRecommendation,
      })

      await workoutService.getExerciseRecommendation(requestWithoutUsername)

      // The service should add the username from localStorage
      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
        expect.objectContaining({
          ExerciseId: 1001,
          WorkoutId: 12345,
          Username: '<EMAIL>',
        })
      )
    })

    it('should handle Data wrapper response format', async () => {
      vi.mocked(apiClient.post).mockResolvedValueOnce({
        data: {
          Data: mockRecommendation,
        },
      })

      const result = await workoutService.getExerciseRecommendation(mockRequest)
      expect(result).toEqual(mockRecommendation)
    })

    it('should return null on error for graceful degradation', async () => {
      const error = new Error('API error')
      vi.mocked(apiClient.post).mockRejectedValueOnce(error)

      const result = await workoutService.getExerciseRecommendation(mockRequest)
      expect(result).toBeNull()
    })
  })

  describe('getCacheKey', () => {
    it('should generate correct cache key format', () => {
      const key = workoutService.getCacheKey('user123', 1001, 12345)
      expect(key).toBe('user123-1001-12345')
    })
  })
})
