import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useSuccessAnimation } from '../useSuccessAnimation'
import { ANIMATION_DURATION } from '@/types/animations'

describe('useSuccessAnimation', () => {
  beforeEach(() => {
    vi.useFakeTimers()
    // Reset matchMedia mock
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        addListener: vi.fn(), // Legacy
        removeListener: vi.fn(), // Legacy
        dispatchEvent: vi.fn(),
      })),
    })
  })

  afterEach(() => {
    vi.clearAllTimers()
    vi.useRealTimers()
  })

  it('should initialize with idle state', () => {
    const { result } = renderHook(() => useSuccessAnimation())

    expect(result.current.state).toBe('idle')
    expect(result.current.isAnimating).toBe(false)
    expect(result.current.prefersReducedMotion).toBe(false)
  })

  it('should transition through animation states', async () => {
    const { result } = renderHook(() => useSuccessAnimation())

    // Start animation
    act(() => {
      result.current.start()
    })

    // Should be entering
    expect(result.current.state).toBe('entering')
    expect(result.current.isAnimating).toBe(true)

    // Advance to active state
    act(() => {
      vi.advanceTimersByTime(0)
    })

    expect(result.current.state).toBe('active')
    expect(result.current.isAnimating).toBe(true)

    // Advance to complete state
    act(() => {
      vi.advanceTimersByTime(ANIMATION_DURATION.TOTAL)
    })

    expect(result.current.state).toBe('complete')
    expect(result.current.isAnimating).toBe(false)
  })

  it('should call onComplete callback when animation finishes', async () => {
    const onComplete = vi.fn()
    const { result } = renderHook(() => useSuccessAnimation({ onComplete }))

    act(() => {
      result.current.start()
    })

    expect(onComplete).not.toHaveBeenCalled()

    // Complete animation
    act(() => {
      vi.advanceTimersByTime(ANIMATION_DURATION.TOTAL)
    })

    expect(onComplete).toHaveBeenCalledTimes(1)
  })

  it('should respect custom timing options', () => {
    const customDuration = 500
    const customDelay = 100
    const onComplete = vi.fn()

    const { result } = renderHook(() =>
      useSuccessAnimation({
        timing: { duration: customDuration, delay: customDelay },
        onComplete,
      })
    )

    act(() => {
      result.current.start()
    })

    // Should still be entering after custom delay
    act(() => {
      vi.advanceTimersByTime(customDelay - 1)
    })
    expect(result.current.state).toBe('entering')

    // Should transition to active after delay
    act(() => {
      vi.advanceTimersByTime(1)
    })
    expect(result.current.state).toBe('active')

    // Should not be complete before custom duration
    act(() => {
      vi.advanceTimersByTime(customDuration - 1)
    })
    expect(onComplete).not.toHaveBeenCalled()

    // Should complete after custom duration
    act(() => {
      vi.advanceTimersByTime(1)
    })
    expect(onComplete).toHaveBeenCalledTimes(1)
  })

  it('should detect reduced motion preference', () => {
    // Mock reduced motion preference
    window.matchMedia = vi.fn().mockImplementation((query) => ({
      matches: query === '(prefers-reduced-motion: reduce)',
      media: query,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    }))

    const { result } = renderHook(() => useSuccessAnimation())
    expect(result.current.prefersReducedMotion).toBe(true)
  })

  it('should skip animations when reduced motion is preferred', () => {
    // Mock reduced motion preference
    window.matchMedia = vi.fn().mockImplementation(() => ({
      matches: true,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    }))

    const onComplete = vi.fn()
    const { result } = renderHook(() =>
      useSuccessAnimation({ respectReducedMotion: true, onComplete })
    )

    act(() => {
      result.current.start()
    })

    // Should complete instantly with reduced motion
    act(() => {
      vi.advanceTimersByTime(0)
    })

    expect(onComplete).toHaveBeenCalledTimes(1)
  })

  it('should allow animations when respectReducedMotion is false', () => {
    // Mock reduced motion preference
    window.matchMedia = vi.fn().mockImplementation(() => ({
      matches: true,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    }))

    const onComplete = vi.fn()
    const { result } = renderHook(() =>
      useSuccessAnimation({ respectReducedMotion: false, onComplete })
    )

    act(() => {
      result.current.start()
    })

    // Should not complete instantly even with reduced motion
    act(() => {
      vi.advanceTimersByTime(0)
    })
    expect(onComplete).not.toHaveBeenCalled()

    // Should complete after normal duration
    act(() => {
      vi.advanceTimersByTime(ANIMATION_DURATION.TOTAL)
    })
    expect(onComplete).toHaveBeenCalledTimes(1)
  })

  it('should reset animation state', () => {
    const { result } = renderHook(() => useSuccessAnimation())

    // Start animation
    act(() => {
      result.current.start()
    })
    expect(result.current.state).toBe('entering')

    // Reset
    act(() => {
      result.current.reset()
    })
    expect(result.current.state).toBe('idle')
    expect(result.current.isAnimating).toBe(false)
  })

  it('should clear timeouts when resetting', () => {
    const onComplete = vi.fn()
    const { result } = renderHook(() => useSuccessAnimation({ onComplete }))

    // Start animation
    act(() => {
      result.current.start()
    })

    // Reset before completion
    act(() => {
      result.current.reset()
    })

    // Advance time - onComplete should not be called
    act(() => {
      vi.advanceTimersByTime(ANIMATION_DURATION.TOTAL * 2)
    })
    expect(onComplete).not.toHaveBeenCalled()
  })

  it('should not start new animation if already animating', () => {
    const { result } = renderHook(() => useSuccessAnimation())

    // Start first animation
    act(() => {
      result.current.start()
    })
    expect(result.current.state).toBe('entering')

    // Try to start again - should not change state
    act(() => {
      result.current.start()
    })
    expect(result.current.state).toBe('entering')

    // Advance to active
    act(() => {
      vi.advanceTimersByTime(0)
    })
    expect(result.current.state).toBe('active')

    // Try to start again - should still not change
    act(() => {
      result.current.start()
    })
    expect(result.current.state).toBe('active')
  })

  it('should allow restart after completion', () => {
    const { result } = renderHook(() => useSuccessAnimation())

    // Complete first animation
    act(() => {
      result.current.start()
      vi.advanceTimersByTime(ANIMATION_DURATION.TOTAL)
    })
    expect(result.current.state).toBe('complete')

    // Should be able to start again
    act(() => {
      result.current.start()
    })
    expect(result.current.state).toBe('entering')
  })

  it('should cleanup on unmount', () => {
    const onComplete = vi.fn()
    const { result, unmount } = renderHook(() =>
      useSuccessAnimation({ onComplete })
    )

    // Start animation
    act(() => {
      result.current.start()
    })

    // Unmount before completion
    unmount()

    // Advance time - onComplete should not be called
    act(() => {
      vi.advanceTimersByTime(ANIMATION_DURATION.TOTAL * 2)
    })
    expect(onComplete).not.toHaveBeenCalled()
  })

  it('should handle legacy browsers without addEventListener', () => {
    const addListener = vi.fn()
    const removeListener = vi.fn()

    window.matchMedia = vi.fn().mockImplementation(() => ({
      matches: false,
      addListener,
      removeListener,
    }))

    const { unmount } = renderHook(() => useSuccessAnimation())

    // Should use legacy addListener
    expect(addListener).toHaveBeenCalledTimes(1)

    // Cleanup should use legacy removeListener
    unmount()
    expect(removeListener).toHaveBeenCalledTimes(1)
  })

  it('should update prefersReducedMotion when media query changes', () => {
    const listeners: ((e: MediaQueryListEvent) => void)[] = []

    window.matchMedia = vi.fn().mockImplementation(() => ({
      matches: false,
      addEventListener: (
        event: string,
        handler: (e: MediaQueryListEvent) => void
      ) => {
        if (event === 'change') {
          listeners.push(handler)
        }
      },
      removeEventListener: vi.fn(),
    }))

    const { result } = renderHook(() => useSuccessAnimation())
    expect(result.current.prefersReducedMotion).toBe(false)

    // Simulate media query change
    act(() => {
      listeners.forEach((listener) => {
        listener({ matches: true } as MediaQueryListEvent)
      })
    })

    expect(result.current.prefersReducedMotion).toBe(true)
  })
})
