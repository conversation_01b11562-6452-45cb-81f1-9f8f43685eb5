/**
 * Cache getter actions for the workout store
 */

import type { WorkoutState } from './types'
import { trackCacheOperation } from './helpers'
import { CACHE_EXPIRY } from './constants'

export const createGetCacheActions = (
  set: (
    partial:
      | Partial<WorkoutState>
      | ((state: WorkoutState) => Partial<WorkoutState>)
  ) => void,
  get: () => WorkoutState
) => ({
  getCachedUserProgramInfo: () => {
    const startTime = performance.now()
    const { hasHydrated, cachedData } = get()

    if (!hasHydrated) return null

    const result = cachedData.userProgramInfo
    const latency = performance.now() - startTime

    // Track cache operation
    trackCacheOperation(set, get, !!result, latency)

    return result
  },

  getCachedUserWorkouts: () => {
    const startTime = performance.now()
    const { hasHydrated, cachedData } = get()

    if (!hasHydrated) return null

    const result = cachedData.userWorkouts
    const latency = performance.now() - startTime

    // Track cache operation
    trackCacheOperation(set, get, !!result, latency)

    return result
  },

  getCachedTodaysWorkout: () => {
    const startTime = performance.now()
    const { hasHydrated, cachedData } = get()

    if (!hasHydrated) return null

    const result = cachedData.todaysWorkout
    const latency = performance.now() - startTime

    // Track cache operation
    trackCacheOperation(set, get, !!result, latency)

    return result
  },

  getCachedExerciseRecommendation: (exerciseId: number) => {
    const startTime = performance.now()
    const { hasHydrated, cachedData } = get()

    if (!hasHydrated) return undefined

    // Return undefined if not cached, or the actual value (which could be null)
    const hasKey = Object.prototype.hasOwnProperty.call(
      cachedData.exerciseRecommendations,
      exerciseId
    )
    const result = hasKey
      ? cachedData.exerciseRecommendations[exerciseId]
      : undefined
    const latency = performance.now() - startTime

    // Track cache operation - count as hit if key exists (even if value is null)
    trackCacheOperation(set, get, hasKey, latency)

    return result
  },

  getCacheSize: () => {
    const { cachedData } = get()
    try {
      return JSON.stringify(cachedData).length
    } catch {
      return 0
    }
  },

  isCacheStale: (
    type:
      | 'userProgramInfo'
      | 'userWorkouts'
      | 'todaysWorkout'
      | 'exerciseRecommendation',
    exerciseId?: number
  ): boolean => {
    const { cachedData } = get()
    const now = Date.now()

    if (type === 'exerciseRecommendation' && exerciseId !== undefined) {
      const lastUpdated =
        cachedData.lastUpdated.exerciseRecommendations[exerciseId] || 0
      return (
        lastUpdated === 0 ||
        now - lastUpdated > CACHE_EXPIRY.exerciseRecommendation
      )
    }

    if (type === 'todaysWorkout') {
      const lastUpdated = cachedData.lastUpdated.todaysWorkout || 0
      return lastUpdated === 0 || now - lastUpdated > CACHE_EXPIRY.todaysWorkout
    }

    const lastUpdated =
      cachedData.lastUpdated[type as 'userProgramInfo' | 'userWorkouts'] || 0
    return (
      lastUpdated === 0 ||
      now - lastUpdated >
        CACHE_EXPIRY[type as 'userProgramInfo' | 'userWorkouts']
    )
  },
})
