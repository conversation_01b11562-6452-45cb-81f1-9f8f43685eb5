# Displaying Home Page Stats Card (Web App)

This guide explains how to fetch and display the user stats card on the home (program) page, as done in the DrMuscle MAUI app. It is intended for an LLM or developer working on the web app who wants to replicate the same stats and logic.

---

## Stats Displayed

- **Weeks streak** (e.g., 119)
- **Workouts** (e.g., 1599)
- **Lbs lifted** (e.g., 179B)
- **Body weight** (e.g., 178.57)
- **Recovery** (e.g., 1 day)
- **Coach says** (e.g., Train/Rest)

---

## Data Source

- All stats are fetched from the backend via a single API call:
  - **Endpoint:** `/api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2`
  - **Response Model:** `GetUserWorkoutLogAverageResponse`

### Key Fields in the Response

| Stat         | API Field / Calculation                                   |
| ------------ | --------------------------------------------------------- |
| Weeks streak | `ConsecutiveWeeksModel.ConsecutiveWeeks`                  |
| Workouts     | `WorkoutCount`                                            |
| Lbs lifted   | Aggregate from `Averages` or similar                      |
| Body weight  | Latest from user profile/log                              |
| Recovery     | `Now - LastWorkoutDate`                                   |
| Coach says   | Logic: if recovery time > threshold, "Train", else "Rest" |

---

## Implementation Steps

1. **Call the API**
   - Use the endpoint `/api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2`.
   - Authenticate as the user.

2. **Parse the Response**
   - Extract the fields as shown above.

3. **Display**
   - Render a card with the same layout as the MAUI app.
   - Map each stat to its corresponding UI element.

4. **Business Logic**
   - For "Coach says", use the same logic as the app:
     - If recovery time (since `LastWorkoutDate`) is greater than a threshold (e.g., 18 or 42 hours, depending on program), show **"Train"**.
     - Otherwise, show **"Rest"**.

---

## Example (Pseudocode)

```js
// Fetch stats
const response = await fetch('/api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2', { headers: { Authorization: 'Bearer ...' } });
const data = await response.json();

// Map fields
const weeksStreak = data.ConsecutiveWeeks[0]?.ConsecutiveWeeks;
const workouts = data.WorkoutCount;
const lbsLifted = /* sum or aggregate from data.Averages */;
const bodyWeight = /* latest from user profile or log */;
const lastWorkoutDate = new Date(data.LastWorkoutDate);
const now = new Date();
const recoveryHours = (now - lastWorkoutDate) / (1000 * 60 * 60);
const coachSays = recoveryHours > threshold ? 'Train' : 'Rest';
```

---

## Notes

- The MAUI app uses a view model (`BotModel`) to bind these fields to the UI. You can use a similar model or state management in your web app.
- The threshold for "Coach says" may depend on the user's program (e.g., 18 or 42 hours). See the MAUI app logic for details.
- All stats are available from the single API call; no need for multiple requests.

---

**For further details, see the MAUI app's `MainAIPage.xaml` and `MainAIPage.xaml.cs`.**
