import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useAuth } from '@/hooks/useAuth'
import { authApi } from '@/api/auth'
import { programApi } from '@/api/program'
import { useAuthStore } from '@/stores/authStore'
import { useProgramStore } from '@/stores/programStore'
import * as userInfoPerformance from '@/utils/userInfoPerformance'
import type {
  LoginModel,
  LoginSuccessResult,
  LoginSuccessResultAlt,
} from '@/types'

// Mock dependencies
vi.mock('@/api/auth', () => ({
  authApi: {
    login: vi.fn(),
    register: vi.fn(),
    refreshToken: vi.fn(),
    // logout method removed - handled client-side only
  },
}))
vi.mock('@/api/program')
vi.mock('@/stores/authStore')
vi.mock('@/stores/programStore')
vi.mock('@/utils/userInfoPerformance')

// Create wrapper component for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return function ({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

describe('useAuth Hook', () => {
  const mockSetAuth = vi.fn()
  const mockLogout = vi.fn()
  const mockSetError = vi.fn()
  const mockClearError = vi.fn()
  const mockSetLoading = vi.fn()
  const mockUpdateUser = vi.fn()
  const mockSetCachedUserInfo = vi.fn()
  const mockSetCachedProgram = vi.fn()
  const mockSetCachedProgress = vi.fn()
  const mockSetCachedStats = vi.fn()
  const mockStartSession = vi.fn()
  const mockEndSession = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock auth store
    ;(useAuthStore as any).mockReturnValue({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      setAuth: mockSetAuth,
      logout: mockLogout,
      setError: mockSetError,
      clearError: mockClearError,
      setLoading: mockSetLoading,
      updateUser: mockUpdateUser,
      setCachedUserInfo: mockSetCachedUserInfo,
    })

    // Mock auth store getState
    ;(useAuthStore.getState as any) = vi.fn().mockReturnValue({
      updateUser: mockUpdateUser,
      setCachedUserInfo: mockSetCachedUserInfo,
    })

    // Mock program store getState
    ;(useProgramStore.getState as any) = vi.fn().mockReturnValue({
      setCachedProgram: mockSetCachedProgram,
      setCachedProgress: mockSetCachedProgress,
      setCachedStats: mockSetCachedStats,
    })

    // Mock userInfoPerformance
    mockStartSession.mockReturnValue('test-session-id')
    ;(userInfoPerformance.startSession as any) = mockStartSession
    ;(userInfoPerformance.endSession as any) = mockEndSession

    // Mock program API methods to prevent errors
    ;(programApi.getUserProgram as any) = vi.fn().mockResolvedValue(null)
    ;(programApi.getProgramProgress as any) = vi.fn().mockResolvedValue(null)
    ;(programApi.getProgramStats as any) = vi.fn().mockResolvedValue(null)

    // Mock auth API getUserInfo to prevent errors in tests that don't explicitly set it
    ;(authApi.getUserInfo as any) = vi.fn().mockResolvedValue(null)
  })

  describe('Initial State', () => {
    it('should return initial auth state', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      expect(result.current.user).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
    })
  })

  describe('Login', () => {
    it('should handle successful login', async () => {
      // Given: Mock successful login response
      const credentials: LoginModel = {
        Username: '<EMAIL>',
        Password: 'password123',
      }

      const mockResponse: LoginSuccessResultAlt = {
        Result: true,
        UserData: {
          Email: credentials.Username,
          Name: 'Test User',
        },
        UserToken: 'auth-token',
        RefreshToken: 'refresh-token',
      }

      ;(authApi.login as any).mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // When: Login is called
      result.current.login(credentials)

      // Then: Auth is set correctly
      await waitFor(() => {
        expect(authApi.login).toHaveBeenCalledWith(credentials)
        expect(mockSetAuth).toHaveBeenCalledWith(mockResponse)
        expect(mockClearError).toHaveBeenCalled()
      })
    })

    it('should prefetch user info during login success', async () => {
      // Given: Mock successful login and user info responses
      const credentials: LoginModel = {
        Username: '<EMAIL>',
        Password: 'password123',
      }

      const mockLoginResponse: LoginSuccessResult = {
        userName: credentials.Username,
        access_token: 'auth-token',
        token_type: 'Bearer',
        expires_in: 3600,
        issued: new Date().toISOString(),
        expires: new Date(Date.now() + 3600000).toISOString(),
      }

      const mockUserInfo = {
        Result: {
          FirstName: 'John',
          LastName: 'Doe',
          Email: '<EMAIL>',
        },
      }

      ;(authApi.login as any).mockResolvedValueOnce(mockLoginResponse)
      ;(authApi.getUserInfo as any).mockResolvedValueOnce(mockUserInfo)

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // When: Login is called
      await result.current.login(credentials)

      // Then: UserInfo is fetched and cached
      await waitFor(() => {
        expect(mockStartSession).toHaveBeenCalled()
        expect(authApi.getUserInfo).toHaveBeenCalled()
        expect(mockUpdateUser).toHaveBeenCalledWith({
          firstName: 'John',
          lastName: 'Doe',
        })
        expect(mockSetCachedUserInfo).toHaveBeenCalledWith({
          firstName: 'John',
          lastName: 'Doe',
          FirstName: 'John',
          LastName: 'Doe',
          Email: '<EMAIL>',
        })
        expect(mockEndSession).toHaveBeenCalledWith('test-session-id', true)
      })
    })

    it('should handle user info prefetch failure gracefully', async () => {
      // Given: Mock successful login but user info fails
      const credentials: LoginModel = {
        Username: '<EMAIL>',
        Password: 'password123',
      }

      const mockLoginResponse: LoginSuccessResult = {
        userName: credentials.Username,
        access_token: 'auth-token',
        token_type: 'Bearer',
        expires_in: 3600,
        issued: new Date().toISOString(),
        expires: new Date(Date.now() + 3600000).toISOString(),
      }

      ;(authApi.login as any).mockResolvedValueOnce(mockLoginResponse)
      ;(authApi.getUserInfo as any).mockRejectedValueOnce(
        new Error('Network error')
      )

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // When: Login is called
      await result.current.login(credentials)

      // Then: Login succeeds even if user info fails
      await waitFor(() => {
        expect(mockSetAuth).toHaveBeenCalledWith(mockLoginResponse)
        expect(mockStartSession).toHaveBeenCalled()
        expect(authApi.getUserInfo).toHaveBeenCalled()
        expect(mockEndSession).toHaveBeenCalledWith('test-session-id', false)
        // User info should not be updated
        expect(mockUpdateUser).not.toHaveBeenCalled()
        expect(mockSetCachedUserInfo).not.toHaveBeenCalled()
      })
    })

    it('should handle login errors', async () => {
      // Given: Mock login error
      const credentials: LoginModel = {
        Username: '<EMAIL>',
        Password: 'wrong',
      }

      const errorMessage = 'Invalid credentials'
      ;(authApi.login as any).mockRejectedValueOnce(new Error(errorMessage))

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // When: Login fails
      await expect(result.current.login(credentials)).rejects.toThrow(
        errorMessage
      )

      // Then: Error is set
      await waitFor(() => {
        expect(authApi.login).toHaveBeenCalledWith(credentials)
        expect(mockSetError).toHaveBeenCalledWith(errorMessage)
        expect(mockSetAuth).not.toHaveBeenCalled()
      })
    })

    it('should show loading state during login', async () => {
      // Given: Mock auth store with loading updates
      const loadingStates: boolean[] = []
      ;(useAuthStore as any).mockImplementation(() => ({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: loadingStates[loadingStates.length - 1] || false,
        error: null,
        setAuth: mockSetAuth,
        logout: mockLogout,
        setError: mockSetError,
        clearError: mockClearError,
        setLoading: (loading: boolean) => {
          loadingStates.push(loading)
          mockSetLoading(loading)
        },
      }))

      const credentials: LoginModel = {
        Username: '<EMAIL>',
        Password: 'password123',
      }

      ;(authApi.login as any).mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(() => resolve({ Result: true }), 100)
          )
      )

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // When: Login is called
      expect(result.current.loginMutation.isPending).toBe(false)

      result.current.login(credentials)

      // Then: Loading state changes
      await waitFor(() => {
        expect(result.current.loginMutation.isPending).toBe(true)
      })
    })
  })

  describe('Logout', () => {
    it('should handle logout', async () => {
      // Given: User is authenticated
      ;(useAuthStore as any).mockReturnValue({
        user: { email: '<EMAIL>' },
        token: 'auth-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
        setAuth: mockSetAuth,
        logout: mockLogout,
        setError: mockSetError,
        clearError: mockClearError,
        setLoading: mockSetLoading,
      })
      // No API call needed for logout

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // When: Logout is called
      result.current.logout()

      // Then: Store logout is called (no API call)
      await waitFor(() => {
        // No API logout call - handled client-side only
        expect(mockLogout).toHaveBeenCalled()
      })
    })

    it('should handle logout without API call', async () => {
      // Given: Normal logout flow (no API needed)
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // When: Logout is called
      result.current.logout()

      // Then: Store logout is called directly
      await waitFor(() => {
        // No API logout call - handled client-side only
        expect(mockLogout).toHaveBeenCalled()
      })
    })
  })

  describe('Integration', () => {
    it('should provide complete auth interface', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // Verify all expected properties and methods
      expect(result.current).toHaveProperty('user')
      expect(result.current).toHaveProperty('isAuthenticated')
      expect(result.current).toHaveProperty('isLoading')
      expect(result.current).toHaveProperty('error')
      expect(result.current).toHaveProperty('login')
      expect(result.current).toHaveProperty('logout')
      expect(result.current).toHaveProperty('clearError')
      expect(result.current).toHaveProperty('loginMutation')
      expect(result.current).toHaveProperty('logoutMutation')
    })
  })
})
