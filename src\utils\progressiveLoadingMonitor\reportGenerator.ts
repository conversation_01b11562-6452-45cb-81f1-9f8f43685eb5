/**
 * Report generation utilities for progressive loading monitor
 */

import { getUserInfoMetrics } from '../userInfoPerformance'

export function generatePerformanceReport(): string {
  const metrics = getUserInfoMetrics()
  const report: string[] = []

  report.push('=== Progressive Loading Performance Report ===')
  report.push('')

  // Loading times
  report.push('Loading Times:')
  report.push(
    `  Login to First Byte: ${metrics.loginToFirstByte?.toFixed(0) || 'N/A'}ms`
  )
  report.push(
    `  UserInfo Load: ${metrics.userInfoLoadTime?.toFixed(0) || 'N/A'}ms`
  )
  report.push(`  Stats Load: ${metrics.statsLoadTime?.toFixed(0) || 'N/A'}ms`)
  report.push(
    `  Overall Page Ready: ${metrics.overallPageReady?.toFixed(0) || 'N/A'}ms`
  )
  report.push('')

  // Metric load times
  report.push('Individual Metrics:')
  report.push(
    `  Streak: ${metrics.metricLoadTimes.streak?.toFixed(0) || 'N/A'}ms`
  )
  report.push(
    `  Workouts: ${metrics.metricLoadTimes.workouts?.toFixed(0) || 'N/A'}ms`
  )
  report.push(
    `  Volume: ${metrics.metricLoadTimes.volume?.toFixed(0) || 'N/A'}ms`
  )
  report.push('')

  // Cache performance
  report.push('Cache Performance:')
  report.push(`  Hit Rate: ${metrics.cacheMetrics.hitRate.toFixed(1)}%`)
  report.push(`  Hits: ${metrics.cacheMetrics.hits}`)
  report.push(`  Misses: ${metrics.cacheMetrics.misses}`)
  report.push(
    `  Read Latency: ${metrics.cacheMetrics.readLatency?.toFixed(1) || 'N/A'}ms`
  )
  report.push(
    `  Write Latency: ${
      metrics.cacheMetrics.writeLatency?.toFixed(1) || 'N/A'
    }ms`
  )
  report.push('')

  // Retry metrics
  if (metrics.retryMetrics.totalRetries > 0) {
    report.push('API Retries:')
    report.push(`  UserInfo: ${metrics.retryMetrics.userInfoRetries}`)
    report.push(`  Stats: ${metrics.retryMetrics.statsRetries}`)
    report.push(`  Total: ${metrics.retryMetrics.totalRetries}`)
    if (metrics.retryMetrics.lastRetryReason) {
      report.push(`  Last Reason: ${metrics.retryMetrics.lastRetryReason}`)
    }
    report.push('')
  }

  report.push(`Session ID: ${metrics.analytics.sessionId}`)
  report.push(
    `Timestamp: ${new Date(metrics.analytics.timestamp).toISOString()}`
  )

  return report.join('\n')
}
