'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { TimerScreen } from './TimerScreen'
import { useWorkoutStore } from '@/stores/workoutStore'

export function TimerScreenWrapper() {
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [vibrationEnabled, setVibrationEnabled] = useState(true)
  const searchParams = useSearchParams()
  const isBetweenSets = searchParams.get('between-sets') === 'true'
  const shouldProgress = searchParams.get('shouldProgress') === 'true'
  const nextSet = useWorkoutStore((state) => state.nextSet)

  // Progress to next set if coming from RIR selection
  useEffect(() => {
    if (shouldProgress) {
      nextSet()
    }
  }, [shouldProgress, nextSet])

  // Load preferences from localStorage
  useEffect(() => {
    const savedSoundPref = localStorage.getItem('soundEnabled')
    if (savedSoundPref !== null) {
      setSoundEnabled(savedSoundPref === 'true')
    }

    const savedVibrationPref = localStorage.getItem('vibrationEnabled')
    if (savedVibrationPref !== null) {
      setVibrationEnabled(savedVibrationPref === 'true')
    }
  }, [])

  // Listen for storage changes to sync state across components
  useEffect(() => {
    const handleStorageChange = () => {
      const savedSoundPref = localStorage.getItem('soundEnabled')
      if (savedSoundPref !== null) {
        setSoundEnabled(savedSoundPref === 'true')
      }

      const savedVibrationPref = localStorage.getItem('vibrationEnabled')
      if (savedVibrationPref !== null) {
        setVibrationEnabled(savedVibrationPref === 'true')
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  return (
    <TimerScreen
      soundEnabled={soundEnabled}
      vibrationEnabled={vibrationEnabled}
      isBetweenSets={isBetweenSets}
    />
  )
}
