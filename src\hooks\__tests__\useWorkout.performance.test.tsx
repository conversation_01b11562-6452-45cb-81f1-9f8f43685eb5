import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useWorkout } from '../useWorkout'
import * as workoutApiModule from '@/api/workouts'
import type {
  MockProgramInfoResponse,
  MockUserWorkoutResponse,
} from './useWorkout.types'

// Mock the API
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserProgramInfo: vi.fn(),
    getUserWorkout: vi.fn(),
    getExerciseRecommendation: vi.fn(),
    saveWorkoutSet: vi.fn(),
    completeWorkout: vi.fn(),
  },
}))

// Mock auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: vi.fn(() => ({
    isAuthenticated: true,
  })),
}))

// Mock workout store
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(() => ({
    currentWorkout: null,
    currentSetIndex: 0,
    currentExerciseIndex: 0,
    exercises: [],
    workoutSession: null,
    isLoading: false,
    error: null,
    setWorkout: vi.fn(),
    startWorkout: vi.fn(),
    nextSet: vi.fn(),
    nextExercise: vi.fn(),
    saveSet: vi.fn(),
    completeWorkout: vi.fn(),
    setError: vi.fn(),
    getCurrentExercise: vi.fn(),
    getNextExercise: vi.fn(),
    getRestDuration: vi.fn(),
  })),
}))

describe('useWorkout - Performance Measurement', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })
    vi.clearAllMocks()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  it('should complete both API calls faster in parallel than sequential', async () => {
    // Simulate API calls that take 100ms each
    const DELAY_MS = 100

    const programInfoPromise = new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          GetUserProgramInfoResponseModel: {
            RecommendedProgram: { Id: 1, Label: 'Test Program' },
            NextWorkoutTemplate: {
              Id: 1,
              Label: 'Test Workout',
              Exercises: [
                { Id: 1, Label: 'Bench Press' },
                { Id: 2, Label: 'Squat' },
              ],
            },
            WorkoutTemplates: [],
          },
        })
      }, DELAY_MS)
    })

    const userWorkoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            Id: 1,
            Label: 'Test Workout',
            Exercises: [
              { Id: 1, Label: 'Bench Press' },
              { Id: 2, Label: 'Squat' },
            ],
          },
        ])
      }, DELAY_MS)
    })

    vi.spyOn(workoutApiModule.workoutApi, 'getUserProgramInfo').mockReturnValue(
      programInfoPromise as Promise<MockProgramInfoResponse>
    )
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockReturnValue(
      userWorkoutPromise as Promise<MockUserWorkoutResponse>
    )

    const startTime = performance.now()
    const { result } = renderHook(() => useWorkout(), { wrapper })

    // Initially loading
    expect(result.current.isLoadingWorkout).toBe(true)

    // Wait for both to complete
    await waitFor(
      () => {
        expect(result.current.isLoadingWorkout).toBe(false)
      },
      { timeout: 200 }
    ) // Should complete in ~100ms, not 200ms

    const endTime = performance.now()
    const duration = endTime - startTime

    // With parallel execution, should complete in ~100ms (plus some overhead)
    // With sequential execution, would take ~200ms
    // eslint-disable-next-line no-console
    console.log(`Parallel API calls completed in ${Math.round(duration)}ms`)

    // Allow up to 150ms for overhead, but should be much less than 200ms
    expect(duration).toBeLessThan(150)

    // Verify both APIs were called
    expect(
      workoutApiModule.workoutApi.getUserProgramInfo
    ).toHaveBeenCalledTimes(1)
    expect(workoutApiModule.workoutApi.getUserWorkout).toHaveBeenCalledTimes(1)

    // Verify data is correctly merged
    expect(result.current.todaysWorkout).toBeTruthy()
    expect(
      result.current.todaysWorkout![0].WorkoutTemplates[0].Exercises
    ).toHaveLength(2)
  })

  it('should show progress as each API completes', async () => {
    // Different delays to see progress
    const programInfoPromise = new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          GetUserProgramInfoResponseModel: {
            RecommendedProgram: { Id: 1, Label: 'Test Program' },
            NextWorkoutTemplate: { Id: 1, Label: 'Test Workout' },
          },
        })
      }, 50) // Completes first
    })

    const userWorkoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        resolve([{ Id: 1, Label: 'Test Workout', Exercises: [] }])
      }, 150) // Completes later
    })

    vi.spyOn(workoutApiModule.workoutApi, 'getUserProgramInfo').mockReturnValue(
      programInfoPromise as Promise<MockProgramInfoResponse>
    )
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockReturnValue(
      userWorkoutPromise as Promise<MockUserWorkoutResponse>
    )

    const { result } = renderHook(() => useWorkout(), { wrapper })

    // Track loading states over time
    const loadingStates: Array<{
      time: number
      programInfo: boolean
      userWorkout: boolean
    }> = []

    const startTime = performance.now()

    // Initially both loading
    loadingStates.push({
      time: 0,
      programInfo: result.current.loadingStates.programInfo,
      userWorkout: result.current.loadingStates.userWorkout,
    })

    // Check after 75ms (program info should be done)
    await new Promise((resolve) => {
      setTimeout(resolve, 75)
    })
    loadingStates.push({
      time: 75,
      programInfo: result.current.loadingStates.programInfo,
      userWorkout: result.current.loadingStates.userWorkout,
    })

    // Wait for all to complete
    await waitFor(() => {
      expect(result.current.isLoadingWorkout).toBe(false)
    })

    loadingStates.push({
      time: Math.round(performance.now() - startTime),
      programInfo: result.current.loadingStates.programInfo,
      userWorkout: result.current.loadingStates.userWorkout,
    })

    // Verify progressive loading
    expect(loadingStates[0]).toEqual({
      time: 0,
      programInfo: true,
      userWorkout: true,
    })

    // After 75ms, program info should be done but user workout still loading
    expect(loadingStates[1].programInfo).toBe(false)
    expect(loadingStates[1].userWorkout).toBe(true)

    // Finally, both should be done
    expect(loadingStates[2].programInfo).toBe(false)
    expect(loadingStates[2].userWorkout).toBe(false)
  })
})
