MAUI Mobile App - Save Set API Flow
Overview
This document describes the complete flow of saving workout sets in the Dr. Muscle MAUI mobile app, from the UI interaction to the API calls and local data handling.

Key Components

1. SaveSetPage.xaml.cs
   The main UI page for logging workout sets during an exercise.

Location: /DrMuscle/DrMuscle/Screens/Exercises/SaveSetPage.xaml.cs

2. DrMuscleRestClient.cs
   The REST API client that handles all API communications.

Location: /DrMuscle/DrMuscle/DrMuscleRestClient.cs

3. Data Models
   WorkoutLogSerieModel - Represents a single workout set
   SaveWorkoutModel - Used to save/complete a workout
   API Endpoints
1. Save Individual Set
   Endpoint: /api/Exercise/AddWorkoutLogSerieNew Method: POST Client Method: DrMuscleRestClient.Instance.AddWorkoutLogSerie(WorkoutLogSerieModel model)

1. Complete Workout
   Endpoint: /api/Workout/SaveWorkoutV3Pro Method: POST Client Method: DrMuscleRestClient.Instance.SaveWorkoutV3(SaveWorkoutModel model)

Detailed Flow
Phase 1: Set Collection (Local)
When a user saves a set in SaveSetPage:

User Input: Weight and reps are entered via UI controls
RIR Collection: After first work set, user is prompted for Rate of Perceived Exertion (RIR)
Local Storage: Set is added to workoutLogSerieModel collection
WorkoutLogSerieModelEx serieModel = new WorkoutLogSerieModelEx()
{
Exercice = new ExerciceModel() { Id = CurrentLog.Instance.ExerciseLog.Exercice.Id },
Reps = currentReps,
UserId = CurrentLog.Instance.ExerciseLog.UserId,
Weight = new MultiUnityWeight(currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value),
RIR = RIR,
IsWarmups = workoutLogSerieModel.Count < reco.WarmUpsList.Count ? true : false
};
workoutLogSerieModel.Add(serieModel);
Timer Management: Rest timer automatically starts after each set
Phase 2: Exercise Completion
When user taps "Finish Exercise":

Confirmation Dialog: User confirms they want to finish and save
PushToDataServer() method is called
API Calls: Each set in the collection is sent to the server
foreach (WorkoutLogSerieModel l in workoutLogSerieModel)
{
BooleanModel b = await DrMuscleRestClient.Instance.AddWorkoutLogSerie(l);
result = result && b.Result;
}
Phase 3: Workout Completion
When all exercises are done:

SaveWorkoutV3 is called with workout ID
Minimal Data: Only WorkoutId and WorkoutTemplateId are sent
successWorkoutLog = await DrMuscleRestClient.Instance.SaveWorkoutV3(
new SaveWorkoutModel()
{
WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id,
WorkoutTemplateId = CurrentLog.Instance.CurrentWorkoutTemplateGroup.Id
});
Data Model Details
WorkoutLogSerieModel
public class WorkoutLogSerieModel
{
public long Id { get; set; }
public ExerciceModel Exercice { get; set; }
public long? BodypartId { get; set; }
public string UserId { get; set; }
public DateTime LogDate { get; set; }
public int Reps { get; set; }
public MultiUnityWeight Weight { get; set; }
public MultiUnityWeight OneRM { get; set; }
public bool IsWarmups { get; set; }
public bool Isbodyweight { get; set; }
public int? RIR { get; set; } // Rate in Reserve (0-4)
// ... other properties
}
SaveWorkoutModel
public class SaveWorkoutModel
{
public long WorkoutId { get; set; }
public long WorkoutTemplateId { get; set; }
}
Local Data Management
Before API Calls
Sets are stored in CurrentLog.Instance.WorkoutLogSeriesByExercise dictionary
Daily set counts tracked: LocalDBManager.Instance.SetDBSetting($"Sets{DateTime.Now.Date}", count)
Exercise completion tracked: LocalDBManager.Instance.SetDBSetting($"workout{exerciseId}", "true")
After Successful API Calls
Exercise removed from working collection: CurrentLog.Instance.WorkoutLogSeriesByExercise.Remove(exerciseId)
Finished exercises tracked in app instance
Navigation to EndExercisePage
Error Handling
Network Failures: Shows "Please check internet connection" alert
API Failures: Individual set failures don't block exercise completion
Crash Tracking: Errors logged with AppCenter Crashes
Key Features
Offline Support: Sets collected locally before server sync
Warm-up Detection: Automatic based on recommendation model
RIR Collection: Only after first work set
Rest Timer Integration: Auto-starts based on set type
Unilateral Exercise Support: Separate tracking for each side
Light Session Support: Tracks deload days
Important Notes
Sets are saved individually to the API, not in batches
Workout completion is a separate API call that marks the workout as done
The app maintains local state throughout the workout for resilience
Timer and rest recommendations are calculated client-side based on set type

MAUI Save Set - Sequence Diagram
ASCII Sequence Diagram
User SaveSetPage CurrentLog DrMuscleRestClient API Server
| | | | |
|--Enter Weight/Reps->| | | |
| | | | |
|----Save Set-------->| | | |
| | | | |
| |--Create SerieModel>| | |
| | | | |
|<--RIR Prompt--------| | | |
| (if first work set) | | | |
| | | | |
|----Select RIR------>| | | |
| | | | |
| |--Add to Collection>| | |
| | (Local) | | |
| | | | |
| |<--Start Timer-----| | |
| | | | |
| [Repeat for each set] | | |
| | | | |
|--Finish Exercise--->| | | |
| | | | |
|<--Confirm Dialog----| | | |
| | | | |
|------Confirm------->| | | |
| | | | |
| |--PushToDataServer>| | |
| | | | |
| | |--Loop each set----->| |
| | | | |
| | | |--AddWorkoutLogSerieNew->|
| | | | (POST) |
| | | | |
| | | |<----BooleanModel------|
| | | | |
| | |<--Result-----------| |
| | | | |
| | | | |
| |--Navigate to------>| | |
| | EndExercisePage | | |
| | | | |
| [After all exercises complete] | | |
| | | | |
| | |--SaveWorkout------>| |
| | | |--SaveWorkoutV3Pro---->|
| | | | (POST) |
| | | | |
| | | |<----BooleanModel------|
| | |<--Result-----------| |
Key Points
Local First: All sets are collected locally before any API calls
Batch Upload: Sets are uploaded when exercise is finished, not as they're entered
Two-Phase Save: Individual sets first, then workout completion
Error Resilience: Local storage ensures data isn't lost on network failure
State Management
CurrentLog.Instance
WorkoutLogSeriesByExercise: Dictionary<long, ObservableCollection>
ExerciseLog: Current exercise being performed
RecommendationsByExercise: Exercise-specific recommendations (sets, reps, weight)
LocalDBManager
Daily stats tracking
User preferences (mass unit, timer settings)
Workout progress flags
API Request/Response Examples
AddWorkoutLogSerieNew Request
{
"Id": 0,
"Exercice": {
"Id": 12345
},
"UserId": "user-guid-here",
"Reps": 12,
"Weight": {
"Kg": 50.0,
"Lb": 110.23
},
"RIR": 2,
"IsWarmups": false,
"LogDate": "2024-01-15T10:30:00Z"
}
SaveWorkoutV3Pro Request
{
"WorkoutId": 98765,
"WorkoutTemplateId": 54321
}
Response (Both endpoints)
{
"Result": true
}

Save Set API Calls in Order

1. Individual Set Saving (/api/Exercise/AddWorkoutLogSerieNew)
   When: After each set completion
   Endpoint: POST /api/Exercise/AddWorkoutLogSerieNew
   Local Handling:

Sets stored in CurrentLog.Instance.WorkoutLogSeriesByExercise dictionary
Each set tracked locally before API sync
Code Example (DrMuscle/Views/SaveSetPage.xaml.cs:821):

csharp

private async Task PushToDataServer()
{
foreach (WorkoutLogSerieModel l in workoutLogSerieModel)
{
// Send each set individually
BooleanModel b = await DrMuscleRestClient.Instance.AddWorkoutLogSerie(l);
result = result && b.Result;
}
} 2. Batch Set Saving (/api/Exercise/AddWorkoutLogSerieListNew)
When: When finishing an exercise (preferred method)
Endpoint: POST /api/Exercise/AddWorkoutLogSerieListNew
Local Handling:

All sets for exercise sent together
More efficient than individual calls
Code Example (DrMuscle/Views/DemoWorkoutPage.xaml.cs:3066):

csharp

// Send all sets at once
List<WorkoutLogSerieModel> serieModelList =
CurrentLog.Instance.WorkoutLogSeriesByExercise[CurrentLog.Instance.ExerciseLog.Exercice.Id];

BooleanModel b = await DrMuscleRestClient.Instance
.AddWorkoutLogSerieListwithoutLoader(serieModelList); 3. Workout Completion (/api/Workout/SaveWorkoutV3Pro)
When: After all exercises completed
Endpoint: POST /api/Workout/SaveWorkoutV3Pro
Local Handling:

Minimal data sent (just IDs)
Marks entire workout as complete
Code Example (DrMuscle/Views/DemoWorkoutPage.xaml.cs:3132):

csharp

SaveWorkoutModel saveWorkout = new SaveWorkoutModel
{
WorkoutId = Convert.ToInt64(workoutid),
WorkoutTemplateId = workoutTemplateId
};

var result = await DrMuscleRestClient.Instance.SaveWorkoutV3(saveWorkout);
Complete Logic Flow
Local Data Management:
csharp

// 1. Set data collected locally first
WorkoutLogSerieModelEx set = new WorkoutLogSerieModelEx
{
Exercice = CurrentLog.Instance.ExerciseLog.Exercice,
Reps = reps,
Weight = new MultiUnityWeight(weight, unit),
IsWarmups = isWarmup,
RIR = rirValue, // Collected after first work set
LogDate = DateTime.UtcNow
};

// 2. Add to local collection
CurrentLog.Instance.WorkoutLogSeriesByExercise[exerciseId].Add(set);

// 3. Update local state
LocalDBManager.Instance.SetDBSetting($"Exercise\_{exerciseId}\_Completed", "true");
API Client Implementation:
csharp

// With retry logic
public async Task<BooleanModel> AddWorkoutLogSerie(WorkoutLogSerieModel model)
{
int retryCount = 0;
while (retryCount < 4)
{
try
{
var result = await RestClient.PostAsync<BooleanModel>(
"api/Exercise/AddWorkoutLogSerieNew", model);
return result;
}
catch (Exception ex)
{
retryCount++;
if (retryCount >= 4) throw;
await Task.Delay(1000 \* retryCount); // Exponential backoff
}
}
}
Error Handling:
csharp

// Network check before API calls
if (Connectivity.NetworkAccess != NetworkAccess.Internet)
{
// Store locally and show offline message
await DisplayAlert("Offline", "Sets saved locally. Will sync when online.", "OK");
return;
}

// API call with error handling
try
{
var success = await PushToDataServer();
if (!success)
{
// Retry or save for later
LocalDBManager.Instance.SetDBSetting("PendingSets", JsonConvert.SerializeObject(sets));
}
}
catch (Exception ex)
{
// Log error and save locally
Analytics.TrackEvent("SaveSetError", new Dictionary<string, string> {
{ "Error", ex.Message }
});
}
Summary
The save set workflow follows an offline-first approach where sets are collected locally during the workout, then synced to the API when completing each exercise. The workflow uses batch operations when possible for efficiency and includes comprehensive error handling with automatic retry logic.
