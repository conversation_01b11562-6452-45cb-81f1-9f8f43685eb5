import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseItemSkeleton } from '../ExerciseItemSkeleton'

describe('ExerciseItemSkeleton', () => {
  it('should render skeleton with correct structure', () => {
    render(<ExerciseItemSkeleton />)

    const skeleton = screen.getByTestId('exercise-item-skeleton')
    expect(skeleton).toBeInTheDocument()
  })

  it('should have shimmer animation class', () => {
    render(<ExerciseItemSkeleton />)

    const skeleton = screen.getByTestId('exercise-item-skeleton')
    expect(skeleton).toHaveClass('animate-pulse')
  })

  it('should match exercise item dimensions', () => {
    render(<ExerciseItemSkeleton />)

    const skeleton = screen.getByTestId('exercise-item-skeleton')
    expect(skeleton).toHaveClass('rounded-lg', 'bg-white', 'p-4', 'shadow-sm')
  })

  it('should contain exercise name skeleton', () => {
    render(<ExerciseItemSkeleton />)

    const nameSkeleton = screen.getByTestId('exercise-name-skeleton')
    expect(nameSkeleton).toBeInTheDocument()
    expect(nameSkeleton).toHaveClass('h-6', 'w-2/3', 'bg-gray-200')
  })

  it('should have aria-busy attribute for accessibility', () => {
    render(<ExerciseItemSkeleton />)

    const skeleton = screen.getByTestId('exercise-item-skeleton')
    expect(skeleton).toHaveAttribute('aria-busy', 'true')
  })

  it('should have aria-label for screen readers', () => {
    render(<ExerciseItemSkeleton />)

    const skeleton = screen.getByTestId('exercise-item-skeleton')
    expect(skeleton).toHaveAttribute('aria-label', 'Loading exercise')
  })

  it('should accept custom className', () => {
    render(<ExerciseItemSkeleton className="custom-class" />)

    const skeleton = screen.getByTestId('exercise-item-skeleton')
    expect(skeleton).toHaveClass('custom-class')
  })

  it('should maintain base classes when custom className is provided', () => {
    render(<ExerciseItemSkeleton className="custom-class" />)

    const skeleton = screen.getByTestId('exercise-item-skeleton')
    expect(skeleton).toHaveClass('rounded-lg', 'bg-white', 'p-4', 'shadow-sm')
    expect(skeleton).toHaveClass('custom-class')
  })

  it('should have role=status for accessibility', () => {
    render(<ExerciseItemSkeleton />)

    const skeleton = screen.getByTestId('exercise-item-skeleton')
    expect(skeleton).toHaveAttribute('role', 'status')
  })

  it('should render multiple skeletons correctly', () => {
    render(
      <>
        <ExerciseItemSkeleton />
        <ExerciseItemSkeleton />
        <ExerciseItemSkeleton />
        <ExerciseItemSkeleton />
        <ExerciseItemSkeleton />
      </>
    )

    const skeletons = screen.getAllByTestId('exercise-item-skeleton')
    expect(skeletons).toHaveLength(5)
  })

  it('should have rounded corners for skeleton elements', () => {
    render(<ExerciseItemSkeleton />)

    const nameSkeleton = screen.getByTestId('exercise-name-skeleton')
    expect(nameSkeleton).toHaveClass('rounded')
  })

  it('should not cause layout shift', () => {
    render(<ExerciseItemSkeleton />)

    const skeleton = screen.getByTestId('exercise-item-skeleton')

    // Check that skeleton has defined height through padding
    expect(skeleton).toHaveClass('p-4')

    // Check that inner element has defined height
    const nameSkeleton = screen.getByTestId('exercise-name-skeleton')
    expect(nameSkeleton).toHaveClass('h-6')
  })

  it('should be visually hidden from screen readers content', () => {
    render(<ExerciseItemSkeleton />)

    const nameSkeleton = screen.getByTestId('exercise-name-skeleton')

    // Skeleton element should not have text content
    expect(nameSkeleton).toHaveTextContent('')
  })

  it('should match the hover state styling', () => {
    render(<ExerciseItemSkeleton />)

    const skeleton = screen.getByTestId('exercise-item-skeleton')
    // Should not have hover classes since it's a skeleton
    expect(skeleton).not.toHaveClass('cursor-pointer')
    expect(skeleton).not.toHaveClass('hover:bg-gray-50')
  })

  it('should have consistent spacing with exercise items', () => {
    render(
      <div className="space-y-3">
        <ExerciseItemSkeleton />
        <ExerciseItemSkeleton />
      </div>
    )

    const skeletons = screen.getAllByTestId('exercise-item-skeleton')
    expect(skeletons).toHaveLength(2)

    // Parent should handle spacing, skeleton should not have margin
    skeletons.forEach((skeleton) => {
      expect(skeleton).not.toHaveClass('mb-3', 'mt-3')
    })
  })
})
