/**
 * Authentication-related API Types
 *
 * These TypeScript interfaces map to authentication models from the DrMuscle production API.
 */

import type { MultiUnityWeight } from './workout'

/**
 * Login credentials model
 * @example
 * const login: LoginModel = {
 *   Username: "<EMAIL>", // Email address
 *   Password: "securePassword123"
 * }
 */
export interface LoginModel {
  Username: string // This is the email address
  Password: string
  NewPassword?: string
  Validation?: string
}

/**
 * Successful login response with OAuth 2.0 Bearer token
 * @example
 * const result: LoginSuccessResult = {
 *   access_token: "eyJhbGciOiJIUzI1NiIsInR...",
 *   token_type: "Bearer",
 *   expires_in: 86400,
 *   // ... other fields
 * }
 */
export interface LoginSuccessResult {
  access_token: string
  token_type: string
  expires_in: number
  userName: string
  issued: string
  expires: string
}

/**
 * Alternative login response structure used in some endpoints
 */
export interface LoginSuccessResultAlt {
  Result: boolean
  UserData: {
    Email: string
    Name?: string
    FirstName?: string
    LastName?: string
    Id?: string
    [key: string]: string | undefined
  }
  UserToken: string
  RefreshToken: string
}

/**
 * User registration model
 */
export interface RegisterModel {
  Email: string
  Password: string
  ConfirmPassword: string
  Firstname?: string
  Lastname?: string
  BirthDate?: Date
  MassUnit?: string
  Gender?: string
  IsAthlete?: boolean
  IsVegan?: boolean
  IsPurchasedPlan?: boolean
  BodyWeight?: MultiUnityWeight
  IsHumanSupport?: boolean
}

/**
 * OAuth login model for API requests
 */
export interface OAuthLoginModel {
  Provider: 'Google' | 'Apple'
  Token: string
  AuthorizationCode?: string
  FirstName?: string
  LastName?: string
  Email?: string
}
