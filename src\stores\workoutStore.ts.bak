import { create } from 'zustand'
import { persist, devtools } from 'zustand/middleware'
import type {
  WorkoutTemplateModel,
  WorkoutTemplateGroupModel,
  ExerciseModel,
  WorkoutLogSerieModel,
  GetUserProgramInfoResponseModel,
  RecommendationModel,
} from '@/types'
import type { WorkoutSession } from '@/types/app'

interface CurrentSetData {
  reps?: number
  weight?: number
  rir?: number
}

interface CachedAPIData {
  userProgramInfo: GetUserProgramInfoResponseModel | null
  userWorkouts: WorkoutTemplateModel[] | null
  todaysWorkout: WorkoutTemplateGroupModel[] | null
  exerciseRecommendations: Record<number, RecommendationModel | null>
  lastUpdated: {
    userProgramInfo: number
    userWorkouts: number
    todaysWorkout: number
    exerciseRecommendations: Record<number, number>
  }
}

// Constants
const CACHE_VERSION = 1
const MAX_RECOMMENDATIONS = 50
// const MAX_CACHE_SIZE = 500 * 1024 // 500KB - Reserved for future use

// Cache expiry times in milliseconds
const CACHE_EXPIRY = {
  userProgramInfo: 24 * 60 * 60 * 1000, // 24 hours
  userWorkouts: 24 * 60 * 60 * 1000, // 24 hours
  todaysWorkout: 24 * 60 * 60 * 1000, // 24 hours
  exerciseRecommendation: 60 * 60 * 1000, // 1 hour
}

// Cache statistics interface
export interface CacheStats {
  hits: number
  misses: number
  hitRate: number
  operationCount: number
  averageLatency: number
  totalLatency: number
  totalSize: number
  itemCount: number
  oldestDataAge: number
  freshDataCount: number
  staleDataCount: number
  hydrationTime: number
}

// Cache health interface
export interface CacheHealth {
  isHealthy: boolean
  warnings: string[]
}

interface WorkoutState {
  // State
  currentWorkout: WorkoutTemplateModel | null
  currentProgram: WorkoutTemplateGroupModel | null
  exercises: ExerciseModel[]
  currentExerciseIndex: number
  currentSetIndex: number
  workoutSession: WorkoutSession | null
  isLoading: boolean
  error: string | null
  currentSetData: CurrentSetData
  exerciseRecommendations: Map<string, RecommendationModel>
  loadingStates: Map<number, boolean>
  errors: Map<number, Error>

  // Cache State
  cachedData: CachedAPIData
  hasHydrated: boolean
  cacheVersion: number
  cacheStats: CacheStats

  // Actions
  setWorkout: (workout: WorkoutTemplateModel) => void
  startWorkout: () => void
  nextSet: () => void
  nextExercise: () => void
  saveSet: (setData: WorkoutLogSerieModel) => void
  completeWorkout: () => void
  updateCurrentSet: (data: CurrentSetData) => void
  setLoading: (loading: boolean) => void
  setError: (error: string) => void
  resetWorkout: () => void
  clearCache: () => void

  // Workout Loading Actions
  loadWorkoutProgram: () => Promise<void>
  loadWorkoutDetails: (workoutId: number) => Promise<void>
  loadExerciseRecommendation: (exerciseId: number) => Promise<void>
  getCacheKey: (userId: string, exerciseId: number, workoutId: number) => string

  // Cache Actions
  setCachedUserProgramInfo: (
    data: GetUserProgramInfoResponseModel | null
  ) => void
  setCachedUserWorkouts: (data: WorkoutTemplateModel[] | null) => void
  setCachedTodaysWorkout: (data: WorkoutTemplateGroupModel[] | null) => void
  setCachedExerciseRecommendation: (
    exerciseId: number,
    data: RecommendationModel | null
  ) => void
  getCachedUserProgramInfo: () => GetUserProgramInfoResponseModel | null
  getCachedUserWorkouts: () => WorkoutTemplateModel[] | null
  getCachedTodaysWorkout: () => WorkoutTemplateGroupModel[] | null
  getCachedExerciseRecommendation: (
    exerciseId: number
  ) => RecommendationModel | null | undefined
  setHasHydrated: (hydrated: boolean) => void
  handleCacheVersionMismatch: () => void
  getCacheSize: () => number
  isCacheStale: (
    type:
      | 'userProgramInfo'
      | 'userWorkouts'
      | 'todaysWorkout'
      | 'exerciseRecommendation',
    exerciseId?: number
  ) => boolean
  clearExpiredCache: () => void

  // Cache Monitoring
  getCacheStats: () => CacheStats
  resetCacheStats: () => void
  logCacheContents: () => void
  clearAllCache: () => void
  getCacheHealth: () => CacheHealth

  // Getters
  getCurrentExercise: () => ExerciseModel | null
  getCurrentSet: () => CurrentSetData | null
  isWorkoutComplete: () => boolean
  getWorkoutDuration: () => number
  getNextExercise: () => ExerciseModel | null
  getRestDuration: () => number
  getExerciseProgress: () => {
    totalSets: number
    completedSets: number
    isFirstWorkSet: boolean
    currentSetIsWarmup: boolean
    hasRIR: boolean
  } | null
  updateSetRIR: (exerciseId: number) => void

  // Selectors
  getExerciseRecommendation: (exerciseId: number) => RecommendationModel | undefined
  isExerciseLoading: (exerciseId: number) => boolean
  getWorkoutExercises: () => ExerciseModel[]
}

// Helper functions for validation
const isValidUserProgramInfo = (
  data: unknown
): data is GetUserProgramInfoResponseModel => {
  return (
    data !== null &&
    typeof data === 'object' &&
    'RecommendedProgram' in data &&
    data.RecommendedProgram !== null &&
    typeof data.RecommendedProgram === 'object' &&
    'Id' in data.RecommendedProgram &&
    'Label' in data.RecommendedProgram &&
    typeof data.RecommendedProgram.Id === 'number' &&
    typeof data.RecommendedProgram.Label === 'string'
  )
}

const isValidWorkoutTemplate = (
  data: unknown
): data is WorkoutTemplateModel => {
  if (data === null || typeof data !== 'object') {
    return false
  }

  const obj = data as Record<string, unknown>
  return (
    'Id' in obj &&
    'Label' in obj &&
    'Exercises' in obj &&
    typeof obj.Id === 'number' &&
    typeof obj.Label === 'string' &&
    Array.isArray(obj.Exercises)
  )
}

const isValidWorkoutTemplateArray = (
  data: unknown
): data is WorkoutTemplateModel[] => {
  return Array.isArray(data) && data.every(isValidWorkoutTemplate)
}

const isValidRecommendation = (data: unknown): data is RecommendationModel => {
  if (data === null || typeof data !== 'object') {
    return false
  }

  const obj = data as Record<string, unknown>

  if (!('Series' in obj) || !('Reps' in obj) || !('Weight' in obj)) {
    return false
  }

  if (typeof obj.Series !== 'number' || typeof obj.Reps !== 'number') {
    return false
  }

  if (obj.Weight === null || typeof obj.Weight !== 'object') {
    return false
  }

  const weight = obj.Weight as Record<string, unknown>
  return typeof weight.Lb === 'number' && typeof weight.Kg === 'number'
}

const initialCachedData: CachedAPIData = {
  userProgramInfo: null,
  userWorkouts: null,
  todaysWorkout: null,
  exerciseRecommendations: {},
  lastUpdated: {
    userProgramInfo: 0,
    userWorkouts: 0,
    todaysWorkout: 0,
    exerciseRecommendations: {},
  },
}

const initialCacheStats: CacheStats = {
  hits: 0,
  misses: 0,
  hitRate: 0,
  operationCount: 0,
  averageLatency: 0,
  totalLatency: 0,
  totalSize: 0,
  itemCount: 0,
  oldestDataAge: 0,
  freshDataCount: 0,
  staleDataCount: 0,
  hydrationTime: 0,
}

const initialState = {
  currentWorkout: null,
  currentProgram: null,
  exercises: [],
  currentExerciseIndex: 0,
  currentSetIndex: 0,
  workoutSession: null,
  isLoading: false,
  error: null,
  currentSetData: {},
  exerciseRecommendations: new Map<string, RecommendationModel>(),
  loadingStates: new Map<number, boolean>(),
  errors: new Map<number, Error>(),
  cachedData: initialCachedData,
  hasHydrated: false,
  cacheVersion: CACHE_VERSION,
  cacheStats: initialCacheStats,
}

// Helper function to track cache operation metrics
const trackCacheOperation = (
  set: (partial: Partial<WorkoutState>) => void,
  get: () => WorkoutState,
  hasResult: boolean,
  latency: number
) => {
  const state = get()
  const newHits = state.cacheStats.hits + (hasResult ? 1 : 0)
  const newMisses = state.cacheStats.misses + (hasResult ? 0 : 1)
  const newOperationCount = state.cacheStats.operationCount + 1
  const newTotalLatency = state.cacheStats.totalLatency + latency

  set({
    cacheStats: {
      ...state.cacheStats,
      hits: newHits,
      misses: newMisses,
      operationCount: newOperationCount,
      totalLatency: newTotalLatency,
      hitRate: newOperationCount > 0 ? newHits / newOperationCount : 0,
      averageLatency:
        newOperationCount > 0 ? newTotalLatency / newOperationCount : 0,
    },
  })
}

export const useWorkoutStore = create<WorkoutState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        setWorkout: (workout: WorkoutTemplateModel) => {
          set({
            currentWorkout: workout,
            exercises: workout.Exercises || [],
            currentExerciseIndex: 0,
            currentSetIndex: 0,
            error: null,
          })
        },

        startWorkout: () => {
          const { currentWorkout } = get()
          if (!currentWorkout) return

          set({
            workoutSession: {
              id: `session-${Date.now()}`,
              startTime: new Date(),
              exercises: [],
              exerciseRIRStatus: {},
            },
          })
        },

        nextSet: () => {
          set((state) => ({
            currentSetIndex: state.currentSetIndex + 1,
            currentSetData: {},
          }))
        },

        nextExercise: () => {
          const { currentExerciseIndex, exercises } = get()
          if (currentExerciseIndex < exercises.length - 1) {
            set({
              currentExerciseIndex: currentExerciseIndex + 1,
              currentSetIndex: 0,
              currentSetData: {},
            })
          }
        },

        saveSet: (setData: WorkoutLogSerieModel) => {
          const { workoutSession } = get()
          if (!workoutSession) return

          const exerciseId = setData.ExerciseId
          if (!exerciseId) return // Exit if no exercise ID

          const existingExercise = workoutSession.exercises.find(
            (e) => e.exerciseId === exerciseId
          )

          const newSet = {
            setNumber: 1, // Default set number, should be calculated based on existing sets
            reps: setData.Reps,
            weight: setData.Weight,
            rir: setData.RIR,
            isWarmup: setData.IsWarmups,
            timestamp: new Date(),
          }

          if (existingExercise) {
            existingExercise.sets.push(newSet)
          } else {
            workoutSession.exercises.push({
              exerciseId,
              name: 'Exercise', // Default name, should be retrieved from exercise data
              sets: [newSet],
            })
          }

          set({ workoutSession: { ...workoutSession } })
        },

        completeWorkout: () => {
          const { workoutSession } = get()
          if (!workoutSession) return

          set({
            workoutSession: {
              ...workoutSession,
              endTime: new Date(),
            },
          })
        },

        updateCurrentSet: (data: CurrentSetData) => {
          set((state) => ({
            currentSetData: {
              ...state.currentSetData,
              ...data,
            },
          }))
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading })
        },

        setError: (error: string) => {
          // Set error without clearing cache
          set({ error, isLoading: false })
        },

        resetWorkout: () => {
          // Reset workout state but preserve cache
          const { cachedData, cacheStats, hasHydrated, cacheVersion, currentProgram, exerciseRecommendations } = get()
          set({
            ...initialState,
            cachedData,
            cacheStats,
            hasHydrated,
            cacheVersion,
            currentProgram,
            exerciseRecommendations,
          })
        },

        clearCache: () => {
          // User-initiated cache clear only
          set({
            cachedData: initialCachedData,
            cacheStats: initialCacheStats,
          })
        },

        // Getters
        getCurrentExercise: () => {
          const { exercises, currentExerciseIndex } = get()
          return exercises[currentExerciseIndex] || null
        },

        getCurrentSet: () => {
          const { currentSetData } = get()
          return Object.keys(currentSetData).length > 0 ? currentSetData : null
        },

        isWorkoutComplete: () => {
          const { workoutSession } = get()
          return workoutSession?.endTime !== undefined
        },

        getWorkoutDuration: () => {
          const { workoutSession } = get()
          if (!workoutSession || !workoutSession.endTime) return 0

          return (
            workoutSession.endTime.getTime() -
            workoutSession.startTime.getTime()
          )
        },

        getNextExercise: () => {
          const { exercises, currentExerciseIndex } = get()
          if (currentExerciseIndex < exercises.length - 1) {
            return exercises[currentExerciseIndex + 1] || null
          }
          return null
        },

        getRestDuration: () => {
          // Simplified rest duration logic
          return 120 // Default 2 minutes
        },

        getExerciseProgress: () => {
          const {
            currentSetIndex,
            workoutSession,
            exercises,
            currentExerciseIndex,
          } = get()
          const currentExercise = exercises[currentExerciseIndex]

          if (!currentExercise) return null

          // Simplified exercise progress
          const totalSets = 4 // Default sets
          const completedSets = currentSetIndex
          const isFirstWorkSet = currentSetIndex === 0
          const currentSetIsWarmup = false // No warmup detection

          // Check if RIR has been captured for this exercise
          const hasRIR =
            workoutSession?.exerciseRIRStatus?.[currentExercise.Id] || false

          return {
            totalSets,
            completedSets,
            isFirstWorkSet,
            currentSetIsWarmup,
            hasRIR,
          }
        },

        updateSetRIR: (exerciseId: number) => {
          const { workoutSession } = get()
          if (!workoutSession) return

          // Mark that RIR has been captured for this exercise
          const updatedSession = {
            ...workoutSession,
            exerciseRIRStatus: {
              ...workoutSession.exerciseRIRStatus,
              [exerciseId]: true,
            },
          }

          set({ workoutSession: updatedSession })
        },

        // Cache Actions
        setCachedUserProgramInfo: (
          data: GetUserProgramInfoResponseModel | null
        ) => {
          if (data !== null && !isValidUserProgramInfo(data)) {
            console.warn('Invalid user program info data, not caching')
            return
          }

          set((state) => ({
            cachedData: {
              ...state.cachedData,
              userProgramInfo: data,
              lastUpdated: {
                ...state.cachedData.lastUpdated,
                userProgramInfo: Date.now(),
              },
            },
          }))
        },

        setCachedUserWorkouts: (data: WorkoutTemplateModel[] | null) => {
          if (data !== null && !isValidWorkoutTemplateArray(data)) {
            console.warn('Invalid workout templates data, not caching')
            return
          }

          set((state) => ({
            cachedData: {
              ...state.cachedData,
              userWorkouts: data,
              lastUpdated: {
                ...state.cachedData.lastUpdated,
                userWorkouts: Date.now(),
              },
            },
          }))
        },

        setCachedTodaysWorkout: (data: WorkoutTemplateGroupModel[] | null) => {
          set((state) => ({
            cachedData: {
              ...state.cachedData,
              todaysWorkout: data,
              lastUpdated: {
                ...state.cachedData.lastUpdated,
                todaysWorkout: Date.now(),
              },
            },
          }))
        },

        setCachedExerciseRecommendation: (
          exerciseId: number,
          data: RecommendationModel | null
        ) => {
          if (data !== null && !isValidRecommendation(data)) {
            console.warn(
              `Invalid recommendation data for exercise ${exerciseId}, not caching`
            )
            return
          }

          set((state) => {
            const recommendations = {
              ...state.cachedData.exerciseRecommendations,
            }
            const timestamps = {
              ...state.cachedData.lastUpdated.exerciseRecommendations,
            }

            // Enforce max recommendations limit
            const recommendationCount = Object.keys(recommendations).length
            if (
              recommendationCount >= MAX_RECOMMENDATIONS &&
              !recommendations[exerciseId]
            ) {
              // Remove oldest recommendation
              const sortedEntries = Object.entries(timestamps).sort(
                ([, a], [, b]) => a - b
              )
              if (sortedEntries.length > 0 && sortedEntries[0]) {
                const [oldestId] = sortedEntries[0]
                delete recommendations[Number(oldestId)]
                delete timestamps[Number(oldestId)]
              }
            }

            recommendations[exerciseId] = data
            timestamps[exerciseId] = Date.now()

            return {
              cachedData: {
                ...state.cachedData,
                exerciseRecommendations: recommendations,
                lastUpdated: {
                  ...state.cachedData.lastUpdated,
                  exerciseRecommendations: timestamps,
                },
              },
            }
          })
        },

        getCachedUserProgramInfo: () => {
          const startTime = performance.now()
          const { hasHydrated, cachedData } = get()

          if (!hasHydrated) return null

          const result = cachedData.userProgramInfo
          const latency = performance.now() - startTime

          // Track cache operation
          trackCacheOperation(set, get, !!result, latency)

          return result
        },

        getCachedUserWorkouts: () => {
          const startTime = performance.now()
          const { hasHydrated, cachedData } = get()

          if (!hasHydrated) return null

          const result = cachedData.userWorkouts
          const latency = performance.now() - startTime

          // Track cache operation
          trackCacheOperation(set, get, !!result, latency)

          return result
        },

        getCachedTodaysWorkout: () => {
          const startTime = performance.now()
          const { hasHydrated, cachedData } = get()

          if (!hasHydrated) return null

          const result = cachedData.todaysWorkout
          const latency = performance.now() - startTime

          // Track cache operation
          trackCacheOperation(set, get, !!result, latency)

          return result
        },

        getCachedExerciseRecommendation: (exerciseId: number) => {
          const startTime = performance.now()
          const { hasHydrated, cachedData } = get()

          if (!hasHydrated) return undefined

          // Return undefined if not cached, or the actual value (which could be null)
          const hasKey = Object.prototype.hasOwnProperty.call(
            cachedData.exerciseRecommendations,
            exerciseId
          )
          const result = hasKey
            ? cachedData.exerciseRecommendations[exerciseId]
            : undefined
          const latency = performance.now() - startTime

          // Track cache operation - count as hit if key exists (even if value is null)
          trackCacheOperation(set, get, hasKey, latency)

          return result
        },

        setHasHydrated: (hydrated: boolean) => {
          if (hydrated && !get().hasHydrated) {
            // Track hydration time
            const hydrationTime = performance.now()
            set((state) => ({
              hasHydrated: hydrated,
              cacheStats: {
                ...state.cacheStats,
                hydrationTime,
              },
            }))
          } else {
            set({ hasHydrated: hydrated })
          }
        },

        getCacheSize: () => {
          const { cachedData } = get()
          try {
            return JSON.stringify(cachedData).length
          } catch {
            return 0
          }
        },

        handleCacheVersionMismatch: () => {
          // Clear all cached data when version mismatch occurs
          set({
            cachedData: {
              userProgramInfo: null,
              userWorkouts: null,
              todaysWorkout: null,
              exerciseRecommendations: {},
              lastUpdated: {
                userProgramInfo: 0,
                userWorkouts: 0,
                todaysWorkout: 0,
                exerciseRecommendations: {},
              },
            },
            cacheVersion: CACHE_VERSION,
          })
        },

        isCacheStale: (
          type:
            | 'userProgramInfo'
            | 'userWorkouts'
            | 'todaysWorkout'
            | 'exerciseRecommendation',
          exerciseId?: number
        ): boolean => {
          const { cachedData } = get()
          const now = Date.now()

          if (type === 'exerciseRecommendation' && exerciseId !== undefined) {
            const lastUpdated =
              cachedData.lastUpdated.exerciseRecommendations[exerciseId] || 0
            return (
              lastUpdated === 0 ||
              now - lastUpdated > CACHE_EXPIRY.exerciseRecommendation
            )
          }

          if (type === 'todaysWorkout') {
            const lastUpdated = cachedData.lastUpdated.todaysWorkout || 0
            return (
              lastUpdated === 0 ||
              now - lastUpdated > CACHE_EXPIRY.todaysWorkout
            )
          }

          const lastUpdated =
            cachedData.lastUpdated[
              type as 'userProgramInfo' | 'userWorkouts'
            ] || 0
          return (
            lastUpdated === 0 ||
            now - lastUpdated >
              CACHE_EXPIRY[type as 'userProgramInfo' | 'userWorkouts']
          )
        },

        clearExpiredCache: () => {
          const { cachedData, isCacheStale } = get()

          // Clear expired main caches
          const newCachedData = { ...cachedData }

          if (isCacheStale('userProgramInfo')) {
            newCachedData.userProgramInfo = null
            newCachedData.lastUpdated.userProgramInfo = 0
          }

          if (isCacheStale('userWorkouts')) {
            newCachedData.userWorkouts = null
            newCachedData.lastUpdated.userWorkouts = 0
          }

          if (isCacheStale('todaysWorkout')) {
            newCachedData.todaysWorkout = null
            newCachedData.lastUpdated.todaysWorkout = 0
          }

          // Clear expired exercise recommendations
          const newRecommendations = { ...cachedData.exerciseRecommendations }
          const newTimestamps = {
            ...cachedData.lastUpdated.exerciseRecommendations,
          }

          Object.keys(newRecommendations).forEach((exerciseIdStr) => {
            const exerciseId = Number(exerciseIdStr)
            if (isCacheStale('exerciseRecommendation', exerciseId)) {
              delete newRecommendations[exerciseId]
              delete newTimestamps[exerciseId]
            }
          })

          newCachedData.exerciseRecommendations = newRecommendations
          newCachedData.lastUpdated.exerciseRecommendations = newTimestamps

          set({ cachedData: newCachedData })
        },

        // Cache Monitoring Methods
        getCacheStats: (): CacheStats => {
          const { cachedData, cacheStats } = get()
          const now = Date.now()

          // Calculate total cache size
          let totalSize = 0
          try {
            totalSize = JSON.stringify(cachedData).length
          } catch {
            totalSize = 0
          }

          // Count items
          let itemCount = 0
          if (cachedData.userProgramInfo) itemCount++
          if (cachedData.userWorkouts) itemCount++
          if (cachedData.todaysWorkout) itemCount++
          itemCount += Object.keys(cachedData.exerciseRecommendations).length

          // Find oldest data
          let oldestTimestamp = now
          if (cachedData.lastUpdated.userProgramInfo > 0) {
            oldestTimestamp = Math.min(
              oldestTimestamp,
              cachedData.lastUpdated.userProgramInfo
            )
          }
          if (cachedData.lastUpdated.userWorkouts > 0) {
            oldestTimestamp = Math.min(
              oldestTimestamp,
              cachedData.lastUpdated.userWorkouts
            )
          }
          if (cachedData.lastUpdated.todaysWorkout > 0) {
            oldestTimestamp = Math.min(
              oldestTimestamp,
              cachedData.lastUpdated.todaysWorkout
            )
          }
          Object.values(cachedData.lastUpdated.exerciseRecommendations).forEach(
            (timestamp) => {
              if (timestamp > 0) {
                oldestTimestamp = Math.min(oldestTimestamp, timestamp)
              }
            }
          )

          const oldestDataAge =
            oldestTimestamp < now ? now - oldestTimestamp : 0

          // Count fresh vs stale data
          let freshDataCount = 0
          let staleDataCount = 0

          const { isCacheStale } = get()

          if (cachedData.userProgramInfo) {
            if (isCacheStale('userProgramInfo')) {
              staleDataCount++
            } else {
              freshDataCount++
            }
          }

          if (cachedData.userWorkouts) {
            if (isCacheStale('userWorkouts')) {
              staleDataCount++
            } else {
              freshDataCount++
            }
          }

          if (cachedData.todaysWorkout) {
            if (isCacheStale('todaysWorkout')) {
              staleDataCount++
            } else {
              freshDataCount++
            }
          }

          Object.keys(cachedData.exerciseRecommendations).forEach(
            (exerciseIdStr) => {
              const exerciseId = Number(exerciseIdStr)
              if (isCacheStale('exerciseRecommendation', exerciseId)) {
                staleDataCount++
              } else {
                freshDataCount++
              }
            }
          )

          // Calculate derived metrics
          const hitRate =
            cacheStats.operationCount > 0
              ? cacheStats.hits / cacheStats.operationCount
              : 0
          const averageLatency =
            cacheStats.operationCount > 0
              ? cacheStats.totalLatency / cacheStats.operationCount
              : 0

          return {
            hits: cacheStats.hits,
            misses: cacheStats.misses,
            hitRate,
            operationCount: cacheStats.operationCount,
            averageLatency,
            totalLatency: cacheStats.totalLatency,
            totalSize,
            itemCount,
            oldestDataAge,
            freshDataCount,
            staleDataCount,
            hydrationTime: cacheStats.hydrationTime,
          }
        },

        resetCacheStats: () => {
          set({ cacheStats: initialCacheStats })
        },

        logCacheContents: () => {
          if (process.env.NODE_ENV === 'development') {
            const stats = get().getCacheStats()
            const { cachedData } = get()
            // eslint-disable-next-line no-console
            console.log('Cache Stats:', stats)
            // eslint-disable-next-line no-console
            console.log('Cache Contents:', cachedData)
          }
        },

        clearAllCache: () => {
          set({
            cachedData: initialCachedData,
            cacheStats: initialCacheStats,
          })
        },

        getCacheHealth: (): CacheHealth => {
          const stats = get().getCacheStats()
          const warnings: string[] = []

          // Check hit rate
          if (stats.operationCount > 10 && stats.hitRate < 0.5) {
            warnings.push('Low cache hit rate')
          }

          // Check cache size
          if (stats.totalSize > 200 * 1024) {
            warnings.push('Cache size exceeds recommended limit')
          }

          // Check staleness
          if (
            stats.itemCount > 0 &&
            stats.staleDataCount > stats.freshDataCount
          ) {
            warnings.push('Majority of cache is stale')
          }

          // Check average latency
          if (stats.averageLatency > 1) {
            warnings.push('Cache operations are slow')
          }

          return {
            isHealthy: warnings.length === 0,
            warnings,
          }
        },

        // Workout Loading Actions
        loadWorkoutProgram: async () => {
          const { setCachedUserProgramInfo, setLoading, setError } = get()
          
          setLoading(true)
          setError(null)
          
          try {
            // Import workoutService dynamically to avoid circular dependencies
            const { workoutService } = await import('@/api/workout')
            const response = await workoutService.getUserWorkoutProgramInfo()
            
            if (response?.GetUserProgramInfoResponseModel) {
              setCachedUserProgramInfo(response.GetUserProgramInfoResponseModel)
              
              // Extract next workout template if available
              const nextWorkout = response.GetUserProgramInfoResponseModel.NextWorkoutTemplate
              if (nextWorkout) {
                set({ currentWorkout: nextWorkout })
              }
              
              // Extract program info if available
              const program = response.GetUserProgramInfoResponseModel.RecommendedProgram
              if (program) {
                set({ currentProgram: program })
              }
            }
          } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to load workout program')
            logger.error('Failed to load workout program:', error)
          } finally {
            setLoading(false)
          }
        },

        loadWorkoutDetails: async (workoutId: number) => {
          const { setCachedTodaysWorkout, setLoading, setError } = get()
          
          setLoading(true)
          setError(null)
          
          try {
            // Import workoutService dynamically to avoid circular dependencies
            const { workoutService } = await import('@/api/workout')
            const workout = await workoutService.getWorkoutDetails(workoutId)
            
            if (workout) {
              set({ 
                currentWorkout: workout,
                exercises: workout.Exercises || []
              })
              
              // Cache the workout
              setCachedTodaysWorkout([workout] as unknown as WorkoutTemplateGroupModel[])
            }
          } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to load workout details')
            logger.error('Failed to load workout details:', error)
          } finally {
            setLoading(false)
          }
        },

        loadExerciseRecommendation: async (exerciseId: number) => {
          const { 
            currentWorkout, 
            exerciseRecommendations, 
            loadingStates,
            errors,
            setCachedExerciseRecommendation
          } = get()
          
          // Check if already loading
          if (loadingStates.get(exerciseId)) {
            return
          }
          
          // Get workout ID
          const workoutId = currentWorkout?.Id
          if (!workoutId) {
            logger.warn('No current workout ID for recommendation request')
            return
          }
          
          // Get user from auth state
          const authState = localStorage.getItem('drmuscle-auth')
          if (!authState) {
            logger.warn('No auth state for recommendation request')
            return
          }
          
          const parsed = JSON.parse(authState)
          const username = parsed.state?.user?.email
          if (!username) {
            logger.warn('No username for recommendation request')
            return
          }
          
          // Generate cache key
          const cacheKey = get().getCacheKey(username, exerciseId, workoutId)
          
          // Set loading state
          set((state) => {
            const newLoadingStates = new Map(state.loadingStates)
            newLoadingStates.set(exerciseId, true)
            return { loadingStates: newLoadingStates }
          })
          
          try {
            // Import workoutService dynamically to avoid circular dependencies
            const { workoutService } = await import('@/api/workout')
            const recommendation = await workoutService.getExerciseRecommendation({
              Username: username,
              ExerciseId: exerciseId,
              WorkoutId: workoutId,
            })
            
            if (recommendation) {
              // Store in memory map
              set((state) => {
                const newRecommendations = new Map(state.exerciseRecommendations)
                newRecommendations.set(cacheKey, recommendation)
                return { exerciseRecommendations: newRecommendations }
              })
              
              // Store in cache
              setCachedExerciseRecommendation(exerciseId, recommendation)
            }
          } catch (error) {
            // Store error
            set((state) => {
              const newErrors = new Map(state.errors)
              newErrors.set(exerciseId, error instanceof Error ? error : new Error('Failed to load recommendation'))
              return { errors: newErrors }
            })
            logger.error(`Failed to load recommendation for exercise ${exerciseId}:`, error)
          } finally {
            // Clear loading state
            set((state) => {
              const newLoadingStates = new Map(state.loadingStates)
              newLoadingStates.delete(exerciseId)
              return { loadingStates: newLoadingStates }
            })
          }
        },

        getCacheKey: (userId: string, exerciseId: number, workoutId: number): string => {
          return `${userId}-${exerciseId}-${workoutId}`
        },

        // Selectors
        getExerciseRecommendation: (exerciseId: number): RecommendationModel | undefined => {
          const { exerciseRecommendations, currentWorkout } = get()
          
          // Get user from auth state
          const authState = localStorage.getItem('drmuscle-auth')
          if (!authState) return undefined
          
          const parsed = JSON.parse(authState)
          const username = parsed.state?.user?.email
          if (!username || !currentWorkout?.Id) return undefined
          
          const cacheKey = get().getCacheKey(username, exerciseId, currentWorkout.Id)
          return exerciseRecommendations.get(cacheKey)
        },

        isExerciseLoading: (exerciseId: number): boolean => {
          return get().loadingStates.get(exerciseId) || false
        },

        getWorkoutExercises: (): ExerciseModel[] => {
          return get().exercises
        },
      }),
      {
        name: 'drmuscle-workout',
        partialize: (state: WorkoutState) => ({
          workoutSession: state.workoutSession,
          cachedData: state.cachedData,
          cacheVersion: state.cacheVersion,
          currentProgram: state.currentProgram,
          // Convert Maps to objects for serialization
          exerciseRecommendations: Object.fromEntries(state.exerciseRecommendations),
        }),
        onRehydrateStorage: () => (state: WorkoutState | undefined) => {
          if (state) {
            // Track hydration time
            const hydrationStart = performance.now()
            
            // Restore Maps from persisted objects
            if (state.exerciseRecommendations && typeof state.exerciseRecommendations === 'object' && !state.exerciseRecommendations instanceof Map) {
              state.exerciseRecommendations = new Map(Object.entries(state.exerciseRecommendations))
            }
            
            // Initialize missing Maps
            if (!state.loadingStates || !(state.loadingStates instanceof Map)) {
              state.loadingStates = new Map()
            }
            if (!state.errors || !(state.errors instanceof Map)) {
              state.errors = new Map()
            }
            
            state.setHasHydrated(true)
            // Clean expired data on hydration
            state.clearExpiredCache()
            // Update hydration time
            state.cacheStats.hydrationTime = performance.now() - hydrationStart
          }
        },
      }
    )
  )
)
