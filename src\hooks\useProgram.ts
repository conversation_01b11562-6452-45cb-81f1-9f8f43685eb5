import { useQuery } from '@tanstack/react-query'
import { programApi } from '@/api/program'
import { useAuthStore } from '@/stores/authStore'
import type { ProgramModel, ProgramProgress, ProgramStats } from '@/types'

/**
 * Hook to fetch current user's program information
 * @returns Program data with loading and error states
 */
export function useProgram() {
  const { isAuthenticated } = useAuthStore()

  const {
    data: program,
    isLoading,
    error,
    refetch,
  } = useQuery<ProgramModel | null>({
    queryKey: ['program'],
    queryFn: programApi.getUserProgram,
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  })

  return {
    program,
    isLoading,
    error,
    refetch,
  }
}

/**
 * Hook to fetch program progress data
 * @param programId The program ID (optional, will use current program if not provided)
 * @returns Progress data with loading and error states
 */
export function useProgramProgress(programId?: number) {
  const { isAuthenticated } = useAuthStore()

  const {
    data: progress,
    isLoading,
    error,
    refetch,
  } = useQuery<ProgramProgress | null>({
    queryKey: ['programProgress', programId],
    queryFn: () => programApi.getProgramProgress(),
    enabled: isAuthenticated && !!programId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  })

  return {
    progress,
    isLoading,
    error,
    refetch,
  }
}

/**
 * Hook to fetch program statistics
 * @param programId The program ID (optional, will use current program if not provided)
 * @returns Stats data with loading and error states
 */
export function useProgramStats(programId?: number) {
  const { isAuthenticated } = useAuthStore()

  const {
    data: stats,
    isLoading,
    error,
    refetch,
  } = useQuery<ProgramStats>({
    queryKey: ['programStats', programId],
    queryFn: () => programApi.getProgramStats(),
    enabled: isAuthenticated && !!programId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  })

  return {
    stats,
    isLoading,
    error,
    refetch,
  }
}

/**
 * Combined hook that fetches all program data
 * Useful for the Program Overview page
 */
export function useProgramData() {
  const {
    program,
    isLoading: isLoadingProgram,
    error: programError,
  } = useProgram()
  const programId = program?.id

  const {
    progress,
    isLoading: isLoadingProgress,
    error: progressError,
  } = useProgramProgress(programId)

  const {
    stats,
    isLoading: isLoadingStats,
    error: statsError,
  } = useProgramStats(programId)

  return {
    program,
    progress,
    stats,
    isLoading: isLoadingProgram || isLoadingProgress || isLoadingStats,
    isLoadingProgram,
    isLoadingProgress,
    isLoadingStats,
    error: programError || progressError || statsError,
    hasData: !!program,
  }
}
