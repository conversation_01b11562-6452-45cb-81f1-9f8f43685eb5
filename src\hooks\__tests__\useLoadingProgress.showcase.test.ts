import { renderHook, act } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { useLoadingProgress } from '../useLoadingProgress'

describe('useLoadingProgress Showcase', () => {
  it('should demonstrate typical workout loading flow', () => {
    const { result } = renderHook(() => useLoadingProgress())

    // Simulate login flow
    act(() => {
      result.current.addTask('auth', 'Authenticating...', 1)
    })
    expect(result.current.progress).toBe(0)
    expect(result.current.status).toBe('Authenticating...')

    act(() => {
      result.current.updateTask('auth', 100, 'Authentication complete')
    })
    expect(result.current.progress).toBe(100)

    // Start loading workout data
    act(() => {
      result.current.addTask('userInfo', 'Loading user information...', 1)
      result.current.addTask('workout', 'Loading workout plan...', 2)
      result.current.addTask('exercises', 'Loading exercises...', 2)
    })

    // Progress should drop as new tasks are added
    expect(result.current.progress).toBeLessThan(100)

    // Simulate progressive loading
    act(() => {
      result.current.updateTask('userInfo', 100, 'User information loaded')
    })

    act(() => {
      result.current.updateTask('workout', 50, 'Loading workout details...')
    })

    act(() => {
      result.current.updateTask('exercises', 30, 'Loading exercise data...')
    })

    // Verify weighted progress calculation
    // auth: 100 * 1 = 100 (weight 1)
    // userInfo: 100 * 1 = 100 (weight 1)
    // workout: 50 * 2 = 100 (weight 2)
    // exercises: 30 * 2 = 60 (weight 2)
    // Total: (100 + 100 + 100 + 60) / (1 + 1 + 2 + 2) = 360 / 6 = 60
    expect(result.current.progress).toBe(60)

    // Complete all tasks
    act(() => {
      result.current.updateTask('workout', 100, 'Workout loaded')
      result.current.updateTask('exercises', 100, 'All exercises loaded')
    })

    expect(result.current.progress).toBe(100)
    expect(result.current.isComplete).toBe(true)
    expect(result.current.status).toBe('All exercises loaded')
  })

  it('should handle parallel data loading scenario', () => {
    const { result } = renderHook(() => useLoadingProgress())

    // Add all tasks at once (parallel loading)
    act(() => {
      result.current.addTask('profile', 'Loading profile...', 1)
      result.current.addTask('workout', 'Loading workout...', 3)
      result.current.addTask('recommendations', 'Loading recommendations...', 1)
      result.current.addTask('history', 'Loading history...', 1)
    })

    // Simulate different completion speeds
    act(() => {
      // Quick tasks complete first
      result.current.updateTask('profile', 100)
      result.current.updateTask('history', 100)

      // Heavier tasks still loading
      result.current.updateTask('workout', 30)
      result.current.updateTask('recommendations', 80)
    })

    expect(result.current.progress).toBeGreaterThan(0)
    expect(result.current.progress).toBeLessThan(100)
    expect(result.current.isComplete).toBe(false)

    // Complete remaining tasks
    act(() => {
      result.current.updateTask('workout', 100, 'Workout ready!')
      result.current.updateTask('recommendations', 100)
    })

    expect(result.current.isComplete).toBe(true)
    // Last update was recommendations, so it should be the current status
    expect(result.current.status).toBe('Loading recommendations...')
  })

  it('should handle error recovery scenario', () => {
    const { result } = renderHook(() => useLoadingProgress())

    // Start loading
    act(() => {
      result.current.addTask('data', 'Loading data...', 1)
      result.current.updateTask('data', 50)
    })

    // Simulate error - reset and retry
    act(() => {
      result.current.reset()
    })

    expect(result.current.progress).toBe(0)
    expect(result.current.tasks).toEqual({})

    // Retry with new tasks
    act(() => {
      result.current.addTask('retry', 'Retrying...', 1)
      result.current.updateTask('retry', 100, 'Success!')
    })

    expect(result.current.isComplete).toBe(true)
  })
})
