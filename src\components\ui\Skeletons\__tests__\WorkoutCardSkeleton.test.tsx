import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { WorkoutCardSkeleton } from '../WorkoutCardSkeleton'

describe('WorkoutCardSkeleton', () => {
  it('should render skeleton with correct structure', () => {
    render(<WorkoutCardSkeleton />)

    const skeleton = screen.getByTestId('workout-card-skeleton')
    expect(skeleton).toBeInTheDocument()
  })

  it('should have shimmer animation class', () => {
    render(<WorkoutCardSkeleton />)

    const skeleton = screen.getByTestId('workout-card-skeleton')
    expect(skeleton).toHaveClass('animate-pulse')
  })

  it('should match workout card dimensions', () => {
    render(<WorkoutCardSkeleton />)

    const skeleton = screen.getByTestId('workout-card-skeleton')
    expect(skeleton).toHaveClass('rounded-lg', 'bg-white', 'p-6', 'shadow-sm')
  })

  it('should contain title skeleton', () => {
    render(<WorkoutCardSkeleton />)

    const titleSkeleton = screen.getByTestId('workout-card-title-skeleton')
    expect(titleSkeleton).toBeInTheDocument()
    expect(titleSkeleton).toHaveClass('h-7', 'w-3/4', 'bg-gray-200')
  })

  it('should contain subtitle skeleton', () => {
    render(<WorkoutCardSkeleton />)

    const subtitleSkeleton = screen.getByTestId(
      'workout-card-subtitle-skeleton'
    )
    expect(subtitleSkeleton).toBeInTheDocument()
    expect(subtitleSkeleton).toHaveClass('h-5', 'w-1/2', 'bg-gray-200')
  })

  it('should have proper spacing between elements', () => {
    render(<WorkoutCardSkeleton />)

    const titleSkeleton = screen.getByTestId('workout-card-title-skeleton')
    expect(titleSkeleton).toHaveClass('mb-2')
  })

  it('should have aria-busy attribute for accessibility', () => {
    render(<WorkoutCardSkeleton />)

    const skeleton = screen.getByTestId('workout-card-skeleton')
    expect(skeleton).toHaveAttribute('aria-busy', 'true')
  })

  it('should have aria-label for screen readers', () => {
    render(<WorkoutCardSkeleton />)

    const skeleton = screen.getByTestId('workout-card-skeleton')
    expect(skeleton).toHaveAttribute(
      'aria-label',
      'Loading workout information'
    )
  })

  it('should accept custom className', () => {
    render(<WorkoutCardSkeleton className="custom-class" />)

    const skeleton = screen.getByTestId('workout-card-skeleton')
    expect(skeleton).toHaveClass('custom-class')
  })

  it('should maintain base classes when custom className is provided', () => {
    render(<WorkoutCardSkeleton className="custom-class" />)

    const skeleton = screen.getByTestId('workout-card-skeleton')
    expect(skeleton).toHaveClass('rounded-lg', 'bg-white', 'p-6', 'shadow-sm')
    expect(skeleton).toHaveClass('custom-class')
  })

  it('should have role=status for accessibility', () => {
    render(<WorkoutCardSkeleton />)

    const skeleton = screen.getByTestId('workout-card-skeleton')
    expect(skeleton).toHaveAttribute('role', 'status')
  })

  it('should render multiple skeletons correctly', () => {
    render(
      <>
        <WorkoutCardSkeleton />
        <WorkoutCardSkeleton />
        <WorkoutCardSkeleton />
      </>
    )

    const skeletons = screen.getAllByTestId('workout-card-skeleton')
    expect(skeletons).toHaveLength(3)
  })

  it('should have rounded corners for skeleton elements', () => {
    render(<WorkoutCardSkeleton />)

    const titleSkeleton = screen.getByTestId('workout-card-title-skeleton')
    const subtitleSkeleton = screen.getByTestId(
      'workout-card-subtitle-skeleton'
    )

    expect(titleSkeleton).toHaveClass('rounded')
    expect(subtitleSkeleton).toHaveClass('rounded')
  })

  it('should not cause layout shift', () => {
    render(<WorkoutCardSkeleton />)

    const skeleton = screen.getByTestId('workout-card-skeleton')

    // Check that skeleton has defined height through padding
    expect(skeleton).toHaveClass('p-6')

    // Check that inner elements have defined heights
    const titleSkeleton = screen.getByTestId('workout-card-title-skeleton')
    const subtitleSkeleton = screen.getByTestId(
      'workout-card-subtitle-skeleton'
    )

    expect(titleSkeleton).toHaveClass('h-7')
    expect(subtitleSkeleton).toHaveClass('h-5')
  })

  it('should be visually hidden from screen readers content', () => {
    render(<WorkoutCardSkeleton />)

    const titleSkeleton = screen.getByTestId('workout-card-title-skeleton')
    const subtitleSkeleton = screen.getByTestId(
      'workout-card-subtitle-skeleton'
    )

    // Skeleton elements should not have text content
    expect(titleSkeleton).toHaveTextContent('')
    expect(subtitleSkeleton).toHaveTextContent('')
  })
})
