/**
 * Analytics reporting for program performance
 */

import type { PerformanceMetrics } from './constants'

type WindowWithGtag = Window & {
  gtag?: (
    command: string,
    eventName: string,
    parameters: Record<string, unknown>
  ) => void
}

export function sendMetricsToAnalytics(metrics: PerformanceMetrics): void {
  if (process.env.NODE_ENV !== 'production' || typeof window === 'undefined') {
    return
  }

  if (!('gtag' in window)) {
    return
  }

  const windowWithGtag = window as WindowWithGtag
  const { gtag } = windowWithGtag

  if (!gtag) {
    return
  }

  if (metrics.pageLoad > 0) {
    gtag('event', 'timing_complete', {
      name: 'program_page_load',
      value: Math.round(metrics.pageLoad),
      event_category: 'performance',
    })
  }

  if (metrics.dataFetch > 0) {
    gtag('event', 'timing_complete', {
      name: 'program_data_fetch',
      value: Math.round(metrics.dataFetch),
      event_category: 'performance',
    })
  }

  // Report memory usage
  if (metrics.memory) {
    gtag('event', 'memory_usage', {
      value: metrics.memory.percentage,
      event_category: 'performance',
      event_label: 'program_page',
    })
  }
}
