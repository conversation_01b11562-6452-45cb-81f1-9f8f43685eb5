import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act, render } from '@testing-library/react'
import { NavigationProvider, useNavigation } from './NavigationContext'
import React from 'react'

// Mock Next.js router
const mockPush = vi.fn()
const mockBack = vi.fn()

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    back: mockBack,
  }),
}))

describe('NavigationContext', () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <NavigationProvider>{children}</NavigationProvider>
  )

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('provides initial navigation state', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })

    expect(result.current.title).toBe('')
    expect(result.current.canGoBack).toBe(false)
    expect(result.current.navigationStack).toEqual([])
  })

  it('allows setting title', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })

    act(() => {
      result.current.setTitle('Test Title')
    })

    expect(result.current.title).toBe('Test Title')
  })

  it('manages navigation stack', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })

    act(() => {
      result.current.pushRoute('/program')
    })

    expect(result.current.navigationStack).toEqual(['/program'])
    expect(result.current.canGoBack).toBe(true)

    act(() => {
      result.current.pushRoute('/workout')
    })

    expect(result.current.navigationStack).toEqual(['/program', '/workout'])

    act(() => {
      result.current.popRoute()
    })

    expect(result.current.navigationStack).toEqual(['/program'])
  })

  it('handles goBack correctly', async () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })

    // Add routes to stack
    act(() => {
      result.current.pushRoute('/program')
      result.current.pushRoute('/workout')
    })

    // Go back
    act(() => {
      result.current.goBack()
    })

    // Should navigate to the previous route
    expect(mockPush).toHaveBeenCalledWith('/workout')
    // Should pop the stack
    expect(result.current.navigationStack).toEqual(['/program'])
  })

  it('does not go back when stack is empty', () => {
    const { result } = renderHook(() => useNavigation(), { wrapper })

    expect(result.current.canGoBack).toBe(false)

    act(() => {
      result.current.goBack()
    })

    // Should remain unchanged
    expect(result.current.navigationStack).toEqual([])
  })

  it('throws error when used outside provider', () => {
    expect(() => {
      renderHook(() => useNavigation())
    }).toThrow('useNavigation must be used within NavigationProvider')
  })

  it('maintains shared state for multiple consumers', () => {
    function TestComponent() {
      const nav1 = useNavigation()
      const nav2 = useNavigation()
      return (
        <div>
          <span data-testid="title1">{nav1.title}</span>
          <span data-testid="title2">{nav2.title}</span>
          <button onClick={() => nav1.setTitle('Title 1')}>Set Title</button>
        </div>
      )
    }

    const { getByTestId, getByText } = render(
      <NavigationProvider>
        <TestComponent />
      </NavigationProvider>
    )

    // Initial state
    expect(getByTestId('title1')).toHaveTextContent('')
    expect(getByTestId('title2')).toHaveTextContent('')

    // Set title through first hook
    act(() => {
      getByText('Set Title').click()
    })

    // Both hooks should see the same state
    expect(getByTestId('title1')).toHaveTextContent('Title 1')
    expect(getByTestId('title2')).toHaveTextContent('Title 1')
  })
})
