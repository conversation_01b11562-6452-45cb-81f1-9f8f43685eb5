import React from 'react'
import { AnimatedCounter } from '@/components/AnimatedCounter'

export interface StatCardProps {
  /** The numeric value to display */
  value: number | null | undefined
  /** Label for the stat */
  label: string
  /** Icon to display with the stat */
  icon: React.ReactNode
  /** Loading state */
  isLoading?: boolean
  /** Show shimmer effect */
  showShimmer?: boolean
  /** Shimmer animation offset */
  shimmerOffset?: number
  /** Additional CSS classes */
  className?: string
}

export function StatCard({
  value,
  label,
  icon,
  isLoading = false,
  showShimmer = false,
  shimmerOffset = 0,
  className = '',
}: StatCardProps) {
  return (
    <div
      data-testid="stat-card"
      className={`
        bg-bg-secondary
        bg-opacity-95
        border border-brand-primary/20
        rounded-theme
        p-6
        shadow-theme-md
        transition-all duration-200
        hover:scale-[1.02]
        hover:shadow-theme-lg
        min-h-[140px]
        flex items-center justify-center
        ${className}
      `}
      role="article"
      aria-label={`${label} statistic`}
    >
      <AnimatedCounter
        targetValue={value}
        label={label}
        icon={icon}
        size="large"
        isLoading={isLoading}
        showShimmer={showShimmer}
        shimmerOffset={shimmerOffset}
      />
    </div>
  )
}
