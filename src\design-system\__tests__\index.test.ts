import { describe, it, expect } from 'vitest'
import * as DesignSystem from '../index'

describe('Design System Exports', () => {
  it('should export tokens', () => {
    expect(DesignSystem.tokens).toBeDefined()
  })

  it('should export ThemeProvider', () => {
    expect(DesignSystem.ThemeProvider).toBeDefined()
  })

  it('should export useTheme hook', () => {
    expect(DesignSystem.useTheme).toBeDefined()
  })

  it('should export design system version', () => {
    expect(DesignSystem.VERSION).toBeDefined()
    expect(typeof DesignSystem.VERSION).toBe('string')
  })

  it('should have proper TypeScript types', () => {
    // This test ensures our exports are properly typed
    const theme: DesignSystem.Theme | undefined = undefined
    const themeVariant: DesignSystem.ThemeVariant | undefined = undefined

    // TypeScript will fail to compile if these types don't exist
    expect(theme).toBeUndefined()
    expect(themeVariant).toBeUndefined()
  })
})
