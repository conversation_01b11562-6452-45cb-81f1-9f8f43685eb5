I'll analyze the Dr. Muscle mobile app to understand the save workout process and list all API calls in order. Let me start by searching for the workout save functionality.

Now let me find the specific code implementations and local handling for these API calls.

Now let me find more specific details about the models and any additional API calls during the workout save process.

Now let me search for any additional API calls related to the workout completion flow, such as light session updates or exercise history updates.

Based on my comprehensive analysis of the Dr. Muscle codebase, here's the complete API workflow for saving a workout:

## Complete Workout Save API Workflow

### 1. **During Exercise Execution**

Each completed set is logged individually:

- **Endpoint**: `/api/Exercise/AddWorkoutLogSerieListNew`
- **Method**: POST
- **Payload**: `List<WorkoutLogSerieModel>`

```csharp
// Local handling before API call
List<WorkoutLogSerieModel> serieModelList = new List<WorkoutLogSerieModel>();
foreach (WorkoutLogSerieModelRef l in model)
{
    if (l.IsFinished)
    {
        serieModelList.Add(new WorkoutLogSerieModel
        {
            Exercice = exercise,
            Reps = l.Reps,
            Weight = l.Weight,
            RIR = l.RIR,
            IsWarmups = l.IsWarmup,
            LogDate = DateTime.UtcNow,
            // ... other properties
        });
    }
}
await DrMuscleRestClient.Instance.AddWorkoutLogSerieListwithoutLoader(serieModelList);
```

### 2. **After Each Exercise Completion**

Update light session status:

- **Endpoint**: `/api/Exercise/AddUpdateExerciseUserLightSessionWithBodyPart`
- **Method**: POST
- **Payload**: `LightSessionModel`

```csharp
await DrMuscleRestClient.Instance.AddUpdateExerciseUserLightSessionWithBodyPart(new LightSessionModel
{
    ExerciseId = model.Id,
    IsLightSession = CurrentLog.Instance.RecommendationsByExercise[model.Id].IsLightSession,
    MaxChallengeDate = maxDate,
    IsQuickMode = LocalDBManager.Instance.GetDBSetting("QuickMode").Value == "false" ? false : true,
    BodypartId = (long)model.BodyPartId
});
```

### 3. **Workout Completion Save**

Primary save method:

- **Endpoint**: `/api/Workout/SaveWorkoutV3Pro`
- **Method**: POST
- **Payload**: `SaveWorkoutModel`

```csharp
var successWorkoutLog = await DrMuscleRestClient.Instance.SaveWorkoutV3(new SaveWorkoutModel
{
    WorkoutId = CurrentLog.Instance.CurrentWorkoutTemplate.Id,
    WorkoutTemplateId = CurrentLog.Instance.CurrentWorkoutTemplateGroup.Id
});
```

Alternative with program info:

- **Endpoint**: `/api/Workout/SaveGetWorkoutInfoPro`
- **Response**: `GetUserProgramInfoResponseModel` (includes next workout)

### 4. **Post-Save Updates**

After successful save, fetch updated stats:

#### a. Get Updated Program Info

- **Endpoint**: `/api/UserWorkoutProgram/GetTimeZone`
- **Method**: POST
- **Response**: Next workout template, program progression

#### b. Get Workout Statistics

- **Endpoint**: `/api/LogAverage/GetWithSets`
- **Method**: GET
- **Response**: Comprehensive stats including:
  - Recent sets history
  - Workout count and streaks
  - Exercise averages and PRs
  - Total weight lifted

### 5. **Local Data Management**

```csharp
// Clean up local exercise data after save
foreach (ExerciceModel exerciceModel in CurrentLog.Instance.CurrentWorkoutTemplate.Exercises)
{
    if (CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.ContainsKey(exerciceModel.Id))
        CurrentLog.Instance.WorkoutLogSeriesByExerciseRef.Remove(exerciceModel.Id);

    LocalDBManager.Instance.SetDBSetting($"workout{exerciceModel.Id}", "false");
}

// Update last workout info
LocalDBManager.Instance.SetDBSetting("last_workout_label", CurrentLog.Instance.CurrentWorkoutTemplate.Label);
LocalDBManager.Instance.SetDBSetting("lastDoneProgram", CurrentLog.Instance.CurrentWorkoutTemplate.Id.ToString());
```

### Complete API Call Sequence:

1. `/api/Exercise/AddWorkoutLogSerieListNew` - Per exercise set logging
2. `/api/Exercise/AddUpdateExerciseUserLightSessionWithBodyPart` - Per exercise session update
3. `/api/Workout/SaveWorkoutV3Pro` or `/api/Workout/SaveGetWorkoutInfoPro` - Final workout save
4. `/api/UserWorkoutProgram/GetTimeZone` - Get updated program info
5. `/api/LogAverage/GetWithSets` - Get comprehensive workout statistics

This workflow ensures all workout data is properly saved and the app stays synchronized with the latest user progress and program information.
