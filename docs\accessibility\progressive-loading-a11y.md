# Progressive Loading Accessibility Guide

This document outlines the accessibility features implemented for the progressive loading system in the Dr. Muscle X app.

## Overview

The progressive loading system ensures that users with assistive technologies receive appropriate feedback about loading states, content updates, and dynamic changes.

## Key Components

### 1. AnimatedCounter

**Features:**

- **ARIA Live Regions**: Announces value changes to screen readers
- **Loading States**: Proper `role="status"` and `aria-busy` attributes
- **Reduced Motion**: Respects `prefers-reduced-motion` media query
- **Label Association**: Unique IDs for label elements

```tsx
<div role="group" aria-label={label}>
  <div aria-live="polite" aria-atomic="true" aria-label={`${label}: ${value}`}>
    {formatNumber(displayValue)}
  </div>
</div>
```

### 2. ShimmerEffect

**Features:**

- **Status Role**: Indicates loading state to screen readers
- **Screen Reader Text**: Hidden text announces loading status
- **Reduced Motion**: Falls back to static opacity for users with motion preferences

```tsx
<div role="status" aria-busy="true" aria-label="Loading">
  <span className="sr-only">Loading</span>
  <div aria-hidden="true">{/* Visual shimmer effect */}</div>
</div>
```

### 3. ScreenReaderAnnouncer

**Purpose**: Provides dynamic announcements for state changes

**Features:**

- **Live Regions**: Configurable `polite` or `assertive` announcements
- **Auto-clear**: Messages clear after configurable delay
- **Hook Integration**: Easy integration with React components

```tsx
const { announce } = useScreenReaderAnnouncer()
announce('Program data loaded successfully')
```

### 4. Program Page Integration

**Features:**

- **Main Landmark**: `role="main"` for primary content
- **Navigation Landmark**: `role="navigation"` for action buttons
- **Loading Announcements**: Announces data loading and refresh status
- **Focus Management**: Proper focus indicators and keyboard navigation

## Best Practices

### 1. Loading States

Always provide appropriate feedback for loading states:

```tsx
<div role="status" aria-busy="true" aria-label="Loading user statistics">
  {/* Loading content */}
</div>
```

### 2. Dynamic Updates

Use live regions for important updates:

```tsx
<div aria-live="polite" aria-atomic="true">
  {updatedContent}
</div>
```

### 3. Reduced Motion

Check for motion preferences:

```tsx
const prefersReducedMotion = window.matchMedia(
  '(prefers-reduced-motion: reduce)'
).matches

if (prefersReducedMotion) {
  // Skip animations
}
```

### 4. Hidden Decorative Elements

Hide purely decorative elements:

```tsx
<div aria-hidden="true" className="decorative-shimmer" />
```

## Testing

### Screen Reader Testing

Test with common screen readers:

- **NVDA** (Windows)
- **JAWS** (Windows)
- **VoiceOver** (macOS/iOS)
- **TalkBack** (Android)

### Automated Testing

Run accessibility tests:

```bash
npm test -- src/components/__tests__/*.accessibility.test.tsx
```

### Manual Testing Checklist

- [ ] All loading states announce properly
- [ ] Dynamic content updates are announced
- [ ] Keyboard navigation works throughout
- [ ] Focus indicators are visible
- [ ] Reduced motion preference is respected
- [ ] Color contrast meets WCAG standards

## Common Patterns

### Progressive Enhancement

Start with accessible baseline, enhance with visual effects:

```tsx
// Base: Accessible loading text
<span role="status">Loading...</span>

// Enhanced: Add visual shimmer
<ShimmerOverlay show={isLoading} />
```

### Announcement Timing

Debounce rapid updates to avoid announcement spam:

```tsx
const debouncedAnnounce = useDebounce(announce, 1000)
```

## Resources

- [ARIA Live Regions](https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/ARIA_Live_Regions)
- [WCAG Loading States](https://www.w3.org/WAI/WCAG21/Understanding/status-messages.html)
- [Inclusive Components](https://inclusive-components.design/)
