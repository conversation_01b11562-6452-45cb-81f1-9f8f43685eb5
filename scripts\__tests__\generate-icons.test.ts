import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import fs from 'fs'
import path from 'path'
import { generateIcons } from '../generate-icons.js'

// Mock sharp
vi.mock('sharp', () => {
  const mockSharp = {
    resize: vi.fn().mockReturnThis(),
    extend: vi.fn().mockReturnThis(),
    toFile: vi.fn().mockResolvedValue({ format: 'png', width: 192, height: 192 }),
    metadata: vi.fn().mockResolvedValue({ width: 1024, height: 1024, format: 'png' }),
  }
  return {
    default: vi.fn(() => mockSharp),
  }
})

// Mock fs
vi.mock('fs', () => ({
  default: {
    existsSync: vi.fn(),
    mkdirSync: vi.fn(),
    readFileSync: vi.fn(),
  },
}))

describe('Icon Generation Script', () => {
  const mockFs = fs as { default: { existsSync: ReturnType<typeof vi.fn>, mkdirSync: ReturnType<typeof vi.fn>, readFileSync: ReturnType<typeof vi.fn> } }
  const testOutputDir = path.join(process.cwd(), 'public', 'icons')
  const testSourcePath = path.join(process.cwd(), 'public', 'app-icon.png')

  beforeEach(() => {
    vi.clearAllMocks()
    mockFs.existsSync.mockReturnValue(true)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should generate all required icon sizes', async () => {
    const expectedSizes = [72, 96, 128, 144, 152, 192, 384, 512]
    
    const result = await generateIcons(testSourcePath, testOutputDir)
    
    expect(result.generated).toHaveLength(expectedSizes.length * 2) // regular + maskable
    
    // Check that each size was generated
    expectedSizes.forEach(size => {
      expect(result.generated).toContainEqual(
        expect.objectContaining({
          size,
          type: 'regular',
          path: expect.stringContaining(`icon-${size}x${size}.png`),
        })
      )
      expect(result.generated).toContainEqual(
        expect.objectContaining({
          size,
          type: 'maskable',
          path: expect.stringContaining(`icon-maskable-${size}x${size}.png`),
        })
      )
    })
  })

  it('should create output directory if it does not exist', async () => {
    mockFs.existsSync.mockImplementation((path: string) => {
      return path !== testOutputDir
    })

    await generateIcons(testSourcePath, testOutputDir)

    expect(mockFs.mkdirSync).toHaveBeenCalledWith(testOutputDir, { recursive: true })
  })

  it('should throw error if source file does not exist', async () => {
    mockFs.existsSync.mockImplementation((path: string) => {
      return path !== testSourcePath
    })

    await expect(generateIcons(testSourcePath, testOutputDir)).rejects.toThrow(
      'Source icon file not found'
    )
  })

  it('should add padding for maskable icons', async () => {
    const sharp = await import('sharp')
    const mockSharpInstance = sharp.default()

    await generateIcons(testSourcePath, testOutputDir)

    // Check that extend was called for maskable icons
    const extendCalls = (mockSharpInstance.extend as ReturnType<typeof vi.fn>).mock.calls
    expect(extendCalls.length).toBeGreaterThan(0)
    
    // Verify padding is 20% on each side
    extendCalls.forEach((call: unknown[]) => {
      const [options] = call
      expect(options).toHaveProperty('top')
      expect(options).toHaveProperty('bottom')
      expect(options).toHaveProperty('left')
      expect(options).toHaveProperty('right')
      expect(options).toHaveProperty('background')
    })
  })

  it('should maintain aspect ratio when resizing', async () => {
    const sharp = await import('sharp')
    const mockSharpInstance = sharp.default()

    await generateIcons(testSourcePath, testOutputDir)

    // Check resize was called with correct options
    const resizeCalls = (mockSharpInstance.resize as ReturnType<typeof vi.fn>).mock.calls
    resizeCalls.forEach((call: unknown[]) => {
      const [width, height, options] = call
      expect(width).toBe(height) // Square icons
      expect(options).toEqual({ fit: 'contain', background: { r: 255, g: 255, b: 255, alpha: 0 } })
    })
  })

  it('should generate valid PNG files', async () => {
    const sharp = await import('sharp')
    const mockSharpInstance = sharp.default()

    await generateIcons(testSourcePath, testOutputDir)

    // Verify toFile was called for each icon
    const toFileCalls = (mockSharpInstance.toFile as ReturnType<typeof vi.fn>).mock.calls
    expect(toFileCalls.length).toBe(16) // 8 sizes × 2 types

    // Check file paths are correct
    toFileCalls.forEach((call: unknown[]) => {
      const [filePath] = call
      expect(filePath).toMatch(/icon-(maskable-)?\d+x\d+\.png$/)
    })
  })

  it('should return summary of generated icons', async () => {
    const result = await generateIcons(testSourcePath, testOutputDir)

    expect(result).toHaveProperty('source', testSourcePath)
    expect(result).toHaveProperty('outputDir', testOutputDir)
    expect(result).toHaveProperty('generated')
    expect(result).toHaveProperty('totalSize')
    expect(result.generated).toBeInstanceOf(Array)
  })

  it('should use white background for padding', async () => {
    const sharp = await import('sharp')
    const mockSharpInstance = sharp.default()

    await generateIcons(testSourcePath, testOutputDir)

    // Check that white background is used for maskable icons
    const extendCalls = (mockSharpInstance.extend as ReturnType<typeof vi.fn>).mock.calls
    extendCalls.forEach((call: unknown[]) => {
      const [options] = call as [{ background: { r: number, g: number, b: number, alpha: number } }]
      expect(options.background).toEqual({ r: 255, g: 255, b: 255, alpha: 1 })
    })
  })

  it('should handle sharp errors gracefully', async () => {
    const sharp = await import('sharp')
    const mockSharpInstance = sharp.default()
    (mockSharpInstance.toFile as ReturnType<typeof vi.fn>).mockRejectedValueOnce(new Error('Sharp processing failed'))

    await expect(generateIcons(testSourcePath, testOutputDir)).rejects.toThrow(
      'Failed to generate icon'
    )
  })
})