import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import DesignSystemPage from '../page'

// Mock Next.js font
vi.mock('next/font/google', () => ({
  Inter: () => ({ className: 'mocked-inter-class' }),
  Space_Grotesk: () => ({ className: 'mocked-space-grotesk-class' }),
  Playfair_Display: () => ({ className: 'mocked-playfair-class' }),
  Bebas_Neue: () => ({ className: 'mocked-bebas-class' }),
}))

describe('Theme Switching', () => {
  beforeEach(() => {
    // Reset document state
    document.documentElement.removeAttribute('data-theme')
    localStorage.clear()

    // Mock CSS.supports to always return true
    // @ts-expect-error Mock CSS for testing
    global.CSS = { supports: vi.fn(() => true) }

    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    }
    global.localStorage = localStorageMock as any
  })

  it('should update data-theme attribute when switching themes', async () => {
    render(<DesignSystemPage />)

    // Initial theme should be subtle-depth
    await waitFor(() => {
      expect(document.documentElement.dataset.theme).toBe('subtle-depth')
    })

    // Click on flat-bold theme
    const flatBoldButton = screen.getByText('Flat Bold')
    fireEvent.click(flatBoldButton)

    await waitFor(() => {
      expect(document.documentElement.dataset.theme).toBe('flat-bold')
    })

    // Click on glassmorphism theme
    const glassmorphismButton = screen.getByText('Glassmorphism')
    fireEvent.click(glassmorphismButton)

    await waitFor(() => {
      expect(document.documentElement.dataset.theme).toBe('glassmorphism')
    })

    // Click on ultra-minimal theme
    const ultraMinimalButton = screen.getByText('Ultra-Minimal')
    fireEvent.click(ultraMinimalButton)

    await waitFor(() => {
      expect(document.documentElement.dataset.theme).toBe('ultra-minimal')
    })
  })

  it('should persist theme selection in localStorage', async () => {
    render(<DesignSystemPage />)

    // Click on flat-bold theme
    const flatBoldButton = screen.getByText('Flat Bold')
    fireEvent.click(flatBoldButton)

    await waitFor(() => {
      expect(localStorage.setItem).toHaveBeenCalledWith(
        'dr-muscle-x-theme',
        'flat-bold'
      )
    })
  })

  it('should apply correct CSS classes based on theme', async () => {
    render(<DesignSystemPage />)

    // Check initial theme button is highlighted
    const subtleDepthButton = screen.getByText('Subtle Depth')
    expect(subtleDepthButton).toHaveClass('bg-brand-primary')

    // Click on flat-bold theme
    const flatBoldButton = screen.getByText('Flat Bold')
    fireEvent.click(flatBoldButton)

    await waitFor(() => {
      // Flat Bold button should now be highlighted
      expect(flatBoldButton).toHaveClass('bg-brand-primary')
      // Subtle Depth button should not be highlighted
      expect(subtleDepthButton).toHaveClass('bg-bg-tertiary')
    })
  })

  it('should update CSS variables when theme changes', async () => {
    render(<DesignSystemPage />)

    // Wait for initial theme
    await waitFor(() => {
      expect(document.documentElement.dataset.theme).toBe('subtle-depth')
    })

    // Click on flat-bold theme
    const flatBoldButton = screen.getByText('Flat Bold')
    fireEvent.click(flatBoldButton)

    await waitFor(() => {
      expect(document.documentElement.dataset.theme).toBe('flat-bold')
    })

    // In a real browser, CSS variables would update based on data-theme
    // Since we're in jsdom, we can at least verify the data-theme attribute changes
    expect(document.documentElement.dataset.theme).toBe('flat-bold')
  })
})
