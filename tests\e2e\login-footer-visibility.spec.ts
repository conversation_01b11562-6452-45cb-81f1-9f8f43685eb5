import { test, expect } from '@playwright/test'

test.describe('Login Page Footer Visibility', () => {
  test('should have readable footer text with proper theme colors', async ({
    page,
  }) => {
    // Navigate to login page
    await page.goto('/login')

    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle')

    // Check that the footer text is visible
    const footerText = page.locator('text="Don\'t have an account?"')
    await expect(footerText).toBeVisible()

    // Check that the sign up link is visible
    const signUpLink = page.getByRole('link', { name: 'Sign up' })
    await expect(signUpLink).toBeVisible()

    // Get the footer paragraph element
    const footerParagraph = page.locator(
      'p:has-text("Don\'t have an account?")'
    )

    // Check that the footer uses theme-aware text color
    await expect(footerParagraph).toHaveClass(/text-text-secondary/)

    // Check that the sign up link uses theme-aware brand color
    await expect(signUpLink).toHaveClass(/text-brand-primary/)

    // Check color contrast by getting computed styles
    const footerColor = await footerParagraph.evaluate((el) => {
      return window.getComputedStyle(el).color
    })

    const signUpColor = await signUpLink.evaluate((el) => {
      return window.getComputedStyle(el).color
    })

    const backgroundColor = await page.locator('body').evaluate((el) => {
      return window.getComputedStyle(el).backgroundColor
    })

    // Log colors for debugging (will show in test output)
    console.log('Background color:', backgroundColor)
    console.log('Footer text color:', footerColor)
    console.log('Sign up link color:', signUpColor)

    // Verify colors are not the same (ensuring contrast)
    expect(footerColor).not.toBe(backgroundColor)
    expect(signUpColor).not.toBe(backgroundColor)

    // Test hover state on sign up link
    await signUpLink.hover()
    await expect(signUpLink).toHaveClass(/hover:text-brand-secondary/)

    // Take a screenshot for visual verification
    await page.screenshot({
      path: 'tests/e2e/screenshots/login-footer-visibility.png',
      fullPage: true,
    })
  })

  test('should maintain visibility in mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Navigate to login page
    await page.goto('/login')

    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle')

    // Check that footer elements are still visible on mobile
    const footerText = page.locator('text="Don\'t have an account?"')
    const signUpLink = page.getByRole('link', { name: 'Sign up' })

    await expect(footerText).toBeVisible()
    await expect(signUpLink).toBeVisible()

    // Verify the link is clickable
    await expect(signUpLink).toBeEnabled()
    await expect(signUpLink).toHaveAttribute('href', '/register')
  })
})
