/**
 * Global type declarations for OAuth SDKs
 */

/**
 * Google Sign-In SDK types
 */
declare namespace google {
  namespace accounts {
    namespace id {
      interface InitializeRequest {
        client_id: string
        callback: (response: CredentialResponse) => void
        auto_select?: boolean
        cancel_on_tap_outside?: boolean
        context?: 'signin' | 'signup' | 'use'
        prompt_parent_id?: string
        state_cookie_domain?: string
        ux_mode?: 'popup' | 'redirect'
        nonce?: string
      }

      interface CredentialResponse {
        credential: string
        select_by:
          | 'auto'
          | 'user'
          | 'user_1tap'
          | 'user_2tap'
          | 'btn'
          | 'btn_confirm'
          | 'btn_add_session'
          | 'btn_confirm_add_session'
        clientId?: string
      }

      interface GsiButtonConfiguration {
        type?: 'standard' | 'icon'
        theme?: 'outline' | 'filled_blue' | 'filled_black'
        size?: 'large' | 'medium' | 'small'
        text?: 'signin_with' | 'signup_with' | 'continue_with' | 'signin'
        shape?: 'rectangular' | 'pill' | 'circle' | 'square'
        logo_alignment?: 'left' | 'center'
        width?: number | string
        locale?: string
      }

      function initialize(config: InitializeRequest): void
      function prompt(
        callback?: (notification: PromptNotification) => void
      ): void
      function renderButton(
        parent: HTMLElement,
        options: GsiButtonConfiguration
      ): void
      function disableAutoSelect(): void
      function cancel(): void
      function revoke(email: string, callback?: () => void): void

      interface PromptNotification {
        isNotDisplayed(): boolean
        isSkippedMoment(): boolean
        isDismissedMoment(): boolean
        getNotDisplayedReason():
          | 'browser_not_supported'
          | 'invalid_client'
          | 'missing_client_id'
          | 'opt_out_or_no_session'
          | 'secure_http_required'
          | 'suppressed_by_user'
          | 'unregistered_origin'
          | 'unknown_reason'
        getSkippedReason():
          | 'auto_cancel'
          | 'user_cancel'
          | 'tap_outside'
          | 'issuing_failed'
        getDismissedReason():
          | 'credential_returned'
          | 'cancel_called'
          | 'flow_restarted'
      }
    }
  }
}

/**
 * Apple Sign-In SDK types
 */
declare namespace AppleID {
  namespace auth {
    interface ClientConfigObject {
      clientId: string
      scope: string
      redirectURI: string
      state?: string
      nonce?: string
      usePopup?: boolean
    }

    interface SignInResponse {
      authorization: {
        code: string
        id_token: string
        state?: string
      }
      user?: {
        email: string
        name: {
          firstName: string
          lastName: string
        }
      }
    }

    interface SignInError {
      error: string
    }

    function init(config: ClientConfigObject): void
    function signIn(config?: ClientConfigObject): Promise<SignInResponse>
  }
}

/**
 * Extend Window interface with OAuth SDK globals
 */
interface Window {
  google?: typeof google
  AppleID?: typeof AppleID
}
