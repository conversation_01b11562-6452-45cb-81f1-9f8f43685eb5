'use client'

interface GoldLoadingTransitionProps {
  message?: string
  exerciseName?: string
}

export function GoldLoadingTransition({
  message = 'Loading...',
  exerciseName,
}: GoldLoadingTransitionProps) {
  const displayMessage = exerciseName ? `Loading ${exerciseName}...` : message

  return (
    <div className="flex flex-col items-center justify-center min-h-[100dvh] p-4 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary">
      {/* Gold accent background */}
      <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/5 via-amber-500/10 to-yellow-600/5" />

      {/* Content */}
      <div className="relative z-10 text-center">
        {/* Gold loading spinner */}
        <div className="relative mb-6">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-yellow-400/20 border-t-yellow-500 mx-auto" />
          {/* Inner glow effect */}
          <div className="absolute inset-0 animate-pulse rounded-full h-16 w-16 border-2 border-yellow-400/40 mx-auto" />
        </div>

        {/* Loading message */}
        <div className="space-y-2">
          <p className="text-xl font-semibold text-transparent bg-gradient-to-r from-yellow-500 via-amber-500 to-yellow-600 bg-clip-text animate-pulse">
            {displayMessage}
          </p>
          <p className="text-sm text-text-secondary/80">
            Preparing your workout experience
          </p>
        </div>

        {/* Animated dots */}
        <div className="flex justify-center space-x-1 mt-4">
          <div
            className="w-2 h-2 bg-yellow-500 rounded-full animate-bounce"
            style={{ animationDelay: '0ms' }}
          />
          <div
            className="w-2 h-2 bg-amber-500 rounded-full animate-bounce"
            style={{ animationDelay: '150ms' }}
          />
          <div
            className="w-2 h-2 bg-yellow-600 rounded-full animate-bounce"
            style={{ animationDelay: '300ms' }}
          />
        </div>
      </div>
    </div>
  )
}
