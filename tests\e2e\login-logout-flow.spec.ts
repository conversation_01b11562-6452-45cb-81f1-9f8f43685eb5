import { test, expect } from '@playwright/test'

test.describe('Login/Logout Flow @critical', () => {
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456'
  }

  test.beforeEach(async ({ page }) => {
    // Clear any existing auth state
    await page.context().clearCookies()
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })
    await page.goto('/login')
  })

  test('should complete full login flow successfully @critical', async ({ page }) => {
    // Verify login page elements
    await expect(page).toHaveTitle(/Dr\. Muscle/)
    await expect(page.getByLabel('Email')).toBeVisible()
    await expect(page.getByLabel('Password')).toBeVisible()
    await expect(page.getByRole('button', { name: /log in/i })).toBeVisible()

    // Fill login form
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)

    // Submit login
    const loginPromise = page.waitForResponse(response =>
      response.url().includes('/token') && response.status() === 200
    )
    await page.getByRole('button', { name: /log in/i }).click()

    // Wait for successful login
    const loginResponse = await loginPromise
    expect(loginResponse.status()).toBe(200)

    // Verify redirect to program page
    await page.waitForURL('/program', { timeout: 10000 })
    await expect(page).toHaveURL('/program')

    // Verify user is logged in
    await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible()
    
    // Verify auth token is stored
    const authStore = await page.evaluate(() => localStorage.getItem('auth-storage'))
    expect(authStore).toBeTruthy()
    const authData = JSON.parse(authStore!)
    expect(authData.state.token).toBeTruthy()
    expect(authData.state.user).toBeTruthy()
  })

  test('should handle invalid credentials @critical', async ({ page }) => {
    // Fill with invalid credentials
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('wrongpassword')

    // Submit login
    await page.getByRole('button', { name: /log in/i }).click()

    // Should show error message
    await expect(page.locator('[role="alert"]')).toBeVisible()
    await expect(page.locator('[role="alert"]')).toContainText(/invalid|incorrect|failed/i)

    // Should remain on login page
    await expect(page).toHaveURL('/login')
  })

  test('should complete logout flow successfully @critical', async ({ page }) => {
    // First login
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)
    await page.getByRole('button', { name: /log in/i }).click()
    await page.waitForURL('/program')

    // Click user avatar to open menu
    await page.locator('[data-testid="user-avatar"]').click()
    
    // Click logout
    await page.getByRole('button', { name: /log out|sign out/i }).click()

    // Verify redirect to login page
    await page.waitForURL('/login')
    await expect(page).toHaveURL('/login')

    // Verify auth state is cleared
    const authStore = await page.evaluate(() => localStorage.getItem('auth-storage'))
    if (authStore) {
      const authData = JSON.parse(authStore)
      expect(authData.state.token).toBeFalsy()
      expect(authData.state.user).toBeFalsy()
    }

    // Verify user cannot access protected routes
    await page.goto('/program')
    await expect(page).toHaveURL('/login')
  })

  test('should persist login across page refreshes @critical', async ({ page }) => {
    // Login
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)
    await page.getByRole('button', { name: /log in/i }).click()
    await page.waitForURL('/program')

    // Refresh page
    await page.reload()

    // Should still be logged in
    await expect(page).toHaveURL('/program')
    await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible()
  })

  test('should handle token expiration gracefully @critical', async ({ page }) => {
    // Login
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)
    await page.getByRole('button', { name: /log in/i }).click()
    await page.waitForURL('/program')

    // Simulate expired token by modifying localStorage
    await page.evaluate(() => {
      const authStore = localStorage.getItem('auth-storage')
      if (authStore) {
        const authData = JSON.parse(authStore)
        authData.state.token = 'expired-token'
        localStorage.setItem('auth-storage', JSON.stringify(authData))
      }
    })

    // Try to access protected content
    await page.goto('/workout')

    // Should redirect to login
    await expect(page).toHaveURL('/login')
  })

  test('should handle network errors during login @critical', async ({ page }) => {
    // Intercept login request and fail it
    await page.route('**/token', route => route.abort('failed'))

    // Try to login
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)
    await page.getByRole('button', { name: /log in/i }).click()

    // Should show network error
    await expect(page.locator('[role="alert"]')).toBeVisible()
    await expect(page.locator('[role="alert"]')).toContainText(/network|connection|failed/i)

    // Should remain on login page
    await expect(page).toHaveURL('/login')
  })

  test('should validate email format @critical', async ({ page }) => {
    // Try invalid email format
    await page.getByLabel('Email').fill('notanemail')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: /log in/i }).click()

    // Should show validation error
    const emailInput = page.getByLabel('Email')
    await expect(emailInput).toHaveAttribute('aria-invalid', 'true')
  })

  test('should require both email and password @critical', async ({ page }) => {
    // Try to submit empty form
    await page.getByRole('button', { name: /log in/i }).click()

    // Should show required field errors
    const emailInput = page.getByLabel('Email')
    const passwordInput = page.getByLabel('Password')
    
    await expect(emailInput).toHaveAttribute('required', '')
    await expect(passwordInput).toHaveAttribute('required', '')
  })

  test('should clear form errors on input @critical', async ({ page }) => {
    // Submit with invalid credentials to trigger error
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('wrongpassword')
    await page.getByRole('button', { name: /log in/i }).click()

    // Wait for error
    await expect(page.locator('[role="alert"]')).toBeVisible()

    // Start typing in email field
    await page.getByLabel('Email').fill('<EMAIL>')

    // Error should be cleared
    await expect(page.locator('[role="alert"]')).not.toBeVisible()
  })

  test('should handle rapid login/logout cycles @critical', async ({ page }) => {
    // Perform multiple login/logout cycles
    for (let i = 0; i < 3; i++) {
      // Login
      await page.getByLabel('Email').fill(TEST_USER.email)
      await page.getByLabel('Password').fill(TEST_USER.password)
      await page.getByRole('button', { name: /log in/i }).click()
      await page.waitForURL('/program')

      // Logout
      await page.locator('[data-testid="user-avatar"]').click()
      await page.getByRole('button', { name: /log out|sign out/i }).click()
      await page.waitForURL('/login')
    }

    // Should still work correctly
    await expect(page).toHaveURL('/login')
  })
})