'use client'

import { Logo } from '@/components'

export default function LogoDemoPage() {
  return (
    <div className="min-h-[100dvh] bg-bg-primary p-8">
      <h1 className="font-heading text-3xl font-bold mb-8">
        Logo Component Demo
      </h1>

      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        <div className="bg-bg-secondary p-6 rounded-lg shadow">
          <h2 className="font-heading text-lg font-semibold mb-4">
            Default Logo
          </h2>
          <div className="flex justify-center">
            <Logo />
          </div>
          <p className="text-sm text-text-tertiary mt-4">200x200px (default)</p>
        </div>

        <div className="bg-bg-secondary p-6 rounded-lg shadow">
          <h2 className="font-heading text-lg font-semibold mb-4">
            Small Logo
          </h2>
          <div className="flex justify-center">
            <Logo width={100} height={100} />
          </div>
          <p className="text-sm text-text-tertiary mt-4">100x100px</p>
        </div>

        <div className="bg-bg-secondary p-6 rounded-lg shadow">
          <h2 className="font-heading text-lg font-semibold mb-4">
            Large Logo
          </h2>
          <div className="flex justify-center">
            <Logo width={300} height={300} />
          </div>
          <p className="text-sm text-text-tertiary mt-4">300x300px</p>
        </div>

        <div className="bg-bg-secondary p-6 rounded-lg shadow">
          <h2 className="font-heading text-lg font-semibold mb-4">
            With Custom Class
          </h2>
          <div className="flex justify-center">
            <Logo className="rounded-full shadow-lg" />
          </div>
          <p className="text-sm text-text-tertiary mt-4">Rounded with shadow</p>
        </div>

        <div className="bg-bg-secondary p-6 rounded-lg shadow">
          <h2 className="font-heading text-lg font-semibold mb-4">
            Priority Loading
          </h2>
          <div className="flex justify-center">
            <Logo priority />
          </div>
          <p className="text-sm text-text-tertiary mt-4">Loads immediately</p>
        </div>

        <div className="bg-bg-tertiary p-6 rounded-lg shadow">
          <h2 className="font-heading text-lg font-semibold mb-4 text-text-primary">
            On Dark Background
          </h2>
          <div className="flex justify-center">
            <Logo />
          </div>
          <p className="text-sm text-text-secondary mt-4">Logo on dark</p>
        </div>
      </div>

      <div className="mt-12 bg-bg-secondary p-6 rounded-lg shadow">
        <h2 className="font-heading text-xl font-semibold mb-4">
          Logo Features
        </h2>
        <ul className="space-y-2 text-text-primary">
          <li>✓ Responsive sizing with width/height props</li>
          <li>✓ Blur placeholder for smooth loading</li>
          <li>✓ Fade-in animation on load</li>
          <li>✓ Error handling with graceful fallback</li>
          <li>✓ Priority loading support</li>
          <li>✓ Full accessibility with alt text</li>
          <li>✓ Optimized with Next.js Image component</li>
        </ul>
      </div>
    </div>
  )
}
