import { test, expect } from '@playwright/test'
import { injectAxe, checkA11y, getViolations } from 'axe-playwright'

test.describe('Accessibility Tests', () => {
  test('Homepage meets WCAG standards', async ({ page }) => {
    await page.goto('/')
    await injectAxe(page)
    
    // Check for accessibility violations
    const violations = await getViolations(page, null, {
      detailedReport: true,
      detailedReportOptions: {
        html: true,
      },
    })
    
    // Log violations for debugging
    if (violations.length > 0) {
      console.log('Accessibility violations found:')
      violations.forEach((violation) => {
        console.log(`- ${violation.id}: ${violation.description}`)
        console.log(`  Impact: ${violation.impact}`)
        console.log(`  Affected elements: ${violation.nodes.length}`)
      })
    }
    
    // Assert no critical violations
    const criticalViolations = violations.filter(v => v.impact === 'critical' || v.impact === 'serious')
    expect(criticalViolations).toHaveLength(0)
  })

  test('Login page meets WCAG standards', async ({ page }) => {
    // Navigate to login if available
    await page.goto('/')
    const loginLink = page.getByRole('link', { name: /log in|sign in/i }).or(
      page.getByRole('button', { name: /log in|sign in/i })
    )
    
    if (await loginLink.isVisible()) {
      await loginLink.click()
      await injectAxe(page)
      
      const violations = await getViolations(page)
      const criticalViolations = violations.filter(v => v.impact === 'critical' || v.impact === 'serious')
      expect(criticalViolations).toHaveLength(0)
    }
  })

  test('Color contrast meets WCAG AA standards', async ({ page }) => {
    await page.goto('/')
    await injectAxe(page)
    
    // Check specifically for color contrast
    await checkA11y(page, null, {
      rules: {
        'color-contrast': { enabled: true },
      },
    })
  })

  test('All images have alt text', async ({ page }) => {
    await page.goto('/')
    
    // Get all images
    const images = await page.locator('img').all()
    
    for (const img of images) {
      const altText = await img.getAttribute('alt')
      const src = await img.getAttribute('src')
      
      // Skip decorative images (those with empty alt text)
      if (altText !== '') {
        expect(altText).toBeTruthy()
        console.log(`Image ${src} has alt text: ${altText}`)
      }
    }
  })

  test('Form inputs have labels', async ({ page }) => {
    await page.goto('/')
    
    // Navigate to a form page
    const formLink = page.getByRole('link', { name: /sign up|register|login/i }).or(
      page.getByRole('button', { name: /sign up|register|login/i })
    )
    
    if (await formLink.isVisible()) {
      await formLink.click()
      
      // Check all form inputs
      const inputs = await page.locator('input:not([type="hidden"]), select, textarea').all()
      
      for (const input of inputs) {
        const inputId = await input.getAttribute('id')
        const inputName = await input.getAttribute('name')
        const ariaLabel = await input.getAttribute('aria-label')
        const ariaLabelledBy = await input.getAttribute('aria-labelledby')
        
        // Check if input has associated label
        if (inputId) {
          const label = page.locator(`label[for="${inputId}"]`)
          const hasLabel = await label.isVisible().catch(() => false)
          
          // Input should have either a label, aria-label, or aria-labelledby
          const hasAccessibleName = hasLabel || ariaLabel || ariaLabelledBy
          expect(hasAccessibleName).toBeTruthy()
        }
      }
    }
  })

  test('Interactive elements are keyboard accessible', async ({ page }) => {
    await page.goto('/')
    
    // Tab through interactive elements
    const interactiveElements = await page.locator('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])').all()
    
    // Test first 10 interactive elements
    for (let i = 0; i < Math.min(interactiveElements.length, 10); i++) {
      await page.keyboard.press('Tab')
      
      // Check if element is focused
      const focusedElement = await page.evaluate(() => document.activeElement?.tagName)
      expect(focusedElement).toBeTruthy()
    }
  })

  test('Page has proper heading structure', async ({ page }) => {
    await page.goto('/')
    
    // Check for h1
    const h1Count = await page.locator('h1').count()
    expect(h1Count).toBe(1) // Should have exactly one h1
    
    // Check heading hierarchy
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all()
    let previousLevel = 0
    
    for (const heading of headings) {
      const tagName = await heading.evaluate(el => el.tagName)
      const level = parseInt(tagName.substring(1))
      
      // Heading levels shouldn't skip (e.g., h1 -> h3)
      if (previousLevel > 0) {
        expect(level).toBeLessThanOrEqual(previousLevel + 1)
      }
      previousLevel = level
    }
  })

  test('Focus indicators are visible', async ({ page }) => {
    await page.goto('/')
    
    // Tab to first interactive element
    await page.keyboard.press('Tab')
    
    // Check if focused element has visible focus indicator
    const focusedElement = page.locator(':focus')
    const hasOutline = await focusedElement.evaluate((el) => {
      const styles = window.getComputedStyle(el)
      return styles.outlineWidth !== '0px' || styles.boxShadow !== 'none'
    })
    
    expect(hasOutline).toBeTruthy()
  })

  test('ARIA landmarks are properly used', async ({ page }) => {
    await page.goto('/')
    
    // Check for main landmark
    const main = page.locator('main, [role="main"]')
    await expect(main).toBeVisible()
    
    // Check for navigation landmark
    const nav = page.locator('nav, [role="navigation"]')
    const navCount = await nav.count()
    expect(navCount).toBeGreaterThan(0)
    
    // Check for banner (header)
    const banner = page.locator('header, [role="banner"]')
    const bannerCount = await banner.count()
    expect(bannerCount).toBeLessThanOrEqual(1) // At most one banner
  })

  test('Mobile touch targets meet minimum size', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip()
    }
    
    await page.goto('/')
    
    // Check interactive elements
    const interactiveElements = await page.locator('a, button, input, select, [role="button"]').all()
    
    for (const element of interactiveElements.slice(0, 20)) {
      if (await element.isVisible()) {
        const box = await element.boundingBox()
        if (box) {
          // WCAG 2.1 requires 44x44 CSS pixels for touch targets
          const isLargeEnough = box.width >= 44 && box.height >= 44
          
          if (!isLargeEnough) {
            // Check if element has sufficient spacing from other targets
            const hasSpacing = await element.evaluate((el) => {
              const rect = el.getBoundingClientRect()
              const centerX = rect.left + rect.width / 2
              const centerY = rect.top + rect.height / 2
              
              // Check distance to nearest interactive element
              const allInteractive = document.querySelectorAll('a, button, input, select, [role="button"]')
              let minDistance = Infinity
              
              allInteractive.forEach((other) => {
                if (other !== el) {
                  const otherRect = other.getBoundingClientRect()
                  const otherCenterX = otherRect.left + otherRect.width / 2
                  const otherCenterY = otherRect.top + otherRect.height / 2
                  
                  const distance = Math.sqrt(
                    Math.pow(centerX - otherCenterX, 2) + 
                    Math.pow(centerY - otherCenterY, 2)
                  )
                  
                  minDistance = Math.min(minDistance, distance)
                }
              })
              
              return minDistance >= 44
            })
            
            expect(isLargeEnough || hasSpacing).toBeTruthy()
          }
        }
      }
    }
  })
})