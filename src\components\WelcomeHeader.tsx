import React from 'react'
import { useAuthStore } from '@/stores/authStore'
import { useUserInfo } from '@/hooks/useUserInfo'

export interface WelcomeHeaderProps {
  /** Additional CSS classes */
  className?: string
}

export function WelcomeHeader({ className = '' }: WelcomeHeaderProps) {
  const user = useAuthStore((state) => state.user)
  const { isLoading } = useUserInfo()

  // Get display name - use name, firstName, email, or fallback
  // Email is available immediately after login, name/firstName come from UserInfo API
  const displayName =
    user?.name || user?.firstName || user?.email?.split('@')[0] || 'Champion'

  return (
    <div data-testid="welcome-header" className={`${className}`}>
      <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
        {isLoading ? (
          <span className="inline-flex items-center">
            Welcome back{' '}
            <span className="ml-2 inline-block h-6 w-32 animate-pulse rounded bg-gray-300 dark:bg-gray-700" />
          </span>
        ) : (
          `Welcome back ${displayName}`
        )}
      </h1>
    </div>
  )
}
