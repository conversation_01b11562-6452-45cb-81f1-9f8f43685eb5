import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { MobileNavHeader } from '../MobileNavHeader'
import { useAuth } from '@/hooks/useAuth'

// Mock dependencies
vi.mock('@/hooks/useAuth')
vi.mock('../UserMenu', () => ({
  UserMenu: ({ isOpen, onClose, user }: any) =>
    isOpen ? (
      <div data-testid="user-menu">
        User Menu for {user.email}
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
}))

describe('MobileNavHeader', () => {
  const mockUser = { email: '<EMAIL>', name: 'Test User' }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should not render when user is not authenticated', () => {
    vi.mocked(useAuth).mockReturnValue({
      user: null,
      isAuthenticated: false,
    } as any)

    const { container } = render(<MobileNavHeader />)
    expect(container.firstChild).toBeNull()
  })

  it('should render header when user is authenticated', () => {
    vi.mocked(useAuth).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
    } as any)

    render(<MobileNavHeader />)

    expect(screen.getByText('Dr. Muscle X')).toBeInTheDocument()
    expect(screen.getByText('T')).toBeInTheDocument() // First letter of email
    expect(screen.getByLabelText('Open user menu')).toBeInTheDocument()
  })

  it('should display first letter of email in avatar', () => {
    vi.mocked(useAuth).mockReturnValue({
      user: { email: '<EMAIL>' },
      isAuthenticated: true,
    } as any)

    render(<MobileNavHeader />)
    expect(screen.getByText('J')).toBeInTheDocument()
  })

  it('should open user menu when avatar is clicked', () => {
    vi.mocked(useAuth).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
    } as any)

    render(<MobileNavHeader />)

    // Menu should not be visible initially
    expect(screen.queryByTestId('user-menu')).not.toBeInTheDocument()

    // Click avatar to open menu
    fireEvent.click(screen.getByLabelText('Open user menu'))

    // Menu should be visible
    expect(screen.getByTestId('user-menu')).toBeInTheDocument()
    expect(
      screen.getByText('User <NAME_EMAIL>')
    ).toBeInTheDocument()
  })

  it('should close user menu when close is triggered', () => {
    vi.mocked(useAuth).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
    } as any)

    render(<MobileNavHeader />)

    // Open menu
    fireEvent.click(screen.getByLabelText('Open user menu'))
    expect(screen.getByTestId('user-menu')).toBeInTheDocument()

    // Close menu
    fireEvent.click(screen.getByText('Close'))
    expect(screen.queryByTestId('user-menu')).not.toBeInTheDocument()
  })

  it('should apply custom className', () => {
    vi.mocked(useAuth).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
    } as any)

    render(<MobileNavHeader className="custom-class" />)

    const header = screen.getByText('Dr. Muscle X').closest('header')
    expect(header).toHaveClass('custom-class')
  })

  it('should have proper z-index for layering', () => {
    vi.mocked(useAuth).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
    } as any)

    render(<MobileNavHeader />)

    const header = screen.getByText('Dr. Muscle X').closest('header')
    expect(header).toHaveClass('z-40')
  })
})
