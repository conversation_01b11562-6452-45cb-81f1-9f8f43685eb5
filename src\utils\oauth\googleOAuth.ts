/**
 * Google OAuth Helper for Dr. Muscle X
 *
 * Handles Google Sign-In SDK loading, initialization, and authentication flow
 * with comprehensive error handling and performance tracking.
 */

import type {
  GoogleCredentialResponse,
  GoogleTokenPayload,
  OAuthSuccessCallback,
  OAuthErrorCallback,
  OAuthError,
  OAuthUserData,
} from '../../types/oauth'
import { PerformanceMonitor } from '../performance'

/**
 * Google OAuth Helper class
 * Provides static methods for Google Sign-In integration
 */
export class GoogleOAuthHelper {
  private static readonly GOOGLE_SDK_URL =
    'https://accounts.google.com/gsi/client'

  private static readonly GOOGLE_CLIENT_ID =
    '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com'

  private static isInitialized = false

  private static loadPromise: Promise<void> | null = null

  /**
   * Load Google Sign-In SDK
   * Uses script injection with proper error handling
   */
  private static async loadGoogleSDK(): Promise<void> {
    // Return immediately if already loaded
    if (window.google?.accounts?.id) {
      return Promise.resolve()
    }

    // Return existing promise if SDK is already loading
    if (this.loadPromise) {
      return this.loadPromise
    }

    this.loadPromise = new Promise<void>((resolve, reject) => {
      PerformanceMonitor.mark('google-oauth-sdk-load-start')

      const script = document.createElement('script')
      script.src = this.GOOGLE_SDK_URL
      script.async = true
      script.defer = true

      script.onload = () => {
        PerformanceMonitor.mark('google-oauth-sdk-load-complete')
        PerformanceMonitor.measure(
          'google-oauth-sdk-load',
          'google-oauth-sdk-load-start',
          'google-oauth-sdk-load-complete'
        )
        resolve()
      }

      script.onerror = () => {
        this.loadPromise = null
        reject(new Error('Failed to load Google Sign-In SDK'))
      }

      document.head.appendChild(script)
    })

    return this.loadPromise
  }

  /**
   * Initialize Google Sign-In
   * @param onSuccess - Success callback
   * @param onError - Error callback
   * @param config - Additional configuration options
   */
  static async initialize(
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback,
    config?: {
      autoSelect?: boolean
      cancelOnTapOutside?: boolean
      context?: 'signin' | 'signup' | 'use'
      nonce?: string
    }
  ): Promise<void> {
    try {
      PerformanceMonitor.mark('google-oauth-init-start')

      // Load SDK if not already loaded
      await this.loadGoogleSDK()

      if (!window.google?.accounts?.id) {
        throw new Error('Google Sign-In SDK not available')
      }

      // Initialize Google Sign-In
      window.google.accounts.id.initialize({
        client_id: this.GOOGLE_CLIENT_ID,
        callback: (response: GoogleCredentialResponse) => {
          this.handleCredentialResponse(response, onSuccess, onError)
        },
        auto_select: config?.autoSelect ?? false,
        cancel_on_tap_outside: config?.cancelOnTapOutside ?? true,
        context: config?.context ?? 'signin',
        nonce: config?.nonce,
      })

      this.isInitialized = true
      PerformanceMonitor.mark('google-oauth-init-complete')
      PerformanceMonitor.measure(
        'google-oauth-init',
        'google-oauth-init-start',
        'google-oauth-init-complete'
      )
    } catch (error) {
      const oauthError: OAuthError = {
        code: 'provider_error',
        message: 'Failed to initialize Google Sign-In',
        provider: 'google',
        providerError: error,
        details: {
          originalError: error instanceof Error ? error.message : String(error),
        },
      }
      onError(oauthError)
      throw error // Re-throw to make the promise reject
    }
  }

  /**
   * Initiate Google Sign-In flow
   * @param onSuccess - Success callback
   * @param onError - Error callback
   * @param useOneTap - Whether to use One Tap (default: true)
   */
  static async signIn(
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback,
    useOneTap = true
  ): Promise<void> {
    try {
      PerformanceMonitor.mark('google-oauth-signin-start')

      // Initialize if not already done
      if (!this.isInitialized) {
        await this.initialize(onSuccess, onError)
      }

      if (!window.google?.accounts?.id) {
        throw new Error('Google Sign-In SDK not initialized')
      }

      if (useOneTap) {
        // Try One Tap first
        window.google.accounts.id.prompt((notification) => {
          if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
            // One Tap failed, fall back to button click
            const reason = notification.isNotDisplayed()
              ? notification.getNotDisplayedReason()
              : notification.getSkippedReason()

            // For certain reasons, we should show an error
            if (
              reason === 'invalid_client' ||
              reason === 'missing_client_id' ||
              reason === 'unregistered_origin'
            ) {
              const oauthError: OAuthError = {
                code: 'provider_error',
                message: `Google Sign-In failed: ${reason.replace(/_/g, ' ')}`,
                provider: 'google',
                details: { reason },
              }
              onError(oauthError)
            } else {
              // For other reasons, silently fall back to manual sign-in
              // User needs to click a sign-in button
            }
          }
          // If credential_returned, the callback will handle it
        })
      }
    } catch (error) {
      const oauthError: OAuthError = {
        code: 'oauth_failed',
        message: 'Failed to initiate Google Sign-In',
        provider: 'google',
        providerError: error,
        details: {
          originalError: error instanceof Error ? error.message : String(error),
        },
      }
      onError(oauthError)
    }
  }

  /**
   * Render Google Sign-In button
   * @param element - HTML element to render button into
   * @param config - Button configuration
   */
  static renderButton(
    element: HTMLElement,
    config?: Partial<google.accounts.id.GsiButtonConfiguration>
  ): void {
    if (!window.google?.accounts?.id) {
      throw new Error('Google Sign-In SDK not initialized')
    }

    const defaultConfig: google.accounts.id.GsiButtonConfiguration = {
      type: 'standard',
      theme: 'outline',
      size: 'large',
      text: 'signin_with',
      shape: 'rectangular',
      ...config,
    }

    window.google.accounts.id.renderButton(element, defaultConfig)
  }

  /**
   * Handle credential response from Google
   * @param response - Google credential response
   * @param onSuccess - Success callback
   * @param onError - Error callback
   */
  private static handleCredentialResponse(
    response: GoogleCredentialResponse,
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback
  ): void {
    try {
      PerformanceMonitor.mark('google-oauth-response-start')

      // Decode JWT token
      const tokenPayload = this.decodeJWT(response.credential)
      if (!tokenPayload) {
        throw new Error('Failed to decode Google ID token')
      }

      // Create unified OAuth user data
      const userData: OAuthUserData = {
        provider: 'google',
        providerId: tokenPayload.sub,
        email: tokenPayload.email,
        emailVerified: tokenPayload.email_verified,
        name: tokenPayload.name,
        firstName: tokenPayload.given_name,
        lastName: tokenPayload.family_name,
        pictureUrl: tokenPayload.picture,
        rawToken: response.credential,
        tokenPayload,
        metadata: {
          selectBy: response.select_by,
          clientId: response.clientId,
        },
      }

      PerformanceMonitor.mark('google-oauth-response-complete')
      PerformanceMonitor.measure(
        'google-oauth-response',
        'google-oauth-response-start',
        'google-oauth-response-complete'
      )

      PerformanceMonitor.measure(
        'google-oauth-signin',
        'google-oauth-signin-start',
        'google-oauth-response-complete'
      )

      onSuccess(userData)
    } catch (error) {
      const oauthError: OAuthError = {
        code: 'invalid_token',
        message: 'Failed to process Google credential',
        provider: 'google',
        providerError: error,
        details: {
          originalError: error instanceof Error ? error.message : String(error),
        },
      }
      onError(oauthError)
    }
  }

  /**
   * Decode JWT token
   * @param token - JWT token string
   * @returns Decoded token payload or null
   */
  static decodeJWT(token: string): GoogleTokenPayload | null {
    try {
      const parts = token.split('.')
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format')
      }

      // Decode base64url
      const base64Url = parts[1]!
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
          .join('')
      )

      const payload = JSON.parse(jsonPayload) as GoogleTokenPayload

      // Validate required fields
      if (!payload.sub || !payload.email || !payload.iss || !payload.aud) {
        throw new Error('Invalid token payload')
      }

      // Validate issuer
      if (
        payload.iss !== 'https://accounts.google.com' &&
        payload.iss !== 'accounts.google.com'
      ) {
        throw new Error('Invalid token issuer')
      }

      // Validate audience (client ID)
      if (payload.aud !== this.GOOGLE_CLIENT_ID) {
        throw new Error('Invalid token audience')
      }

      // Check expiration
      const now = Math.floor(Date.now() / 1000)
      if (payload.exp < now) {
        throw new Error('Token expired')
      }

      return payload
    } catch (error) {
      // Token decode failed
      return null
    }
  }

  /**
   * Get user info from access token (fallback method)
   * @param accessToken - Google access token
   * @returns User info or null
   */
  static async getUserInfo(accessToken: string): Promise<{
    id: string
    email: string
    verified_email: boolean
    name?: string
    given_name?: string
    family_name?: string
    picture?: string
  } | null> {
    try {
      const response = await fetch(
        `https://www.googleapis.com/oauth2/v1/userinfo?access_token=${accessToken}`
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      // User info fetch failed
      return null
    }
  }

  /**
   * Cancel One Tap flow
   */
  static cancel(): void {
    if (window.google?.accounts?.id) {
      window.google.accounts.id.cancel()
    }
  }

  /**
   * Disable auto-select for One Tap
   */
  static disableAutoSelect(): void {
    if (window.google?.accounts?.id) {
      window.google.accounts.id.disableAutoSelect()
    }
  }

  /**
   * Revoke Google authorization
   * @param email - User email to revoke
   * @param callback - Optional callback after revocation
   */
  static revoke(email: string, callback?: () => void): void {
    if (window.google?.accounts?.id) {
      window.google.accounts.id.revoke(email, callback)
    }
  }

  /**
   * Check if Google Sign-In is available
   */
  static isAvailable(): boolean {
    return typeof window !== 'undefined' && !!window.google?.accounts?.id
  }

  /**
   * Check if Google Sign-In is initialized
   */
  static isReady(): boolean {
    return this.isInitialized && this.isAvailable()
  }
}

// Export to window for testing
if (typeof window !== 'undefined' && process.env.NODE_ENV !== 'production') {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ;(window as any).GoogleOAuthHelper = GoogleOAuthHelper
}
