Based on my analysis of the MAUI mobile app, here's a comprehensive breakdown of all API calls during app open and login steps:

App Open and Login Flow - API Call Sequence

1. App Startup (App.xaml.cs:418-654)
   When the app starts, it checks for existing authentication:

csharp

// Check if user has valid token
if (LocalDBManager.Instance.GetDBSetting("token") != null)
{
// Token exists - check expiration
var tokenExpires = LocalDBManager.Instance.GetDBSetting("token_expires_date");
if (DateTime.Now < expiryDate)
{
// Valid token - proceed to main app
await PagesFactory.PushAsync<MainTabbedPage>();
}
} 2. Login Flow - Email/Password
API Call: POST /token

csharp

// DrMuscleRestClient.cs:1079
public async Task<LoginSuccessResult> Login(LoginModel model)
{
var body = new List<KeyValuePair<string, string>>
{
new KeyValuePair<string, string>("grant_type","password"),
new KeyValuePair<string, string>("password",model.Password),
new KeyValuePair<string, string>("username",model.Username)
};
response = await client.PostAsync("token", content);
}
Response Handling:

csharp

// WelcomePage.xaml.cs:1635-1641
LoginSuccessResult lr = await DrMuscleRestClient.Instance.Login(new LoginModel()
{
Username = EmailEntry.Text,
Password = PasswordEntry.Text
});

if (lr != null && !string.IsNullOrEmpty(lr.access_token))
{
// Store token locally
LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
LocalDBManager.Instance.SetDBSetting("token_expires_date",
DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
} 3. Get User Info After Login
API Call: POST /api/Account/GetUserInfoPyramid

csharp

// WelcomePage.xaml.cs:1645
UserInfosModel uim = await DrMuscleRestClient.Instance.GetUserInfo();
Response Handling:

csharp

// Store user data locally
LocalDBManager.Instance.SetDBSetting("email", uim.Email);
LocalDBManager.Instance.SetDBSetting("firstname", uim.Firstname);
LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString());
LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString());
// ... many more settings stored 4. Google Login Flow
API Call: POST /token with Google token

csharp

// DrMuscleRestClient.cs:1154
public async Task<LoginSuccessResult> GoogleLogin(string GoogleToken, string email,
string name, string bodyWeight, string massUnit)
{
var body = new List<KeyValuePair<string, string>>
{
new KeyValuePair<string, string>("grant_type","google"),
new KeyValuePair<string, string>("accesstoken", GoogleToken),
new KeyValuePair<string, string>("provider", "google"),
new KeyValuePair<string, string>("email", email),
new KeyValuePair<string, string>("name", name),
new KeyValuePair<string, string>("bodyweight", bodyWeight),
new KeyValuePair<string, string>("massunit", massUnit)
};
} 5. Apple Sign In Flow
Apple Sign In doesn't have a specific API endpoint but uses the standard login after getting Apple credentials:

csharp

// WelcomePage.xaml.cs:653-711
var account = await appleSignInService.SignInAsync();
if (account != null)
{
// Store Apple credentials in SecureStorage
await SecureStorage.SetAsync("Email", account.Email);
await SecureStorage.SetAsync("Name", account.Name);

    // Then proceed with standard login flow
    OnLoginComplete(new GoogleUser() { Email = account.Email, Name = account.Name }, "");

} 6. Check If Email Exists (Before Registration)
API Call: POST /api/Account/IsEmailAlreadyExist

csharp

// WelcomePage.xaml.cs:1591
BooleanModel existingUser = await DrMuscleRestClient.Instance.IsEmailAlreadyExist(
new IsEmailAlreadyExistModel() { email = EmailEntry.Text }); 7. New User Registration Flow
Step 1 - Register User: API Call: POST /api/Account/Register or POST /api/Account/RegisterUserBeforeDemo

csharp

// MainOnboardingPage.xaml.cs
RegisterModel registerModel = new RegisterModel();
registerModel.Firstname = LocalDBManager.Instance.GetDBSetting("firstname").Value;
registerModel.EmailAddress = LocalDBManager.Instance.GetDBSetting("email").Value;
registerModel.MassUnit = "lb";
registerModel.BodyWeight = new MultiUnityWeight(150, "lb");
registerModel.Password = LocalDBManager.Instance.GetDBSetting("password").Value;

BooleanModel registerResponse = await DrMuscleRestClient.Instance.RegisterUserBeforeDemo(registerModel);
Step 2 - Login After Registration:

csharp

LoginSuccessResult lr = await DrMuscleRestClient.Instance.LoginWithoutLoader(new LoginModel()
{
Username = registerModel.EmailAddress,
Password = registerModel.Password
});
Step 3 - Complete Registration After Onboarding: API Call: POST /api/Account/RegisterUserAfterDemoV2

csharp

UserInfosModel registerResponse = await DrMuscleRestClient.Instance.RegisterUserAfterDemo(registerModel); 8. After Successful Login - Load User's Workout Data
API Call: POST /api/Workout/GetUserWorkoutTemplateGroup

csharp

// This loads user's workout programs
GetUserWorkoutTemplateGroupResponseModel workouts =
await DrMuscleRestClient.Instance.GetUserWorkoutGroup(); 9. Device Token Registration (for Push Notifications)
API Call: POST /api/Account/AddDeviceToken

csharp

// App.cs - RegisterDeviceToken()
await DrMuscleRestClient.Instance.AddDeviceToken(new DeviceModel()
{
Token = deviceToken,
Platform = Device.RuntimePlatform,
// ... other device info
}); 10. Password Reset Flow
API Call: POST /api/Account/ForgotPassword

csharp

// WelcomePage.xaml.cs:1498
BooleanModel response = await DrMuscleRestClient.Instance.ForgotPassword(
new ForgotPasswordModel() { Email = email });
Data Storage Pattern
All API responses are stored locally using LocalDBManager:

csharp

// Example of local storage pattern
LocalDBManager.Instance.SetDBSetting("key", "value");

// Common stored values:

- "token" - JWT authentication token
- "token_expires_date" - Token expiration timestamp
- "email", "firstname", "lastname" - User profile
- "massunit" - kg or lb preference
- "BodyWeight", "WeightGoal" - User measurements
- "Equipment", "ChinUp", "Dumbbell" etc. - Equipment settings
- "SetStyle", "IsPyramid" - Workout preferences
- "timer\_\*" - Timer settings
  Error Handling Pattern
  csharp

try
{
// API call
var result = await DrMuscleRestClient.Instance.SomeMethod();
if (result != null)
{
// Handle success
}
}
catch (Exception ex)
{
await UserDialogs.Instance.AlertAsync(new AlertConfig()
{
Message = "Error message",
Title = AppResources.Error
});
}
Summary of Login Flow Sequence:
App Launch → Check stored token validity
If No Valid Token → Show WelcomePage
User Chooses Login Method:
Email: Check if email exists → Login with credentials
Google: Get Google token → Login with Google endpoint
Apple: Get Apple credentials → Use standard login
After Login Success → Get user info
Store All Data Locally → Navigate to main app
Register Device Token → For push notifications
The app uses a combination of OAuth-style token authentication and local storage to maintain user sessions. All sensitive data like tokens are stored with expiration dates, and the app checks these on startup to determine if re-authentication is needed.
