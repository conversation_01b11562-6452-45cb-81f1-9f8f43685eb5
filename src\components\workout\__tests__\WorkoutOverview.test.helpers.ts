import { vi } from 'vitest'
import type { MockUseWorkoutReturn } from './WorkoutOverview.types'
import type {
  WorkoutTemplateGroupModel,
  GetUserWorkoutLogAverageResponse,
  RecommendationModel,
  ExerciseWorkSetsModel,
  ExerciseModel,
} from '@/types'
import type { QueryObserverResult } from '@tanstack/react-query'

export function createMockUseWorkoutReturn(
  overrides?: Partial<MockUseWorkoutReturn>
): MockUseWorkoutReturn {
  return {
    // Data
    todaysWorkout: null,
    userProgramInfo: null,
    currentWorkout: null,
    currentExercise: null,
    nextExercise: null,
    currentSetIndex: 0,
    totalSets: 0,
    isLastSet: false,
    isLastExercise: false,
    isWarmupSet: false,
    workoutSession: null,

    // New hierarchical data structure with progressive loading
    exercises: [],
    currentExerciseWorkSets: null,
    exerciseLoadingStateManager: undefined,

    // Loading states
    isLoadingWorkout: false,
    isLoading: false,
    loadingStates: {
      programInfo: false,
      userWorkout: false,
      recommendation: false,
    },

    // Errors
    workoutError: null,
    error: null,

    // Recommendation
    recommendation: null,
    refetchRecommendation: vi.fn().mockResolvedValue({
      data: null,
      error: null,
      isError: false,
      isSuccess: true,
      isLoading: false,
      isLoadingError: false,
      isRefetchError: false,
      isPending: false,
      status: 'success',
      fetchStatus: 'idle',
      dataUpdatedAt: Date.now(),
      errorUpdatedAt: 0,
      failureCount: 0,
      errorUpdateCount: 0,
      isFetched: true,
      isFetchedAfterMount: true,
      isFetching: false,
      isPaused: false,
      isRefetching: false,
      isStale: false,
      refetch: vi.fn(),
      isPlaceholderData: false,
      failureReason: null,
      isInitialLoading: false,
      promise: Promise.resolve({
        data: null,
        error: null,
        isError: false,
        isSuccess: true,
        isLoading: false,
        isLoadingError: false,
        isRefetchError: false,
        isPending: false,
        status: 'success',
        fetchStatus: 'idle',
        dataUpdatedAt: Date.now(),
        errorUpdatedAt: 0,
        failureCount: 0,
        errorUpdateCount: 0,
        isFetched: true,
        isFetchedAfterMount: true,
        isFetching: false,
        isPaused: false,
        isRefetching: false,
        isStale: false,
        refetch: vi.fn(),
        isPlaceholderData: false,
        failureReason: null,
        isInitialLoading: false,
        promise: undefined as unknown as Promise<
          QueryObserverResult<RecommendationModel | null, Error>
        >,
      }),
    } as unknown as QueryObserverResult<RecommendationModel | null, Error>),

    // Actions
    saveSet: vi.fn(),
    startWorkout: vi.fn(),
    finishWorkout: vi.fn(),
    nextSet: vi.fn(),
    restoreWorkout: vi.fn(),
    goToNextExercise: vi.fn(),
    getRecommendation: vi.fn(),
    getRestDuration: vi.fn().mockReturnValue(120),

    // Status
    isOffline: false,

    // Progressive loading support
    expectedExerciseCount: undefined,

    // Optimistic loading states
    hasInitialData: false,
    isLoadingFresh: false,

    // Refresh function
    refreshWorkout: vi.fn().mockResolvedValue(undefined),

    // Helper functions for new structure
    getExerciseWorkSets: vi.fn(),
    updateExerciseWorkSets: vi.fn(),

    // Legacy (for backward compatibility)
    setCurrentWorkout: vi.fn(),
    completeWorkout: vi.fn(),
    updateSetLog: vi.fn(),
    addSetLog: vi.fn(),
    updateExerciseRIR: vi.fn(),
    getNextExercise: vi.fn(),

    // Apply overrides
    ...overrides,
  }
}

export function createMockWorkoutTemplateGroup(
  overrides?: Partial<WorkoutTemplateGroupModel>
): WorkoutTemplateGroupModel {
  return {
    Id: 1,
    Label: 'Test Program',
    WorkoutTemplates: [],
    IsFeaturedProgram: false,
    UserId: '',
    IsSystemExercise: true,
    RequiredWorkoutToLevelUp: 5,
    ProgramId: 1,
    ...overrides,
  }
}

export function createMockUserProgramInfo(
  overrides?: Partial<GetUserWorkoutLogAverageResponse>
): GetUserWorkoutLogAverageResponse {
  return {
    Sets: [],
    Histograms: [],
    ...overrides,
  }
}

export function createMockExerciseWorkSetsModel(
  overrides?: Partial<ExerciseWorkSetsModel>
): ExerciseWorkSetsModel {
  return {
    Id: 1,
    Label: 'Test Exercise',
    BodyPartId: 1,
    IsFinished: false,
    IsNextExercise: false,
    isLoadingSets: false,
    setsError: null,
    lastSetsUpdate: Date.now(),
    sets: [],
    IsBodyweight: false,
    IsEasy: false,
    IsMedium: false,
    VideoLink: '',
    LocalVideo: undefined,
    IsAssisted: false,
    ...overrides,
  }
}

export function createMockExerciseWorkSetsFromExercise(
  exercise: ExerciseModel
): ExerciseWorkSetsModel {
  return createMockExerciseWorkSetsModel({
    Id: exercise.Id,
    Label: exercise.Label,
    BodyPartId: exercise.BodyPartId || 0,
    IsBodyweight: exercise.IsBodyweight || false,
    IsEasy: exercise.IsEasy || false,
    IsMedium: exercise.IsMedium || false,
    IsNextExercise: exercise.IsNextExercise || false,
    IsFinished: exercise.IsFinished || false,
    VideoLink: exercise.VideoUrl || undefined,
    LocalVideo: exercise.LocalVideo || undefined,
    IsAssisted: exercise.IsAssisted || false,
  })
}
