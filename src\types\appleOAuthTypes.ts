/**
 * Apple OAuth Authentication Types for Dr. Muscle X
 */

/**
 * Apple Sign-In response structure
 */
export interface AppleSignInResponse {
  /** Authorization object containing tokens */
  authorization?: {
    /** Authorization code for server validation */
    code: string
    /** ID token (JWT) containing user information */
    id_token: string
    /** State parameter for CSRF protection */
    state?: string
  }
  /** Direct properties (alternative format) */
  code?: string
  id_token?: string
  state?: string
  /** User information (only provided on first sign-in) */
  user?: {
    /** User's email address */
    email: string
    /** User's name information */
    name?: {
      /** First/given name */
      firstName: string
      /** Last/family name */
      lastName: string
    }
  }
}

/**
 * Decoded Apple ID token payload
 */
export interface AppleTokenPayload {
  /** Unique Apple user ID */
  sub: string
  /** User's email (may be private relay email) */
  email?: string
  /** Whether email is verified */
  email_verified?: boolean | string
  /** Whether this is a private relay email */
  is_private_email?: boolean | string
  /** Real user status (2 = real user) */
  real_user_status?: number
  /** Issued at timestamp */
  iat: number
  /** Expiration timestamp */
  exp: number
  /** Issuer (apple) */
  iss: string
  /** Audience (bundle ID) */
  aud: string
  /** Nonce for security */
  nonce?: string
  /** Authentication time */
  auth_time?: number
}

/**
 * Apple OAuth configuration
 */
export interface AppleOAuthConfig {
  clientId: string
  /** OAuth scopes (space-separated) */
  scope?: string
  /** Redirect URI for Apple Sign-In */
  redirectUri: string
  /** Response type */
  responseType?: 'code' | 'code id_token'
  /** Response mode */
  responseMode?: 'query' | 'fragment' | 'form_post' | 'web_message'
  /** State for CSRF protection */
  state?: string
  /** Nonce for security */
  nonce?: string
  /** Use popup instead of redirect */
  usePopup?: boolean
}

/**
 * Type guard for Apple sign-in response
 */
export function isAppleSignInResponse(
  response: unknown
): response is AppleSignInResponse {
  return (
    typeof response === 'object' &&
    response !== null &&
    'authorization' in response &&
    typeof (response as AppleSignInResponse).authorization === 'object'
  )
}
