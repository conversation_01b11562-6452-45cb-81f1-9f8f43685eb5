import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { act } from '@testing-library/react'
import { useProgramStore } from '@/stores/programStore'
import type { ProgramModel, ProgramProgress, ProgramStats } from '@/types'

// Mock data
const mockProgram: ProgramModel = {
  id: 1,
  name: 'Test Program',
  description: 'Test Description',
  category: 'Strength',
  totalDays: 90,
  currentDay: 10,
  workoutsCompleted: 10,
  startDate: '2024-01-01T00:00:00Z',
  totalWorkouts: 36,
  imageUrl: 'https://example.com/image.jpg',
}

const mockProgress: ProgramProgress = {
  percentage: 28,
  daysCompleted: 10,
  totalWorkouts: 36,
  currentWeek: 4,
  workoutsThisWeek: 1,
  remainingWorkouts: 26,
}

const mockStats: ProgramStats = {
  averageWorkoutTime: 45.5,
  totalVolume: 50000,
  personalRecords: 5,
  consecutiveWeeks: 3,
  lastWorkoutDate: '2024-01-10T00:00:00Z',
  totalWorkoutsCompleted: 10,
}

describe('Program Store', () => {
  beforeEach(() => {
    // Clear the store and localStorage before each test
    localStorage.clear()
    useProgramStore.setState({
      cachedData: {
        program: null,
        progress: null,
        stats: null,
        lastUpdated: {
          program: 0,
          progress: 0,
          stats: 0,
        },
      },
      hasHydrated: false,
      isLoadingProgram: false,
      isLoadingProgress: false,
      isLoadingStats: false,
      isRefreshingProgram: false,
      isRefreshingProgress: false,
      isRefreshingStats: false,
      programError: null,
      progressError: null,
      statsError: null,
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Cache Storage', () => {
    it('should store and retrieve program data with timestamp', () => {
      const beforeTime = Date.now()

      act(() => {
        useProgramStore.getState().setCachedProgram(mockProgram)
      })

      const state = useProgramStore.getState()
      expect(state.getCachedProgram()).toEqual(mockProgram)
      expect(state.cachedData.lastUpdated.program).toBeGreaterThanOrEqual(
        beforeTime
      )
      expect(state.cachedData.lastUpdated.program).toBeLessThanOrEqual(
        Date.now()
      )
    })

    it('should store and retrieve progress data with timestamp', () => {
      const beforeTime = Date.now()

      act(() => {
        useProgramStore.getState().setCachedProgress(mockProgress)
      })

      const state = useProgramStore.getState()
      expect(state.getCachedProgress()).toEqual(mockProgress)
      expect(state.cachedData.lastUpdated.progress).toBeGreaterThanOrEqual(
        beforeTime
      )
    })

    it('should store and retrieve stats data with timestamp', () => {
      const beforeTime = Date.now()

      act(() => {
        useProgramStore.getState().setCachedStats(mockStats)
      })

      const state = useProgramStore.getState()
      expect(state.getCachedStats()).toEqual(mockStats)
      expect(state.cachedData.lastUpdated.stats).toBeGreaterThanOrEqual(
        beforeTime
      )
    })

    it('should handle null data gracefully', () => {
      act(() => {
        useProgramStore.getState().setCachedProgram(null)
        useProgramStore.getState().setCachedProgress(null)
        useProgramStore.getState().setCachedStats(null)
      })

      const state = useProgramStore.getState()
      expect(state.getCachedProgram()).toBeNull()
      expect(state.getCachedProgress()).toBeNull()
      expect(state.getCachedStats()).toBeNull()
    })
  })

  describe('Cache Staleness Detection', () => {
    it('should detect fresh program cache', () => {
      act(() => {
        useProgramStore.getState().setCachedProgram(mockProgram)
      })

      expect(useProgramStore.getState().isProgramCacheStale()).toBe(false)
    })

    it('should detect stale program cache (24 hours)', () => {
      const twentyFiveHoursAgo = Date.now() - 25 * 60 * 60 * 1000

      act(() => {
        useProgramStore.setState({
          cachedData: {
            program: mockProgram,
            progress: null,
            stats: null,
            lastUpdated: {
              program: twentyFiveHoursAgo,
              progress: 0,
              stats: 0,
            },
          },
        })
      })

      expect(useProgramStore.getState().isProgramCacheStale()).toBe(true)
    })

    it('should detect stale progress cache (1 hour)', () => {
      const twoHoursAgo = Date.now() - 2 * 60 * 60 * 1000

      act(() => {
        useProgramStore.setState({
          cachedData: {
            program: null,
            progress: mockProgress,
            stats: null,
            lastUpdated: {
              program: 0,
              progress: twoHoursAgo,
              stats: 0,
            },
          },
        })
      })

      expect(useProgramStore.getState().isProgressCacheStale()).toBe(true)
    })

    it('should use generic isCacheStale method correctly', () => {
      const thirtyMinutesAgo = Date.now() - 30 * 60 * 1000

      act(() => {
        useProgramStore.setState({
          cachedData: {
            program: mockProgram,
            progress: mockProgress,
            stats: mockStats,
            lastUpdated: {
              program: thirtyMinutesAgo,
              progress: thirtyMinutesAgo,
              stats: thirtyMinutesAgo,
            },
          },
        })
      })

      const state = useProgramStore.getState()
      expect(state.isCacheStale('program')).toBe(false)
      expect(state.isCacheStale('progress')).toBe(false)
      expect(state.isCacheStale('stats')).toBe(false)
    })
  })

  describe('Cache Cleanup', () => {
    it('should clear expired program cache', () => {
      const twentyFiveHoursAgo = Date.now() - 25 * 60 * 60 * 1000
      const thirtyMinutesAgo = Date.now() - 30 * 60 * 1000

      act(() => {
        useProgramStore.setState({
          cachedData: {
            program: mockProgram,
            progress: mockProgress,
            stats: mockStats,
            lastUpdated: {
              program: twentyFiveHoursAgo, // Expired
              progress: thirtyMinutesAgo, // Fresh
              stats: thirtyMinutesAgo, // Fresh
            },
          },
        })
      })

      act(() => {
        useProgramStore.getState().clearExpiredCache()
      })

      const state = useProgramStore.getState()
      expect(state.getCachedProgram()).toBeNull()
      expect(state.getCachedProgress()).toEqual(mockProgress) // Should remain
      expect(state.getCachedStats()).toEqual(mockStats) // Should remain
    })

    it('should clear all cache when requested', () => {
      act(() => {
        useProgramStore.getState().setCachedProgram(mockProgram)
        useProgramStore.getState().setCachedProgress(mockProgress)
        useProgramStore.getState().setCachedStats(mockStats)
      })

      act(() => {
        useProgramStore.getState().clearAllCache()
      })

      const state = useProgramStore.getState()
      expect(state.getCachedProgram()).toBeNull()
      expect(state.getCachedProgress()).toBeNull()
      expect(state.getCachedStats()).toBeNull()
      expect(state.cachedData.lastUpdated.program).toBe(0)
      expect(state.cachedData.lastUpdated.progress).toBe(0)
      expect(state.cachedData.lastUpdated.stats).toBe(0)
    })
  })

  describe('Loading States', () => {
    it('should manage loading states independently', () => {
      act(() => {
        useProgramStore.getState().setIsLoadingProgram(true)
        useProgramStore.getState().setIsLoadingProgress(false)
        useProgramStore.getState().setIsLoadingStats(true)
      })

      const state = useProgramStore.getState()
      expect(state.isLoadingProgram).toBe(true)
      expect(state.isLoadingProgress).toBe(false)
      expect(state.isLoadingStats).toBe(true)
    })

    it('should manage refresh states independently', () => {
      act(() => {
        useProgramStore.getState().setIsRefreshingProgram(true)
        useProgramStore.getState().setIsRefreshingProgress(false)
        useProgramStore.getState().setIsRefreshingStats(true)
      })

      const state = useProgramStore.getState()
      expect(state.isRefreshingProgram).toBe(true)
      expect(state.isRefreshingProgress).toBe(false)
      expect(state.isRefreshingStats).toBe(true)
    })
  })

  describe('Error Management', () => {
    it('should store and clear errors independently', () => {
      const error1 = new Error('Program error')
      const error2 = new Error('Stats error')

      act(() => {
        useProgramStore.getState().setProgramError(error1)
        useProgramStore.getState().setStatsError(error2)
      })

      let state = useProgramStore.getState()
      expect(state.programError).toEqual(error1)
      expect(state.progressError).toBeNull()
      expect(state.statsError).toEqual(error2)

      act(() => {
        useProgramStore.getState().setProgramError(null)
      })

      state = useProgramStore.getState()
      expect(state.programError).toBeNull()
      expect(state.statsError).toEqual(error2) // Should remain
    })
  })

  describe('Cache Utilities', () => {
    it('should calculate cache age correctly', () => {
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000

      act(() => {
        useProgramStore.setState({
          cachedData: {
            program: mockProgram,
            progress: null,
            stats: null,
            lastUpdated: {
              program: fiveMinutesAgo,
              progress: 0,
              stats: 0,
            },
          },
        })
      })

      const state = useProgramStore.getState()
      const programAge = state.getCacheAge('program')

      expect(programAge).not.toBeNull()
      expect(programAge).toBeGreaterThanOrEqual(5 * 60 * 1000 - 100) // Allow 100ms variance
      expect(programAge).toBeLessThanOrEqual(5 * 60 * 1000 + 100)
    })

    it('should return null for cache age when no data cached', () => {
      const state = useProgramStore.getState()
      expect(state.getCacheAge('program')).toBeNull()
      expect(state.getCacheAge('progress')).toBeNull()
      expect(state.getCacheAge('stats')).toBeNull()
    })

    it('should provide cache statistics', () => {
      act(() => {
        useProgramStore.getState().setCachedProgram(mockProgram)
        useProgramStore.getState().setCachedProgress(mockProgress)
        useProgramStore.getState().setHasHydrated(true)
      })

      const stats = useProgramStore.getState().getCacheStats()

      expect(stats.totalSize).toBeGreaterThan(0)
      expect(stats.programAge).not.toBeNull()
      expect(stats.progressAge).not.toBeNull()
      expect(stats.statsAge).toBeNull() // No stats cached
      expect(stats.isHydrated).toBe(true)
    })
  })

  describe('Persistence', () => {
    it('should persist cached data to localStorage', async () => {
      act(() => {
        useProgramStore.getState().setCachedProgram(mockProgram)
        useProgramStore.getState().setCachedProgress(mockProgress)
      })

      // Wait for persistence to complete
      await new Promise<void>((resolve) => {
        setTimeout(resolve, 100)
      })

      // Check localStorage
      const stored = localStorage.getItem('drmuscle-program')
      expect(stored).not.toBeNull()
      expect(stored).not.toBe('undefined')

      if (stored) {
        const parsed = JSON.parse(stored)
        expect(parsed.state.cachedData.program).toEqual(mockProgram)
        expect(parsed.state.cachedData.progress).toEqual(mockProgress)
      }
    })

    it('should rehydrate from localStorage', async () => {
      // This test verifies that persistence works by testing the store's persist functionality
      // In a real scenario, the store would rehydrate automatically on initialization

      // First, save data to the store
      act(() => {
        useProgramStore.getState().setCachedProgram(mockProgram)
        useProgramStore.getState().setCachedProgress(mockProgress)
      })

      // Wait for persistence
      await new Promise<void>((resolve) => {
        setTimeout(resolve, 100)
      })

      // Verify data was persisted
      const stored = localStorage.getItem('drmuscle-program')
      expect(stored).toBeTruthy()

      // Clear the store state (simulating a fresh start)
      act(() => {
        useProgramStore.setState({
          cachedData: {
            program: null,
            progress: null,
            stats: null,
            lastUpdated: {
              program: 0,
              progress: 0,
              stats: 0,
            },
          },
          hasHydrated: false,
        })
      })

      // Manually trigger rehydration
      await act(async () => {
        const storedData = localStorage.getItem('drmuscle-program')
        if (storedData) {
          const parsed = JSON.parse(storedData)
          useProgramStore.setState({
            ...parsed.state,
            hasHydrated: true,
          })
        }
      })

      const state = useProgramStore.getState()
      expect(state.getCachedProgram()).toEqual(mockProgram)
      expect(state.getCachedProgress()).toEqual(mockProgress)
      expect(state.hasHydrated).toBe(true)
    })
  })

  describe('useProgramCache Hook', () => {
    it('should provide convenient access to cache data', () => {
      act(() => {
        useProgramStore.getState().setCachedProgram(mockProgram)
        useProgramStore.getState().setCachedProgress(mockProgress)
        useProgramStore.getState().setCachedStats(mockStats)
        useProgramStore.getState().setHasHydrated(true)
      })

      // Get the state directly using selector
      const state = useProgramStore.getState()
      const cacheData = {
        program: state.getCachedProgram(),
        progress: state.getCachedProgress(),
        stats: state.getCachedStats(),
        isProgramStale: state.isProgramCacheStale(),
        isProgressStale: state.isProgressCacheStale(),
        isStatsStale: state.isStatsCacheStale(),
        hasHydrated: state.hasHydrated,
      }

      expect(cacheData.program).toEqual(mockProgram)
      expect(cacheData.progress).toEqual(mockProgress)
      expect(cacheData.stats).toEqual(mockStats)
      expect(cacheData.isProgramStale).toBe(false)
      expect(cacheData.isProgressStale).toBe(false)
      expect(cacheData.isStatsStale).toBe(false)
      expect(cacheData.hasHydrated).toBe(true)
    })
  })
})
