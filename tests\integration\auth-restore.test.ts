import { test, expect } from '@playwright/test'

test.describe('Auth Token Restoration', () => {
  test('should restore auth token from cookies on page reload', async ({
    page,
    context,
  }) => {
    // Navigate to login page
    await page.goto('/login')

    // Log in with test credentials
    await page.fill('input[name="email"]', '<EMAIL>')
    await page.fill('input[name="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('/program', { timeout: 10000 })

    // Verify we're authenticated
    const programTitle = await page.textContent('h1')
    expect(programTitle).toContain('Program')

    // Store cookies before reload
    const cookies = await context.cookies()
    const authCookie = cookies.find((c) => c.name === 'drmuscle-auth-token')
    expect(authCookie).toBeTruthy()

    // Reload the page - this will clear JavaScript memory but keep cookies
    await page.reload()

    // Wait for the page to fully load
    await page.waitForLoadState('networkidle')

    // Check console logs for auth token restoration
    const consoleLogs: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'info' && msg.text().includes('Auth token restored')) {
        consoleLogs.push(msg.text())
      }
    })

    // Navigate to a protected route to verify auth still works
    await page.goto('/workout')

    // Should stay on workout page, not redirect to login
    await expect(page).toHaveURL('/workout')

    // Verify the page loads correctly
    const workoutContent = await page.textContent('body')
    expect(workoutContent).not.toContain('Login')
  })

  test('should not restore token when no auth cookie present', async ({
    page,
  }) => {
    // Clear all cookies first
    await page.context().clearCookies()

    // Navigate directly to a protected route
    await page.goto('/program')

    // Should be redirected to login
    await expect(page).toHaveURL('/login')
  })
})
