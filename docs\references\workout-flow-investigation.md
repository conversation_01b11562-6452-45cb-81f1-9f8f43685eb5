# Dr. Muscle Workout Flow Investigation Guide

**Objective:** Understand why the web app shows "No workout scheduled" when users should always have a next workout queued up.

**Context:** The web app successfully authenticates and calls `/api/Workout/GetUserWorkoutTemplateGroup` but receives empty data. This suggests either:

1. Missing API calls before/after the workout request
2. Incorrect endpoint or request format
3. Missing user setup/onboarding steps
4. Different workout scheduling logic than expected

## CRITICAL INVESTIGATION TASKS

### 1. WORKOUT INITIALIZATION FLOW

**Goal:** Find how new users get their first workout assigned

**Search Patterns:**

```bash
# Look for workout template assignment
grep -r "WorkoutTemplate" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "AssignWorkout" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "CreateWorkout" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "ChooseWorkout" --include="*.cs" --include="*.swift" --include="*.kt"

# Look for user onboarding/setup
grep -r "Onboarding" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "SetupWorkout" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "FirstTime" --include="*.cs" --include="*.swift" --include="*.kt"
```

**Expected Findings:**

- Initial workout template selection process
- User setup/configuration steps
- Default workout assignment logic

### 2. WORKOUT SCHEDULING SYSTEM

**Goal:** Understand how workouts are scheduled and queued

**Search Patterns:**

```bash
# Look for scheduling logic
grep -r "ScheduleWorkout" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "NextWorkout" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "TodayWorkout" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "WorkoutSchedule" --include="*.cs" --include="*.swift" --include="*.kt"

# Look for workout program logic
grep -r "WorkoutProgram" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "TrainingProgram" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "Program.*Select" --include="*.cs" --include="*.swift" --include="*.kt"
```

**Expected Findings:**

- How workouts are automatically scheduled
- Program selection and assignment
- Workout rotation/cycling logic

### 3. API ENDPOINT ANALYSIS

**Goal:** Verify we're using the correct endpoints and request formats

**Search Patterns:**

```bash
# Find all workout-related API calls
grep -r "GetUserWorkoutTemplateGroup" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "/api/Workout/" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "GetTodayWorkout" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "GetCurrentWorkout" --include="*.cs" --include="*.swift" --include="*.kt"

# Look for different workout endpoints
grep -r "Workout.*Get" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "api.*workout" --include="*.cs" --include="*.swift" --include="*.kt" -i
```

**Expected Findings:**

- Complete list of workout API endpoints
- Request/response patterns
- Proper endpoint usage sequence

### 4. USER STATE AND DATA MODELS

**Goal:** Understand what user data is required for workout scheduling

**Search Patterns:**

```bash
# Look for user profile/settings that affect workouts
grep -r "UserProfile" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "UserSettings" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "TrainingLevel" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "Experience.*Level" --include="*.cs" --include="*.swift" --include="*.kt"

# Look for prerequisites
grep -r "HasWorkout" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "IsSetup" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "IsConfigured" --include="*.cs" --include="*.swift" --include="*.kt"
```

**Expected Findings:**

- Required user configuration for workout assignment
- User state that determines workout availability
- Setup completion flags

### 5. MOBILE APP STARTUP SEQUENCE

**Goal:** Find the exact sequence of API calls when the app starts

**Search Patterns:**

```bash
# Look for app initialization
grep -r "Application.*Start" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "App.*Launch" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "DidFinishLaunching" --include="*.swift"
grep -r "onCreate" --include="*.kt"

# Look for login success handling
grep -r "LoginSuccess" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "OnLoginComplete" --include="*.cs" --include="*.swift" --include="*.kt"
grep -r "AfterLogin" --include="*.cs" --include="*.swift" --include="*.kt"
```

**Expected Findings:**

- Sequence of API calls after successful login
- Data initialization flow
- Required setup steps

## SPECIFIC FILES TO EXAMINE

### 1. Main Application Files

```
- App.xaml.cs (Xamarin)
- AppDelegate.swift (iOS)
- MainActivity.kt/java (Android)
- Program.cs or Startup.cs (if backend)
```

### 2. Authentication Handlers

```
- LoginPage.xaml.cs
- AuthService.cs/swift/kt
- TokenManager.cs/swift/kt
```

### 3. Workout-Related Classes

```
- WorkoutService.cs/swift/kt
- WorkoutViewModel.cs/swift/kt
- WorkoutRepository.cs/swift/kt
- Any file containing "Workout" in the name
```

### 4. User Setup/Onboarding

```
- OnboardingFlow.cs/swift/kt
- SetupWizard.cs/swift/kt
- FirstTimeUser.cs/swift/kt
```

## EXPECTED OUTPUT FORMAT

Create a markdown file with this structure:

```markdown
# Dr. Muscle Mobile App Workout Flow Analysis

## DISCOVERY SUMMARY

- [Key findings about workout initialization]
- [Missing steps in our web app implementation]
- [Required API calls we're not making]

## WORKOUT INITIALIZATION SEQUENCE

1. [Step 1: After login...]
2. [Step 2: User setup check...]
3. [Step 3: Workout assignment...]
4. [etc...]

## MISSING API CALLS

- [Endpoint we need to call]
- [Required parameters]
- [Expected response]

## USER SETUP REQUIREMENTS

- [Required user data]
- [Setup completion flags]
- [Default configurations]

## RECOMMENDED WEB APP FIXES

1. [Add missing API call]
2. [Implement user setup flow]
3. [etc...]
```

## CRITICAL QUESTIONS TO ANSWER

1. **What happens immediately after login in the mobile app?**
2. **Is there a user setup/onboarding flow for new users?**
3. **How are workout templates assigned to users?**
4. **What API calls are made to initialize workout data?**
5. **Are there prerequisite API calls before GetUserWorkoutTemplateGroup?**
6. **What user state/flags determine workout availability?**
7. **Is there a default workout program assigned to new users?**

## DEBUGGING APPROACH

1. **Trace the mobile app startup sequence** from login to workout display
2. **Find all API calls** made during this flow
3. **Identify missing calls** in our web app
4. **Determine required user data** for workout scheduling
5. **Implement missing initialization steps** in web app

## SUCCESS CRITERIA

We've found the answer when we can:

- ✅ Identify why our API returns empty workout data
- ✅ Determine what API calls/setup steps we're missing
- ✅ Understand the complete workout initialization flow
- ✅ Know how to fix the "No workout scheduled" issue
