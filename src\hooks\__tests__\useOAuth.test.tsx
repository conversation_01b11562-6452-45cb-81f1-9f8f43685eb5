import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach, Mock } from 'vitest'
import { useOAuth } from '../useOAuth'
import { FirebaseOAuthHelper } from '@/utils/oauth/firebaseOAuth'
import { AppleOAuthHelper } from '@/utils/oauth/appleOAuth'
import { OAuthIntegration } from '@/utils/oauth/oauthIntegration'
import type { OAuthUserData } from '@/types/oauth'

vi.mock('@/config/oauth', () => ({
  oauthConfig: {
    google: {
      clientId: 'test-google-client-id',
      isConfigured: vi.fn().mockReturnValue(true),
    },
    apple: {
      teamId: 'test-team-id',
      bundleId: 'test-bundle-id',
      isConfigured: vi.fn().mockReturnValue(true),
    },
    hasAnyProvider: vi.fn().mockReturnValue(true),
  },
  oauthRedirectUris: {
    google: vi.fn().mockReturnValue('https://app.com/oauth/google'),
    apple: vi.fn().mockReturnValue('https://app.com/oauth/apple'),
  },
}))

vi.mock('@/utils/oauth/firebaseOAuth', () => ({
  FirebaseOAuthHelper: {
    initialize: vi.fn().mockResolvedValue(undefined),
    signInWithGoogle: vi.fn(),
  },
}))

vi.mock('@/utils/oauth/appleOAuth', () => ({
  AppleOAuthHelper: {
    signIn: vi.fn(),
  },
}))

vi.mock('@/utils/oauth/oauthIntegration', () => ({
  OAuthIntegration: {
    handleOAuthSuccess: vi.fn().mockResolvedValue(undefined),
    normalizeError: vi.fn(),
  },
}))

describe('useOAuth', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Configuration', () => {
    it('should return OAuth configuration', () => {
      const { result } = renderHook(() => useOAuth())

      expect(result.current.google).toEqual({
        clientId: 'test-google-client-id',
        isConfigured: true,
        redirectUri: 'https://app.com/oauth/google',
      })

      expect(result.current.apple).toEqual({
        teamId: 'test-team-id',
        bundleId: 'test-bundle-id',
        isConfigured: true,
        redirectUri: 'https://app.com/oauth/apple',
      })

      expect(result.current.hasAnyProvider).toBe(true)
    })

    it('should initialize Firebase OAuth on mount', () => {
      renderHook(() => useOAuth())
      expect(FirebaseOAuthHelper.initialize).toHaveBeenCalled()
    })

    it('should handle Firebase initialization failure gracefully', async () => {
      const consoleError = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})
      ;(FirebaseOAuthHelper.initialize as Mock).mockRejectedValue(
        new Error('Init failed')
      )

      renderHook(() => useOAuth())

      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0))
      })

      expect(consoleError).toHaveBeenCalledWith(
        'Failed to initialize Firebase OAuth for Google:',
        expect.any(Error)
      )
      consoleError.mockRestore()
    })
  })

  describe('Google Sign In', () => {
    it('should handle successful Google sign in', async () => {
      const mockGoogleUser: OAuthUserData = {
        userId: 'google-123',
        email: '<EMAIL>',
        name: 'Test User',
        picture: 'https://example.com/pic.jpg',
      }

      const onSuccess = vi.fn()
      const onError = vi.fn()

      ;(FirebaseOAuthHelper.signInWithGoogle as Mock).mockImplementation(
        (successCallback) => {
          successCallback(mockGoogleUser)
          return Promise.resolve()
        }
      )

      const { result } = renderHook(() => useOAuth())

      await act(async () => {
        await result.current.signInWithGoogle(onSuccess, onError)
      })

      expect(FirebaseOAuthHelper.signInWithGoogle).toHaveBeenCalled()
      expect(OAuthIntegration.handleOAuthSuccess).toHaveBeenCalledWith(
        mockGoogleUser,
        'google'
      )
      expect(onSuccess).toHaveBeenCalledWith(mockGoogleUser)
      expect(onError).not.toHaveBeenCalled()
      expect(result.current.isLoading).toBeNull()
      expect(result.current.error).toBeNull()
    })

    it('should track loading state during Google sign in', async () => {
      let resolveSignIn: () => void
      const signInPromise = new Promise<void>((resolve) => {
        resolveSignIn = resolve
      })

      ;(FirebaseOAuthHelper.signInWithGoogle as Mock).mockImplementation(
        () => signInPromise
      )

      const { result } = renderHook(() => useOAuth())

      expect(result.current.isLoading).toBeNull()

      act(() => {
        result.current.signInWithGoogle()
      })

      expect(result.current.isLoading).toBe('google')

      await act(async () => {
        resolveSignIn!()
        await signInPromise
      })

      expect(result.current.isLoading).toBeNull()
    })

    it('should handle Google sign in errors', async () => {
      const mockError = new Error('Google auth failed')
      const onSuccess = vi.fn()
      const onError = vi.fn()

      ;(FirebaseOAuthHelper.signInWithGoogle as Mock).mockImplementation(
        (_, errorCallback) => {
          errorCallback(mockError)
          return Promise.resolve()
        }
      )
      ;(OAuthIntegration.normalizeError as Mock).mockReturnValue({
        message: 'Google authentication failed',
        code: 'AUTH_FAILED',
        provider: 'google',
      })

      const { result } = renderHook(() => useOAuth())

      await act(async () => {
        await result.current.signInWithGoogle(onSuccess, onError)
      })

      expect(OAuthIntegration.normalizeError).toHaveBeenCalledWith(
        mockError,
        'google'
      )
      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Google authentication failed',
          code: 'AUTH_FAILED',
          provider: 'google',
        })
      )
      expect(onSuccess).not.toHaveBeenCalled()
      expect(result.current.error).toBe('Google authentication failed')
      expect(result.current.isLoading).toBeNull()
    })

    it('should handle unexpected errors during Google sign in', async () => {
      const onError = vi.fn()

      ;(FirebaseOAuthHelper.signInWithGoogle as Mock).mockRejectedValue(
        'String error'
      )

      const { result } = renderHook(() => useOAuth())

      await act(async () => {
        await result.current.signInWithGoogle(undefined, onError)
      })

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Google sign-in failed',
        })
      )
      expect(result.current.error).toBe('Google sign-in failed')
    })
  })

  describe('Apple Sign In', () => {
    it('should handle successful Apple sign in', async () => {
      const mockAppleUser: OAuthUserData = {
        userId: 'apple-123',
        email: '<EMAIL>',
        name: 'Apple User',
      }

      const onSuccess = vi.fn()
      const onError = vi.fn()

      ;(AppleOAuthHelper.signIn as Mock).mockImplementation(
        (successCallback) => {
          successCallback(mockAppleUser)
          return Promise.resolve()
        }
      )

      const { result } = renderHook(() => useOAuth())

      await act(async () => {
        await result.current.signInWithApple(onSuccess, onError)
      })

      expect(AppleOAuthHelper.signIn).toHaveBeenCalled()
      expect(OAuthIntegration.handleOAuthSuccess).toHaveBeenCalledWith(
        mockAppleUser,
        'apple'
      )
      expect(onSuccess).toHaveBeenCalledWith(mockAppleUser)
      expect(onError).not.toHaveBeenCalled()
      expect(result.current.isLoading).toBeNull()
      expect(result.current.error).toBeNull()
    })

    it('should track loading state during Apple sign in', async () => {
      let resolveSignIn: () => void
      const signInPromise = new Promise<void>((resolve) => {
        resolveSignIn = resolve
      })

      ;(AppleOAuthHelper.signIn as Mock).mockImplementation(() => signInPromise)

      const { result } = renderHook(() => useOAuth())

      expect(result.current.isLoading).toBeNull()

      act(() => {
        result.current.signInWithApple()
      })

      expect(result.current.isLoading).toBe('apple')

      await act(async () => {
        resolveSignIn!()
        await signInPromise
      })

      expect(result.current.isLoading).toBeNull()
    })

    it('should handle Apple sign in errors', async () => {
      const mockError = new Error('Apple auth failed')
      const onSuccess = vi.fn()
      const onError = vi.fn()

      ;(AppleOAuthHelper.signIn as Mock).mockImplementation(
        (_, errorCallback) => {
          errorCallback(mockError)
          return Promise.resolve()
        }
      )
      ;(OAuthIntegration.normalizeError as Mock).mockReturnValue({
        message: 'Apple authentication failed',
        code: 'AUTH_FAILED',
        provider: 'apple',
      })

      const { result } = renderHook(() => useOAuth())

      await act(async () => {
        await result.current.signInWithApple(onSuccess, onError)
      })

      expect(OAuthIntegration.normalizeError).toHaveBeenCalledWith(
        mockError,
        'apple'
      )
      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Apple authentication failed',
          code: 'AUTH_FAILED',
          provider: 'apple',
        })
      )
      expect(onSuccess).not.toHaveBeenCalled()
      expect(result.current.error).toBe('Apple authentication failed')
      expect(result.current.isLoading).toBeNull()
    })

    it('should handle unexpected errors during Apple sign in', async () => {
      const onError = vi.fn()

      ;(AppleOAuthHelper.signIn as Mock).mockRejectedValue('String error')

      const { result } = renderHook(() => useOAuth())

      await act(async () => {
        await result.current.signInWithApple(undefined, onError)
      })

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Apple sign-in failed',
        })
      )
      expect(result.current.error).toBe('Apple sign-in failed')
    })
  })

  describe('Error State Management', () => {
    it('should clear error state when starting new sign in', async () => {
      ;(FirebaseOAuthHelper.signInWithGoogle as Mock).mockImplementation(() =>
        Promise.resolve()
      )

      const { result } = renderHook(() => useOAuth())

      // Set initial error
      act(() => {
        result.current.signInWithGoogle()
      })

      // Manually set error to simulate previous failure
      await act(async () => {
        ;(FirebaseOAuthHelper.signInWithGoogle as Mock).mockImplementation(
          (_, errorCallback) => {
            errorCallback(new Error('Previous error'))
            return Promise.resolve()
          }
        )
        ;(OAuthIntegration.normalizeError as Mock).mockReturnValue({
          message: 'Previous error',
          code: 'ERROR',
          provider: 'google',
        })
        await result.current.signInWithGoogle()
      })

      expect(result.current.error).toBe('Previous error')

      // Start new sign in
      ;(FirebaseOAuthHelper.signInWithGoogle as Mock).mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      act(() => {
        result.current.signInWithGoogle()
      })

      // Error should be cleared
      expect(result.current.error).toBeNull()
    })
  })

  describe('Concurrent Sign In Prevention', () => {
    it('should track which provider is currently loading', async () => {
      ;(FirebaseOAuthHelper.signInWithGoogle as Mock).mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )
      ;(AppleOAuthHelper.signIn as Mock).mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      const { result } = renderHook(() => useOAuth())

      // Start Google sign in
      act(() => {
        result.current.signInWithGoogle()
      })

      expect(result.current.isLoading).toBe('google')

      // Try Apple sign in while Google is loading
      act(() => {
        result.current.signInWithApple()
      })

      // Should switch to Apple
      expect(result.current.isLoading).toBe('apple')
    })
  })
})
