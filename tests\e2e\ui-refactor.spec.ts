import { test, expect } from '@playwright/test'

test.describe('UI Refactoring Tests', () => {
  test('refactored components render correctly', async ({ page }) => {
    // Navigate to login page which uses refactored components
    await page.goto('/login')

    // Check that the login form is visible
    await expect(page.getByRole('heading', { name: 'Sign in' })).toBeVisible()

    // Test SetInputs components functionality
    await page.goto('/workout/exercise/1')

    // Wait for page to load
    await page.waitForLoadState('networkidle')

    // Check if the refactored input components are present
    const repsInput = page.locator('#reps-input')
    const weightInput = page.locator('#weight-input')

    // Check if inputs exist
    await expect(repsInput).toBeVisible({ timeout: 10000 })
    await expect(weightInput).toBeVisible({ timeout: 10000 })

    // Test input functionality
    await repsInput.fill('10')
    await expect(repsInput).toHaveValue('10')

    await weightInput.fill('100')
    await expect(weightInput).toHaveValue('100')

    // Test quick select buttons
    const quickButton = page.getByRole('button', { name: '12' }).first()
    if (await quickButton.isVisible()) {
      await quickButton.click()
      await expect(repsInput).toHaveValue('12')
    }
  })

  test('animated counter displays values correctly', async ({ page }) => {
    // Navigate to home page which uses AnimatedCounter
    await page.goto('/')

    // Wait for counters to be visible
    await page.waitForSelector('[data-testid="animated-counter"]', {
      state: 'visible',
      timeout: 10000,
    })

    // Check that counters exist
    const counters = page.locator('[data-testid="animated-counter"]')
    const count = await counters.count()
    expect(count).toBeGreaterThan(0)

    // Check that counter values are displayed
    const firstCounterValue = page
      .locator('[data-testid="animated-counter-value"]')
      .first()
    await expect(firstCounterValue).toBeVisible()

    // Verify the counter has some text content
    const text = await firstCounterValue.textContent()
    expect(text).toBeDefined()
  })
})
