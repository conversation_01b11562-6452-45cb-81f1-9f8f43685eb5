import { describe, it, expect, expectTypeOf } from 'vitest'
import type {
  UIState,
  LoadingState,
  ErrorState,
  LoginFormData,
  RegisterFormData,
  SetFormData,
  ComponentProps,
  AuthStoreState,
  WorkoutStoreState,
  ValidationError,
  APIError,
  AsyncState,
} from '@/types/app'

describe('App Types', () => {
  describe('UI State Types', () => {
    it('should correctly type UIState', () => {
      const uiState: UIState = {
        isMobileMenuOpen: false,
        isRIRModalOpen: false,
        activeModal: null,
        toastMessage: 'Success!',
        isKeyboardVisible: true,
      }

      expectTypeOf(uiState).toMatchTypeOf<UIState>()
      expect(uiState.isMobileMenuOpen).toBe(false)
    })

    it('should correctly type LoadingState', () => {
      const loadingState: LoadingState = {
        isLoading: true,
        loadingMessage: 'Fetching workout...',
        progress: 50,
      }

      expectTypeOf(loadingState).toMatchTypeOf<LoadingState>()
      expect(loadingState.isLoading).toBe(true)
    })

    it('should correctly type ErrorState', () => {
      const errorState: ErrorState = {
        hasError: true,
        errorMessage: 'Network error',
        errorCode: 'NETWORK_ERROR',
        errorDetails: { status: 500 },
      }

      expectTypeOf(errorState).toMatchTypeOf<ErrorState>()
      expect(errorState.hasError).toBe(true)
    })
  })

  describe('Form Data Types', () => {
    it('should correctly type LoginFormData', () => {
      const loginForm: LoginFormData = {
        email: '<EMAIL>',
        password: 'securePassword123',
        rememberMe: true,
      }

      expectTypeOf(loginForm).toMatchTypeOf<LoginFormData>()
      expect(loginForm.email).toBe('<EMAIL>')
    })

    it('should correctly type RegisterFormData', () => {
      const registerForm: RegisterFormData = {
        email: '<EMAIL>',
        password: 'securePassword123',
        confirmPassword: 'securePassword123',
        firstName: 'John',
        lastName: 'Doe',
        birthDate: '1990-01-01',
        massUnit: 'lbs',
        gender: 'male',
        isAthlete: true,
        isVegan: false,
        bodyWeight: 180,
        acceptTerms: true,
      }

      expectTypeOf(registerForm).toMatchTypeOf<RegisterFormData>()
      expect(registerForm.acceptTerms).toBe(true)
    })

    it('should correctly type SetFormData', () => {
      const setForm: SetFormData = {
        reps: 10,
        weight: 135,
        unit: 'lbs',
        rir: 2,
      }

      expectTypeOf(setForm).toMatchTypeOf<SetFormData>()
      expect(setForm.reps).toBe(10)
    })
  })

  describe('Store State Types', () => {
    it('should correctly type AuthStoreState', () => {
      const authState: AuthStoreState = {
        user: {
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          id: 'user123',
        },
        token: 'access_token',
        refreshToken: 'refresh_token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
        hasHydrated: true,
      }

      expectTypeOf(authState).toMatchTypeOf<AuthStoreState>()
      expect(authState.isAuthenticated).toBe(true)
    })

    it('should correctly type WorkoutStoreState', () => {
      const workoutState: WorkoutStoreState = {
        currentWorkout: null,
        exercises: [],
        currentExerciseIndex: 0,
        currentSetIndex: 0,
        workoutSession: {
          id: 'session123',
          startTime: new Date(),
          exercises: [],
        },
        isLoading: false,
        error: null,
      }

      expectTypeOf(workoutState).toMatchTypeOf<WorkoutStoreState>()
      expect(workoutState.currentExerciseIndex).toBe(0)
    })
  })

  describe('Error Types', () => {
    it('should correctly type ValidationError', () => {
      const validationError: ValidationError = {
        field: 'email',
        message: 'Invalid email format',
        code: 'INVALID_EMAIL',
      }

      expectTypeOf(validationError).toMatchTypeOf<ValidationError>()
      expect(validationError.field).toBe('email')
    })

    it('should correctly type APIError', () => {
      const apiError: APIError = {
        status: 401,
        message: 'Unauthorized',
        code: 'UNAUTHORIZED',
        details: { reason: 'Token expired' },
      }

      expectTypeOf(apiError).toMatchTypeOf<APIError>()
      expect(apiError.status).toBe(401)
    })
  })

  describe('Utility Types', () => {
    it('should correctly type AsyncState', () => {
      const pendingState: AsyncState<string> = {
        status: 'pending',
        data: null,
        error: null,
      }

      const successState: AsyncState<string> = {
        status: 'success',
        data: 'Hello',
        error: null,
      }

      const errorState: AsyncState<string> = {
        status: 'error',
        data: null,
        error: new Error('Failed'),
      }

      expectTypeOf(pendingState).toMatchTypeOf<AsyncState<string>>()
      expectTypeOf(successState).toMatchTypeOf<AsyncState<string>>()
      expectTypeOf(errorState).toMatchTypeOf<AsyncState<string>>()

      expect(pendingState.status).toBe('pending')
      expect(successState.data).toBe('Hello')
      expect(errorState.error).toBeInstanceOf(Error)
    })
  })

  describe('Component Props Types', () => {
    it('should correctly type common component props', () => {
      const componentProps: ComponentProps = {
        className: 'custom-class',
        style: { padding: '10px' },
        children: null,
        onClick: () => {},
        disabled: false,
        'aria-label': 'Button',
        'data-testid': 'test-button',
      }

      expectTypeOf(componentProps).toMatchTypeOf<ComponentProps>()
      expect(componentProps.disabled).toBe(false)
    })
  })
})
