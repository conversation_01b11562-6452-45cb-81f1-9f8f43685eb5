import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { WorkoutOverview } from '../WorkoutOverview'
import * as useWorkoutModule from '@/hooks/useWorkout'
import {
  createMockUseWorkoutReturn,
  createMockWorkoutTemplateGroup,
  createMockExerciseWorkSetsFromExercise,
} from './WorkoutOverview.test.helpers'
import type { ExerciseModel } from '@/types'

// Mock next/navigation
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

describe('WorkoutOverview - Navigation', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Mock useWorkout with valid workout data
    const mockExercises = [
      { Id: 1, Label: 'Bench Press' } as ExerciseModel,
      { Id: 2, Label: 'Shoulder Press' } as ExerciseModel,
    ]

    const mockWorkoutGroup = createMockWorkoutTemplateGroup({
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Upper Body Day',
          IsSystemExercise: true,
          UserId: '',
          Exercises: mockExercises,
          WorkoutSettingsModel: {},
        },
      ],
    })

    const exercises = mockExercises.map((ex) =>
      createMockExerciseWorkSetsFromExercise(ex)
    )

    const mockReturn = createMockUseWorkoutReturn({
      todaysWorkout: [mockWorkoutGroup],
      exercises,
      isLoadingWorkout: false,
      hasInitialData: true,
      currentWorkout: mockWorkoutGroup.WorkoutTemplates?.[0],
    })

    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(mockReturn)
  })

  it('should navigate to exercise page when exercise is clicked', async () => {
    const user = userEvent.setup()
    render(<WorkoutOverview />)

    const exerciseCard = screen.getByText('Bench Press')
    await user.click(exerciseCard)

    expect(mockPush).toHaveBeenCalledWith('/workout/exercise/1')
  })

  it('should call startWorkout when exercise is clicked', async () => {
    const mockStartWorkout = vi.fn()
    const mockExercises = [
      { Id: 1, Label: 'Bench Press' } as ExerciseModel,
      { Id: 2, Label: 'Shoulder Press' } as ExerciseModel,
    ]

    const mockWorkoutGroup = createMockWorkoutTemplateGroup({
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Upper Body Day',
          IsSystemExercise: true,
          UserId: '',
          Exercises: mockExercises,
          WorkoutSettingsModel: {},
        },
      ],
    })

    const exercises = mockExercises.map((ex) =>
      createMockExerciseWorkSetsFromExercise(ex)
    )

    const mockReturn = createMockUseWorkoutReturn({
      todaysWorkout: [mockWorkoutGroup],
      exercises,
      isLoadingWorkout: false,
      hasInitialData: true,
      startWorkout: mockStartWorkout,
    })

    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(mockReturn)

    const user = userEvent.setup()
    render(<WorkoutOverview />)

    const exerciseCard = screen.getByText('Bench Press')
    await user.click(exerciseCard)

    expect(mockStartWorkout).toHaveBeenCalled()
  })

  it('should navigate to first exercise when start button is clicked', async () => {
    const mockStartWorkout = vi.fn()
    const mockExercises = [
      { Id: 1, Label: 'Bench Press' } as ExerciseModel,
      { Id: 2, Label: 'Shoulder Press' } as ExerciseModel,
    ]

    const mockWorkoutGroup = createMockWorkoutTemplateGroup({
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Upper Body Day',
          IsSystemExercise: true,
          UserId: '',
          Exercises: mockExercises,
          WorkoutSettingsModel: {},
        },
      ],
    })

    const exercises = mockExercises.map((ex) =>
      createMockExerciseWorkSetsFromExercise(ex)
    )

    const mockReturn = createMockUseWorkoutReturn({
      todaysWorkout: [mockWorkoutGroup],
      exercises,
      isLoadingWorkout: false,
      hasInitialData: true,
      startWorkout: mockStartWorkout,
    })

    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(mockReturn)

    const user = userEvent.setup()
    render(<WorkoutOverview />)

    const startButton = screen.getByRole('button', { name: /start workout/i })
    await user.click(startButton)

    expect(mockStartWorkout).toHaveBeenCalled()
    expect(mockPush).toHaveBeenCalledWith('/workout/exercise/1')
  })
})
