import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { SetInputs } from '@/components/workout/SetInputs'

describe('SetInputs', () => {
  const mockOnChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  const defaultProps = {
    reps: 8,
    weight: 100,
    unit: 'lbs' as const,
    onChange: mockOnChange,
  }

  describe('Reps Input', () => {
    it('should display reps input with correct value', () => {
      // When
      render(<SetInputs {...defaultProps} />)

      // Then
      const repsInput = screen.getByLabelText('Reps') as HTMLInputElement
      expect(repsInput).toBeInTheDocument()
      expect(repsInput.value).toBe('8')
      expect(repsInput.type).toBe('number')
    })

    it('should call onChange when reps change', async () => {
      // Given
      render(<SetInputs {...defaultProps} />)

      // When
      const repsInput = screen.getByLabelText('Reps')
      fireEvent.change(repsInput, { target: { value: '10' } })

      // Then
      expect(mockOnChange).toHaveBeenCalledWith({ reps: 10, weight: 100 })
    })

    it('should validate reps range', async () => {
      // Given
      render(<SetInputs {...defaultProps} />)

      // When - Try zero (less than minimum)
      const repsInput = screen.getByLabelText('Reps')
      fireEvent.change(repsInput, { target: { value: '0' } })

      // Then
      expect(screen.getByText(/Reps must be at least 1/i)).toBeInTheDocument()

      // When - Try too high
      fireEvent.change(repsInput, { target: { value: '101' } })

      // Then
      expect(screen.getByText(/Reps must be 100 or less/i)).toBeInTheDocument()
    })

    it('should show quick select reps buttons', () => {
      // When
      render(<SetInputs {...defaultProps} />)

      // Then
      const quickButtons = ['5', '8', '10', '12', '15']
      quickButtons.forEach((reps) => {
        expect(screen.getByRole('button', { name: reps })).toBeInTheDocument()
      })
    })

    it('should update reps with quick select', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetInputs {...defaultProps} />)

      // When
      await user.click(screen.getByRole('button', { name: '12' }))

      // Then
      expect(mockOnChange).toHaveBeenCalledWith({ reps: 12, weight: 100 })
    })
  })

  describe('Weight Input', () => {
    it('should display weight input with correct value and unit', () => {
      // When
      render(<SetInputs {...defaultProps} />)

      // Then
      const weightInput = screen.getByLabelText('Weight') as HTMLInputElement
      expect(weightInput).toBeInTheDocument()
      expect(weightInput.value).toBe('100')
      expect(screen.getByText('lbs')).toBeInTheDocument()
    })

    it('should call onChange when weight changes', async () => {
      // Given
      render(<SetInputs {...defaultProps} />)

      // When
      const weightInput = screen.getByLabelText('Weight')
      fireEvent.change(weightInput, { target: { value: '105' } })

      // Then
      expect(mockOnChange).toHaveBeenCalledWith({ reps: 8, weight: 105 })
    })

    it('should support decimal weights', async () => {
      // Given
      render(<SetInputs {...defaultProps} />)

      // When
      const weightInput = screen.getByLabelText('Weight')
      fireEvent.change(weightInput, { target: { value: '102.5' } })

      // Then
      expect(mockOnChange).toHaveBeenCalledWith({ reps: 8, weight: 102.5 })
    })

    it('should validate weight range', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetInputs {...defaultProps} />)

      // When - Try invalid weight (0 is not allowed for non-bodyweight)
      const weightInput = screen.getByLabelText('Weight')
      await user.clear(weightInput)
      // Set value directly for testing validation
      fireEvent.change(weightInput, { target: { value: '0' } })

      // Then
      expect(screen.getByText(/Weight must be at least 1/i)).toBeInTheDocument()

      // When - Try too high
      await user.clear(weightInput)
      await user.type(weightInput, '1001')

      // Then
      expect(
        screen.getByText(/Weight must be 1000 or less/i)
      ).toBeInTheDocument()
    })

    it('should display correct unit', () => {
      // When - lbs
      const { rerender } = render(<SetInputs {...defaultProps} />)
      expect(screen.getByText('lbs')).toBeInTheDocument()

      // When - kg
      rerender(<SetInputs {...defaultProps} unit="kg" />)
      expect(screen.getByText('kg')).toBeInTheDocument()
    })
  })

  describe('Increment/Decrement Controls', () => {
    it('should show increment and decrement buttons for weight', () => {
      // When
      render(<SetInputs {...defaultProps} />)

      // Then
      expect(
        screen.getByRole('button', { name: /decrease weight/i })
      ).toBeInTheDocument()
      expect(
        screen.getByRole('button', { name: /increase weight/i })
      ).toBeInTheDocument()
    })

    it('should increment weight by 5 lbs', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetInputs {...defaultProps} />)

      // When
      await user.click(screen.getByRole('button', { name: /increase weight/i }))

      // Then
      expect(mockOnChange).toHaveBeenCalledWith({ reps: 8, weight: 105 })
    })

    it('should decrement weight by 5 lbs', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetInputs {...defaultProps} />)

      // When
      await user.click(screen.getByRole('button', { name: /decrease weight/i }))

      // Then
      expect(mockOnChange).toHaveBeenCalledWith({ reps: 8, weight: 95 })
    })

    it('should increment weight by 2.5 kg when unit is kg', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetInputs {...defaultProps} unit="kg" weight={50} />)

      // When
      await user.click(screen.getByRole('button', { name: /increase weight/i }))

      // Then
      expect(mockOnChange).toHaveBeenCalledWith({ reps: 8, weight: 52.5 })
    })

    it('should not go below 0 when decrementing', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetInputs {...defaultProps} weight={2} />)

      // When
      await user.click(screen.getByRole('button', { name: /decrease weight/i }))

      // Then
      expect(mockOnChange).toHaveBeenCalledWith({ reps: 8, weight: 0 })
    })
  })

  describe('Plate Calculator', () => {
    it('should show plate calculator button', () => {
      // When
      render(<SetInputs {...defaultProps} />)

      // Then
      expect(
        screen.getByRole('button', { name: /plate calculator/i })
      ).toBeInTheDocument()
    })

    it('should open plate calculator modal', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetInputs {...defaultProps} />)

      // When
      await user.click(
        screen.getByRole('button', { name: /plate calculator/i })
      )

      // Then
      await waitFor(() => {
        expect(
          screen.getByText(/Plates needed for 100 lbs/i)
        ).toBeInTheDocument()
      })
      // The plate calculator should show the breakdown
      // 100 lbs - 45 lb bar = 55 lbs to load
      // This would be 2x25 lb plates + 2x2.5 lb plates
      const modal = screen
        .getByText(/Plates needed for 100 lbs/i)
        .closest('div')
      expect(modal).toHaveTextContent('25 lbs')
      expect(modal).toHaveTextContent('× 2')
      expect(modal).toHaveTextContent('2.5 lbs')
    })

    it('should calculate plates correctly for different weights', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetInputs {...defaultProps} weight={135} />)

      // When
      await user.click(
        screen.getByRole('button', { name: /plate calculator/i })
      )

      // Then
      await waitFor(() => {
        expect(
          screen.getByText(/Plates needed for 135 lbs/i)
        ).toBeInTheDocument()
      })
      // 135 lbs - 45 lb bar = 90 lbs = 2x45 lb plates
      const modal = screen
        .getByText(/Plates needed for 135 lbs/i)
        .closest('div')
      expect(modal).toHaveTextContent('45 lbs')
    })

    it('should show plate calculator for kg', async () => {
      // Given
      const user = userEvent.setup()
      render(<SetInputs {...defaultProps} unit="kg" weight={60} />)

      // When
      await user.click(
        screen.getByRole('button', { name: /plate calculator/i })
      )

      // Then
      await waitFor(() => {
        expect(screen.getByText(/Plates needed for 60 kg/i)).toBeInTheDocument()
      })
      // 60 kg - 20 kg bar = 40 kg = 2x20 kg plates
      const modal = screen.getByText(/Plates needed for 60 kg/i).closest('div')
      expect(modal).toHaveTextContent('20 kg')
    })
  })

  describe('Time-Based Mode', () => {
    it('should show duration input for time-based exercises', () => {
      // When
      render(<SetInputs {...defaultProps} isTimeBased duration={45} />)

      // Then
      const durationInput = screen.getByLabelText(
        /Duration/i
      ) as HTMLInputElement
      expect(durationInput).toBeInTheDocument()
      expect(durationInput.value).toBe('45')
      expect(screen.getByText('seconds')).toBeInTheDocument()
    })

    it('should not show reps input for time-based exercises', () => {
      // When
      render(<SetInputs {...defaultProps} isTimeBased duration={45} />)

      // Then
      expect(screen.queryByLabelText(/^Reps$/)).not.toBeInTheDocument()
    })

    it('should update duration', async () => {
      // Given
      render(<SetInputs {...defaultProps} isTimeBased duration={45} />)

      // When
      const durationInput = screen.getByLabelText(/Duration/i)
      fireEvent.change(durationInput, { target: { value: '60' } })

      // Then
      expect(mockOnChange).toHaveBeenCalledWith({ weight: 100, duration: 60 })
    })

    it('should show quick duration buttons', () => {
      // When
      render(<SetInputs {...defaultProps} isTimeBased duration={45} />)

      // Then
      const quickButtons = ['30', '45', '60', '90']
      quickButtons.forEach((seconds) => {
        expect(
          screen.getByRole('button', { name: `${seconds}s` })
        ).toBeInTheDocument()
      })
    })
  })

  describe('Bodyweight Mode', () => {
    it('should show additional weight label for bodyweight', () => {
      // When
      render(<SetInputs {...defaultProps} isBodyweight />)

      // Then
      expect(screen.getByLabelText(/Additional Weight/i)).toBeInTheDocument()
    })

    it('should allow 0 weight for bodyweight exercises', async () => {
      // Given
      render(<SetInputs {...defaultProps} isBodyweight weight={0} />)

      // When
      const weightInput = screen.getByLabelText(/Additional Weight/i)
      fireEvent.change(weightInput, { target: { value: '0' } })

      // Then
      // Should not show error for 0 weight on bodyweight exercises
      expect(
        screen.queryByText(/Weight must be at least/i)
      ).not.toBeInTheDocument()
    })
  })

  describe('Disabled State', () => {
    it('should disable all inputs when disabled prop is true', () => {
      // When
      render(<SetInputs {...defaultProps} disabled />)

      // Then
      expect(screen.getByLabelText('Reps')).toBeDisabled()
      expect(screen.getByLabelText('Weight')).toBeDisabled()
      expect(
        screen.getByRole('button', { name: /increase weight/i })
      ).toBeDisabled()
      expect(
        screen.getByRole('button', { name: /decrease weight/i })
      ).toBeDisabled()
      expect(
        screen.getByRole('button', { name: /plate calculator/i })
      ).toBeDisabled()
    })
  })

  describe('Accessibility', () => {
    it('should have proper input types and attributes', () => {
      // When
      render(<SetInputs {...defaultProps} />)

      // Then
      const repsInput = screen.getByLabelText('Reps')
      expect(repsInput).toHaveAttribute('type', 'number')
      expect(repsInput).toHaveAttribute('min', '1')
      expect(repsInput).toHaveAttribute('max', '100')
      expect(repsInput).toHaveAttribute('inputMode', 'numeric')

      const weightInput = screen.getByLabelText('Weight')
      expect(weightInput).toHaveAttribute('type', 'number')
      expect(weightInput).toHaveAttribute('min', '0')
      expect(weightInput).toHaveAttribute('max', '1000')
      expect(weightInput).toHaveAttribute('step', '0.5')
      expect(weightInput).toHaveAttribute('inputMode', 'decimal')
    })

    it('should announce errors to screen readers', async () => {
      // Given
      render(<SetInputs {...defaultProps} />)

      // When
      const repsInput = screen.getByLabelText('Reps') as HTMLInputElement
      fireEvent.change(repsInput, { target: { value: '0' } })

      // Then
      await waitFor(() => {
        const error = screen.getByText(/Reps must be at least 1/i)
        expect(error).toHaveAttribute('role', 'alert')
      })
    })

    it('should have keyboard navigation support', async () => {
      // Given
      render(<SetInputs {...defaultProps} />)

      // When
      const repsInput = screen.getByLabelText('Reps')
      const weightInput = screen.getByLabelText('Weight')

      // Then - verify tab indices allow navigation
      expect(repsInput).toBeInTheDocument()
      expect(weightInput).toBeInTheDocument()
      // Tab navigation is handled by browser, not testable in jsdom
    })
  })
})
