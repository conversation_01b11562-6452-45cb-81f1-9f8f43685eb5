'use client'

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useMemo,
} from 'react'
import { themeConfig, type ThemeName } from '@/config/theme'

interface ThemeContextType {
  theme: ThemeName
  setTheme: (theme: ThemeName) => void
  config: (typeof themeConfig.themes)[ThemeName]
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setThemeState] = useState<ThemeName>(themeConfig.defaultTheme)
  const [mounted, setMounted] = useState(false)

  // Load theme from localStorage on mount
  useEffect(() => {
    setMounted(true)
    const savedTheme = localStorage.getItem('dr-muscle-theme') as ThemeName
    if (savedTheme && savedTheme in themeConfig.themes) {
      setThemeState(savedTheme)
    }
  }, [])

  // Apply theme to document
  useEffect(() => {
    if (!mounted) return

    // Apply theme attribute to html element
    document.documentElement.setAttribute('data-theme', theme)

    // Apply CSS variables
    const themeData = themeConfig.themes[theme]
    const root = document.documentElement

    // Colors
    Object.entries(themeData.colors).forEach(([category, values]) => {
      Object.entries(values).forEach(([key, value]) => {
        root.style.setProperty(`--color-${category}-${key}`, value)
      })
    })

    // Typography
    root.style.setProperty('--font-heading', themeData.typography.heading)
    root.style.setProperty('--font-body', themeData.typography.body)

    // Shadows
    Object.entries(themeData.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--shadow-${key}`, value)
    })

    // Borders
    Object.entries(themeData.borders.radius).forEach(([key, value]) => {
      root.style.setProperty(`--radius-${key}`, value)
    })
  }, [theme, mounted])

  const setTheme = (newTheme: ThemeName) => {
    setThemeState(newTheme)
    localStorage.setItem('dr-muscle-theme', newTheme)
  }

  // Memoize context value to prevent re-renders
  const contextValue = useMemo(
    () => ({
      theme,
      setTheme,
      config: themeConfig.themes[theme],
    }),
    [theme]
  )

  // Prevent flash of wrong theme
  if (!mounted) {
    return null
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
