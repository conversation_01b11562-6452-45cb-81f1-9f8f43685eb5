import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ProgramDescription } from '../ProgramDescription'
import React from 'react'

describe('ProgramDescription', () => {
  const shortDescription = 'This is a short description.'
  const longDescription = `This is a very long description that spans multiple lines. 
    It contains detailed information about the program and its benefits. 
    The description goes on and on to test the truncation functionality. 
    We need to make sure that the expand and collapse feature works properly 
    when the content exceeds the maximum number of lines specified.`

  describe('Basic rendering', () => {
    it('should render short description without truncation', () => {
      render(<ProgramDescription description={shortDescription} />)

      expect(screen.getByText(shortDescription)).toBeInTheDocument()
      expect(screen.queryByText('Read more')).not.toBeInTheDocument()
    })

    it('should render loading skeleton when loading', () => {
      render(<ProgramDescription description={shortDescription} isLoading />)

      expect(screen.getByTestId('description-skeleton')).toBeInTheDocument()
      expect(screen.queryByText(shortDescription)).not.toBeInTheDocument()
    })

    it('should handle empty description gracefully', () => {
      render(<ProgramDescription description="" />)

      expect(screen.getByTestId('program-description')).toBeInTheDocument()
      expect(screen.queryByText('Read more')).not.toBeInTheDocument()
    })
  })

  describe('Truncation and expansion', () => {
    it('should truncate long description and show Read more button', () => {
      render(<ProgramDescription description={longDescription} maxLines={3} />)

      const container = screen.getByTestId('program-description')
      expect(container).toHaveClass('line-clamp-3')
      expect(screen.getByText('Read more')).toBeInTheDocument()
    })

    it('should expand description when Read more is clicked', async () => {
      render(<ProgramDescription description={longDescription} maxLines={3} />)

      const readMoreButton = screen.getByText('Read more')
      fireEvent.click(readMoreButton)

      await waitFor(() => {
        const container = screen.getByTestId('program-description')
        expect(container).not.toHaveClass('line-clamp-3')
        expect(screen.getByText('Show less')).toBeInTheDocument()
      })
    })

    it('should collapse description when Show less is clicked', async () => {
      render(<ProgramDescription description={longDescription} maxLines={3} />)

      // First expand
      fireEvent.click(screen.getByText('Read more'))

      // Then collapse
      await waitFor(() => {
        fireEvent.click(screen.getByText('Show less'))
      })

      await waitFor(() => {
        const container = screen.getByTestId('program-description')
        expect(container).toHaveClass('line-clamp-3')
        expect(screen.getByText('Read more')).toBeInTheDocument()
      })
    })

    it('should respect custom maxLines prop', () => {
      render(<ProgramDescription description={longDescription} maxLines={2} />)

      const container = screen.getByTestId('program-description')
      expect(container).toHaveClass('line-clamp-2')
    })
  })

  describe('Animation', () => {
    it('should animate height on expand/collapse', async () => {
      render(<ProgramDescription description={longDescription} maxLines={3} />)

      const descriptionEl = screen.getByTestId('program-description')

      // Check initial state has transition class
      expect(descriptionEl).toHaveClass('transition-all')

      // Expand
      fireEvent.click(screen.getByText('Read more'))

      await waitFor(() => {
        expect(descriptionEl).toHaveClass('transition-all')
      })
    })

    it('should respect prefers-reduced-motion', () => {
      // Mock matchMedia
      window.matchMedia = vi.fn().mockImplementation((query) => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      }))

      render(<ProgramDescription description={longDescription} maxLines={3} />)

      const descriptionEl = screen.getByTestId('program-description')
      expect(descriptionEl).toHaveClass('duration-0')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<ProgramDescription description={longDescription} maxLines={3} />)

      const readMoreButton = screen.getByText('Read more')
      expect(readMoreButton).toHaveAttribute('aria-expanded', 'false')

      fireEvent.click(readMoreButton)

      waitFor(() => {
        const showLessButton = screen.getByText('Show less')
        expect(showLessButton).toHaveAttribute('aria-expanded', 'true')
      })
    })

    it('should have proper button role and label', () => {
      render(<ProgramDescription description={longDescription} maxLines={3} />)

      const button = screen.getByRole('button', { name: /read more/i })
      expect(button).toBeInTheDocument()
    })
  })

  describe('Styling', () => {
    it('should apply custom className', () => {
      render(
        <ProgramDescription
          description={shortDescription}
          className="custom-class"
        />
      )

      const wrapper = screen.getByTestId('program-description-wrapper')
      expect(wrapper).toHaveClass('custom-class')
    })

    it('should have proper mobile typography', () => {
      render(<ProgramDescription description={shortDescription} />)

      const descriptionEl = screen.getByTestId('program-description')
      expect(descriptionEl).toHaveClass('text-base') // Base font size for mobile
      expect(descriptionEl).toHaveClass('leading-relaxed') // Good line height
    })

    it('should support dark mode', () => {
      render(
        <div className="dark">
          <ProgramDescription description={shortDescription} />
        </div>
      )

      const descriptionEl = screen.getByTestId('program-description')
      expect(descriptionEl).toHaveClass('dark:text-gray-300')
    })

    it('should have proper touch target size for button', () => {
      render(<ProgramDescription description={longDescription} maxLines={3} />)

      const button = screen.getByText('Read more')
      expect(button).toHaveClass('min-h-[44px]') // iOS touch target guideline
    })
  })

  describe('HTML content safety', () => {
    it('should render plain text safely', () => {
      const unsafeContent = '<script>alert("XSS")</script>Some text'
      render(<ProgramDescription description={unsafeContent} />)

      // Should render as text, not execute script
      expect(screen.getByText(unsafeContent)).toBeInTheDocument()
    })
  })
})
