import { useState, useEffect, useRef, useCallback } from 'react'

interface UseTimerOptions {
  onComplete?: () => void
  onTick?: (timeRemaining: number) => void
  autoStart?: boolean
}

interface UseTimerReturn {
  timeRemaining: number
  isRunning: boolean
  isPaused: boolean
  isComplete: boolean
  progress: number
  formattedTime: string
  start: () => void
  pause: () => void
  resume: () => void
  reset: () => void
  skip: () => void
}

export function useTimer(
  duration: number,
  options: UseTimerOptions = {}
): UseTimerReturn {
  const { onComplete, onTick, autoStart = false } = options

  // Ensure duration is non-negative
  const safeDuration = Math.max(0, duration)

  const [timeRemaining, setTimeRemaining] = useState(safeDuration)
  const [isRunning, setIsRunning] = useState(autoStart && safeDuration > 0)
  const [isPaused, setIsPaused] = useState(false)

  const startTimeRef = useRef<number | null>(null)
  const pausedTimeRef = useRef<number>(0)
  const animationFrameRef = useRef<number | null>(null)
  const lastTickRef = useRef<number>(safeDuration)

  // Calculate derived states
  const isComplete = timeRemaining === 0
  const progress = safeDuration > 0 ? (timeRemaining / safeDuration) * 100 : 0

  // Format time as MM:SS
  const formattedTime = useCallback(() => {
    const minutes = Math.floor(timeRemaining / 60)
    const seconds = timeRemaining % 60
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }, [timeRemaining])

  // Update timer using requestAnimationFrame for smooth updates
  const updateTimer = useCallback(() => {
    if (!startTimeRef.current || !isRunning || isPaused) return

    const elapsed =
      (Date.now() - startTimeRef.current - pausedTimeRef.current) / 1000
    const newTimeRemaining = Math.max(0, Math.round(safeDuration - elapsed))

    // Only update if the second has changed
    if (newTimeRemaining !== lastTickRef.current) {
      lastTickRef.current = newTimeRemaining
      setTimeRemaining(newTimeRemaining)

      // Call onTick if provided
      if (onTick && newTimeRemaining < safeDuration) {
        onTick(newTimeRemaining)
      }

      // Check if complete
      if (newTimeRemaining === 0) {
        setIsRunning(false)
        if (onComplete) {
          onComplete()
        }
        return
      }
    }

    // Continue animation loop
    if (typeof window !== 'undefined' && window.requestAnimationFrame) {
      animationFrameRef.current = window.requestAnimationFrame(updateTimer)
    } else if (typeof requestAnimationFrame !== 'undefined') {
      animationFrameRef.current = requestAnimationFrame(updateTimer)
    }
  }, [isRunning, isPaused, safeDuration, onComplete, onTick])

  // Start timer
  const start = useCallback(() => {
    if (timeRemaining === 0) return

    startTimeRef.current = Date.now()
    pausedTimeRef.current = 0
    lastTickRef.current = timeRemaining
    setIsRunning(true)
    setIsPaused(false)
  }, [timeRemaining])

  // Pause timer
  const pause = useCallback(() => {
    if (!isRunning || isPaused) return

    pausedTimeRef.current += Date.now() - (startTimeRef.current || Date.now())
    setIsPaused(true)
    setIsRunning(false)

    // Cancel animation frame when pausing
    if (animationFrameRef.current) {
      if (typeof window !== 'undefined' && window.cancelAnimationFrame) {
        window.cancelAnimationFrame(animationFrameRef.current)
      } else if (typeof cancelAnimationFrame !== 'undefined') {
        cancelAnimationFrame(animationFrameRef.current)
      }
      animationFrameRef.current = null
    }
  }, [isRunning, isPaused])

  // Resume timer
  const resume = useCallback(() => {
    if (!isPaused) return

    startTimeRef.current = Date.now()
    setIsRunning(true)
    setIsPaused(false)
  }, [isPaused])

  // Reset timer
  const reset = useCallback(() => {
    setTimeRemaining(safeDuration)
    setIsRunning(false)
    setIsPaused(false)
    startTimeRef.current = null
    pausedTimeRef.current = 0
    lastTickRef.current = safeDuration

    if (animationFrameRef.current) {
      if (typeof window !== 'undefined' && window.cancelAnimationFrame) {
        window.cancelAnimationFrame(animationFrameRef.current)
      } else if (typeof cancelAnimationFrame !== 'undefined') {
        cancelAnimationFrame(animationFrameRef.current)
      }
      animationFrameRef.current = null
    }
  }, [safeDuration])

  // Skip timer (complete immediately)
  const skip = useCallback(() => {
    setTimeRemaining(0)
    setIsRunning(false)
    setIsPaused(false)

    if (animationFrameRef.current) {
      if (typeof window !== 'undefined' && window.cancelAnimationFrame) {
        window.cancelAnimationFrame(animationFrameRef.current)
      } else if (typeof cancelAnimationFrame !== 'undefined') {
        cancelAnimationFrame(animationFrameRef.current)
      }
      animationFrameRef.current = null
    }

    if (onComplete) {
      onComplete()
    }
  }, [onComplete])

  // Handle auto-start
  useEffect(() => {
    if (autoStart && safeDuration > 0 && !startTimeRef.current) {
      startTimeRef.current = Date.now()
      pausedTimeRef.current = 0
      lastTickRef.current = safeDuration
    }
  }, [autoStart, safeDuration])

  // Handle duration changes
  useEffect(() => {
    if (!isRunning && !isPaused) {
      setTimeRemaining(safeDuration)
      lastTickRef.current = safeDuration
    }
  }, [safeDuration, isRunning, isPaused])

  // Start/stop animation loop
  useEffect(() => {
    if (isRunning && !isPaused) {
      if (typeof window !== 'undefined' && window.requestAnimationFrame) {
        animationFrameRef.current = window.requestAnimationFrame(updateTimer)
      } else if (typeof requestAnimationFrame !== 'undefined') {
        animationFrameRef.current = requestAnimationFrame(updateTimer)
      }
    } else if (animationFrameRef.current) {
      if (typeof window !== 'undefined' && window.cancelAnimationFrame) {
        window.cancelAnimationFrame(animationFrameRef.current)
      } else if (typeof cancelAnimationFrame !== 'undefined') {
        cancelAnimationFrame(animationFrameRef.current)
      }
      animationFrameRef.current = null
    }

    return () => {
      if (animationFrameRef.current) {
        if (typeof window !== 'undefined' && window.cancelAnimationFrame) {
          window.cancelAnimationFrame(animationFrameRef.current)
        } else if (typeof cancelAnimationFrame !== 'undefined') {
          cancelAnimationFrame(animationFrameRef.current)
        }
      }
    }
  }, [isRunning, isPaused, updateTimer])

  // Handle initial completion state
  useEffect(() => {
    if (safeDuration === 0 && onComplete && !isRunning && !isPaused) {
      // Call onComplete on next tick to avoid calling during render
      const timer = setTimeout(onComplete, 0)
      return () => clearTimeout(timer)
    }
    // Return undefined cleanup function for other cases
    return undefined
  }, [safeDuration, onComplete, isRunning, isPaused])

  return {
    timeRemaining,
    isRunning,
    isPaused,
    isComplete,
    progress,
    formattedTime: formattedTime(),
    start,
    pause,
    resume,
    reset,
    skip,
  }
}
