import { test, expect } from '@playwright/test'

test.describe('Program Page Stats Display', () => {
  test('should display weeks streak, workouts completed, and lbs lifted', async ({
    page,
  }) => {
    // Go to login page
    await page.goto('/')

    // Login with test account
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('**/program', { timeout: 10000 })

    // Wait for the page to load - look for the program content
    await page.waitForSelector('[role="main"]', { timeout: 15000 })

    // Wait a bit for stats to load
    await page.waitForTimeout(2000)

    // Check weeks streak value
    const weeksStreakText = await page
      .locator('text="Weeks streak"')
      .locator('..')
      .locator('text=/\\d+/')
      .first()
      .textContent()
    // console.log('Weeks streak:', weeksStreakText)
    expect(parseInt(weeksStreakText || '0')).toBeGreaterThan(0)

    // Check workouts completed value
    const workoutsText = await page
      .locator('text="Workouts"')
      .locator('..')
      .locator('text=/\\d+/')
      .first()
      .textContent()
    // console.log('Workouts completed:', workoutsText)
    expect(parseInt(workoutsText || '0')).toBeGreaterThan(0)

    // Check lbs lifted value
    const lbsText = await page
      .locator('text="Lbs lifted"')
      .locator('..')
      .locator('text=/\\d+/')
      .first()
      .textContent()
    // console.log('Lbs lifted:', lbsText)
    expect(parseInt(lbsText || '0')).toBeGreaterThan(0)

    // Verify the specific values we expect from the complete API
    // Week streak should be 1 (based on ConsecutiveWeeks array last entry)
    // Workouts should be 79 (from HistoryExerciseModel.TotalWorkoutCompleted)
    // Lbs should be 848978 (from HistoryExerciseModel.TotalWeight.Lb)
    expect(parseInt(weeksStreakText || '0')).toBeGreaterThanOrEqual(1)
    expect(parseInt(workoutsText || '0')).toBe(79)
    expect(parseInt(lbsText || '0')).toBeGreaterThan(800000) // 848,978 lbs

    // Take a screenshot for verification
    await page.screenshot({ path: 'program-stats.png', fullPage: false })
  })
})
