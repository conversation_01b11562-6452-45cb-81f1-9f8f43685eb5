import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useUserInfo } from '../useUserInfo'
import { useAuthStore } from '@/stores/authStore'
import { authApi } from '@/api/auth'
import { userInfoPerformance } from '@/utils/userInfoPerformance'
import { shouldClearCacheOnError } from '@/utils/apiRetry'
import React from 'react'

// Mock dependencies
vi.mock('@/stores/authStore')
vi.mock('@/api/auth')
vi.mock('@/utils/userInfoPerformance')
vi.mock('@/utils/apiRetry')

// Create a wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
  return wrapper
}

describe('useUserInfo', () => {
  const mockUser = { email: '<EMAIL>' }
  const mockUpdateUser = vi.fn()
  const mockGetCachedUserInfo = vi.fn()
  const mockSetCachedUserInfo = vi.fn()
  const mockIsCacheStale = vi.fn()
  const mockClearUserInfoCache = vi.fn()
  const mockStartSession = vi.fn().mockReturnValue('session-123')
  const mockEndSession = vi.fn()
  const mockRecordCacheMetrics = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup default mocks
    vi.mocked(useAuthStore).mockReturnValue({
      user: mockUser,
      updateUser: mockUpdateUser,
      isAuthenticated: true,
      getCachedUserInfo: mockGetCachedUserInfo,
      setCachedUserInfo: mockSetCachedUserInfo,
      isCacheStale: mockIsCacheStale,
      clearUserInfoCache: mockClearUserInfoCache,
    } as any)

    vi.mocked(userInfoPerformance).startSession = mockStartSession
    vi.mocked(userInfoPerformance).endSession = mockEndSession
    vi.mocked(userInfoPerformance).recordCacheMetrics = mockRecordCacheMetrics

    vi.mocked(shouldClearCacheOnError).mockReturnValue(false)

    // Default cache state
    mockGetCachedUserInfo.mockReturnValue(null)
    mockIsCacheStale.mockReturnValue(true)
  })

  it('should not fetch user info when user already has firstName', () => {
    vi.mocked(useAuthStore).mockReturnValue({
      user: { ...mockUser, firstName: 'John' },
      updateUser: mockUpdateUser,
      isAuthenticated: true,
      getCachedUserInfo: mockGetCachedUserInfo,
      setCachedUserInfo: mockSetCachedUserInfo,
      isCacheStale: mockIsCacheStale,
      clearUserInfoCache: mockClearUserInfoCache,
    } as any)

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    expect(result.current.isLoading).toBe(false)
    expect(result.current.hasUserInfo).toBe(true)
    expect(authApi.getUserInfo).not.toHaveBeenCalled()
  })

  it('should fetch user info when firstName is missing', async () => {
    const mockUserInfo = {
      Result: {
        FirstName: 'John',
        LastName: 'Doe',
        Email: '<EMAIL>',
      },
    }

    vi.mocked(authApi.getUserInfo).mockResolvedValue(mockUserInfo)

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    expect(result.current.isLoading).toBe(true)

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(authApi.getUserInfo).toHaveBeenCalled()
    expect(mockUpdateUser).toHaveBeenCalledWith({
      firstName: 'John',
      lastName: 'Doe',
    })
    expect(mockSetCachedUserInfo).toHaveBeenCalledWith({
      firstName: 'John',
      lastName: 'Doe',
      FirstName: 'John',
      LastName: 'Doe',
      Email: '<EMAIL>',
    })
  })

  it('should handle direct response format (not wrapped)', async () => {
    const mockUserInfo = {
      FirstName: 'John',
      LastName: 'Doe',
      Email: '<EMAIL>',
    }

    vi.mocked(authApi.getUserInfo).mockResolvedValue(mockUserInfo)

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(mockUpdateUser).toHaveBeenCalledWith({
      firstName: 'John',
      lastName: 'Doe',
    })
  })

  it('should check cache but not display cached values initially', async () => {
    const cachedData = {
      firstName: 'Cached',
      lastName: 'User',
    }

    mockGetCachedUserInfo.mockReturnValue(cachedData)
    mockIsCacheStale.mockReturnValue(false)

    vi.mocked(authApi.getUserInfo).mockResolvedValue({
      Result: {
        FirstName: 'Fresh',
        LastName: 'Data',
      },
    })

    renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    // Should still fetch fresh data even with valid cache
    await waitFor(() => {
      expect(authApi.getUserInfo).toHaveBeenCalled()
    })

    expect(mockRecordCacheMetrics).toHaveBeenCalledWith('hit')

    await waitFor(() => {
      expect(mockUpdateUser).toHaveBeenCalledWith({
        firstName: 'Fresh',
        lastName: 'Data',
      })
    })
  })

  it('should record cache miss when cache is stale', async () => {
    mockGetCachedUserInfo.mockReturnValue({ firstName: 'Old' })
    mockIsCacheStale.mockReturnValue(true)

    vi.mocked(authApi.getUserInfo).mockResolvedValue({
      Result: { FirstName: 'New' },
    })

    renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(mockRecordCacheMetrics).toHaveBeenCalledWith('miss')
    })
  })

  it('should handle UserInfo endpoint not available error', async () => {
    const error = new Error('UserInfo endpoint not available')
    vi.mocked(authApi.getUserInfo).mockRejectedValue(error)

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.error).toBeNull()
    expect(mockClearUserInfoCache).not.toHaveBeenCalled()
  })

  it('should clear cache on authentication errors', async () => {
    const error = new Error('Unauthorized')
    vi.mocked(authApi.getUserInfo).mockRejectedValue(error)
    vi.mocked(shouldClearCacheOnError).mockReturnValue(true)

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(result.current.error).toBeTruthy()
    })

    expect(shouldClearCacheOnError).toHaveBeenCalledWith(error)
    expect(mockClearUserInfoCache).toHaveBeenCalled()
  })

  it('should track performance session', async () => {
    vi.mocked(authApi.getUserInfo).mockResolvedValue({
      Result: { FirstName: 'John' },
    })

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    expect(mockStartSession).toHaveBeenCalled()

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(mockEndSession).toHaveBeenCalledWith('session-123', true)
  })

  it('should end session on error', async () => {
    const error = new Error('Network error')
    vi.mocked(authApi.getUserInfo).mockRejectedValue(error)

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(result.current.error).toBeTruthy()
    })

    expect(mockEndSession).toHaveBeenCalledWith('session-123', false)
  })

  it('should handle background loading with cached data', async () => {
    const cachedData = {
      firstName: 'Cached',
      lastName: 'User',
    }

    mockGetCachedUserInfo.mockReturnValue(cachedData)
    mockIsCacheStale.mockReturnValue(false)

    let resolveUserInfo: (value: any) => void
    const userInfoPromise = new Promise((resolve) => {
      resolveUserInfo = resolve
    })

    vi.mocked(authApi.getUserInfo).mockReturnValue(userInfoPromise)

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    // Should not show loading when we have cached data
    expect(result.current.isLoading).toBe(false)
    expect(result.current.hasUserInfo).toBe(true)
    expect(result.current.isBackgroundLoading).toBe(true)
    expect(result.current.isCacheHit).toBe(true)

    // Resolve the API call
    resolveUserInfo!({
      Result: {
        FirstName: 'Fresh',
        LastName: 'Data',
      },
    })

    await waitFor(() => {
      expect(result.current.isBackgroundLoading).toBe(false)
    })

    expect(result.current.isCacheHit).toBe(false)
  })

  it('should support refetch', async () => {
    vi.mocked(authApi.getUserInfo).mockResolvedValue({
      Result: { FirstName: 'John' },
    })

    const { result } = renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    vi.mocked(authApi.getUserInfo).mockClear()

    await result.current.refetch()

    expect(authApi.getUserInfo).toHaveBeenCalled()
  })

  it('should not start session when not authenticated', () => {
    vi.mocked(useAuthStore).mockReturnValue({
      user: null,
      updateUser: mockUpdateUser,
      isAuthenticated: false,
      getCachedUserInfo: mockGetCachedUserInfo,
      setCachedUserInfo: mockSetCachedUserInfo,
      isCacheStale: mockIsCacheStale,
      clearUserInfoCache: mockClearUserInfoCache,
    } as any)

    renderHook(() => useUserInfo(), {
      wrapper: createWrapper(),
    })

    expect(mockStartSession).not.toHaveBeenCalled()
  })
})
