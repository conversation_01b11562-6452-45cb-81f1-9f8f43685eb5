import React, { useEffect, useState } from 'react'

export interface SuccessAnimationProps {
  /** Whether to show the animation */
  show: boolean
  /** Duration of the animation in milliseconds */
  duration?: number
  /** Callback when animation completes */
  onComplete?: () => void
  /** Additional CSS classes */
  className?: string
}

export function SuccessAnimation({
  show,
  duration = 1500,
  onComplete,
  className = '',
}: SuccessAnimationProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (show) {
      setIsVisible(true)
      setIsAnimating(true)

      const timer = setTimeout(() => {
        setIsAnimating(false)
        setTimeout(() => {
          setIsVisible(false)
          onComplete?.()
        }, 200) // Fade out duration
      }, duration)

      return () => clearTimeout(timer)
    }
    return undefined
  }, [show, duration, onComplete])

  if (!isVisible) return null

  return (
    <div
      data-testid="success-animation"
      className={`fixed inset-0 z-50 flex items-center justify-center pointer-events-none ${className}`}
    >
      <div
        className={`
          transition-all duration-200 transform
          ${isAnimating ? 'scale-100 opacity-100' : 'scale-0 opacity-0'}
        `}
      >
        <div className="bg-white dark:bg-gray-800 rounded-full p-4 shadow-2xl">
          <svg
            className="w-16 h-16 text-green-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
              className={isAnimating ? 'animate-check-mark' : ''}
            />
          </svg>
        </div>
      </div>
    </div>
  )
}

// Add keyframes for check mark animation
if (typeof window !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = `
    @keyframes check-mark {
      0% {
        stroke-dasharray: 0 100;
      }
      100% {
        stroke-dasharray: 100 100;
      }
    }
    .animate-check-mark {
      stroke-dasharray: 100 100;
      animation: check-mark 0.5s ease-out;
    }
  `
  document.head.appendChild(style)
}
