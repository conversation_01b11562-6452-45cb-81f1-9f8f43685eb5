import { test, expect } from '@playwright/test'

test.describe('Design System Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3000/design-system')
  })

  test('should allow scrolling on the page', async ({ page }) => {
    // Check that the page is scrollable
    const scrollHeight = await page.evaluate(
      () => document.documentElement.scrollHeight
    )
    const clientHeight = await page.evaluate(
      () => document.documentElement.clientHeight
    )

    // If content is longer than viewport, page should be scrollable
    if (scrollHeight > clientHeight) {
      // Try to scroll down
      await page.evaluate(() => window.scrollTo(0, 100))
      const scrollTop = await page.evaluate(() => window.pageYOffset)

      // Verify that scroll position changed
      expect(scrollTop).toBeGreaterThan(0)
    }
  })

  test('theme buttons should change the theme', async ({ page }) => {
    // Get initial theme
    const initialTheme = await page.evaluate(
      () => document.documentElement.dataset.theme
    )

    // Click on a different theme button
    const targetTheme =
      initialTheme === 'flat-bold' ? 'glassmorphism' : 'flat-bold'
    await page.click(
      `button:has-text("${targetTheme.replace('-', ' ').replace(/\b\w/g, (l) => l.toUpperCase())}")`
    )

    // Verify theme changed
    const newTheme = await page.evaluate(
      () => document.documentElement.dataset.theme
    )
    expect(newTheme).toBe(targetTheme)

    // Verify visual changes (button should be highlighted)
    const activeButton = await page
      .locator(
        `button:has-text("${targetTheme.replace('-', ' ').replace(/\b\w/g, (l) => l.toUpperCase())}")`
      )
      .getAttribute('class')
    expect(activeButton).toContain('bg-brand-primary')
  })
})
