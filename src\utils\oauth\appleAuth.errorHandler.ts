/**
 * Apple OAuth Error Handler
 * Handles error scenarios and provides user-friendly error messages
 */

import type { OAuthError, OAuthErrorCallback } from '../../types/oauth'

export class AppleAuthErrorHandler {
  /**
   * Handle sign-in errors
   */
  static handleSignInError(
    error: AppleID.auth.SignInError | Error,
    onError: OAuthErrorCallback
  ): void {
    console.error('[Apple OAuth] Sign in error:', error)

    let oauthError: OAuthError

    if ('error' in error) {
      // Handle AppleID.auth.SignInError
      switch (error.error) {
        case 'popup_closed_by_user':
          oauthError = {
            code: 'user_cancelled',
            message: 'Sign in was cancelled',
            provider: 'apple',
            details: error as unknown as Record<string, unknown>,
          }
          break

        case 'user_cancelled_authorize':
          oauthError = {
            code: 'user_cancelled',
            message: 'Authorization was cancelled',
            provider: 'apple',
            details: error as unknown as Record<string, unknown>,
          }
          break

        case 'invalid_request':
          oauthError = {
            code: 'oauth_failed',
            message:
              'Invalid sign in request. Please check your configuration.',
            provider: 'apple',
            details: error as unknown as Record<string, unknown>,
          }
          break

        case 'invalid_client':
          oauthError = {
            code: 'oauth_failed',
            message: 'Invalid client configuration. Please contact support.',
            provider: 'apple',
            details: error as unknown as Record<string, unknown>,
          }
          break

        case 'access_denied':
          oauthError = {
            code: 'oauth_failed',
            message: 'Access was denied. Please try again.',
            provider: 'apple',
            details: error as unknown as Record<string, unknown>,
          }
          break

        default:
          oauthError = {
            code: 'oauth_failed',
            message: `Sign in failed: ${error.error}`,
            provider: 'apple',
            details: error as unknown as Record<string, unknown>,
          }
      }
    } else {
      // Handle generic Error
      oauthError = {
        code: 'oauth_failed',
        message: error.message || 'An unexpected error occurred',
        provider: 'apple',
        details: { error: error.message },
      }
    }

    onError(oauthError)
  }

  /**
   * Handle initialization errors
   */
  static handleInitError(error: Error): OAuthError {
    console.error('[Apple OAuth] Initialization error:', error)

    return {
      code: 'oauth_failed',
      message: 'Failed to initialize Apple Sign-In',
      provider: 'apple',
      details: { error: error.message },
    }
  }

  /**
   * Handle redirect callback errors
   */
  static handleRedirectError(error: Error): OAuthError {
    console.error('[Apple OAuth] Redirect callback error:', error)

    return {
      code: 'oauth_failed',
      message: 'Failed to process redirect callback',
      provider: 'apple',
      details: { error: error.message },
    }
  }

  /**
   * Create error for missing configuration
   */
  static createConfigError(missing: string): OAuthError {
    return {
      code: 'oauth_failed',
      message: `Missing configuration: ${missing}`,
      provider: 'apple',
      details: { missing },
    }
  }

  /**
   * Create error for SDK not loaded
   */
  static createSDKError(): OAuthError {
    return {
      code: 'oauth_failed',
      message: 'Apple Sign-In SDK is not loaded',
      provider: 'apple',
      details: { suggestion: 'Call initialize() first' },
    }
  }
}
