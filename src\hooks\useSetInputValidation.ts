import { useCallback } from 'react'

interface ValidationRules {
  isBodyweight?: boolean
}

export interface ValidationError {
  reps?: string
  weight?: string
  duration?: string
}

export function useSetInputValidation(rules: ValidationRules = {}) {
  const { isBodyweight = false } = rules

  const validateReps = useCallback((value: number): string | undefined => {
    if (value < 1) return 'Reps must be at least 1'
    if (value > 100) return 'Reps must be 100 or less'
    return undefined
  }, [])

  const validateWeight = useCallback(
    (value: number): string | undefined => {
      if (!isBodyweight && value < 1) return 'Weight must be at least 1'
      if (isBodyweight && value < 0) return 'Weight must be at least 0'
      if (value > 1000) return 'Weight must be 1000 or less'
      return undefined
    },
    [isBodyweight]
  )

  const validateDuration = useCallback((value: number): string | undefined => {
    if (value < 1) return 'Duration must be at least 1'
    if (value > 300) return 'Duration must be 300 or less'
    return undefined
  }, [])

  return {
    validateReps,
    validateWeight,
    validateDuration,
  }
}
