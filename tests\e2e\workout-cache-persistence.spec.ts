import { test, expect } from '@playwright/test'

const testUser = {
  email: '<EMAIL>',
  password: 'Dr123456',
}

test.describe('Workout Cache Persistence', () => {
  test('should preserve workout cache after logout and re-login', async ({
    page,
  }) => {
    // Step 1: Login and navigate to workout
    await page.goto('/login')
    await page.fill('input[name="email"]', testUser.email)
    await page.fill('input[name="password"]', testUser.password)
    await page.click('button[type="submit"]')

    // Wait for redirect to program page
    await page.waitForURL('**/program')

    // Navigate to workout page
    const workoutButton = page.getByRole('button', {
      name: /Continue to workout/i,
    })
    await expect(workoutButton).toBeVisible({ timeout: 10000 })
    await workoutButton.click()

    // Wait for workout page to load
    await page.waitForURL('**/workout')

    // Verify exercises are loaded
    await expect(page.locator('text=/exercise/i').first()).toBeVisible({
      timeout: 10000,
    })

    // Verify exercises are loaded for later comparison
    await expect(page.locator('main').textContent()).resolves.toContain(
      'exercise'
    )

    // Step 2: Logout
    await page.click('[aria-label="User menu"]')
    await page.click('text=Log out')

    // Wait for redirect to login page
    await page.waitForURL('**/login')

    // Step 3: Login again
    await page.fill('input[name="email"]', testUser.email)
    await page.fill('input[name="password"]', testUser.password)
    await page.click('button[type="submit"]')

    // Wait for redirect to program page
    await page.waitForURL('**/program')

    // Navigate to workout page again
    const workoutButtonAgain = page.getByRole('button', {
      name: /Continue to workout/i,
    })
    await expect(workoutButtonAgain).toBeVisible({ timeout: 10000 })
    await workoutButtonAgain.click()

    // Wait for workout page
    await page.waitForURL('**/workout')

    // Verify exercises are still loaded (from cache)
    await expect(page.locator('text=/exercise/i').first()).toBeVisible({
      timeout: 10000,
    })

    // Verify the workout content is the same
    const workoutContentAfterRelogin = await page.locator('main').textContent()
    expect(workoutContentAfterRelogin).toContain('exercise')
  })

  test('clearAllCaches should still clear workout cache when explicitly called', async ({
    page,
  }) => {
    // Login
    await page.goto('/login')
    await page.fill('input[name="email"]', testUser.email)
    await page.fill('input[name="password"]', testUser.password)
    await page.click('button[type="submit"]')

    // Wait for program page
    await page.waitForURL('**/program')

    // Navigate to workout
    const workoutButton = page.getByRole('button', {
      name: /Continue to workout/i,
    })
    await expect(workoutButton).toBeVisible({ timeout: 10000 })
    await workoutButton.click()

    // Wait for workout page
    await page.waitForURL('**/workout')

    // Verify exercises are loaded
    await expect(page.locator('text=/exercise/i').first()).toBeVisible({
      timeout: 10000,
    })

    // Clear all caches using browser console
    await page.evaluate(() => {
      localStorage.removeItem('drmuscle-workout')
    })

    // Refresh the page
    await page.reload()

    // Should show no workout available after cache is cleared
    await expect(page.locator('text=/No workout available/i')).toBeVisible({
      timeout: 10000,
    })
  })
})
