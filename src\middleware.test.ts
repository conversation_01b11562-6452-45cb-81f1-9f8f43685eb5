import { NextRequest } from 'next/server'
import { middleware } from './middleware'

describe('Middleware CSP Configuration', () => {
  const originalEnv = process.env.NODE_ENV

  afterEach(() => {
    process.env.NODE_ENV = originalEnv
  })

  it('should include Firebase domain in frame-src directive', () => {
    const request = new NextRequest(new URL('https://example.com'))
    const response = middleware(request)

    const csp = response.headers.get('Content-Security-Policy')
    expect(csp).toBeDefined()

    // Extract frame-src directive
    const frameSrcMatch = csp?.match(/frame-src([^;]+)/)
    const frameSrc = frameSrcMatch ? frameSrcMatch[1].trim() : ''

    // Verify all required domains are present
    expect(frameSrc).toContain("'self'")
    expect(frameSrc).toContain('https://accounts.google.com')
    expect(frameSrc).toContain('https://appleid.apple.com')
    expect(frameSrc).toContain('https://drmuscle-4a08d.firebaseapp.com')
  })

  it('should include Google APIs in connect-src directive', () => {
    const request = new NextRequest(new URL('https://example.com'))
    const response = middleware(request)

    const csp = response.headers.get('Content-Security-Policy')
    expect(csp).toBeDefined()

    // Extract connect-src directive
    const connectSrcMatch = csp?.match(/connect-src([^;]+)/)
    const connectSrc = connectSrcMatch ? connectSrcMatch[1].trim() : ''

    // Verify all required domains are present
    expect(connectSrc).toContain("'self'")
    expect(connectSrc).toContain('https://drmuscle.azurewebsites.net')
    expect(connectSrc).toContain('https://www.google-analytics.com')
    expect(connectSrc).toContain('https://vitals.vercel-insights.com')
    expect(connectSrc).toContain('https://region1.google-analytics.com')
    expect(connectSrc).toContain('https://identitytoolkit.googleapis.com')
    expect(connectSrc).toContain('https://securetoken.googleapis.com')
    expect(connectSrc).toContain('https://www.googleapis.com')
    expect(connectSrc).toContain('https://apis.google.com')
  })

  it('should include Vercel Live in script-src directive', () => {
    const request = new NextRequest(new URL('https://example.com'))
    const response = middleware(request)

    const csp = response.headers.get('Content-Security-Policy')
    expect(csp).toBeDefined()

    // Extract script-src directive
    const scriptSrcMatch = csp?.match(/script-src([^;]+)/)
    const scriptSrc = scriptSrcMatch ? scriptSrcMatch[1].trim() : ''

    // Verify Vercel Live domain is present for feedback widget
    expect(scriptSrc).toContain('https://vercel.live')
  })

  it('should include Vercel Live in frame-src for development', () => {
    process.env.NODE_ENV = 'development'
    const request = new NextRequest(new URL('https://example.com'))
    const response = middleware(request)

    const csp = response.headers.get('Content-Security-Policy')
    expect(csp).toBeDefined()

    // Extract frame-src directive
    const frameSrcMatch = csp?.match(/frame-src([^;]+)/)
    const frameSrc = frameSrcMatch ? frameSrcMatch[1].trim() : ''

    // Verify Vercel Live is included in development
    expect(frameSrc).toContain('https://vercel.live')
  })

  it('should NOT include Vercel Live in frame-src for production', () => {
    process.env.NODE_ENV = 'production'
    const request = new NextRequest(new URL('https://example.com'))
    const response = middleware(request)

    const csp = response.headers.get('Content-Security-Policy')
    expect(csp).toBeDefined()

    // Extract frame-src directive
    const frameSrcMatch = csp?.match(/frame-src([^;]+)/)
    const frameSrc = frameSrcMatch ? frameSrcMatch[1].trim() : ''

    // Verify Vercel Live is NOT included in production
    expect(frameSrc).not.toContain('https://vercel.live')
  })

  it('should set all required security headers', () => {
    const request = new NextRequest(new URL('https://example.com'))
    const response = middleware(request)

    expect(response.headers.get('X-Content-Type-Options')).toBe('nosniff')
    expect(response.headers.get('X-Frame-Options')).toBe('DENY')
    expect(response.headers.get('X-XSS-Protection')).toBe('1; mode=block')
    expect(response.headers.get('Referrer-Policy')).toBe(
      'strict-origin-when-cross-origin'
    )
    expect(response.headers.get('Permissions-Policy')).toBe(
      'camera=(), microphone=(), geolocation=()'
    )
    expect(response.headers.get('Strict-Transport-Security')).toBe(
      'max-age=63072000; includeSubDomains; preload'
    )
  })
})
