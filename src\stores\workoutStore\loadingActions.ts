/**
 * Workout loading actions for the workout store
 */

import type { WorkoutState } from './types'
import type { WorkoutTemplateGroupModel } from '@/types'
import { logger } from '@/utils/logger'
import { createRecommendationActions } from './recommendationActions'

export const createLoadingActions = (
  set: (
    partial:
      | Partial<WorkoutState>
      | ((state: WorkoutState) => Partial<WorkoutState>)
  ) => void,
  get: () => WorkoutState
) => ({
  // Workout Loading Actions
  loadWorkoutProgram: async () => {
    const { setCachedUserProgramInfo, setLoading, setError } = get()

    setLoading(true)
    setError('')

    try {
      // Import getUserWorkoutProgramInfo dynamically to avoid circular dependencies
      const { getUserWorkoutProgramInfo } = await import(
        '@/services/api/workout'
      )
      const response = await getUserWorkoutProgramInfo()

      if (response) {
        setCachedUserProgramInfo(response)

        // Extract next workout template if available
        const nextWorkout =
          response.GetUserProgramInfoResponseModel?.NextWorkoutTemplate
        if (nextWorkout) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          set({ currentWorkout: nextWorkout as any }) // Type cast needed due to API response differences
        }

        // Extract program info if available
        const program =
          response.GetUserProgramInfoResponseModel?.RecommendedProgram
        if (program) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          set({ currentProgram: program as any }) // Type cast needed due to API response differences
        }
      }
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to load workout program'
      )
      logger.error('Failed to load workout program:', error)
    } finally {
      setLoading(false)
    }
  },

  loadWorkoutDetails: async (workoutId: number) => {
    const { setCachedTodaysWorkout, setLoading, setError } = get()

    setLoading(true)
    setError('')

    try {
      // Import getWorkoutDetails dynamically to avoid circular dependencies
      const { getWorkoutDetails } = await import('@/services/api/workout')
      const workout = await getWorkoutDetails(workoutId)

      if (workout) {
        set({
          currentWorkout: workout,
          exercises: workout.Exercises || [],
        })

        // Cache the workout
        setCachedTodaysWorkout([
          workout,
        ] as unknown as WorkoutTemplateGroupModel[])
      }
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to load workout details'
      )
      logger.error('Failed to load workout details:', error)
    } finally {
      setLoading(false)
    }
  },

  getWorkoutExercises: () => {
    return get().exercises
  },

  // Include recommendation actions
  ...createRecommendationActions(set, get),
})
