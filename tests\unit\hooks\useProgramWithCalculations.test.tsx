import { describe, expect, it, vi } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useProgramWithCalculations } from '@/hooks/useProgramWithCalculations'
import * as useProgramModule from '@/hooks/useProgram'
import * as programCalculations from '@/lib/programCalculations'
import type { ProgramModel, ProgramProgress, ProgramStats } from '@/types'

// Mock the useProgram hooks
vi.mock('@/hooks/useProgram', () => ({
  useProgram: vi.fn(),
  useProgramProgress: vi.fn(),
  useProgramStats: vi.fn(),
}))

// Mock the calculation utilities
vi.mock('@/lib/programCalculations', () => ({
  calculateProgress: vi.fn(),
  calculateCompletionDate: vi.fn(),
  getDaysRemaining: vi.fn(),
  getMissedWorkouts: vi.fn(),
  getWorkoutsPerWeek: vi.fn(),
}))

const mockProgram: ProgramModel = {
  id: 1,
  name: '12-Week Transformation',
  description: 'Build muscle and strength',
  category: 'Muscle Building',
  totalDays: 84,
  currentDay: 21,
  workoutsCompleted: 9,
  startDate: '2024-01-01T00:00:00Z',
  totalWorkouts: 36,
  imageUrl: null,
}

const mockApiProgress: Partial<ProgramProgress> = {
  percentage: 25,
  totalWorkouts: 36,
  workoutsThisWeek: 2,
  remainingWorkouts: 27,
  daysCompleted: 21,
  currentWeek: 3,
}

const mockStats: ProgramStats = {
  averageWorkoutTime: 45.5,
  totalVolume: 125000,
  personalRecords: 12,
  consecutiveWeeks: 3,
  lastWorkoutDate: '2024-01-21',
  totalWorkoutsCompleted: 9,
}

const mockCalculatedProgress: ProgramProgress = {
  percentage: 25,
  daysCompleted: 21,
  currentWeek: 3,
  totalWorkouts: 36,
  workoutsThisWeek: 2,
  remainingWorkouts: 27,
}

describe('useProgramWithCalculations', () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
    },
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  beforeEach(() => {
    vi.clearAllMocks()

    // Setup default mocks
    vi.mocked(useProgramModule.useProgram).mockReturnValue({
      program: mockProgram,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    vi.mocked(useProgramModule.useProgramProgress).mockReturnValue({
      progress: mockApiProgress as ProgramProgress,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    vi.mocked(useProgramModule.useProgramStats).mockReturnValue({
      stats: mockStats,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    vi.mocked(programCalculations.calculateProgress).mockReturnValue(
      mockCalculatedProgress
    )
    vi.mocked(programCalculations.getDaysRemaining).mockReturnValue(63)
    vi.mocked(programCalculations.getMissedWorkouts).mockReturnValue(0)
    vi.mocked(programCalculations.getWorkoutsPerWeek).mockReturnValue(3)
    vi.mocked(programCalculations.calculateCompletionDate).mockReturnValue(
      new Date('2024-04-01')
    )
  })

  it('should combine API data with calculated metrics', () => {
    const { result } = renderHook(() => useProgramWithCalculations(), {
      wrapper,
    })

    expect(result.current.program).toEqual(mockProgram)
    expect(result.current.progress).toEqual({
      percentage: 25, // From API
      daysCompleted: 21, // From calculations
      currentWeek: 3, // From calculations
      totalWorkouts: 36, // From API
      workoutsThisWeek: 2, // From API
      remainingWorkouts: 27, // From API
    })
    expect(result.current.additionalMetrics).toEqual({
      daysRemaining: 63,
      missedWorkouts: 0,
      workoutsPerWeek: 3,
      completionDate: new Date('2024-04-01'),
    })
  })

  it('should handle loading states correctly', () => {
    vi.mocked(useProgramModule.useProgram).mockReturnValue({
      program: null,
      isLoading: true,
      error: null,
      refetch: vi.fn(),
    })

    const { result } = renderHook(() => useProgramWithCalculations(), {
      wrapper,
    })

    expect(result.current.isLoading).toBe(true)
    expect(result.current.program).toBeNull()
    expect(result.current.progress).toBeNull()
  })

  it('should handle errors appropriately', () => {
    const error = new Error('API Error')
    vi.mocked(useProgramModule.useProgram).mockReturnValue({
      program: null,
      isLoading: false,
      error,
      refetch: vi.fn(),
    })

    const { result } = renderHook(() => useProgramWithCalculations(), {
      wrapper,
    })

    expect(result.current.error).toBe(error)
    expect(result.current.hasPartialDataError).toBe(false)
  })

  it('should identify partial data errors', () => {
    const progressError = new Error('Progress API Error')
    vi.mocked(useProgramModule.useProgramProgress).mockReturnValue({
      progress: null,
      isLoading: false,
      error: progressError,
      refetch: vi.fn(),
    })

    const { result } = renderHook(() => useProgramWithCalculations(), {
      wrapper,
    })

    expect(result.current.hasPartialDataError).toBe(true)
    expect(result.current.error).toBe(progressError)
    expect(result.current.program).toEqual(mockProgram)
  })

  it('should handle missing API progress data', () => {
    vi.mocked(useProgramModule.useProgramProgress).mockReturnValue({
      progress: null,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    const { result } = renderHook(() => useProgramWithCalculations(), {
      wrapper,
    })

    // Should use calculated values when API data is missing
    expect(result.current.progress).toEqual({
      percentage: mockCalculatedProgress.percentage,
      daysCompleted: mockCalculatedProgress.daysCompleted,
      currentWeek: mockCalculatedProgress.currentWeek,
      totalWorkouts: mockCalculatedProgress.totalWorkouts,
      workoutsThisWeek: mockCalculatedProgress.workoutsThisWeek,
      remainingWorkouts: mockCalculatedProgress.remainingWorkouts,
    })
  })

  it('should add totalWorkouts to stats from program', () => {
    const { result } = renderHook(() => useProgramWithCalculations(), {
      wrapper,
    })

    expect(result.current.stats).toEqual({
      ...mockStats,
      totalWorkouts: mockProgram.totalWorkouts,
    })
  })

  it('should refetch all data when refetch is called', async () => {
    const refetchProgram = vi.fn().mockResolvedValue({})
    const refetchProgress = vi.fn().mockResolvedValue({})
    const refetchStats = vi.fn().mockResolvedValue({})

    vi.mocked(useProgramModule.useProgram).mockReturnValue({
      program: mockProgram,
      isLoading: false,
      error: null,
      refetch: refetchProgram,
    })

    vi.mocked(useProgramModule.useProgramProgress).mockReturnValue({
      progress: mockApiProgress as ProgramProgress,
      isLoading: false,
      error: null,
      refetch: refetchProgress,
    })

    vi.mocked(useProgramModule.useProgramStats).mockReturnValue({
      stats: mockStats,
      isLoading: false,
      error: null,
      refetch: refetchStats,
    })

    const { result } = renderHook(() => useProgramWithCalculations(), {
      wrapper,
    })

    await result.current.refetch()

    await waitFor(() => {
      expect(refetchProgram).toHaveBeenCalled()
      expect(refetchProgress).toHaveBeenCalled()
      expect(refetchStats).toHaveBeenCalled()
    })
  })

  it('should handle null program gracefully', () => {
    vi.mocked(useProgramModule.useProgram).mockReturnValue({
      program: null,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    const { result } = renderHook(() => useProgramWithCalculations(), {
      wrapper,
    })

    expect(result.current.program).toBeNull()
    expect(result.current.progress).toBeNull()
    expect(result.current.additionalMetrics).toBeNull()
  })

  it('should calculate metrics only when program and progress exist', () => {
    const { result, rerender } = renderHook(
      () => useProgramWithCalculations(),
      { wrapper }
    )

    // Verify calculations are called
    expect(programCalculations.getDaysRemaining).toHaveBeenCalledWith(
      mockProgram
    )
    expect(programCalculations.getMissedWorkouts).toHaveBeenCalledWith(
      mockProgram
    )
    expect(programCalculations.getWorkoutsPerWeek).toHaveBeenCalledWith(
      mockProgram
    )
    expect(programCalculations.calculateCompletionDate).toHaveBeenCalledWith(
      mockProgram,
      expect.objectContaining({ percentage: 25 })
    )

    // Update to null program
    vi.mocked(useProgramModule.useProgram).mockReturnValue({
      program: null,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    rerender()

    expect(result.current.additionalMetrics).toBeNull()
  })
})
