/**
 * Cache operation utilities for UserInfo performance tracking
 */

import { UserInfoPerformanceMarks } from './types'
import type { UserInfoPerformanceMetrics } from './types'
import { mark } from './performanceUtils'

export class CacheOperations {
  static trackCacheOperation(
    metrics: UserInfoPerformanceMetrics,
    operation: 'hit' | 'miss' | 'write',
    duration?: number
  ): void {
    if (operation === 'hit') {
      metrics.cacheMetrics.hits++
      mark(UserInfoPerformanceMarks.USERINFO_CACHE_HIT)
      if (duration !== undefined) {
        metrics.cacheMetrics.readLatency = duration
      }
    } else if (operation === 'miss') {
      metrics.cacheMetrics.misses++
      mark(UserInfoPerformanceMarks.USERINFO_CACHE_MISS)
    } else if (operation === 'write' && duration !== undefined) {
      metrics.cacheMetrics.writeLatency = duration
      mark(UserInfoPerformanceMarks.USERINFO_CACHE_WRITE)
    }

    // Update hit rate
    const total = metrics.cacheMetrics.hits + metrics.cacheMetrics.misses
    metrics.cacheMetrics.hitRate =
      total > 0 ? (metrics.cacheMetrics.hits / total) * 100 : 0
  }
}
