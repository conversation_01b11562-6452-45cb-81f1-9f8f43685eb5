import { test, expect } from '@playwright/test'

test.describe('Data Sync Functionality @critical', () => {
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456'
  }

  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login')
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)
    await page.getByRole('button', { name: /log in/i }).click()
    await page.waitForURL('/program')
  })

  test('should sync workout data with server @critical', async ({ page }) => {
    // Navigate to workout
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')

    // Complete a set
    await page.locator('[data-testid="exercise-item"]').first().click()
    await page.locator('input[name="weight"]').fill('100')
    await page.locator('input[name="reps"]').fill('10')

    // Monitor network for sync request
    const syncPromise = page.waitForResponse(response =>
      response.url().includes('/SaveWorkoutLog') || 
      response.url().includes('/sync') ||
      response.url().includes('/api/workout')
    )

    await page.getByRole('button', { name: /save|next|done/i }).click()

    // Verify sync occurred
    const syncResponse = await syncPromise
    expect(syncResponse.status()).toBe(200)

    // Verify data persists after refresh
    await page.reload()
    
    // Navigate back to the exercise
    await page.locator('[data-testid="exercise-item"]').first().click()
    
    // Check if previous data is displayed
    const exerciseHistory = page.locator('[data-testid="exercise-history"]')
    if (await exerciseHistory.isVisible({ timeout: 5000 })) {
      await expect(exerciseHistory).toContainText('100')
      await expect(exerciseHistory).toContainText('10')
    }
  })

  test('should handle sync conflicts gracefully @critical', async ({ page }) => {
    // Start workout on first tab
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')

    // Open second tab (simulate another device)
    const context = page.context()
    const page2 = await context.newPage()
    
    // Login on second tab
    await page2.goto('/login')
    await page2.getByLabel('Email').fill(TEST_USER.email)
    await page2.getByLabel('Password').fill(TEST_USER.password)
    await page2.getByRole('button', { name: /log in/i }).click()
    await page2.waitForURL('/program')

    // Start same workout on second tab
    await page2.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page2.waitForURL('/workout')

    // Complete set on first tab
    await page.locator('[data-testid="exercise-item"]').first().click()
    await page.locator('input[name="weight"]').fill('100')
    await page.locator('input[name="reps"]').fill('10')
    await page.getByRole('button', { name: /save|next|done/i }).click()

    // Complete different set on second tab
    await page2.locator('[data-testid="exercise-item"]').first().click()
    await page2.locator('input[name="weight"]').fill('110')
    await page2.locator('input[name="reps"]').fill('8')
    await page2.getByRole('button', { name: /save|next|done/i }).click()

    // Both should succeed without conflicts
    await expect(page.locator('[role="alert"][data-type="error"]')).not.toBeVisible()
    await expect(page2.locator('[role="alert"][data-type="error"]')).not.toBeVisible()

    // Close second tab
    await page2.close()
  })

  test('should queue data when offline and sync when online @critical', async ({ page, context }) => {
    // Navigate to workout
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')
    await page.locator('[data-testid="exercise-item"]').first().click()

    // Go offline
    await context.setOffline(true)

    // Complete a set while offline
    await page.locator('input[name="weight"]').fill('100')
    await page.locator('input[name="reps"]').fill('10')
    await page.getByRole('button', { name: /save|next|done/i }).click()

    // Should show offline indicator or queued message
    const offlineIndicator = page.locator('[data-testid="offline-indicator"]')
    const queuedMessage = page.locator('[data-testid="sync-queued"]')
    await expect(offlineIndicator.or(queuedMessage)).toBeVisible({ timeout: 5000 })

    // Monitor for sync when going back online
    const syncPromise = page.waitForResponse(response =>
      response.url().includes('/SaveWorkoutLog') || 
      response.url().includes('/sync'),
      { timeout: 15000 }
    ).catch(() => null)

    // Go back online
    await context.setOffline(false)

    // Wait for automatic sync
    const syncResponse = await syncPromise
    if (syncResponse) {
      expect(syncResponse.status()).toBe(200)
    }

    // Verify sync indicator disappears
    await expect(offlineIndicator.or(queuedMessage)).not.toBeVisible({ timeout: 10000 })
  })

  test('should sync user stats in real-time @critical', async ({ page }) => {
    // Get initial stats
    const workoutsCompleted = page.locator('[data-testid="workouts-completed"]')
    const initialCount = await workoutsCompleted.textContent()

    // Complete a workout
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')

    // Complete one exercise (simplified)
    await page.locator('[data-testid="exercise-item"]').first().click()
    await page.locator('input[name="weight"]').fill('100')
    await page.locator('input[name="reps"]').fill('10')
    await page.getByRole('button', { name: /save|next|done/i }).click()

    // Complete workout
    const completeButton = page.getByRole('button', { name: /complete workout|finish workout/i })
    if (await completeButton.isVisible({ timeout: 5000 })) {
      await completeButton.click()
    }

    // Return to program page
    await page.goto('/program')

    // Stats should be updated
    const updatedCount = await workoutsCompleted.textContent()
    expect(updatedCount).not.toBe(initialCount)
  })

  test('should handle partial sync failures @critical', async ({ page }) => {
    // Navigate to workout
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')

    // Intercept some sync requests to fail
    let requestCount = 0
    await page.route('**/SaveWorkoutLog**', async route => {
      requestCount++
      if (requestCount % 2 === 0) {
        await route.abort('failed')
      } else {
        await route.continue()
      }
    })

    // Complete multiple sets
    for (let i = 0; i < 3; i++) {
      await page.locator('[data-testid="exercise-item"]').first().click()
      await page.locator('input[name="weight"]').fill(`${100 + i * 10}`)
      await page.locator('input[name="reps"]').fill('10')
      await page.getByRole('button', { name: /save|next|done/i }).click()
      await page.waitForTimeout(1000)
    }

    // Should show retry notifications
    const retryIndicator = page.locator('[data-testid="sync-retry"]')
    await expect(retryIndicator).toBeVisible({ timeout: 5000 })
  })

  test('should sync across different app sections @critical', async ({ page }) => {
    // Update profile data
    await page.locator('[data-testid="user-avatar"]').click()
    const profileLink = page.getByRole('link', { name: /profile|settings/i })
    if (await profileLink.isVisible()) {
      await profileLink.click()
      
      // Update some profile field
      const bioField = page.locator('input[name="bio"], textarea[name="bio"]')
      if (await bioField.isVisible({ timeout: 3000 })) {
        await bioField.fill('Updated bio for sync test')
        await page.getByRole('button', { name: /save|update/i }).click()
      }
    }

    // Navigate to workout
    await page.goto('/workout')

    // User data should be synced
    const userInfo = page.locator('[data-testid="user-info"]')
    if (await userInfo.isVisible({ timeout: 3000 })) {
      await expect(userInfo).toContainText(TEST_USER.email.split('@')[0])
    }
  })

  test('should maintain data integrity during sync @critical', async ({ page }) => {
    // Navigate to workout
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')

    // Store initial state
    const exercises = await page.locator('[data-testid="exercise-item"]').count()

    // Complete a set with specific data
    const testData = {
      weight: '123.5',
      reps: '7'
    }

    await page.locator('[data-testid="exercise-item"]').first().click()
    await page.locator('input[name="weight"]').fill(testData.weight)
    await page.locator('input[name="reps"]').fill(testData.reps)

    // Save and wait for sync
    const syncPromise = page.waitForResponse(response =>
      response.url().includes('/SaveWorkoutLog')
    )
    await page.getByRole('button', { name: /save|next|done/i }).click()
    const syncResponse = await syncPromise

    // Verify exact data was sent
    const requestData = syncResponse.request().postData()
    if (requestData) {
      expect(requestData).toContain(testData.weight)
      expect(requestData).toContain(testData.reps)
    }

    // Refresh and verify data integrity
    await page.reload()
    await page.locator('[data-testid="exercise-item"]').first().click()

    // Check if data is preserved exactly
    const history = page.locator('[data-testid="exercise-history"]')
    if (await history.isVisible({ timeout: 5000 })) {
      await expect(history).toContainText(testData.weight)
      await expect(history).toContainText(testData.reps)
    }
  })

  test('should show sync status indicators @critical', async ({ page }) => {
    // Navigate to workout
    await page.getByRole('button', { name: /start workout|today's workout/i }).click()
    await page.waitForURL('/workout')

    // Look for sync status indicators
    const syncStatus = page.locator('[data-testid="sync-status"]')
    const lastSyncTime = page.locator('[data-testid="last-sync"]')

    // At least one should be visible
    await expect(syncStatus.or(lastSyncTime)).toBeVisible({ timeout: 5000 })

    // Complete an action that triggers sync
    await page.locator('[data-testid="exercise-item"]').first().click()
    await page.locator('input[name="weight"]').fill('100')
    await page.locator('input[name="reps"]').fill('10')
    await page.getByRole('button', { name: /save|next|done/i }).click()

    // Sync indicator should update
    const syncingIndicator = page.locator('[data-testid="syncing"]')
    await expect(syncingIndicator).toBeVisible({ timeout: 3000 })
    await expect(syncingIndicator).not.toBeVisible({ timeout: 10000 })
  })
})