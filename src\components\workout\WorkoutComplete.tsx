'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { FloatingCTAButton } from '@/components/ui'
import type { WorkoutSession } from '@/types'

export function WorkoutComplete() {
  const router = useRouter()
  const { workoutSession, finishWorkout, isLoading, error } = useWorkout()
  const [saveError, setSaveError] = useState<string | null>(null)

  // Calculate workout statistics
  const calculateStats = (session: WorkoutSession | null) => {
    if (!session) return null

    const totalSets = session.exercises.reduce(
      (sum, exercise) => sum + exercise.sets.length,
      0
    )

    const workingSets = session.exercises.reduce(
      (sum, exercise) =>
        sum + exercise.sets.filter((set) => !set.isWarmup).length,
      0
    )

    const totalVolume = session.exercises.reduce(
      (sum, exercise) =>
        sum +
        exercise.sets.reduce(
          (setSum, set) => setSum + set.reps * set.weight.Lb,
          0
        ),
      0
    )

    // Calculate average RIR from sets that have RIR data
    const rirValues = session.exercises.flatMap((exercise) =>
      exercise.sets
        .filter((set) => set.rir !== undefined)
        .map((set) => set.rir as number)
    )
    const avgRir =
      rirValues.length > 0
        ? rirValues.reduce((sum, rir) => sum + rir, 0) / rirValues.length
        : null

    return {
      totalExercises: session.exercises.length,
      totalSets,
      workingSets,
      totalVolume,
      avgRir,
    }
  }

  const stats = calculateStats(workoutSession)

  const formatVolume = (volume: number) => {
    // Format with comma separator
    return `${volume.toLocaleString('en-US')} lbs`
  }

  const handleFinishWorkout = async () => {
    try {
      setSaveError(null)
      await finishWorkout()
      router.push('/program')
    } catch (err) {
      setSaveError('Failed to save workout')
    }
  }

  const getButtonLabel = () => {
    if (error) return 'Retry'
    if (isLoading) return 'Saving...'
    return 'Back to Home'
  }

  const getButtonAriaLabel = () => {
    if (error) return 'Retry saving workout'
    if (isLoading) return 'Saving workout data'
    return 'Return to program page'
  }

  // Check for personal records (mock data for now)
  const personalRecords =
    (
      useWorkout() as unknown as {
        personalRecords?: Array<{
          exercise: string
          type: string
          value: string
        }>
      }
    ).personalRecords || []

  // Check offline status
  const isOffline =
    (useWorkout() as unknown as { isOffline?: boolean }).isOffline || false

  if (!workoutSession) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6">
        <p className="text-text-secondary">No workout data</p>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full bg-bg-primary">
      {/* Celebration Animation */}
      <div
        data-testid="celebration-animation"
        className="absolute inset-0 pointer-events-none overflow-hidden"
      >
        {/* Add confetti or animation here */}
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto p-6 pb-24">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-text-primary mb-2">
            Nice work!
          </h1>
        </div>

        {/* Stats Container */}
        <div data-testid="workout-stats" className="space-y-6">
          {/* Summary Stats */}
          <div className="bg-bg-secondary rounded-theme shadow-theme-md p-6 space-y-4">
            <h2 className="text-lg font-semibold text-text-primary">Summary</h2>

            {/* Exercises */}
            <div className="flex justify-between items-center">
              <span className="text-text-secondary">Exercises</span>
              <span
                className="font-medium text-text-primary"
                data-testid="total-exercises"
              >
                {stats?.totalExercises || 0}
              </span>
            </div>

            {/* Sets */}
            <div className="flex justify-between items-center">
              <span className="text-text-secondary">Sets</span>
              <span
                className="font-medium text-text-primary"
                data-testid="total-sets"
              >
                {stats?.totalSets || 0}
              </span>
            </div>

            {/* Total Volume */}
            <div className="flex justify-between items-center">
              <span className="text-text-secondary">Total Volume</span>
              <span className="font-medium text-text-primary">
                {stats ? formatVolume(stats.totalVolume) : '0 lbs'}
              </span>
            </div>

            {/* Average RIR */}
            {stats && stats.avgRir !== null && stats.avgRir !== undefined && (
              <div className="flex justify-between items-center">
                <span className="text-text-secondary">Avg RIR</span>
                <span className="font-medium text-text-primary">
                  {stats.avgRir.toFixed(1)}
                </span>
              </div>
            )}
          </div>

          {/* Personal Records */}
          {personalRecords.length > 0 && (
            <div className="bg-success/10 border border-success/20 rounded-lg p-4">
              <p className="text-success font-semibold mb-2">
                New Personal Record!
              </p>
              {personalRecords.map((pr) => (
                <p
                  key={`${pr.exercise}-${pr.type}`}
                  className="text-sm text-success/80"
                >
                  {pr.exercise} - {pr.type}: {pr.value}
                </p>
              ))}
            </div>
          )}

          {/* Offline Mode */}
          {isOffline && (
            <div className="bg-info/10 border border-info/20 rounded-lg p-4">
              <p className="text-info">Offline mode</p>
              <p className="text-sm text-info/80 mt-1">
                Your workout will sync when connected
              </p>
            </div>
          )}

          {/* Error Display */}
          {(error || saveError) && (
            <div className="bg-error/10 border border-error/20 rounded-lg p-4">
              <p className="text-error">
                {error || saveError || 'An error occurred'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Floating CTA Button */}
      <FloatingCTAButton
        onClick={handleFinishWorkout}
        label={getButtonLabel()}
        ariaLabel={getButtonAriaLabel()}
      />
    </div>
  )
}
