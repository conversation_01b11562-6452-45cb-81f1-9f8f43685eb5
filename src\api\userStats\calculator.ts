/**
 * User Stats Calculator
 *
 * Functions for calculating user statistics from workout data
 */

/**
 * Workout date interface for calculations
 */
export interface WorkoutDate {
  Date: string
  Sets?: Array<{
    Weight?: { Lb: number; Kg: number }
    Reps?: number
    IsWarmups?: boolean
  }>
}

/**
 * Calculate stats from workout history
 */
export function calculateStatsFromWorkoutHistory(workoutDates: WorkoutDate[]): {
  workouts: number
  volume: number
  weeks: number
} {
  if (!workoutDates || workoutDates.length === 0) {
    return { workouts: 0, volume: 0, weeks: 0 }
  }

  // Count workouts
  const workouts = workoutDates.length

  // Calculate total volume
  let volume = 0
  workoutDates.forEach((workout) => {
    if (workout.Sets && Array.isArray(workout.Sets)) {
      workout.Sets.forEach((set) => {
        if (!set.IsWarmups && set.Weight && set.Reps) {
          volume += (set.Weight.Lb || 0) * (set.Reps || 0)
        }
      })
    }
  })

  // Calculate consecutive weeks
  const validDates = workoutDates
    .filter((w) => w.Date)
    .map((w) => new Date(w.Date))
    .sort((a, b) => b.getTime() - a.getTime())
  let weeks = 0

  if (validDates.length > 0) {
    const now = new Date()
    const firstDate = validDates[0]
    if (firstDate) {
      const daysSinceLastWorkout = Math.floor(
        (now.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24)
      )

      // If last workout was more than 7 days ago, streak is broken
      if (daysSinceLastWorkout <= 7) {
        // Count weeks with at least one workout
        const weekNumbers = new Set<string>()
        validDates.forEach((date) => {
          const weekStart = new Date(date)
          weekStart.setDate(date.getDate() - date.getDay())
          const weekKey = weekStart.toISOString().split('T')[0]
          if (weekKey) {
            weekNumbers.add(weekKey)
          }
        })

        // Check for consecutive weeks
        const sortedWeeks = Array.from(weekNumbers).sort().reverse()
        weeks = 1 // Start with current week

        for (let i = 1; i < sortedWeeks.length; i++) {
          const currentWeek = new Date(sortedWeeks[i - 1] as string)
          const prevWeek = new Date(sortedWeeks[i] as string)
          const diffDays = Math.floor(
            (currentWeek.getTime() - prevWeek.getTime()) / (1000 * 60 * 60 * 24)
          )

          if (diffDays <= 7) {
            weeks++
          } else {
            break // Streak broken
          }
        }
      }
    }
  }

  return { workouts, volume: Math.round(volume), weeks }
}

/**
 * Calculate week streak from an array of dates
 */
export function calculateWeekStreakFromDates(dates: Date[]): number {
  if (!dates || dates.length === 0) {
    return 0
  }

  const sortedDates = [...dates].sort((a, b) => b.getTime() - a.getTime())
  const now = new Date()
  const mostRecentWorkout = sortedDates[0]

  if (!mostRecentWorkout) {
    return 0
  }

  const daysSinceLastWorkout = Math.floor(
    (now.getTime() - mostRecentWorkout.getTime()) / (1000 * 60 * 60 * 24)
  )

  // If last workout was more than 7 days ago, streak is broken
  if (daysSinceLastWorkout > 7) {
    return 0
  }

  // Count weeks with at least one workout
  const weekNumbers = new Set<string>()
  sortedDates.forEach((date) => {
    const weekStart = new Date(date)
    weekStart.setDate(date.getDate() - date.getDay())
    const weekKey = weekStart.toISOString().split('T')[0]
    if (weekKey) {
      weekNumbers.add(weekKey)
    }
  })

  // Check for consecutive weeks
  const sortedWeeks = Array.from(weekNumbers).sort().reverse()
  let weeks = 1 // Start with current week

  for (let i = 1; i < sortedWeeks.length; i++) {
    const currentWeek = new Date(sortedWeeks[i - 1] as string)
    const prevWeek = new Date(sortedWeeks[i] as string)
    const diffDays = Math.floor(
      (currentWeek.getTime() - prevWeek.getTime()) / (1000 * 60 * 60 * 24)
    )

    if (diffDays <= 7) {
      weeks++
    } else {
      break // Streak broken
    }
  }

  return weeks
}

/**
 * Calculate total volume from averages data
 */
export function calculateVolumeFromAverages(
  averages: Array<{
    Date: string
    Average: {
      Lb: number
      Kg: number
    }
  }>
): number {
  if (!Array.isArray(averages) || averages.length === 0) {
    return 0
  }

  let totalLbs = 0
  averages.forEach((avg) => {
    if (avg.Average && avg.Average.Lb && avg.Average.Lb > 0) {
      totalLbs += avg.Average.Lb
    }
  })

  return Math.round(totalLbs)
}

/**
 * Extract unique workout days from dates array
 */
export function extractUniqueWorkoutDays(dates: string[]): number {
  if (!Array.isArray(dates) || dates.length === 0) {
    return 0
  }

  const uniqueWorkoutDays = new Set(
    dates.map((dateStr: string) => dateStr.split('T')[0])
  )

  return uniqueWorkoutDays.size
}
