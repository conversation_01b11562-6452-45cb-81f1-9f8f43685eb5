import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi } from 'vitest'

// Create a custom render function that includes common providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
}

// Default QueryClient configuration for tests
export function createTestQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Disable retries in tests for faster failure
        retry: false,
        // Set stale time to 0 to always fetch fresh in tests
        staleTime: 0,
        // Disable garbage collection during tests
        gcTime: 0,
      },
      mutations: {
        // Disable retries for mutations too
        retry: false,
      },
    },
    // Suppress error logging in tests
    logger: {
      // eslint-disable-next-line no-console
      log: console.log,
      // eslint-disable-next-line no-console
      warn: console.warn,
      // Don't log errors in test output
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      error: () => {},
    },
  })
}

// Custom render function that wraps components with providers
export function renderWithProviders(
  ui: React.ReactElement,
  {
    queryClient = createTestQueryClient(),
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  // Create wrapper component with all providers
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient,
  }
}

// Helper to wait for React Query to settle
export async function waitForQueryToSettle() {
  // Wait a tick for React Query to process
  await new Promise((resolve) => setTimeout(resolve, 0))
}

// Helper to create a wrapper for renderHook
export function createQueryWrapper(queryClient?: QueryClient) {
  const client = queryClient || createTestQueryClient()

  return function QueryWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={client}>{children}</QueryClientProvider>
  }
}

// Mock successful query helper
export function mockSuccessfulQuery<T>(data: T) {
  return vi.fn().mockResolvedValue(data)
}

// Mock failed query helper
export function mockFailedQuery(error: Error | string) {
  const errorObj = typeof error === 'string' ? new Error(error) : error
  return vi.fn().mockRejectedValue(errorObj)
}

// Re-export everything from React Testing Library
export * from '@testing-library/react'
