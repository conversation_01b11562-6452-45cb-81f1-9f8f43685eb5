import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { AnimatedCounter } from '../AnimatedCounter'

// Mock the performance hook
vi.mock('@/hooks/useComponentPerformance', () => ({
  useComponentPerformance: vi.fn(),
}))

// Mock the performance tracking
vi.mock('@/utils/userInfoPerformance', () => ({
  trackMetricLoaded: vi.fn(),
}))

describe('AnimatedCounter - Large Number Formatting', () => {
  it('formats large weight values with K notation', async () => {
    render(
      <AnimatedCounter
        targetValue={12345}
        label="Lbs lifted"
        duration={0} // Instant animation for testing
      />
    )

    // Wait for the value to be rendered
    await waitFor(() => {
      const valueElement = screen.getByTestId('animated-counter-value')
      expect(valueElement).toHaveTextContent('12.3 K')
    })
  })

  it('formats very large weight values with M notation', async () => {
    render(
      <AnimatedCounter targetValue={1234567} label="Kg lifted" duration={0} />
    )

    await waitFor(() => {
      const valueElement = screen.getByTestId('animated-counter-value')
      expect(valueElement).toHaveTextContent('1.23 M')
    })
  })

  it('does not format small weight values', async () => {
    render(
      <AnimatedCounter targetValue={9999} label="Lbs lifted" duration={0} />
    )

    await waitFor(() => {
      const valueElement = screen.getByTestId('animated-counter-value')
      expect(valueElement).toHaveTextContent('9,999')
    })
  })

  it('does not format non-weight metrics', async () => {
    render(
      <AnimatedCounter targetValue={12345} label="Workouts" duration={0} />
    )

    await waitFor(() => {
      const valueElement = screen.getByTestId('animated-counter-value')
      expect(valueElement).toHaveTextContent('12,345')
    })
  })

  it('formats weight metrics with different labels', async () => {
    const weightLabels = [
      'Lbs lifted',
      'lbs lifted',
      'Kg lifted',
      'Total weight',
      'Weight lifted',
    ]

    // Test each label separately to avoid await in loop
    await Promise.all(
      weightLabels.map(async (label) => {
        const { container } = render(
          <AnimatedCounter targetValue={50000} label={label} duration={0} />
        )

        await waitFor(() => {
          const valueElement = container.querySelector(
            '[data-testid="animated-counter-value"]'
          )
          expect(valueElement).toHaveTextContent('50 K')
        })

        // Clean up by removing the container
        container.remove()
      })
    )
  })

  it('handles edge cases correctly', async () => {
    // Exactly 10,000 should format
    const { rerender } = render(
      <AnimatedCounter targetValue={10000} label="Lbs lifted" duration={0} />
    )

    await waitFor(() => {
      const valueElement = screen.getByTestId('animated-counter-value')
      expect(valueElement).toHaveTextContent('10 K')
    })

    // 999,999 should format as K
    rerender(
      <AnimatedCounter targetValue={999999} label="Lbs lifted" duration={0} />
    )

    await waitFor(() => {
      const valueElement = screen.getByTestId('animated-counter-value')
      expect(valueElement).toHaveTextContent('1 M') // Rounds to 1M due to 3 significant digits
    })

    // 1,000,000 should format as M
    rerender(
      <AnimatedCounter targetValue={1000000} label="Lbs lifted" duration={0} />
    )

    await waitFor(() => {
      const valueElement = screen.getByTestId('animated-counter-value')
      expect(valueElement).toHaveTextContent('1 M')
    })
  })
})
