import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { renderHook, act, waitFor } from '@testing-library/react'
import { useAuthStore } from '@/stores/authStore'
import type { LoginSuccessResultAlt as LoginSuccessResult } from '@/types'

const initialState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  hasHydrated: false,
}

describe('Auth Store', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks()
    ;(localStorage.getItem as any).mockReturnValue(null)

    // Reset the store state
    useAuthStore.setState({
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      hasHydrated: false,
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => useAuthStore())

      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
      expect(result.current.refreshToken).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
      expect(result.current.hasHydrated).toBe(false)
    })

    it('should load persisted auth data on initialization', async () => {
      // Given: Stored auth data
      const storedData = {
        state: {
          user: { id: '123', email: '<EMAIL>' },
          token: 'stored-token',
          refreshToken: 'stored-refresh-token',
          isAuthenticated: true,
          isLoading: false,
          error: null,
        },
        version: 0,
      }
      ;(localStorage.getItem as any).mockReturnValue(JSON.stringify(storedData))

      // When: Store is initialized with fresh state
      // Clear the store first to ensure fresh initialization
      useAuthStore.setState(initialState)

      // Trigger rehydration
      await useAuthStore.persist.rehydrate()

      const { result } = renderHook(() => useAuthStore())

      // Then: Auth data is loaded and hydrated is true
      expect(result.current.user).toEqual(storedData.state.user)
      expect(result.current.token).toBe(storedData.state.token)
      expect(result.current.refreshToken).toBe(storedData.state.refreshToken)
      expect(result.current.isAuthenticated).toBe(true)
      expect(result.current.hasHydrated).toBe(true)
    })
  })

  describe('Hydration', () => {
    it('should set hasHydrated to true after rehydration', async () => {
      const { result } = renderHook(() => useAuthStore())

      // Initially should not be hydrated
      expect(result.current.hasHydrated).toBe(false)

      // When: Trigger rehydration
      await act(async () => {
        await useAuthStore.persist.rehydrate()
      })

      // Then: Should be hydrated
      expect(result.current.hasHydrated).toBe(true)
    })

    it('should allow manual hydration state management', () => {
      const { result } = renderHook(() => useAuthStore())

      // When: Manually set hydrated state
      act(() => {
        result.current.setHasHydrated(true)
      })

      // Then: Hydrated state is updated
      expect(result.current.hasHydrated).toBe(true)
    })
  })

  describe('Login Flow', () => {
    it('should handle successful login', async () => {
      // Given: A user with valid credentials
      const { result } = renderHook(() => useAuthStore())
      const loginData: LoginSuccessResult = {
        Result: true,
        UserData: {
          Email: '<EMAIL>',
          Name: 'Test User',
        },
        UserToken: 'auth-token-123',
        RefreshToken: 'refresh-token-123',
      }

      // When: Login is successful
      await act(async () => {
        await result.current.setAuth(loginData)
      })

      // Then: Auth state is updated correctly
      expect(result.current.user).toEqual({
        email: loginData.UserData.Email,
        name: loginData.UserData.Name,
      })
      expect(result.current.token).toBe(loginData.UserToken)
      expect(result.current.refreshToken).toBe(loginData.RefreshToken)
      expect(result.current.isAuthenticated).toBe(true)
      expect(result.current.error).toBeNull()

      // And: User data is persisted (tokens are not persisted for security)
      await waitFor(() => {
        expect(localStorage.setItem).toHaveBeenCalledWith(
          'drmuscle-auth',
          expect.stringContaining(
            '"user":{"email":"<EMAIL>","name":"Test User"}'
          )
        )
        // Verify tokens are NOT persisted
        const { calls } = (localStorage.setItem as jest.Mock).mock
        const authCall = calls.find((call) => call[0] === 'drmuscle-auth')
        expect(authCall?.[1]).not.toContain('"token"')
        expect(authCall?.[1]).not.toContain('"refreshToken"')
      })
    })

    it('should handle login errors', () => {
      // Given: A login error occurs
      const { result } = renderHook(() => useAuthStore())
      const errorMessage = 'Invalid credentials'

      // When: Error is set
      act(() => {
        result.current.setError(errorMessage)
      })

      // Then: Error state is updated
      expect(result.current.error).toBe(errorMessage)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.isAuthenticated).toBe(false)
    })

    it('should clear errors', () => {
      // Given: An error exists
      const { result } = renderHook(() => useAuthStore())
      act(() => {
        result.current.setError('Some error')
      })

      // When: Error is cleared
      act(() => {
        result.current.clearError()
      })

      // Then: Error is null
      expect(result.current.error).toBeNull()
    })
  })

  describe('Logout Flow', () => {
    it('should clear all auth data on logout but preserve hydration state', async () => {
      // Given: User is authenticated and hydrated
      const { result } = renderHook(() => useAuthStore())
      const loginData: LoginSuccessResult = {
        Result: true,
        UserData: { Email: '<EMAIL>', Name: 'Test User' },
        UserToken: 'auth-token',
        RefreshToken: 'refresh-token',
      }
      act(() => {
        result.current.setAuth(loginData)
        result.current.setHasHydrated(true)
      })

      // When: User logs out
      act(() => {
        result.current.logout()
      })

      // Then: All auth data is cleared but hydration state is preserved
      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
      expect(result.current.refreshToken).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.error).toBeNull()
      expect(result.current.hasHydrated).toBe(true) // Should preserve hydration state

      // And: Local storage is cleared (wait for persist middleware)
      await waitFor(() => {
        expect(localStorage.setItem).toHaveBeenCalledWith(
          'drmuscle-auth',
          expect.stringContaining('"user":null')
        )
      })
    })
  })

  describe('Token Management', () => {
    it('should update tokens when refreshed', async () => {
      // Given: User is authenticated
      const { result } = renderHook(() => useAuthStore())
      const initialAuth: LoginSuccessResult = {
        Result: true,
        UserData: { Email: '<EMAIL>', Name: 'Test User' },
        UserToken: 'old-token',
        RefreshToken: 'old-refresh-token',
      }
      await act(async () => {
        await result.current.setAuth(initialAuth)
      })

      // When: Tokens are refreshed
      const newTokens = {
        token: 'new-token',
        refreshToken: 'new-refresh-token',
      }
      await act(async () => {
        await result.current.updateTokens(
          newTokens.token,
          newTokens.refreshToken
        )
      })

      // Then: Tokens are updated but user data remains
      expect(result.current.token).toBe(newTokens.token)
      expect(result.current.refreshToken).toBe(newTokens.refreshToken)
      expect(result.current.user?.email).toBe(initialAuth.UserData.Email)
      expect(result.current.isAuthenticated).toBe(true)
    })
  })

  describe('Loading State', () => {
    it('should manage loading state', () => {
      const { result } = renderHook(() => useAuthStore())

      // When: Loading starts
      act(() => {
        result.current.setLoading(true)
      })

      // Then: Loading is true
      expect(result.current.isLoading).toBe(true)

      // When: Loading ends
      act(() => {
        result.current.setLoading(false)
      })

      // Then: Loading is false
      expect(result.current.isLoading).toBe(false)
    })
  })
})
