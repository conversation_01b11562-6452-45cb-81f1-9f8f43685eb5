import React from 'react'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ThemeProvider, useTheme } from '../context'

function TestComponent() {
  const { theme, setTheme } = useTheme()
  return (
    <div>
      <div data-testid="theme">{theme}</div>
      <button onClick={() => setTheme('flat-bold')}>Set Flat Bold</button>
      <button onClick={() => setTheme('glassmorphism')}>
        Set Glassmorphism
      </button>
      <button onClick={() => setTheme('ultra-minimal')}>
        Set Ultra Minimal
      </button>
    </div>
  )
}

describe('ThemeProvider CSS Variables', () => {
  beforeEach(() => {
    // Reset to default state
    document.documentElement.removeAttribute('data-theme')
  })

  it('should update CSS variables when theme changes', async () => {
    const user = userEvent.setup()

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    )

    // Check initial state
    expect(document.documentElement.getAttribute('data-theme')).toBe(
      'subtle-depth'
    )

    // Change to flat-bold
    await user.click(screen.getByText('Set Flat Bold'))
    expect(document.documentElement.getAttribute('data-theme')).toBe(
      'flat-bold'
    )

    // Change to glassmorphism
    await user.click(screen.getByText('Set Glassmorphism'))
    expect(document.documentElement.getAttribute('data-theme')).toBe(
      'glassmorphism'
    )

    // Change to ultra-minimal
    await user.click(screen.getByText('Set Ultra Minimal'))
    expect(document.documentElement.getAttribute('data-theme')).toBe(
      'ultra-minimal'
    )
  })

  it('should apply correct CSS variable values for each theme', () => {
    // This test would need CSS to be loaded, which doesn't happen in vitest
    // But we can at least verify the data-theme attribute is set correctly
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    )

    // The CSS should use selectors like:
    // :root[data-theme='subtle-depth'] { --color-bg-primary: #0a0a0b; }
    // :root[data-theme='flat-bold'] { --color-bg-primary: #000000; }
    // etc.

    expect(document.documentElement.getAttribute('data-theme')).toBe(
      'subtle-depth'
    )
  })
})
