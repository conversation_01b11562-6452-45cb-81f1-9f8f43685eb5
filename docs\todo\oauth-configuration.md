# OAuth Configuration Documentation

## Overview

The Dr. Muscle X web app supports OAuth authentication for Google and Apple Sign-In. This document describes the configuration setup and usage.

## Environment Variables

The following environment variables are required for OAuth functionality:

### Google OAuth

- `NEXT_PUBLIC_GOOGLE_CLIENT_ID` - Google OAuth Client ID
  - Current value: `707210235326-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com`

### Apple Sign-In

- `NEXT_PUBLIC_APPLE_TEAM_ID` - Apple Team ID
  - Current value: `7AAXZ47995`
- `NEXT_PUBLIC_APPLE_BUNDLE_ID` - Apple Bundle ID
  - Current value: `com.drmaxmuscle.max`

## Configuration Files

### 1. Environment Configuration (`.env.local`)

Contains the OAuth credentials and other environment-specific settings.

### 2. OAuth Configuration (`src/config/oauth.ts`)

Provides type-safe access to OAuth configuration with validation helpers:

- `googleOAuth` - Google OAuth configuration object
- `appleOAuth` - Apple OAuth configuration object
- `oauthConfig` - Combined configuration with helper methods
- `oauthRedirectUris` - Helper for generating OAuth redirect URIs

### 3. Global Type Declarations (`src/types/oauth.d.ts`)

TypeScript declarations for Google and Apple OAuth SDKs.

### 4. Environment Validation (`src/utils/validateEnv.ts`)

Utility for validating environment variables at runtime:

- Validates required variables are present
- Checks OAuth configuration completeness
- Provides warnings for missing optional configurations

## Usage

### In Components

```typescript
import { useOAuth } from '@/hooks/useOAuth'

function LoginComponent() {
  const oauth = useOAuth()

  if (oauth.google.isConfigured) {
    // Show Google Sign-In button
  }

  if (oauth.apple.isConfigured) {
    // Show Apple Sign-In button
  }
}
```

### Direct Configuration Access

```typescript
import { oauthConfig } from '@/config/oauth'

// Check if any OAuth provider is available
if (oauthConfig.hasAnyProvider()) {
  // Show OAuth login options
}

// Access specific configuration
console.log(oauthConfig.google.clientId)
console.log(oauthConfig.apple.teamId)
```

### Environment Validation

The environment is automatically validated in development mode when the app starts. You can also manually validate:

```typescript
import { validateEnv, logEnvValidation } from '@/utils/validateEnv'

// Get validation results
const result = validateEnv()
if (!result.valid) {
  console.error('Environment validation failed:', result.errors)
}

// Log validation results to console
logEnvValidation()
```

## Testing

Tests are provided for all OAuth configuration modules:

```bash
# Run OAuth configuration tests
npm test src/config/__tests__/oauth.test.ts

# Run environment validation tests
npm test src/utils/__tests__/validateEnv.test.ts
```

## Security Notes

1. OAuth credentials are stored as environment variables and never committed to the repository
2. The `.env.local` file is gitignored to prevent accidental commits
3. All OAuth redirects use secure HTTPS in production
4. Client-side OAuth tokens should be handled securely and never exposed in logs

## Next Steps

To complete OAuth integration:

1. Implement OAuth callback routes (`/auth/callback/google`, `/auth/callback/apple`)
2. Add OAuth SDK initialization in the login component
3. Implement token exchange with the Dr. Muscle API
4. Store authentication tokens securely in the auth store
