'use client'

import { useState, useEffect, useCallback } from 'react'
import { NetworkStatusManager } from '@/lib/networkStatus'

type ConnectionQuality = 'fast' | 'slow' | 'offline'

interface NetworkStatusOptions {
  onOnline?: () => void
  onOffline?: () => void
  onSync?: () => void
}

export function useNetworkStatus(options: NetworkStatusOptions = {}) {
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [connectionQuality, setConnectionQuality] =
    useState<ConnectionQuality>('fast')
  const [networkManager] = useState(() => new NetworkStatusManager())

  const { onOnline, onOffline, onSync } = options

  const checkConnectionQuality = useCallback(async () => {
    const quality = await networkManager.checkConnectionQuality()
    setConnectionQuality(quality)
    return quality
  }, [networkManager])

  useEffect(() => {
    const unsubscribe = networkManager.subscribe((online) => {
      const wasOffline = !isOnline
      setIsOnline(online)

      if (online) {
        onOnline?.()

        // Trigger sync if coming back online
        if (wasOffline && onSync) {
          onSync()
        }
      } else {
        onOffline?.()
      }
    })

    // Check initial connection quality
    checkConnectionQuality()

    return () => {
      unsubscribe()
    }
  }, [
    networkManager,
    onOnline,
    onOffline,
    onSync,
    isOnline,
    checkConnectionQuality,
  ])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      networkManager.destroy()
    }
  }, [networkManager])

  return {
    isOnline,
    isOffline: !isOnline,
    connectionQuality,
    checkConnectionQuality,
  }
}
