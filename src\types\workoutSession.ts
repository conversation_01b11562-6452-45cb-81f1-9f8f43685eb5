/**
 * Workout session and completion tracking types
 */

/**
 * Workout session tracking
 */
export interface WorkoutSession {
  id: string
  startTime: Date
  endTime?: Date
  exercises: CompletedExercise[]
  exerciseRIRStatus?: Record<number, boolean>
}

/**
 * Completed exercise tracking
 */
export interface CompletedExercise {
  exerciseId: number
  name: string
  sets: CompletedSet[]
  notes?: string
}

/**
 * Completed set data
 */
export interface CompletedSet {
  setNumber: number
  reps: number
  weight: import('./api').MultiUnityWeight
  rir?: number
  isWarmup: boolean
  timestamp?: Date
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  percentage: number
  previousWeight?: number
  previousReps?: number
  trend: 'up' | 'down' | 'stable'
}
