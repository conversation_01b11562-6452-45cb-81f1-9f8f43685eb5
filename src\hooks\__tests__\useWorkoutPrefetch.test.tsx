import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, waitFor, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useWorkoutPrefetch } from '../useWorkoutPrefetch'
import { workoutApi } from '@/api/workouts'
import type {
  GetUserWorkoutLogAverageResponse,
  WorkoutTemplateModel,
} from '@/types'

// Mock the workout API
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserProgramInfo: vi.fn(),
    getUserWorkout: vi.fn(),
  },
}))

// Helper to create wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
  return wrapper
}

describe('useWorkoutPrefetch', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('should start with 0 progress and not complete', () => {
    const { result } = renderHook(() => useWorkoutPrefetch(), {
      wrapper: createWrapper(),
    })

    expect(result.current.progress).toBe(0)
    expect(result.current.isComplete).toBe(false)
    expect(result.current.error).toBeNull()
    expect(result.current.status).toBe('Starting...')
  })

  it('should fetch both APIs in parallel when started', async () => {
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: { Id: 1, Label: 'Test Program' },
        NextWorkoutTemplate: { Id: 1, Label: 'Workout A' },
      },
    }

    const mockWorkouts = [
      {
        Id: 1,
        Label: 'Workout A',
        Exercises: [{ Id: 1, Name: 'Bench Press' }],
      },
    ]

    vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue(mockWorkouts)

    const { result } = renderHook(() => useWorkoutPrefetch(), {
      wrapper: createWrapper(),
    })

    // Start prefetching
    await act(async () => {
      result.current.startPrefetch()
    })

    await waitFor(() => {
      expect(workoutApi.getUserProgramInfo).toHaveBeenCalledTimes(1)
      expect(workoutApi.getUserWorkout).toHaveBeenCalledTimes(1)
    })
  })

  it('should update progress incrementally', async () => {
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: { Id: 1, Label: 'Test Program' },
        NextWorkoutTemplate: { Id: 1, Label: 'Workout A' },
      },
    }

    const mockWorkouts = [
      {
        Id: 1,
        Label: 'Workout A',
        Exercises: [{ Id: 1, Name: 'Bench Press' }],
      },
    ]

    vi.mocked(workoutApi.getUserProgramInfo).mockImplementation(
      () =>
        new Promise((resolve) => {
          setTimeout(() => resolve(mockProgramInfo), 100)
        })
    )

    vi.mocked(workoutApi.getUserWorkout).mockImplementation(
      () =>
        new Promise((resolve) => {
          setTimeout(() => resolve(mockWorkouts), 100)
        })
    )

    const { result } = renderHook(() => useWorkoutPrefetch(), {
      wrapper: createWrapper(),
    })

    const progressValues: number[] = []

    // Start prefetching and track progress
    await act(async () => {
      result.current.startPrefetch()
    })

    // Should start at 25% when fetching begins
    await waitFor(() => {
      expect(result.current.progress).toBeGreaterThan(0)
    })

    progressValues.push(result.current.progress)

    // Should reach 100% when complete
    await waitFor(() => {
      expect(result.current.isComplete).toBe(true)
      expect(result.current.progress).toBe(100)
    })

    // Progress should have increased incrementally
    expect(progressValues[0]).toBeGreaterThanOrEqual(25)
  })

  it('should handle errors gracefully', async () => {
    const mockError = new Error('Network error')

    vi.mocked(workoutApi.getUserProgramInfo).mockRejectedValue(mockError)
    vi.mocked(workoutApi.getUserWorkout).mockRejectedValue(
      new Error('Also failed')
    )

    const { result } = renderHook(() => useWorkoutPrefetch(), {
      wrapper: createWrapper(),
    })

    await act(async () => {
      result.current.startPrefetch()
    })

    await waitFor(() => {
      expect(result.current.error).toBe('Failed to load workout data')
      expect(result.current.isComplete).toBe(false)
      expect(result.current.status).toBe('Error loading workout data')
    })
  })

  it('should populate React Query cache on success', async () => {
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: { Id: 1, Label: 'Test Program' },
        NextWorkoutTemplate: { Id: 1, Label: 'Workout A' },
      },
    }

    const mockWorkouts = [
      {
        Id: 1,
        Label: 'Workout A',
        Exercises: [{ Id: 1, Name: 'Bench Press' }],
      },
    ]

    vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue(mockWorkouts)

    const queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )

    const { result } = renderHook(() => useWorkoutPrefetch(), { wrapper })

    await act(async () => {
      result.current.startPrefetch()
    })

    await waitFor(() => {
      expect(result.current.isComplete).toBe(true)
    })

    // Check that data is in cache
    const cachedProgramInfo = queryClient.getQueryData(['userProgramInfo'])
    const cachedWorkouts = queryClient.getQueryData(['userWorkout'])

    expect(cachedProgramInfo).toEqual(mockProgramInfo)
    expect(cachedWorkouts).toEqual(mockWorkouts)
  })

  it('should report correct status messages', async () => {
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: { Id: 1, Label: 'Test Program' },
        NextWorkoutTemplate: { Id: 1, Label: 'Workout A' },
      },
    }

    const mockWorkouts = [
      {
        Id: 1,
        Label: 'Workout A',
        Exercises: Array(5).fill({ Id: 1, Name: 'Bench Press' }),
      },
    ]

    vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue(mockWorkouts)

    const { result } = renderHook(() => useWorkoutPrefetch(), {
      wrapper: createWrapper(),
    })

    const statusMessages: string[] = []

    // Capture initial status
    statusMessages.push(result.current.status)

    // Start prefetching without awaiting so we can capture intermediate states
    act(() => {
      result.current.startPrefetch()
    })

    // Capture status messages during loading
    await waitFor(() => {
      if (
        result.current.status &&
        !statusMessages.includes(result.current.status)
      ) {
        statusMessages.push(result.current.status)
      }
      return result.current.isComplete
    })

    // Should have shown various status messages
    expect(statusMessages.length).toBeGreaterThan(1)
    expect(statusMessages).toContain('Starting...')
    expect(
      statusMessages.some((s) => s.includes('Loading') || s.includes('Ready'))
    ).toBe(true)
  })

  it('should handle partial failures', async () => {
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: { Id: 1, Label: 'Test Program' },
        NextWorkoutTemplate: { Id: 1, Label: 'Workout A' },
      },
    }

    vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockRejectedValue(
      new Error('Failed to load workouts')
    )

    const { result } = renderHook(() => useWorkoutPrefetch(), {
      wrapper: createWrapper(),
    })

    await act(async () => {
      result.current.startPrefetch()
    })

    await waitFor(() => {
      // Should still complete but with partial data
      expect(result.current.isComplete).toBe(true)
      expect(result.current.progress).toBe(100)
      expect(result.current.error).toBeNull() // No error if at least one succeeds
    })
  })

  it('should not start multiple prefetches if already running', () => {
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: { Id: 1, Label: 'Test Program' },
        NextWorkoutTemplate: { Id: 1, Label: 'Workout A' },
      },
    }

    vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([])

    const { result } = renderHook(() => useWorkoutPrefetch(), {
      wrapper: createWrapper(),
    })

    // Start prefetching
    act(() => {
      result.current.startPrefetch()
      result.current.startPrefetch() // Try to start again
    })

    // Should only call APIs once
    expect(workoutApi.getUserProgramInfo).toHaveBeenCalledTimes(1)
    expect(workoutApi.getUserWorkout).toHaveBeenCalledTimes(1)
  })

  it('should reset state when calling reset', async () => {
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: { Id: 1, Label: 'Test Program' },
        NextWorkoutTemplate: { Id: 1, Label: 'Workout A' },
      },
    }

    vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue([])

    const { result } = renderHook(() => useWorkoutPrefetch(), {
      wrapper: createWrapper(),
    })

    await act(async () => {
      result.current.startPrefetch()
    })

    await waitFor(() => {
      expect(result.current.isComplete).toBe(true)
    })

    // Reset the state
    act(() => {
      result.current.reset()
    })

    expect(result.current.progress).toBe(0)
    expect(result.current.isComplete).toBe(false)
    expect(result.current.error).toBeNull()
    expect(result.current.status).toBe('Starting...')
  })

  it('should calculate progress based on both API calls', async () => {
    let programInfoResolve: (value: GetUserWorkoutLogAverageResponse) => void
    let workoutResolve: (value: WorkoutTemplateModel[]) => void

    const programInfoPromise = new Promise<GetUserWorkoutLogAverageResponse>(
      (resolve) => {
        programInfoResolve = resolve
      }
    )

    const workoutPromise = new Promise<WorkoutTemplateModel[]>((resolve) => {
      workoutResolve = resolve
    })

    vi.mocked(workoutApi.getUserProgramInfo).mockReturnValue(
      programInfoPromise as Promise<GetUserWorkoutLogAverageResponse>
    )
    vi.mocked(workoutApi.getUserWorkout).mockReturnValue(
      workoutPromise as Promise<WorkoutTemplateModel[]>
    )

    const { result } = renderHook(() => useWorkoutPrefetch(), {
      wrapper: createWrapper(),
    })

    await act(async () => {
      result.current.startPrefetch()
    })

    // Should be at 25% when starting
    await waitFor(() => {
      expect(result.current.progress).toBe(25)
    })

    // Resolve program info - should be at 50%
    await act(async () => {
      programInfoResolve!({
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: { Id: 1, Label: 'Test' },
          NextWorkoutTemplate: { Id: 1, Label: 'Test' },
        },
      })
    })

    await waitFor(() => {
      expect(result.current.progress).toBe(50)
    })

    // Resolve workouts - should be at 100%
    await act(async () => {
      workoutResolve!([])
    })

    await waitFor(() => {
      expect(result.current.progress).toBe(100)
      expect(result.current.isComplete).toBe(true)
    })
  })
})
