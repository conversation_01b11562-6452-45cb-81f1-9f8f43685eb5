import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '../workoutStore'
import { useAuthStore } from '@/stores/authStore'
import type {
  GetUserProgramInfoResponseModel,
  RecommendationModel,
} from '@/types'
import { workoutApi } from '@/api/workouts'

// Mock API
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserProgramInfo: vi.fn(),
    getUserWorkout: vi.fn(),
    getExerciseRecommendation: vi.fn(),
  },
}))

// Mock data
const mockUserProgramInfo: GetUserProgramInfoResponseModel = {
  UserId: 'test-user',
  WeeklyStatus: 'Week 1',
  ProgramLabel: 'Test Program',
  NbDaysInTheWeek: 5,
  NbNonTrainingDays: 2,
  MondayIsFirst: false,
  TimeLogged: '2024-01-01T10:00:00',
  NextWorkoutDayText: 'Today',
  IsInIntroWorkout: false,
  IsInFirstWeek: true,
  TodaysWorkoutId: '1234',
  TodaysWorkoutText: 'Push Day',
  RecommendedProgram: {
    Id: 1,
    Label: 'Beginner Program',
    RemainingToLevelUp: 10,
    IconUrl: 'https://example.com/icon.png',
  },
  NextWorkoutTemplate: {
    Id: 1,
    Label: 'Push Day',
    IsSystemExercise: false,
    Exercises: [
      {
        Id: 123,
        Label: 'Bench Press',
        Path: 'chest/benchpress',
        TargetWeight: { Mass: 100, MassUnit: 'lbs' },
        TargetReps: 8,
        IsWarmup: false,
        HasPastLogs: true,
      },
    ],
  },
  NextNonTrainingDay: '2024-01-03',
  MondayHere: '2024-01-01',
  NextIntensityTechnique: 'Standard',
  ServerTimeUtc: '2024-01-01T10:00:00Z',
  MaxWorkoutSets: 20,
  NbMediumSets: 5,
  NbChallenges: 3,
  WorkoutTemplates: [],
}

const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 100, Kg: 45.36 },
  Increments: { Lb: 5, Kg: 2.5 },
  LastLogDate: '2024-01-01',
  LastReps: 8,
  LastWeight: { Lb: 95, Kg: 43.09 },
  LastSeries: 3,
  IsWaitingForSwap: false,
  RM: 105,
  RIR: 2,
  History: [],
  IsBodyweight: false,
  IsTimeBased: false,
  IsUnilateral: false,
  IsPlate: false,
  RecommendedCountdown: null,
  NegativeWeight: null,
  PartialWeight: null,
  IsNegative: false,
  IsPartial: false,
}

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: Infinity,
        staleTime: 0,
      },
    },
  })

  return function ({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

describe('Workout Store - E2E Cache Integration', () => {
  beforeEach(() => {
    // Reset stores
    useAuthStore.setState({ isAuthenticated: true })
    useWorkoutStore.setState({
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      hasHydrated: false,
      cacheVersion: 1,
      cacheStats: {
        hits: 0,
        misses: 0,
        operationCount: 0,
        totalLatency: 0,
        hydrationTime: 0,
      },
    })

    // Clear mocks
    vi.clearAllMocks()
  })

  describe('Complete User Flow with Caching', () => {
    it('should show cached data instantly on repeat visit without skeleton', async () => {
      // Given: Initial visit with API data
      vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue({
        GetUserProgramInfoResponseModel: mockUserProgramInfo,
      })

      // First render - data loaded from API
      const { result, rerender } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Initially loading
      expect(result.current.isLoadingWorkout).toBe(true)
      expect(result.current.userProgramInfo).toBeNull()

      // Wait for API data
      await waitFor(() => {
        expect(result.current.isLoadingWorkout).toBe(false)
        expect(result.current.userProgramInfo).toEqual(mockUserProgramInfo)
      })

      // Verify data was cached
      const store = useWorkoutStore.getState()
      expect(store.getCachedUserProgramInfo()).toEqual(mockUserProgramInfo)

      // Simulate user leaving and returning (cache persisted)
      useWorkoutStore.setState({ hasHydrated: true })

      // Clear API mock to ensure cache is used
      vi.mocked(workoutApi.getUserProgramInfo).mockClear()

      // Re-render hook (simulating return visit)
      rerender()

      // Then: Data should be available immediately from cache
      expect(result.current.isLoadingWorkout).toBe(false)
      expect(result.current.userProgramInfo).toEqual(mockUserProgramInfo)
      expect(result.current.hasInitialData).toBe(true)

      // Verify no API call was made
      expect(workoutApi.getUserProgramInfo).not.toHaveBeenCalled()
    })

    it('should update cache silently in background when data is stale', async () => {
      // Given: Cached data that's stale
      const staleData = { ...mockUserProgramInfo, WeeklyStatus: 'Week 1' }
      const freshData = { ...mockUserProgramInfo, WeeklyStatus: 'Week 2' }

      // Set up stale cache
      const store = useWorkoutStore.getState()
      store.setCachedUserProgramInfo(staleData)
      // Make it stale
      useWorkoutStore.setState({
        cachedData: {
          ...store.cachedData,
          lastUpdated: {
            ...store.cachedData.lastUpdated,
            userProgramInfo: Date.now() - 25 * 60 * 60 * 1000, // 25 hours old
          },
        },
        hasHydrated: true,
      })

      // Set up API to return fresh data
      vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue({
        GetUserProgramInfoResponseModel: freshData,
      })

      // When: Hook is rendered
      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Then: Stale data shown immediately
      expect(result.current.isLoadingWorkout).toBe(false)
      expect(result.current.userProgramInfo).toEqual(staleData)

      // Background update should occur
      await waitFor(() => {
        expect(workoutApi.getUserProgramInfo).toHaveBeenCalled()
      })

      // Cache should be updated with fresh data
      await waitFor(() => {
        const updatedStore = useWorkoutStore.getState()
        expect(updatedStore.getCachedUserProgramInfo()).toEqual(freshData)
      })
    })

    it('should handle offline mode with cached data', async () => {
      // Given: User has cached data
      const store = useWorkoutStore.getState()
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setHasHydrated(true)

      // Simulate offline - API calls fail
      vi.mocked(workoutApi.getUserProgramInfo).mockRejectedValue(
        new Error('Network error')
      )

      // When: Hook is rendered
      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // Then: Cached data is available despite network error
      expect(result.current.isLoadingWorkout).toBe(false)
      expect(result.current.userProgramInfo).toEqual(mockUserProgramInfo)
      expect(result.current.hasInitialData).toBe(true)

      // Error should be handled gracefully
      await waitFor(() => {
        expect(result.current.error).toBeNull()
      })
    })

    it('should prefetch exercise recommendations efficiently', async () => {
      // Given: Multiple exercises need recommendations
      const exercises = [123, 456, 789]

      exercises.forEach((id) => {
        vi.mocked(workoutApi.getExerciseRecommendation).mockImplementation(
          async (exerciseId) => {
            if (exerciseId === id) {
              return { ...mockRecommendation, Reps: id }
            }
            return null
          }
        )
      })

      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      // When: Fetching recommendations for multiple exercises
      const recommendations = await Promise.all(
        exercises.map((id) => result.current.getRecommendation(id))
      )

      // Then: All recommendations fetched
      expect(recommendations).toHaveLength(3)
      expect(recommendations[0]?.Reps).toBe(123)
      expect(recommendations[1]?.Reps).toBe(456)
      expect(recommendations[2]?.Reps).toBe(789)

      // Verify all cached
      const store = useWorkoutStore.getState()
      exercises.forEach((id) => {
        expect(store.getCachedExerciseRecommendation(id)).toBeTruthy()
      })

      // Second fetch should use cache
      vi.mocked(workoutApi.getExerciseRecommendation).mockClear()

      const cachedRecommendations = await Promise.all(
        exercises.map((id) => result.current.getRecommendation(id))
      )

      // No API calls made
      expect(workoutApi.getExerciseRecommendation).not.toHaveBeenCalled()
      expect(cachedRecommendations).toEqual(recommendations)
    })
  })

  describe('Performance Characteristics', () => {
    it('should meet performance targets for first paint with cache', async () => {
      // Given: Cached data available
      const store = useWorkoutStore.getState()
      store.setCachedUserProgramInfo(mockUserProgramInfo)
      store.setHasHydrated(true)

      const startTime = performance.now()

      // When: Rendering hook
      const { result } = renderHook(() => useWorkout(), {
        wrapper: createWrapper(),
      })

      const renderTime = performance.now() - startTime

      // Then: Data available instantly
      expect(result.current.userProgramInfo).toEqual(mockUserProgramInfo)
      expect(result.current.isLoadingWorkout).toBe(false)
      expect(renderTime).toBeLessThan(50) // <50ms for cached data
    })

    it('should maintain cache health under normal usage', async () => {
      // Given: Simulated normal usage pattern
      const store = useWorkoutStore.getState()
      store.setHasHydrated(true)

      // User visits multiple times
      for (let visit = 0; visit < 5; visit++) {
        // Cache some data
        store.setCachedUserProgramInfo(mockUserProgramInfo)
        store.setCachedUserWorkouts([])

        // Read data (simulating app usage)
        store.getCachedUserProgramInfo()
        store.getCachedUserWorkouts()

        // Cache exercise recommendations
        for (let i = 0; i < 5; i++) {
          store.setCachedExerciseRecommendation(i, mockRecommendation)
          store.getCachedExerciseRecommendation(i)
        }
      }

      // Then: Cache should be healthy
      const health = store.getCacheHealth()
      expect(health.isHealthy).toBe(true)

      const stats = store.getCacheStats()
      expect(stats.hitRate).toBeGreaterThan(0.8) // High hit rate
      expect(stats.averageLatency).toBeLessThan(1) // Fast operations
      expect(stats.totalSize).toBeLessThan(100 * 1024) // Reasonable size
    })
  })
})
