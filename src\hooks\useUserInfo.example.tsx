/**
 * useUserInfo Hook Examples
 *
 * This file demonstrates how to use the enhanced useUserInfo hook
 * with progressive loading and cache support.
 */

import React from 'react'
import { useUserInfo } from './useUserInfo'
import { useAuthStore } from '@/stores/authStore'
import { usePullToRefresh } from '@/hooks/usePullToRefresh'
import { PullToRefreshIndicator } from '@/components/PullToRefreshIndicator'
import { WelcomeHeader } from '@/components'
// Note: The following import is just for the example
// import { useProgramWithCalculationsAndCache } from '@/hooks/useProgramWithCalculationsAndCache'

/**
 * Example 1: Basic Usage with Loading States
 */
export function UserGreeting() {
  const { isLoading, error } = useUserInfo()
  const { user } = useAuthStore()

  if (error) {
    return <div>Error loading user info</div>
  }

  if (isLoading) {
    // Show skeleton while loading (but only if no cached data)
    return <div className="animate-pulse">Loading...</div>
  }

  return <div>Welcome back, {user?.firstName || user?.email || 'User'}!</div>
}

/**
 * Example 2: Progressive Loading with Cache Awareness
 */
export function ProgressiveUserInfo() {
  const { isCacheHit, isBackgroundLoading } = useUserInfo()
  const { user } = useAuthStore()

  return (
    <div className="relative">
      {/* Main content - shows immediately with cached data */}
      <h1 className="text-2xl font-bold">
        Welcome back, {user?.firstName || 'Champion'}!
      </h1>

      {/* Background loading indicator */}
      {isBackgroundLoading && (
        <div className="absolute top-0 right-0">
          <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-blue-500 rounded-full" />
        </div>
      )}

      {/* Debug info in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="text-xs text-gray-500 mt-2">
          {isCacheHit && '(From cache)'}
          {isBackgroundLoading && ' - Updating...'}
        </div>
      )}
    </div>
  )
}

/**
 * Example 3: Manual Refresh with Pull-to-Refresh
 */
export function RefreshableUserInfo() {
  const { refetch, isLoading, isBackgroundLoading } = useUserInfo()
  const [isRefreshing, setIsRefreshing] = React.useState(false)

  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      await refetch()
    } finally {
      setIsRefreshing(false)
    }
  }

  return (
    <div>
      <button
        onClick={handleRefresh}
        disabled={isRefreshing || isLoading}
        className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
      >
        {isRefreshing ? 'Refreshing...' : 'Refresh User Info'}
      </button>

      {/* Show non-blocking loading state */}
      {isBackgroundLoading && (
        <span className="ml-2 text-sm text-gray-500">
          Updating in background...
        </span>
      )}
    </div>
  )
}

/**
 * Example 6: Pull-to-Refresh Integration
 * This shows how to integrate UserInfo refresh with the program page pull-to-refresh
 */
export function ProgramPageWithPullToRefresh() {
  // Note: This is a simplified example. In the actual program page,
  // you would use useProgramWithCalculationsAndCache
  const refetchProgram = async () => {
    // Simulated program refetch
    console.log('Refreshing program data...')
  }
  const { refetch: refetchUserInfo } = useUserInfo()

  const { pullDistance, isPulling, isRefreshing } = usePullToRefresh({
    onRefresh: async () => {
      // Refresh both program data and user info in parallel
      await Promise.all([refetchProgram(), refetchUserInfo()])
    },
    enabled: true,
  })

  return (
    <div className="relative">
      {/* Pull-to-refresh indicator */}
      <PullToRefreshIndicator
        pullDistance={pullDistance}
        threshold={80}
        isRefreshing={isRefreshing}
        isPulling={isPulling}
      />

      {/* Your page content */}
      <WelcomeHeader />
    </div>
  )
}

/**
 * Example 4: Error Handling with Retry
 */
export function UserInfoWithErrorHandling() {
  const { error, refetch, hasUserInfo } = useUserInfo()
  const { user } = useAuthStore()

  if (error && !hasUserInfo) {
    return (
      <div className="p-4 bg-red-50 rounded">
        <p className="text-red-700">Failed to load user information</p>
        <button
          onClick={() => refetch()}
          className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm"
        >
          Try Again
        </button>
      </div>
    )
  }

  return <div>Hello, {user?.firstName || user?.email || 'there'}!</div>
}

/**
 * Example 5: Performance Monitoring Integration
 */
export function MonitoredUserInfo() {
  const { isLoading, isCacheHit, isBackgroundLoading } = useUserInfo()

  React.useEffect(() => {
    // Log performance metrics
    console.log('UserInfo Loading State:', {
      isLoading,
      isCacheHit,
      isBackgroundLoading,
      timestamp: new Date().toISOString(),
    })
  }, [isLoading, isCacheHit, isBackgroundLoading])

  return <UserGreeting />
}

// Import for examples (normally would be imported at top)
