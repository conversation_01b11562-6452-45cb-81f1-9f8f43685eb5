/**
 * Ultra-Minimal Theme Tokens
 * Pure focus with maximum negative space and hairline borders
 */

import { Theme } from '../types'

export const ultraMinimalTheme: Theme = {
  name: 'ultra-minimal',
  colors: {
    background: {
      primary: '#FFFFFF',
      secondary: '#FAFAFA',
      tertiary: '#F5F5F5',
      overlay: 'rgba(0, 0, 0, 0.05)',
    },
    text: {
      primary: '#000000',
      secondary: '#333333',
      tertiary: '#666666',
      inverse: '#FFFFFF',
    },
    brand: {
      primary: '#000000',
      secondary: '#333333',
      accent: '#0066FF', // Single accent color
    },
    status: {
      success: '#000000',
      error: '#FF0000',
      warning: '#FF9500',
      info: '#0066FF',
    },
    interactive: {
      hover: 'rgba(0, 0, 0, 0.05)',
      active: 'rgba(0, 0, 0, 0.1)',
      disabled: 'rgba(0, 0, 0, 0.02)',
    },
  },
  typography: {
    fonts: {
      heading: '"Didot", Georgia, serif',
      body: '"Helvetica Neue", -apple-system, BlinkMacSystemFont, sans-serif',
      mono: '"Courier", monospace',
    },
    sizes: {
      xs: '0.625rem',
      sm: '0.75rem',
      base: '0.875rem',
      lg: '1rem',
      xl: '1.125rem',
      '2xl': '1.618rem', // Golden ratio
      '3xl': '2.618rem', // Golden ratio
      '4xl': '4.236rem', // Golden ratio
    },
    weights: {
      light: 100,
      regular: 300,
      medium: 400,
      semibold: 500,
      bold: 700,
    },
    lineHeights: {
      tight: 1.2,
      normal: 1.618, // Golden ratio
      relaxed: 2,
    },
  },
  spacing: {
    unit: 4,
    xs: '0.236rem', // Golden ratio
    sm: '0.382rem', // Golden ratio
    md: '0.618rem', // Golden ratio
    lg: '1rem',
    xl: '1.618rem', // Golden ratio
    '2xl': '2.618rem', // Golden ratio
    '3xl': '4.236rem', // Golden ratio
    '4xl': '6.854rem', // Golden ratio
  },
  shadows: {
    none: 'none',
    sm: 'none',
    md: 'none',
    lg: 'none',
    xl: 'none',
    inner: 'none',
  },
  animations: {
    duration: {
      fast: '150ms',
      normal: '250ms',
      slow: '400ms',
    },
    easing: {
      default: 'ease-out',
      smooth: 'ease-in-out',
      spring: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    },
  },
  borders: {
    radius: {
      none: '0',
      sm: '0',
      md: '0',
      lg: '0',
      full: '9999px',
    },
    width: {
      hairline: '0.5px',
      thin: '0.5px',
      medium: '1px',
      thick: '2px',
    },
  },
}
