import { test, expect } from '@playwright/test'
import { login } from './helpers'

test.describe('Program Stats Animation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login')

    // Login with test credentials
    await login(page, '<EMAIL>', 'Dr123456')

    // Wait for program page to load
    await page.waitForSelector('[data-testid="program-overview-page"]', {
      timeout: 15000,
    })
  })

  test('should not double count stats values', async ({ page }) => {
    // Wait for program page to load
    await page.waitForSelector('[data-testid="program-overview-page"]')

    // Wait for stats to start loading
    await page.waitForSelector('[data-testid="animated-counter"]', {
      timeout: 10000,
    })

    // Get all stat counters
    const counters = await page.$$('[data-testid="animated-counter-value"]')
    expect(counters).toHaveLength(3) // Week streak, Workouts done, Lbs lifted

    // Track value changes for each counter
    const valueChanges: number[][] = [[], [], []]

    // Monitor value changes for 2 seconds
    const startTime = Date.now()
    while (Date.now() - startTime < 2000) {
      // Get all values at once to avoid await in loop
      // eslint-disable-next-line no-await-in-loop
      const values = await Promise.all(
        counters.map((counter) => counter.textContent())
      )

      // Process values
      for (let i = 0; i < values.length; i++) {
        const numValue = parseInt(values[i] || '0', 10)

        // Only track if value changed
        const lastValue = valueChanges[i][valueChanges[i].length - 1]
        if (lastValue === undefined || lastValue !== numValue) {
          valueChanges[i].push(numValue)
        }
      }
      // eslint-disable-next-line no-await-in-loop
      await page.waitForTimeout(50) // Check every 50ms
    }

    // Verify no double counting (values should only increase, never decrease)
    for (let i = 0; i < valueChanges.length; i++) {
      const changes = valueChanges[i]
      // eslint-disable-next-line no-console
      console.log(`Counter ${i} changes:`, changes)

      // Check that values only increase (no restart from lower value)
      for (let j = 1; j < changes.length; j++) {
        expect(changes[j]).toBeGreaterThanOrEqual(changes[j - 1])

        // Also check for suspicious drops that would indicate a restart
        if (j > 1 && changes[j - 1] > 10) {
          // If previous value was > 10, current shouldn't drop by more than 10%
          expect(changes[j]).toBeGreaterThan(changes[j - 1] * 0.9)
        }
      }
    }

    // Verify final values are stable
    await page.waitForTimeout(500)
    const finalValues = await Promise.all(
      counters.map((counter) => counter.textContent())
    )

    // Wait another 500ms and check values haven't changed
    await page.waitForTimeout(500)
    const checkValues = await Promise.all(
      counters.map((counter) => counter.textContent())
    )

    // Values should be stable (no ongoing animation)
    expect(checkValues).toEqual(finalValues)
  })

  test('should animate smoothly on navigation', async ({ page }) => {
    // Navigate to workout page
    await page.click('button:has-text("Continue Workout")')
    await page.waitForSelector('[data-testid="workout-overview"]')

    // Navigate back to program page
    await page.goBack()
    await page.waitForSelector('[data-testid="program-overview-page"]')

    // Stats should load without double counting
    const counters = await page.$$('[data-testid="animated-counter-value"]')

    // Get initial values
    const initialValues = await Promise.all(
      counters.map((counter) => counter.textContent())
    )

    // Wait for any animation to complete
    await page.waitForTimeout(1000)

    // Get final values
    const finalValues = await Promise.all(
      counters.map((counter) => counter.textContent())
    )

    // Values should either be the same or have increased (no restart)
    for (let i = 0; i < initialValues.length; i++) {
      const initial = parseInt(initialValues[i] || '0', 10)
      const final = parseInt(finalValues[i] || '0', 10)
      expect(final).toBeGreaterThanOrEqual(initial)
    }
  })
})
