import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { WorkoutScreen } from '@/components/workout/WorkoutScreen'
import { useWorkout } from '@/hooks/useWorkout'
import { useRouter } from 'next/navigation'
import type { WorkoutTemplateModel, ExerciseModel } from '@/types'

// Mock dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

// Mock data
const mockExercise: ExerciseModel = {
  Id: 1,
  Name: 'Bench Press',
  Label: 'Bench Press',
  TargetReps: 8,
  TargetWeight: { Mass: 100, MassUnit: 'lbs' },
  Path: 'chest/benchpress',
  IsWarmup: false,
  IsMedium: false,
  IsBodyweight: false,
  IsNormalSet: true,
  IsDeload: false,
  IsFinished: false,
  IsMediumFinished: false,
  IsEasy: false,
  IsFirstMedium: false,
  IsFirstEasy: false,
  BodyWeight: null,
  EquipmentId: 'barbell',
  TimeSinceLastSet: '00:00:00',
  PreviousExercise: null,
  SwapExerciseTargetPath: null,
  RecommendationInKgRange: null,
  FirstWorkSet: null,
  From1RM: null,
  OneRM: null,
  IsMultiUnity: false,
  IsRecommended: true,
  HasPastLogs: true,
  IsTimeBased: false,
  IsPlate: false,
}

const mockWorkout: WorkoutTemplateModel = {
  Id: 'workout-1',
  Label: 'Push Day',
  Exercices: [
    mockExercise,
    { ...mockExercise, Id: 2, Name: 'Shoulder Press', Label: 'Shoulder Press' },
    {
      ...mockExercise,
      Id: 3,
      Name: 'Tricep Extension',
      Label: 'Tricep Extension',
    },
  ],
  IsFinished: false,
  RepsAtTop: false,
  IsQuickMode: false,
  RecommendationInKgRange: null,
  WorkoutInfo: '',
  MusclesUsed: 'Chest, Shoulders, Triceps',
  DayOfWeek: 1,
  WorkoutTemplateId: 'template-1',
}

const mockWorkoutHook = {
  todaysWorkout: [{ Name: 'Week 1', Workouts: [mockWorkout], Id: 1 }],
  currentWorkout: mockWorkout,
  currentExercise: mockExercise,
  currentSetIndex: 0,
  workoutSession: null,
  isLoadingWorkout: false,
  isLoading: false,
  workoutError: null,
  error: null,
  saveSet: vi.fn(),
  startWorkout: vi.fn(),
  finishWorkout: vi.fn(),
  nextSet: vi.fn(),
  goToNextExercise: vi.fn(),
  getRecommendation: vi.fn().mockResolvedValue(null),
  restoreWorkout: vi.fn(),
  getRestDuration: vi.fn().mockReturnValue(90),
  isOffline: false,
  loadAllExerciseRecommendations: vi.fn(),
}

describe('WorkoutScreen', () => {
  let queryClient: QueryClient
  const mockPush = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })
    ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockWorkoutHook
    )
    ;(useRouter as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      push: mockPush,
    })
  })

  const renderWorkoutScreen = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <WorkoutScreen />
      </QueryClientProvider>
    )
  }

  describe('Initial State', () => {
    it('should display loading state', () => {
      // Given
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        isLoadingWorkout: true,
        currentWorkout: null,
      })

      // When
      renderWorkoutScreen()

      // Then
      expect(screen.getByText(/loading workout/i)).toBeInTheDocument()
    })

    it('should display error state', () => {
      // Given
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        workoutError: new Error('Failed to load'),
        currentWorkout: null,
      })

      // When
      renderWorkoutScreen()

      // Then
      expect(screen.getByText(/error loading workout/i)).toBeInTheDocument()
      expect(screen.getByText(/failed to load/i)).toBeInTheDocument()
    })

    it('should display no workout message', () => {
      // Given
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        todaysWorkout: [],
        currentWorkout: null,
      })

      // When
      renderWorkoutScreen()

      // Then
      expect(screen.getByText(/no workout scheduled/i)).toBeInTheDocument()
    })

    it('should display workout ready state', () => {
      // When
      renderWorkoutScreen()

      // Then
      expect(screen.getByText(/push day/i)).toBeInTheDocument()
      expect(screen.getByText(/3 exercises/i)).toBeInTheDocument()
      expect(
        screen.getByRole('button', { name: /start workout/i })
      ).toBeInTheDocument()
    })
  })

  describe('Starting Workout', () => {
    it('should start workout when button clicked', async () => {
      // Given
      const user = userEvent.setup()
      renderWorkoutScreen()

      // When
      await user.click(screen.getByRole('button', { name: /start workout/i }))

      // Then
      expect(mockWorkoutHook.startWorkout).toHaveBeenCalled()
    })

    it('should show exercise view after starting', async () => {
      // Given
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
      })

      // When
      renderWorkoutScreen()

      // Then
      expect(screen.getByText(/bench press/i)).toBeInTheDocument()
      expect(screen.getByText(/set 1/i)).toBeInTheDocument()
      expect(screen.getByText(/target: 8-12 reps/i)).toBeInTheDocument()
    })
  })

  describe('Exercise Display', () => {
    beforeEach(() => {
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
      })
    })

    it('should display current exercise information', () => {
      // When
      renderWorkoutScreen()

      // Then
      expect(screen.getByText(/bench press/i)).toBeInTheDocument()
      expect(screen.getByText(/exercise 1 of 3/i)).toBeInTheDocument()
      expect(screen.getByText(/set 1/i)).toBeInTheDocument()
    })

    it('should show recommendation when available', async () => {
      // Given
      mockWorkoutHook.getRecommendation.mockResolvedValue({
        Weight: { Lb: 105, Kg: 47.6 },
        Reps: 8,
        RIR: 2,
      })
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
      })

      // When
      renderWorkoutScreen()

      // Then
      await waitFor(() => {
        expect(
          screen.getByText(/recommendation: 105 lbs × 8 reps/i)
        ).toBeInTheDocument()
        expect(screen.getByText(/target rir: 2/i)).toBeInTheDocument()
      })
    })

    it('should display offline indicator', () => {
      // Given
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
        isOffline: true,
      })

      // When
      renderWorkoutScreen()

      // Then
      expect(screen.getByText(/offline mode/i)).toBeInTheDocument()
    })
  })

  describe('Set Logging', () => {
    beforeEach(() => {
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
      })
    })

    it('should open set logging modal', async () => {
      // Given
      const user = userEvent.setup()
      renderWorkoutScreen()

      // When
      await user.click(screen.getByRole('button', { name: /log set/i }))

      // Then
      expect(screen.getByRole('dialog')).toBeInTheDocument()
      expect(screen.getByLabelText('Weight')).toBeInTheDocument()
      expect(screen.getByLabelText('Reps')).toBeInTheDocument()
      expect(
        screen.getByRole('combobox', { name: /rir \(reps in reserve\)/i })
      ).toBeInTheDocument()
    })

    it('should save set with entered data', async () => {
      // Given
      const user = userEvent.setup()
      renderWorkoutScreen()
      await user.click(screen.getByRole('button', { name: /log set/i }))

      // When
      await user.clear(screen.getByLabelText('Weight'))
      await user.type(screen.getByLabelText('Weight'), '105')
      await user.clear(screen.getByLabelText('Reps'))
      await user.type(screen.getByLabelText('Reps'), '8')
      await user.selectOptions(
        screen.getByRole('combobox', { name: /rir \(reps in reserve\)/i }),
        '2'
      )
      await user.click(screen.getByRole('button', { name: /save set/i }))

      // Then - 105 kg input should be converted to lbs
      await waitFor(() => {
        expect(mockWorkoutHook.saveSet).toHaveBeenCalledWith({
          exerciseId: 1,
          weight: {
            Lb: expect.closeTo(231.485, 2), // 105 kg * 2.20462 ≈ 231.49 lbs
            Kg: 105, // Original kg value
          },
          reps: 8,
          isWarmup: false,
          setNumber: 1,
          RIR: 2,
        })
      })
    })

    it('should advance to next set after saving', async () => {
      // Given
      const user = userEvent.setup()
      mockWorkoutHook.saveSet.mockResolvedValue({ Result: true })
      renderWorkoutScreen()
      await user.click(screen.getByRole('button', { name: /log set/i }))

      // When
      await user.type(screen.getByLabelText('Weight'), '100')
      await user.type(screen.getByLabelText('Reps'), '8')
      await user.click(screen.getByRole('button', { name: /save/i }))

      // Then
      await waitFor(() => {
        expect(mockWorkoutHook.nextSet).toHaveBeenCalled()
      })
    })
  })

  describe('Exercise Navigation', () => {
    beforeEach(() => {
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
      })
    })

    it('should skip to next exercise', async () => {
      // Given
      const user = userEvent.setup()
      renderWorkoutScreen()

      // When
      await user.click(screen.getByRole('button', { name: /skip exercise/i }))

      // Then
      expect(mockWorkoutHook.goToNextExercise).toHaveBeenCalled()
    })

    it('should show exercise swap option', async () => {
      // Given
      const user = userEvent.setup()
      renderWorkoutScreen()

      // When
      await user.click(screen.getByRole('button', { name: /swap exercise/i }))

      // Then
      expect(
        screen.getByRole('dialog', { name: /swap exercise/i })
      ).toBeInTheDocument()
    })
  })

  describe('Workout Completion', () => {
    it('should show completion button on last exercise', () => {
      // Given
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        currentExercise: mockWorkout.Exercices[2],
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [
            { exerciseId: 1, sets: [{ reps: 8, weight: 100 }] },
            { exerciseId: 2, sets: [{ reps: 8, weight: 80 }] },
            { exerciseId: 3, sets: [{ reps: 12, weight: 50 }] },
          ],
        },
      })

      // When
      renderWorkoutScreen()

      // Then
      expect(
        screen.getByRole('button', { name: /finish and save workout/i })
      ).toBeInTheDocument()
    })

    it('should complete workout and navigate', async () => {
      // Given
      const user = userEvent.setup()
      mockWorkoutHook.finishWorkout.mockResolvedValue({ Result: true })
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        currentExercise: mockWorkout.Exercices[2],
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [
            { exerciseId: 1, sets: [{ reps: 8, weight: 100 }] },
            { exerciseId: 2, sets: [{ reps: 8, weight: 80 }] },
            { exerciseId: 3, sets: [{ reps: 12, weight: 50 }] },
          ],
        },
      })
      renderWorkoutScreen()

      // When
      await user.click(
        screen.getByRole('button', { name: /finish and save workout/i })
      )

      // Then
      await waitFor(() => {
        expect(mockWorkoutHook.finishWorkout).toHaveBeenCalled()
        // Now shows success screen instead of navigating directly
        expect(screen.getByTestId('workout-success-screen')).toBeInTheDocument()
      })
    })
  })

  describe('Error Handling', () => {
    beforeEach(() => {
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
      })
    })

    it('should display save error', async () => {
      // Given
      const user = userEvent.setup()
      mockWorkoutHook.saveSet.mockRejectedValue(new Error('Network error'))
      renderWorkoutScreen()
      await user.click(screen.getByRole('button', { name: /log set/i }))

      // When
      await user.type(screen.getByLabelText('Weight'), '100')
      await user.type(screen.getByLabelText('Reps'), '8')
      await user.click(screen.getByRole('button', { name: /save/i }))

      // Then
      await waitFor(() => {
        expect(screen.getByText(/failed to save set/i)).toBeInTheDocument()
      })
    })

    it('should handle offline set saving', async () => {
      // Given
      const user = userEvent.setup()
      const mockSaveSet = vi.fn().mockResolvedValue({ Result: true })
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
        isOffline: true,
        saveSet: mockSaveSet,
      })
      renderWorkoutScreen()
      await user.click(screen.getByRole('button', { name: /log set/i }))

      // When
      await user.type(screen.getByLabelText('Weight'), '100')
      await user.type(screen.getByLabelText('Reps'), '8')
      await user.click(screen.getByRole('button', { name: /save/i }))

      // Then
      await waitFor(() => {
        expect(
          screen.getByText(
            /saved offline\. will sync when connection is restored/i
          )
        ).toBeInTheDocument()
      })
    })
  })

  describe('Weight Conversion Bug Fix', () => {
    it('should correctly convert kg input to lb/kg values when saving a set', async () => {
      // Given - workout in progress
      const user = userEvent.setup()
      const mockSaveSet = vi.fn().mockResolvedValue({ Result: true })
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
        saveSet: mockSaveSet,
      })
      renderWorkoutScreen()

      // When - user logs a set with 50kg
      await user.click(screen.getByRole('button', { name: /log set/i }))
      const weightInput = screen.getByLabelText('Weight')
      const repsInput = screen.getByLabelText('Reps')

      await user.clear(weightInput)
      await user.type(weightInput, '50') // User enters 50 kg
      await user.clear(repsInput)
      await user.type(repsInput, '10')

      await user.click(screen.getByRole('button', { name: /save/i }))

      // Then - saveSet should be called with correct conversion
      await waitFor(() => {
        expect(mockSaveSet).toHaveBeenCalledWith(
          expect.objectContaining({
            exerciseId: mockExercise.Id,
            weight: expect.objectContaining({
              Lb: expect.closeTo(110.231, 2), // 50 kg * 2.20462 ≈ 110.23 lbs
              Kg: 50, // Original kg value
            }),
            reps: 10,
            isWarmup: false,
            setNumber: 1,
          })
        )
      })
    })

    it('should handle small kg values correctly', async () => {
      // Given - workout in progress
      const user = userEvent.setup()
      const mockSaveSet = vi.fn().mockResolvedValue({ Result: true })
      ;(useWorkout as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
        ...mockWorkoutHook,
        workoutSession: {
          id: 'session-1',
          startTime: new Date(),
          exercises: [],
        },
        saveSet: mockSaveSet,
      })
      renderWorkoutScreen()

      // When - user logs a set with 2.5kg (common for dumbbells)
      await user.click(screen.getByRole('button', { name: /log set/i }))
      const weightInput = screen.getByLabelText('Weight')
      const repsInput = screen.getByLabelText('Reps')

      await user.clear(weightInput)
      await user.type(weightInput, '2.5') // User enters 2.5 kg
      await user.clear(repsInput)
      await user.type(repsInput, '15')

      await user.click(screen.getByRole('button', { name: /save/i }))

      // Then - saveSet should be called with correct conversion
      await waitFor(() => {
        expect(mockSaveSet).toHaveBeenCalledWith(
          expect.objectContaining({
            weight: expect.objectContaining({
              Lb: expect.closeTo(5.5115, 2), // 2.5 kg * 2.20462 ≈ 5.51 lbs
              Kg: 2.5, // Original kg value
            }),
            reps: 15,
          })
        )
      })
    })
  })
})
