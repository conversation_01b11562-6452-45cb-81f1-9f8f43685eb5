// eslint-disable-next-line import/no-extraneous-dependencies
import { defineConfig, devices } from '@playwright/test'

/**
 * Optimized CI-specific Playwright configuration
 * with parallel execution and sharding support
 */
export default defineConfig({
  testDir: './tests/e2e',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: true,
  /* Retry on CI */
  retries: 2,
  /* Use multiple workers on CI for faster execution */
  workers: process.env.CI ? 2 : undefined,
  /* Support for sharding tests across multiple machines */
  /* Usage: --shard=1/4 --shard=2/4 etc. */
  /* Reporter configuration for CI */
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list'],
    ['json', { outputFile: 'test-results/results.json' }],
  ],
  /* Timeout for each test */
  timeout: 30000,
  /* Shared settings for all the projects below */
  use: {
    /* Base URL for local testing in CI */
    baseURL: 'http://localhost:3000',
    /* Collect trace when retrying the failed test */
    trace: 'on-first-retry',
    /* Screenshot on failure */
    screenshot: 'only-on-failure',
    /* Video on failure */
    video: 'retain-on-failure',
    /* Timeout for each action */
    actionTimeout: 10000,
  },

  /* Configure projects for critical mobile paths */
  projects: [
    /* Mobile Safari - Primary target for critical tests */
    {
      name: 'Mobile Safari',
      use: {
        ...devices['iPhone 13'],
        viewport: { width: 390, height: 844 },
        hasTouch: true,
        isMobile: true,
      },
      testMatch: /.*@critical.*\.spec\.ts$/,
    },
    /* Mobile Chrome - Run for critical tests only */
    {
      name: 'Mobile Chrome',
      use: {
        ...devices['Pixel 5'],
        viewport: { width: 393, height: 851 },
        hasTouch: true,
        isMobile: true,
      },
      testMatch: /.*@critical.*\.spec\.ts$/,
    },
    /* Mobile Safari - All tests */
    {
      name: 'Mobile Safari Full',
      use: {
        ...devices['iPhone 13'],
        viewport: { width: 390, height: 844 },
        hasTouch: true,
        isMobile: true,
      },
      testIgnore: /.*@critical.*\.spec\.ts$/,
    },
  ],

  /* Run local dev server before tests */
  webServer: {
    command: 'npm run start',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },
})