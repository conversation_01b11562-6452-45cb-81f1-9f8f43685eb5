import { NextRequest, NextResponse } from 'next/server'
import { authCookies } from '@/lib/cookies'

export async function POST(request: NextRequest) {
  try {
    // Get token from request body
    const body = await request.json()
    const { token, refreshToken } = body

    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 })
    }

    // Set httpOnly cookies
    await authCookies.setAuthToken(token)

    if (refreshToken) {
      await authCookies.setRefreshToken(refreshToken)
    }

    // Return success response
    return NextResponse.json(
      { success: true, message: 'Tokens stored in secure cookies' },
      { status: 200 }
    )
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to exchange tokens' },
      { status: 500 }
    )
  }
}

export async function DELETE() {
  try {
    // Clear auth cookies
    await authCookies.clearAuthCookies()

    return NextResponse.json(
      { success: true, message: 'Auth cookies cleared' },
      { status: 200 }
    )
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to clear cookies' },
      { status: 500 }
    )
  }
}
