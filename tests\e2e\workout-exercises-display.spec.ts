import { test, expect } from '@playwright/test'

test.describe('Workout exercises display', () => {
  // Use test credentials
  const testEmail = process.env.TEST_EMAIL || '<EMAIL>'
  const testPassword = process.env.TEST_PASSWORD || 'Dr123456'

  test.beforeEach(async ({ page }) => {
    // Mock API responses before navigating
    await page.route(
      '**/api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2',
      async (route) => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            StatusCode: 200,
            Result: {
              GetUserProgramInfoResponseModel: {
                RecommendedProgram: {
                  Id: 1,
                  Label: 'Montréal',
                  RemainingToLevelUp: 10,
                  IconUrl: '/images/program-icon.png',
                },
                NextWorkoutTemplate: {
                  Id: 1,
                  Label: 'Workout A',
                  IsSystemExercise: true,
                  Exercices: [
                    {
                      Id: 1,
                      Label: 'Bench Press',
                      BodyPartId: 1,
                      IsFinished: false,
                      IsNextExercise: true,
                      IsSystemExercise: true,
                      IsSwapTarget: false,
                      IsUnilateral: false,
                      IsTimeBased: false,
                      IsEasy: false,
                      IsMedium: true,
                      IsBodyweight: false,
                      VideoUrl: '',
                      IsPlate: false,
                      IsWeighted: true,
                      IsPyramid: false,
                      IsNormalSets: true,
                      IsBodypartPriority: false,
                      IsFlexibility: false,
                      IsOneHanded: false,
                      LocalVideo: '',
                      IsAssisted: false,
                    },
                    {
                      Id: 2,
                      Label: 'Squat',
                      BodyPartId: 5,
                      IsFinished: false,
                      IsNextExercise: false,
                      IsSystemExercise: true,
                      IsSwapTarget: false,
                      IsUnilateral: false,
                      IsTimeBased: false,
                      IsEasy: false,
                      IsMedium: true,
                      IsBodyweight: false,
                      VideoUrl: '',
                      IsPlate: false,
                      IsWeighted: true,
                      IsPyramid: false,
                      IsNormalSets: true,
                      IsBodypartPriority: false,
                      IsFlexibility: false,
                      IsOneHanded: false,
                      LocalVideo: '',
                      IsAssisted: false,
                    },
                  ],
                },
              },
              WorkoutCount: 5,
              LastWorkoutDateStr: '2025-01-08T10:00:00Z',
            },
          }),
        })
      }
    )

    await page.route('**/api/Workout/GetUserWorkout', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          StatusCode: 200,
          Result: [
            {
              Id: 1,
              Label: 'Workout A',
              UserId: 'test-user',
              IsSystemExercise: true,
              WorkoutSettingsModel: {},
              Exercices: [
                {
                  Id: 1,
                  Label: 'Bench Press',
                  BodyPartId: 1,
                  IsFinished: false,
                  IsNextExercise: true,
                  IsSystemExercise: true,
                  IsSwapTarget: false,
                  IsUnilateral: false,
                  IsTimeBased: false,
                  IsEasy: false,
                  IsMedium: true,
                  IsBodyweight: false,
                  VideoUrl: '',
                  IsPlate: false,
                  IsWeighted: true,
                  IsPyramid: false,
                  IsNormalSets: true,
                  IsBodypartPriority: false,
                  IsFlexibility: false,
                  IsOneHanded: false,
                  LocalVideo: '',
                  IsAssisted: false,
                },
                {
                  Id: 2,
                  Label: 'Squat',
                  BodyPartId: 5,
                  IsFinished: false,
                  IsNextExercise: false,
                  IsSystemExercise: true,
                  IsSwapTarget: false,
                  IsUnilateral: false,
                  IsTimeBased: false,
                  IsEasy: false,
                  IsMedium: true,
                  IsBodyweight: false,
                  VideoUrl: '',
                  IsPlate: false,
                  IsWeighted: true,
                  IsPyramid: false,
                  IsNormalSets: true,
                  IsBodypartPriority: false,
                  IsFlexibility: false,
                  IsOneHanded: false,
                  LocalVideo: '',
                  IsAssisted: false,
                },
                {
                  Id: 3,
                  Label: 'Deadlift',
                  BodyPartId: 6,
                  IsFinished: false,
                  IsNextExercise: false,
                  IsSystemExercise: true,
                  IsSwapTarget: false,
                  IsUnilateral: false,
                  IsTimeBased: false,
                  IsEasy: false,
                  IsMedium: true,
                  IsBodyweight: false,
                  VideoUrl: '',
                  IsPlate: false,
                  IsWeighted: true,
                  IsPyramid: false,
                  IsNormalSets: true,
                  IsBodypartPriority: false,
                  IsFlexibility: false,
                  IsOneHanded: false,
                  LocalVideo: '',
                  IsAssisted: false,
                },
              ],
            },
          ],
        }),
      })
    })

    // Login before each test
    await page.goto('/login')
    await page.getByLabel('Email').fill(testEmail)
    await page.locator('#password').fill(testPassword)
    await page.getByRole('button', { name: /login/i }).click()
    await page.waitForURL('/program', { timeout: 10000 })
  })

  test('should display exercises count after clicking Continue to workout', async ({
    page,
  }) => {
    // Navigate to program page
    await page.goto('/program')
    await page.waitForLoadState('networkidle')

    // Wait for Continue to workout button
    await page.waitForSelector('text=/Continue to workout/i', {
      timeout: 10000,
    })

    // Click Continue to workout
    await page.click('text=/Continue to workout/i')

    // Wait for navigation to workout page
    await page.waitForURL('**/workout')

    // Wait for workout content to load
    await page.waitForSelector('h1', { timeout: 10000 })

    // Check that exercises count is NOT 0
    const exercisesText = await page.textContent('text=/\\d+ exercises/i')
    expect(exercisesText).toBeTruthy()

    // Extract the number and verify it's greater than 0
    const match = exercisesText?.match(/(\d+) exercises/)
    const exerciseCount = match ? parseInt(match[1], 10) : 0
    expect(exerciseCount).toBeGreaterThan(0)
    expect(exerciseCount).toBe(3) // We have 3 exercises in our mock

    // For now, we've verified the exercise count is displayed correctly
    // The exercise cards loading is a separate concern related to progressive loading

    // Log debug information if test fails
    if (exerciseCount === 0) {
      const pageContent = await page.content()
      console.log('Page content when exercises are 0:', pageContent)

      // Check console logs
      page.on('console', (msg) => {
        if (msg.type() === 'log' || msg.type() === 'error') {
          console.log(`Console ${msg.type()}: ${msg.text()}`)
        }
      })
    }
  })

  test('should show exercise names in buttons', async ({ page }) => {
    // Navigate directly to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Wait for exercise names to appear
    await page.waitForSelector('text=/Bench Press/i', {
      timeout: 15000,
    })

    // Verify all exercise names are displayed
    const benchPress = await page.locator('text=/Bench Press/i').isVisible()
    const squat = await page.locator('text=/Squat/i').isVisible()
    const deadlift = await page.locator('text=/Deadlift/i').isVisible()

    expect(benchPress).toBe(true)
    expect(squat).toBe(true)
    expect(deadlift).toBe(true)
  })
})
