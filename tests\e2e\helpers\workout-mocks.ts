import { Page } from '@playwright/test'
import type {
  WorkoutTemplateGroupModel,
  WorkoutLogSerieModel,
  RecommendationModel,
} from '@/types'

export async function mockWorkoutAPI(page: Page) {
  // Mock workout template groups
  await page.route(
    '**/api/Workout/GetUserWorkoutTemplateGroup*',
    async (route) => {
      const mockData: WorkoutTemplateGroupModel[] = [
        {
          Id: 1,
          Label: 'Push Day',
          IsFeaturedProgram: false,
          UserId: 'test-user-123',
          IsSystemExercise: false,
          RequiredWorkoutToLevelUp: 4,
          ProgramId: 1,
          WorkoutTemplates: [
            {
              Id: 101,
              Label: 'Chest & Triceps',
              UserId: 'test-user-123',
              IsSystemExercise: false,
              WorkoutSettingsModel: {},
              Exercices: [
                {
                  Id: 1,
                  Label: 'Bench Press',
                  BodyPartId: 1,
                  IsFinished: false,
                  IsNextExercise: true,
                  IsSystemExercise: true,
                  IsSwapTarget: false,
                  IsUnilateral: false,
                  IsTimeBased: false,
                  IsEasy: false,
                  IsMedium: true,
                  IsBodyweight: false,
                  VideoUrl: '',
                  IsPlate: false,
                  IsWeighted: true,
                  IsPyramid: false,
                  IsNormalSets: true,
                  IsBodypartPriority: false,
                  IsFlexibility: false,
                  IsOneHanded: false,
                  LocalVideo: '',
                  IsAssisted: false,
                },
                {
                  Id: 2,
                  Label: 'Incline Dumbbell Press',
                  BodyPartId: 1,
                  IsFinished: false,
                  IsNextExercise: false,
                  IsSystemExercise: true,
                  IsSwapTarget: false,
                  IsUnilateral: false,
                  IsTimeBased: false,
                  IsEasy: false,
                  IsMedium: true,
                  IsBodyweight: false,
                  VideoUrl: '',
                  IsPlate: false,
                  IsWeighted: true,
                  IsPyramid: false,
                  IsNormalSets: true,
                  IsBodypartPriority: false,
                  IsFlexibility: false,
                  IsOneHanded: false,
                  LocalVideo: '',
                  IsAssisted: false,
                },
                {
                  Id: 3,
                  Label: 'Tricep Extensions',
                  BodyPartId: 3,
                  IsFinished: false,
                  IsNextExercise: false,
                  IsSystemExercise: true,
                  IsSwapTarget: false,
                  IsUnilateral: false,
                  IsTimeBased: false,
                  IsEasy: false,
                  IsMedium: true,
                  IsBodyweight: false,
                  VideoUrl: '',
                  IsPlate: false,
                  IsWeighted: true,
                  IsPyramid: false,
                  IsNormalSets: true,
                  IsBodypartPriority: false,
                  IsFlexibility: false,
                  IsOneHanded: false,
                  LocalVideo: '',
                  IsAssisted: false,
                },
              ],
            },
          ],
        },
      ]

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockData),
      })
    }
  )
}

export async function mockExerciseSets(
  page: Page,
  exerciseId: number,
  sets: Partial<WorkoutLogSerieModel>[]
) {
  await page.route(
    `**/api/Exercise/GetUserWorkoutSets?exerciseId=${exerciseId}*`,
    async (route) => {
      const mockSets: WorkoutLogSerieModel[] = sets.map((set, index) => ({
        Id: set.Id || index + 1,
        ExerciseId: exerciseId,
        Reps: set.Reps || 10,
        Weight: set.Weight || { Lb: 100, Kg: 45.4 },
        IsWarmups: set.IsWarmups || false,
        IsFinished: set.IsFinished || false,
        IsNext: set.IsNext || false,
        ...set,
      }))

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockSets),
      })
    }
  )
}

export async function mockRecommendations(
  page: Page,
  exerciseId: number,
  recommendation: Partial<RecommendationModel>
) {
  await page.route(
    `**/api/Exercise/GetRecommendationForExercise*`,
    async (route) => {
      const url = new URL(route.request().url())
      const params = Object.fromEntries(url.searchParams)

      if (params.ExerciceId === exerciseId.toString()) {
        const mockRecommendation: RecommendationModel = {
          FirstWorkSetWeight: recommendation.FirstWorkSetWeight || {
            Lb: 135,
            Kg: 61.2,
          },
          FirstWorkSetReps: recommendation.FirstWorkSetReps || 10,
          FirstWorkSet1RM: recommendation.FirstWorkSet1RM || {
            Lb: 180,
            Kg: 81.6,
          },
          MinReps: recommendation.MinReps || 8,
          MaxReps: recommendation.MaxReps || 12,
          WarmUpWeightSet1: recommendation.WarmUpWeightSet1 || {
            Lb: 95,
            Kg: 43.1,
          },
          WarmUpWeightSet2: recommendation.WarmUpWeightSet2 || {
            Lb: 115,
            Kg: 52.2,
          },
          IsBodyweight: recommendation.IsBodyweight || false,
          Series: recommendation.Series || 3,
          Reps: recommendation.Reps || 10,
          Weight: recommendation.Weight || { Lb: 135, Kg: 61.2 },
          OneRMProgress: recommendation.OneRMProgress || 0,
          RecommendationInKg: recommendation.RecommendationInKg || 61.2,
          OneRMPercentage: recommendation.OneRMPercentage || 75,
          WarmUpReps1: recommendation.WarmUpReps1 || 5,
          WarmUpReps2: recommendation.WarmUpReps2 || 3,
          WarmUpsList: recommendation.WarmUpsList || [],
          WarmupsCount: recommendation.WarmupsCount || 2,
          RpRest: recommendation.RpRest || 0,
          NbPauses: recommendation.NbPauses || 0,
          NbRepsPauses: recommendation.NbRepsPauses || 0,
          IsEasy: recommendation.IsEasy || false,
          IsMedium: recommendation.IsMedium || true,
          Increments: recommendation.Increments || { Lb: 5, Kg: 2.5 },
          Max: recommendation.Max || { Lb: 500, Kg: 227 },
          Min: recommendation.Min || { Lb: 45, Kg: 20 },
          IsNormalSets: recommendation.IsNormalSets || true,
          IsDeload: recommendation.IsDeload || false,
          IsBackOffSet: recommendation.IsBackOffSet || false,
          BackOffSetWeight: recommendation.BackOffSetWeight || {
            Lb: 115,
            Kg: 52.2,
          },
          IsMaxChallenge: recommendation.IsMaxChallenge || false,
          IsLightSession: recommendation.IsLightSession || false,
          IsPyramid: recommendation.IsPyramid || false,
          IsReversePyramid: recommendation.IsReversePyramid || false,
          HistorySet: recommendation.HistorySet || [],
          ReferenceSetHistory:
            recommendation.ReferenceSetHistory || ({} as WorkoutLogSerieModel),
          isPlateAvailable: recommendation.isPlateAvailable || true,
          isDumbbellAvailable: recommendation.isDumbbellAvailable || true,
          isPulleyAvailable: recommendation.isPulleyAvailable || false,
          isBandsAvailable: recommendation.isBandsAvailable || false,
          Speed: recommendation.Speed || 1,
          IsManual: recommendation.IsManual || false,
          ReferenseReps: recommendation.ReferenseReps || 10,
          ReferenseWeight: recommendation.ReferenseWeight || {
            Lb: 135,
            Kg: 61.2,
          },
          IsDropSet: recommendation.IsDropSet || false,
          ...recommendation,
        }

        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockRecommendation),
        })
      } else {
        await route.continue()
      }
    }
  )
}
