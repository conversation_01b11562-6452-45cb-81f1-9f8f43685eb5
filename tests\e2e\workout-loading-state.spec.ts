import { test, expect } from '@playwright/test'

test.describe('Workout Loading State', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for redirect to program page
    await page.waitForURL('/program')
  })

  test('should not show "Checking for updates" when data is cached and fresh', async ({
    page,
  }) => {
    // First visit - data will be fetched
    await page.goto('/workout')

    // Wait for workout data to load
    await page.waitForSelector('[data-testid="start-workout-button"]')

    // Navigate away and back to test cached data
    await page.goto('/program')
    await page.goto('/workout')

    // Should not show "Checking for updates" since data is cached and fresh
    const checkingText = page.locator('text="Checking for updates..."')
    await expect(checkingText).not.toBeVisible()

    // Should show workout content immediately
    await expect(
      page.locator('[data-testid="start-workout-button"]')
    ).toBeVisible()
  })

  test('should show "Checking for updates" only when actually fetching fresh data', async ({
    page,
  }) => {
    // Mock slow network to see loading state
    await page.route('**/api/**', async (route) => {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      await route.continue()
    })

    await page.goto('/workout')

    // During initial load without cache, should show skeleton, not "Checking for updates"
    const skeleton = page.locator('[data-testid="workout-title-skeleton"]')
    await expect(skeleton).toBeVisible()

    const checkingText = page.locator('text="Checking for updates..."')
    await expect(checkingText).not.toBeVisible()

    // Wait for data to load
    await page.waitForSelector('[data-testid="start-workout-button"]', {
      timeout: 10000,
    })
  })

  test('should show "Refreshing workout..." during pull-to-refresh', async ({
    page,
  }) => {
    await page.goto('/workout')
    await page.waitForSelector('[data-testid="start-workout-button"]')

    // Simulate pull-to-refresh gesture
    const workoutContent = page.locator('.overflow-y-auto').first()

    // Start pull gesture
    await workoutContent.hover()
    await page.mouse.down()
    await page.mouse.move(0, 150) // Pull down 150px
    await page.mouse.up()

    // Should show refresh indicator
    const refreshingText = page.locator('text="Refreshing workout..."')
    await expect(refreshingText).toBeVisible()
  })
})
