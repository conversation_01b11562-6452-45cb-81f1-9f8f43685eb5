/**
 * Analytics and reporting utilities for UserInfo performance
 */

import type { UserInfoPerformanceMetrics } from './types'

/**
 * Report metrics to console (development)
 */
export function reportToConsole(metrics: UserInfoPerformanceMetrics): void {
  if (process.env.NODE_ENV !== 'development') return

  // eslint-disable-next-line no-console
  console.group('[UserInfo Performance]')
  // eslint-disable-next-line no-console
  console.log(
    'Login to First Byte:',
    metrics.loginToFirstByte?.toFixed(2),
    'ms'
  )
  // eslint-disable-next-line no-console
  console.log('UserInfo Load Time:', metrics.userInfoLoadTime?.toFixed(2), 'ms')
  // eslint-disable-next-line no-console
  console.log('Stats Load Time:', metrics.statsLoadTime?.toFixed(2), 'ms')
  // eslint-disable-next-line no-console
  console.log('Overall Page Ready:', metrics.overallPageReady?.toFixed(2), 'ms')
  // eslint-disable-next-line no-console
  console.log('Cache Hit Rate:', metrics.cacheMetrics.hitRate.toFixed(1), '%')
  // eslint-disable-next-line no-console
  console.log('Total Retries:', metrics.retryMetrics.totalRetries)
  // eslint-disable-next-line no-console
  console.groupEnd()
}

/**
 * Send metrics to analytics (production)
 */
export function sendToAnalytics(metrics: UserInfoPerformanceMetrics): void {
  if (process.env.NODE_ENV !== 'production') return

  // Send to Google Analytics if available
  if (typeof window !== 'undefined' && 'gtag' in window) {
    const { gtag } = window as unknown as {
      gtag: (
        command: string,
        eventName: string,
        parameters: Record<string, unknown>
      ) => void
    }

    if (metrics.loginToFirstByte !== null) {
      gtag('event', 'timing_complete', {
        name: 'userinfo_first_byte',
        value: Math.round(metrics.loginToFirstByte),
        event_category: 'userinfo_performance',
      })
    }

    if (metrics.overallPageReady !== null) {
      gtag('event', 'timing_complete', {
        name: 'program_page_ready',
        value: Math.round(metrics.overallPageReady),
        event_category: 'userinfo_performance',
      })
    }

    gtag('event', 'cache_performance', {
      value: Math.round(metrics.cacheMetrics.hitRate),
      event_category: 'userinfo_performance',
      event_label: 'cache_hit_rate',
    })

    if (metrics.retryMetrics.totalRetries > 0) {
      gtag('event', 'api_retries', {
        value: metrics.retryMetrics.totalRetries,
        event_category: 'userinfo_performance',
        userinfo_retries: metrics.retryMetrics.userInfoRetries,
        stats_retries: metrics.retryMetrics.statsRetries,
      })
    }
  }
}
