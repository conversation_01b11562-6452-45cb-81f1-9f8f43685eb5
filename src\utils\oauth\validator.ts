/**
 * OAuth Data Validator
 *
 * Validates OAuth user data and configurations
 */

import type { OAuthUserData } from '@/types/oauth'
import { logger } from '../logger'

/**
 * OAuth Validator class
 */
export class OAuthValidator {
  /**
   * Validate required user data fields
   * @param userData - User data to validate
   * @throws Error if validation fails
   */
  static validateUserData(userData: OAuthUserData): void {
    const errors: string[] = []

    // Check required fields
    if (!userData.provider) {
      errors.push('Provider is required')
    }

    if (!userData.providerId) {
      errors.push('Provider ID is required')
    }

    if (!userData.email) {
      errors.push('Email is required')
    }

    if (!userData.rawToken) {
      errors.push('Authentication token is required')
    }

    if (errors.length > 0) {
      throw new Error(`Invalid user data: ${errors.join(', ')}`)
    }

    // Validate email format
    if (!this.isValidEmail(userData.email)) {
      throw new Error('Invalid email format')
    }

    // Log validation success
    logger.debug('User data validation passed', {
      provider: userData.provider,
      hasAllRequiredFields: true,
    })
  }

  /**
   * Validate email format
   * @param email - Email to validate
   * @returns True if email is valid
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * Validate OAuth configuration
   * @param config - Configuration to validate
   * @returns Validation result with any warnings
   */
  static validateConfig(config: Record<string, unknown> | object): {
    isValid: boolean
    warnings: string[]
  } {
    const warnings: string[] = []
    const configObj = config as Record<string, unknown>

    // Check for common configuration issues
    if (!configObj.google && !configObj.apple) {
      warnings.push('No OAuth providers configured')
    }

    if (configObj.google && typeof configObj.google === 'object') {
      const googleConfig = configObj.google as Record<string, unknown>
      if (!googleConfig.clientId) {
        warnings.push('Google client ID not configured')
      }
    }

    if (configObj.apple && typeof configObj.apple === 'object') {
      const appleConfig = configObj.apple as Record<string, unknown>
      if (!appleConfig.teamId) {
        warnings.push('Apple team ID not configured')
      }
    }

    return {
      isValid: warnings.length === 0,
      warnings,
    }
  }

  /**
   * Sanitize user data for storage/display
   * @param userData - User data to sanitize
   * @returns Sanitized user data
   */
  static sanitizeUserData(userData: OAuthUserData): OAuthUserData {
    return {
      ...userData,
      // Remove any potentially sensitive data from being logged
      rawToken: '[REDACTED]',
      // Ensure name is trimmed and safe
      name: userData.name?.trim().substring(0, 100),
      // Ensure email is lowercase for consistency
      email: userData.email.toLowerCase().trim(),
    }
  }
}
