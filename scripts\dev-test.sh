#!/bin/bash

# Dr. Muscle Development Test Script
# Optimized for fast feedback during development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Parse arguments
FULL_SUITE=false
SKIP_BUILD=false
CRITICAL_ONLY=false

while [[ $# -gt 0 ]]; do
  case $1 in
    --full)
      FULL_SUITE=true
      shift
      ;;
    --skip-build)
      SKIP_BUILD=true
      shift
      ;;
    --critical)
      CRITICAL_ONLY=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: ./scripts/dev-test.sh [--full] [--skip-build] [--critical]"
      exit 1
      ;;
  esac
done

echo "🚀 Dr. Muscle Development Test Suite"
echo "===================================="

# Quick checks (always run)
echo -e "\n${YELLOW}Running quick checks...${NC}"
npm run typecheck &
TYPECHECK_PID=$!
npm run lint &
LINT_PID=$!
npm run check:file-sizes &
FILESIZE_PID=$!

# Wait for quick checks
wait $TYPECHECK_PID
TYPECHECK_EXIT=$?
wait $LINT_PID
LINT_EXIT=$?
wait $FILESIZE_PID
FILESIZE_EXIT=$?

if [ $TYPECHECK_EXIT -ne 0 ] || [ $LINT_EXIT -ne 0 ] || [ $FILESIZE_EXIT -ne 0 ]; then
  echo -e "${RED}❌ Quick checks failed!${NC}"
  exit 1
fi
echo -e "${GREEN}✅ Quick checks passed${NC}"

# Unit tests (always run)
echo -e "\n${YELLOW}Running unit tests...${NC}"
npm run test:ci
if [ $? -ne 0 ]; then
  echo -e "${RED}❌ Unit tests failed!${NC}"
  exit 1
fi
echo -e "${GREEN}✅ Unit tests passed${NC}"

# Build check
if [ "$SKIP_BUILD" = false ]; then
  echo -e "\n${YELLOW}Building application...${NC}"
  npm run build
  if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Build failed!${NC}"
    exit 1
  fi
  echo -e "${GREEN}✅ Build successful${NC}"
  
  # Bundle size check
  echo -e "\n${YELLOW}Checking bundle size...${NC}"
  npm run analyze > /tmp/bundle-analysis.txt 2>&1 || true
  if grep -q "First Load JS" /tmp/bundle-analysis.txt; then
    BUNDLE_SIZE=$(grep "First Load JS" /tmp/bundle-analysis.txt | awk '{print $4}' | sed 's/[^0-9.]//g')
    echo "Bundle size: ${BUNDLE_SIZE}KB"
    if (( $(echo "$BUNDLE_SIZE > 150" | bc -l) )); then
      echo -e "${YELLOW}⚠️  Warning: Bundle size exceeds 150KB limit!${NC}"
    else
      echo -e "${GREEN}✅ Bundle size within limits${NC}"
    fi
  fi
fi

# E2E tests (optional)
if [ "$FULL_SUITE" = true ] || [ "$CRITICAL_ONLY" = true ]; then
  echo -e "\n${YELLOW}Running E2E tests...${NC}"
  
  if [ "$CRITICAL_ONLY" = true ]; then
    npm run test:e2e:critical
  else
    npm run test:e2e
  fi
  
  if [ $? -ne 0 ]; then
    echo -e "${RED}❌ E2E tests failed!${NC}"
    exit 1
  fi
  echo -e "${GREEN}✅ E2E tests passed${NC}"
fi

echo -e "\n${GREEN}🎉 All tests passed!${NC}"
echo -e "\nTest Summary:"
echo "- Type checking: ✅"
echo "- Linting: ✅"
echo "- Unit tests: ✅"
[ "$SKIP_BUILD" = false ] && echo "- Build: ✅"
[ "$CRITICAL_ONLY" = true ] && echo "- Critical E2E tests: ✅"
[ "$FULL_SUITE" = true ] && echo "- Full E2E tests: ✅"