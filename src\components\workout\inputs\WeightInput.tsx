import React from 'react'

interface WeightInputProps {
  weight: number
  unit: 'lbs' | 'kg'
  onChange: (value: string) => void
  onIncrement: () => void
  onDecrement: () => void
  disabled?: boolean
  error?: string
  isBodyweight?: boolean
}

export function WeightInput({
  weight,
  unit,
  onChange,
  onIncrement,
  onDecrement,
  disabled = false,
  error,
  isBodyweight = false,
}: WeightInputProps) {
  return (
    <div>
      <label
        htmlFor="weight-input"
        className="block text-sm font-medium text-text-secondary mb-2"
      >
        {isBodyweight ? 'Additional Weight' : 'Weight'}
      </label>
      <div className="flex items-center gap-2">
        <button
          type="button"
          onClick={onDecrement}
          disabled={disabled}
          className={`p-3 border rounded-lg ${
            disabled
              ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed'
              : 'bg-bg-secondary text-text-primary border-text-tertiary hover:bg-bg-tertiary'
          }`}
          aria-label="Decrease weight"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M20 12H4"
            />
          </svg>
        </button>

        <div className="flex-1 relative">
          <input
            id="weight-input"
            type="number"
            value={weight}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            min={0}
            max={1000}
            step={0.5}
            inputMode="decimal"
            className={`w-full px-4 py-3 pr-12 text-lg text-text-primary border rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-transparent ${
              error ? 'border-error' : 'border-text-tertiary'
            } ${disabled ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed' : 'bg-bg-secondary'}`}
            aria-label="Weight"
            aria-invalid={!!error}
            aria-describedby={error ? 'weight-error' : undefined}
          />
          <span className="absolute right-3 top-1/2 -translate-y-1/2 text-text-tertiary">
            {unit}
          </span>
        </div>

        <button
          type="button"
          onClick={onIncrement}
          disabled={disabled}
          className={`p-3 border rounded-lg ${
            disabled
              ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed'
              : 'bg-bg-secondary text-text-primary border-text-tertiary hover:bg-bg-tertiary'
          }`}
          aria-label="Increase weight"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4v16m8-8H4"
            />
          </svg>
        </button>
      </div>
      {error && (
        <p id="weight-error" className="mt-1 text-sm text-error" role="alert">
          {error}
        </p>
      )}
    </div>
  )
}
