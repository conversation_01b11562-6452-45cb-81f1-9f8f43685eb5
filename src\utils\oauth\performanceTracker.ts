/**
 * OAuth Performance Tracker
 *
 * Tracks and reports performance metrics for OAuth operations
 */

import type { OAuthProvider } from '@/types/oauth'
import { PerformanceMonitor } from '../performance'
import { logger } from '../logger'

/**
 * Performance metric statistics
 */
interface MetricStats {
  count: number
  totalDuration: number
  averageDuration: number
}

/**
 * OAuth Performance Tracker class
 */
export class OAuthPerformanceTracker {
  /**
   * Track OAuth performance metrics
   * @param provider - OAuth provider
   * @param action - Action being tracked
   * @param errorCode - Optional error code for failures
   */
  static markPerformance(
    provider: OAuthProvider,
    action: 'success' | 'error' | 'init' | 'signIn',
    errorCode?: string
  ): void {
    const markName = errorCode
      ? `oauth-${provider}-${action}-${errorCode}`
      : `oauth-${provider}-${action}`

    PerformanceMonitor.mark(markName)

    // Log performance event
    logger.debug('OAuth performance mark', {
      provider,
      action,
      errorCode,
      markName,
    })
  }

  /**
   * Mark the start of an OAuth operation
   * @param provider - OAuth provider
   * @param operation - Operation name
   * @returns Mark name for measuring
   */
  static markStart(provider: OAuthProvider, operation: string): string {
    const markName = `oauth-${provider}-${operation}-start`
    PerformanceMonitor.mark(markName)
    return markName
  }

  /**
   * Mark the completion of an OAuth operation and measure duration
   * @param provider - OAuth provider
   * @param operation - Operation name
   * @param startMark - Start mark name
   */
  static markComplete(
    provider: OAuthProvider,
    operation: string,
    startMark: string
  ): void {
    const endMark = `oauth-${provider}-${operation}-complete`
    PerformanceMonitor.mark(endMark)

    const measureName = `oauth-${provider}-${operation}`
    PerformanceMonitor.measure(measureName, startMark, endMark)
  }

  /**
   * Get OAuth performance metrics
   * @returns Performance metrics for OAuth operations
   */
  static getMetrics(): Record<string, unknown> {
    const entries = PerformanceMonitor.getEntries()
    const oauthEntries = entries.filter((entry) =>
      entry.name.startsWith('oauth-')
    )

    const metrics = {
      totalOperations: oauthEntries.length,
      byProvider: {} as Record<string, MetricStats>,
      byAction: {} as Record<string, MetricStats>,
      errors: {} as Record<string, number>,
    }

    // Group by provider and action
    oauthEntries.forEach((entry) => {
      const parts = entry.name.split('-')
      if (parts.length >= 3 && parts[1] && parts[2]) {
        const provider = parts[1]
        const action = parts[2]

        // By provider
        this.updateMetricStats(metrics.byProvider, provider, entry.duration)

        // By action
        this.updateMetricStats(metrics.byAction, action, entry.duration)

        // Track errors
        if (action === 'error' && parts.length >= 4 && parts[3]) {
          const errorCode = parts[3]
          if (!metrics.errors[errorCode]) {
            metrics.errors[errorCode] = 0
          }
          metrics.errors[errorCode]++
        }
      }
    })

    return metrics
  }

  /**
   * Update metric statistics
   */
  private static updateMetricStats(
    stats: Record<string, MetricStats>,
    key: string,
    duration: number
  ): void {
    if (!stats[key]) {
      stats[key] = {
        count: 0,
        totalDuration: 0,
        averageDuration: 0,
      }
    }

    stats[key].count++
    stats[key].totalDuration += duration
    stats[key].averageDuration = stats[key].totalDuration / stats[key].count
  }

  /**
   * Clear OAuth performance metrics
   */
  static clearMetrics(): void {
    // Implementation depends on PerformanceMonitor API
    logger.debug('OAuth performance metrics cleared')
  }
}
