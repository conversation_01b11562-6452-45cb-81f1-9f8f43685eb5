import { useProgramStore } from './programStore'

/**
 * Development-only cache debugging utilities
 */
export function setupProgramCacheDebug(): void {
  if (process.env.NODE_ENV === 'development') {
    interface WindowWithProgramCache extends Window {
      programCache?: {
        getStats: () => {
          totalSize: number
          programAge: number | null
          progressAge: number | null
          statsAge: number | null
          isHydrated: boolean
        }
        clearCache: () => void
        logCache: () => void
      }
    }
    ;(window as WindowWithProgramCache).programCache = {
      getStats: () => useProgramStore.getState().getCacheStats(),
      clearCache: () => useProgramStore.getState().clearAllCache(),
      logCache: () => {
        // Cache logging removed - use getStats() instead
      },
    }
  }
}
