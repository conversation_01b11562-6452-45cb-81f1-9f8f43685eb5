/**
 * User Stats Parser
 *
 * Functions for parsing and extracting user stats from API responses
 */

import { UserStats, isUserStats } from '@/types/userStats'
import type { GetUserWorkoutLogAverageResponse } from '@/types'
import { logger } from '@/utils/logger'
import {
  calculateStatsFromWorkoutHistory,
  calculateWeekStreakFromDates,
  calculateVolumeFromAverages,
  extractUniqueWorkoutDays,
  type WorkoutDate,
} from './calculator'

/**
 * API response shape for stats extraction
 */
export interface StatsApiResponse {
  // Original expected fields (not being returned)
  ConsecutiveWeeks?: number | Array<{ ConsecutiveWeeks: number }>
  ConsecutiveWeeksModel?: { ConsecutiveWeeks: number }
  WorkoutCount?: number
  TotalWorkoutCompleted?: number
  HistoryExerciseModel?:
    | {
        TotalWeight?: { Lb: number }
        TotalWorkoutCompleted?: number
        ConsecutiveWeeks?: number
      }
    | Array<{
        TotalWeight?: { Lb: number }
        TotalWorkoutCompleted?: number
        ConsecutiveWeeks?: number
      }>
  Averages?: Array<{
    Date: string
    Average: {
      Lb: number
      Kg: number
    }
  }>
  SetsDate?: string[]
  LastMonthWorkoutCount?: number
  LastConsecutiveWorkoutDays?: number

  // New fields that might contain stats
  GetUserProgramInfoResponseModel?: {
    RecommendedProgram?: {
      RemainingToLevelUp?: number
    }
    TotalWorkoutCompleted?: number
    UserStats?: {
      WorkoutCount?: number
      ConsecutiveWeeks?: number
      TotalWeight?: { Lb: number }
    }
  }

  // Workout history that we can use to calculate stats
  WorkoutLogDates?: WorkoutDate[]

  // Exercise data (what we're currently getting)
  AverageExercises?: Array<{ Label?: string }>
  AllExercises?: Array<{ Label?: string }>
}

/**
 * Log response data for debugging
 */
function logResponseData(statsData: StatsApiResponse): void {
  logger.log('[UserStats] Extracting from data:', {
    hasGetUserProgramInfoResponseModel:
      !!statsData.GetUserProgramInfoResponseModel,
    hasConsecutiveWeeks: !!statsData.ConsecutiveWeeks,
    hasWorkoutCount: !!statsData.WorkoutCount,
    hasHistoryExerciseModel: !!statsData.HistoryExerciseModel,
    hasWorkoutLogDates: !!statsData.WorkoutLogDates,
    hasSetsDate: !!statsData.SetsDate,
    hasAverages: !!statsData.Averages,
    topLevelKeys: Object.keys(statsData).slice(0, 15),
    // Log actual values
    ConsecutiveWeeks: statsData.ConsecutiveWeeks,
    WorkoutCount: statsData.WorkoutCount,
    SetsDateLength: statsData.SetsDate?.length,
    AveragesLength: Array.isArray(statsData.Averages)
      ? statsData.Averages.length
      : undefined,
  })
}

/**
 * Extract stats from GetUserProgramInfoResponseModel
 */
function extractFromProgramInfo(
  statsData: StatsApiResponse
): Partial<UserStats> {
  const result: Partial<UserStats> = {
    weekStreak: 0,
    workoutsCompleted: 0,
    lbsLifted: 0,
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const programInfoModel = statsData.GetUserProgramInfoResponseModel as any
  if (programInfoModel?.UserStats) {
    const userStats = programInfoModel.UserStats
    if (userStats.WorkoutCount !== undefined) {
      result.workoutsCompleted = userStats.WorkoutCount
    }
    if (userStats.ConsecutiveWeeks !== undefined) {
      result.weekStreak = userStats.ConsecutiveWeeks
    }
    if (userStats.TotalWeight?.Lb !== undefined) {
      result.lbsLifted = Math.round(userStats.TotalWeight.Lb)
    }
  }

  return result
}

/**
 * Extract stats from HistoryExerciseModel
 */
function extractFromHistoryExerciseModel(
  statsData: StatsApiResponse
): Partial<UserStats> {
  const result: Partial<UserStats> = {
    weekStreak: 0,
    workoutsCompleted: 0,
    lbsLifted: 0,
  }

  if (statsData.HistoryExerciseModel) {
    const historyModel = Array.isArray(statsData.HistoryExerciseModel)
      ? statsData.HistoryExerciseModel[0]
      : statsData.HistoryExerciseModel

    if (historyModel) {
      if (typeof historyModel.TotalWorkoutCompleted === 'number') {
        result.workoutsCompleted = historyModel.TotalWorkoutCompleted
      }
      if (historyModel.TotalWeight?.Lb) {
        result.lbsLifted = Math.round(Number(historyModel.TotalWeight.Lb) || 0)
      }
      if (historyModel.ConsecutiveWeeks !== undefined) {
        result.weekStreak = Number(historyModel.ConsecutiveWeeks) || 0
      }
    }
  }

  return result
}

/**
 * Extract workout count from various fields
 */
function extractWorkoutCount(statsData: StatsApiResponse): number {
  if (typeof statsData.WorkoutCount === 'number') {
    return statsData.WorkoutCount
  }
  if (typeof statsData.TotalWorkoutCompleted === 'number') {
    return statsData.TotalWorkoutCompleted
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const programInfoModel = statsData.GetUserProgramInfoResponseModel as any
  if (programInfoModel?.TotalWorkoutCompleted) {
    return programInfoModel.TotalWorkoutCompleted
  }
  return 0
}

/**
 * Extract consecutive weeks from various sources
 */
function extractConsecutiveWeeks(statsData: StatsApiResponse): number {
  // Check array format
  if (
    Array.isArray(statsData.ConsecutiveWeeks) &&
    statsData.ConsecutiveWeeks.length > 0
  ) {
    const firstWeek = statsData.ConsecutiveWeeks[0]
    if (
      firstWeek &&
      typeof firstWeek === 'object' &&
      'ConsecutiveWeeks' in firstWeek
    ) {
      return Number(firstWeek.ConsecutiveWeeks) || 0
    }
  }
  // Check number format
  if (typeof statsData.ConsecutiveWeeks === 'number') {
    return statsData.ConsecutiveWeeks
  }
  // Check model format
  if (statsData.ConsecutiveWeeksModel?.ConsecutiveWeeks) {
    return Number(statsData.ConsecutiveWeeksModel.ConsecutiveWeeks) || 0
  }
  return 0
}

/**
 * Extract weight from Averages field
 */
function extractWeightFromAverages(averages: unknown): number {
  // Case 1: Averages is an array of objects with date/average data
  if (Array.isArray(averages)) {
    return calculateVolumeFromAverages(
      averages as Parameters<typeof calculateVolumeFromAverages>[0]
    )
  }
  // Case 2: Averages is an object with TotalWeight or TotalVolume
  else if (typeof averages === 'object' && averages !== null) {
    const avgObj = averages as Record<string, number>
    if (avgObj.TotalWeight && avgObj.TotalWeight > 0) {
      return Math.round(avgObj.TotalWeight)
    }
    if (avgObj.TotalVolume && avgObj.TotalVolume > 0) {
      return Math.round(avgObj.TotalVolume)
    }
    if (avgObj.TotalLbsLifted && avgObj.TotalLbsLifted > 0) {
      return Math.round(avgObj.TotalLbsLifted)
    }
  }
  return 0
}

/**
 * Extract UserStats from API response data
 * Handles various response formats from the API
 */
export function extractUserStats(
  data: StatsApiResponse | GetUserWorkoutLogAverageResponse
): UserStats {
  const statsData = data as StatsApiResponse & GetUserWorkoutLogAverageResponse
  // Initialize with defaults
  let weekStreak = 0
  let workoutsCompleted = 0
  let lbsLifted = 0

  // Debug log to see what we're working with
  if (process.env.NODE_ENV === 'development') {
    logResponseData(statsData)
  }

  // 1. Check GetUserProgramInfoResponseModel.UserStats (if exists)
  const extractedFromProgramInfo = extractFromProgramInfo(statsData)
  weekStreak = extractedFromProgramInfo.weekStreak ?? 0
  workoutsCompleted = extractedFromProgramInfo.workoutsCompleted ?? 0
  lbsLifted = extractedFromProgramInfo.lbsLifted ?? 0

  // 2. Check HistoryExerciseModel for total workout count (primary source)
  if (workoutsCompleted === 0 || lbsLifted === 0 || weekStreak === 0) {
    const extractedFromHistory = extractFromHistoryExerciseModel(statsData)
    if (workoutsCompleted === 0)
      workoutsCompleted = extractedFromHistory.workoutsCompleted ?? 0
    if (lbsLifted === 0) lbsLifted = extractedFromHistory.lbsLifted ?? 0
    if (weekStreak === 0) weekStreak = extractedFromHistory.weekStreak ?? 0
  }

  // 3. Check standard fields as fallback
  if (workoutsCompleted === 0) {
    workoutsCompleted = extractWorkoutCount(statsData)
  }

  // 4. Extract consecutive weeks from various sources
  if (weekStreak === 0) {
    weekStreak = extractConsecutiveWeeks(statsData)
  }

  // 5. Extract total weight lifted from Averages
  if (lbsLifted === 0 && statsData.Averages) {
    lbsLifted = extractWeightFromAverages(statsData.Averages)
  }

  // 6. Calculate from workout history (if available)
  const workoutLogDates = (statsData as StatsApiResponse).WorkoutLogDates
  if (
    (workoutsCompleted === 0 || lbsLifted === 0 || weekStreak === 0) &&
    workoutLogDates
  ) {
    const calculated = calculateStatsFromWorkoutHistory(workoutLogDates)
    if (workoutsCompleted === 0) workoutsCompleted = calculated.workouts
    if (lbsLifted === 0) lbsLifted = calculated.volume
    if (weekStreak === 0) weekStreak = calculated.weeks
  }

  // 7. Use SetsDate as a fallback for workout count and week streak
  if (
    (workoutsCompleted === 0 || weekStreak === 0) &&
    statsData.SetsDate &&
    Array.isArray(statsData.SetsDate)
  ) {
    if (workoutsCompleted === 0) {
      workoutsCompleted = extractUniqueWorkoutDays(statsData.SetsDate)
    }
    if (weekStreak === 0) {
      const dates = statsData.SetsDate.map((d: string) => new Date(d))
      weekStreak = calculateWeekStreakFromDates(dates)
    }
  }

  // 8. Handle LastMonthWorkoutCount if other sources are empty
  if (workoutsCompleted === 0) {
    const lastMonthCount = (statsData as StatsApiResponse).LastMonthWorkoutCount
    if (typeof lastMonthCount === 'number' && lastMonthCount > 0) {
      workoutsCompleted = lastMonthCount
    }
  }

  const stats: UserStats = {
    weekStreak,
    workoutsCompleted,
    lbsLifted,
  }

  // Debug log final extracted values
  if (process.env.NODE_ENV === 'development') {
    logger.log('[UserStats] Final extracted values:', stats)
  }

  // Validate the extracted stats
  if (!isUserStats(stats)) {
    logger.warn('[UserStats API] Extracted stats failed validation:', stats)
    // Return defaults if validation fails
    return {
      weekStreak: 0,
      workoutsCompleted: 0,
      lbsLifted: 0,
    }
  }

  return stats
}
