import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useAuth } from '@/hooks/useAuth'
import { authApi } from '@/api/auth'
import { useAuthStore } from '@/stores/authStore'

// Mock the API
vi.mock('@/api/auth', () => ({
  authApi: {
    logout: vi.fn(),
  },
}))

// Mock the auth store
vi.mock('@/stores/authStore', () => ({
  useAuthStore: vi.fn(),
}))

// Create wrapper for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return function ({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
  }
}

describe('useAuth logout', () => {
  const mockStoreLogout = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    ;(useAuthStore as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      user: { email: '<EMAIL>' },
      token: 'test-token',
      isAuthenticated: true,
      isLoading: false,
      error: null,
      setAuth: vi.fn(),
      logout: mockStoreLogout,
      setError: vi.fn(),
      clearError: vi.fn(),
    })
  })

  it('should not call API logout endpoint', async () => {
    const { result } = renderHook(() => useAuth(), { wrapper: createWrapper() })

    await act(async () => {
      result.current.logout()
    })

    // API logout should NOT be called
    expect(authApi.logout).not.toHaveBeenCalled()
  })

  it('should call store logout directly', async () => {
    const { result } = renderHook(() => useAuth(), { wrapper: createWrapper() })

    await act(async () => {
      result.current.logout()
    })

    // Store logout should be called
    expect(mockStoreLogout).toHaveBeenCalledTimes(1)
  })

  it('should handle logout mutation state correctly', async () => {
    const { result } = renderHook(() => useAuth(), { wrapper: createWrapper() })

    // Initial state
    expect(result.current.logoutMutation.isPending).toBe(false)

    await act(async () => {
      result.current.logout()
    })

    // Should complete immediately since no async API call
    expect(mockStoreLogout).toHaveBeenCalled()
  })
})
