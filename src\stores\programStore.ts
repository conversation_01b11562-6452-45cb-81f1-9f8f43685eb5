import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { CACHE_TTL, initialCachedData, cacheUtils } from './programCacheUtils'
import type { ProgramState, ProgramStore } from './programStoreTypes'

/**
 * Initial state for the program store
 */
const initialState: ProgramState = {
  cachedData: initialCachedData,
  hasHydrated: false,
  isLoadingProgram: false,
  isLoadingProgress: false,
  isLoadingStats: false,
  isRefreshingProgram: false,
  isRefreshingProgress: false,
  isRefreshingStats: false,
  programError: null,
  progressError: null,
  statsError: null,
}

/**
 * Program store with comprehensive caching support
 */
export const useProgramStore = create<ProgramStore>()(
  persist(
    immer((set, get) => ({
      ...initialState,

      // Cache setters with automatic timestamp updates
      setCachedProgram: (program) =>
        set((state) => {
          state.cachedData.program = program
          state.cachedData.lastUpdated.program = Date.now()
        }),

      setCachedProgress: (progress) =>
        set((state) => {
          state.cachedData.progress = progress
          state.cachedData.lastUpdated.progress = Date.now()
        }),

      setCachedStats: (stats) =>
        set((state) => {
          state.cachedData.stats = stats
          state.cachedData.lastUpdated.stats = Date.now()
        }),

      // Cache getters
      getCachedProgram: () => get().cachedData.program,
      getCachedProgress: () => get().cachedData.progress,
      getCachedStats: () => get().cachedData.stats,

      // Cache staleness checkers
      isProgramCacheStale: () => {
        const { program } = get().cachedData.lastUpdated
        return cacheUtils.isCacheStale(program, CACHE_TTL.program)
      },

      isProgressCacheStale: () => {
        const { progress } = get().cachedData.lastUpdated
        return cacheUtils.isCacheStale(progress, CACHE_TTL.progress)
      },

      isStatsCacheStale: () => {
        const { stats } = get().cachedData.lastUpdated
        return cacheUtils.isCacheStale(stats, CACHE_TTL.stats)
      },

      isCacheStale: (type) => {
        const lastUpdated = get().cachedData.lastUpdated[type]
        return cacheUtils.isCacheStale(lastUpdated, CACHE_TTL[type])
      },

      // Clear expired cache entries
      clearExpiredCache: () =>
        set((state) => {
          state.cachedData = cacheUtils.clearExpiredEntries(state.cachedData)
        }),

      // Clear all cache
      clearAllCache: () =>
        set((state) => {
          state.cachedData = initialState.cachedData
        }),

      // Loading state setters
      setIsLoadingProgram: (isLoading) =>
        set((state) => {
          state.isLoadingProgram = isLoading
        }),

      setIsLoadingProgress: (isLoading) =>
        set((state) => {
          state.isLoadingProgress = isLoading
        }),

      setIsLoadingStats: (isLoading) =>
        set((state) => {
          state.isLoadingStats = isLoading
        }),

      // Background refresh state setters
      setIsRefreshingProgram: (isRefreshing) =>
        set((state) => {
          state.isRefreshingProgram = isRefreshing
        }),

      setIsRefreshingProgress: (isRefreshing) =>
        set((state) => {
          state.isRefreshingProgress = isRefreshing
        }),

      setIsRefreshingStats: (isRefreshing) =>
        set((state) => {
          state.isRefreshingStats = isRefreshing
        }),

      // Error setters
      setProgramError: (error) =>
        set((state) => {
          state.programError = error
        }),

      setProgressError: (error) =>
        set((state) => {
          state.progressError = error
        }),

      setStatsError: (error) =>
        set((state) => {
          state.statsError = error
        }),

      // Hydration setter
      setHasHydrated: (hasHydrated) =>
        set((state) => {
          state.hasHydrated = hasHydrated
        }),

      // Cache age calculator
      getCacheAge: (type) => {
        const lastUpdated = get().cachedData.lastUpdated[type]
        return cacheUtils.getCacheAge(lastUpdated)
      },

      // Cache statistics
      getCacheStats: () => {
        const state = get()
        return cacheUtils.getCacheStats(state.cachedData, state.hasHydrated)
      },
    })),
    {
      name: 'drmuscle-program',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        cachedData: state.cachedData,
      }),
      onRehydrateStorage: () => (state) => {
        // Mark as hydrated when persistence loads
        state?.setHasHydrated(true)
        // Clean up any expired cache entries
        state?.clearExpiredCache()
      },
    }
  )
)
