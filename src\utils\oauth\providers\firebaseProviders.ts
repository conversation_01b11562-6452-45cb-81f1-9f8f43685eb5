import { GoogleAuthProvider, OAuthProvider } from 'firebase/auth'
import { PerformanceMonitor } from '../../performance'

export class FirebaseProviders {
  private static googleProvider: GoogleAuthProvider | null = null

  private static appleProvider: OAuthProvider | null = null

  private static isInitialized = false

  static async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      PerformanceMonitor.mark('firebase-oauth-init-start')

      this.googleProvider = new GoogleAuthProvider()
      this.googleProvider.addScope('profile')
      this.googleProvider.addScope('email')

      this.appleProvider = new OAuthProvider('apple.com')
      this.appleProvider.addScope('email')
      this.appleProvider.addScope('name')

      this.isInitialized = true

      PerformanceMonitor.mark('firebase-oauth-init-complete')
      PerformanceMonitor.measure(
        'firebase-oauth-init',
        'firebase-oauth-init-start',
        'firebase-oauth-init-complete'
      )
    } catch (error) {
      this.isInitialized = false
      throw error
    }
  }

  static getGoogleProvider(): GoogleAuthProvider {
    if (!this.googleProvider) {
      throw new Error('Google provider not initialized')
    }
    return this.googleProvider
  }

  static getAppleProvider(): OAuthProvider {
    if (!this.appleProvider) {
      throw new Error('Apple provider not initialized')
    }
    return this.appleProvider
  }

  static isReady(): boolean {
    return this.isInitialized
  }
}
