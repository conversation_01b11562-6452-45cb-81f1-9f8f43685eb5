import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor, act } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useWorkoutPrefetch } from '../useWorkoutPrefetch'
import { workoutApi } from '@/api/workouts'

// Mock the workout API
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserProgramInfo: vi.fn(),
    getUserWorkout: vi.fn(),
  },
}))

describe('useWorkoutPrefetch - Showcase', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  const createWrapper = () => {
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
    return wrapper
  }

  it('Showcase 1: Success screen with prefetch during login', async () => {
    // Simulate successful API responses
    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: {
          Id: 123,
          Label: 'Full Body Workout',
          RemainingToLevelUp: 3,
        },
        NextWorkoutTemplate: {
          Id: 456,
          Label: 'Workout A - Push',
          IsSystemExercise: true,
        },
      },
    }

    const mockWorkouts = [
      {
        Id: 456,
        Label: 'Workout A - Push',
        Exercises: [
          { Id: 1, Name: 'Bench Press', Order: 1 },
          { Id: 2, Name: 'Shoulder Press', Order: 2 },
          { Id: 3, Name: 'Tricep Dips', Order: 3 },
          { Id: 4, Name: 'Lateral Raises', Order: 4 },
          { Id: 5, Name: 'Push-ups', Order: 5 },
        ],
      },
    ]

    vi.mocked(workoutApi.getUserProgramInfo).mockResolvedValue(mockProgramInfo)
    vi.mocked(workoutApi.getUserWorkout).mockResolvedValue(mockWorkouts)

    const { result } = renderHook(() => useWorkoutPrefetch(), {
      wrapper: createWrapper(),
    })

    // Initial state - not started
    expect(result.current.progress).toBe(0)
    expect(result.current.isComplete).toBe(false)
    expect(result.current.status).toBe('Starting...')

    // Start prefetching (simulating after login success)
    await act(async () => {
      result.current.startPrefetch()
    })

    // Should show progress during loading
    await waitFor(() => {
      expect(result.current.progress).toBeGreaterThan(0)
    })

    // Should complete successfully
    await waitFor(() => {
      expect(result.current.isComplete).toBe(true)
      expect(result.current.progress).toBe(100)
      expect(result.current.status).toBe('Ready!')
    })

    // Verify data was prefetched
    expect(workoutApi.getUserProgramInfo).toHaveBeenCalledTimes(1)
    expect(workoutApi.getUserWorkout).toHaveBeenCalledTimes(1)
  })

  it('Showcase 2: Handling network errors gracefully', async () => {
    // Simulate both APIs failing
    vi.mocked(workoutApi.getUserProgramInfo).mockRejectedValue(
      new Error('Network error')
    )
    vi.mocked(workoutApi.getUserWorkout).mockRejectedValue(
      new Error('Network error')
    )

    const { result } = renderHook(() => useWorkoutPrefetch(), {
      wrapper: createWrapper(),
    })

    // Start prefetching
    await act(async () => {
      result.current.startPrefetch()
    })

    // Should handle error gracefully
    await waitFor(() => {
      expect(result.current.error).toBe('Failed to load workout data')
      expect(result.current.status).toBe('Error loading workout data')
      expect(result.current.isComplete).toBe(false)
    })

    // User can retry by resetting and starting again
    act(() => {
      result.current.reset()
    })

    expect(result.current.progress).toBe(0)
    expect(result.current.error).toBeNull()
    expect(result.current.status).toBe('Starting...')
  })

  it('Showcase 3: Integration with success screen component', async () => {
    // This showcases how the hook would be used in a success screen component

    const mockProgramInfo = {
      GetUserProgramInfoResponseModel: {
        RecommendedProgram: {
          Id: 789,
          Label: 'Strength Builder',
          RemainingToLevelUp: 5,
        },
        NextWorkoutTemplate: {
          Id: 101,
          Label: 'Day 1 - Upper Body',
        },
      },
    }

    const mockWorkouts = [
      {
        Id: 101,
        Label: 'Day 1 - Upper Body',
        Exercises: Array(8)
          .fill(null)
          .map((_, i) => ({
            Id: i + 1,
            Name: `Exercise ${i + 1}`,
            Order: i + 1,
          })),
      },
    ]

    // Simulate delayed responses to show progress
    vi.mocked(workoutApi.getUserProgramInfo).mockImplementation(
      () =>
        new Promise((resolve) => {
          setTimeout(() => resolve(mockProgramInfo), 500)
        })
    )

    vi.mocked(workoutApi.getUserWorkout).mockImplementation(
      () =>
        new Promise((resolve) => {
          setTimeout(() => resolve(mockWorkouts), 800)
        })
    )

    const queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )

    const { result } = renderHook(() => useWorkoutPrefetch(), { wrapper })

    // Simulate component lifecycle
    const statusUpdates: string[] = []
    const progressUpdates: number[] = []

    // Start prefetching
    await act(async () => {
      result.current.startPrefetch()
    })

    // Capture status and progress updates
    await waitFor(
      () => {
        if (!statusUpdates.includes(result.current.status)) {
          statusUpdates.push(result.current.status)
        }
        if (!progressUpdates.includes(result.current.progress)) {
          progressUpdates.push(result.current.progress)
        }
        return result.current.isComplete
      },
      { timeout: 2000 }
    )

    // Verify progressive loading
    expect(progressUpdates).toContain(25) // Started
    expect(progressUpdates).toContain(50) // Program info loaded
    expect(progressUpdates).toContain(75) // Workout data loading
    expect(progressUpdates).toContain(100) // Complete

    // Verify status messages changed
    expect(statusUpdates.length).toBeGreaterThan(2)
    expect(statusUpdates).toContain('Loading workout program...')

    // Verify data is in cache for immediate use
    const cachedProgramInfo = queryClient.getQueryData(['userProgramInfo'])
    const cachedWorkouts = queryClient.getQueryData(['userWorkout'])

    expect(cachedProgramInfo).toEqual(mockProgramInfo)
    expect(cachedWorkouts).toEqual(mockWorkouts)

    // Success screen would now transition to workout page
    expect(result.current.isComplete).toBe(true)
    expect(result.current.error).toBeNull()
  })
})
