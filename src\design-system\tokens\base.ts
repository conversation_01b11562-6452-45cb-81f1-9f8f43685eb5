/**
 * Base Design Tokens
 * Foundation tokens used across all theme variations
 */

import { BaseTokens } from '../types'

export const baseTokens: BaseTokens = {
  spacing: {
    unit: 4, // 4px base unit
    xs: '0.25rem', // 4px
    sm: '0.5rem', // 8px
    md: '1rem', // 16px
    lg: '1.5rem', // 24px
    xl: '2rem', // 32px
    '2xl': '3rem', // 48px
    '3xl': '4rem', // 64px
    '4xl': '6rem', // 96px
  },
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
  },
  touchTargets: {
    minimum: '44px', // iOS/Android minimum
    comfortable: '56px', // Comfortable for workout conditions
    large: '64px', // Extra large for active workouts
  },
}
