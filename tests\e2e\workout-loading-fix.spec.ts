import { test, expect } from '@playwright/test'

const testUser = {
  email: '<EMAIL>',
  password: 'Dr123456',
}

test.describe('Workout Loading Fix', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login')
    await page.fill('input[name="email"]', testUser.email)
    await page.fill('input[name="password"]', testUser.password)
    await page.click('button[type="submit"]')

    // Wait for redirect to program page
    await page.waitForURL('**/program')
  })

  test('should display exercises after clicking Continue to workout', async ({
    page,
  }) => {
    // Wait for program page to load
    await expect(page.locator('h1:text("Your Program")')).toBeVisible()

    // Click Continue to workout
    await page.getByRole('button', { name: 'Continue to workout' }).click()

    // Wait for navigation to workout page
    await page.waitForURL('**/workout')

    // Wait for exercises to load (should not show "No workout scheduled")
    await expect(page.locator('text=No workout scheduled')).not.toBeVisible({
      timeout: 10000,
    })

    // Should display exercise cards (at least one)
    const exerciseCards = page.locator('[data-testid="exercise-card"]')
    await expect(exerciseCards.first()).toBeVisible({ timeout: 10000 })

    // Verify we have exercises (should have 13 based on the API response)
    const exerciseCount = await exerciseCards.count()
    expect(exerciseCount).toBeGreaterThan(0)

    // Verify Start Workout button is visible and enabled
    const startButton = page.getByRole('button', { name: 'Start Workout' })
    await expect(startButton).toBeVisible()
    await expect(startButton).toBeEnabled()

    // Check that "Checking for updates..." disappears
    await expect(page.locator('text=Checking for updates...')).not.toBeVisible()
  })

  test('should handle API response with Exercises field correctly', async ({
    page,
  }) => {
    // Navigate directly to test-api page
    await page.goto('/test-api')

    // Click Test APIs button
    await page.getByRole('button', { name: 'Test APIs' }).click()

    // Wait for API responses
    await expect(
      page.locator('text=getUserWorkoutProgramInfo Result:')
    ).toBeVisible({ timeout: 30000 })

    // Verify that the response contains exercises
    const responseText = await page.locator('pre').first().textContent()
    expect(responseText).toContain('"Exercises":')
    expect(responseText).toContain('Standing Shoulder Press')

    // Navigate to workout page
    await page.goto('/workout')

    // Verify exercises are displayed
    await expect(
      page.locator('[data-testid="exercise-card"]').first()
    ).toBeVisible({ timeout: 10000 })
  })
})
