# OAuth Integration Layer

This directory contains the OAuth integration layer for Dr. Muscle X, providing a unified interface for Google and Apple Sign-In.

## Architecture

```
oauth/
├── oauthIntegration.ts    # Main integration layer
├── googleOAuth.ts         # Google Sign-In helper
├── appleOAuth.ts          # Apple Sign-In helper
├── index.ts               # Central exports
└── __tests__/             # Unit tests
```

## Usage

### Basic Setup

```typescript
import { oauthIntegration } from '@/utils/oauth'

// Initialize OAuth with configuration
oauthIntegration.initialize({
  google: {
    clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
    autoSelect: true,
    cancelOnTapOutside: true,
  },
  apple: {
    clientId: process.env.NEXT_PUBLIC_APPLE_CLIENT_ID,
    redirectUri: `${window.location.origin}/auth/apple/callback`,
    usePopup: true,
  },
})
```

### Sign In

```typescript
import { oauthIntegration } from '@/utils/oauth'
import type { OAuthUserData, OAuthError } from '@/utils/oauth'

// Handle successful OAuth
const handleSuccess = async (userData: OAuthUserData) => {
  console.log('OAuth success:', userData)
  // userData contains:
  // - provider: 'google' | 'apple'
  // - email: user's email
  // - name: full name (if available)
  // - pictureUrl: profile picture (Google only)
  // - rawToken: JWT token for backend validation
}

// Handle OAuth errors
const handleError = (error: OAuthError) => {
  console.error('OAuth failed:', error)
  // Handle different error types:
  switch (error.code) {
    case 'user_cancelled':
      // User cancelled sign-in
      break
    case 'network_error':
      // Network connection failed
      break
    case 'invalid_token':
      // Invalid authentication response
      break
    default:
    // Generic error
  }
}

// Sign in with Google
await oauthIntegration.signIn('google', handleSuccess, handleError)

// Sign in with Apple
await oauthIntegration.signIn('apple', handleSuccess, handleError)
```

### Check Provider Availability

```typescript
// Check if provider SDK is loaded
const isGoogleAvailable = oauthIntegration.isProviderAvailable('google')
const isAppleAvailable = oauthIntegration.isProviderAvailable('apple')

// Check if provider is initialized and ready
const isGoogleReady = oauthIntegration.isProviderReady('google')
const isAppleReady = oauthIntegration.isProviderReady('apple')
```

### Error Handling

```typescript
// Create error handler with context
const errorHandler = oauthIntegration.createErrorHandler(
  (error) => {
    // Show error to user
    toast.error(error.message)
  },
  { userId: currentUser?.id, action: 'login' }
)

// Use in sign-in
await oauthIntegration.signIn('google', handleSuccess, errorHandler)
```

### Performance Monitoring

```typescript
// Get OAuth performance metrics
const metrics = oauthIntegration.getPerformanceMetrics()
console.log('OAuth Performance:', metrics)
// Returns:
// {
//   totalOperations: number,
//   byProvider: { google: {...}, apple: {...} },
//   byAction: { success: {...}, error: {...} },
//   errors: { network_error: count, ... }
// }
```

## Integration with React

Example React component:

```typescript
import { useState } from 'react'
import { oauthIntegration } from '@/utils/oauth'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/authStore'

export function OAuthButtons() {
  const [loading, setLoading] = useState<'google' | 'apple' | null>(null)
  const router = useRouter()
  const { login } = useAuthStore()

  const handleOAuthSuccess = async (userData: OAuthUserData) => {
    try {
      // In production, send userData to backend API
      const loginRequest = await oauthIntegration.handleOAuthSuccess(
        userData,
        userData.provider
      )

      // Call Dr. Muscle backend API
      const response = await api.oauthLogin(loginRequest)

      // Store auth tokens
      login(response.token, response.user)

      // Redirect to dashboard
      router.push('/dashboard')
    } catch (error) {
      console.error('Login failed:', error)
    } finally {
      setLoading(null)
    }
  }

  const handleOAuthError = (error: OAuthError) => {
    console.error('OAuth error:', error)
    setLoading(null)

    // Show user-friendly error message
    if (error.code === 'user_cancelled') {
      // User cancelled, no need to show error
      return
    }

    toast.error(error.message)
  }

  const signInWithProvider = async (provider: OAuthProvider) => {
    setLoading(provider)
    await oauthIntegration.signIn(provider, handleOAuthSuccess, handleOAuthError)
  }

  return (
    <div className="space-y-3">
      <button
        onClick={() => signInWithProvider('google')}
        disabled={loading !== null}
        className="w-full flex items-center justify-center gap-3 px-4 py-3 border rounded-lg"
      >
        {loading === 'google' ? (
          <Spinner />
        ) : (
          <>
            <GoogleIcon />
            Continue with Google
          </>
        )}
      </button>

      <button
        onClick={() => signInWithProvider('apple')}
        disabled={loading !== null}
        className="w-full flex items-center justify-center gap-3 px-4 py-3 border rounded-lg"
      >
        {loading === 'apple' ? (
          <Spinner />
        ) : (
          <>
            <AppleIcon />
            Continue with Apple
          </>
        )}
      </button>
    </div>
  )
}
```

## Testing

Run the OAuth integration tests:

```bash
npm test -- src/utils/oauth/__tests__/
```

## Future Enhancements

1. **Backend Integration**: Connect to Dr. Muscle OAuth endpoints
2. **Token Refresh**: Implement automatic token refresh
3. **Session Management**: Integrate with auth store
4. **Analytics**: Track OAuth conversion rates
5. **A/B Testing**: Test different OAuth flows
6. **Additional Providers**: Facebook, Microsoft, etc.
