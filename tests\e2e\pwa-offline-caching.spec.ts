import { test, expect, Page, BrowserContext } from '@playwright/test'

test.describe('PWA Offline and Caching Tests', () => {
  let page: Page
  let context: BrowserContext
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p, context: c }) => {
    page = p
    context = c
    await page.goto('/')
  })

  test('should cache static assets for offline use', async () => {
    await page.goto('/')

    // Wait for service worker to be active
    await page.evaluate(() => {
      return navigator.serviceWorker.ready
    })

    // Check if critical assets are cached
    const cachedAssets = await page.evaluate(async () => {
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        const assets: string[] = []

        await Promise.all(
          cacheNames.map(async (name) => {
            const cache = await caches.open(name)
            const requests = await cache.keys()
            assets.push(...requests.map((r) => r.url))
          })
        )

        return assets
      }
      return []
    })

    // Should have cached some assets
    expect(cachedAssets.length).toBeGreaterThan(0)

    // Verify important file types are cached
    const hasHTML = cachedAssets.some(
      (url) => url.endsWith('/') || url.endsWith('.html')
    )
    const hasJS = cachedAssets.some((url) => url.includes('.js'))
    const hasCSS = cachedAssets.some((url) => url.includes('.css'))
    expect(hasHTML || hasJS || hasCSS).toBe(true)
  })

  test('should serve cached content when offline', async () => {
    // First visit to cache resources
    await page.goto('/')
    await page.waitForLoadState('networkidle')

    // Login to cache authenticated pages
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Go offline
    await context.setOffline(true)

    // Try to navigate - should work offline
    await page.goto('/')

    // Page should load from cache
    await expect(page.locator('h1')).toBeVisible()

    // Check for offline indicator (if implemented)
    await page
      .locator('[data-testid="offline-indicator"]')
      .isVisible()
      .catch(() => false)

    // Go back online
    await context.setOffline(false)
  })

  test('should update cache with new app version', async () => {
    await page.goto('/')

    // Check service worker registration
    await page.evaluate(async () => {
      const registrations = await navigator.serviceWorker.getRegistrations()
      if (registrations.length > 0) {
        const sw = registrations[0].active
        // Version might be in SW scope or a custom property
        return sw?.scriptURL || 'v1'
      }
      return null
    })

    // Simulate app update by triggering update check
    const updateFound = await page.evaluate(async () => {
      const registrations = await navigator.serviceWorker.getRegistrations()
      if (registrations.length > 0) {
        try {
          const reg = await registrations[0].update()
          return !!reg.installing || !!reg.waiting
        } catch {
          return false
        }
      }
      return false
    })

    // Check for update notification
    if (updateFound) {
      const updateNotification = await page
        .locator('[data-testid="app-update-available"]')
        .isVisible()
        .catch(() => false)

      if (updateNotification) {
        // Click update button
        await page.click('[data-testid="update-app-button"]')

        // Page should reload with new version
        await page.waitForLoadState('load')
      }
    }
  })

  test('should load app shell before content', async () => {
    // Set up performance observer
    await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const fcp = entries.find((e) => e.name === 'first-contentful-paint')
          const lcp = entries.find(
            (e) => e.entryType === 'largest-contentful-paint'
          )
          resolve({
            fcp: fcp?.startTime,
            lcp: lcp?.startTime,
          })
        }).observe({ entryTypes: ['paint', 'largest-contentful-paint'] })
      })
    })

    await page.goto('/')

    // Wait a bit for metrics
    await page.waitForTimeout(2000)

    const timings = await page.evaluate(() => {
      const navigation = performance.getEntriesByType(
        'navigation'
      )[0] as PerformanceNavigationTiming
      return {
        domContentLoaded:
          navigation.domContentLoadedEventEnd - navigation.fetchStart,
        loaded: navigation.loadEventEnd - navigation.fetchStart,
      }
    })

    // App shell should load quickly
    expect(timings.domContentLoaded).toBeLessThan(1500) // 1.5 seconds
  })

  test('should show skeleton screens while loading content', async () => {
    // Slow down network to see loading states
    await page.route('**/*', (route) => {
      setTimeout(() => route.continue(), 1000)
    })

    // Navigate to program page
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    // Check for skeleton screens during load
    await page
      .locator('[data-testid="skeleton-loader"]')
      .isVisible()
      .catch(() => false)

    // Clear route handler
    await page.unroute('**/*')

    // Wait for content
    await page.waitForURL('/program')

    // Skeletons should be replaced with content
    await expect(
      page.locator('[data-testid="skeleton-loader"]')
    ).not.toBeVisible()
  })
})
