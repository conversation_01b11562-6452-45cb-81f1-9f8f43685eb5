import React from 'react'
import type { ProgramModel } from '@/types'

export interface ProgramTagsProps {
  /** Program data */
  program?: ProgramModel
  /** Exercise count for today's workout */
  exerciseCount?: number
  /** Loading state for exercises */
  isLoadingExercises?: boolean
  /** Additional CSS classes */
  className?: string
}

// Helper function to get category badge color
function getCategoryColor(category: string): string {
  const categoryColors: Record<string, string> = {
    Strength: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    Hypertrophy:
      'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    Beginner:
      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    Advanced: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    Intermediate:
      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'Full Body':
      'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
    Custom: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
    Powerbuilding:
      'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    Bodybuilding:
      'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200',
    Power: 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200',
    Endurance: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200',
  }

  return (
    categoryColors[category] ||
    'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
  )
}

export function ProgramTags({
  program,
  exerciseCount,
  isLoadingExercises = false,
  className = '',
}: ProgramTagsProps) {
  if (!program) {
    return null
  }

  return (
    <div
      data-testid="program-tags"
      className={`flex items-center justify-center gap-2 ${className}`}
    >
      {/* Category badge */}
      <span
        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(
          program.category
        )}`}
      >
        {program.category}
      </span>

      {/* Exercise count badge */}
      {(exerciseCount !== undefined || isLoadingExercises) &&
        (isLoadingExercises ? (
          <span
            data-testid="exercise-count-skeleton"
            className="inline-flex items-center px-3 py-1 h-7 w-20 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"
          />
        ) : (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
            {exerciseCount} {exerciseCount === 1 ? 'exercise' : 'exercises'}
          </span>
        ))}
    </div>
  )
}
