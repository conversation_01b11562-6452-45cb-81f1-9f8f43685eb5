import { test, expect } from '@playwright/test'

test.describe('Workout Navigation - Simple', () => {
  test('should not show "No workout available" when navigating to workout page', async ({
    page,
  }) => {
    // Navigate directly to workout page with test credentials in localStorage
    await page.goto('/')

    // Set up authentication state
    await page.evaluate(() => {
      localStorage.setItem(
        'drmuscle-auth',
        JSON.stringify({
          state: {
            user: { email: '<EMAIL>' },
            token: 'test-token',
            isAuthenticated: true,
          },
          version: 0,
        })
      )
    })

    // Navigate to workout page
    await page.goto('/workout')

    // Wait for page to load
    await page.waitForLoadState('networkidle')

    // Take screenshot for debugging
    await page.screenshot({ path: 'workout-page.png', fullPage: true })

    // Check that "No workout available" is not visible
    const noWorkoutMessage = page.locator('text="No workout available"')
    const isNoWorkoutVisible = await noWorkoutMessage
      .isVisible()
      .catch(() => false)

    // Log what we see on the page
    if (isNoWorkoutVisible) {
      // eslint-disable-next-line no-console
      console.log('ERROR: "No workout available" message is visible')
      // eslint-disable-next-line no-console
      console.log('Page title:', await page.title())
      // eslint-disable-next-line no-console
      console.log('URL:', page.url())
    }

    // Assert that "No workout available" is not visible
    expect(isNoWorkoutVisible).toBe(false)

    // Verify that some workout content is visible instead
    const workoutContent = await page
      .locator('h1')
      .first()
      .textContent()
      .catch(() => '')
    // eslint-disable-next-line no-console
    console.log('Workout page h1 content:', workoutContent)

    // Should see either workout title or loading indicator
    const hasWorkoutContent = workoutContent && workoutContent !== ''
    expect(hasWorkoutContent).toBe(true)
  })
})
