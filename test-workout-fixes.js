/**
 * Test script to verify workout loading fixes
 * Run with: node test-workout-fixes.js
 */

const https = require('https');

const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'Dr123456';
const BASE_URL = 'https://drmuscle.azurewebsites.net';

async function makeRequest(url, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = https.request(url, options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data: body, headers: res.headers });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testWorkoutFlow() {
  console.log('🔍 Testing complete workout flow...');
  
  try {
    // Step 1: Login
    console.log('📝 Step 1: Logging in...');
    const loginResponse = await makeRequest(`${BASE_URL}/token`, 'POST', null, {
      'Content-Type': 'application/x-www-form-urlencoded'
    });

    const formData = `grant_type=password&username=${encodeURIComponent(TEST_EMAIL)}&password=${encodeURIComponent(TEST_PASSWORD)}`;
    
    const loginReq = https.request(`${BASE_URL}/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', async () => {
        try {
          const loginData = JSON.parse(body);
          if (loginData.access_token) {
            console.log('✅ Login successful');
            const token = loginData.access_token;
            
            // Step 2: Get workout program (using correct endpoint)
            console.log('📋 Step 2: Getting workout program...');
            const timeZoneInfo = {
              TimeZoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
              Offset: new Date().getTimezoneOffset() / -60,
              IsDaylightSaving: false,
            };
            const programResponse = await makeRequest(`${BASE_URL}/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo`, 'POST', timeZoneInfo, {
              'Authorization': `Bearer ${token}`
            });

            if (programResponse.status === 200) {
              console.log('✅ Program info loaded');
              const workoutId = programResponse.data?.Result?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Id;
              
              if (workoutId) {
                console.log(`📋 Found workout ID: ${workoutId}`);
                
                // Step 3: Use exercises from program info (no need for separate workout details call)
                console.log('🏋️ Step 3: Using exercises from program info...');
                const exercises = programResponse.data?.Result?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Exercises || [];
                console.log(`✅ Found ${exercises.length} exercises in program info`);

                if (exercises.length > 0) {
                    const firstExercise = exercises[0];
                    console.log(`📝 First exercise: ${firstExercise.Id} - ${firstExercise.Label}`);
                    
                    // Step 4: Test recommendation API with proper endpoint
                    console.log('🎯 Step 4: Testing recommendation API...');
                    const requestBody = {
                      Username: TEST_EMAIL,
                      ExerciseId: firstExercise.Id,
                      WorkoutId: workoutId,
                      SetStyle: firstExercise.SetStyle || 'Normal',
                      IsFlexibility: firstExercise.IsFlexibility || false,
                      IsQuickMode: null,
                      LightSessionDays: null,
                      SwapedExId: null,
                      IsStrengthPhashe: false,
                      IsFreePlan: false,
                      IsFirstWorkoutOfStrengthPhase: false,
                      VersionNo: 1
                    };

                    // Determine endpoint based on exercise type
                    const setStyle = (firstExercise.SetStyle || 'Normal').toLowerCase();
                    const isRestPause = setStyle === 'restpause' || setStyle === 'rest-pause';
                    const shouldUseNormal = firstExercise.IsFlexibility || !isRestPause;

                    const endpoint = shouldUseNormal
                      ? '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'
                      : '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew';

                    console.log(`🚀 Using endpoint: ${endpoint}`);
                    console.log(`📤 Request body:`, JSON.stringify(requestBody, null, 2));

                    const recommendationResponse = await makeRequest(`${BASE_URL}${endpoint}`, 'POST', requestBody, {
                      'Authorization': `Bearer ${token}`
                    });

                    console.log(`📥 Recommendation response status: ${recommendationResponse.status}`);
                    
                    if (recommendationResponse.status === 200) {
                      const recommendation = recommendationResponse.data?.Result;
                      if (recommendation) {
                        console.log('✅ Recommendation received:');
                        console.log(`   Sets: ${recommendation.Series}`);
                        console.log(`   Reps: ${recommendation.Reps}`);
                        console.log(`   Weight: ${recommendation.Weight?.Kg} kg / ${recommendation.Weight?.Lb} lbs`);
                        console.log('🎉 ALL TESTS PASSED! Workout flow is working correctly.');
                      } else {
                        console.log('⚠️ Recommendation is null (no exercise history) - this is expected for new exercises');
                        console.log('✅ API is working correctly, just no data for this exercise');
                      }
                    } else if (recommendationResponse.status === 404) {
                      console.log('⚠️ 404 response - this should now be handled gracefully by the web app');
                      console.log('✅ The web app should not show console errors for this');
                    } else {
                      console.log(`❌ Recommendation failed: ${recommendationResponse.status}`);
                      console.log('Response:', recommendationResponse.data);
                    }
                  }
                } else {
                  console.log('❌ No exercises found in program info');
                }
              } else {
                console.log('❌ No workout ID found in program response');
              }
            } else {
              console.log(`❌ Program info failed: ${programResponse.status}`);
            }
          } else {
            console.log('❌ Login failed - no access token');
          }
        } catch (error) {
          console.log('❌ Login error:', error.message);
        }
      });
    });

    loginReq.on('error', (error) => {
      console.log('❌ Login request error:', error.message);
    });

    loginReq.write(formData);
    loginReq.end();

  } catch (error) {
    console.log('❌ Test error:', error.message);
  }
}

// Run the test
testWorkoutFlow();
