import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useProgram, useProgramProgress, useProgramStats } from '../useProgram'
import { programApi } from '@/api/program'
import type { ProgramModel, ProgramProgress, ProgramStats } from '@/types'

// Mock the program API
vi.mock('@/api/program')

// Mock auth store
let mockIsAuthenticated = true
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    isAuthenticated: mockIsAuthenticated,
  }),
}))

describe('useProgram hooks', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
          gcTime: 0,
        },
      },
    })
    vi.clearAllMocks()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  describe('useProgram', () => {
    it('should fetch and return program data', async () => {
      const mockProgram: ProgramModel = {
        id: 1,
        name: 'Test Program',
        description: 'Test Description',
        category: 'Strength',
        totalDays: 90,
        currentDay: 15,
        workoutsCompleted: 15,
        startDate: '2024-01-01',
        imageUrl: '/test.jpg',
      }

      vi.mocked(programApi.getUserProgram).mockResolvedValueOnce(mockProgram)

      const { result } = renderHook(() => useProgram(), { wrapper })

      expect(result.current.isLoading).toBe(true)
      expect(result.current.program).toBeUndefined()

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.program).toEqual(mockProgram)
      expect(result.current.error).toBeNull()
    })

    it('should handle errors gracefully', async () => {
      const error = new Error('Failed to fetch program')
      vi.mocked(programApi.getUserProgram).mockRejectedValueOnce(error)

      const { result } = renderHook(() => useProgram(), { wrapper })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.program).toBeUndefined()
      expect(result.current.error).toEqual(error)
    })

    it('should not fetch when not authenticated', () => {
      // Mock unauthenticated state
      mockIsAuthenticated = false

      const { result } = renderHook(() => useProgram(), { wrapper })

      expect(result.current.isLoading).toBe(false)
      expect(result.current.program).toBeUndefined()
      expect(vi.mocked(programApi.getUserProgram)).not.toHaveBeenCalled()

      // Reset for other tests
      mockIsAuthenticated = true
    })
  })

  describe('useProgramProgress', () => {
    it('should fetch and return progress data', async () => {
      const mockProgress: ProgramProgress = {
        percentage: 50,
        daysCompleted: 45,
        totalWorkouts: 90,
        currentWeek: 7,
        workoutsThisWeek: 3,
        remainingWorkouts: 45,
      }

      vi.mocked(programApi.getProgramProgress).mockResolvedValueOnce(
        mockProgress
      )

      const { result } = renderHook(() => useProgramProgress(1), { wrapper })

      expect(result.current.isLoading).toBe(true)
      expect(result.current.progress).toBeUndefined()

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.progress).toEqual(mockProgress)
      expect(result.current.error).toBeNull()
    })

    it('should not fetch without program ID', () => {
      const { result } = renderHook(() => useProgramProgress(undefined), {
        wrapper,
      })

      expect(result.current.isLoading).toBe(false)
      expect(result.current.progress).toBeUndefined()
      expect(vi.mocked(programApi.getProgramProgress)).not.toHaveBeenCalled()
    })
  })

  describe('useProgramStats', () => {
    it('should fetch and return stats data', async () => {
      const mockStats: ProgramStats = {
        averageWorkoutTime: 45,
        totalVolume: 50000,
        personalRecords: 5,
        consecutiveWeeks: 4,
        lastWorkoutDate: '2024-01-15',
        totalWorkoutsCompleted: 25,
      }

      vi.mocked(programApi.getProgramStats).mockResolvedValueOnce(mockStats)

      const { result } = renderHook(() => useProgramStats(1), { wrapper })

      expect(result.current.isLoading).toBe(true)
      expect(result.current.stats).toBeUndefined()

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      expect(result.current.stats).toEqual(mockStats)
      expect(result.current.error).toBeNull()
    })

    it('should cache results properly', async () => {
      const mockStats: ProgramStats = {
        averageWorkoutTime: 45,
        totalVolume: 50000,
        personalRecords: 5,
        consecutiveWeeks: 4,
        lastWorkoutDate: '2024-01-15',
        totalWorkoutsCompleted: 25,
      }

      vi.mocked(programApi.getProgramStats).mockResolvedValueOnce(mockStats)

      // First render
      const { result: result1 } = renderHook(() => useProgramStats(1), {
        wrapper,
      })

      await waitFor(() => {
        expect(result1.current.isLoading).toBe(false)
      })

      // Second render - should use cache
      const { result: result2 } = renderHook(() => useProgramStats(1), {
        wrapper,
      })

      expect(result2.current.isLoading).toBe(false)
      expect(result2.current.stats).toEqual(mockStats)
      expect(vi.mocked(programApi.getProgramStats)).toHaveBeenCalledTimes(1)
    })
  })
})
