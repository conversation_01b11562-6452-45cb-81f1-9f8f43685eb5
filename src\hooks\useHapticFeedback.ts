import { useCallback, useMemo } from 'react'

/**
 * Custom hook for haptic feedback on mobile devices
 * Provides different vibration patterns for various interactions
 */
export function useHapticFeedback() {
  const isSupported = useMemo(() => {
    return typeof navigator !== 'undefined' && 'vibrate' in navigator
  }, [])

  const vibrate = useCallback(
    (pattern: number | number[]) => {
      if (!isSupported) return

      try {
        navigator.vibrate(pattern)
      } catch (error) {
        // Silently fail if vibration is not supported or throws
      }
    },
    [isSupported]
  )

  const light = useCallback(() => {
    vibrate(10) // 10ms - light tap feedback
  }, [vibrate])

  const medium = useCallback(() => {
    vibrate(20) // 20ms - button press feedback
  }, [vibrate])

  const heavy = useCallback(() => {
    vibrate(30) // 30ms - important action feedback
  }, [vibrate])

  const success = useCallback(() => {
    vibrate([10, 50, 20]) // Success pattern
  }, [vibrate])

  const error = useCallback(() => {
    vibrate([30, 100, 30]) // Error pattern
  }, [vibrate])

  return {
    light,
    medium,
    heavy,
    success,
    error,
    isSupported,
  }
}
