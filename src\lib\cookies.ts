import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

const AUTH_COOKIE_NAME = 'drmuscle-auth-token'
const REFRESH_COOKIE_NAME = 'drmuscle-refresh-token'

// Cookie options for production security
const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  path: '/',
  maxAge: 60 * 60 * 24 * 7, // 7 days
}

// Server-side cookie utilities
export const authCookies = {
  // Set auth token cookie
  setAuthToken: async (token: string) => {
    const cookieStore = await cookies()
    cookieStore.set(AUTH_COOKIE_NAME, token, COOKIE_OPTIONS)
  },

  // Set refresh token cookie
  setRefreshToken: async (token: string) => {
    const cookieStore = await cookies()
    cookieStore.set(REFRESH_COOKIE_NAME, token, {
      ...COOKIE_OPTIONS,
      maxAge: 60 * 60 * 24 * 30, // 30 days for refresh token
    })
  },

  // Get auth token from cookies
  getAuthToken: async (): Promise<string | undefined> => {
    const cookieStore = await cookies()
    return cookieStore.get(AUTH_COOKIE_NAME)?.value
  },

  // Get refresh token from cookies
  getRefreshToken: async (): Promise<string | undefined> => {
    const cookieStore = await cookies()
    return cookieStore.get(REFRESH_COOKIE_NAME)?.value
  },

  // Clear auth cookies
  clearAuthCookies: async () => {
    const cookieStore = await cookies()
    cookieStore.delete(AUTH_COOKIE_NAME)
    cookieStore.delete(REFRESH_COOKIE_NAME)
  },
}

// Middleware cookie utilities (for use in middleware.ts)
export const middlewareCookies = {
  // Set auth token in response
  setAuthToken: (response: NextResponse, token: string) => {
    response.cookies.set(AUTH_COOKIE_NAME, token, COOKIE_OPTIONS)
    return response
  },

  // Set refresh token in response
  setRefreshToken: (response: NextResponse, token: string) => {
    response.cookies.set(REFRESH_COOKIE_NAME, token, {
      ...COOKIE_OPTIONS,
      maxAge: 60 * 60 * 24 * 30, // 30 days for refresh token
    })
    return response
  },

  // Get auth token from request
  getAuthToken: (request: NextRequest): string | undefined => {
    return request.cookies.get(AUTH_COOKIE_NAME)?.value
  },

  // Get refresh token from request
  getRefreshToken: (request: NextRequest): string | undefined => {
    return request.cookies.get(REFRESH_COOKIE_NAME)?.value
  },

  // Clear auth cookies in response
  clearAuthCookies: (response: NextResponse) => {
    response.cookies.delete(AUTH_COOKIE_NAME)
    response.cookies.delete(REFRESH_COOKIE_NAME)
    return response
  },
}
