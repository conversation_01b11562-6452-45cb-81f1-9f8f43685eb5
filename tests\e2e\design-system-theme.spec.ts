import { test, expect } from '@playwright/test'

test.describe('Design System Theme Switching', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/design-system')
    await page.waitForLoadState('networkidle')
  })

  test('should switch themes and update CSS variables', async ({ page }) => {
    // Check initial theme
    const htmlElement = page.locator('html')
    await expect(htmlElement).toHaveAttribute('data-theme', 'subtle-depth')

    // Get initial background color
    const initialBgColor = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue(
        '--color-bg-primary'
      )
    })
    expect(initialBgColor).toBe('#0a0a0b') // subtle-depth bg color

    // Switch to flat-bold theme
    await page.getByRole('button', { name: 'Flat Bold' }).click()
    await expect(htmlElement).toHaveAttribute('data-theme', 'flat-bold')

    // Check that CSS variables updated
    const flatBoldBgColor = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue(
        '--color-bg-primary'
      )
    })
    expect(flatBoldBgColor).toBe('#000000') // flat-bold bg color

    // Check that the main container background actually changed
    const mainContainer = page.locator('.min-h-screen')
    await expect(mainContainer).toHaveCSS('background-color', 'rgb(0, 0, 0)')

    // Switch to glassmorphism theme
    await page.getByRole('button', { name: 'Glassmorphism' }).click()
    await expect(htmlElement).toHaveAttribute('data-theme', 'glassmorphism')

    const glassmorphismBgColor = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue(
        '--color-bg-primary'
      )
    })
    expect(glassmorphismBgColor).toBe('#0a0f1b') // glassmorphism bg color

    // Switch to ultra-minimal theme
    await page.getByRole('button', { name: 'Ultra-Minimal' }).click()
    await expect(htmlElement).toHaveAttribute('data-theme', 'ultra-minimal')

    const ultraMinimalBgColor = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue(
        '--color-bg-primary'
      )
    })
    expect(ultraMinimalBgColor).toBe('#ffffff') // ultra-minimal bg color

    // Check that the main container background is now white
    await expect(mainContainer).toHaveCSS(
      'background-color',
      'rgb(255, 255, 255)'
    )
  })

  test('should persist theme selection across page reloads', async ({
    page,
  }) => {
    // Switch to flat-bold theme
    await page.getByRole('button', { name: 'Flat Bold' }).click()
    await expect(page.locator('html')).toHaveAttribute(
      'data-theme',
      'flat-bold'
    )

    // Reload the page
    await page.reload()
    await page.waitForLoadState('networkidle')

    // Check that theme persisted
    await expect(page.locator('html')).toHaveAttribute(
      'data-theme',
      'flat-bold'
    )

    // Check that the correct button is highlighted
    const flatBoldButton = page.getByRole('button', { name: 'Flat Bold' })
    await expect(flatBoldButton).toHaveClass(/bg-brand-primary/)
  })

  test('should work correctly on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Check initial theme
    await expect(page.locator('html')).toHaveAttribute(
      'data-theme',
      'subtle-depth'
    )

    // Switch to flat-bold theme
    await page.getByRole('button', { name: 'Flat Bold' }).click()
    await expect(page.locator('html')).toHaveAttribute(
      'data-theme',
      'flat-bold'
    )

    // Check that CSS variables updated
    const flatBoldBgColor = await page.evaluate(() => {
      return getComputedStyle(document.documentElement).getPropertyValue(
        '--color-bg-primary'
      )
    })
    expect(flatBoldBgColor).toBe('#000000')

    // Check that buttons are properly styled on mobile
    const buttons = page
      .locator('button')
      .filter({ hasText: /Subtle Depth|Flat Bold|Glassmorphism|Ultra-Minimal/ })
    const buttonsCount = await buttons.count()
    expect(buttonsCount).toBe(4)

    // Verify touch targets are at least 44px
    const touchTargetChecks = Array.from(
      { length: buttonsCount },
      (_, i) => i
    ).map(async (i) => {
      const button = buttons.nth(i)
      const box = await button.boundingBox()
      expect(box?.height).toBeGreaterThanOrEqual(44)
    })
    await Promise.all(touchTargetChecks)
  })

  test('should update all theme-dependent elements', async ({ page }) => {
    // Check initial colors
    const colorPaletteSection = page
      .locator('section')
      .filter({ hasText: 'Color Palette' })
    const brandPrimaryBox = colorPaletteSection
      .locator('.bg-brand-primary')
      .first()

    // Get initial brand color
    const initialBrandColor = await brandPrimaryBox.evaluate((el) => {
      return getComputedStyle(el).backgroundColor
    })
    expect(initialBrandColor).toBe('rgb(212, 175, 55)') // subtle-depth brand color

    // Switch to flat-bold
    await page.getByRole('button', { name: 'Flat Bold' }).click()

    // Check that brand color updated
    const flatBoldBrandColor = await brandPrimaryBox.evaluate((el) => {
      return getComputedStyle(el).backgroundColor
    })
    expect(flatBoldBrandColor).toBe('rgb(0, 255, 136)') // flat-bold brand color

    // Check that text colors updated
    const heading = page
      .locator('h1')
      .filter({ hasText: 'Dr. Muscle X Design System' })
    await expect(heading).toHaveCSS('color', 'rgb(255, 255, 255)') // white text on black bg
  })
})
