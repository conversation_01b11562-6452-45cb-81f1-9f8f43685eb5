import '@testing-library/jest-dom'
import { vi, beforeEach, afterEach } from 'vitest'
import { cleanup } from '@testing-library/react'

// Mock localStorage for tests
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
global.localStorage = localStorageMock as any

// Mock window.matchMedia for responsive tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock IntersectionObserver for lazy loading tests
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
})) as any

// Mock navigator for PWA tests
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true,
})

// Mock requestAnimationFrame and cancelAnimationFrame
global.requestAnimationFrame = vi.fn((cb: FrameRequestCallback): number => {
  const id = setTimeout(() => cb(Date.now()), 16)
  return id as unknown as number
})
global.cancelAnimationFrame = vi.fn((id: number) => {
  clearTimeout(id)
})

// Cleanup after each test
afterEach(() => {
  cleanup()
})

// Reset mocks before each test
beforeEach(() => {
  localStorageMock.getItem.mockReset()
  localStorageMock.setItem.mockReset()
  localStorageMock.removeItem.mockReset()
  localStorageMock.clear.mockReset()

  // Reset navigator.onLine to true
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ;(navigator as any).onLine = true
})
