import { test, expect, Page } from '@playwright/test'

test.describe('OAuth Provider Flow Tests', () => {
  let page: Page

  test.beforeEach(async ({ page: p }) => {
    page = p

    // Mock OAuth SDKs with realistic responses
    await page.addInitScript(() => {
      const mockWindow = window as any

      // Mock Google OAuth with realistic flow
      mockWindow.google = {
        accounts: {
          id: {
            initialize: (config: any) => {
              mockWindow.__googleConfig = config
            },
            prompt: (callback?: any) => {
              // Simulate Google One Tap UI
              if (callback) {
                setTimeout(() => {
                  callback({
                    getMomentType: () => 'display',
                    getDismissedReason: () => null,
                  })
                }, 100)
              }
            },
            renderButton: (element: HTMLElement) => {
              // Create actual button
              const button = document.createElement('button')
              button.textContent = 'Sign in with Google'
              button.onclick = () => {
                // Simulate credential response
                if (mockWindow.__googleConfig?.callback) {
                  mockWindow.__googleConfig.callback({
                    credential: 'mock-google-jwt-token',
                    select_by: 'btn',
                  })
                }
              }
              element.appendChild(button)
            },
            disableAutoSelect: () => {},
          },
        },
      }

      // Mock Apple OAuth with realistic flow
      mockWindow.AppleID = {
        auth: {
          init: (config: any) => {
            mockWindow.__appleConfig = config
          },
          signIn: () => {
            return new Promise((resolve) => {
              setTimeout(() => {
                resolve({
                  authorization: {
                    code: 'mock-apple-auth-code',
                    id_token: 'mock-apple-id-token',
                    state: mockWindow.__appleConfig?.state || 'mock-state',
                  },
                  user: {
                    email: '<EMAIL>',
                    name: {
                      firstName: 'Test',
                      lastName: 'User',
                    },
                  },
                })
              }, 200)
            })
          },
        },
      }
    })

    await page.goto('/login')
  })

  test('should complete full Google OAuth flow with token exchange', async () => {
    // Mock backend OAuth token exchange
    await page.route('**/api/oauth/google/token', (route) => {
      route.fulfill({
        status: 200,
        json: {
          access_token: 'dr-muscle-access-token',
          refresh_token: 'dr-muscle-refresh-token',
          token_type: 'Bearer',
          expires_in: 3600,
          user: {
            id: 'user-123',
            email: '<EMAIL>',
            name: 'Test User',
          },
        },
      })
    })

    // Click Google sign in button
    const googleButton = page.locator('button:has-text("Sign in with Google")')
    await expect(googleButton).toBeVisible()
    await googleButton.click()

    // Should show loading state
    await expect(page.locator('text=Signing in with Google')).toBeVisible()

    // Should exchange token with backend
    await page.waitForRequest('**/api/oauth/google/token')

    // Should redirect to program page
    await expect(page).toHaveURL('/program', { timeout: 5000 })

    // Should store auth tokens
    const authState = await page.evaluate(() =>
      localStorage.getItem('auth-storage')
    )
    expect(authState).toBeTruthy()
    const parsed = JSON.parse(authState!)
    expect(parsed.state.token).toBe('dr-muscle-access-token')
  })

  test('should complete full Apple OAuth flow with authorization code', async () => {
    // Mock backend OAuth token exchange
    await page.route('**/api/oauth/apple/token', (route) => {
      const body = route.request().postData()
      expect(body).toContain('code=mock-apple-auth-code')
      expect(body).toContain('id_token=mock-apple-id-token')

      route.fulfill({
        status: 200,
        json: {
          access_token: 'dr-muscle-apple-access-token',
          refresh_token: 'dr-muscle-apple-refresh-token',
          token_type: 'Bearer',
          expires_in: 3600,
          user: {
            id: 'apple-user-123',
            email: '<EMAIL>',
            name: 'Test User',
          },
        },
      })
    })

    // Click Apple sign in button
    const appleButton = page.locator('button:has-text("Sign in with Apple")')
    await expect(appleButton).toBeVisible()
    await appleButton.click()

    // Should show loading state
    await expect(page.locator('text=Signing in with Apple')).toBeVisible()

    // Should exchange auth code with backend
    await page.waitForRequest('**/api/oauth/apple/token')

    // Should redirect to program page
    await expect(page).toHaveURL('/program', { timeout: 5000 })
  })

  test('should handle Google OAuth cancellation gracefully', async () => {
    // Override Google mock to simulate user cancellation
    await page.evaluate(() => {
      const mockWindow = window as any
      mockWindow.google.accounts.id.prompt = (callback?: any) => {
        if (callback) {
          setTimeout(() => {
            callback({
              getMomentType: () => 'dismissed',
              getDismissedReason: () => 'user_cancel',
            })
          }, 100)
        }
      }
    })

    // Trigger Google One Tap
    await page.evaluate(() => {
      const mockWindow = window as any
      mockWindow.google.accounts.id.prompt()
    })

    // Should not show error for user cancellation
    await expect(page.locator('[role="alert"]')).not.toBeVisible()

    // Should remain on login page
    await expect(page).toHaveURL('/login')
  })

  test('should handle Apple OAuth with missing user info', async () => {
    // Override Apple mock to not return user info (happens on subsequent logins)
    await page.evaluate(() => {
      const mockWindow = window as any
      mockWindow.AppleID.auth.signIn = () => {
        return Promise.resolve({
          authorization: {
            code: 'mock-apple-auth-code',
            id_token: 'mock-apple-id-token-no-user',
            state: 'mock-state',
          },
          // No user object
        })
      }
    })

    // Mock backend to handle missing user info
    await page.route('**/api/oauth/apple/token', (route) => {
      route.fulfill({
        status: 200,
        json: {
          access_token: 'dr-muscle-apple-access-token',
          refresh_token: 'dr-muscle-apple-refresh-token',
          token_type: 'Bearer',
          expires_in: 3600,
          user: {
            id: 'apple-user-123',
            email: '<EMAIL>', // Email decoded from ID token
            name: 'Decoded User',
          },
        },
      })
    })

    // Click Apple sign in
    const appleButton = page.locator('button:has-text("Sign in with Apple")')
    await appleButton.click()

    // Should still succeed
    await expect(page).toHaveURL('/program', { timeout: 5000 })
  })

  test('should handle OAuth provider SDK loading failures', async () => {
    // Create new page without OAuth mocks
    const newPage = await page.context().newPage()
    await newPage.goto('/login')

    // OAuth buttons should be disabled or hidden
    const googleButton = newPage.locator(
      'button:has-text("Sign in with Google")'
    )
    const appleButton = newPage.locator('button:has-text("Sign in with Apple")')

    // Wait a bit for any async initialization
    await newPage.waitForTimeout(1000)

    // Buttons should either be disabled or not visible
    const googleVisible = await googleButton.isVisible()
    const appleVisible = await appleButton.isVisible()

    if (googleVisible) {
      await expect(googleButton).toBeDisabled()
    }
    if (appleVisible) {
      await expect(appleButton).toBeDisabled()
    }

    await newPage.close()
  })
})
