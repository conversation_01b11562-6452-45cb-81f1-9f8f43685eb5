import { NextResponse } from 'next/server'
import { authCookies } from '@/lib/cookies'

export async function GET() {
  try {
    // Get auth token from httpOnly cookie
    const token = await authCookies.getAuthToken()

    if (token) {
      return NextResponse.json({
        token,
        authenticated: true,
      })
    }

    // No token found
    return NextResponse.json({
      token: null,
      authenticated: false,
    })
  } catch (error) {
    console.error('[Auth Token API] Error retrieving token:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve auth token' },
      { status: 500 }
    )
  }
}
