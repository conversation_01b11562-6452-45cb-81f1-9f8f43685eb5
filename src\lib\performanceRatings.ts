/**
 * Rating functions based on Web Vitals thresholds
 */
export class PerformanceRatings {
  static rateLCP(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 2500) return 'good'
    if (value <= 4000) return 'needs-improvement'
    return 'poor'
  }

  static rateFID(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 100) return 'good'
    if (value <= 300) return 'needs-improvement'
    return 'poor'
  }

  static rateCLS(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 0.1) return 'good'
    if (value <= 0.25) return 'needs-improvement'
    return 'poor'
  }

  static rateFCP(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 1800) return 'good'
    if (value <= 3000) return 'needs-improvement'
    return 'poor'
  }

  static rateTTI(value: number): 'good' | 'needs-improvement' | 'poor' {
    if (value <= 3800) return 'good'
    if (value <= 7300) return 'needs-improvement'
    return 'poor'
  }

  static rateCustomMetric(
    value: number
  ): 'good' | 'needs-improvement' | 'poor' {
    // Custom thresholds for workout app interactions
    if (value <= 100) return 'good' // Under 100ms is good
    if (value <= 300) return 'needs-improvement' // Under 300ms needs improvement
    return 'poor' // Over 300ms is poor
  }
}
