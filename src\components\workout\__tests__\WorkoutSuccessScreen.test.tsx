import { render, screen, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { WorkoutSuccessScreen } from '../WorkoutSuccessScreen'
import { useRouter } from 'next/navigation'

vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

describe('WorkoutSuccessScreen', () => {
  const mockPush = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue({ push: mockPush } as any)
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should render with theme-aware colors', () => {
    render(<WorkoutSuccessScreen exerciseCount={3} />)

    // Check that the background uses theme color
    const container = screen.getByTestId('workout-success-screen')
    expect(container).toHaveClass('bg-bg-primary')

    // The SuccessIcon component already uses theme colors
    expect(screen.getByTestId('success-icon')).toBeInTheDocument()

    // Wait for message to appear
    act(() => {
      vi.advanceTimersByTime(500)
    })

    // Check that the heading uses theme text color
    const heading = screen.getByText('Nice work!')
    expect(heading).toHaveClass('text-text-primary')

    // Check that the exercise count text uses theme secondary color
    const countText = screen.getByText('3 exercises done')
    expect(countText).toHaveClass('text-text-secondary')
  })

  it('should handle singular exercise count', () => {
    render(<WorkoutSuccessScreen exerciseCount={1} />)

    act(() => {
      vi.advanceTimersByTime(500)
    })

    const countText = screen.getByText('1 exercise done')
    expect(countText).toBeInTheDocument()
  })

  it('should navigate to program page after 2 seconds', () => {
    render(<WorkoutSuccessScreen exerciseCount={5} />)

    // Should not navigate immediately
    expect(mockPush).not.toHaveBeenCalled()

    // Advance time by 1999ms
    act(() => {
      vi.advanceTimersByTime(1999)
    })
    expect(mockPush).not.toHaveBeenCalled()

    // Advance time by 1ms more (total 2000ms)
    act(() => {
      vi.advanceTimersByTime(1)
    })
    expect(mockPush).toHaveBeenCalledWith('/program')
  })
})
