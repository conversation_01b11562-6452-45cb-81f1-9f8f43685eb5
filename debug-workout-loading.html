<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Workout Loading</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
        .info { background: #e3f2fd; color: #1565c0; }
        button { padding: 10px 20px; margin: 5px; background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #1976d2; }
    </style>
</head>
<body>
    <h1>DrMuscle Workout Loading Debug</h1>
    
    <div>
        <button onclick="testLogin()">1. Test Login</button>
        <button onclick="testWorkoutData()">2. Test Workout Data</button>
        <button onclick="testRecommendation()">3. Test Recommendation</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>
    
    <div id="logs"></div>

    <script>
        const API_BASE = 'https://drmuscle.azurewebsites.net';
        const TEST_EMAIL = '<EMAIL>';
        const TEST_PASSWORD = 'Dr123456';
        let authToken = null;

        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(div);
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
                        ...options.headers
                    },
                    ...options
                });

                const data = await response.json();
                return { status: response.status, data, ok: response.ok };
            } catch (error) {
                log(`Request failed: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testLogin() {
            log('🔍 Testing login...', 'info');
            
            try {
                const result = await makeRequest(`${API_BASE}/api/Account/Login`, {
                    method: 'POST',
                    body: JSON.stringify({
                        Email: TEST_EMAIL,
                        Password: TEST_PASSWORD
                    })
                });

                if (result.ok && result.data.access_token) {
                    authToken = result.data.access_token;
                    log(`✅ Login successful! Token: ${authToken.substring(0, 20)}...`, 'success');
                } else {
                    log(`❌ Login failed: ${JSON.stringify(result.data)}`, 'error');
                }
            } catch (error) {
                log(`❌ Login error: ${error.message}`, 'error');
            }
        }

        async function testWorkoutData() {
            if (!authToken) {
                log('❌ Please login first', 'error');
                return;
            }

            log('🔍 Testing workout data...', 'info');
            
            try {
                // Test program info
                const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                const programResult = await makeRequest(`${API_BASE}/api/Workout/GetUserWorkoutProgramTimeZoneInfo`, {
                    method: 'POST',
                    body: JSON.stringify({
                        Username: TEST_EMAIL,
                        TimeZone: timezone
                    })
                });

                if (programResult.ok) {
                    log(`✅ Program info loaded: ${JSON.stringify(programResult.data).substring(0, 200)}...`, 'success');
                    
                    // Extract workout ID
                    const workoutId = programResult.data?.Result?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Id;
                    if (workoutId) {
                        log(`📋 Found workout ID: ${workoutId}`, 'info');
                        
                        // Test workout details
                        const workoutResult = await makeRequest(`${API_BASE}/api/Workout/GetUserCustomizedCurrentWorkout`, {
                            method: 'POST',
                            body: JSON.stringify({
                                Username: TEST_EMAIL,
                                WorkoutId: workoutId
                            })
                        });

                        if (workoutResult.ok) {
                            const exercises = workoutResult.data?.Result?.[0]?.Exercises || [];
                            log(`✅ Workout details loaded: ${exercises.length} exercises`, 'success');
                            
                            if (exercises.length > 0) {
                                log(`📝 First exercise: ${exercises[0].Id} - ${exercises[0].Label}`, 'info');
                            }
                        } else {
                            log(`❌ Workout details failed: ${JSON.stringify(workoutResult.data)}`, 'error');
                        }
                    }
                } else {
                    log(`❌ Program info failed: ${JSON.stringify(programResult.data)}`, 'error');
                }
            } catch (error) {
                log(`❌ Workout data error: ${error.message}`, 'error');
            }
        }

        async function testRecommendation() {
            if (!authToken) {
                log('❌ Please login first', 'error');
                return;
            }

            log('🔍 Testing recommendation API...', 'info');
            
            try {
                // Use known exercise ID from the test
                const exerciseId = 12980; // Airborne Lunge
                const workoutId = 14019;

                const requestBody = {
                    Username: TEST_EMAIL,
                    ExerciseId: exerciseId,
                    WorkoutId: workoutId,
                    SetStyle: 'Normal',
                    IsFlexibility: false,
                    IsQuickMode: null,
                    LightSessionDays: null,
                    SwapedExId: null,
                    IsStrengthPhashe: false,
                    IsFreePlan: false,
                    IsFirstWorkoutOfStrengthPhase: false,
                    VersionNo: 1
                };

                log(`📤 Request: ${JSON.stringify(requestBody)}`, 'info');

                const result = await makeRequest(`${API_BASE}/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew`, {
                    method: 'POST',
                    body: JSON.stringify(requestBody)
                });

                if (result.ok) {
                    const recommendation = result.data?.Result;
                    if (recommendation) {
                        log(`✅ Recommendation received:`, 'success');
                        log(`   Sets: ${recommendation.Series}`, 'success');
                        log(`   Reps: ${recommendation.Reps}`, 'success');
                        log(`   Weight: ${recommendation.Weight?.Kg} kg / ${recommendation.Weight?.Lb} lbs`, 'success');
                    } else {
                        log(`⚠️ Recommendation is null (no exercise history)`, 'info');
                    }
                } else {
                    log(`❌ Recommendation failed: ${JSON.stringify(result.data)}`, 'error');
                }
            } catch (error) {
                log(`❌ Recommendation error: ${error.message}`, 'error');
            }
        }

        // Auto-run login on page load
        window.onload = () => {
            log('🚀 Debug page loaded. Click buttons to test API endpoints.', 'info');
        };
    </script>
</body>
</html>
