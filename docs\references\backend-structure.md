Directory structure:
└── dr-muscle-drmusce-backend/
├── DrMaxMuscle2.sln
├── publishingdoc.keystore
├── CATest/
│ ├── Algo.cs
│ ├── AlgoCode.txt
│ ├── App.config
│ ├── CATest.csproj
│ ├── packages.config
│ ├── Program.cs
│ └── Properties/
│ └── AssemblyInfo.cs
├── DrMaxMuscle/
│ ├── DrMaxMuscle/
│ │ ├── app.config
│ │ ├── App.xaml
│ │ ├── App.xaml.cs
│ │ ├── ChooseYourExercicePage.xaml
│ │ ├── ChooseYourExercicePage.xaml.cs
│ │ ├── CurrentLog.cs
│ │ ├── DBSetting.cs
│ │ ├── DrMaxMuscle.csproj
│ │ ├── EndExercisePage.xaml
│ │ ├── EndExercisePage.xaml.cs
│ │ ├── ExerciceNavigationPage.xaml
│ │ ├── ExerciceNavigationPage.xaml.cs
│ │ ├── ExerciseChartPage.xaml
│ │ ├── ExerciseChartPage.xaml.cs
│ │ ├── FirstTimeExerciceReps1Page.xaml
│ │ ├── FirstTimeExerciceReps1Page.xaml.cs
│ │ ├── FirstTimeExerciceReps2Page.xaml
│ │ ├── FirstTimeExerciceReps2Page.xaml.cs
│ │ ├── FirstTimeExerciceReps3Page.xaml
│ │ ├── FirstTimeExerciceReps3Page.xaml.cs
│ │ ├── FirstTimeExerciceWeight1Page.xaml
│ │ ├── FirstTimeExerciceWeight1Page.xaml.cs
│ │ ├── FirstTimeExerciceWeight2Page.xaml
│ │ ├── FirstTimeExerciceWeight2Page.xaml.cs
│ │ ├── FirstTimeExerciceWeight3Page.xaml
│ │ ├── FirstTimeExerciceWeight3Page.xaml.cs
│ │ ├── FirstTimeExerciseIntroPage.xaml
│ │ ├── FirstTimeExerciseIntroPage.xaml.cs
│ │ ├── GettingStarted.Xamarin
│ │ ├── HistoryExerciseLogView.xaml
│ │ ├── HistoryExerciseLogView.xaml.cs
│ │ ├── HistoryExercisePage.xaml
│ │ ├── HistoryExercisePage.xaml.cs
│ │ ├── HistoryPage.xaml
│ │ ├── HistoryPage.xaml.cs
│ │ ├── IDrMuscleSubcription.cs
│ │ ├── ISQLite.cs
│ │ ├── LearnMorePage.xaml
│ │ ├── LearnMorePage.xaml.cs
│ │ ├── LocalDBManager.cs
│ │ ├── LoginPage.xaml
│ │ ├── LoginPage.xaml.cs
│ │ ├── MainPage.xaml
│ │ ├── MainPage.xaml.cs
│ │ ├── NowPage.xaml
│ │ ├── NowPage.xaml.cs
│ │ ├── packages.config
│ │ ├── RightSideMasterPage.xaml
│ │ ├── RightSideMasterPage.xaml.cs
│ │ ├── SaveWorkoutPage.xaml
│ │ ├── SaveWorkoutPage.xaml.cs
│ │ ├── SubscriptionPage.cs
│ │ ├── SubscriptionPage.xaml
│ │ ├── SubscriptionPage.xaml.cs
│ │ ├── TellMeMoreEmailPage.xaml
│ │ ├── TellMeMoreEmailPage.xaml.cs
│ │ ├── TellMeMoreFirstnamePage.xaml
│ │ ├── TellMeMoreFirstnamePage.xaml.cs
│ │ ├── TellMeMoreGenderPage.xaml
│ │ ├── TellMeMoreGenderPage.xaml.cs
│ │ ├── TellMeMoreMassUnitPage.xaml
│ │ ├── TellMeMoreMassUnitPage.xaml.cs
│ │ ├── TellMeMorePasswordPage.xaml
│ │ ├── TellMeMorePasswordPage.xaml.cs
│ │ ├── Timer.cs
│ │ ├── TODO.txt
│ │ ├── WelcomePage.xaml
│ │ ├── WelcomePage.xaml.cs
│ │ ├── Models/
│ │ │ ├── InAppProduct.cs
│ │ │ ├── InAppPurchase.cs
│ │ │ └── InAppPurchaseList.cs
│ │ ├── Properties/
│ │ │ ├── AssemblyInfo.cs
│ │ │ ├── Resources.Designer.cs
│ │ │ └── Resources.resx
│ │ ├── Services/
│ │ │ └── IInAppService.cs
│ │ └── ViewModels/
│ │ ├── InAppViewModel.cs
│ │ └── ViewModelBase.cs
│ ├── DrMaxMuscle.Droid/
│ │ ├── app.config
│ │ ├── DrMaxMuscle.Droid.csproj
│ │ ├── DrMuscleSubscription_Droid.cs
│ │ ├── GenericButtonRenderer.cs
│ │ ├── MainActivity.cs
│ │ ├── MainApplication.cs
│ │ ├── packages.config
│ │ ├── PurchaseManager.cs
│ │ ├── Resource.cs
│ │ ├── SplashActivity.cs
│ │ ├── SQLite_Android.cs
│ │ ├── SubscriptionPageRenderer.cs
│ │ ├── Assets/
│ │ │ └── AboutAssets.txt
│ │ ├── Properties/
│ │ │ ├── AndroidManifest.xml
│ │ │ └── AssemblyInfo.cs
│ │ ├── Resources/
│ │ │ ├── AboutResources.txt
│ │ │ ├── Resource.Designer.cs
│ │ │ ├── drawable/
│ │ │ │ └── splash_screen.xml
│ │ │ ├── layout/
│ │ │ │ ├── Tabbar.axml
│ │ │ │ └── Toolbar.axml
│ │ │ └── values/
│ │ │ ├── colors.xml
│ │ │ └── styles.xml
│ │ └── Services/
│ │ └── InAppService.cs
│ └── DrMaxMuscle.iOS/
│ ├── app.config
│ ├── AppDelegate.cs
│ ├── DrMaxMuscle.iOS.csproj
│ ├── DrMuscleSubscription_iOS.cs
│ ├── Entitlements.plist
│ ├── Info.plist
│ ├── iTunesArtwork
│ ├── iTunesArtwork@2x
│ ├── Main.cs
│ ├── packages.config
│ ├── PurchaseManager.cs
│ ├── Receipt.cs
│ ├── ReceiptVerification.cs
│ ├── SQLite_iOS.cs
│ ├── SubscriptionPageRenderer.cs
│ ├── Assets.xcassets/
│ │ └── AppIcon.appiconset/
│ │ └── Contents.json
│ ├── Properties/
│ │ └── AssemblyInfo.cs
│ ├── Resources/
│ │ ├── LaunchScreen.storyboard
│ │ └── .smbdeleteAAA9000000000846
│ └── Services/
│ └── InAppService.cs
├── DrMaxMuscleAdminWeb/
│ ├── ApplicationInsights.config
│ ├── DrMaxMuscleAdminWeb.csproj
│ ├── Global.asax
│ ├── Global.asax.cs
│ ├── packages.config
│ ├── Project_Readme.html
│ ├── Web.config
│ ├── Web.Debug.config
│ ├── Web.Release.config
│ ├── App_Start/
│ │ ├── BundleConfig.cs
│ │ ├── FilterConfig.cs
│ │ └── RouteConfig.cs
│ ├── Content/
│ │ ├── bootstrap-theme.css
│ │ ├── bootstrap.css
│ │ ├── magnific-popup.css
│ │ ├── PagedList.css
│ │ ├── Site.css
│ │ ├── jquery.jqGrid/
│ │ │ ├── ellipsis-xbl.xml
│ │ │ └── ui.jqgrid.css
│ │ └── themes/
│ │ └── base/
│ │ ├── jquery-ui.css
│ │ ├── jquery.ui.accordion.css
│ │ ├── jquery.ui.all.css
│ │ ├── jquery.ui.autocomplete.css
│ │ ├── jquery.ui.base.css
│ │ ├── jquery.ui.button.css
│ │ ├── jquery.ui.core.css
│ │ ├── jquery.ui.datepicker.css
│ │ ├── jquery.ui.dialog.css
│ │ ├── jquery.ui.menu.css
│ │ ├── jquery.ui.progressbar.css
│ │ ├── jquery.ui.resizable.css
│ │ ├── jquery.ui.selectable.css
│ │ ├── jquery.ui.slider.css
│ │ ├── jquery.ui.spinner.css
│ │ ├── jquery.ui.tabs.css
│ │ ├── jquery.ui.theme.css
│ │ └── jquery.ui.tooltip.css
│ ├── Controllers/
│ │ ├── dmmAlgoController.cs
│ │ ├── dmmUsersController.cs
│ │ ├── dmmV1UsersController.cs
│ │ └── HomeController.cs
│ ├── fonts/
│ │ ├── glyphicons-halflings-regular.eot
│ │ ├── glyphicons-halflings-regular.ttf
│ │ └── glyphicons-halflings-regular.woff
│ ├── Helpers/
│ │ └── CsvHelpers.cs
│ ├── Properties/
│ │ ├── AssemblyInfo.cs
│ │ ├── PublishProfiles/
│ │ │ ├── Dr Muscle Dev WebServer.pubxml
│ │ │ ├── drmuscleappadmin - Web Deploy.pubxml
│ │ │ └── drmuscleappadmin.pubxml
│ │ └── ServiceDependencies/
│ │ └── drmuscleappadmin - Web Deploy/
│ │ └── profile.arm.json
│ ├── Scripts/
│ │ ├── \_references.js
│ │ ├── ai.0.22.19-build00125.js
│ │ ├── bootstrap.js
│ │ ├── jquery-1.10.2.intellisense.js
│ │ ├── jquery-1.10.2.js
│ │ ├── jquery-ui-1.10.0.js
│ │ ├── jquery.jqGrid.js
│ │ ├── jquery.magnific-popup.js
│ │ ├── jquery.validate-vsdoc.js
│ │ ├── jquery.validate.js
│ │ ├── jquery.validate.unobtrusive.js
│ │ ├── modernizr-2.6.2.js
│ │ ├── respond.js
│ │ └── i18n/
│ │ ├── grid.locale-ar.js
│ │ ├── grid.locale-bg.js
│ │ ├── grid.locale-bg1251.js
│ │ ├── grid.locale-cat.js
│ │ ├── grid.locale-cn.js
│ │ ├── grid.locale-cs.js
│ │ ├── grid.locale-da.js
│ │ ├── grid.locale-de.js
│ │ ├── grid.locale-dk.js
│ │ ├── grid.locale-el.js
│ │ ├── grid.locale-en.js
│ │ ├── grid.locale-es.js
│ │ ├── grid.locale-fa.js
│ │ ├── grid.locale-fi.js
│ │ ├── grid.locale-fr.js
│ │ ├── grid.locale-gl.js
│ │ ├── grid.locale-he.js
│ │ ├── grid.locale-hr.js
│ │ ├── grid.locale-hr1250.js
│ │ ├── grid.locale-hu.js
│ │ ├── grid.locale-id.js
│ │ ├── grid.locale-is.js
│ │ ├── grid.locale-it.js
│ │ ├── grid.locale-ja.js
│ │ ├── grid.locale-kr.js
│ │ ├── grid.locale-lt.js
│ │ ├── grid.locale-mne.js
│ │ ├── grid.locale-nl.js
│ │ ├── grid.locale-no.js
│ │ ├── grid.locale-pl.js
│ │ ├── grid.locale-pt-br.js
│ │ ├── grid.locale-pt.js
│ │ ├── grid.locale-ro.js
│ │ ├── grid.locale-ru.js
│ │ ├── grid.locale-sk.js
│ │ ├── grid.locale-sr-latin.js
│ │ ├── grid.locale-sr.js
│ │ ├── grid.locale-sv.js
│ │ ├── grid.locale-th.js
│ │ ├── grid.locale-tr.js
│ │ ├── grid.locale-tw.js
│ │ ├── grid.locale-ua.js
│ │ └── grid.locale-vi.js
│ └── Views/
│ ├── \_ViewStart.cshtml
│ ├── Web.config
│ ├── dmmAlgo/
│ │ ├── Create.cshtml
│ │ ├── Delete.cshtml
│ │ ├── Details.cshtml
│ │ ├── Edit.cshtml
│ │ └── Index.cshtml
│ ├── dmmUsers/
│ │ └── Index.cshtml
│ ├── dmmV1Users/
│ │ └── Index.cshtml
│ ├── Home/
│ │ ├── About.cshtml
│ │ ├── Contact.cshtml
│ │ └── Index.cshtml
│ └── Shared/
│ ├── \_Layout.cshtml
│ └── Error.cshtml
├── DrMaxMuscleDataLayer/
│ ├── App.Config
│ ├── AspNetRole.cs
│ ├── AspNetRoles.cs
│ ├── AspNetUser.cs
│ ├── AspNetUserClaim.cs
│ ├── AspNetUserClaims.cs
│ ├── AspNetUserLogin.cs
│ ├── AspNetUserLogins.cs
│ ├── AspNetUsers.cs
│ ├── C**RefactorLog.cs
│ ├── database_firewall_rules.cs
│ ├── database_firewall_rules1.cs
│ ├── dmmAlgo.cs
│ ├── dmmAutomatedEmail.cs
│ ├── dmmBodyPart.cs
│ ├── dmmChat.cs
│ ├── dmmChatRoom.cs
│ ├── dmmDevice.cs
│ ├── dmmDumbbellType.cs
│ ├── dmmEquipment.cs
│ ├── dmmEquipmentSetting.cs
│ ├── dmmExercice.cs
│ ├── dmmExerciseFeaturedSetting.cs
│ ├── dmmExerciseSetting.cs
│ ├── dmmGroupChat.cs
│ ├── dmmLastLightSession.cs
│ ├── dmmMaxChallenge.cs
│ ├── dmmMeal.cs
│ ├── dmmMealPlan.cs
│ ├── dmmMealPlan2.cs
│ ├── dmmNewRecordEmail.cs
│ ├── dmmRecommendation.cs
│ ├── dmmRecommendations.cs
│ ├── dmmReminderEmail.cs
│ ├── dmmSatisfactionSurvey.cs
│ ├── dmmSatisfactionSurveyFollowup.cs
│ ├── dmmSubscription.cs
│ ├── dmmUnlockCode.cs
│ ├── dmmUnlockedProgram.cs
│ ├── dmmUnsubscriber.cs
│ ├── dmmUser.cs
│ ├── dmmUserNextWorkoutDetail.cs
│ ├── dmmUserStat.cs
│ ├── dmmUserWorkoutTemplateExercice.cs
│ ├── dmmV1Users.cs
│ ├── dmmV1Users_old.cs
│ ├── dmmWeight.cs
│ ├── dmmWorkoutGroupLevelHandler.cs
│ ├── dmmWorkoutLog.cs
│ ├── dmmWorkoutLogSerie.cs
│ ├── dmmWorkoutTemplate.cs
│ ├── dmmWorkoutTemplateExercice.cs
│ ├── dmmWorkoutTemplateGroup.cs
│ ├── dmmWorkoutTemplateGroupWorkoutTemplate.cs
│ ├── dmmWorkoutTemplateLog.cs
│ ├── dmmWorkoutTemplateSetting.cs
│ ├── DrMaxMuscleDataLayer.csproj
│ ├── DrMaxMuscleModel.Context.cs
│ ├── DrMaxMuscleModel.Context.tt
│ ├── DrMaxMuscleModel.cs
│ ├── DrMaxMuscleModel.Designer.cs
│ ├── DrMaxMuscleModel.edmx
│ ├── DrMaxMuscleModel.edmx.diagram
│ ├── DrMaxMuscleModel.edmx.sql
│ ├── DrMaxMuscleModel.tt
│ ├── DrMaxMuscleModel1.cs
│ ├── GetConsecutiveWeeks_Result.cs
│ ├── GetProgressionAverage_Result.cs
│ ├── GetProgressionAverageExercises_Result.cs
│ ├── GetProgressionAverageForExercise_Result.cs
│ ├── GetProgressionAverageForExerciseForPeriod_Result.cs
│ ├── packages.config
│ ├── tcCharge.cs
│ ├── tcCustomer.cs
│ ├── tcEvent.cs
│ ├── tcFutureCharge.cs
│ ├── tcOrder.cs
│ ├── tcPurchas.cs
│ └── Properties/
│ └── AssemblyInfo.cs
├── DrMaxMuscleDb/
│ ├── DrMaxMuscleDb.refactorlog
│ ├── DrMaxMuscleDb.sqlproj
│ ├── Script.InitPostDeployment.sql
│ └── dbo/
│ ├── Functions/
│ │ ├── GetProgressionAverage.sql
│ │ ├── GetProgressionAverageExercises.sql
│ │ ├── GetProgressionAverageForExercise.sql
│ │ └── GetProgressionAverageForExerciseForPeriod.sql
│ └── Tables/
│ ├── **MigrationHistory.sql
│ ├── AspNetRoles.sql
│ ├── AspNetUserClaims.sql
│ ├── AspNetUserLogins.sql
│ ├── AspNetUserRoles.sql
│ ├── AspNetUsers.sql
│ ├── dmmAlgo.sql
│ ├── dmmAlgo_1.sql
│ ├── dmmBodyPart.sql
│ ├── dmmExercice.sql
│ ├── dmmRecommendations.sql
│ ├── dmmUser.sql
│ ├── dmmV1Users.sql
│ ├── dmmWorkoutGroupLevelHandler.sql
│ ├── dmmWorkoutGroupLevelHandler_1.sql
│ ├── dmmWorkoutLog.sql
│ ├── dmmWorkoutLogSerie.sql
│ ├── dmmWorkoutTemplate.sql
│ ├── dmmWorkoutTemplateExercice.sql
│ ├── dmmWorkoutTemplateGroup.sql
│ ├── dmmWorkoutTemplateGroupWorkoutTemplate.sql
│ ├── dmmWorkoutTemplateLog.sql
│ └── dmmWorkoutTemplateLog_1.sql
├── DrMaxMuscleWebApi/
│ ├── api-6482325372984387544-936121-63d14505f0ee.p12
│ ├── DrMaxMuscleWebApi.csproj
│ ├── drmuscle-1576045734033-d324fbce0cc4.json
│ ├── Global.asax
│ ├── Global.asax.cs
│ ├── packages.config
│ ├── Startup.cs
│ ├── Web.config
│ ├── Web.Release.config
│ ├── WrappingHandler.cs
│ ├── ActionFilters/
│ │ ├── GlobalExceptionAttribute.cs
│ │ └── LoggingFilterAttribute.cs
│ ├── App_Data/
│ │ ├── AlgoCode.txt
│ │ ├── ConfirmNewPasswordMailTemplate.txt
│ │ ├── ForgotPasswordMailTemplate.txt
│ │ └── WelcomeMailTemplate.txt
│ ├── App_Start/
│ │ ├── RouteConfig.cs
│ │ └── WebApiConfig.cs
│ ├── AuthProviders/
│ │ └── SimpleAuthorizationServerProvider .cs
│ ├── Controllers/
│ │ ├── AccountController.cs
│ │ ├── ExerciseController.cs
│ │ ├── Function1.cs
│ │ ├── WorkoutController.cs
│ │ └── WorkoutLogController.cs
│ ├── Converters/
│ │ ├── JWSTransactionDecodedPayloadConverter.cs
│ │ └── TimestampToDateTimeOffsetConverter.cs
│ ├── Enums/
│ │ └── WeightUnity.cs
│ ├── Extensions/
│ │ └── DateTimeExtensions.cs
│ ├── Helpers/
│ │ ├── ClassParser.cs
│ │ ├── Crypto.cs
│ │ ├── CryptoHash.cs
│ │ ├── EmailServiceSMTP.cs
│ │ ├── EmailSMTP.cs
│ │ ├── JSONHelper.cs
│ │ ├── NLogger.cs
│ │ └── WriteLog.cs
│ ├── Models/
│ │ ├── AppleNotification.cs
│ │ ├── AuthContext.cs
│ │ ├── Data.cs
│ │ ├── Environment.cs
│ │ ├── JWSTransactionDecodedPayload.cs
│ │ ├── NotificationSubtype.cs
│ │ ├── NotificationType.cs
│ │ ├── ResponseBodyV2.cs
│ │ ├── ResponseBodyV2DecodedPayload.cs
│ │ └── SubscriptionModel.cs
│ ├── Properties/
│ │ ├── AssemblyInfo.cs
│ │ ├── PublishProfiles/
│ │ │ ├── drmuscle (production) - Web Deploy (Carl).pubxml
│ │ │ ├── drmuscle2 (staging API + DB) - Web Deploy (Carl).pubxml
│ │ │ ├── drmuscle2 (staging) - Web Deploy (Carl).pubxml
│ │ │ ├── drmuscle2 - FTP - Release.pubxml
│ │ │ ├── drmuscle2 - FTP.pubxml
│ │ │ ├── drmuscle2 - Web Deploy (Carl staging API) (debug config = staging DB).pubxml
│ │ │ ├── drmuscle2 - Web Deploy.pubxml
│ │ │ └── FTP Azure.pubxml
│ │ └── ServiceDependencies/
│ │ ├── drmuscle (production) - Web Deploy (Carl)/
│ │ │ └── profile.arm.json
│ │ ├── drmuscle2 (staging API + DB) - Web Deploy (Carl)/
│ │ │ └── profile.arm.json
│ │ ├── drmuscle2 (staging) - Web Deploy (Carl)/
│ │ │ └── profile.arm.json
│ │ └── drmuscle2 - Web Deploy (Carl staging API) (debug config = staging DB)/
│ │ └── profile.arm.json
│ ├── Repository/
│ │ ├── AuthRepository.cs
│ │ ├── DrMuscleAlgo.cs
│ │ └── DrMuscleRepository.cs
│ └── Views/
│ └── web.config
├── DrMaxMuscleWebApiSharedModel/
│ ├── AddUserExerciseModel.cs
│ ├── ApiResponse.cs
│ ├── BaseModel.cs
│ ├── BodyPartModel.cs
│ ├── BooleanModel.cs
│ ├── CompareAttribute.cs
│ ├── DateAttribute.cs
│ ├── DrMaxMuscleWebApiSharedModel.csproj
│ ├── ErrorResponse.cs
│ ├── ExerciceModel.cs
│ ├── GenderAttribute.cs
│ ├── GetOneRMforExerciseModel.cs
│ ├── GetRecommendationForExerciseModel.cs
│ ├── GetUserExerciseResponseModel.cs
│ ├── HistoryExerciseModel.cs
│ ├── HistoryModel.cs
│ ├── IsEmailAlreadyExistModel.cs
│ ├── LoginErrorResponseModel.cs
│ ├── LoginModel.cs
│ ├── LoginSuccessResult.cs
│ ├── MassUnitAttribute.cs
│ ├── MultiUnityWeight.cs
│ ├── NewExerciceLogModel.cs
│ ├── NewExerciceModel.cs
│ ├── OneRMModel.cs
│ ├── packages.config
│ ├── RecommendationModel.cs
│ ├── RegisterModel.cs
│ ├── SetLog.cs
│ ├── SetModel.cs
│ ├── UserInfosModel.cs
│ ├── WorkoutLogSerieModel.cs
│ └── Properties/
│ └── AssemblyInfo.cs
├── DrMuscle/
│ ├── DrMuscle/
│ │ ├── app.config
│ │ ├── App.xaml
│ │ ├── App.xaml.cs
│ │ ├── Config.cs
│ │ ├── CurrentLog.cs
│ │ ├── DBSetting.cs
│ │ ├── DrMuscle.csproj
│ │ ├── DrMuscleRestClient.cs
│ │ ├── Exercises.json
│ │ ├── HistoryPage2.xaml
│ │ ├── HistoryPage2.xaml.cs
│ │ ├── IActiveAware.cs
│ │ ├── LocalDBManager.cs
│ │ ├── LocalReco.json
│ │ ├── MainPage.xaml
│ │ ├── MainPage.xaml.cs
│ │ ├── MainTabbedPage.xaml
│ │ ├── MainTabbedPage.xaml.cs
│ │ ├── NewIntialPage.xaml
│ │ ├── NewIntialPage.xaml.cs
│ │ ├── ObservableListCollection.cs
│ │ ├── PlateModel.cs
│ │ ├── ProgramList.json
│ │ ├── Timer.cs
│ │ ├── WorkoutLogSerieModelEx.cs
│ │ ├── Behaviors/
│ │ │ ├── ActivePageTabbedPageBehavior.cs
│ │ │ ├── BehaviorBase.cs
│ │ │ ├── EventToCommandBehavior.cs
│ │ │ ├── LazyContentPageBehavior.cs
│ │ │ ├── LazyContentViewBehavior.cs
│ │ │ ├── LoadContentOnActivateBehavior.cs
│ │ │ └── ShowPasswordTriggerAction.cs
│ │ ├── Cells/
│ │ │ ├── AddExerciseCell.xaml
│ │ │ ├── AddExerciseCell.xaml.cs
│ │ │ ├── AIAnalysisCell.xaml
│ │ │ ├── AIAnalysisCell.xaml.cs
│ │ │ ├── AnchorLinkCell.xaml
│ │ │ ├── AnchorLinkCell.xaml.cs
│ │ │ ├── AnswerCell.xaml
│ │ │ ├── AnswerCell.xaml.cs
│ │ │ ├── AttributedLabel.xaml
│ │ │ ├── AttributedLabel.xaml.cs
│ │ │ ├── BotDataTemplateSelector.cs
│ │ │ ├── CaroselDataTemplateSelector.cs
│ │ │ ├── ChartCell.xaml
│ │ │ ├── ChartCell.xaml.cs
│ │ │ ├── ChatDataTemplateSelector.cs
│ │ │ ├── CongratulationsCardCell.xaml
│ │ │ ├── CongratulationsCardCell.xaml.cs
│ │ │ ├── CongratulationsCell.xaml
│ │ │ ├── CongratulationsCell.xaml.cs
│ │ │ ├── CustomCell.cs
│ │ │ ├── EmptyCell.xaml
│ │ │ ├── EmptyCell.xaml.cs
│ │ │ ├── ExerciseCell.xaml
│ │ │ ├── ExerciseCell.xaml.cs
│ │ │ ├── ExplainerCell.xaml
│ │ │ ├── ExplainerCell.xaml.cs
│ │ │ ├── HeaderCell.xaml
│ │ │ ├── HeaderCell.xaml.cs
│ │ │ ├── InboxCell.xaml
│ │ │ ├── InboxCell.xaml.cs
│ │ │ ├── IncommingViewCell.xaml
│ │ │ ├── IncommingViewCell.xaml.cs
│ │ │ ├── LastWorkoutWasCardCell.xaml
│ │ │ ├── LastWorkoutWasCardCell.xaml.cs
│ │ │ ├── LearnDayCell.xaml
│ │ │ ├── LearnDayCell.xaml.cs
│ │ │ ├── LevelUpCell.xaml
│ │ │ ├── LevelUpCell.xaml.cs
│ │ │ ├── LiftedCell.xaml
│ │ │ ├── LiftedCell.xaml.cs
│ │ │ ├── LinkCell.xaml
│ │ │ ├── LinkCell.xaml.cs
│ │ │ ├── LinkGestureCell.xaml
│ │ │ ├── LinkGestureCell.xaml.cs
│ │ │ ├── LoaderCell.xaml
│ │ │ ├── LoaderCell.xaml.cs
│ │ │ ├── ModeratorView.xaml
│ │ │ ├── ModeratorView.xaml.cs
│ │ │ ├── NewDemo1Cell.xaml
│ │ │ ├── NewDemo1Cell.xaml.cs
│ │ │ ├── NewDemo2Cell.xaml
│ │ │ ├── NewDemo2Cell.xaml.cs
│ │ │ ├── NewRecordCardCell.xaml
│ │ │ ├── NewRecordCardCell.xaml.cs
│ │ │ ├── NewRecordCell.xaml
│ │ │ ├── NewRecordCell.xaml.cs
│ │ │ ├── NextWorkoutLoadingCardCell.xaml
│ │ │ ├── NextWorkoutLoadingCardCell.xaml.cs
│ │ │ ├── NextWorkoutLoadingCell.xaml
│ │ │ ├── NextWorkoutLoadingCell.xaml.cs
│ │ │ ├── OutgoingViewCell.xaml
│ │ │ ├── OutgoingViewCell.xaml.cs
│ │ │ ├── PhotoCell.xaml
│ │ │ ├── PhotoCell.xaml.cs
│ │ │ ├── QuestionCell.xaml
│ │ │ ├── QuestionCell.xaml.cs
│ │ │ ├── RestRecoveredCell.xaml
│ │ │ ├── RestRecoveredCell.xaml.cs
│ │ │ ├── ReviewCell.xaml
│ │ │ ├── ReviewCell.xaml.cs
│ │ │ ├── ReviewFullCell.xaml
│ │ │ ├── ReviewFullCell.xaml.cs
│ │ │ ├── ReviewTestimonialCell.xaml
│ │ │ ├── ReviewTestimonialCell.xaml.cs
│ │ │ ├── SetBindingCloseItem.xaml
│ │ │ ├── SetBindingCloseItem.xaml.cs
│ │ │ ├── SetBindingItem.xaml
│ │ │ ├── SetBindingItem.xaml.cs
│ │ │ ├── SetBindingNextItem.xaml
│ │ │ ├── SetBindingNextItem.xaml.cs
│ │ │ ├── SetCloseCell.xaml
│ │ │ ├── SetCloseCell.xaml.cs
│ │ │ ├── SetDataTemplateSelector.cs
│ │ │ ├── SetDisplayCell.xaml
│ │ │ ├── SetDisplayCell.xaml.cs
│ │ │ ├── SetsCell.xaml
│ │ │ ├── SetsCell.xaml.cs
│ │ │ ├── SetsDemoCell.xaml
│ │ │ ├── SetsDemoCell.xaml.cs
│ │ │ ├── SetsNextCell.xaml
│ │ │ ├── SetsNextCell.xaml.cs
│ │ │ ├── StatsCell.xaml
│ │ │ ├── StatsCell.xaml.cs
│ │ │ ├── SummaryLevelup.xaml
│ │ │ ├── SummaryLevelup.xaml.cs
│ │ │ ├── SummaryRest.xaml
│ │ │ ├── SummaryRest.xaml.cs
│ │ │ ├── SurveyTemplate.xaml
│ │ │ ├── SurveyTemplate.xaml.cs
│ │ │ ├── TipCell.xaml
│ │ │ ├── TipCell.xaml.cs
│ │ │ ├── UserOutgoingCell.xaml
│ │ │ ├── UserOutgoingCell.xaml.cs
│ │ │ ├── WeightCell.xaml
│ │ │ ├── WeightCell.xaml.cs
│ │ │ ├── WelcomeCell.xaml
│ │ │ ├── WelcomeCell.xaml.cs
│ │ │ ├── WorkoutCell.xaml
│ │ │ └── WorkoutCell.xaml.cs
│ │ ├── Constants/
│ │ │ ├── AppThemeConstants.cs
│ │ │ └── RecoComputation.cs
│ │ ├── Controls/
│ │ │ ├── AppleSignInButton.cs
│ │ │ ├── AutoBotListView.cs
│ │ │ ├── AutoSizeLabel.cs
│ │ │ ├── CalendarHeaderView.xaml
│ │ │ ├── CalendarHeaderView.xaml.cs
│ │ │ ├── ChatInputBarView.xaml
│ │ │ ├── ChatInputBarView.xaml.cs
│ │ │ ├── ContextMenuButton.cs
│ │ │ ├── ContextMenuPage.xaml
│ │ │ ├── ContextMenuPage.xaml.cs
│ │ │ ├── CustomBoxView.cs
│ │ │ ├── CustomFrame.cs
│ │ │ ├── CustomImageButton.xaml
│ │ │ ├── CustomImageButton.xaml.cs
│ │ │ ├── DrMuscleEntry.cs
│ │ │ ├── DrMuscleImageButton.xaml
│ │ │ ├── DrMuscleImageButton.xaml.cs
│ │ │ ├── DropDownPicker.cs
│ │ │ ├── ExtendedButton.cs
│ │ │ ├── ExtendedEditorControl.cs
│ │ │ ├── ExtendedLabel.cs
│ │ │ ├── ExtendedLightBlueLabel.cs
│ │ │ ├── ExtendedListView.cs
│ │ │ ├── FlowLayout.cs
│ │ │ ├── FullScreenEnabledWebView.cs
│ │ │ ├── HyperlinkLabel.cs
│ │ │ ├── KeyboardView.cs
│ │ │ ├── ToolTipView.cs
│ │ │ └── ZoomableScrollview.cs
│ │ ├── Converters/
│ │ │ ├── BoolInverter.cs
│ │ │ ├── DecimalDigitVisibleConverter.cs
│ │ │ ├── IdToBodyPartConverter.cs
│ │ │ ├── IdToTransparentBodyPartConverter.cs
│ │ │ ├── IntegerDigitVisibleConverter.cs
│ │ │ ├── InttoBoolConvertor.cs
│ │ │ ├── StringToBoolConvertor.cs
│ │ │ └── SurveySelectionEnumToVisibilityConverter.cs
│ │ ├── Dependencies/
│ │ │ ├── AlarmAndNotificationService.cs
│ │ │ ├── IAlarmAndNotificationService.cs
│ │ │ ├── IAppleSignInService.cs
│ │ │ ├── IAppSettingsHelper.cs
│ │ │ ├── IAudio.cs
│ │ │ ├── ICustomVisual.cs
│ │ │ ├── IDrMuscleSubcription.cs
│ │ │ ├── IFacebookManager.cs
│ │ │ ├── IFirebase.cs
│ │ │ ├── IHealthData.cs
│ │ │ ├── IKeyboardHelper.cs
│ │ │ ├── IKeyboardService.cs
│ │ │ ├── IKillAppService.cs
│ │ │ ├── ILocalize.cs
│ │ │ ├── ILoginFB.cs
│ │ │ ├── INetworkCheck.cs
│ │ │ ├── INotificationRequestService.cs
│ │ │ ├── INotificationsInterface.cs
│ │ │ ├── IOpenManager.cs
│ │ │ ├── IOrientationService.cs
│ │ │ ├── IRemoteConfigurationService.cs
│ │ │ ├── IScreenshotService.cs
│ │ │ ├── IShareService.cs
│ │ │ ├── ISQLite.cs
│ │ │ ├── IStyles.cs
│ │ │ ├── IVersionInfoService.cs
│ │ │ ├── IWindowBackgroundColor.cs
│ │ │ └── PlatformCulture.cs
│ │ ├── Effects/
│ │ │ ├── DropShadowEffect.cs
│ │ │ └── TooltipEffect.cs
│ │ ├── Entity/
│ │ │ ├── AdvanceEventModel.cs
│ │ │ ├── DayEventCollection.cs
│ │ │ ├── FacebookUser.cs
│ │ │ ├── Language.cs
│ │ │ ├── SBLocalMessage.cs
│ │ │ └── UserProfile.cs
│ │ ├── Enums/
│ │ │ ├── BodyPartEnum.cs
│ │ │ ├── GeneralPopupEnum.cs
│ │ │ ├── WatchMessageType.cs
│ │ │ └── WeightType.cs
│ │ ├── FormsVideoLibrary/
│ │ │ ├── FileVideoSource.cs
│ │ │ ├── IVideoPicker.cs
│ │ │ ├── IVideoPlayerController.cs
│ │ │ ├── PositionSlider.cs
│ │ │ ├── ResourceVideoSource.cs
│ │ │ ├── UriVideoSource.cs
│ │ │ ├── VideoInfo.cs
│ │ │ ├── VideoPlayer.cs
│ │ │ ├── VideoSource.cs
│ │ │ ├── VideoSourceConverter.cs
│ │ │ └── VideoStatus.cs
│ │ ├── Helpers/
│ │ │ ├── AppleAccount.cs
│ │ │ ├── BodyPartSection.cs
│ │ │ ├── BotModel.cs
│ │ │ ├── CollectionHelper.cs
│ │ │ ├── EAlertStyles.cs
│ │ │ ├── Emails.cs
│ │ │ ├── ExerciseViewModel.cs
│ │ │ ├── ExerciseWorkSetsModel.cs
│ │ │ ├── FacebookLoginEventArgs.cs
│ │ │ ├── FacebookSettings.cs
│ │ │ ├── FeatureConfiguration.cs
│ │ │ ├── Languages.cs
│ │ │ ├── MealModel.cs
│ │ │ ├── MealPlanModel.cs
│ │ │ ├── Messages.cs
│ │ │ ├── NegateBooleanInverter.cs
│ │ │ ├── NewRecordModel.cs
│ │ │ ├── ObservableGroupCollection.cs
│ │ │ ├── PageFactory.cs
│ │ │ ├── RecoContext.cs
│ │ │ ├── ResourceLoader.cs
│ │ │ ├── ReviewsModel.cs
│ │ │ ├── Settings.cs
│ │ │ ├── StringResource.cs
│ │ │ ├── SwapExercisecontext.cs
│ │ │ ├── TabData.cs
│ │ │ ├── TempProgramModel.cs
│ │ │ ├── TranslateExtension.cs
│ │ │ ├── UserModel.cs
│ │ │ ├── UserWorkoutContext.cs
│ │ │ ├── WeightChangedModel.cs
│ │ │ ├── WeightModel.cs
│ │ │ ├── WorkoutListContext.cs
│ │ │ └── WorkoutLogContext.cs
│ │ ├── Layout/
│ │ │ ├── DrEntry.cs
│ │ │ ├── DrMuscleButton.cs
│ │ │ ├── DrMuscleEditor.cs
│ │ │ ├── DrMuscleEntry.cs
│ │ │ ├── DrMuscleListView.cs
│ │ │ ├── DrMuscleListViewCache.cs
│ │ │ ├── DrMusclePage.cs
│ │ │ ├── EquipmentPage.xaml
│ │ │ ├── EquipmentPage.xaml.cs
│ │ │ ├── LanguagesPage.xaml
│ │ │ ├── LanguagesPage.xaml.cs
│ │ │ ├── NoAnimationNavigationPage.cs
│ │ │ ├── OnBoardingPage.cs
│ │ │ ├── RightSideMasterPage.xaml
│ │ │ ├── RightSideMasterPage.xaml.cs
│ │ │ ├── TimerOverlay.xaml
│ │ │ ├── TimerOverlay.xaml.cs
│ │ │ └── WorkoutEntry.cs
│ │ ├── Message/
│ │ │ ├── AddedMealInfoMessage.cs
│ │ │ ├── AddManualMealMessage.cs
│ │ │ ├── AddSetMessage.cs
│ │ │ ├── BodyweightMessage.cs
│ │ │ ├── BodyweightUpdateMessage.cs
│ │ │ ├── CellUpdateMessage.cs
│ │ │ ├── ClosePopupMessage.cs
│ │ │ ├── DeleteChatMessage.cs
│ │ │ ├── DeleteSetMessage.cs
│ │ │ ├── EnterForegroundMessage.cs
│ │ │ ├── ExerciseDeleteMessage.cs
│ │ │ ├── FinishExerciseMessage.cs
│ │ │ ├── FinishSetReceivedFromWatchOS.cs
│ │ │ ├── FinishWorkoutMessage.cs
│ │ │ ├── GeneralMessage.cs
│ │ │ ├── GlobalSettingsChangeMessage.cs
│ │ │ ├── HomeOptionsMessage.cs
│ │ │ ├── HowWasWorkoutMessage.cs
│ │ │ ├── LanguageChangeMessage.cs
│ │ │ ├── LevelUpInfoMessage.cs
│ │ │ ├── LoadChatMessage.cs
│ │ │ ├── LoadedAnalysisGPTMessage.cs
│ │ │ ├── LoadedPoemGPTMessage.cs
│ │ │ ├── LoadNextExercise.cs
│ │ │ ├── MoreTappedMessage.cs
│ │ │ ├── MuteUnmuteUserMessage.cs
│ │ │ ├── NavigationOnNotificationTappedMessage.cs
│ │ │ ├── OneRMChangedMessage.cs
│ │ │ ├── PlateCalulatorMessage.cs
│ │ │ ├── PlayAudioFileMessage.cs
│ │ │ ├── QuickTimeMessage.cs
│ │ │ ├── ReceivedWatchMessage.cs
│ │ │ ├── SaveSetMessage.cs
│ │ │ ├── SendWatchMessage.cs
│ │ │ ├── ShareMessage.cs
│ │ │ ├── SignupFinishMessage.cs
│ │ │ ├── StartNormalWorkout.cs
│ │ │ ├── StartTimerMessage.cs
│ │ │ ├── StopTimerMessage.cs
│ │ │ ├── SubscriptionPurchaseMessage.cs
│ │ │ ├── SubscriptionSuccessfulMessage.cs
│ │ │ ├── UnLoadChatMessage.cs
│ │ │ ├── UpdateAnimationMessage.cs
│ │ │ ├── UpdatedWorkoutMessage.cs
│ │ │ ├── UpdateMeMessage.cs
│ │ │ ├── UpdateSettingsMessage.cs
│ │ │ ├── UpdateSetTitleMessage.cs
│ │ │ ├── WeightRepsUpdatedMessage.cs
│ │ │ └── WorkoutLoadedMessage.cs
│ │ ├── OnBoarding/
│ │ │ ├── CoroView.xaml
│ │ │ ├── CoroView.xaml.cs
│ │ │ ├── Page1.xaml
│ │ │ ├── Page1.xaml.cs
│ │ │ ├── Page2.xaml
│ │ │ ├── Page2.xaml.cs
│ │ │ ├── Page3.xaml
│ │ │ ├── Page3.xaml.cs
│ │ │ ├── Page4.xaml
│ │ │ ├── Page4.xaml.cs
│ │ │ ├── WalkThroughPage.xaml
│ │ │ └── WalkThroughPage.xaml.cs
│ │ ├── Resx/
│ │ │ ├── AppResources.de-CH.resx
│ │ │ ├── AppResources.Designer.cs
│ │ │ ├── AppResources.fr.resx
│ │ │ ├── AppResources.resx
│ │ │ └── AppResources.sv.resx
│ │ ├── Screens/
│ │ │ ├── Demo/
│ │ │ │ ├── DemoChallengePage.xaml
│ │ │ │ ├── DemoChallengePage.xaml.cs
│ │ │ │ ├── DemoHomePage.xaml
│ │ │ │ ├── DemoHomePage.xaml.cs
│ │ │ │ ├── DemoPage.xaml
│ │ │ │ ├── DemoPage.xaml.cs
│ │ │ │ ├── DemoPage2.xaml
│ │ │ │ ├── DemoPage2.xaml.cs
│ │ │ │ ├── DemoPage3.xaml
│ │ │ │ ├── DemoPage3.xaml.cs
│ │ │ │ ├── EndPage.xaml
│ │ │ │ ├── EndPage.xaml.cs
│ │ │ │ └── Nux/
│ │ │ │ ├── NewDemoChallengePage.xaml
│ │ │ │ ├── NewDemoChallengePage.xaml.cs
│ │ │ │ ├── NewDemoHomePage.xaml
│ │ │ │ ├── NewDemoHomePage.xaml.cs
│ │ │ │ ├── NewDemoPage.xaml
│ │ │ │ ├── NewDemoPage.xaml.cs
│ │ │ │ ├── NewDemoPage2.xaml
│ │ │ │ ├── NewDemoPage2.xaml.cs
│ │ │ │ ├── NewDemoPage3.xaml
│ │ │ │ ├── NewDemoPage3.xaml.cs
│ │ │ │ ├── NewEndPage.xaml
│ │ │ │ └── NewEndPage.xaml.cs
│ │ │ ├── Eve/
│ │ │ │ ├── MealInfoPage.xaml
│ │ │ │ └── MealInfoPage.xaml.cs
│ │ │ ├── Exercises/
│ │ │ │ ├── AllExercisePage.xaml
│ │ │ │ ├── AllExercisePage.xaml.cs
│ │ │ │ ├── AllExercisesView.xaml
│ │ │ │ ├── AllExercisesView.xaml.cs
│ │ │ │ ├── ChooseDrMuscleOrCustomExercisePage.xaml
│ │ │ │ ├── ChooseDrMuscleOrCustomExercisePage.xaml.cs
│ │ │ │ ├── ChooseYourCustomExercisePage.xaml
│ │ │ │ ├── ChooseYourCustomExercisePage.xaml.cs
│ │ │ │ ├── ChooseYourDrMuscleExercisePage.xaml
│ │ │ │ ├── ChooseYourDrMuscleExercisePage.xaml.cs
│ │ │ │ ├── EndExercisePage.xaml
│ │ │ │ ├── EndExercisePage.xaml.cs
│ │ │ │ ├── ExerciseChartPage.xaml
│ │ │ │ ├── ExerciseChartPage.xaml.cs
│ │ │ │ ├── ExerciseCustomSettingsPage.xaml
│ │ │ │ ├── ExerciseCustomSettingsPage.xaml.cs
│ │ │ │ ├── ExerciseSettingsPage.xaml
│ │ │ │ ├── ExerciseSettingsPage.xaml.cs
│ │ │ │ ├── NewExercisePage.xaml
│ │ │ │ ├── NewExercisePage.xaml.cs
│ │ │ │ ├── SaveSetPage.xaml
│ │ │ │ └── SaveSetPage.xaml.cs
│ │ │ ├── History/
│ │ │ │ ├── HistortWeightPage.xaml
│ │ │ │ ├── HistortWeightPage.xaml.cs
│ │ │ │ ├── HistoryExerciseLogView.xaml
│ │ │ │ ├── HistoryExerciseLogView.xaml.cs
│ │ │ │ ├── HistoryExercisePage.xaml
│ │ │ │ ├── HistoryExercisePage.xaml.cs
│ │ │ │ ├── HistoryPage.xaml
│ │ │ │ └── HistoryPage.xaml.cs
│ │ │ ├── Me/
│ │ │ │ ├── MeCombinePage.xaml
│ │ │ │ ├── MeCombinePage.xaml.cs
│ │ │ │ ├── MePage.xaml
│ │ │ │ ├── MePage.xaml.cs
│ │ │ │ ├── MeView.xaml
│ │ │ │ └── MeView.xaml.cs
│ │ │ ├── Subscription/
│ │ │ │ ├── SubscriptionPage.xaml
│ │ │ │ └── SubscriptionPage.xaml.cs
│ │ │ ├── User/
│ │ │ │ ├── ChatPage.xaml
│ │ │ │ ├── ChatPage.xaml.cs
│ │ │ │ ├── ChatView.xaml
│ │ │ │ ├── ChatView.xaml.cs
│ │ │ │ ├── CustomDemo.xaml
│ │ │ │ ├── CustomDemo.xaml.cs
│ │ │ │ ├── EquipmentSettingsPage.xaml
│ │ │ │ ├── EquipmentSettingsPage.xaml.cs
│ │ │ │ ├── FAQPage.xaml
│ │ │ │ ├── FAQPage.xaml.cs
│ │ │ │ ├── GroupChatPage.xaml
│ │ │ │ ├── GroupChatPage.xaml.cs
│ │ │ │ ├── InboxPage.xaml
│ │ │ │ ├── InboxPage.xaml.cs
│ │ │ │ ├── InboxView.xaml
│ │ │ │ ├── InboxView.xaml.cs
│ │ │ │ ├── LearnPage.xaml
│ │ │ │ ├── LearnPage.xaml.cs
│ │ │ │ ├── MainAIPage.xaml
│ │ │ │ ├── MainAIPage.xaml.cs
│ │ │ │ ├── MorePage.xaml
│ │ │ │ ├── MorePage.xaml.cs
│ │ │ │ ├── RegistrationPage.xaml
│ │ │ │ ├── RegistrationPage.xaml.cs
│ │ │ │ ├── SettingsPage.xaml
│ │ │ │ ├── SettingsPage.xaml.cs
│ │ │ │ ├── SettingsView.xaml
│ │ │ │ ├── SettingsView.xaml.cs
│ │ │ │ ├── SupportPage.xaml
│ │ │ │ ├── SupportPage.xaml.cs
│ │ │ │ ├── WelcomePage.xaml
│ │ │ │ ├── WelcomePage.xaml.cs
│ │ │ │ ├── WorkoutChainPage.xaml
│ │ │ │ ├── WorkoutChainPage.xaml.cs
│ │ │ │ ├── OnBoarding/
│ │ │ │ │ ├── BoardingBotPage.xaml
│ │ │ │ │ ├── BoardingBotPage.xaml.cs
│ │ │ │ │ ├── IntroPage1.xaml
│ │ │ │ │ ├── IntroPage1.xaml.cs
│ │ │ │ │ ├── IntroPage2.xaml
│ │ │ │ │ ├── IntroPage2.xaml.cs
│ │ │ │ │ ├── IntroPage2Half.xaml
│ │ │ │ │ ├── IntroPage2Half.xaml.cs
│ │ │ │ │ ├── IntroPage3.xaml
│ │ │ │ │ ├── IntroPage3.xaml.cs
│ │ │ │ │ ├── MainOnboardingPage.xaml
│ │ │ │ │ ├── MainOnboardingPage.xaml.cs
│ │ │ │ │ ├── OBPage1.cs
│ │ │ │ │ ├── OBPage2.cs
│ │ │ │ │ ├── OBPage3.cs
│ │ │ │ │ ├── OnBoardingAI.xaml
│ │ │ │ │ ├── OnBoardingAI.xaml.cs
│ │ │ │ │ ├── OnBoardingGoalMan.xaml
│ │ │ │ │ ├── OnBoardingGoalMan.xaml.cs
│ │ │ │ │ ├── OnBoardingGoalWoman.xaml
│ │ │ │ │ ├── OnBoardingGoalWoman.xaml.cs
│ │ │ │ │ ├── OnBoardingManBodyBig.xaml
│ │ │ │ │ ├── OnBoardingManBodyBig.xaml.cs
│ │ │ │ │ ├── OnBoardingManBodyMidsize.xaml
│ │ │ │ │ ├── OnBoardingManBodyMidsize.xaml.cs
│ │ │ │ │ ├── OnBoardingManBodySkinny.xaml
│ │ │ │ │ ├── OnBoardingManBodySkinny.xaml.cs
│ │ │ │ │ ├── OnBoardingManBodySkinny2.xaml
│ │ │ │ │ ├── OnBoardingManBodySkinny2.xaml.cs
│ │ │ │ │ ├── OnBoardingManBodyType.xaml
│ │ │ │ │ ├── OnBoardingManBodyType.xaml.cs
│ │ │ │ │ ├── OnboardingPage10Firtname.xaml
│ │ │ │ │ ├── OnboardingPage10Firtname.xaml.cs
│ │ │ │ │ ├── OnboardingPage1Welcome.xaml
│ │ │ │ │ ├── OnboardingPage1Welcome.xaml.cs
│ │ │ │ │ ├── OnboardingPage3Beginner.xaml
│ │ │ │ │ ├── OnboardingPage3Beginner.xaml.cs
│ │ │ │ │ ├── OnboardingPage4Experience.xaml
│ │ │ │ │ ├── OnboardingPage4Experience.xaml.cs
│ │ │ │ │ ├── OnboardingPage5WorkoutPlace.xaml
│ │ │ │ │ ├── OnboardingPage5WorkoutPlace.xaml.cs
│ │ │ │ │ ├── OnboardingPage6Increments.xaml
│ │ │ │ │ ├── OnboardingPage6Increments.xaml.cs
│ │ │ │ │ ├── OnboardingPage7MassUnit.xaml
│ │ │ │ │ ├── OnboardingPage7MassUnit.xaml.cs
│ │ │ │ │ ├── OnboardingPage8LevelUp.xaml
│ │ │ │ │ ├── OnboardingPage8LevelUp.xaml.cs
│ │ │ │ │ ├── OnboardingPage9Program.xaml
│ │ │ │ │ ├── OnboardingPage9Program.xaml.cs
│ │ │ │ │ ├── OnBoardingWomanBodyFullF.xaml
│ │ │ │ │ ├── OnBoardingWomanBodyFullF.xaml.cs
│ │ │ │ │ ├── OnBoardingWomanBodyFullF2.xaml
│ │ │ │ │ ├── OnBoardingWomanBodyFullF2.xaml.cs
│ │ │ │ │ ├── OnBoardingWomanBodyMidsize.xaml
│ │ │ │ │ ├── OnBoardingWomanBodyMidsize.xaml.cs
│ │ │ │ │ ├── OnBoardingWomanBodyThin.xaml
│ │ │ │ │ ├── OnBoardingWomanBodyThin.xaml.cs
│ │ │ │ │ ├── OnBoardingWomanBodyType.xaml
│ │ │ │ │ ├── OnBoardingWomanBodyType.xaml.cs
│ │ │ │ │ ├── ReconfigureBoardingPage.xaml
│ │ │ │ │ └── ReconfigureBoardingPage.xaml.cs
│ │ │ │ └── TellMeMore/
│ │ │ │ ├── OneEntry.xaml
│ │ │ │ ├── OneEntry.xaml.cs
│ │ │ │ ├── TwoChoices.xaml
│ │ │ │ └── TwoChoices.xaml.cs
│ │ │ └── Workouts/
│ │ │ ├── AddExercisesToWorkoutPage.xaml
│ │ │ ├── AddExercisesToWorkoutPage.xaml.cs
│ │ │ ├── AddWorkoutToWorkoutOrderPage.xaml
│ │ │ ├── AddWorkoutToWorkoutOrderPage.xaml.cs
│ │ │ ├── ChooseDrMuscleOrCustomPage.xaml
│ │ │ ├── ChooseDrMuscleOrCustomPage.xaml.cs
│ │ │ ├── ChooseGymOrHome.xaml
│ │ │ ├── ChooseGymOrHome.xaml.cs
│ │ │ ├── ChooseWorkoutExerciseOrder.xaml
│ │ │ ├── ChooseWorkoutExerciseOrder.xaml.cs
│ │ │ ├── ChooseWorkoutOrder.xaml
│ │ │ ├── ChooseWorkoutOrder.xaml.cs
│ │ │ ├── ChooseYourBodyweightWorkoutPage.xaml
│ │ │ ├── ChooseYourBodyweightWorkoutPage.xaml.cs
│ │ │ ├── ChooseYourCustomWorkoutPage.xaml
│ │ │ ├── ChooseYourCustomWorkoutPage.xaml.cs
│ │ │ ├── ChooseYourGymWorkoutPage.xaml
│ │ │ ├── ChooseYourGymWorkoutPage.xaml.cs
│ │ │ ├── ChooseYourHomeWorkoutPage.xaml
│ │ │ ├── ChooseYourHomeWorkoutPage.xaml.cs
│ │ │ ├── ChooseYourProgramPage.xaml
│ │ │ ├── ChooseYourProgramPage.xaml.cs
│ │ │ ├── ChooseYourWorkoutExercisePage.xaml
│ │ │ ├── ChooseYourWorkoutExercisePage.xaml.cs
│ │ │ ├── ChooseYourWorkoutTemplateInGroup.xaml
│ │ │ ├── ChooseYourWorkoutTemplateInGroup.xaml.cs
│ │ │ ├── ExerciseVideoPage.xaml
│ │ │ ├── ExerciseVideoPage.xaml.cs
│ │ │ ├── FeaturedProgramPage.xaml
│ │ │ ├── FeaturedProgramPage.xaml.cs
│ │ │ ├── KenkoChooseYourWorkoutExercisePage.xaml
│ │ │ ├── KenkoChooseYourWorkoutExercisePage.xaml.cs
│ │ │ ├── KenkoChooseYourWorkoutViewModel.cs
│ │ │ ├── KenkoDemoWorkoutExercisePage.xaml
│ │ │ ├── KenkoDemoWorkoutExercisePage.xaml.cs
│ │ │ ├── KenkoSingleExercisePage.xaml
│ │ │ ├── KenkoSingleExercisePage.xaml.cs
│ │ │ ├── PinLockPage.xaml
│ │ │ ├── PinLockPage.xaml.cs
│ │ │ ├── WorkoutSettingsPage.xaml
│ │ │ └── WorkoutSettingsPage.xaml.cs
│ │ ├── Utility/
│ │ │ └── HelperClass.cs
│ │ └── Views/
│ │ ├── BodyweightExercisePopup.xaml
│ │ ├── BodyweightExercisePopup.xaml.cs
│ │ ├── BodyweightPopup.xaml
│ │ ├── BodyweightPopup.xaml.cs
│ │ ├── CongratulationsPopup.xaml
│ │ ├── CongratulationsPopup.xaml.cs
│ │ ├── CustomPopup.xaml
│ │ ├── CustomPopup.xaml.cs
│ │ ├── DemoWorkoutPage.xaml
│ │ ├── DemoWorkoutPage.xaml.cs
│ │ ├── EndExercisePopup.xaml
│ │ ├── EndExercisePopup.xaml.cs
│ │ ├── FacebookLoginButton.cs
│ │ ├── FeedbackView.xaml
│ │ ├── FeedbackView.xaml.cs
│ │ ├── FullReview.xaml
│ │ ├── FullReview.xaml.cs
│ │ ├── FullscreenMenu.xaml
│ │ ├── FullscreenMenu.xaml.cs
│ │ ├── GeneralPopup.xaml
│ │ ├── GeneralPopup.xaml.cs
│ │ ├── MealBodyweightPopup.xaml
│ │ ├── MealBodyweightPopup.xaml.cs
│ │ ├── MealGeneralPopup.xaml
│ │ ├── MealGeneralPopup.xaml.cs
│ │ ├── MealInfoPopup.xaml
│ │ ├── MealInfoPopup.xaml.cs
│ │ ├── NormalExercisePopup.xaml
│ │ ├── NormalExercisePopup.xaml.cs
│ │ ├── PickerView.cs
│ │ ├── PlateCalculatorPopup.xaml
│ │ ├── PlateCalculatorPopup.xaml.cs
│ │ ├── PreviewOverlay.xaml
│ │ ├── PreviewOverlay.xaml.cs
│ │ ├── ProgramListPopup.xaml
│ │ ├── ProgramListPopup.xaml.cs
│ │ ├── ReminderPopup.xaml
│ │ ├── ReminderPopup.xaml.cs
│ │ ├── TimePickerView.xaml
│ │ ├── TimePickerView.xaml.cs
│ │ ├── TimerPopup.xaml
│ │ ├── TimerPopup.xaml.cs
│ │ ├── UserHeightView.xaml
│ │ ├── UserHeightView.xaml.cs
│ │ ├── WeightGoalPopup.xaml
│ │ ├── WeightGoalPopup.xaml.cs
│ │ ├── WelcomeAIOverlay.xaml
│ │ ├── WelcomeAIOverlay.xaml.cs
│ │ ├── WelcomePage.xaml
│ │ ├── WorkoutGeneralPopup.xaml
│ │ ├── WorkoutGeneralPopup.xaml.cs
│ │ ├── WorkoutPreviewOverlay.xaml
│ │ └── WorkoutPreviewOverlay.xaml.cs
│ ├── DrMuscle.Droid/
│ │ ├── app.config
│ │ ├── AudioService.cs
│ │ ├── CustomPickerRenderer.cs
│ │ ├── CustomSwitchRenderer.cs
│ │ ├── CustomTabbedPageRenderer.cs
│ │ ├── DrMuscle.Droid.csproj
│ │ ├── DrMuscleEntryRenderer.cs
│ │ ├── DrMusclePageRenderer.cs
│ │ ├── DrMuscleSubscription_Droid.cs
│ │ ├── ExtendedEntry.cs
│ │ ├── FacebookLoginButtonRenderer.cs
│ │ ├── FacebookManager_Droid.cs
│ │ ├── Firebase_Droid.cs
│ │ ├── GenericButtonRenderer.cs
│ │ ├── google-services.json
│ │ ├── KillAppService.cs
│ │ ├── Localize.cs
│ │ ├── MainActivity.cs
│ │ ├── MainApplication.cs
│ │ ├── packages.config
│ │ ├── PurchaseManager.cs
│ │ ├── Resources.cs
│ │ ├── SplashActivity.cs
│ │ ├── SQLite_Android.cs
│ │ ├── Styles_Droid.cs
│ │ ├── Assets/
│ │ │ └── AboutAssets.txt
│ │ ├── BroadcastReceiver/
│ │ │ └── AlarmReceiver.cs
│ │ ├── Effects/
│ │ │ ├── DropShadowEffect.cs
│ │ │ ├── ListViewSortableEffect.cs
│ │ │ └── TooltipEffect.cs
│ │ ├── FormsVideoLibrary/
│ │ │ ├── VideoPicker.cs
│ │ │ └── VideoPlayerRenderer.cs
│ │ ├── Helpers/
│ │ │ ├── BadgeView.cs
│ │ │ ├── Extensions.cs
│ │ │ └── Settings.cs
│ │ ├── Properties/
│ │ │ ├── AndroidManifest.xml
│ │ │ └── AssemblyInfo.cs
│ │ ├── Renderer/
│ │ │ ├── AutoBotListView.cs
│ │ │ ├── AutoSizeLabelRenderer.cs
│ │ │ ├── ContextMenuButtonRenderer.cs
│ │ │ ├── CustomEditorRenderer.cs
│ │ │ ├── CustomFrameShadowRenderer.cs
│ │ │ ├── CustomPushHandlerForFirebaseNotification.cs
│ │ │ ├── DrMuscleEditorRenderer.cs
│ │ │ ├── DrMuscleEntryRenderer.cs
│ │ │ ├── DrMuscleScrollView.cs
│ │ │ ├── DropDownPickerRenderer.cs
│ │ │ ├── EnterFullScreenRequestedEventArgs.cs
│ │ │ ├── ExtendedButtonRenderer.cs
│ │ │ ├── ExtendedLabelRenderer.cs
│ │ │ ├── ExtendedLightBlueLabelRender.cs
│ │ │ ├── FullScreenEnabledWebChromeClient.cs
│ │ │ ├── FullScreenEnabledWebViewRenderer.cs
│ │ │ ├── HyperlinkLabelRenderer.cs
│ │ │ ├── KeyboardHelper.cs
│ │ │ ├── ListViewRenderer.cs
│ │ │ ├── MyShellRenderer.cs
│ │ │ ├── PickerViewRenderer.cs
│ │ │ ├── ScreenshotService.cs
│ │ │ ├── SetsCellRenderer.cs
│ │ │ ├── TimePickerRender.cs
│ │ │ ├── WindowBackgroundColorImplementation.cs
│ │ │ ├── WorkoutEntryRenderer.cs
│ │ │ └── ZoomableScrollviewRenderer.cs
│ │ ├── Resources/
│ │ │ ├── AboutResources.txt
│ │ │ ├── Resource.Designer.cs
│ │ │ ├── drawable/
│ │ │ │ ├── buttonGray.xml
│ │ │ │ ├── buttonGreen.xml
│ │ │ │ ├── buttonRed.xml
│ │ │ │ ├── GradientPopupBackground.xml
│ │ │ │ ├── Shadow.xml
│ │ │ │ └── splash_screen.xml
│ │ │ ├── layout/
│ │ │ │ ├── Launch.axml
│ │ │ │ ├── Tabbar.axml
│ │ │ │ └── Toolbar.axml
│ │ │ ├── values/
│ │ │ │ ├── colors.xml
│ │ │ │ ├── dimens.xml
│ │ │ │ ├── strings.xml
│ │ │ │ └── styles.xml
│ │ │ └── xml/
│ │ │ ├── file_paths.xml
│ │ │ ├── network_securoty_config.xml
│ │ │ ├── RemoteConfigDefaults.xml
│ │ │ └── wearable_app_desc.xml
│ │ └── Service/
│ │ ├── AlarmAndNotificationService.cs
│ │ ├── AppSettingsInterface.cs
│ │ ├── DroidShareService.cs
│ │ ├── KeyboardServiceRenderer.cs
│ │ ├── MyRemoteConfigurationService.cs
│ │ ├── NetworkCheck.cs
│ │ ├── NotificationRequestService.cs
│ │ ├── NotificationsInterface.cs
│ │ ├── OpenImplementation.cs
│ │ ├── VersionInfoService.cs
│ │ └── WearService.cs
│ └── DrMuscle.iOS/
│ ├── app.config
│ ├── AppDelegate.cs
│ ├── AudioService.cs
│ ├── DrMuscle.iOS.csproj
│ ├── DrMuscleEntryRenderer.cs
│ ├── DrMusclePageRenderer.cs
│ ├── DrMuscleSubscription_iOS.cs
│ ├── Entitlements.plist
│ ├── FacebookLoginButtonRenderer.cs
│ ├── FacebookManager_iOS.cs
│ ├── Firebase_iOS.cs
│ ├── GoogleService-Info.plist
│ ├── Info.plist
│ ├── iTunesArtwork
│ ├── iTunesArtwork@2x
│ ├── Localize.cs
│ ├── LoginFB_iOS.cs
│ ├── Main.cs
│ ├── packages.config
│ ├── PurchaseManager.cs
│ ├── RemoteConfigDefaults.plist
│ ├── SQLite_iOS.cs
│ ├── Styles_iOS.cs
│ ├── Assets.xcassets/
│ │ ├── AppIcon.appiconset/
│ │ │ └── Contents.json
│ │ ├── closeEye.imageset/
│ │ │ └── Contents.json
│ │ ├── DrIconWhite.imageset/
│ │ │ └── Contents.json
│ │ ├── facebook_icon.imageset/
│ │ │ └── Contents.json
│ │ ├── mail_icon.imageset/
│ │ │ └── Contents.json
│ │ ├── openEye.imageset/
│ │ │ └── Contents.json
│ │ ├── ProgressIcon.imageset/
│ │ │ └── Contents.json
│ │ ├── roundedicon.imageset/
│ │ │ └── Contents.json
│ │ ├── SendMesageIcon.imageset/
│ │ │ └── Contents.json
│ │ └── victoriaProfileRound.imageset/
│ │ └── Contents.json
│ ├── Controls/
│ │ └── HyperlinkUIView.cs
│ ├── Effects/
│ │ ├── DropShadowEffect.cs
│ │ └── TooltipEffect.cs
│ ├── FormsVideoLibrary/
│ │ ├── VideoPicker.cs
│ │ └── VideoPlayerRenderer.cs
│ ├── Helpers/
│ │ └── Settings.cs
│ ├── Properties/
│ │ └── AssemblyInfo.cs
│ ├── Renderer/
│ │ ├── AppleSignInButtonRenderer.cs
│ │ ├── AutoBotListViewRenderer.cs
│ │ ├── AutoSizeLabelRenderer.cs
│ │ ├── BackgroundImageTabbedPageRenderer.cs
│ │ ├── ChatEntryRenderer.cs
│ │ ├── ContextMenuButtonRenderer.cs
│ │ ├── CustomCellRenderer.cs
│ │ ├── CustomEditorRenderer.cs
│ │ ├── CustomFrameShadowRenderer.cs
│ │ ├── DrMuscleButtonRender.cs
│ │ ├── DrMuscleEditorRenderer.cs
│ │ ├── DrMuscleEntry.cs
│ │ ├── DrMuscleEntryRenderer.cs
│ │ ├── DrMuscleListViewRenderer.cs
│ │ ├── DropDownPickerRenderer.cs
│ │ ├── DynamicListView.cs
│ │ ├── ExerciseVideoPageRenderer.cs
│ │ ├── ExtendedButtonRenderer.cs
│ │ ├── ExtendedLabelRenderer.cs
│ │ ├── ExtendedLightBlueLabelRender.cs
│ │ ├── ExtendedListViewRenderer.cs
│ │ ├── HeaderCellRenderer.cs
│ │ ├── HyperlinkLabelRenderer.cs
│ │ ├── KeyboardHelpers.cs
│ │ ├── KeyboardViewRenderer.cs
│ │ ├── PickerViewRenderer.cs
│ │ ├── ScreenshotService.cs
│ │ ├── TimePickerRender.cs
│ │ ├── TimePickerRenderer.cs
│ │ ├── WorkoutEntryRenderer.cs
│ │ └── ZoomableScrollviewRenderer.cs
│ ├── Resources/
│ │ ├── AlarmTone.caf
│ │ ├── Jura-Bold.ttf
│ │ ├── JuraDemiBold.ttf
│ │ ├── LaunchScreen.storyboard
│ │ └── RobotoRegular.ttf
│ └── Service/
│ ├── AlarmAndNotificationService.cs
│ ├── AppleSignInService.cs
│ ├── AppSettingsInterface.cs
│ ├── HealthData.cs
│ ├── iOSShareService.cs
│ ├── KillAppService.cs
│ ├── MyRemoteConfigurationService.cs
│ ├── NotificationsInterface.cs
│ ├── OpenImplementation.cs
│ ├── OrientationService.cs
│ └── VersionInfoService.cs
├── DrMuscleWatch/
│ ├── DrMuscleWatch.WatchOSApp/
│ │ ├── DrMuscleWatch.WatchOSApp.csproj
│ │ ├── Entitlements.plist
│ │ ├── Info.plist
│ │ ├── Interface.storyboard
│ │ ├── mono_crash.11a6748113.0.json
│ │ ├── mono_crash.11a6748113.1.json
│ │ ├── mono_crash.11a6748113.2.json
│ │ ├── mono_crash.11a6748113.3.json
│ │ ├── mono_crash.11a6748113.4.json
│ │ └── Assets.xcassets/
│ │ └── AppIcon.appiconset/
│ │ └── Contents.json
│ └── DrMuscleWatch.WatchOSExtension/
│ ├── DrMuscleWatch.WatchOSExtension.csproj
│ ├── Entitlements.plist
│ ├── ExtensionDelegate.cs
│ ├── Info.plist
│ ├── InterfaceController.cs
│ ├── InterfaceController.designer.cs
│ ├── NotificationController.cs
│ ├── NotificationController.designer.cs
│ ├── PushNotificationPayload.json
│ └── SessionManager/
│ └── WCSessionManager.cs
├── DrMuscleWear/
│ └── DrMuscleWear/
│ ├── DrMuscleWear.csproj
│ ├── MainActivity.cs
│ ├── Assets/
│ │ └── AboutAssets.txt
│ ├── Properties/
│ │ ├── AndroidManifest.xml
│ │ └── AssemblyInfo.cs
│ └── Resources/
│ ├── AboutResources.txt
│ ├── Resource.designer.cs
│ ├── layout/
│ │ ├── activity_main.xml
│ │ ├── custom_spinner_adapter.xml
│ │ ├── rect_layout.xml
│ │ └── round_layout.xml
│ ├── values/
│ │ ├── dimens.xml
│ │ └── strings.xml
│ └── values-round/
│ └── strings.xml
├── DrMuscleWeb/
│ ├── DrMuscleWeb.csproj
│ ├── Global.asax
│ ├── Global.asax.cs
│ ├── mono_crash.dfe951116.0.json
│ ├── packages.config
│ ├── Project_Readme.html
│ ├── Web.config
│ ├── Web.Debug.config
│ ├── Web.Release.config
│ ├── App_Start/
│ │ ├── BundleConfig.cs
│ │ ├── FilterConfig.cs
│ │ └── RouteConfig.cs
│ ├── Connected Services/
│ │ └── Application Insights/
│ │ └── ConnectedService.json
│ ├── Content/
│ │ ├── bootstrap-theme.css
│ │ ├── bootstrap.css
│ │ ├── cyborg.css
│ │ └── Site.css
│ ├── Controllers/
│ │ ├── AccountController.cs
│ │ ├── ExerciseController.cs
│ │ └── HomeController.cs
│ ├── ErrorHandler/
│ │ └── AiHandleErrorAttribute.cs
│ ├── fonts/
│ │ ├── glyphicons-halflings-regular.eot
│ │ ├── glyphicons-halflings-regular.ttf
│ │ ├── glyphicons-halflings-regular.woff
│ │ └── glyphicons-halflings-regular.woff2
│ ├── Models/
│ │ ├── GetUserProgramInfoResponseModel.cs
│ │ ├── WorkoutTemplateGroupModel.cs
│ │ └── ViewModels/
│ │ └── ChooseYourExerciseViewModel.cs
│ ├── Properties/
│ │ ├── AssemblyInfo.cs
│ │ └── PublishProfiles/
│ │ ├── Dr Muscle Dev Server.pubxml
│ │ ├── drmuscleapp - Web Deploy.pubxml
│ │ └── FTP Azure.pubxml
│ ├── Repository/
│ │ └── DrMuscleRestClient.cs
│ ├── Scripts/
│ │ ├── \_references.js
│ │ ├── bootstrap.js
│ │ ├── jquery-1.10.2.intellisense.js
│ │ ├── jquery-1.10.2.js
│ │ ├── jquery.validate-vsdoc.js
│ │ ├── jquery.validate.bootstrap.popover.js
│ │ ├── jquery.validate.js
│ │ ├── jquery.validate.unobtrusive.js
│ │ ├── modernizr-2.6.2.js
│ │ └── respond.js
│ └── Views/
│ ├── \_ViewStart.cshtml
│ ├── Web.config
│ ├── Account/
│ │ ├── ResetPassword.cshtml
│ │ └── ResetPasswordDone.cshtml
│ ├── Exercise/
│ │ └── ChooseYourExercice.cshtml
│ ├── Home/
│ │ ├── About.cshtml
│ │ ├── Contact.cshtml
│ │ └── Index.cshtml
│ └── Shared/
│ ├── \_Layout.cshtml
│ └── Error.cshtml
├── DrMuscleWebApiSharedModel/
│ └── DrMuscleWebApiSharedModel/
│ ├── AddUserExerciseModel.cs
│ ├── AddUserWorkoutTemplateModel.cs
│ ├── ApiResponse.cs
│ ├── app.config
│ ├── AppleNotificationModel.cs
│ ├── AvailablePlateModel.cs
│ ├── BaseModel.cs
│ ├── BodyPartModel.cs
│ ├── BooleanModel.cs
│ ├── ChatModel.cs
│ ├── ChatRoomModel.cs
│ ├── ConsecutiveWeeksModel.cs
│ ├── DeleteWorkoutLogExerciseModel.cs
│ ├── DeviceModel.cs
│ ├── DrMuscleWebApiSharedModel.csproj
│ ├── EquipmentModel.cs
│ ├── ErrorResponse.cs
│ ├── ExerciceModel.cs
│ ├── ExerciseSettingsModel.cs
│ ├── ExerciseStates.cs
│ ├── FeaturedProgramModel.cs
│ ├── ForgotPasswordModel.cs
│ ├── ForgotPasswordResponseModel.cs
│ ├── GetExerciseRequest.cs
│ ├── GetExercisesLogResponseModel.cs
│ ├── GetOneRMforExerciseModel.cs
│ ├── GetRecommendationForExerciseModel.cs
│ ├── GetUserExerciseResponseModel.cs
│ ├── GetUserProgramInfoResponseModel.cs
│ ├── GetUserWorkoutLogAverageForExerciseRequest.cs
│ ├── GetUserWorkoutLogAverageResponse.cs
│ ├── GetUserWorkoutResponseModel.cs
│ ├── GetUserWorkoutTemplateGroupResponseModel.cs
│ ├── GooglePushNotificationModel.cs
│ ├── GroupChatModel.cs
│ ├── HistoryExerciseModel.cs
│ ├── HistoryModel.cs
│ ├── IsEmailAlreadyExistModel.cs
│ ├── LifeCycle.cs
│ ├── LightSessionModel.cs
│ ├── LoginErrorResponseModel.cs
│ ├── LoginModel.cs
│ ├── LoginSuccessResult.cs
│ ├── MealPlanModel.cs
│ ├── MultiUnityWeight.cs
│ ├── NewExerciceLogModel.cs
│ ├── NewExerciceModel.cs
│ ├── NewExerciseLogResponseModel.cs
│ ├── NewUserSubscriptionModel.cs
│ ├── OneRMAverage.cs
│ ├── OneRMModel.cs
│ ├── packages.config
│ ├── PhoneToWatchModel.cs
│ ├── ProgramExerciseModel.cs
│ ├── Receipt.cs
│ ├── RecommendationModel.cs
│ ├── RegisterModel.cs
│ ├── ResetPasswordModel.cs
│ ├── SatisfactionSurveyModel.cs
│ ├── SaveWorkoutModel.cs
│ ├── SendBirdNotificationModel.cs
│ ├── SetLog.cs
│ ├── SetModel.cs
│ ├── SingleIntegerModel.cs
│ ├── SubscriptionModel.cs
│ ├── SubscriptionSourceModel.cs
│ ├── ThriveCartSubscription.cs
│ ├── UnlockCodeResponseModel.cs
│ ├── UnsubscribeModel.cs
│ ├── UserInfoMealPlanModel.cs
│ ├── UserInfosModel.cs
│ ├── UserWeight.cs
│ ├── WorkoutLogSerieModel.cs
│ ├── WorkoutTemplateGroupModel.cs
│ ├── WorkoutTemplateModel.cs
│ ├── WorkoutTemplateSettingsModel.cs
│ └── Properties/
│ └── AssemblyInfo.cs
├── DrMuscleWebFeaturedProgram/
│ ├── ApplicationDbContext.cs
│ ├── appsettings.Development.json
│ ├── appsettings.json
│ ├── DrMuscleWebFeaturedProgram.csproj
│ ├── Program.cs
│ ├── Startup.cs
│ ├── Controllers/
│ │ ├── DefaultProgramController.cs
│ │ ├── EditDefaultProgramController.cs
│ │ ├── EditProgramController.cs
│ │ ├── EveController.cs
│ │ ├── HelpController.cs
│ │ ├── HistoryController.cs
│ │ ├── HomeController.cs
│ │ ├── MainController.cs
│ │ ├── MealPlanController.cs
│ │ ├── ResetPasswordController.cs
│ │ ├── SearchHistoryController.cs
│ │ ├── TempClass.cs
│ │ ├── UnsubscribeController.cs
│ │ ├── UnsubscriberController.cs
│ │ ├── UploadProgramController.cs
│ │ ├── UserProfileController.cs
│ │ └── UserProgramController.cs
│ ├── Data/
│ │ ├── DbInitializer.cs
│ │ └── SchoolContext.cs
│ ├── Models/
│ │ ├── AspNetUsers.cs
│ │ ├── DmmEveUser.cs
│ │ ├── DmmMeal.cs
│ │ ├── dmmMealPlan.cs
│ │ ├── dmmSubscription.cs
│ │ ├── dmmUser.cs
│ │ ├── dmmV1Users.cs
│ │ ├── dmmWorkoutLogSerie.cs
│ │ ├── EditEntity.cs
│ │ ├── EditExerciseModel.cs
│ │ ├── EditProgramModel.cs
│ │ ├── ErrorViewModel.cs
│ │ ├── LoginEntity.cs
│ │ ├── MealPlanUserModel.cs
│ │ ├── UnsubscribeModel.cs
│ │ ├── UploadEntity.cs
│ │ └── WeightModel.cs
│ ├── Properties/
│ │ ├── launchSettings.json
│ │ ├── PublishProfiles/
│ │ │ ├── DrMuscleWebFeaturedProgram - Web Deploy.pubxml
│ │ │ └── DrMuscleWebProgram - Web Deploy.pubxml
│ │ └── ServiceDependencies/
│ │ └── DrMuscleWebFeaturedProgram - Web Deploy/
│ │ └── profile.arm.json
│ ├── Repository/
│ │ ├── DrMuscleRestClient.cs
│ │ └── EveRestClient.cs
│ ├── Views/
│ │ ├── \_ViewImports.cshtml
│ │ ├── \_ViewStart.cshtml
│ │ ├── DefaultProgram/
│ │ │ ├── DefaultProgram.cshtml
│ │ │ └── Review.cshtml
│ │ ├── EditDefaultProgram/
│ │ │ ├── Edit.cshtml
│ │ │ ├── EditProgramitem.cshtml
│ │ │ └── Index.cshtml
│ │ ├── EditProgram/
│ │ │ ├── Edit.cshtml
│ │ │ ├── EditProgramitem.cshtml
│ │ │ └── Index.cshtml
│ │ ├── Eve/
│ │ │ └── Index.cshtml
│ │ ├── Help/
│ │ │ └── Index.cshtml
│ │ ├── History/
│ │ │ └── Index.cshtml
│ │ ├── Home/
│ │ │ ├── Index.cshtml
│ │ │ └── Privacy.cshtml
│ │ ├── Main/
│ │ │ └── Index.cshtml
│ │ ├── MealPlan/
│ │ │ └── Index.cshtml
│ │ ├── ResetPassword/
│ │ │ ├── Index.cshtml
│ │ │ ├── ResetPasswordDone.cshtml
│ │ │ └── ResetPasswordError.cshtml
│ │ ├── SearchHistory/
│ │ │ └── Index.cshtml
│ │ ├── Shared/
│ │ │ ├── \_CookieConsentPartial.cshtml
│ │ │ ├── \_EmptyLayout.cshtml
│ │ │ ├── \_Layout.cshtml
│ │ │ ├── \_Menupartial.cshtml
│ │ │ ├── \_ValidationScriptsPartial.cshtml
│ │ │ ├── Error.cshtml
│ │ │ ├── Review.cshtml
│ │ │ └── UploadProgram.cshtml
│ │ ├── Unsubscribe/
│ │ │ └── Index.cshtml
│ │ ├── Unsubscriber/
│ │ │ └── Index.cshtml
│ │ ├── UserProfile/
│ │ │ └── Index.cshtml
│ │ └── UserProgram/
│ │ └── index.cshtml
│ └── wwwroot/
│ ├── Content/
│ │ ├── bootstrap-theme.css
│ │ ├── bootstrap.css
│ │ ├── magnific-popup.css
│ │ ├── PagedList.css
│ │ ├── Site.css
│ │ ├── jquery.jqGrid/
│ │ │ ├── ellipsis-xbl.xml
│ │ │ └── ui.jqgrid.css
│ │ └── themes/
│ │ └── base/
│ │ ├── jquery-ui.css
│ │ ├── jquery.ui.accordion.css
│ │ ├── jquery.ui.all.css
│ │ ├── jquery.ui.autocomplete.css
│ │ ├── jquery.ui.base.css
│ │ ├── jquery.ui.button.css
│ │ ├── jquery.ui.core.css
│ │ ├── jquery.ui.datepicker.css
│ │ ├── jquery.ui.dialog.css
│ │ ├── jquery.ui.menu.css
│ │ ├── jquery.ui.progressbar.css
│ │ ├── jquery.ui.resizable.css
│ │ ├── jquery.ui.selectable.css
│ │ ├── jquery.ui.slider.css
│ │ ├── jquery.ui.spinner.css
│ │ ├── jquery.ui.tabs.css
│ │ ├── jquery.ui.theme.css
│ │ └── jquery.ui.tooltip.css
│ ├── css/
│ │ └── site.css
│ ├── Files/
│ │ ├── custom_program_template.csv
│ │ ├── Default_program_template.csv
│ │ └── Featured_program_template.csv
│ ├── js/
│ │ └── site.js
│ ├── lib/
│ │ ├── bootstrap/
│ │ │ └── LICENSE
│ │ ├── Content/
│ │ │ ├── bootstrap-theme.css
│ │ │ ├── bootstrap.css
│ │ │ ├── magnific-popup.css
│ │ │ ├── PagedList.css
│ │ │ ├── Site.css
│ │ │ ├── jquery.jqGrid/
│ │ │ │ ├── ellipsis-xbl.xml
│ │ │ │ └── ui.jqgrid.css
│ │ │ └── themes/
│ │ │ └── base/
│ │ │ ├── jquery-ui.css
│ │ │ ├── jquery.ui.accordion.css
│ │ │ ├── jquery.ui.all.css
│ │ │ ├── jquery.ui.autocomplete.css
│ │ │ ├── jquery.ui.base.css
│ │ │ ├── jquery.ui.button.css
│ │ │ ├── jquery.ui.core.css
│ │ │ ├── jquery.ui.datepicker.css
│ │ │ ├── jquery.ui.dialog.css
│ │ │ ├── jquery.ui.menu.css
│ │ │ ├── jquery.ui.progressbar.css
│ │ │ ├── jquery.ui.resizable.css
│ │ │ ├── jquery.ui.selectable.css
│ │ │ ├── jquery.ui.slider.css
│ │ │ ├── jquery.ui.spinner.css
│ │ │ ├── jquery.ui.tabs.css
│ │ │ ├── jquery.ui.theme.css
│ │ │ └── jquery.ui.tooltip.css
│ │ ├── jquery/
│ │ │ └── LICENSE.txt
│ │ ├── jquery-validation/
│ │ │ └── LICENSE.md
│ │ ├── jquery-validation-unobtrusive/
│ │ │ ├── jquery.validate.unobtrusive.js
│ │ │ └── LICENSE.txt
│ │ └── Scripts/
│ │ ├── \_references.js
│ │ ├── ai.0.22.19-build00125.js
│ │ ├── bootstrap.js
│ │ ├── jquery-1.10.2.intellisense.js
│ │ ├── jquery-1.10.2.js
│ │ ├── jquery-ui-1.10.0.js
│ │ ├── jquery.jqGrid.js
│ │ ├── jquery.magnific-popup.js
│ │ ├── jquery.validate-vsdoc.js
│ │ ├── jquery.validate.js
│ │ ├── jquery.validate.unobtrusive.js
│ │ ├── modernizr-2.6.2.js
│ │ ├── respond.js
│ │ └── i18n/
│ │ ├── grid.locale-ar.js
│ │ ├── grid.locale-bg.js
│ │ ├── grid.locale-bg1251.js
│ │ ├── grid.locale-cat.js
│ │ ├── grid.locale-cn.js
│ │ ├── grid.locale-cs.js
│ │ ├── grid.locale-da.js
│ │ ├── grid.locale-de.js
│ │ ├── grid.locale-dk.js
│ │ ├── grid.locale-el.js
│ │ ├── grid.locale-en.js
│ │ ├── grid.locale-es.js
│ │ ├── grid.locale-fa.js
│ │ ├── grid.locale-fi.js
│ │ ├── grid.locale-fr.js
│ │ ├── grid.locale-gl.js
│ │ ├── grid.locale-he.js
│ │ ├── grid.locale-hr.js
│ │ ├── grid.locale-hr1250.js
│ │ ├── grid.locale-hu.js
│ │ ├── grid.locale-id.js
│ │ ├── grid.locale-is.js
│ │ ├── grid.locale-it.js
│ │ ├── grid.locale-ja.js
│ │ ├── grid.locale-kr.js
│ │ ├── grid.locale-lt.js
│ │ ├── grid.locale-mne.js
│ │ ├── grid.locale-nl.js
│ │ ├── grid.locale-no.js
│ │ ├── grid.locale-pl.js
│ │ ├── grid.locale-pt-br.js
│ │ ├── grid.locale-pt.js
│ │ ├── grid.locale-ro.js
│ │ ├── grid.locale-ru.js
│ │ ├── grid.locale-sk.js
│ │ ├── grid.locale-sr-latin.js
│ │ ├── grid.locale-sr.js
│ │ ├── grid.locale-sv.js
│ │ ├── grid.locale-th.js
│ │ ├── grid.locale-tr.js
│ │ ├── grid.locale-tw.js
│ │ ├── grid.locale-ua.js
│ │ └── grid.locale-vi.js
│ ├── Scripts/
│ │ ├── \_references.js
│ │ ├── ai.0.22.19-build00125.js
│ │ ├── bootstrap.js
│ │ ├── jquery-1.10.2.intellisense.js
│ │ ├── jquery-1.10.2.js
│ │ ├── jquery-ui-1.10.0.js
│ │ ├── jquery.jqGrid.js
│ │ ├── jquery.magnific-popup.js
│ │ ├── jquery.validate-vsdoc.js
│ │ ├── jquery.validate.js
│ │ ├── jquery.validate.unobtrusive.js
│ │ ├── modernizr-2.6.2.js
│ │ ├── respond.js
│ │ └── i18n/
│ │ ├── grid.locale-ar.js
│ │ ├── grid.locale-bg.js
│ │ ├── grid.locale-bg1251.js
│ │ ├── grid.locale-cat.js
│ │ ├── grid.locale-cn.js
│ │ ├── grid.locale-cs.js
│ │ ├── grid.locale-da.js
│ │ ├── grid.locale-de.js
│ │ ├── grid.locale-dk.js
│ │ ├── grid.locale-el.js
│ │ ├── grid.locale-en.js
│ │ ├── grid.locale-es.js
│ │ ├── grid.locale-fa.js
│ │ ├── grid.locale-fi.js
│ │ ├── grid.locale-fr.js
│ │ ├── grid.locale-gl.js
│ │ ├── grid.locale-he.js
│ │ ├── grid.locale-hr.js
│ │ ├── grid.locale-hr1250.js
│ │ ├── grid.locale-hu.js
│ │ ├── grid.locale-id.js
│ │ ├── grid.locale-is.js
│ │ ├── grid.locale-it.js
│ │ ├── grid.locale-ja.js
│ │ ├── grid.locale-kr.js
│ │ ├── grid.locale-lt.js
│ │ ├── grid.locale-mne.js
│ │ ├── grid.locale-nl.js
│ │ ├── grid.locale-no.js
│ │ ├── grid.locale-pl.js
│ │ ├── grid.locale-pt-br.js
│ │ ├── grid.locale-pt.js
│ │ ├── grid.locale-ro.js
│ │ ├── grid.locale-ru.js
│ │ ├── grid.locale-sk.js
│ │ ├── grid.locale-sr-latin.js
│ │ ├── grid.locale-sr.js
│ │ ├── grid.locale-sv.js
│ │ ├── grid.locale-th.js
│ │ ├── grid.locale-tr.js
│ │ ├── grid.locale-tw.js
│ │ ├── grid.locale-ua.js
│ │ └── grid.locale-vi.js
│ └── Upload/
│ ├── custom_featured_program_template.csv
│ └── error.csv
├── DrMuscleWebProgram/
│ ├── appsettings.Development.json
│ ├── appsettings.json
│ ├── DrMuscleWebProgram.csproj
│ ├── Program.cs
│ ├── Startup.cs
│ ├── Controllers/
│ │ ├── HomeController.cs
│ │ └── UploadProgramController.cs
│ ├── Models/
│ │ ├── ErrorViewModel.cs
│ │ ├── LoginEntity.cs
│ │ └── UploadEntity.cs
│ ├── Properties/
│ │ ├── launchSettings.json
│ │ └── PublishProfiles/
│ │ └── DrMuscleWebProgram - Web Deploy.pubxml
│ ├── Repository/
│ │ └── DrMuscleRestClient.cs
│ ├── Views/
│ │ ├── \_ViewImports.cshtml
│ │ ├── \_ViewStart.cshtml
│ │ ├── Home/
│ │ │ ├── Index.cshtml
│ │ │ └── Privacy.cshtml
│ │ └── Shared/
│ │ ├── \_CookieConsentPartial.cshtml
│ │ ├── \_Layout.cshtml
│ │ ├── \_ValidationScriptsPartial.cshtml
│ │ ├── Error.cshtml
│ │ └── UploadProgram.cshtml
│ └── wwwroot/
│ ├── css/
│ │ └── site.css
│ ├── js/
│ │ └── site.js
│ └── lib/
│ ├── bootstrap/
│ │ └── LICENSE
│ ├── jquery/
│ │ └── LICENSE.txt
│ ├── jquery-validation/
│ │ └── LICENSE.md
│ └── jquery-validation-unobtrusive/
│ ├── jquery.validate.unobtrusive.js
│ └── LICENSE.txt
└── EmbyClientStatus/
└── EmbyClientStatus/
└── packages.config
