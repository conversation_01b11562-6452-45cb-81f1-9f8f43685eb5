import { render } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { LoginSuccessScreen } from '../LoginSuccessScreen'
import { useAuthStore } from '@/stores/authStore'

// Mock the auth store
vi.mock('@/stores/authStore')

// Mock components to avoid complex setup
vi.mock('@/components/workout', () => ({
  SuccessIcon: () => <div>Success Icon</div>,
  SuccessMessage: ({ title, message }: any) => (
    <div>
      <h1>{title}</h1>
      <p>{message}</p>
    </div>
  ),
  LoadingProgress: ({ progress, status }: any) => (
    <div>
      <div>Progress: {progress}%</div>
      <div>{status}</div>
    </div>
  ),
}))

describe('LoginSuccessScreen Showcase', () => {
  it('should render successfully in different user scenarios', () => {
    // With user name
    vi.mocked(useAuthStore).mockReturnValue({
      user: {
        email: '<EMAIL>',
        name: '<PERSON>',
        token: 'mock-token',
      },
      isAuthenticated: true,
    } as any)

    const { container: withName } = render(<LoginSuccessScreen />)
    expect(withName).toBeTruthy()

    // Without user name
    vi.mocked(useAuthStore).mockReturnValue({
      user: {
        email: '<EMAIL>',
        token: 'mock-token',
      },
      isAuthenticated: true,
    } as any)

    const { container: withoutName } = render(<LoginSuccessScreen />)
    expect(withoutName).toBeTruthy()

    // With callback
    const onComplete = vi.fn()
    const { container: withCallback } = render(
      <LoginSuccessScreen onComplete={onComplete} />
    )
    expect(withCallback).toBeTruthy()

    // With custom styling
    const { container: customStyle } = render(
      <LoginSuccessScreen className="custom-class" />
    )
    expect(customStyle).toBeTruthy()

    // No user (logged out state)
    vi.mocked(useAuthStore).mockReturnValue({
      user: null,
      isAuthenticated: false,
    } as any)

    const { container: noUser } = render(<LoginSuccessScreen />)
    expect(noUser).toBeTruthy()
  })
})
