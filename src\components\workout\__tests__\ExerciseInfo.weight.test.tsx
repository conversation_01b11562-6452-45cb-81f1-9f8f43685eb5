import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseInfo } from '../ExerciseInfo'
import type { ExerciseModel, RecommendationModel } from '@/types'

describe('ExerciseInfo - Weight Recommendation Edge Cases', () => {
  const mockExercise: ExerciseModel = {
    Id: 27474,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    Timer: 0,
    SetStyle: 0,
    IsFlexibility: false,
  }

  it('should show "No weight recommendation" when API returns null weight', () => {
    const recommendationWithNullWeight: RecommendationModel = {
      Series: 3,
      Reps: 10,
      Weight: null as any, // API might return null
      OneRMProgress: 0,
      RecommendationInKg: 0,
      OneRMPercentage: 0,
      WarmUpReps1: 0,
      WarmUpReps2: 0,
      WarmUpWeightSet1: { Lb: 0, Kg: 0 },
      WarmUpWeightSet2: { Lb: 0, Kg: 0 },
      WarmUpsList: [],
      WarmupsCount: 0,
      RpRest: 180,
      NbPauses: 0,
      NbRepsPauses: 0,
      ReferenceSetHistory: null,
    }

    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={recommendationWithNullWeight}
        performancePercentage={null}
      />
    )

    expect(screen.getByText(/No weight recommendation/)).toBeInTheDocument()
  })

  it('should show "No weight recommendation" when weight.Lb is 0', () => {
    const recommendationWithZeroWeight: RecommendationModel = {
      Series: 3,
      Reps: 10,
      Weight: { Lb: 0, Kg: 0 },
      OneRMProgress: 0,
      RecommendationInKg: 0,
      OneRMPercentage: 0,
      WarmUpReps1: 0,
      WarmUpReps2: 0,
      WarmUpWeightSet1: { Lb: 0, Kg: 0 },
      WarmUpWeightSet2: { Lb: 0, Kg: 0 },
      WarmUpsList: [],
      WarmupsCount: 0,
      RpRest: 180,
      NbPauses: 0,
      NbRepsPauses: 0,
      ReferenceSetHistory: null,
    }

    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={recommendationWithZeroWeight}
        performancePercentage={null}
      />
    )

    expect(screen.getByText(/No weight recommendation/)).toBeInTheDocument()
  })

  it('should log recommendation data in development mode', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'
    const consoleSpy = vi.spyOn(console, 'log')

    const recommendation: RecommendationModel = {
      Series: 3,
      Reps: 10,
      Weight: { Lb: 135, Kg: 61.23 },
      OneRMProgress: 0,
      RecommendationInKg: 61.23,
      OneRMPercentage: 0,
      WarmUpReps1: 0,
      WarmUpReps2: 0,
      WarmUpWeightSet1: { Lb: 0, Kg: 0 },
      WarmUpWeightSet2: { Lb: 0, Kg: 0 },
      WarmUpsList: [],
      WarmupsCount: 0,
      RpRest: 180,
      NbPauses: 0,
      NbRepsPauses: 0,
      ReferenceSetHistory: null,
    }

    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={recommendation}
        performancePercentage={null}
      />
    )

    expect(consoleSpy).toHaveBeenCalledWith(
      '[ExerciseInfo] Recommendation data:',
      {
        exerciseId: 27474,
        exerciseLabel: 'Bench Press',
        isBodyweight: false,
        hasRecommendation: true,
        weight: { Lb: 135, Kg: 61.23 },
        weightLb: 135,
        weightIsNull: false,
        weightIsZero: false,
        reps: 10,
        series: 3,
      }
    )

    consoleSpy.mockRestore()
    process.env.NODE_ENV = originalEnv
  })
})
