import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import { FloatingCTAButton } from '../FloatingCTAButton'

describe('FloatingCTAButton', () => {
  const mockOnClick = vi.fn()

  beforeEach(() => {
    mockOnClick.mockClear()
  })

  it('should render with default props', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button', { name: /open workout/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Open Workout')
  })

  it('should render with custom label', () => {
    render(<FloatingCTAButton onClick={mockOnClick} label="Start Exercise" />)

    const button = screen.getByRole('button')
    expect(button).toHaveTextContent('Start Exercise')
  })

  it('should render with custom aria-label', () => {
    render(
      <FloatingCTAButton
        onClick={mockOnClick}
        ariaLabel="Begin your workout session"
      />
    )

    const button = screen.getByRole('button', {
      name: 'Begin your workout session',
    })
    expect(button).toBeInTheDocument()
  })

  it('should call onClick when clicked', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    expect(mockOnClick).toHaveBeenCalledTimes(1)
  })

  it('should have centered floating positioning styles', () => {
    const { container } = render(<FloatingCTAButton onClick={mockOnClick} />)

    const wrapper = container.querySelector(
      '[data-testid="floating-cta-container"]'
    )
    expect(wrapper).toHaveClass('fixed')
    expect(wrapper).toHaveClass('bottom-6')
    expect(wrapper).toHaveClass('left-0')
    expect(wrapper).toHaveClass('right-0')
    expect(wrapper).toHaveClass('z-50')
    // px-4 should be on inner container for proper alignment
    expect(wrapper).not.toHaveClass('px-4')
  })

  it('should have theme-aware rounded corners with proper sizing', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('px-7') // Increased from px-6
    expect(button).toHaveClass('py-5') // Increased from py-4
    expect(button).toHaveClass('rounded-theme')
    expect(button).toHaveClass('min-h-[62px]') // 10% bigger than 56px
  })

  it('should use theme colors and gradient', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('bg-gradient-to-r')
    expect(button).toHaveClass('from-brand-primary')
    expect(button).toHaveClass('to-brand-secondary')
    expect(button).toHaveClass('text-text-inverse')
    expect(button).toHaveClass('font-semibold')
  })

  it('should have enhanced shadow and hover effects', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('shadow-lg')
    expect(button).toHaveClass('shadow-brand-primary/25')
    expect(button).toHaveClass('hover:shadow-xl')
    expect(button).toHaveClass('hover:shadow-brand-primary/30')
    expect(button).toHaveClass('hover:scale-[1.02]')
  })

  it('should have proper focus styles', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('focus:outline-none')
    expect(button).toHaveClass('focus:ring-2')
    expect(button).toHaveClass('focus:ring-brand-primary/50')
  })

  it('should have active scale animation', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('active:scale-[0.98]')
  })

  it('should have premium button styling', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('uppercase')
    expect(button).toHaveClass('tracking-wider')
    expect(button).toHaveClass('text-base') // Increased from text-sm
  })

  it('should have transition effects', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('transition-all')
  })

  it('should meet minimum touch target size', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')

    // The min-h-[62px] class ensures 10% bigger than original 56px
    expect(button).toHaveClass('min-h-[62px]')
  })

  it('should be accessible with navigation role', () => {
    const { container } = render(<FloatingCTAButton onClick={mockOnClick} />)

    const wrapper = container.querySelector('[role="navigation"]')
    expect(wrapper).toBeInTheDocument()
    expect(wrapper).toHaveAttribute('aria-label', 'Primary actions')
  })

  it('should have max width constraint matching card width', () => {
    const { container } = render(<FloatingCTAButton onClick={mockOnClick} />)

    const innerContainer = container.querySelector('.max-w-lg')
    expect(innerContainer).toBeInTheDocument()
    expect(innerContainer).toHaveClass('mx-auto')
    expect(innerContainer).toHaveClass('w-full')
  })

  it('should have padding inside max-width container for proper alignment', () => {
    const { container } = render(<FloatingCTAButton onClick={mockOnClick} />)

    // The fixed container should not have horizontal padding
    const fixedContainer = container.querySelector(
      '[data-testid="floating-cta-container"]'
    )
    expect(fixedContainer).not.toHaveClass('px-4')

    // The max-width container should have the padding
    const maxWidthContainer = container.querySelector('.max-w-lg')
    expect(maxWidthContainer).toHaveClass('px-4')

    // This ensures alignment with content area which has structure:
    // <div class="p-4"><div class="max-w-lg">content</div></div>
  })
})
