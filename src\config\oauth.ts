/**
 * OAuth Configuration
 *
 * This module provides type-safe OAuth configuration constants
 * for Google and Apple authentication in the Dr. Muscle X web app.
 */

/**
 * Get Google OAuth configuration
 *
 * This function allows for dynamic environment variable access,
 * which is important for testing.
 */
export function getGoogleOAuth() {
  return {
    /**
     * Google OAuth Client ID for web authentication
     */
    clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',

    /**
     * OAuth scopes for Google Sign-In
     */
    scopes: ['profile', 'email'] as const,

    /**
     * Check if Google OAuth is properly configured
     */
    isConfigured(): boolean {
      return Boolean(process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID)
    },
  }
}

/**
 * Google OAuth configuration
 */
export const googleOAuth = getGoogleOAuth()

/**
 * Get Apple OAuth configuration
 *
 * This function allows for dynamic environment variable access,
 * which is important for testing.
 */
export function getAppleOAuth() {
  return {
    /**
     * Apple Team ID
     */
    teamId: process.env.NEXT_PUBLIC_APPLE_TEAM_ID || '',

    /**
     * Apple Bundle ID
     */
    bundleId: process.env.NEXT_PUBLIC_APPLE_BUNDLE_ID || '',

    /**
     * OAuth scopes for Apple Sign-In
     */
    scopes: ['name', 'email'] as const,

    /**
     * Check if Apple OAuth is properly configured
     */
    isConfigured(): boolean {
      return Boolean(
        process.env.NEXT_PUBLIC_APPLE_TEAM_ID &&
          process.env.NEXT_PUBLIC_APPLE_BUNDLE_ID
      )
    },
  }
}

/**
 * Apple Sign-In configuration
 */
export const appleOAuth = getAppleOAuth()

/**
 * Get combined OAuth configuration
 *
 * This function allows for dynamic environment variable access,
 * which is important for testing.
 */
export function getOAuthConfig() {
  const google = getGoogleOAuth()
  const apple = getAppleOAuth()

  return {
    google,
    apple,

    /**
     * Check if any OAuth provider is configured
     */
    hasAnyProvider(): boolean {
      return google.isConfigured() || apple.isConfigured()
    },
  }
}

/**
 * Combined OAuth configuration
 */
export const oauthConfig = getOAuthConfig()

/**
 * OAuth redirect URIs based on environment
 */
export const oauthRedirectUris = {
  /**
   * Get the OAuth redirect URI for the current environment
   */
  getRedirectUri(): string {
    const baseUrl =
      typeof window !== 'undefined'
        ? window.location.origin
        : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'

    return `${baseUrl}/auth/callback`
  },

  /**
   * Get the Google OAuth redirect URI
   */
  google(): string {
    return `${this.getRedirectUri()}/google`
  },

  /**
   * Get the Apple OAuth redirect URI
   */
  apple(): string {
    return `${this.getRedirectUri()}/apple`
  },
} as const

export default oauthConfig
