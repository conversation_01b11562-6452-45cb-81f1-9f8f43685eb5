/**
 * Navigation constants and utilities for Dr. Muscle X
 */

export const ROUTES = {
  LOGIN: '/login',
  PROGRAM: '/program',
  WORKOUT: '/workout',
  PROFILE: '/profile',
  SETTINGS: '/settings',
} as const

export type RouteKey = keyof typeof ROUTES
export type RouteValue = (typeof ROUTES)[RouteKey]

/**
 * Navigation flow configuration
 */
export const NAVIGATION_FLOW = {
  // After login, go to program overview
  postLogin: ROUTES.PROGRAM,
  // From program, go to workout
  programToWorkout: ROUTES.WORKOUT,
  // From workout back to program
  workoutToProgram: ROUTES.PROGRAM,
} as const

/**
 * Protected routes that require authentication
 */
export const PROTECTED_ROUTES: RouteValue[] = [
  ROUTES.PROGRAM,
  ROUTES.WORKOUT,
  ROUTES.PROFILE,
  ROUTES.SETTINGS,
]

/**
 * Routes that should redirect to program if accessed directly
 */
export const REDIRECT_TO_PROGRAM_ROUTES: RouteValue[] = [ROUTES.WORKOUT]

/**
 * Build URL with query parameters preserved
 */
export function buildUrlWithParams(path: string, currentUrl?: string): string {
  if (!currentUrl) return path

  try {
    const url = new URL(currentUrl)
    const params = url.searchParams.toString()
    return params ? `${path}?${params}` : path
  } catch {
    return path
  }
}
