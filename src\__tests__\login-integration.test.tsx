import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { LoginPageClient } from '@/components/LoginPageClient'
import type { UseMutationResult } from '@tanstack/react-query'
import type { LoginSuccessResult, LoginModel } from '@/types'

// Mock dependencies
vi.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
    login: vi.fn().mockResolvedValue({ success: true }),
    logout: vi.fn(),
    clearError: vi.fn(),
    loginMutation: {
      mutate: vi.fn(),
      mutateAsync: vi.fn(),
      isPending: false,
      isError: false,
      isSuccess: false,
      error: null,
      data: undefined,
      reset: vi.fn(),
    },
    logoutMutation: {
      mutate: vi.fn(),
      mutateAsync: vi.fn(),
      isPending: false,
      isError: false,
      isSuccess: false,
      error: null,
      data: undefined,
      reset: vi.fn(),
    },
  }),
}))

vi.mock('@/hooks/useHapticFeedback', () => ({
  useHapticFeedback: () => ({
    success: vi.fn(),
    error: vi.fn(),
  }),
}))

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(() => null),
  }),
}))

describe('Login Page Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Complete User Flow', () => {
    it('should render all branding elements correctly', () => {
      render(<LoginPageClient />)

      // Logo
      expect(screen.getByAltText('Dr. Muscle X')).toBeInTheDocument()

      // Tagline
      expect(
        screen.getByText("World's Fastest AI Personal Trainer")
      ).toBeInTheDocument()

      // Form elements
      expect(screen.getByLabelText('Email')).toBeInTheDocument()
      expect(screen.getByLabelText('Password')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Login' })).toBeInTheDocument()

      // Footer
      expect(screen.getByText("Don't have an account?")).toBeInTheDocument()
      expect(screen.getByRole('link', { name: 'Sign up' })).toBeInTheDocument()
    })

    it('should handle complete login flow', async () => {
      const user = userEvent.setup()
      const mockLogin = vi.fn().mockResolvedValue({ success: true })

      // Re-mock with the specific login function
      vi.unmock('@/hooks/useAuth')
      vi.mock('@/hooks/useAuth', () => ({
        useAuth: () => ({
          login: mockLogin,
          isLoading: false,
          error: null,
        }),
      }))

      render(<LoginPageClient />)

      // Fill in form
      await user.type(screen.getByLabelText('Email'), '<EMAIL>')
      await user.type(screen.getByLabelText('Password'), 'password123')

      // Submit form
      await user.click(screen.getByRole('button', { name: 'Login' }))

      // Verify login was called
      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        })
      })
    })
  })

  describe('Performance', () => {
    it('should have optimized logo loading', () => {
      render(<LoginPageClient />)

      const logo = screen.getByAltText('Dr. Muscle X')

      // Should have priority attribute set (Next.js uses fetchpriority internally)
      expect(logo.getAttribute('fetchpriority')).toBe('high')

      // Should have blur placeholder
      const wrapper = logo.closest('[data-testid="logo-wrapper"]')
      expect(wrapper).toBeInTheDocument()
    })

    it('should use responsive image sizing', () => {
      render(<LoginPageClient />)

      const logo = screen.getByAltText('Dr. Muscle X')

      // Should have srcset for different densities
      expect(logo).toHaveAttribute('srcset')

      // Should have proper dimensions
      expect(logo).toHaveAttribute('width')
      expect(logo).toHaveAttribute('height')
    })

    it('should have smooth animations without jank', () => {
      render(<LoginPageClient />)

      // Check for animation classes
      const form = screen.getByTestId('login-form')
      expect(form.className).toContain('animate-slide-up')

      // Check for will-change or transform for GPU acceleration
      const animatedElements = document.querySelectorAll('.transition-all')
      expect(animatedElements.length).toBeGreaterThan(0)
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<LoginPageClient />)

      // Form should have proper labeling
      expect(screen.getByLabelText('Email')).toBeInTheDocument()
      expect(screen.getByLabelText('Password')).toBeInTheDocument()

      // Buttons should have accessible names
      expect(screen.getByRole('button', { name: 'Login' })).toBeInTheDocument()
      expect(
        screen.getByRole('button', { name: 'Show password' })
      ).toBeInTheDocument()
    })

    it('should have proper focus management', async () => {
      const user = userEvent.setup()
      render(<LoginPageClient />)

      // Tab through elements
      await user.tab()
      expect(screen.getByLabelText('Email')).toHaveFocus()

      await user.tab()
      expect(screen.getByLabelText('Password')).toHaveFocus()

      await user.tab()
      expect(
        screen.getByRole('button', { name: 'Show password' })
      ).toHaveFocus()

      await user.tab()
      expect(screen.getByRole('button', { name: 'Login' })).toHaveFocus()
    })

    it('should announce errors to screen readers', async () => {
      const user = userEvent.setup()
      render(<LoginPageClient />)

      // Submit empty form
      await user.click(screen.getByRole('button', { name: 'Login' }))

      // Check for error messages with proper ARIA
      await waitFor(() => {
        const emailError = screen.getByText('Email is required')
        expect(emailError).toHaveAttribute('role', 'alert')
      })
    })
  })

  describe('PWA Features', () => {
    it('should have safe area support', () => {
      render(<LoginPageClient />)

      // Check for safe area classes
      expect(document.querySelector('.safe-area-top')).toBeInTheDocument()
      expect(document.querySelector('.safe-area-bottom')).toBeInTheDocument()
    })

    it('should handle offline state gracefully', async () => {
      // Simulate offline
      vi.stubGlobal('navigator', { onLine: false })

      render(<LoginPageClient />)

      // Form should still be interactive
      expect(screen.getByRole('button', { name: 'Login' })).not.toBeDisabled()

      vi.unstubAllGlobals()
    })
  })

  describe('Responsive Design', () => {
    it('should adapt to different screen sizes', () => {
      render(<LoginPageClient />)

      // Check for responsive classes
      const tagline = screen.getByText("World's Fastest AI Personal Trainer")
      expect(tagline.className).toContain('text-lg')
      expect(tagline.className).toContain('sm:text-xl')
      expect(tagline.className).toContain('lg:text-2xl')
    })

    it('should handle landscape orientation', () => {
      render(<LoginPageClient />)

      const container = document.querySelector('.min-h-screen')
      expect(container?.className).toContain('landscape:')
    })
  })

  describe('Font Loading', () => {
    it('should have font loading optimization', () => {
      render(<LoginPageClient />)

      // Check for font loading monitor
      expect(document.documentElement.className).toBeDefined()
    })

    it('should use proper font fallbacks', () => {
      render(<LoginPageClient />)

      const heading = screen.getByText("World's Fastest AI Personal Trainer")
      expect(heading.className).toContain('font-heading')
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      const user = userEvent.setup()
      const { useAuth } = await import('@/hooks/useAuth')
      const mockLogin = vi.fn().mockRejectedValue(new Error('Network error'))
      vi.mocked(useAuth).mockReturnValue({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: 'Network error',
        login: mockLogin,
        logout: vi.fn(),
        clearError: vi.fn(),
        loginMutation: {
          mutate: vi.fn(),
          mutateAsync: vi.fn(),
          isPending: false,
          isError: true,
          isSuccess: false,
          error: new Error('Network error'),
          data: undefined,
          reset: vi.fn(),
        } as UseMutationResult<LoginSuccessResult, Error, LoginModel>,
        logoutMutation: {
          mutate: vi.fn(),
          mutateAsync: vi.fn(),
          isPending: false,
          isError: false,
          isSuccess: false,
          error: null,
          data: undefined,
          reset: vi.fn(),
        } as UseMutationResult<void, Error, void>,
      })

      render(<LoginPageClient />)

      // Fill and submit form
      await user.type(screen.getByLabelText('Email'), '<EMAIL>')
      await user.type(screen.getByLabelText('Password'), 'password123')
      await user.click(screen.getByRole('button', { name: 'Login' }))

      // Should show error
      await waitFor(() => {
        expect(screen.getByText('Network error')).toBeInTheDocument()
      })
    })
  })
})
