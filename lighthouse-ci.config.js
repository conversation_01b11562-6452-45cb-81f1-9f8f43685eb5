module.exports = {
  ci: {
    collect: {
      startServerCommand: 'npm run start',
      startServerReadyPattern: 'ready on',
      numberOfRuns: 3,
      url: ['http://localhost:3000'],
      settings: {
        preset: 'desktop',
        onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
        // Mobile-first settings
        formFactor: 'mobile',
        screenEmulation: {
          mobile: true,
          width: 375,
          height: 667,
          deviceScaleFactor: 2,
          disabled: false,
        },
        throttling: {
          rttMs: 150,
          throughputKbps: 1638.4,
          cpuSlowdownMultiplier: 4,
        },
      },
    },
    assert: {
      assertions: {
        // Performance metrics
        'first-contentful-paint': ['error', { maxNumericValue: 1000 }], // < 1s
        'interactive': ['error', { maxNumericValue: 1500 }], // < 1.5s
        'speed-index': ['error', { maxNumericValue: 1300 }],
        'total-blocking-time': ['warn', { maxNumericValue: 200 }],
        'largest-contentful-paint': ['error', { maxNumericValue: 1200 }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        
        // Bundle size check
        'total-byte-weight': ['error', { maxNumericValue: 153600 }], // 150KB
        'uses-text-compression': 'error',
        'uses-optimized-images': 'warn',
        'uses-webp-images': 'warn',
        
        // Categories
        'categories:performance': ['error', { minScore: 0.9 }], // 90+ score
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['warn', { minScore: 0.9 }],
        
        // Accessibility
        'tap-targets': 'error', // Touch targets >= 44px
        'button-name': 'error',
        'link-name': 'error',
        'label': 'error',
        
        // PWA
        'service-worker': 'warn',
        'works-offline': 'warn',
        'installable-manifest': 'warn',
        
        // Mobile specific
        'viewport': 'error',
        'font-size': 'error',
      },
    },
    upload: {
      target: 'temporary-public-storage',
    },
  },
}