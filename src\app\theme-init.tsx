/**
 * Theme initialization script to prevent flash of unstyled content
 * This injects critical theme CSS variables before the main app loads
 */

export function ThemeInitScript() {
  const themeScript = `
    // Apply theme immediately
    (function() {
      // Get saved theme or default to subtle-depth
      const savedTheme = localStorage.getItem('dr-muscle-x-theme') || 'subtle-depth';
      
      // Apply theme attribute
      document.documentElement.setAttribute('data-theme', savedTheme);
      
      // Define subtle-depth theme CSS variables (default)
      const themeStyles = {
        'subtle-depth': {
          '--color-bg-primary': '#0a0a0b',
          '--color-bg-secondary': '#1a1a1c',
          '--color-bg-tertiary': '#2a2a2c',
          '--color-bg-overlay': 'rgba(0, 0, 0, 0.7)',
          '--color-text-primary': '#ffffff',
          '--color-text-secondary': '#b8b8bc',
          '--color-text-tertiary': '#7a7a7e',
          '--color-text-inverse': '#0a0a0b',
          '--color-brand-primary': '#d4af37',
          '--color-brand-secondary': '#f7e98e',
          '--color-brand-accent': '#fff8dc',
          '--font-heading': '"Playfair Display", serif',
          '--font-body': '"SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
          '--shadow-sm': '0 2px 4px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(212, 175, 55, 0.1)',
          '--shadow-md': '0 4px 12px rgba(0, 0, 0, 0.6), 0 2px 4px rgba(212, 175, 55, 0.15)',
          '--shadow-lg': '0 8px 24px rgba(0, 0, 0, 0.7), 0 4px 8px rgba(212, 175, 55, 0.2)',
          '--shadow-xl': '0 16px 48px rgba(0, 0, 0, 0.8), 0 8px 16px rgba(212, 175, 55, 0.25)',
          '--radius-button': '0.5rem',
          '--color-error': '#ef4444',
          '--color-warning': '#f59e0b',
          '--color-success': '#10b981',
          '--color-info': '#3b82f6',
        }
      };
      
      // Apply CSS variables
      const root = document.documentElement;
      const styles = themeStyles[savedTheme] || themeStyles['subtle-depth'];
      
      Object.entries(styles).forEach(([key, value]) => {
        root.style.setProperty(key, value);
      });
      
      // Apply immediate inline styles to html and body to prevent white flash
      // This ensures theme is applied even before CSS loads
      root.style.backgroundColor = styles['--color-bg-primary'];
      root.style.color = styles['--color-text-primary'];
      
      // Create a style element to ensure body styles are applied immediately
      const criticalStyles = document.createElement('style');
      criticalStyles.textContent = \`
        html, body {
          background-color: \${styles['--color-bg-primary']} !important;
          color: \${styles['--color-text-primary']} !important;
        }
      \`;
      document.head.insertBefore(criticalStyles, document.head.firstChild);
      
      // Also set body styles when it becomes available
      if (document.body) {
        document.body.style.backgroundColor = styles['--color-bg-primary'];
        document.body.style.color = styles['--color-text-primary'];
        document.body.classList.add('bg-bg-primary', 'text-text-primary');
      } else {
        // If body doesn't exist yet, wait for it
        const observer = new MutationObserver(function() {
          if (document.body) {
            document.body.style.backgroundColor = styles['--color-bg-primary'];
            document.body.style.color = styles['--color-text-primary'];
            document.body.classList.add('bg-bg-primary', 'text-text-primary');
            observer.disconnect();
          }
        });
        observer.observe(document.documentElement, { childList: true, subtree: true });
      }
    })();
  `

  return (
    <script
      // eslint-disable-next-line react/no-danger
      dangerouslySetInnerHTML={{ __html: themeScript }}
      // Run before React hydration
    />
  )
}
