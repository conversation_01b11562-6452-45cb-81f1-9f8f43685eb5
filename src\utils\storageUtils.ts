/**
 * Storage utilities for handling localStorage quota and errors
 */

import { logger } from '@/utils/logger'

/**
 * Clear old or less important data from localStorage
 */
function clearOldStorageData(): boolean {
  try {
    // Clear workout cache data but preserve session
    const workoutData = localStorage.getItem('drmuscle-workout')
    if (workoutData) {
      try {
        const parsed = JSON.parse(workoutData)
        // Clear cached API data but keep workout session
        if (parsed.state?.cachedData) {
          parsed.state.cachedData = {
            userProgramInfo: null,
            userWorkouts: null,
            todaysWorkout: null,
            exerciseRecommendations: {},
            lastUpdated: {
              userProgramInfo: 0,
              userWorkouts: 0,
              todaysWorkout: 0,
              exerciseRecommendations: {},
            },
          }
          localStorage.setItem('drmuscle-workout', JSON.stringify(parsed))
          logger.log('Cleared workout cache data to free up space')
          return true
        }
      } catch (e) {
        logger.error('Error parsing workout data:', e)
      }
    }

    // If that didn't work, clear other non-essential data
    const nonEssentialKeys = [
      'drmuscle-debug',
      'drmuscle-telemetry',
      'drmuscle-preferences',
    ]

    nonEssentialKeys.forEach((key) => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key)
        logger.log(`Removed ${key} to free up space`)
      }
    })

    return true
  } catch (error) {
    logger.error('Error clearing old storage data:', error)
    return false
  }
}

/**
 * Get available localStorage space (approximate)
 */
export function getStorageQuota(): { used: number; available: number } {
  let used = 0
  let available = 5 * 1024 * 1024 // 5MB default

  try {
    // Calculate used space
    Object.keys(localStorage).forEach((key) => {
      used += localStorage.getItem(key)?.length || 0
      used += key.length
    })

    // Try to estimate available space
    const testKey = 'drmuscle-quota-test'
    const testData = new Array(1024).join('a') // 1KB string
    let chunks = 0

    try {
      // Try adding 1KB chunks until we hit quota
      while (chunks < 10240) {
        // Max 10MB test
        localStorage.setItem(testKey + chunks, testData)
        chunks++
      }
    } catch (e) {
      // Quota exceeded, we found the limit
    }

    // Clean up test data
    for (let i = 0; i < chunks; i++) {
      localStorage.removeItem(testKey + i)
    }

    available = used + chunks * 1024
  } catch (error) {
    logger.error('Error calculating storage quota:', error)
  }

  return { used, available }
}

/**
 * Safely set an item in localStorage with quota error handling
 */
export function safeSetItem(key: string, value: string): boolean {
  try {
    localStorage.setItem(key, value)
    return true
  } catch (error) {
    if (error instanceof DOMException && error.name === 'QuotaExceededError') {
      logger.error(`localStorage quota exceeded for key: ${key}`)
      // Try to clear old data and retry
      if (clearOldStorageData()) {
        try {
          localStorage.setItem(key, value)
          return true
        } catch (retryError) {
          logger.error(
            'Failed to set item even after clearing old data:',
            retryError
          )
          return false
        }
      }
    }
    logger.error('Error setting localStorage item:', error)
    return false
  }
}

/**
 * Compress data before storing (optional, for future use)
 */
export function compressData(data: string): string {
  // For now, just return the data as-is
  // In the future, we could implement LZ compression here
  return data
}

/**
 * Decompress data after retrieving (optional, for future use)
 */
export function decompressData(data: string): string {
  // For now, just return the data as-is
  // In the future, we could implement LZ decompression here
  return data
}
