import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  validateEnv,
  getEnvVar,
  isProduction,
  isDevelopment,
  isStaging,
} from '../validateEnv'

describe('Environment Validation', () => {
  const originalEnv = process.env

  beforeEach(() => {
    vi.resetModules()
    process.env = { ...originalEnv }
  })

  describe('validateEnv', () => {
    it('should pass validation with all required variables', () => {
      process.env.NEXT_PUBLIC_API_URL = 'https://api.example.com'
      process.env.NEXT_PUBLIC_APP_ENV = 'production'

      const result = validateEnv()
      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should fail validation when API URL is missing', () => {
      delete process.env.NEXT_PUBLIC_API_URL
      process.env.NEXT_PUBLIC_APP_ENV = 'production'

      const result = validateEnv()
      expect(result.valid).toBe(false)
      expect(result.errors).toContain(
        'Missing required environment variable: NEXT_PUBLIC_API_URL'
      )
    })

    it('should fail validation when API URL is invalid', () => {
      process.env.NEXT_PUBLIC_API_URL = 'invalid-url'
      process.env.NEXT_PUBLIC_APP_ENV = 'production'

      const result = validateEnv()
      expect(result.valid).toBe(false)
      expect(result.errors).toContain(
        'Invalid value for environment variable: NEXT_PUBLIC_API_URL'
      )
    })

    it('should fail validation when APP_ENV has invalid value', () => {
      process.env.NEXT_PUBLIC_API_URL = 'https://api.example.com'
      process.env.NEXT_PUBLIC_APP_ENV = 'invalid'

      const result = validateEnv()
      expect(result.valid).toBe(false)
      expect(result.errors).toContain(
        'Invalid value for environment variable: NEXT_PUBLIC_APP_ENV'
      )
    })

    it('should warn when no OAuth providers are configured', () => {
      process.env.NEXT_PUBLIC_API_URL = 'https://api.example.com'
      process.env.NEXT_PUBLIC_APP_ENV = 'production'
      delete process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID
      delete process.env.NEXT_PUBLIC_APPLE_TEAM_ID

      const result = validateEnv()
      expect(result.valid).toBe(true)
      expect(result.warnings).toContain(
        'No OAuth providers configured. At least one OAuth provider (Google or Apple) should be configured.'
      )
    })

    it('should fail when Apple Team ID is set but Bundle ID is missing', () => {
      process.env.NEXT_PUBLIC_API_URL = 'https://api.example.com'
      process.env.NEXT_PUBLIC_APP_ENV = 'production'
      process.env.NEXT_PUBLIC_APPLE_TEAM_ID = 'team-id'
      delete process.env.NEXT_PUBLIC_APPLE_BUNDLE_ID

      const result = validateEnv()
      expect(result.valid).toBe(false)
      expect(result.errors).toContain(
        'Apple Team ID is configured but Bundle ID is missing'
      )
    })

    it('should use default value for APP_ENV if not set', () => {
      process.env.NEXT_PUBLIC_API_URL = 'https://api.example.com'
      delete process.env.NEXT_PUBLIC_APP_ENV

      const result = validateEnv()
      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
  })

  describe('getEnvVar', () => {
    it('should return environment variable value', () => {
      process.env.TEST_VAR = 'test-value'
      expect(getEnvVar('TEST_VAR')).toBe('test-value')
    })

    it('should return default value when variable is not set', () => {
      delete process.env.TEST_VAR
      expect(getEnvVar('TEST_VAR', 'default')).toBe('default')
    })

    it('should throw error when variable is not set and no default provided', () => {
      delete process.env.TEST_VAR
      expect(() => getEnvVar('TEST_VAR')).toThrow(
        'Environment variable TEST_VAR is not set'
      )
    })
  })

  describe('Environment helpers', () => {
    it('should correctly identify production environment', () => {
      process.env.NEXT_PUBLIC_APP_ENV = 'production'
      expect(isProduction()).toBe(true)
      expect(isDevelopment()).toBe(false)
      expect(isStaging()).toBe(false)
    })

    it('should correctly identify development environment', () => {
      process.env.NEXT_PUBLIC_APP_ENV = 'development'
      expect(isProduction()).toBe(false)
      expect(isDevelopment()).toBe(true)
      expect(isStaging()).toBe(false)
    })

    it('should correctly identify staging environment', () => {
      process.env.NEXT_PUBLIC_APP_ENV = 'staging'
      expect(isProduction()).toBe(false)
      expect(isDevelopment()).toBe(false)
      expect(isStaging()).toBe(true)
    })
  })
})
