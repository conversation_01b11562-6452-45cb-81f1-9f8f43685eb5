import { test, expect, Page } from '@playwright/test'

const TEST_USER = {
  email: '<EMAIL>',
  password: 'Dr123456',
}

test.describe('Authentication Network Failure Handling', () => {
  let page: Page

  test.beforeEach(async ({ page: p }) => {
    page = p
    await page.goto('/')
  })

  test('should handle network failure during login', async () => {
    await page.goto('/login')

    // Block API requests
    await page.route('**/api/token', (route) =>
      route.abort('internetdisconnected')
    )

    // Try to login
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    // Should show network error message
    await expect(
      page.locator('text=Network error. Please check your connection.')
    ).toBeVisible({ timeout: 10000 })

    // Should remain on login page
    await expect(page).toHaveURL('/login')
  })

  test('should retry login after network recovery', async () => {
    await page.goto('/login')

    let requestCount = 0
    await page.route('**/api/token', (route) => {
      requestCount++
      if (requestCount <= 2) {
        // Fail first 2 attempts
        route.abort('internetdisconnected')
      } else {
        // Succeed on 3rd attempt
        route.continue()
      }
    })

    // Try to login
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    // Should show retry button after failure
    await expect(page.locator('button:has-text("Retry")')).toBeVisible({
      timeout: 10000,
    })

    // Click retry
    await page.click('button:has-text("Retry")')

    // Should eventually succeed
    await expect(page).toHaveURL('/program', { timeout: 15000 })
  })

  test('should handle OAuth SDK loading failure', async () => {
    await page.goto('/login')

    // Block Google SDK
    await page.route('**/accounts.google.com/**', (route) => route.abort())

    // Try to sign in with Google
    await page.click('button:has-text("Continue with Google")')

    // Should show error message
    await expect(
      page.locator('text=Failed to load Google Sign-In')
    ).toBeVisible({ timeout: 10000 })
  })

  test('should handle Apple SDK loading failure', async () => {
    await page.goto('/login')

    // Block Apple SDK
    await page.route('**/appleid.apple.com/**', (route) => route.abort())

    // Try to sign in with Apple
    await page.click('button:has-text("Continue with Apple")')

    // Should show error message
    await expect(page.locator('text=Failed to load Apple Sign-In')).toBeVisible(
      { timeout: 10000 }
    )
  })

  test('should handle token refresh network failure', async () => {
    // First login successfully
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Simulate token expiration
    await page.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        parsed.state.tokenExpiry = Date.now() - 1000 // Expired
        localStorage.setItem('auth-storage', JSON.stringify(parsed))
      }
    })

    // Block refresh token requests
    await page.route('**/api/token/refresh', (route) =>
      route.abort('internetdisconnected')
    )

    // Navigate to protected page
    await page.goto('/workout')

    // Should show session expired message
    await expect(page.locator('text=Session expired')).toBeVisible({
      timeout: 10000,
    })

    // Should redirect to login
    await expect(page).toHaveURL('/login')
  })

  test('should handle offline mode gracefully', async () => {
    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Go offline
    await page.context().setOffline(true)

    // Try to navigate
    await page.goto('/workout')

    // Should show offline message
    await expect(page.locator('text=You are offline')).toBeVisible()

    // Should still show cached data if available
    await expect(page.locator('h1')).toContainText('Workout')
  })

  test('should queue auth requests when offline', async () => {
    await page.goto('/login')

    // Go offline before login
    await page.context().setOffline(true)

    // Try to login
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    // Should show offline message
    await expect(
      page.locator(
        'text=You are offline. Your login will be processed when connection is restored.'
      )
    ).toBeVisible()

    // Go back online
    await page.context().setOffline(false)

    // Should automatically process login
    await expect(page).toHaveURL('/program', { timeout: 10000 })
  })

  test('should handle timeout during OAuth', async () => {
    await page.goto('/login')

    // Add artificial delay to OAuth endpoint
    await page.route('**/api/oauth/**', async (route) => {
      await new Promise((resolve) => setTimeout(resolve, 35000)) // 35 second delay
      route.continue()
    })

    // Click Google sign in
    const popupPromise = page.waitForEvent('popup')
    await page.click('button:has-text("Continue with Google")')
    const popup = await popupPromise

    // Close popup to simulate completion
    await popup.close()

    // Should show timeout error
    await expect(page.locator('text=Authentication timeout')).toBeVisible({
      timeout: 40000,
    })
  })

  test('should handle CORS errors gracefully', async () => {
    await page.goto('/login')

    // Intercept and modify response to simulate CORS error
    await page.route('**/api/token', (route) => {
      route.fulfill({
        status: 0,
        body: '',
      })
    })

    // Try to login
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    // Should show generic error (not expose CORS details)
    await expect(page.locator('text=Unable to connect to server')).toBeVisible()
  })

  test('should handle slow network gracefully', async () => {
    await page.goto('/login')

    // Add 3 second delay to all requests
    await page.route('**/api/**', async (route) => {
      await new Promise((resolve) => setTimeout(resolve, 3000))
      route.continue()
    })

    // Try to login
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    // Should show loading state
    await expect(page.locator('button[type="submit"]')).toBeDisabled()
    await expect(page.locator('text=Signing in...')).toBeVisible()

    // Should eventually succeed
    await expect(page).toHaveURL('/program', { timeout: 10000 })
  })

  test('should handle intermittent network failures', async () => {
    await page.goto('/login')

    let requestCount = 0
    await page.route('**/api/**', (route) => {
      requestCount++
      // Fail every other request
      if (requestCount % 2 === 0) {
        route.abort('failed')
      } else {
        route.continue()
      }
    })

    // Should eventually succeed with retries
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    await expect(page).toHaveURL('/program', { timeout: 15000 })
  })

  test('should preserve form data during network failures', async () => {
    await page.goto('/login')

    // Fill form
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)

    // Block first request
    let blocked = true
    await page.route('**/api/token', (route) => {
      if (blocked) {
        blocked = false
        route.abort('internetdisconnected')
      } else {
        route.continue()
      }
    })

    // Try to submit
    await page.click('button[type="submit"]')

    // Wait for error
    await expect(page.locator('text=Network error')).toBeVisible()

    // Form data should be preserved
    await expect(page.locator('input[type="email"]')).toHaveValue(
      TEST_USER.email
    )
    await expect(page.locator('input[type="password"]')).toHaveValue(
      TEST_USER.password
    )

    // Retry should work without re-entering data
    await page.click('button[type="submit"]')
    await expect(page).toHaveURL('/program', { timeout: 10000 })
  })
})
