import { test, expect, Page } from '@playwright/test'

test.describe('Cache Storage Management Tests', () => {
  let page: Page
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p }) => {
    page = p
    // Clear all caches before each test
    await page.goto('/')
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
      if ('caches' in window) {
        caches.keys().then((names) => {
          names.forEach((name) => caches.delete(name))
        })
      }
    })
  })

  test('should properly initialize cache stores on first login', async () => {
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Check cache initialization
    const cacheState = await page.evaluate(() => {
      return {
        hasAuthCache: !!localStorage.getItem('auth-storage'),
        hasWorkoutCache: !!localStorage.getItem('drmuscle-workout'),
        hasProgramCache: !!localStorage.getItem('drmuscle-program'),
        hasUserStatsCache: !!localStorage.getItem('drmuscle-userstats'),
      }
    })

    expect(cacheState.hasAuthCache).toBe(true)
    // Other caches may be lazy-loaded
  })

  test('should cache workout data after first load', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Navigate to workout
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Wait for workout data to load
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

    // Check cache contents
    const cacheData = await page.evaluate(() => {
      const workoutCache = localStorage.getItem('drmuscle-workout')
      return workoutCache ? JSON.parse(workoutCache) : null
    })

    expect(cacheData).toBeTruthy()
    expect(cacheData.state).toHaveProperty('workoutInfo')
    expect(cacheData.state).toHaveProperty('exercises')
  })

  test('should serve cached data on subsequent visits', async () => {
    // Login and load workout
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

    // Wait for initial load to complete
    await page.waitForTimeout(100)

    // Navigate away and back
    await page.goto('/program')

    // Block API requests to ensure cache is used
    await page.route('**/GetWorkoutInfo', (route) => route.abort())
    await page.route('**/GetExercise*', (route) => route.abort())

    // Navigate back to workout
    const startTime = await page.evaluate(() => performance.now())
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Should still show exercises from cache
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

    const cacheLoadTime = await page.evaluate(
      (start) => performance.now() - start,
      startTime
    )

    // Cache load should be much faster
    expect(cacheLoadTime).toBeLessThan(1000) // Less than 1 second
  })

  test('should handle cache size limits gracefully', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Fill localStorage to near capacity
    await page.evaluate(() => {
      const largeData = 'x'.repeat(1024 * 1024) // 1MB string
      let counter = 0
      try {
        // Fill until we can't anymore
        while (counter < 5) {
          localStorage.setItem(`test-data-${counter}`, largeData)
          counter++
        }
      } catch (e) {
        // Expected to fail at some point
      }
    })

    // Try to cache workout data
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Should still work (either by clearing old data or using memory cache)
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

    // Clean up test data
    await page.evaluate(() => {
      for (let i = 0; i < 5; i++) {
        localStorage.removeItem(`test-data-${i}`)
      }
    })
  })

  test('should clear specific cache entries without affecting others', async () => {
    // Login and load data
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Load workout to populate cache
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

    // Clear only workout cache
    await page.evaluate(() => {
      localStorage.removeItem('drmuscle-workout')
    })

    // Auth cache should still exist
    const authCacheExists = await page.evaluate(() => {
      return !!localStorage.getItem('auth-storage')
    })
    expect(authCacheExists).toBe(true)

    // Should still be logged in
    await page.reload()
    await expect(page).toHaveURL('/workout')
  })

  test('should handle cache corruption gracefully', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Corrupt cache data
    await page.evaluate(() => {
      localStorage.setItem('drmuscle-workout', 'corrupted{invalid-json')
    })

    // Try to load workout
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Should recover and load fresh data
    await expect(page.locator('[data-testid="exercise-list"]')).toBeVisible()

    // Cache should be repaired
    const cacheValid = await page.evaluate(() => {
      try {
        const cache = localStorage.getItem('drmuscle-workout')
        return cache ? !!JSON.parse(cache) : false
      } catch {
        return false
      }
    })
    expect(cacheValid).toBe(true)
  })
})
