'use client'

export default function OfflinePage() {
  return (
    <div className="flex min-h-[100dvh] items-center justify-center p-4">
      <div className="text-center">
        <h1 className="mb-4 text-4xl font-bold">You're Offline</h1>
        <p className="mb-8 text-text-secondary">
          Please check your internet connection and try again.
        </p>
        <button
          onClick={() => window.location.reload()}
          className="rounded-theme bg-brand-primary px-6 py-2 text-text-inverse hover:bg-brand-primary/90"
        >
          Try Again
        </button>
      </div>
    </div>
  )
}
