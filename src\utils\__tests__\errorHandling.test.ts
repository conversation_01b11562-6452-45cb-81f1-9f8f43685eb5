import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  isCorruptedCacheError,
  clearCorruptedCache,
  getUserFriendlyErrorMessage,
  handleErrorWithRecovery,
  withErrorRecovery,
} from '../errorHandling'
import { useAuthStore } from '@/stores/authStore'
import { useProgramStore } from '@/stores/programStore'

// Mock stores
vi.mock('@/stores/authStore')
vi.mock('@/stores/programStore')

// Mock toast but make it undefined for console.error tests
vi.mock('@/components/ui/toast', () => ({
  toast: undefined,
}))

describe('errorHandling', () => {
  let originalOnLine: boolean

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()

    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    }
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    })

    // Store original onLine value
    originalOnLine = navigator.onLine
  })

  afterEach(() => {
    // Restore navigator.onLine using vi.stubGlobal
    vi.stubGlobal('navigator', { ...navigator, onLine: originalOnLine })
  })

  describe('isCorruptedCacheError', () => {
    it('should detect JSON parse errors', () => {
      const error = new Error('JSON.parse failed')
      expect(isCorruptedCacheError(error)).toBe(true)
    })

    it('should detect SyntaxError in message', () => {
      const error = new Error('SyntaxError: Unexpected token')
      expect(isCorruptedCacheError(error)).toBe(true)
    })

    it('should detect TypeError with Cannot read', () => {
      const error = new TypeError('Cannot read property of undefined')
      expect(isCorruptedCacheError(error)).toBe(true)
    })

    it('should detect persist errors', () => {
      const error = new Error('persist middleware error')
      expect(isCorruptedCacheError(error)).toBe(true)
    })

    it('should detect storage errors', () => {
      const error = new Error('storage quota exceeded')
      expect(isCorruptedCacheError(error)).toBe(true)
    })

    it('should return false for normal errors', () => {
      const error = new Error('Network timeout')
      expect(isCorruptedCacheError(error)).toBe(false)
    })

    it('should return false for non-Error objects', () => {
      expect(isCorruptedCacheError('string error')).toBe(false)
      expect(isCorruptedCacheError(null)).toBe(false)
      expect(isCorruptedCacheError(undefined)).toBe(false)
    })
  })

  describe('clearCorruptedCache', () => {
    it('should clear auth store cache', () => {
      const clearUserInfoCache = vi.fn()
      vi.mocked(useAuthStore).getState.mockReturnValue({
        clearUserInfoCache,
      } as any)

      clearCorruptedCache('auth')
      expect(clearUserInfoCache).toHaveBeenCalled()
    })

    it('should clear program store cache', () => {
      const clearAllCache = vi.fn()
      vi.mocked(useProgramStore).getState.mockReturnValue({
        clearAllCache,
      } as any)

      clearCorruptedCache('program')
      expect(clearAllCache).toHaveBeenCalled()
    })

    it('should clear both stores when no store specified', () => {
      const clearUserInfoCache = vi.fn()
      const clearAllCache = vi.fn()

      vi.mocked(useAuthStore).getState.mockReturnValue({
        clearUserInfoCache,
      } as any)
      vi.mocked(useProgramStore).getState.mockReturnValue({
        clearAllCache,
      } as any)

      clearCorruptedCache()
      expect(clearUserInfoCache).toHaveBeenCalled()
      expect(clearAllCache).toHaveBeenCalled()
    })

    it('should remove corrupted localStorage items', () => {
      const removeItem = vi.fn()
      window.localStorage.getItem = vi.fn().mockImplementation((key) => {
        if (key === 'auth-store') {
          return 'invalid json {'
        }
        return null
      })
      window.localStorage.removeItem = removeItem

      clearCorruptedCache()
      expect(removeItem).toHaveBeenCalledWith('auth-store')
    })

    it('should handle errors gracefully', () => {
      vi.mocked(useAuthStore).getState.mockImplementation(() => {
        throw new Error('Store error')
      })

      // Should not throw
      expect(() => clearCorruptedCache()).not.toThrow()
    })
  })

  describe('getUserFriendlyErrorMessage', () => {
    it('should return network error message when offline', () => {
      vi.stubGlobal('navigator', { ...navigator, onLine: false })
      const error = new Error('Failed to fetch')
      expect(getUserFriendlyErrorMessage(error)).toBe(
        'Unable to connect. Please check your internet connection.'
      )
    })

    it('should handle 401 errors', () => {
      const error = new Error('Unauthorized') as any
      error.status = 401
      expect(getUserFriendlyErrorMessage(error)).toBe(
        'Your session has expired. Please log in again.'
      )
    })

    it('should handle 403 errors', () => {
      const error = new Error('Forbidden') as any
      error.status = 403
      expect(getUserFriendlyErrorMessage(error)).toBe(
        "You don't have permission to access this resource."
      )
    })

    it('should handle 404 errors', () => {
      const error = new Error('Not found') as any
      error.status = 404
      expect(getUserFriendlyErrorMessage(error)).toBe(
        'The requested information could not be found.'
      )
    })

    it('should handle 5xx errors', () => {
      const error = new Error('Server error') as any
      error.status = 500
      expect(getUserFriendlyErrorMessage(error)).toBe(
        'Our servers are experiencing issues. Please try again later.'
      )
    })

    it('should handle timeout errors', () => {
      const error = new Error('Request timed out')
      expect(getUserFriendlyErrorMessage(error)).toBe(
        'The request took too long. Please try again.'
      )
    })

    it('should handle corrupted cache errors', () => {
      const error = new Error('JSON.parse failed')
      expect(getUserFriendlyErrorMessage(error)).toBe(
        "There was an issue with your saved data. We've cleared it and you can try again."
      )
    })

    it('should return generic message for unknown errors', () => {
      const error = new Error('Unknown error')
      expect(getUserFriendlyErrorMessage(error)).toBe(
        'Something went wrong. Please try again.'
      )
    })

    it('should handle non-Error objects', () => {
      expect(getUserFriendlyErrorMessage('string')).toBe(
        'An unexpected error occurred'
      )
    })
  })

  describe('handleErrorWithRecovery', () => {
    it('should clear cache and retry for corrupted cache errors', async () => {
      const retry = vi.fn().mockResolvedValue(undefined)
      const onCacheCleared = vi.fn()
      const error = new Error('JSON.parse failed')

      await handleErrorWithRecovery(error, {
        retry,
        onCacheCleared,
        storeName: 'auth',
      })

      expect(onCacheCleared).toHaveBeenCalled()
      expect(retry).toHaveBeenCalled()
    })

    it('should show notification for other errors', async () => {
      const error = new Error('Network error')
      const consoleError = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      await handleErrorWithRecovery(error)

      // showErrorNotification is called, which should log to console
      expect(consoleError).toHaveBeenCalled()
      const { calls } = consoleError.mock
      expect(calls.length).toBeGreaterThan(0)
      expect(calls[0]).toContain('Error:')

      consoleError.mockRestore()
    })

    it('should handle retry failures', async () => {
      const retry = vi.fn().mockRejectedValue(new Error('Retry failed'))
      const error = new Error('JSON.parse failed')
      const consoleError = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      await handleErrorWithRecovery(error, { retry })

      expect(retry).toHaveBeenCalled()
      expect(consoleError).toHaveBeenCalled()

      consoleError.mockRestore()
    })
  })

  describe('withErrorRecovery', () => {
    it('should wrap async functions with error recovery', async () => {
      const mockFn = vi.fn().mockResolvedValue('success')
      const wrapped = withErrorRecovery(mockFn)

      const result = await wrapped('arg1', 'arg2')
      expect(result).toBe('success')
      expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2')
    })

    it('should handle errors and re-throw', async () => {
      const error = new Error('Test error')
      const mockFn = vi.fn().mockRejectedValue(error)
      const wrapped = withErrorRecovery(mockFn)

      await expect(wrapped()).rejects.toThrow('Test error')
      expect(mockFn).toHaveBeenCalledTimes(1)
    })

    it('should retry on corrupted cache errors', async () => {
      const error = new Error('JSON.parse failed')
      const mockFn = vi
        .fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValueOnce('success')

      const onCacheCleared = vi.fn()
      const wrapped = withErrorRecovery(mockFn, {
        storeName: 'auth',
        onCacheCleared,
      })

      await expect(wrapped()).rejects.toThrow('JSON.parse failed')
      expect(mockFn).toHaveBeenCalledTimes(2) // Original + retry
      expect(onCacheCleared).toHaveBeenCalled()
    })
  })
})
