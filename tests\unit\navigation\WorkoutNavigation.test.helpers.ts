import { vi } from 'vitest'

// Type for the useWorkout hook return
export type UseWorkoutReturn = ReturnType<
  typeof import('@/hooks/useWorkout').useWorkout
>

export function createMockUseWorkoutReturn(
  overrides?: Partial<UseWorkoutReturn>
): UseWorkoutReturn {
  return {
    // Data
    todaysWorkout: null,
    userProgramInfo: null,
    currentWorkout: null,
    currentExercise: null,
    nextExercise: null,
    currentSetIndex: 0,
    totalSets: 0,
    isLastSet: false,
    isLastExercise: false,
    isWarmupSet: false,
    workoutSession: null,

    // Loading states
    isLoadingWorkout: false,
    isLoading: false,
    loadingStates: {
      programInfo: false,
      userWorkout: false,
      recommendation: false,
    },

    // Errors
    workoutError: null,
    error: null,

    // Recommendation
    recommendation: null,
    refetchRecommendation: vi.fn(),

    // Actions
    saveSet: vi.fn(),
    startWorkout: vi.fn(),
    finishWorkout: vi.fn(),
    nextSet: vi.fn(),
    restoreWorkout: vi.fn(),
    goToNextExercise: vi.fn(),
    getRecommendation: vi.fn(),
    getRestDuration: vi.fn().mockReturnValue(120),

    // Status
    isOffline: false,

    // Progressive loading support
    expectedExerciseCount: undefined,

    // Apply overrides
    ...overrides,
  } as UseWorkoutReturn
}
