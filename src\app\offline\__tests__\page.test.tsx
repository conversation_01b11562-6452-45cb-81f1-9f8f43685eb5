import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import OfflinePage from '../page'

describe('OfflinePage', () => {
  it('should use theme CSS variables instead of hardcoded colors', () => {
    render(<OfflinePage />)

    // Check that the description text uses theme color
    const description = screen.getByText(
      /Please check your internet connection/i
    )
    expect(description).toHaveClass('text-text-secondary')
    expect(description).not.toHaveClass('text-gray-600')

    // Check that the button uses theme colors
    const button = screen.getByRole('button', { name: /Try Again/i })
    expect(button).toHaveClass('bg-brand-primary')
    expect(button).toHaveClass('text-text-inverse')
    expect(button).toHaveClass('hover:bg-brand-primary/90')
    expect(button).not.toHaveClass('bg-blue-500')
    expect(button).not.toHaveClass('text-white')
    expect(button).not.toHaveClass('hover:bg-blue-600')
  })

  it('should have correct rounded corners using theme', () => {
    render(<OfflinePage />)

    const button = screen.getByRole('button', { name: /Try Again/i })
    expect(button).toHaveClass('rounded-theme')
    expect(button).not.toHaveClass('rounded')
  })
})
