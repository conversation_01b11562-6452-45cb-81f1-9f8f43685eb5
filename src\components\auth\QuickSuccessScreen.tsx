'use client'

import { useEffect, useState } from 'react'
import { SuccessIcon } from '@/components/workout'

interface QuickSuccessScreenProps {
  onComplete: () => void
}

export function QuickSuccessScreen({ onComplete }: QuickSuccessScreenProps) {
  const [showCheckmark, setShowCheckmark] = useState(true)
  const [showWelcome, setShowWelcome] = useState(false)

  useEffect(() => {
    // Hide checkmark after 400ms
    const hideCheckmarkTimer = setTimeout(() => setShowCheckmark(false), 400)
    // Show welcome text at 400ms
    const welcomeTimer = setTimeout(() => setShowWelcome(true), 400)
    // Complete at 800ms using requestAnimationFrame to avoid XHR violation
    const completeTimer = setTimeout(() => {
      // Use requestAnimationFrame to avoid XHR violation during DOM updates
      // This prevents synchronous XHR requests during layout/paint operations
      if (typeof requestAnimationFrame !== 'undefined') {
        requestAnimationFrame(() => {
          onComplete()
        })
      } else {
        // Fallback for environments without requestAnimationFrame
        onComplete()
      }
    }, 800)

    return () => {
      clearTimeout(hideCheckmarkTimer)
      clearTimeout(welcomeTimer)
      clearTimeout(completeTimer)
    }
  }, [onComplete])

  return (
    <div
      data-testid="quick-success-screen"
      className="min-h-[100dvh] flex flex-col items-center justify-center bg-bg-primary px-6"
    >
      {showCheckmark && <SuccessIcon size={120} />}
      {showWelcome && (
        <p className="text-xl font-medium text-text-primary animate-fade-in">
          Welcome back!
        </p>
      )}
    </div>
  )
}
