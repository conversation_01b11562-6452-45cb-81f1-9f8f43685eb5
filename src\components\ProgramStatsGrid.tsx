import React from 'react'
import { StatCard } from './StatCard'
import type { ProgramStats } from '@/types'
import { useComponentPerformance } from '@/hooks/useComponentPerformance'

export interface ProgramStatsGridProps {
  /** Program statistics */
  stats?: ProgramStats
  /** Loading state */
  isLoading?: boolean
  /** Additional CSS classes */
  className?: string
}

// Icon components
function StreakIcon() {
  return (
    <svg
      className="w-5 h-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z"
      />
    </svg>
  )
}

function WorkoutsIcon() {
  return (
    <svg
      className="w-5 h-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
      />
    </svg>
  )
}

function WeightLiftedIcon() {
  return (
    <svg
      className="w-5 h-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 9c0-2.21-1.79-4-4-4H9c-2.21 0-4 1.79-4 4 0 1.66 1.01 3.08 2.44 3.68l.56.22v2.6c0 .83.67 1.5 1.5 1.5h5c.83 0 1.5-.67 1.5-1.5v-2.6l.56-.22C17.99 12.08 19 10.66 19 9z"
      />
    </svg>
  )
}

function BodyWeightIcon() {
  return (
    <svg
      className="w-5 h-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"
      />
    </svg>
  )
}

function RecoveryIcon() {
  return (
    <svg
      className="w-5 h-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  )
}

function CoachIcon() {
  return (
    <svg
      className="w-5 h-5"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
      />
    </svg>
  )
}

// Helper function to format large numbers
function formatNumber(num: number): string {
  if (num >= 1000000000) {
    return `${(num / 1000000000).toFixed(0)}B`
  } else if (num >= 1000000) {
    return `${(num / 1000000).toFixed(0)}M`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(0)}K`
  }
  return num.toString()
}

export function ProgramStatsGrid({
  stats,
  isLoading = false,
  className = '',
}: ProgramStatsGridProps) {
  // Track component performance
  useComponentPerformance('ProgramStatsGrid')

  return (
    <div
      data-testid="program-stats-grid"
      className={`grid grid-cols-3 gap-3 ${className}`}
    >
      {/* Weeks Streak */}
      <StatCard
        icon={<StreakIcon />}
        label="Weeks streak"
        value={stats?.consecutiveWeeks ?? '-'}
        format={(v) => (v === '-' ? '-' : `${v}`)}
        isLoading={isLoading}
      />

      {/* Workouts Complete */}
      <StatCard
        icon={<WorkoutsIcon />}
        label="Workouts done"
        value={stats?.totalWorkoutsCompleted ?? '-'}
        format={(v) => (v === '-' ? '-' : `${v}`)}
        isLoading={isLoading}
      />

      {/* Lbs Lifted */}
      <StatCard
        icon={<WeightLiftedIcon />}
        label="Lbs lifted"
        value={stats?.totalVolume ?? '-'}
        format={(v) => (v === '-' ? '-' : formatNumber(Number(v)))}
        isLoading={isLoading}
      />

      {/* Body Weight */}
      <StatCard
        icon={<BodyWeightIcon />}
        label="Body weight"
        value={stats?.bodyWeight ?? '-'}
        format={(v) => (v === '-' ? '-' : `${v}`)}
        isLoading={isLoading}
      />

      {/* Recovery */}
      <StatCard
        icon={<RecoveryIcon />}
        label="Recovery"
        value={stats?.recoveryDays ?? '-'}
        format={(v) =>
          v === '-' ? '-' : `${v} ${Number(v) === 1 ? 'day' : 'days'}`
        }
        isLoading={isLoading}
      />

      {/* Coach Says */}
      <StatCard
        icon={<CoachIcon />}
        label="Coach says"
        value={stats?.coachRecommendation ?? '-'}
        isLoading={isLoading}
      />
    </div>
  )
}
