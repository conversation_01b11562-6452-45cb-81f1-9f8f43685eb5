import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { LoginPageClient } from '@/components/LoginPageClient'

// Mock dependencies
vi.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    login: vi.fn(),
    loginStatus: 'idle',
    errorMessage: null,
  }),
}))

vi.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    login: vi.fn(),
    isLoading: false,
    error: null,
  }),
}))

vi.mock('@/hooks/useHapticFeedback', () => ({
  useHapticFeedback: () => ({
    success: vi.fn(),
    error: vi.fn(),
  }),
}))

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
  useSearchParams: () => ({
    get: vi.fn(() => null),
  }),
}))

describe('Login Page Animations', () => {
  beforeEach(() => {
    // Mock IntersectionObserver
    global.IntersectionObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    }))
  })

  it('should have fade-in animation classes on logo', async () => {
    render(<LoginPageClient />)

    const logo = screen.getByAltText('Dr. Muscle X')
    // The animation is on the header container, not the logo itself
    const headerContainer = logo.closest('.text-center')

    // Check for animation classes
    expect(headerContainer?.className).toMatch(
      /transition-all|opacity-\d+|translate/
    )
  })

  it('should have slide-up animation on form', async () => {
    render(<LoginPageClient />)

    const form = screen.getByTestId('login-form')

    // Check for slide-up animation classes
    expect(form.className).toMatch(/animate-slide-up|transition/)
  })

  it('should have hover states on buttons', () => {
    render(<LoginPageClient />)

    const submitButton = screen.getByRole('button', { name: 'Login' })

    // Check for hover classes
    expect(submitButton.className).toMatch(/hover:|transition/)
  })

  it('should have active states on buttons', () => {
    render(<LoginPageClient />)

    const submitButton = screen.getByRole('button', { name: 'Login' })

    // Check for active classes
    expect(submitButton.className).toMatch(/active:|transition/)
  })

  it('should respect prefers-reduced-motion', () => {
    // Mock matchMedia for reduced motion
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation((query) => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    })

    render(<LoginPageClient />)

    const logo = screen.getByAltText('Dr. Muscle X')

    // Should have motion-reduce classes or no animation
    const headerContainer = logo.closest('.text-center')
    expect(headerContainer?.className).toMatch(/opacity-100/)
    // Check that there's no translate animation when motion is reduced
    expect(headerContainer?.className).not.toMatch(/translate/)
  })

  it('should have smooth transitions between states', async () => {
    render(<LoginPageClient />)

    // Check all interactive elements have transition classes
    const inputs = screen.getAllByRole('textbox')
    const submitButton = screen.getByRole('button', { name: 'Login' })

    inputs.forEach((input) => {
      expect(input.className).toMatch(/transition|duration/)
    })

    // Check submit button has transitions
    expect(submitButton.className).toMatch(/transition|duration/)
  })

  it('should have proper touch states on mobile', () => {
    render(<LoginPageClient />)

    const submitButton = screen.getByRole('button', { name: 'Login' })

    // Check for touch-friendly classes - the button has active: state and transform animation
    expect(submitButton.className).toMatch(/active:|transform|transition/)
  })

  it('should stagger form element animations', () => {
    render(<LoginPageClient />)

    // Get the form container which has animation delay
    const formContainer = screen.getByTestId('login-form').closest('.bg-white')

    // Check that the form container has transition delay style
    expect(formContainer).toBeTruthy()
    if (formContainer) {
      expect(formContainer.getAttribute('style')).toMatch(
        /transition-delay|delay/
      )
    }
  })
})
