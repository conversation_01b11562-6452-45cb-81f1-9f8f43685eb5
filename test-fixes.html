<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DrMuscle Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #2d5a2d; }
        .warning { background: #5a4d2d; }
        .error { background: #5a2d2d; }
        .info { background: #2d4d5a; }
        .counter {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
        }
        .log {
            background: #2a2a2a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3a8eef;
        }
        .test-section {
            border: 1px solid #444;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DrMuscle Fix Test Dashboard</h1>
        
        <div class="test-section">
            <h2>Console Message Monitor</h2>
            <div class="counter" id="messageCounter">Messages: 0</div>
            <div class="counter" id="errorCounter">Errors: 0</div>
            <div class="counter" id="warningCounter">Warnings: 0</div>
            <div class="counter" id="loopCounter">Potential Loops: 0</div>
            
            <button onclick="clearCounters()">Clear Counters</button>
            <button onclick="openApp()">Open DrMuscle App</button>
            <button onclick="testLogin()">Test Login Flow</button>
            
            <div class="log" id="consoleLog"></div>
        </div>

        <div class="test-section">
            <h2>Test Results</h2>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        let messageCount = 0;
        let errorCount = 0;
        let warningCount = 0;
        let loopCount = 0;
        let lastMessages = [];
        let loopDetectionMap = new Map();

        // Override console methods to monitor
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error,
            info: console.info
        };

        function logMessage(type, message, ...args) {
            messageCount++;
            
            const fullMessage = typeof message === 'string' ? message : JSON.stringify(message);
            const timestamp = new Date().toLocaleTimeString();
            
            // Detect potential infinite loops
            const messageKey = fullMessage.substring(0, 100);
            const now = Date.now();
            
            if (!loopDetectionMap.has(messageKey)) {
                loopDetectionMap.set(messageKey, []);
            }
            
            const times = loopDetectionMap.get(messageKey);
            times.push(now);
            
            // Keep only messages from last 10 seconds
            const recent = times.filter(time => now - time < 10000);
            loopDetectionMap.set(messageKey, recent);
            
            // If more than 20 similar messages in 10 seconds, it's likely a loop
            if (recent.length > 20) {
                loopCount++;
                updateCounters();
                addToLog('LOOP', `Potential infinite loop detected: ${messageKey}`, 'error');
            }
            
            if (type === 'error') errorCount++;
            if (type === 'warn') warningCount++;
            
            updateCounters();
            addToLog(type.toUpperCase(), fullMessage);
            
            // Call original console method
            originalConsole[type](message, ...args);
        }

        console.log = (...args) => logMessage('log', ...args);
        console.warn = (...args) => logMessage('warn', ...args);
        console.error = (...args) => logMessage('error', ...args);
        console.info = (...args) => logMessage('info', ...args);

        function updateCounters() {
            document.getElementById('messageCounter').textContent = `Messages: ${messageCount}`;
            document.getElementById('errorCounter').textContent = `Errors: ${errorCount}`;
            document.getElementById('warningCounter').textContent = `Warnings: ${warningCount}`;
            document.getElementById('loopCounter').textContent = `Potential Loops: ${loopCount}`;
        }

        function addToLog(type, message, className = '') {
            const log = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = className || type.toLowerCase();
            div.innerHTML = `<strong>[${timestamp}] ${type}:</strong> ${message}`;
            log.appendChild(div);
            log.scrollTop = log.scrollHeight;
        }

        function clearCounters() {
            messageCount = 0;
            errorCount = 0;
            warningCount = 0;
            loopCount = 0;
            loopDetectionMap.clear();
            updateCounters();
            document.getElementById('consoleLog').innerHTML = '';
            addToLog('INFO', 'Counters cleared');
        }

        function openApp() {
            addToLog('INFO', 'Opening DrMuscle app...');
            window.open('http://localhost:3000', '_blank');
        }

        function testLogin() {
            addToLog('INFO', 'Starting login test...');
            const testWindow = window.open('http://localhost:3000', '_blank');
            
            setTimeout(() => {
                addToLog('INFO', 'Test completed. Check console for results.');
                updateTestResults();
            }, 30000);
        }

        function updateTestResults() {
            const results = document.getElementById('testResults');
            const status = [];
            
            if (errorCount === 0) {
                status.push('<div class="status success">✅ No errors detected</div>');
            } else {
                status.push(`<div class="status error">❌ ${errorCount} errors detected</div>`);
            }
            
            if (loopCount === 0) {
                status.push('<div class="status success">✅ No infinite loops detected</div>');
            } else {
                status.push(`<div class="status error">❌ ${loopCount} potential infinite loops detected</div>`);
            }
            
            if (messageCount < 100) {
                status.push('<div class="status success">✅ Message count is reasonable</div>');
            } else if (messageCount < 1000) {
                status.push(`<div class="status warning">⚠️ High message count: ${messageCount}</div>`);
            } else {
                status.push(`<div class="status error">❌ Very high message count: ${messageCount}</div>`);
            }
            
            results.innerHTML = status.join('');
        }

        // Initial setup
        addToLog('INFO', 'DrMuscle Fix Test Dashboard initialized');
        addToLog('INFO', 'Monitoring console messages for infinite loops and errors...');
    </script>
</body>
</html>
