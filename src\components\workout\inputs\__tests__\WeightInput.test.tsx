import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { WeightInput } from '../WeightInput'

describe('WeightInput', () => {
  const defaultProps = {
    weight: 100,
    unit: 'lbs' as const,
    onChange: vi.fn(),
    onIncrement: vi.fn(),
    onDecrement: vi.fn(),
  }

  it('renders with theme-aware text colors for label', () => {
    render(<WeightInput {...defaultProps} />)

    const label = screen.getByText('Weight')
    expect(label).toHaveClass('text-text-secondary')
  })

  it('renders input with theme-aware text and border colors', () => {
    render(<WeightInput {...defaultProps} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('text-text-primary')
    expect(input).toHaveClass('border-text-tertiary')
    expect(input).toHaveClass('bg-bg-secondary')
  })

  it('renders unit text with theme-aware color', () => {
    render(<WeightInput {...defaultProps} />)

    const unitText = screen.getByText('lbs')
    expect(unitText).toHaveClass('text-text-tertiary')
  })

  it('renders buttons with theme-aware colors', () => {
    render(<WeightInput {...defaultProps} />)

    const buttons = screen.getAllByRole('button')
    buttons.forEach((button) => {
      expect(button).toHaveClass('bg-bg-secondary')
      expect(button).toHaveClass('text-text-primary')
      expect(button).toHaveClass('border-text-tertiary')
    })
  })

  it('maintains focus styles with theme colors', () => {
    render(<WeightInput {...defaultProps} />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    expect(input).toHaveClass('focus:ring-brand-primary')
  })

  it('shows error state with semantic error color', () => {
    render(<WeightInput {...defaultProps} error="Invalid weight" />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    const errorText = screen.getByRole('alert')

    expect(input).toHaveClass('border-error')
    expect(errorText).toHaveClass('text-error')
  })

  it('shows disabled state with theme-aware colors', () => {
    render(<WeightInput {...defaultProps} disabled />)

    const input = screen.getByRole('spinbutton', { name: 'Weight' })
    const buttons = screen.getAllByRole('button')

    expect(input).toHaveClass('bg-bg-tertiary')
    expect(input).toHaveClass('text-text-tertiary')

    buttons.forEach((button) => {
      expect(button).toHaveClass('bg-bg-tertiary')
      expect(button).toHaveClass('text-text-tertiary')
    })
  })

  it('renders alternative label for bodyweight exercises', () => {
    render(<WeightInput {...defaultProps} isBodyweight />)

    const label = screen.getByText('Additional Weight')
    expect(label).toHaveClass('text-text-secondary')
  })
})
