/**
 * Workout API Types
 *
 * Common types used across workout API operations
 */

import type { ExerciseModel, WorkoutTemplateModel, BooleanModel } from '@/types'

/**
 * Type for API response that may have either Exercises or Exercices field
 */
export type WorkoutTemplateApiResponse = Omit<
  WorkoutTemplateModel,
  'Exercises'
> & {
  Exercices?: ExerciseModel[]
  Exercises?: ExerciseModel[]
}

/**
 * Workout completion data
 */
export interface WorkoutCompletionData {
  WorkoutId: string
  StartTime: string
  EndTime: string
  TotalSets: number
  TotalExercises: number
  Notes?: string
}

/**
 * Workout completion response
 */
export interface WorkoutCompletionResponse extends BooleanModel {
  NextWorkoutDate?: string
}

/**
 * Exercise swap request
 */
export interface ExerciseSwapRequest {
  CurrentExerciseId: number
  NewExerciseId: number
}
