import { test, expect } from '@playwright/test'

test.describe('Workout Prefetch on Login', () => {
  test('should prefetch workout data during login success animation', async ({
    page,
  }) => {
    // Navigate to login page
    await page.goto('/login')

    // Fill in login credentials
    await page.fill('[name="username"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')

    // Set up request interception to monitor API calls
    const apiCalls: string[] = []
    page.on('request', (request) => {
      const url = request.url()
      if (url.includes('/api/')) {
        apiCalls.push(url)
      }
    })

    // Click login button
    await page.click('[type="submit"]')

    // Wait for the quick success screen
    await expect(
      page.locator('[data-testid="quick-success-screen"]')
    ).toBeVisible()

    // Wait a bit to ensure prefetch calls are made
    await page.waitForTimeout(500)

    // Verify that workout prefetch APIs were called
    const workoutProgramCall = apiCalls.some((url) =>
      url.includes('/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo')
    )
    const userWorkoutCall = apiCalls.some((url) =>
      url.includes('/api/WorkoutLog/GetUserWorkout')
    )

    expect(workoutProgramCall).toBe(true)
    expect(userWorkoutCall).toBe(true)

    // Wait for navigation to program page
    await page.waitForURL('/program')

    // Navigate to workout page
    await page.click('button:has-text("Continue to Workout")')

    // Verify workout page loads quickly (should be cached)
    await expect(page.locator('h1:has-text("Today\'s Workout")')).toBeVisible({
      timeout: 1000, // Should be fast since data is prefetched
    })

    // Check for the "Loaded" indicator
    await expect(page.locator('text="Loaded"').first()).toBeVisible()
  })

  test('should show green "Loaded" indicator when workout data is ready', async ({
    page,
  }) => {
    // Login first
    await page.goto('/login')
    await page.fill('[name="username"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('[type="submit"]')
    await page.waitForURL('/program')

    // Check for "Loaded" indicator on program page
    await expect(
      page.locator('.text-green-600:has-text("Loaded")')
    ).toBeVisible()

    // Navigate to workout page
    await page.click('button:has-text("Continue to Workout")')
    await page.waitForURL('/workout')

    // Check for "Loaded" indicator on workout page
    await expect(
      page.locator('.text-green-600:has-text("Loaded")')
    ).toBeVisible()
  })
})
