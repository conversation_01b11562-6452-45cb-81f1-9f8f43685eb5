import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import MockAdapter from 'axios-mock-adapter'
import { apiClient } from '@/api/client'
import { getExerciseSets, getTodaysWorkout } from '../read'
import type { WorkoutLogSerieModel } from '@/types'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import {
  getUserWorkoutProgramInfo,
  getWorkoutDetails,
} from '@/services/api/workout'

// Create mock adapter
const mock = new MockAdapter(apiClient)

// Mock the auth-utils module
vi.mock('@/lib/auth-utils', () => ({
  getCurrentUserEmail: vi.fn(),
}))

// Mock the workout service module
vi.mock('@/services/api/workout', () => ({
  getUserWorkoutProgramInfo: vi.fn(),
  getWorkoutDetails: vi.fn(),
}))

const mockedGetCurrentUserEmail = vi.mocked(getCurrentUserEmail)
const mockedGetUserWorkoutProgramInfo = vi.mocked(getUserWorkoutProgramInfo)
const mockedGetWorkoutDetails = vi.mocked(getWorkoutDetails)

// Mock data
const mockSets: WorkoutLogSerieModel[] = [
  {
    ExerciseId: 1,
    Weight: { Lb: 100, Kg: 45.36 },
    Reps: 8,
    RIR: 2,
    IsWarmups: false,
    IsNext: false,
    IsFinished: true,
  },
  {
    ExerciseId: 1,
    Weight: { Lb: 100, Kg: 45.36 },
    Reps: 7,
    RIR: 1,
    IsWarmups: false,
    IsNext: false,
    IsFinished: true,
  },
]

describe('Workout API - Read Operations', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    mock.reset()
  })

  describe('getExerciseSets', () => {
    it('should fetch exercise sets successfully when user is authenticated', async () => {
      // Given
      const exerciseId = 1
      const userEmail = '<EMAIL>'
      mockedGetCurrentUserEmail.mockReturnValue(userEmail)

      mock
        .onGet(`/api/WorkoutLog/GetExerciseSets/${userEmail}/${exerciseId}`)
        .reply(200, { Result: mockSets })

      // When
      const result = await getExerciseSets(exerciseId)

      // Then
      expect(result).toEqual(mockSets)
      expect(mockedGetCurrentUserEmail).toHaveBeenCalled()
    })

    it('should handle empty sets response', async () => {
      // Given
      const exerciseId = 2
      const userEmail = '<EMAIL>'
      mockedGetCurrentUserEmail.mockReturnValue(userEmail)

      mock
        .onGet(`/api/WorkoutLog/GetExerciseSets/${userEmail}/${exerciseId}`)
        .reply(200, { Result: [] })

      // When
      const result = await getExerciseSets(exerciseId)

      // Then
      expect(result).toEqual([])
    })

    it('should handle direct data format (not wrapped in Result)', async () => {
      // Given
      const exerciseId = 1
      const userEmail = '<EMAIL>'
      mockedGetCurrentUserEmail.mockReturnValue(userEmail)

      mock
        .onGet(`/api/WorkoutLog/GetExerciseSets/${userEmail}/${exerciseId}`)
        .reply(200, mockSets)

      // When
      const result = await getExerciseSets(exerciseId)

      // Then
      expect(result).toEqual(mockSets)
    })

    it('should return empty array when user is not authenticated', async () => {
      // Given
      const exerciseId = 1
      mockedGetCurrentUserEmail.mockReturnValue(null)

      // When
      const result = await getExerciseSets(exerciseId)

      // Then
      expect(result).toEqual([])
      expect(mock.history.get).toHaveLength(0) // No API call should be made
    })

    it('should handle API errors gracefully', async () => {
      // Given
      const exerciseId = 1
      const userEmail = '<EMAIL>'
      mockedGetCurrentUserEmail.mockReturnValue(userEmail)

      mock
        .onGet(`/api/WorkoutLog/GetExerciseSets/${userEmail}/${exerciseId}`)
        .reply(500, { error: 'Internal server error' })

      // When/Then
      await expect(getExerciseSets(exerciseId)).rejects.toThrow()
    })

    it('should handle non-array responses by returning empty array', async () => {
      // Given
      const exerciseId = 1
      const userEmail = '<EMAIL>'
      mockedGetCurrentUserEmail.mockReturnValue(userEmail)

      mock
        .onGet(`/api/WorkoutLog/GetExerciseSets/${userEmail}/${exerciseId}`)
        .reply(200, { Result: 'not an array' })

      // When
      const result = await getExerciseSets(exerciseId)

      // Then
      expect(result).toEqual([])
    })

    it('should handle Data wrapper format', async () => {
      // Given
      const exerciseId = 1
      const userEmail = '<EMAIL>'
      mockedGetCurrentUserEmail.mockReturnValue(userEmail)

      mock
        .onGet(`/api/WorkoutLog/GetExerciseSets/${userEmail}/${exerciseId}`)
        .reply(200, { Data: mockSets })

      // When
      const result = await getExerciseSets(exerciseId)

      // Then
      expect(result).toEqual(mockSets)
    })
  })

  describe('getTodaysWorkout - Mobile App Workflow', () => {
    beforeEach(() => {
      mockedGetCurrentUserEmail.mockReturnValue('<EMAIL>')
    })

    it('should follow mobile app workflow: GetUserWorkoutProgramTimeZoneInfo -> GetUserCustomizedCurrentWorkout', async () => {
      // Mock the response from GetUserWorkoutProgramTimeZoneInfo
      const mockProgramInfo = {
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: {
            Id: 4330,
            Label: 'Montréal',
            RemainingToLevelUp: 0,
          },
          NextWorkoutTemplate: {
            Id: 27792,
            Label: 'Montréal',
            IsSystemExercise: false,
            Exercises: null, // Note: exercises are null here, need to fetch from second endpoint
          },
        },
      }

      // Mock the response from GetUserCustomizedCurrentWorkout
      const mockWorkoutDetails = {
        Id: 27792,
        UserId: 'bcee85f1-b0a8-4281-aca5-0af3100d5541',
        Label: 'Montréal',
        IsSystemExercise: false,
        WorkoutSettingsModel: {},
        Exercises: [
          {
            Id: 27474,
            Label: 'Standing Shoulder Press (elbows out)',
            IsSystemExercise: false,
            BodyPartId: 2,
          },
          {
            Id: 3216,
            Label: 'Cable Curl',
            IsSystemExercise: true,
            BodyPartId: 5,
          },
        ],
      }

      mockedGetUserWorkoutProgramInfo.mockResolvedValue(mockProgramInfo as any)
      mockedGetWorkoutDetails.mockResolvedValue(mockWorkoutDetails as any)

      const result = await getTodaysWorkout()

      // Verify the correct API calls were made in order
      expect(mockedGetUserWorkoutProgramInfo).toHaveBeenCalledTimes(1)
      expect(mockedGetWorkoutDetails).toHaveBeenCalledWith(27792) // The workout ID from NextWorkoutTemplate

      // Verify the result structure
      expect(result).toHaveLength(1)
      expect(result[0]).toMatchObject({
        Label: 'Montréal',
        WorkoutTemplates: [mockWorkoutDetails],
      })
      expect(result[0].WorkoutTemplates[0].Exercises).toHaveLength(2)
    })

    it('should handle when GetUserWorkoutProgramTimeZoneInfo returns hasData: false but has workout ID', async () => {
      const mockProgramInfo = {
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: {
            Id: 4330,
            Label: 'Montréal',
          },
          NextWorkoutTemplate: {
            Id: 27792,
            Label: 'Montréal',
            IsSystemExercise: false,
          },
        },
      }

      const mockWorkoutDetails = {
        Id: 27792,
        Label: 'Montréal',
        Exercises: [{ Id: 1, Label: 'Test Exercise' }],
        IsSystemExercise: false,
        UserId: 'test-user',
        WorkoutSettingsModel: {},
      }

      mockedGetUserWorkoutProgramInfo.mockResolvedValue(mockProgramInfo as any)
      mockedGetWorkoutDetails.mockResolvedValue(mockWorkoutDetails as any)

      const result = await getTodaysWorkout()

      expect(result).toHaveLength(1)
      expect(result[0].WorkoutTemplates[0].Exercises).toHaveLength(1)
    })

    it('should handle when Exercises field is named "Exercices" (French locale)', async () => {
      const mockProgramInfo = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: {
            Id: 27792,
            Label: 'Montréal',
          },
        },
      }

      const mockWorkoutDetails = {
        Id: 27792,
        Label: 'Montréal',
        Exercices: [{ Id: 1, Label: 'Test Exercise' }], // Note: "Exercices" not "Exercises"
        IsSystemExercise: false,
        UserId: 'test-user',
        WorkoutSettingsModel: {},
      }

      mockedGetUserWorkoutProgramInfo.mockResolvedValue(mockProgramInfo as any)
      mockedGetWorkoutDetails.mockResolvedValue(mockWorkoutDetails as any)

      const result = await getTodaysWorkout()

      // Should normalize "Exercices" to "Exercises"
      expect(result[0].WorkoutTemplates[0].Exercises).toBeDefined()
      expect(result[0].WorkoutTemplates[0].Exercises).toHaveLength(1)
    })

    it('should fall back to GetUserWorkoutTemplateGroup when program info fails', async () => {
      mockedGetUserWorkoutProgramInfo.mockRejectedValue(new Error('API Error'))

      // Mock the fallback endpoint
      mock.onPost('/api/Workout/GetUserWorkoutTemplateGroup').reply(200, [
        {
          Id: 1,
          Label: 'Fallback Workout',
          WorkoutTemplates: [
            {
              Id: 123,
              Label: 'Workout A',
              Exercises: [{ Id: 1, Label: 'Exercise 1' }],
            },
          ],
        },
      ])

      const result = await getTodaysWorkout()

      expect(result).toHaveLength(1)
      expect(result[0].Label).toBe('Fallback Workout')
    })

    it('should return empty array when no workout is available', async () => {
      mockedGetUserWorkoutProgramInfo.mockResolvedValue({
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: null,
        },
      } as any)

      const result = await getTodaysWorkout()

      expect(result).toEqual([])
      expect(mockedGetWorkoutDetails).not.toHaveBeenCalled()
    })
  })
})
