import { describe, it, expect } from 'vitest'
import { formatLargeNumber } from '../formatLargeNumber'

describe('formatLargeNumber', () => {
  describe('small numbers (< 1,000)', () => {
    it('formats zero correctly', () => {
      expect(formatLargeNumber(0)).toBe('0')
    })

    it('formats small positive numbers', () => {
      expect(formatLargeNumber(1)).toBe('1')
      expect(formatLargeNumber(50)).toBe('50')
      expect(formatLargeNumber(999)).toBe('999')
    })

    it('formats small negative numbers', () => {
      expect(formatLargeNumber(-1)).toBe('-1')
      expect(formatLargeNumber(-50)).toBe('-50')
      expect(formatLargeNumber(-999)).toBe('-999')
    })

    it('handles null and undefined', () => {
      expect(formatLargeNumber(null as any)).toBe('0')
      expect(formatLargeNumber(undefined as any)).toBe('0')
    })
  })

  describe('thousands (1K - 999K)', () => {
    it('formats thousands with one decimal place', () => {
      expect(formatLargeNumber(1000)).toBe('1 K')
      expect(formatLargeNumber(1234)).toBe('1.2 K')
      expect(formatLargeNumber(1500)).toBe('1.5 K')
      expect(formatLargeNumber(1999)).toBe('2 K') // Rounds to 3 significant digits
    })

    it('formats larger thousands', () => {
      expect(formatLargeNumber(10000)).toBe('10 K')
      expect(formatLargeNumber(12345)).toBe('12.3 K')
      expect(formatLargeNumber(99999)).toBe('100 K') // Rounds up
      expect(formatLargeNumber(123456)).toBe('123 K')
    })

    it('formats negative thousands', () => {
      expect(formatLargeNumber(-1234)).toBe('-1.2 K')
      expect(formatLargeNumber(-12345)).toBe('-12.3 K')
      expect(formatLargeNumber(-123456)).toBe('-123 K')
    })

    it('removes trailing zeros', () => {
      expect(formatLargeNumber(1000)).toBe('1 K')
      expect(formatLargeNumber(10000)).toBe('10 K')
      expect(formatLargeNumber(100000)).toBe('100 K')
    })
  })

  describe('millions (1M - 999M)', () => {
    it('formats millions with up to two decimal places', () => {
      expect(formatLargeNumber(1000000)).toBe('1 M')
      expect(formatLargeNumber(1234567)).toBe('1.23 M')
      expect(formatLargeNumber(1500000)).toBe('1.5 M')
      expect(formatLargeNumber(1999999)).toBe('2 M') // Rounds to 3 significant digits
    })

    it('formats larger millions', () => {
      expect(formatLargeNumber(10000000)).toBe('10 M')
      expect(formatLargeNumber(12345678)).toBe('12.3 M')
      expect(formatLargeNumber(99999999)).toBe('100 M') // Rounds up
      expect(formatLargeNumber(123456789)).toBe('123 M')
    })

    it('formats negative millions', () => {
      expect(formatLargeNumber(-1234567)).toBe('-1.23 M')
      expect(formatLargeNumber(-12345678)).toBe('-12.3 M')
      expect(formatLargeNumber(-123456789)).toBe('-123 M')
    })

    it('removes trailing zeros', () => {
      expect(formatLargeNumber(1000000)).toBe('1 M')
      expect(formatLargeNumber(10000000)).toBe('10 M')
      expect(formatLargeNumber(100000000)).toBe('100 M')
    })
  })

  describe('billions (1B+)', () => {
    it('formats billions with up to three decimal places', () => {
      expect(formatLargeNumber(1000000000)).toBe('1 B')
      expect(formatLargeNumber(1234567890)).toBe('1.23 B') // Rounds to 3 significant digits
      expect(formatLargeNumber(1500000000)).toBe('1.5 B')
      expect(formatLargeNumber(1999999999)).toBe('2 B') // Rounds to 3 significant digits
    })

    it('formats larger billions', () => {
      expect(formatLargeNumber(10000000000)).toBe('10 B')
      expect(formatLargeNumber(12345678901)).toBe('12.3 B')
      expect(formatLargeNumber(99999999999)).toBe('100 B') // Rounds up
      expect(formatLargeNumber(123456789012)).toBe('123 B')
    })

    it('formats negative billions', () => {
      expect(formatLargeNumber(-1234567890)).toBe('-1.23 B')
      expect(formatLargeNumber(-12345678901)).toBe('-12.3 B')
      expect(formatLargeNumber(-123456789012)).toBe('-123 B')
    })

    it('removes trailing zeros', () => {
      expect(formatLargeNumber(1000000000)).toBe('1 B')
      expect(formatLargeNumber(10000000000)).toBe('10 B')
      expect(formatLargeNumber(100000000000)).toBe('100 B')
    })
  })

  describe('3 significant digits rounding', () => {
    it('rounds to 3 significant digits for all ranges', () => {
      // Thousands
      expect(formatLargeNumber(1234)).toBe('1.2 K') // 1230 → 1.23 K → 1.2 K (trailing zero removed)
      expect(formatLargeNumber(1235)).toBe('1.2 K') // 1240 → 1.24 K → 1.2 K
      expect(formatLargeNumber(1251)).toBe('1.3 K') // 1250 → 1.25 K → 1.3 K (rounded up)

      // Millions
      expect(formatLargeNumber(1234567)).toBe('1.23 M') // 1230000 → 1.23 M
      expect(formatLargeNumber(1235678)).toBe('1.24 M') // 1240000 → 1.24 M
      expect(formatLargeNumber(1236789)).toBe('1.24 M') // 1240000 → 1.24 M

      // Billions
      expect(formatLargeNumber(1234567890)).toBe('1.23 B') // 1230000000 → 1.23 B
      expect(formatLargeNumber(1235678901)).toBe('1.24 B') // 1240000000 → 1.24 B
    })
  })

  describe('real-world examples', () => {
    it('formats typical workout volumes', () => {
      expect(formatLargeNumber(385000)).toBe('385 K') // 385k kg lifted
      expect(formatLargeNumber(847605)).toBe('848 K') // ~848k lbs lifted
      expect(formatLargeNumber(1234567)).toBe('1.23 M') // 1.23M lbs lifted
      expect(formatLargeNumber(5432109)).toBe('5.43 M') // 5.43M lbs lifted
    })

    it('formats edge cases from mobile app', () => {
      // Based on mobile app examples
      expect(formatLargeNumber(875)).toBe('875')
      expect(formatLargeNumber(12345)).toBe('12.3 K')
      expect(formatLargeNumber(1234567)).toBe('1.23 M')
      expect(formatLargeNumber(1234567890)).toBe('1.23 B') // Rounds to 1.23
    })
  })
})
