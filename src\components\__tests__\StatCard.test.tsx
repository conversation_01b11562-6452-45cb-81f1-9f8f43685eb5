import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { StatCard } from '../StatCard'
import React from 'react'

describe('StatCard', () => {
  describe('Basic rendering', () => {
    it('should render with icon, label and value', () => {
      render(
        <StatCard
          icon={<span data-testid="test-icon">📊</span>}
          label="Total Workouts"
          value="15"
        />
      )

      expect(screen.getByTestId('test-icon')).toBeInTheDocument()
      expect(screen.getByText('Total Workouts')).toBeInTheDocument()
      expect(screen.getByText('15')).toBeInTheDocument()
    })

    it('should render with formatted value', () => {
      render(
        <StatCard
          icon={<span>📊</span>}
          label="Progress"
          value="75"
          format={(v) => `${v}%`}
        />
      )

      expect(screen.getByText('75%')).toBeInTheDocument()
    })

    it('should render loading skeleton when loading', () => {
      render(
        <StatCard
          icon={<span>📊</span>}
          label="Total Workouts"
          value="15"
          isLoading
        />
      )

      expect(screen.getByTestId('stat-card-skeleton')).toBeInTheDocument()
      expect(screen.queryByText('15')).not.toBeInTheDocument()
    })
  })

  describe('Trend indicator', () => {
    it('should show upward trend', () => {
      render(
        <StatCard
          icon={<span>📊</span>}
          label="Progress"
          value="75"
          trend="up"
        />
      )

      const trendIcon = screen.getByTestId('trend-icon')
      expect(trendIcon).toHaveClass('text-green-500')
    })

    it('should show downward trend', () => {
      render(
        <StatCard
          icon={<span>📊</span>}
          label="Missed Days"
          value="2"
          trend="down"
        />
      )

      const trendIcon = screen.getByTestId('trend-icon')
      expect(trendIcon).toHaveClass('text-red-500')
    })

    it('should not show trend when not provided', () => {
      render(<StatCard icon={<span>📊</span>} label="Current Week" value="3" />)

      expect(screen.queryByTestId('trend-icon')).not.toBeInTheDocument()
    })
  })

  describe('Styling', () => {
    it('should apply custom className', () => {
      render(
        <StatCard
          icon={<span>📊</span>}
          label="Test"
          value="1"
          className="custom-class"
        />
      )

      const card = screen.getByTestId('stat-card')
      expect(card).toHaveClass('custom-class')
    })

    it('should have proper mobile styling', () => {
      render(<StatCard icon={<span>📊</span>} label="Test" value="1" />)

      const card = screen.getByTestId('stat-card')
      expect(card).toHaveClass('min-h-[100px]') // Minimum height for touch targets
      expect(card).toHaveClass('p-4') // Proper padding
    })

    it('should support dark mode', () => {
      render(
        <div className="dark">
          <StatCard icon={<span>📊</span>} label="Test" value="1" />
        </div>
      )

      const card = screen.getByTestId('stat-card')
      expect(card).toHaveClass('dark:bg-gray-800')
    })
  })
})
