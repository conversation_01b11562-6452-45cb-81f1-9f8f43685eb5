# Dr. Muscle X Testing Patterns Reference

> TDD/BDD patterns and practices from the Dr. Muscle Watch app for web app development

## Overview

This document captures the proven testing patterns and methodologies used successfully in the Dr. Muscle Watch app development. These patterns ensure high code quality, prevent regressions, and provide clear acceptance criteria for features.

## Core Testing Philosophy

### Test-Driven Development (TDD) Cycle

1. **Red**: Write a failing test first
2. **Green**: Write the minimum code to make it pass
3. **Refactor**: Improve code while keeping tests green
4. **Documentation**: Update relevant docs

### Behavior-Driven Development (BDD) Format

All acceptance criteria follow the Given/When/Then pattern:

```
**Scenario:** [Descriptive scenario name]
  Given [initial context/state]
  When [action or trigger]
  Then [expected outcome]
  And [additional expectations]
```

## Testing Patterns from Watch App

### 1. Authentication Testing

**Example from Watch App (AUTH-01):**

```
**Scenario:** Successful Sign In (Existing Linked User)
  Given the native Apple Sign In sheet is presented
  When the user successfully authenticates with their Apple ID
  And the app receives a valid identity token and user identifier from Apple
  And the backend confirms this Apple ID *is* linked to an existing Dr. Muscle account
  Then the app securely stores the authentication state
  And the app transitions to the main authenticated view (Workout List)
```

**Web App Equivalent:**

```
**Scenario:** Successful Email/Password Login
  Given the user is on the login screen
  And they have entered valid email "<EMAIL>" and password "validPass123"
  When they tap the "Login" button
  And the API returns a successful authentication response with valid tokens
  Then the authentication tokens are securely stored
  And the user is redirected to today's workout screen
  And the auth state indicates the user is authenticated
```

### 2. API Integration Testing

**Example from Watch App (API-01):**

```
**Scenario:** Injecting Auth Token into Requests
  Given the user is authenticated
  And the app has a valid authentication token
  When the API Client prepares to send a request to a protected endpoint
  Then the request includes the necessary Authorization header (e.g., `Authorization: Bearer <token>`)
```

**Web App Pattern:**

```typescript
// Test Example
describe('API Client', () => {
  it('should include auth token in protected requests', async () => {
    // Given
    const mockToken = 'valid-jwt-token'
    authStore.setToken(mockToken)

    // When
    const request = apiClient.getWorkouts()

    // Then
    expect(mockAxios.lastReqGet()).toHaveProperty(
      'headers.Authorization',
      `Bearer ${mockToken}`
    )
  })
})
```

### 3. UI Component Testing

**Example from Watch App (WKOUT-EXEC-01):**

```
**Scenario:** Display First Set Details
  Given the user has started a workout and the plan is stored locally
  And the app has navigated to the Set Screen for the first set of the first exercise (e.g., Bench Press, Target: 8 reps, 100 lbs)
  When the Set Screen appears
  Then the screen displays the current Exercise Name ("Bench Press")
  And the screen displays the Target Reps ("8")
  And the screen displays the Target Weight ("100")
  And the screen displays the Weight Unit ("lbs" or "kg", based on user settings/API data)
```

**Web App Component Test:**

```typescript
describe('SetScreen Component', () => {
  it('should display exercise details correctly', () => {
    // Given
    const mockExercise = {
      name: 'Bench Press',
      targetReps: 8,
      targetWeight: { value: 100, unit: 'lbs' }
    };

    // When
    render(<SetScreen exercise={mockExercise} />);

    // Then
    expect(screen.getByText('Bench Press')).toBeInTheDocument();
    expect(screen.getByText('8')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
    expect(screen.getByText('lbs')).toBeInTheDocument();
  });
});
```

### 4. User Interaction Testing

**Example from Watch App (INPUT-01):**

```
**Scenario:** Select New Reps Value
  Given the Reps Picker sheet is presented
  And the current value is "8"
  When the user scrolls and taps on a different value (e.g., "10")
  Then the modal sheet is dismissed
  And the Set Screen updates to display the newly selected reps value ("10")
  And the underlying state holding the reps for the current set is updated to 10
```

**Web App Interaction Test:**

```typescript
describe('Reps Input', () => {
  it('should update reps when user selects new value', async () => {
    // Given
    render(<SetScreen />);
    const repsInput = screen.getByTestId('reps-input');

    // When
    await user.click(repsInput);
    const newValue = screen.getByText('10');
    await user.click(newValue);

    // Then
    expect(screen.getByDisplayValue('10')).toBeInTheDocument();
    expect(mockSetState).toHaveBeenCalledWith(expect.objectContaining({ reps: 10 }));
  });
});
```

### 5. Error Handling Testing

**Example from Watch App (AUTH-01):**

```
**Scenario:** Sign In Fails (Backend Error)
  Given the user successfully authenticates with Apple
  And the app sends the token to the backend for validation/linking
  When the backend returns an error (e.g., server unavailable, validation failed)
  Then an appropriate error message is displayed to the user (e.g., "Could not connect to Dr. Muscle. Please try again.")
  And the user remains on the initial view with the "Sign in with Apple" button visible
  And no authentication state is stored
```

**Web App Error Test:**

```typescript
describe('Login Error Handling', () => {
  it('should handle backend errors gracefully', async () => {
    // Given
    mockApi.post.mockRejectedValueOnce(new Error('Server unavailable'));
    render(<LoginForm />);

    // When
    await user.type(screen.getByLabelText('Email'), '<EMAIL>');
    await user.type(screen.getByLabelText('Password'), 'password123');
    await user.click(screen.getByText('Login'));

    // Then
    expect(screen.getByText('Could not connect to Dr. Muscle. Please try again.')).toBeInTheDocument();
    expect(screen.getByText('Login')).toBeInTheDocument(); // Button still visible
    expect(authStore.isAuthenticated()).toBe(false);
  });
});
```

## Testing Architecture for Web App

### Unit Tests (Vitest + React Testing Library)

**File Structure:**

```
tests/unit/
├── components/
│   ├── SetScreen.test.tsx
│   ├── LoginForm.test.tsx
│   └── WorkoutList.test.tsx
├── hooks/
│   ├── useAuth.test.ts
│   └── useWorkout.test.ts
├── stores/
│   ├── authStore.test.ts
│   └── workoutStore.test.ts
└── utils/
    ├── apiClient.test.ts
    └── formatters.test.ts
```

**Test Template:**

```typescript
describe('[Component/Function Name]', () => {
  beforeEach(() => {
    // Setup common test state
  })

  describe('when [condition]', () => {
    it('should [expected behavior]', () => {
      // Given
      // When
      // Then
    })
  })
})
```

### Integration Tests

**API Integration:**

```typescript
describe('Workout API Integration', () => {
  it('should fetch and display today\'s workout', async () => {
    // Given
    mockServer.use(
      rest.get('/api/Workout/GetUserWorkoutTemplateGroup', (req, res, ctx) => {
        return res(ctx.json({ Data: mockWorkoutData }));
      })
    );

    // When
    render(<WorkoutScreen />);

    // Then
    await waitFor(() => {
      expect(screen.getByText('Today\'s Workout')).toBeInTheDocument();
    });
  });
});
```

### End-to-End Tests (Playwright)

**Complete User Journey:**

```typescript
test('complete workout flow', async ({ page }) => {
  // Given - User is logged in
  await page.goto('/login')
  await page.fill('[data-testid="email"]', '<EMAIL>')
  await page.fill('[data-testid="password"]', 'password123')
  await page.click('[data-testid="login-button"]')

  // When - User completes a workout
  await page.waitForSelector('[data-testid="start-workout"]')
  await page.click('[data-testid="start-workout"]')

  // Complete first set
  await page.fill('[data-testid="reps-input"]', '10')
  await page.fill('[data-testid="weight-input"]', '100')
  await page.click('[data-testid="save-set"]')

  // Then - Workout is saved successfully
  await expect(page.locator('[data-testid="workout-completed"]')).toBeVisible()
})
```

## Quality Gates

### Coverage Requirements

- **Unit Tests**: 90%+ coverage on business logic
- **Integration Tests**: All API endpoints covered
- **E2E Tests**: Critical user paths covered

### Performance Testing

```typescript
describe('Performance', () => {
  it('should load workout screen in under 1 second', async () => {
    const startTime = performance.now();

    render(<WorkoutScreen />);
    await waitFor(() => screen.getByText('Today\'s Workout'));

    const loadTime = performance.now() - startTime;
    expect(loadTime).toBeLessThan(1000);
  });
});
```

### Accessibility Testing

```typescript
describe('Accessibility', () => {
  it('should be accessible to screen readers', () => {
    render(<SetScreen />);

    expect(screen.getByLabelText('Exercise name')).toBeInTheDocument();
    expect(screen.getByLabelText('Target reps')).toBeInTheDocument();
    expect(screen.getByLabelText('Target weight')).toBeInTheDocument();
  });
});
```

## Test Data Management

### Mock Data Factory

```typescript
export const createMockWorkout = (overrides = {}) => ({
  id: 1,
  name: 'Push Day',
  exercises: [
    {
      id: 1,
      name: 'Bench Press',
      targetReps: 8,
      targetWeight: { value: 100, unit: 'lbs' },
    },
  ],
  ...overrides,
})
```

### API Mocking with MSW

```typescript
export const handlers = [
  rest.get('/api/Workout/GetUserWorkoutTemplateGroup', (req, res, ctx) => {
    return res(ctx.json({ Data: [createMockWorkout()] }))
  }),
]
```

## Continuous Integration

### Pre-commit Hooks

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": ["eslint --fix", "prettier --write"],
    "*.test.{ts,tsx}": ["vitest run --passWithNoTests"]
  }
}
```

### CI Pipeline

1. **Lint**: ESLint + Prettier
2. **Type Check**: TypeScript compilation
3. **Unit Tests**: Vitest with coverage
4. **Integration Tests**: API integration tests
5. **E2E Tests**: Playwright (critical paths only)
6. **Performance**: Lighthouse CI
7. **Deploy**: Vercel preview deployment

---

## Mobile-Specific Testing Patterns

### Touch Interaction Testing

```typescript
describe('Mobile Touch Interactions', () => {
  it('should handle touch events with proper feedback', async () => {
    // Given
    render(<StartWorkoutButton />);
    const button = screen.getByRole('button', { name: 'Start Workout' });

    // When
    await user.pointer([
      { keys: '[TouchA>]', target: button, coords: { x: 0, y: 0 } },
      { keys: '[/TouchA]' }
    ]);

    // Then
    expect(mockHapticFeedback).toHaveBeenCalled();
    expect(mockStartWorkout).toHaveBeenCalled();
  });
});
```

### Mobile Viewport Testing

```typescript
describe('Mobile Responsive Design', () => {
  beforeEach(() => {
    // Set mobile viewport
    global.innerWidth = 375;
    global.innerHeight = 667;
    global.dispatchEvent(new Event('resize'));
  });

  it('should display mobile-optimized layout', () => {
    render(<WorkoutScreen />);

    // Then
    expect(screen.getByTestId('mobile-layout')).toBeInTheDocument();
    expect(screen.getByTestId('start-workout-button')).toHaveStyle('width: 100%');
  });
});
```

### PWA Feature Testing

```typescript
describe('PWA Installation', () => {
  it('should prompt for installation on mobile', async () => {
    // Given
    const mockInstallPrompt = jest.fn();
    window.addEventListener('beforeinstallprompt', mockInstallPrompt);

    // When
    render(<App />);

    // Then
    await waitFor(() => {
      expect(screen.getByText('Install App')).toBeInTheDocument();
    });
  });
});
```

---

_These patterns are proven from the successful Dr. Muscle Watch app development and adapted for mobile-first web app development._
