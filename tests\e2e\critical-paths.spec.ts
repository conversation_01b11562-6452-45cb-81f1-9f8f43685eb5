import { test, expect } from '@playwright/test'

test.describe('Critical User Paths @critical', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('Homepage loads successfully @critical', async ({ page }) => {
    // Verify page loads
    await expect(page).toHaveTitle(/Dr.*Muscle/i)
    
    // Verify core elements are visible
    const mainContent = page.locator('main')
    await expect(mainContent).toBeVisible()
    
    // Check for proper mobile viewport
    const viewport = page.viewportSize()
    expect(viewport?.width).toBeLessThanOrEqual(430)
    expect(viewport?.width).toBeGreaterThanOrEqual(320)
  })

  test('Login flow works correctly @critical', async ({ page }) => {
    // Navigate to login
    const loginLink = page.getByRole('link', { name: /log in|sign in/i }).or(
      page.getByRole('button', { name: /log in|sign in/i })
    )
    
    if (await loginLink.isVisible()) {
      await loginLink.click()
      await expect(page).toHaveURL(/login|signin|auth/i)
      
      // Check login form elements
      const emailInput = page.locator('input[type="email"], input[name*="email"], input[id*="email"]')
      const passwordInput = page.locator('input[type="password"]')
      
      await expect(emailInput).toBeVisible()
      await expect(passwordInput).toBeVisible()
    }
  })

  test('Workout creation flow @critical', async ({ page }) => {
    // This test would need to be expanded based on actual app structure
    // Look for workout-related UI elements
    const workoutButton = page.getByRole('button', { name: /workout|start|new/i }).or(
      page.getByRole('link', { name: /workout|start|new/i })
    )
    
    if (await workoutButton.isVisible({ timeout: 5000 })) {
      await workoutButton.click()
      
      // Verify navigation to workout page
      await expect(page).toHaveURL(/workout|exercise|program/i)
    }
  })

  test('Mobile touch targets meet 44px requirement @critical', async ({ page }) => {
    // Get all interactive elements
    const interactiveElements = page.locator('button, a, input, select, textarea, [role="button"]')
    const count = await interactiveElements.count()
    
    let tooSmallTargets = 0
    const minSize = 44 // Minimum touch target size in pixels
    
    for (let i = 0; i < Math.min(count, 20); i++) {
      const element = interactiveElements.nth(i)
      if (await element.isVisible()) {
        const box = await element.boundingBox()
        if (box && (box.width < minSize || box.height < minSize)) {
          tooSmallTargets++
          console.log(`Touch target too small: ${box.width}x${box.height}px`)
        }
      }
    }
    
    // Allow up to 10% of elements to be smaller (for icons with larger click areas)
    const threshold = Math.ceil(count * 0.1)
    expect(tooSmallTargets).toBeLessThanOrEqual(threshold)
  })

  test('PWA features work correctly @critical', async ({ page, context }) => {
    // Check manifest
    const manifestResponse = await page.goto('/manifest.json')
    expect(manifestResponse?.status()).toBe(200)
    
    // Go back to homepage
    await page.goto('/')
    
    // Test offline capability
    await context.setOffline(true)
    
    // Try to reload
    const reloadPromise = page.reload().catch(() => null)
    await reloadPromise
    
    // Check if service worker provides offline content
    const hasContent = await page.locator('body').isVisible({ timeout: 5000 }).catch(() => false)
    expect(hasContent).toBeTruthy()
    
    // Go back online
    await context.setOffline(false)
  })

  test('Critical performance metrics @critical', async ({ page }) => {
    // Measure page load performance
    const metrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
      }
    })
    
    // Performance assertions
    expect(metrics.firstContentfulPaint).toBeLessThan(1000) // FCP < 1s
    expect(metrics.domContentLoaded).toBeLessThan(1500) // DOM ready < 1.5s
  })

  test('User can navigate main sections @critical', async ({ page }) => {
    // Test navigation to key sections
    const navigationLinks = page.locator('nav a, [role="navigation"] a')
    const linkCount = await navigationLinks.count()
    
    if (linkCount > 0) {
      // Click first navigation link
      const firstLink = navigationLinks.first()
      const linkText = await firstLink.textContent()
      
      if (linkText) {
        await firstLink.click()
        
        // Verify navigation occurred
        await expect(page).not.toHaveURL('/')
        
        // Verify page loaded
        await expect(page.locator('main')).toBeVisible()
      }
    }
  })

  test('Forms have proper validation @critical', async ({ page }) => {
    // Navigate to a page with a form (login/signup)
    const formLink = page.getByRole('link', { name: /sign up|register|join/i }).or(
      page.getByRole('button', { name: /sign up|register|join/i })
    )
    
    if (await formLink.isVisible({ timeout: 5000 })) {
      await formLink.click()
      
      // Find form
      const form = page.locator('form')
      if (await form.isVisible()) {
        // Try to submit empty form
        const submitButton = form.locator('button[type="submit"], input[type="submit"]')
        if (await submitButton.isVisible()) {
          await submitButton.click()
          
          // Check for validation messages
          const errorMessages = page.locator('[aria-invalid="true"], .error, .invalid, [role="alert"]')
          await expect(errorMessages.first()).toBeVisible({ timeout: 3000 })
        }
      }
    }
  })
})

test.describe('Mobile-specific critical paths @critical @mobile', () => {
  test.use({
    viewport: { width: 375, height: 667 }, // iPhone SE dimensions
    hasTouch: true,
    isMobile: true,
  })

  test('Swipe gestures work on mobile @critical @mobile', async ({ page }) => {
    await page.goto('/')
    
    // Test horizontal swipe
    await page.locator('body').dispatchEvent('touchstart', {
      touches: [{ clientX: 300, clientY: 400 }],
    })
    
    await page.locator('body').dispatchEvent('touchmove', {
      touches: [{ clientX: 100, clientY: 400 }],
    })
    
    await page.locator('body').dispatchEvent('touchend')
    
    // Verify the page is still responsive after gesture
    await expect(page.locator('main')).toBeVisible()
  })

  test('Mobile menu works correctly @critical @mobile', async ({ page }) => {
    await page.goto('/')
    
    // Look for mobile menu button
    const menuButton = page.locator('[aria-label*="menu"], [class*="menu"], button:has-text("Menu")')
    
    if (await menuButton.isVisible({ timeout: 3000 })) {
      await menuButton.click()
      
      // Check if menu opened
      const mobileNav = page.locator('[role="navigation"], nav').filter({ hasText: /home|about|contact/i })
      await expect(mobileNav).toBeVisible()
    }
  })
})