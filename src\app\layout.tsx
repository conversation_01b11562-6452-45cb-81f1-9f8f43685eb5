import type { <PERSON>ada<PERSON> } from 'next'
import { Inter, Space_Grotesk } from 'next/font/google'
import '../styles/globals.css'
import { Providers } from '@/components/Providers'
import { SyncIndicator } from '@/components/SyncIndicator'
import { DebugToggle } from '@/components/DebugToggle'
import { FontLoadingMonitor } from '@/components/FontLoadingMonitor'
import { NavigationWrapper } from '@/components/NavigationWrapper'
import { ThemeInitScript } from './theme-init'

// Import debug scripts in development
if (process.env.NODE_ENV === 'development') {
  import('@/debug/testRecommendationAPI')
}

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  preload: true,
  fallback: [
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'sans-serif',
  ],
})

const spaceGrotesk = Space_Grotesk({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-heading',
  weight: ['400', '500', '600', '700'],
  preload: true,
  fallback: [
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'sans-serif',
  ],
})

export const metadata: Metadata = {
  title: "Dr. Muscle X - World's Fastest AI Personal Trainer",
  description:
    "World's Fastest AI Personal Trainer for optimal muscle building and strength training. The mobile-first PWA that gets out of your way.",
  metadataBase: new URL('https://web.drmuscle.com'),
  manifest: '/manifest.json',
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/icons/icon-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/icons/icon-512x512.png', sizes: '512x512', type: 'image/png' },
    ],
    apple: '/apple-touch-icon.png',
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Dr. Muscle X',
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    title: "Dr. Muscle X - World's Fastest AI Personal Trainer",
    description:
      "World's Fastest AI Personal Trainer for optimal muscle building and strength training",
    type: 'website',
    locale: 'en_US',
    siteName: 'Dr. Muscle X',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Dr. Muscle X - World's Fastest AI Personal Trainer",
    description:
      "World's Fastest AI Personal Trainer for optimal muscle building and strength training",
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#0a0a0b', // subtle-depth primary background
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${spaceGrotesk.variable}`}>
      <head>
        <ThemeInitScript />
      </head>
      <body className={inter.className}>
        <FontLoadingMonitor />
        <Providers>
          <NavigationWrapper>{children}</NavigationWrapper>
          <SyncIndicator />
          <DebugToggle />
        </Providers>
      </body>
    </html>
  )
}
