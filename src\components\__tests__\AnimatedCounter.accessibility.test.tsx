import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { AnimatedCounter } from '../AnimatedCounter'

// Mock requestAnimationFrame
beforeEach(() => {
  vi.stubGlobal(
    'requestAnimationFrame',
    vi.fn((cb) => setTimeout(() => cb(Date.now()), 16))
  )
  vi.stubGlobal(
    'cancelAnimationFrame',
    vi.fn((id) => clearTimeout(id))
  )
})

describe('AnimatedCounter - Accessibility', () => {
  describe('ARIA attributes', () => {
    it('should have proper ARIA attributes for main content', () => {
      render(<AnimatedCounter targetValue={100} label="Test Counter" />)

      const container = screen.getByTestId('animated-counter')
      expect(container).toHaveAttribute('role', 'group')
      expect(container).toHaveAttribute('aria-label', 'Test Counter')
    })

    it('should use generic label when no label provided', () => {
      render(<AnimatedCounter targetValue={100} />)

      const container = screen.getByTestId('animated-counter')
      expect(container).toHaveAttribute('aria-label', 'Counter')
    })

    it('should have live region for value updates', () => {
      render(<AnimatedCounter targetValue={100} label="Score" />)

      const value = screen.getByTestId('animated-counter-value')
      expect(value).toHaveAttribute('aria-atomic', 'true')
      expect(value).toHaveAttribute('aria-relevant', 'text')
      expect(value).toHaveAttribute('aria-live', 'polite')
      expect(value).toHaveAttribute('aria-label', 'Score: 0')
    })

    it('should disable live region when shimmer is showing', () => {
      render(
        <AnimatedCounter targetValue={0} showShimmer label="Loading Value" />
      )

      const value = screen.getByTestId('animated-counter-value')
      expect(value).toHaveAttribute('aria-live', 'off')
    })
  })

  describe('Loading states', () => {
    it('should have proper ARIA attributes for loading skeleton', () => {
      render(<AnimatedCounter targetValue={100} isLoading label="Test Value" />)

      const container = screen.getByTestId('animated-counter')
      expect(container).toHaveAttribute('role', 'status')
      expect(container).toHaveAttribute('aria-busy', 'true')
      expect(container).toHaveAttribute('aria-label', 'Loading Test Value')

      // Check for screen reader text
      expect(screen.getByText('Loading Test Value')).toHaveClass('sr-only')
    })

    it('should hide visual loading elements from screen readers', () => {
      render(<AnimatedCounter targetValue={100} isLoading label="Test" />)

      const shimmer = screen.getByTestId('shimmer-overlay')
      expect(shimmer).toHaveAttribute('aria-hidden', 'true')
    })
  })

  describe('Shimmer overlay accessibility', () => {
    it('should hide shimmer overlay from screen readers', () => {
      render(<AnimatedCounter targetValue={0} showShimmer />)

      const overlay = screen.getByTestId('shimmer-overlay')
      expect(overlay).toHaveAttribute('aria-hidden', 'true')
    })

    it('should include loading status in shimmer', () => {
      render(<AnimatedCounter targetValue={0} showShimmer />)

      // The shimmer status is hidden from screen readers because it's inside aria-hidden
      // But we can check that it exists in the DOM
      const shimmerStatus = screen.getByRole('status', { hidden: true })
      expect(shimmerStatus).toHaveAttribute('aria-label', 'Loading value')
      expect(shimmerStatus).toHaveAttribute('aria-busy', 'true')
    })
  })

  describe('Reduced motion preference', () => {
    it('should respect prefers-reduced-motion', () => {
      // Mock matchMedia
      const mockMatchMedia = vi.fn().mockReturnValue({
        matches: true,
        media: '(prefers-reduced-motion: reduce)',
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      })
      vi.stubGlobal('matchMedia', mockMatchMedia)

      const { rerender } = render(<AnimatedCounter targetValue={0} />)

      // Update value
      rerender(<AnimatedCounter targetValue={1000} />)

      // Should update immediately without animation
      const value = screen.getByTestId('animated-counter-value')
      expect(value).toHaveTextContent('1,000')
    })
  })

  describe('Dynamic label updates', () => {
    it('should update aria-label when value changes', async () => {
      const { rerender } = render(
        <AnimatedCounter targetValue={0} label="Points" />
      )

      const value = screen.getByTestId('animated-counter-value')
      expect(value).toHaveAttribute('aria-label', 'Points: 0')

      rerender(<AnimatedCounter targetValue={500} label="Points" />)

      await waitFor(() => {
        expect(value).toHaveAttribute(
          'aria-label',
          expect.stringMatching(/Points: \d+/)
        )
      })
    })
  })

  describe('Label association', () => {
    it('should create unique id for label element', () => {
      render(<AnimatedCounter targetValue={100} label="Week Streak" />)

      const labelElement = screen.getByText('Week Streak')
      expect(labelElement).toHaveAttribute('id', 'counter-label-week-streak')
    })

    it('should handle label with special characters', () => {
      render(<AnimatedCounter targetValue={100} label="Total Volume (lbs)" />)

      const labelElement = screen.getByText('Total Volume (lbs)')
      expect(labelElement).toHaveAttribute(
        'id',
        'counter-label-total-volume-(lbs)'
      )
    })
  })
})
