import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { WorkoutErrorBoundary } from '../WorkoutErrorBoundary'
import { WorkoutOverview } from '@/components/workout/WorkoutOverview'
import * as useWorkoutModule from '@/hooks/useWorkout'
import type { UseWorkoutReturn } from '@/hooks/useWorkout'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'

// Mock console.error to avoid test noise
vi.spyOn(console, 'error').mockImplementation(() => {})

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

describe('WorkoutErrorBoundary Integration', () => {
  const createQueryClient = () =>
    new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })

  it('should catch errors from WorkoutOverview and show fallback UI', () => {
    // Mock useWorkout to throw an error
    vi.spyOn(useWorkoutModule, 'useWorkout').mockImplementation(() => {
      throw new Error('Failed to load workout data')
    })

    render(
      <QueryClientProvider client={createQueryClient()}>
        <WorkoutErrorBoundary>
          <WorkoutOverview />
        </WorkoutErrorBoundary>
      </QueryClientProvider>
    )

    expect(screen.getByText('Workout Error')).toBeInTheDocument()
    expect(screen.getByTestId('retry-workout-button')).toBeInTheDocument()
  })

  it('should recover when retry is successful', async () => {
    // Start with error
    const mockUseWorkout = vi
      .spyOn(useWorkoutModule, 'useWorkout')
      .mockImplementation(() => {
        throw new Error('Initial error')
      })

    const { rerender } = render(
      <QueryClientProvider client={createQueryClient()}>
        <WorkoutErrorBoundary>
          <WorkoutOverview />
        </WorkoutErrorBoundary>
      </QueryClientProvider>
    )

    expect(screen.getByText('Workout Error')).toBeInTheDocument()

    // Fix the error before retry
    mockUseWorkout.mockReturnValue({
      todaysWorkout: null,
      userProgramInfo: null,
      exercises: [],
      currentWorkout: null,
      currentExercise: null,
      nextExercise: null,
      currentSetIndex: 0,
      totalSets: 0,
      isLastSet: false,
      isLastExercise: false,
      isWarmupSet: false,
      workoutSession: null,
      isLoading: false,
      isLoadingWorkout: false,
      hasInitialData: false,
      isLoadingFresh: false,
      loadingStates: {
        programInfo: false,
        userWorkout: false,
        recommendation: false,
      },
      workoutError: null,
      error: null,
      recommendation: null,
      refetchRecommendation: vi.fn(),
      saveSet: vi.fn(),
      startWorkout: vi.fn(),
      finishWorkout: vi.fn(),
      nextSet: vi.fn(),
      restoreWorkout: vi.fn(),
      goToNextExercise: vi.fn(),
      getRecommendation: vi.fn(),
      getRestDuration: vi.fn(),
      isOffline: false,
      expectedExerciseCount: undefined,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    } as UseWorkoutReturn)

    // Click retry
    fireEvent.click(screen.getByTestId('retry-workout-button'))

    // Rerender to trigger recovery
    rerender(
      <QueryClientProvider client={createQueryClient()}>
        <WorkoutErrorBoundary>
          <WorkoutOverview />
        </WorkoutErrorBoundary>
      </QueryClientProvider>
    )

    await waitFor(() => {
      expect(screen.queryByText('Workout Error')).not.toBeInTheDocument()
      expect(screen.getByText('No Workout Available')).toBeInTheDocument()
    })
  })

  it('should show network-specific error for network failures', () => {
    vi.spyOn(useWorkoutModule, 'useWorkout').mockImplementation(() => {
      throw new Error('Network request failed')
    })

    render(
      <QueryClientProvider client={createQueryClient()}>
        <WorkoutErrorBoundary>
          <WorkoutOverview />
        </WorkoutErrorBoundary>
      </QueryClientProvider>
    )

    expect(screen.getByText('Connection Problem')).toBeInTheDocument()
    expect(
      screen.getByText(/check your internet connection/)
    ).toBeInTheDocument()
  })

  it('should show authentication error for 401 errors', () => {
    vi.spyOn(useWorkoutModule, 'useWorkout').mockImplementation(() => {
      throw new Error('API Error: 401 Unauthorized')
    })

    render(
      <QueryClientProvider client={createQueryClient()}>
        <WorkoutErrorBoundary>
          <WorkoutOverview />
        </WorkoutErrorBoundary>
      </QueryClientProvider>
    )

    expect(
      screen.getByText(/Your session may have expired/)
    ).toBeInTheDocument()
    expect(screen.getByTestId('login-button')).toBeInTheDocument()
  })

  it('should not catch async errors (React limitation)', async () => {
    function AsyncErrorComponent() {
      const [hasError, setHasError] = React.useState(false)

      React.useEffect(() => {
        // This async error won't be caught by the error boundary
        setTimeout(() => {
          try {
            throw new Error('Async error')
          } catch (e) {
            // In real apps, you'd handle this differently
            setHasError(true)
          }
        }, 0)
      }, [])

      if (hasError) {
        // This synchronous throw WILL be caught
        throw new Error('Sync error from async operation')
      }

      return <div>Loading...</div>
    }

    // Test shows that async errors need manual handling
    render(
      <WorkoutErrorBoundary>
        <AsyncErrorComponent />
      </WorkoutErrorBoundary>
    )

    // Initially renders loading
    expect(screen.getByText('Loading...')).toBeInTheDocument()

    // After async error and re-render, error boundary catches it
    await waitFor(() => {
      expect(screen.getByText('Workout Error')).toBeInTheDocument()
    })
  })

  it('should pass through successful workout rendering', async () => {
    const mockWorkoutGroup = {
      Id: 1,
      Label: 'Test Program',
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Upper Body Day',
          IsSystemExercise: true,
          UserId: '',
          Exercises: [],
          WorkoutSettingsModel: {},
        },
      ],
      IsFeaturedProgram: false,
      UserId: '',
      IsSystemExercise: true,
      RequiredWorkoutToLevelUp: 5,
      ProgramId: 1,
    }

    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue({
      todaysWorkout: [mockWorkoutGroup],
      userProgramInfo: null,
      exercises: [],
      currentWorkout: null,
      currentExercise: null,
      nextExercise: null,
      currentSetIndex: 0,
      totalSets: 0,
      isLastSet: false,
      isLastExercise: false,
      isWarmupSet: false,
      workoutSession: null,
      isLoading: false,
      isLoadingWorkout: false,
      hasInitialData: true,
      isLoadingFresh: false,
      loadingStates: {
        programInfo: false,
        userWorkout: false,
        recommendation: false,
      },
      workoutError: null,
      error: null,
      recommendation: null,
      refetchRecommendation: vi.fn(),
      saveSet: vi.fn(),
      startWorkout: vi.fn(),
      finishWorkout: vi.fn(),
      nextSet: vi.fn(),
      restoreWorkout: vi.fn(),
      goToNextExercise: vi.fn(),
      getRecommendation: vi.fn(),
      getRestDuration: vi.fn(),
      isOffline: false,
      expectedExerciseCount: 0,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
    } as UseWorkoutReturn)

    render(
      <QueryClientProvider client={createQueryClient()}>
        <WorkoutErrorBoundary>
          <WorkoutOverview />
        </WorkoutErrorBoundary>
      </QueryClientProvider>
    )

    // Should render workout content, not error
    expect(screen.getByText('Start Workout')).toBeInTheDocument()
    expect(screen.queryByText('Workout Error')).not.toBeInTheDocument()
  })

  it('should log errors in production mode', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'production'

    const consoleErrorSpy = vi
      .spyOn(console, 'error')
      .mockImplementation(() => {})

    vi.spyOn(useWorkoutModule, 'useWorkout').mockImplementation(() => {
      throw new Error('Production error')
    })

    render(
      <QueryClientProvider client={createQueryClient()}>
        <WorkoutErrorBoundary>
          <WorkoutOverview />
        </WorkoutErrorBoundary>
      </QueryClientProvider>
    )

    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Workout Error:',
      expect.objectContaining({
        error: 'Production error',
        timestamp: expect.any(String),
      })
    )

    process.env.NODE_ENV = originalEnv
  })
})
