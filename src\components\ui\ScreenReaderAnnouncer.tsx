'use client'

import React, { useEffect, useRef } from 'react'

export interface ScreenReaderAnnouncerProps {
  /** Message to announce */
  message: string
  /** Priority of the announcement */
  priority?: 'polite' | 'assertive'
  /** Clear message after this delay (ms) */
  clearAfter?: number
  /** Whether the announcement is enabled */
  enabled?: boolean
}

/**
 * ScreenReaderAnnouncer component for making dynamic announcements to screen readers
 * Used for progressive loading states and dynamic content updates
 */
export function ScreenReaderAnnouncer({
  message,
  priority = 'polite',
  clearAfter = 5000,
  enabled = true,
}: ScreenReaderAnnouncerProps) {
  const announcerRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)

  useEffect(() => {
    if (!enabled || !message) return

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Update the announcement
    if (announcerRef.current) {
      announcerRef.current.textContent = message
    }

    // Clear the announcement after the specified delay
    if (clearAfter > 0) {
      timeoutRef.current = setTimeout(() => {
        if (announcerRef.current) {
          announcerRef.current.textContent = ''
        }
      }, clearAfter)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [message, clearAfter, enabled])

  if (!enabled) return null

  return (
    <div
      ref={announcerRef}
      role="status"
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
    />
  )
}

/**
 * Hook for managing screen reader announcements
 */
export function useScreenReaderAnnouncer() {
  const [announcement, setAnnouncement] = React.useState('')
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)

  const announce = React.useCallback(
    (message: string, options?: { clearAfter?: number }) => {
      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      setAnnouncement(message)

      // Clear after specified time
      const clearAfter = options?.clearAfter ?? 5000
      if (clearAfter > 0) {
        timeoutRef.current = setTimeout(() => {
          setAnnouncement('')
        }, clearAfter)
      }
    },
    []
  )

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return {
    announcement,
    announce,
  }
}
