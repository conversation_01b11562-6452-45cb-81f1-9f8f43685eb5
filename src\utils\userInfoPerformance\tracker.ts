/**
 * Core UserInfo performance tracker implementation
 */

import {
  UserInfoPerformanceMarks,
  type UserInfoPerformanceMetrics,
} from './types'
import { reportToConsole, sendToAnalytics } from './analytics'
import { mark, measure, generateSessionId } from './performanceUtils'
import { CacheOperations } from './cacheOperations'
import { MetricOperations } from './metricOperations'

export class UserInfoPerformanceTracker {
  private static instance: UserInfoPerformanceTracker

  private metrics: UserInfoPerformanceMetrics

  private sessionId: string

  private retryCount: Map<string, number> = new Map()

  private constructor() {
    this.sessionId = this.generateSessionId()
    this.metrics = this.initializeMetrics()
  }

  static getInstance(): UserInfoPerformanceTracker {
    if (!UserInfoPerformanceTracker.instance) {
      UserInfoPerformanceTracker.instance = new UserInfoPerformanceTracker()
    }
    return UserInfoPerformanceTracker.instance
  }

  generateSessionId = generateSessionId

  private initializeMetrics(): UserInfoPerformanceMetrics {
    return {
      loginToFirstByte: null,
      userInfoLoadTime: null,
      statsLoadTime: null,
      overallPageReady: null,
      metricLoadTimes: {
        streak: null,
        workouts: null,
        volume: null,
      },
      cacheMetrics: {
        hitRate: 0,
        hits: 0,
        misses: 0,
        writeLatency: null,
        readLatency: null,
      },
      retryMetrics: {
        userInfoRetries: 0,
        statsRetries: 0,
        totalRetries: 0,
      },
      analytics: {
        timestamp: Date.now(),
        sessionId: this.sessionId,
      },
    }
  }

  /**
   * Mark a performance event
   */
  mark = mark

  /**
   * Measure duration between two marks
   */
  measure = measure

  /**
   * Track login success and start timing
   */
  trackLoginSuccess(): void {
    this.mark(UserInfoPerformanceMarks.LOGIN_SUCCESS)
  }

  /**
   * Track UserInfo API call
   */
  trackUserInfoFetch(isRetry = false): void {
    this.mark(UserInfoPerformanceMarks.USERINFO_FETCH_START)

    if (isRetry) {
      this.incrementRetry('userInfo')
    }
  }

  /**
   * Track UserInfo first byte received
   */
  trackUserInfoFirstByte(): void {
    MetricOperations.trackUserInfoFirstByte(this.metrics)
  }

  /**
   * Track UserInfo load complete
   */
  trackUserInfoComplete(fromCache = false): void {
    MetricOperations.trackUserInfoComplete(this.metrics, fromCache)
  }

  /**
   * Track stats API call
   */
  trackStatsFetch(isRetry = false): void {
    this.mark(UserInfoPerformanceMarks.STATS_FETCH_START)

    if (isRetry) {
      this.incrementRetry('stats')
    }
  }

  /**
   * Track stats load complete
   */
  trackStatsComplete(): void {
    MetricOperations.trackStatsComplete(this.metrics)
  }

  /**
   * Track individual metric load
   */
  trackMetricLoaded(metric: 'streak' | 'workouts' | 'volume'): void {
    MetricOperations.trackMetricLoaded(this.metrics, metric)
  }

  /**
   * Track cache operations
   */
  trackCacheOperation(
    operation: 'hit' | 'miss' | 'write',
    duration?: number
  ): void {
    CacheOperations.trackCacheOperation(this.metrics, operation, duration)
  }

  /**
   * Track page ready
   */
  trackPageReady(): void {
    MetricOperations.trackPageReady(this.metrics)
  }

  /**
   * Increment retry counter
   */
  private incrementRetry(api: 'userInfo' | 'stats'): void {
    const current = this.retryCount.get(api) || 0
    this.retryCount.set(api, current + 1)

    if (api === 'userInfo') {
      this.metrics.retryMetrics.userInfoRetries = current + 1
    } else {
      this.metrics.retryMetrics.statsRetries = current + 1
    }

    this.metrics.retryMetrics.totalRetries =
      this.metrics.retryMetrics.userInfoRetries +
      this.metrics.retryMetrics.statsRetries
  }

  /**
   * Get current metrics
   */
  getMetrics(): UserInfoPerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * Report metrics to console (development)
   */
  reportToConsole(): void {
    reportToConsole(this.getMetrics())
  }

  /**
   * Send metrics to analytics (production)
   */
  sendToAnalytics(): void {
    sendToAnalytics(this.getMetrics())
  }

  /**
   * Reset metrics for new session
   */
  reset(): void {
    this.sessionId = this.generateSessionId()
    this.metrics = this.initializeMetrics()
    this.retryCount.clear()
  }
}
