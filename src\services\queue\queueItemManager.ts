import type { QueueItem } from './queueTypes'

/**
 * Queue item management utilities
 */
export class QueueItemManager {
  /**
   * Create a new queue item
   */
  static createItem<T>(
    request: () => Promise<T>,
    priority: number,
    key?: string,
    resolve?: (value: T) => void,
    reject?: (error: unknown) => void
  ): QueueItem<T> {
    const id = key || `${Date.now()}-${Math.random()}`
    const abortController = new AbortController()

    return {
      id,
      priority,
      request,
      resolve: resolve!,
      reject: reject!,
      abortController,
      retryCount: 0,
    }
  }

  /**
   * Find insertion index based on priority
   */
  static findInsertIndex<T>(queue: QueueItem<T>[], priority: number): number {
    const insertIndex = queue.findIndex((q) => q.priority < priority)
    return insertIndex === -1 ? queue.length : insertIndex
  }

  /**
   * Insert item into queue at correct position
   */
  static insertByPriority<T>(queue: QueueItem<T>[], item: QueueItem<T>): void {
    const insertIndex = this.findInsertIndex(queue, item.priority)
    if (insertIndex === queue.length) {
      queue.push(item)
    } else {
      queue.splice(insertIndex, 0, item)
    }
  }
}
