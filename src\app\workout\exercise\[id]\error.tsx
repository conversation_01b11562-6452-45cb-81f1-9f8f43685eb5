'use client'

export default function ExerciseError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="flex items-center justify-center min-h-[100dvh] bg-gray-50">
      <div className="text-center p-6 max-w-md">
        <h2 className="text-2xl font-bold text-red-600 mb-4">
          Failed to Load Exercise
        </h2>
        <p className="text-gray-600 mb-6">
          {error.message || 'Something went wrong loading this exercise.'}
        </p>
        <div className="space-y-3">
          <button
            onClick={reset}
            className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors min-h-[44px]"
          >
            Try Again
          </button>
          <button
            onClick={() => {
              window.location.href = '/workout'
            }}
            className="w-full px-6 py-3 bg-gray-200 text-gray-700 rounded-lg font-semibold hover:bg-gray-300 transition-colors min-h-[44px]"
          >
            Back to Workout
          </button>
        </div>
      </div>
    </div>
  )
}
