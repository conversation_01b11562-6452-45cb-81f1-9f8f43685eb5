import { RecommendationModel } from '@/types/api'

interface CacheEntry {
  key: string // userId-exerciseId-workoutId
  data: RecommendationModel
  timestamp: number
  workoutTemplateId: number
}

export class RecommendationCache {
  private static instance: RecommendationCache

  private readonly CACHE_EXPIRY_MS = 2 * 24 * 60 * 60 * 1000 // 2 days

  private readonly STORAGE_KEY = 'dr-muscle-recommendation-cache'

  private readonly MAX_CACHE_SIZE = 100 // Limit cache entries

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {}

  static getInstance(): RecommendationCache {
    if (!RecommendationCache.instance) {
      RecommendationCache.instance = new RecommendationCache()
    }
    return RecommendationCache.instance
  }

  /**
   * Generate cache key for a recommendation
   */
  // eslint-disable-next-line class-methods-use-this
  generateKey(userId: string, exerciseId: number, workoutId: number): string {
    return `${userId}-${exerciseId}-${workoutId}`
  }

  /**
   * Get a cached recommendation if valid
   */
  get(key: string): RecommendationModel | null {
    try {
      const cache = this.loadCache()
      const entry = cache[key]

      if (!entry) {
        return null
      }

      if (this.isExpired(entry)) {
        delete cache[key]
        this.saveCache(cache)
        return null
      }

      return entry.data
    } catch (error) {
      console.error('Error reading from recommendation cache:', error)
      return null
    }
  }

  /**
   * Set a recommendation in the cache
   */
  set(key: string, data: RecommendationModel, workoutTemplateId: number): void {
    try {
      const cache = this.loadCache()

      // Implement LRU eviction if cache is too large
      if (Object.keys(cache).length >= this.MAX_CACHE_SIZE) {
        this.evictOldest(cache)
      }

      cache[key] = {
        key,
        data,
        timestamp: Date.now(),
        workoutTemplateId,
      }

      this.saveCache(cache)
    } catch (error) {
      console.error('Error writing to recommendation cache:', error)
      // If storage is full, clear cache and try again
      if (
        error instanceof DOMException &&
        error.name === 'QuotaExceededError'
      ) {
        this.clear()
        try {
          const cache = {
            [key]: { key, data, timestamp: Date.now(), workoutTemplateId },
          }
          this.saveCache(cache)
        } catch {
          // Fail silently if still can't save
        }
      }
    }
  }

  /**
   * Clear all cached recommendations
   */
  clear(): void {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.removeItem(this.STORAGE_KEY)
      }
    } catch (error) {
      console.error('Error clearing recommendation cache:', error)
    }
  }

  /**
   * Clear recommendations for a specific workout template
   */
  clearByWorkout(workoutTemplateId: number): void {
    try {
      const cache = this.loadCache()
      const updatedCache: Record<string, CacheEntry> = {}

      Object.entries(cache).forEach(([key, entry]) => {
        if (entry.workoutTemplateId !== workoutTemplateId) {
          updatedCache[key] = entry
        }
      })

      this.saveCache(updatedCache)
    } catch (error) {
      console.error('Error clearing workout-specific cache:', error)
    }
  }

  /**
   * Check if a cache entry has expired
   */
  isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > this.CACHE_EXPIRY_MS
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; hitRate: number; oldestEntry: number | null } {
    try {
      const cache = this.loadCache()
      const entries = Object.values(cache)

      if (entries.length === 0) {
        return { size: 0, hitRate: 0, oldestEntry: null }
      }

      const oldestTimestamp = Math.min(...entries.map((e) => e.timestamp))

      return {
        size: entries.length,
        hitRate: 0, // Would need to track hits/misses for real hit rate
        oldestEntry: oldestTimestamp,
      }
    } catch {
      return { size: 0, hitRate: 0, oldestEntry: null }
    }
  }

  /**
   * Load cache from localStorage
   */
  private loadCache(): Record<string, CacheEntry> {
    if (typeof window === 'undefined' || !window.localStorage) {
      return {}
    }

    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      return stored ? JSON.parse(stored) : {}
    } catch {
      return {}
    }
  }

  /**
   * Save cache to localStorage
   */
  private saveCache(cache: Record<string, CacheEntry>): void {
    if (typeof window === 'undefined' || !window.localStorage) {
      return
    }

    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(cache))
  }

  /**
   * Evict oldest entries to make room (LRU eviction)
   */
  // eslint-disable-next-line class-methods-use-this
  private evictOldest(cache: Record<string, CacheEntry>): void {
    const entries = Object.entries(cache)
    if (entries.length === 0) return

    // Sort by timestamp (oldest first)
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp)

    // Remove oldest 10% of entries
    const toRemove = Math.max(1, Math.floor(entries.length * 0.1))
    for (let i = 0; i < toRemove; i++) {
      const entry = entries[i]
      if (entry) {
        delete cache[entry[0]]
      }
    }
  }
}

// Export singleton instance
export const recommendationCache = RecommendationCache.getInstance()
