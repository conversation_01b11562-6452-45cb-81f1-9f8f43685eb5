# Testing Guide for Dr. Muscle X

This guide explains how to write tests for components and hooks that use React Query in the Dr. Muscle X application.

## Test Setup

Our test environment is configured with:

- **Vitest** as the test runner
- **React Testing Library** for component testing
- **React Query** testing utilities
- **Happy DOM** as the test environment

## File Structure

```
tests/
├── setup.ts                    # Global test setup
├── test-utils.tsx             # Custom render functions with providers
├── react-query-helpers.ts     # React Query specific test helpers
├── examples/                  # Example test patterns
├── e2e/                      # End-to-end tests
└── unit/                     # Unit tests
```

## Writing Tests with React Query

### 1. Basic Component Test

Use `renderWithProviders` to automatically wrap your component with QueryClientProvider:

```typescript
import { renderWithProviders, screen, waitFor } from '@/tests/test-utils'

it('should display data from query', async () => {
  renderWithProviders(<MyComponent />)

  expect(screen.getByText('Loading...')).toBeInTheDocument()

  await waitFor(() => {
    expect(screen.getByText('Data loaded!')).toBeInTheDocument()
  })
})
```

### 2. Testing Custom Hooks

Use `createQueryWrapper` to create a wrapper for `renderHook`:

```typescript
import { renderHook } from '@testing-library/react'
import { createQueryWrapper, createTestQueryClient } from '@/tests/test-utils'

it('should fetch data', async () => {
  const queryClient = createTestQueryClient()

  const { result } = renderHook(() => useMyHook(), {
    wrapper: createQueryWrapper(queryClient),
  })

  await waitFor(() => {
    expect(result.current.data).toBeDefined()
  })
})
```

### 3. Mocking API Responses

Use the provided mock helpers:

```typescript
import { mockSuccessfulQuery, mockFailedQuery } from '@/tests/test-utils'

// Mock successful response
global.fetch = mockSuccessfulQuery({ id: 1, name: 'Test' })

// Mock failed response
global.fetch = mockFailedQuery('Network error')
```

### 4. Testing with Pre-populated Cache

```typescript
import { setQueryData } from '@/tests/react-query-helpers'

it('should use cached data', () => {
  const queryClient = createTestQueryClient()

  // Pre-populate cache
  setQueryData(queryClient, ['users', '1'], { id: '1', name: 'Cached User' })

  // Component will use cached data immediately
  renderWithProviders(<UserProfile userId="1" />, { queryClient })
})
```

### 5. Testing Mutations

```typescript
import { createMockMutation } from '@/tests/react-query-helpers'

it('should handle mutation', async () => {
  const mockMutation = createMockMutation({
    data: { success: true },
  })

  global.fetch = mockMutation.mutationFn

  // Test your mutation...
})
```

### 6. Testing Offline Behavior

```typescript
import { mockNetworkConditions } from '@/tests/react-query-helpers'

it('should handle offline state', async () => {
  const cleanup = mockNetworkConditions({ isOnline: false })

  // Test offline behavior...

  cleanup() // Restore network state
})
```

## Best Practices

1. **Always use the custom render functions** - They ensure proper provider setup
2. **Reset query cache between tests** - Prevents test interference
3. **Mock at the fetch level** - More realistic than mocking React Query directly
4. **Test loading, success, and error states** - Cover all query states
5. **Use waitFor for async assertions** - Ensures queries have completed
6. **Set gcTime to 0 in tests** - Prevents stale data issues

## Common Patterns

### Testing a Query with Loading State

```typescript
it('should show loading then data', async () => {
  const mockData = { items: [1, 2, 3] }
  global.fetch = mockSuccessfulQuery(mockData)

  renderWithProviders(<MyList />)

  // Initially shows loading
  expect(screen.getByText('Loading...')).toBeInTheDocument()

  // Wait for data
  await waitFor(() => {
    expect(screen.getByText('3 items')).toBeInTheDocument()
  })
})
```

### Testing Error Handling

```typescript
it('should show error message', async () => {
  global.fetch = mockFailedQuery('Server error')

  renderWithProviders(<MyComponent />)

  await waitFor(() => {
    expect(screen.getByText(/error/i)).toBeInTheDocument()
  })
})
```

### Testing Refetch

```typescript
it('should refetch on button click', async () => {
  let callCount = 0
  global.fetch = vi.fn(() => {
    callCount++
    return Promise.resolve({
      ok: true,
      json: () => Promise.resolve({ count: callCount })
    })
  })

  const { getByText } = renderWithProviders(<RefetchExample />)

  await waitFor(() => {
    expect(getByText('Count: 1')).toBeInTheDocument()
  })

  fireEvent.click(getByText('Refetch'))

  await waitFor(() => {
    expect(getByText('Count: 2')).toBeInTheDocument()
  })
})
```

## Debugging Tips

1. **Query State**: Use `queryClient.getQueryState(queryKey)` to inspect query state
2. **Cache Contents**: Use `queryClient.getQueryData(queryKey)` to see cached data
3. **Pending Queries**: Check `queryClient.isFetching()` to see if queries are running
4. **Enable Logging**: Set `logger` in QueryClient options for detailed logs

## Examples

See `tests/examples/react-query-test-example.tsx` for comprehensive examples of all these patterns.
