/**
 * Type definitions for workout API services
 */

import type { ExerciseModel } from '@/types/api'

// Request/Response types
export interface GetUserWorkoutProgramTimeZoneInfoResponse {
  GetUserProgramInfoResponseModel: {
    NextWorkoutTemplate: {
      Id: number
      Label: string
      IsSystemExercise: boolean
      Exercises: ExerciseModel[] | null
    }
    RecommendedProgram?: {
      Id: number
      Label: string
      RemainingToLevelUp?: number
    }
  }
  LastWorkoutDate?: string
  LastConsecutiveWorkoutDays?: number
}

export interface GetRecommendationForExerciseRequest {
  Username: string
  ExerciseId: number
  WorkoutId: number
  IsQuickMode?: boolean | null
  LightSessionDays?: number | null
  SwapedExId?: number
  IsStrengthPhashe?: boolean // Note: API has typo in parameter name
  IsFreePlan?: boolean
  IsFirstWorkoutOfStrengthPhase?: boolean
  VersionNo?: number
  SetStyle?: string
  IsFlexibility?: boolean
}
