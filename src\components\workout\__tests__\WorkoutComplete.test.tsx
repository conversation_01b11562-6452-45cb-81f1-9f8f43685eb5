import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { WorkoutComplete } from '../WorkoutComplete'

// Mock the hooks
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(() => ({
    push: vi.fn(),
  })),
}))

vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(() => ({
    workoutSession: {
      workoutId: '123',
      startTime: '2024-01-01T10:00:00Z',
      endTime: '2024-01-01T11:00:00Z',
      exercises: [
        {
          id: '1',
          name: 'Bench Press',
          sets: [
            {
              id: '1',
              reps: 10,
              weight: { Lb: 135, Kg: 61 },
              isWarmup: false,
              rir: 2,
            },
            {
              id: '2',
              reps: 8,
              weight: { Lb: 155, Kg: 70 },
              isWarmup: false,
              rir: 1,
            },
          ],
        },
      ],
    },
    finishWorkout: vi.fn(),
    isLoading: false,
    error: null,
  })),
}))

describe('WorkoutComplete', () => {
  describe('Theme Application', () => {
    it('should use theme-aware background instead of hardcoded gray-50', () => {
      const { container } = render(<WorkoutComplete />)

      const mainDiv = container.querySelector('.flex.flex-col.h-full')
      expect(mainDiv).toHaveClass('bg-bg-primary')
      expect(mainDiv).not.toHaveClass('bg-gray-50')
    })

    it('should use theme-aware text colors instead of hardcoded gray-900', () => {
      render(<WorkoutComplete />)

      const title = screen.getByText('Nice work!')
      expect(title).toHaveClass('text-text-primary')
      expect(title).not.toHaveClass('text-gray-900')

      const summaryTitle = screen.getByText('Summary')
      expect(summaryTitle).toHaveClass('text-text-primary')
      expect(summaryTitle).not.toHaveClass('text-gray-900')
    })

    it('should use theme-aware card background instead of hardcoded white', () => {
      const { container } = render(<WorkoutComplete />)

      const summaryCard = container.querySelector('.rounded-lg.shadow-sm')
      expect(summaryCard).toHaveClass('bg-bg-secondary')
      expect(summaryCard).not.toHaveClass('bg-white')
    })

    it('should use theme-aware text colors for labels instead of hardcoded gray-600', () => {
      render(<WorkoutComplete />)

      const exercisesLabel = screen.getByText('Exercises')
      expect(exercisesLabel).toHaveClass('text-text-tertiary')
      expect(exercisesLabel).not.toHaveClass('text-gray-600')
    })

    // Skip testing personal records section as it relies on multiple useWorkout calls
    // and the mock doesn't properly support this pattern

    // Skip testing offline section as it relies on multiple useWorkout calls
    // and the mock doesn't properly support this pattern

    it('should use theme-aware button colors instead of hardcoded blue-600', () => {
      render(<WorkoutComplete />)

      const finishButton = screen.getByRole('button', { name: /back to home/i })
      expect(finishButton).toHaveClass('bg-brand-primary')
      expect(finishButton).toHaveClass('hover:bg-brand-primary/90')
      expect(finishButton).toHaveClass('text-text-inverse')
      expect(finishButton).not.toHaveClass('bg-blue-600')
      expect(finishButton).not.toHaveClass('hover:bg-blue-700')
    })

    it('should use theme-aware bottom bar instead of hardcoded white', () => {
      const { container } = render(<WorkoutComplete />)

      const bottomBar = container.querySelector('.fixed.bottom-0')
      expect(bottomBar).toHaveClass('bg-bg-secondary')
      expect(bottomBar).toHaveClass('border-border-primary')
      expect(bottomBar).not.toHaveClass('bg-white')
      expect(bottomBar).not.toHaveClass('border-gray-200')
    })
  })

  describe('Original Functionality', () => {
    it('should render workout summary', () => {
      render(<WorkoutComplete />)

      expect(screen.getByText('Nice work!')).toBeInTheDocument()
      expect(screen.getByText('Summary')).toBeInTheDocument()
      expect(screen.getByText('1')).toBeInTheDocument() // exercises count
      expect(screen.getByText('2')).toBeInTheDocument() // sets count
    })

    it('should render finish button', () => {
      render(<WorkoutComplete />)

      const button = screen.getByRole('button')
      expect(button).toHaveTextContent('Back to Home')
    })
  })
})
