import { useCallback, useMemo, useRef, useEffect } from 'react'
import { useWorkoutStore } from '@/stores/workoutStore/index'
import { useAuthStore } from '@/stores/authStore'
// import { recommendationCache } from '@/services/cache/recommendationCache'
import { logger } from '@/utils/logger'
import {
  getExerciseRecommendation,
  // generateRecommendationCacheKey,
} from '@/services/api/workout'
import { hasAuthToken } from '@/api/client'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import { getUserSettings } from '@/services/userSettings'
import type { RecommendationModel } from '@/types'

// Track pending recommendation requests to avoid duplicates
const pendingRecommendationRequests = new Map<
  number,
  Promise<RecommendationModel | null>
>()

// Clear pending requests for testing
export const clearPendingRequests = () => {
  pendingRecommendationRequests.clear()
}

export function useWorkoutRecommendations() {
  const { isAuthenticated, hasHydrated } = useAuthStore()
  const {
    getCachedExerciseRecommendation,
    setCachedExerciseRecommendation,
    loadAllExerciseRecommendations,
    currentWorkout,
  } = useWorkoutStore()

  // Use refs for auth state to prevent unnecessary re-renders
  const authStateRef = useRef({
    isAuthenticated,
    hasHydrated,
  })

  // Use ref for workout ID to prevent unnecessary re-renders
  const workoutIdRef = useRef(currentWorkout?.Id)

  // Update refs when values change
  useEffect(() => {
    authStateRef.current = {
      isAuthenticated,
      hasHydrated,
    }
  }, [isAuthenticated, hasHydrated])

  useEffect(() => {
    workoutIdRef.current = currentWorkout?.Id
  }, [currentWorkout?.Id])

  // Only allow API calls when authentication is fully ready
  const canMakeApiCalls = () =>
    authStateRef.current.isAuthenticated &&
    authStateRef.current.hasHydrated

  const loadRecommendation = useCallback(
    async (
      exerciseId: number,
      exerciseName: string
    ): Promise<RecommendationModel | null> => {
      // Only log in development to reduce console spam
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log(
          '🎯 [useWorkoutRecommendations] loadRecommendation called',
          {
            exerciseId,
            exerciseName,
            currentWorkoutId: workoutIdRef.current,
            hasCurrentWorkout: !!currentWorkout,
          }
        )
      }

      // Remove excessive logging to prevent spam

      // Check if user is authenticated and ready to make API calls
      if (!canMakeApiCalls()) {
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.warn(
            '❌ [useWorkoutRecommendations] Not ready to make API calls',
            {
              authState: authStateRef.current,
            }
          )
        }
        // Not ready to make API calls
        return null
      }

      const userEmail = getCurrentUserEmail()
      if (!userEmail) {
        // eslint-disable-next-line no-console
        console.error('❌ [useWorkoutRecommendations] No user email found')
        // No user email found
        return null
      }

      // Check if API client has authorization token
      if (!hasAuthToken()) {
        // eslint-disable-next-line no-console
        console.warn(
          '❌ [useWorkoutRecommendations] No authorization token in API client'
        )
        // No authorization token
        return null
      }

      // TODO: Fix generateRecommendationCacheKey to accept 2 args
      // const cacheKey = `${userEmail}:${exerciseId}`

      // Check cache first
      const cached = getCachedExerciseRecommendation(exerciseId)
      if (cached) {
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.log(
            '💾 [useWorkoutRecommendations] Using cached recommendation',
            {
              exerciseId,
              cached,
            }
          )
        }
        // Using cached recommendation
        return cached
      }

      // Check if a request is already pending
      const pending = pendingRecommendationRequests.get(exerciseId)
      if (pending) {
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.log(
            '⏳ [useWorkoutRecommendations] Request already pending',
            {
              exerciseId,
            }
          )
        }
        // Request already pending
        return pending
      }

      // Create the request promise
      const requestPromise: Promise<RecommendationModel | null> = (async () => {
        try {
          // eslint-disable-next-line no-console
          console.log('🔄 [useWorkoutRecommendations] Fetching from API', {
            exerciseId,
            workoutId: currentWorkout?.Id,
          })
          // Fetching from API

          // Get workout ID from ref
          const workoutId = workoutIdRef.current
          if (!workoutId) {
            if (process.env.NODE_ENV === 'development') {
              // eslint-disable-next-line no-console
              console.error(
                '❌ [useWorkoutRecommendations] No current workout ID available'
              )
            }
            // No current workout ID available
            return null
          }

          // Find the exercise to get its SetStyle and IsFlexibility
          const exercise = currentWorkout?.Exercises?.find(
            (ex) => ex.Id === exerciseId
          )
          if (!exercise) {
            // eslint-disable-next-line no-console
            console.error(
              `❌ [useWorkoutRecommendations] Exercise ${exerciseId} not found in current workout`,
              {
                availableExercises: currentWorkout?.Exercises?.map((ex) => ({
                  id: ex.Id,
                  label: ex.Label,
                })),
              }
            )
            logger.warn(`Exercise ${exerciseId} not found in current workout`)
            return null
          }

          // eslint-disable-next-line no-console
          console.log('📋 [useWorkoutRecommendations] Exercise details', {
            exerciseId,
            label: exercise.Label,
            setStyle: exercise.SetStyle,
            isFlexibility: exercise.IsFlexibility,
            bodyPartId: exercise.BodyPartId,
          })

          // Get user settings for the recommendation request
          const userSettings = await getUserSettings()

          // eslint-disable-next-line no-console
          console.log(
            '👤 [useWorkoutRecommendations] User settings',
            userSettings
          )

          // Create the request object for getExerciseRecommendation following mobile app pattern
          const request = {
            Username: userEmail,
            ExerciseId: exerciseId,
            WorkoutId: workoutId,
            SetStyle: exercise.SetStyle || 'Normal', // Default to Normal if not specified
            IsFlexibility: exercise.IsFlexibility || false, // Default to false if not specified
            IsQuickMode: userSettings.isQuickMode,
            LightSessionDays: userSettings.lightSessionDays,
            SwapedExId: undefined, // Only set if exercise was swapped
            IsStrengthPhashe: userSettings.isStrengthPhase, // Note: API has typo
            IsFreePlan: userSettings.isFreePlan,
            IsFirstWorkoutOfStrengthPhase:
              userSettings.isFirstWorkoutOfStrengthPhase,
            VersionNo: 1, // API version number
          }

          // eslint-disable-next-line no-console
          console.log(
            '📤 [useWorkoutRecommendations] Calling getExerciseRecommendation with request:',
            request
          )

          // Add timeout to prevent hanging requests
          const timeoutPromise = new Promise<RecommendationModel | null>(
            (resolve) => {
              setTimeout(
                () => resolve(null), // Return null on timeout instead of rejecting
                30000
              ) // 30 second timeout
            }
          )

          const recommendation = await Promise.race([
            getExerciseRecommendation(request),
            timeoutPromise,
          ])

          // eslint-disable-next-line no-console
          console.log(
            '📥 [useWorkoutRecommendations] Received recommendation:',
            {
              exerciseId,
              hasRecommendation: !!recommendation,
              recommendation,
            }
          )

          if (recommendation) {
            // Update store cache
            setCachedExerciseRecommendation(exerciseId, recommendation)
            // Update recommendationCache
            // TODO: Fix recommendationCache.set signature
            // recommendationCache.set(cacheKey, recommendation)
          }

          return recommendation
        } catch (error) {
          // Handle different types of errors with appropriate logging
          const isNetworkError =
            error instanceof Error &&
            (error.message.includes('Network Error') ||
              error.message.includes('Failed to fetch') ||
              error.message.includes('ERR_CONNECTION_REFUSED'))

          const is404Error =
            error instanceof Error && error.message.includes('404')

          if (is404Error) {
            // 404 errors are expected for exercises without history - log as info only
            if (process.env.NODE_ENV === 'development') {
              // eslint-disable-next-line no-console
              console.info(
                '📝 [useWorkoutRecommendations] No recommendation available (404) - exercise has no history',
                { exerciseId }
              )
            }
            logger.info(
              '[loadRecommendation] No recommendation available (404)',
              {
                exerciseId,
              }
            )
          } else if (isNetworkError) {
            // Network errors should be logged as warnings
            if (process.env.NODE_ENV === 'development') {
              // eslint-disable-next-line no-console
              console.warn(
                '🌐 [useWorkoutRecommendations] Network error fetching recommendation',
                { exerciseId, error: error.message }
              )
            }
            // Network error - likely offline
          } else if (process.env.NODE_ENV === 'development') {
            // Other errors should be logged as errors
            // eslint-disable-next-line no-console
            console.error(
              '❌ [useWorkoutRecommendations] Failed to fetch recommendation',
              { exerciseId, error }
            )
            // Failed to fetch recommendation
          }
          return null
        } finally {
          // Remove from pending requests
          pendingRecommendationRequests.delete(exerciseId)
        }
      })()

      // Track the pending request
      pendingRecommendationRequests.set(exerciseId, requestPromise)

      return requestPromise
    },
    [
      getCachedExerciseRecommendation,
      setCachedExerciseRecommendation,
      // Using refs for auth state and workoutId to prevent dependency issues
      // eslint-disable-next-line react-hooks/exhaustive-deps
    ]
  )

  const preloadRecommendations = useCallback(
    async (exercises: Array<{ Id: number; Label: string }>) => {
      // Starting preload for exercises

      const promises = exercises.map((exercise) =>
        loadRecommendation(exercise.Id, exercise.Label)
      )

      const results = await Promise.allSettled(promises)

      const successful = results.filter(
        (r) => r.status === 'fulfilled' && r.value !== null
      ).length

      // Preload completed

      return successful
    },
    [loadRecommendation]
  )

  const invalidateRecommendation = useCallback(
    (exerciseId: number) => {
      const userEmail = getCurrentUserEmail()
      if (!userEmail) return

      // TODO: Fix generateRecommendationCacheKey to accept 2 args
      // const cacheKey = `${userEmail}:${exerciseId}`
      // TODO: Fix delete method
      // recommendationCache.delete(cacheKey)

      // Also clear from store
      setCachedExerciseRecommendation(exerciseId, null)
    },
    [setCachedExerciseRecommendation]
  )

  return useMemo(
    () => ({
      loadRecommendation,
      preloadRecommendations,
      invalidateRecommendation,
      loadAllExerciseRecommendations,
    }),
    [
      loadRecommendation,
      preloadRecommendations,
      invalidateRecommendation,
      loadAllExerciseRecommendations,
    ]
  )
}
