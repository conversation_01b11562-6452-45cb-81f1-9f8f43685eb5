import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ProgramStatsGrid } from '../ProgramStatsGrid'
import type { ProgramStats } from '@/types'

describe('ProgramStatsGrid (Simplified)', () => {
  const mockStats: ProgramStats = {
    averageWorkoutTime: 45.5,
    totalVolume: 125000,
    personalRecords: 3,
    consecutiveWeeks: 4,
    lastWorkoutDate: '2024-01-15',
    totalWorkoutsCompleted: 24,
  }

  it('renders only 2 stat cards', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    const cards = screen.getAllByTestId(/stat-card/)
    expect(cards).toHaveLength(2)
  })

  it('displays workout streak in weeks', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.getByText('Workout Streak')).toBeInTheDocument()
    expect(screen.getByText('4 weeks')).toBeInTheDocument()
  })

  it('displays last workout date', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.getByText('Last Workout')).toBeInTheDocument()
    // Date should be formatted nicely (might be off by one due to timezone)
    expect(screen.getByText(/Jan 1[45]|January 1[45]/)).toBeInTheDocument()
  })

  it('handles missing stats gracefully', () => {
    render(<ProgramStatsGrid stats={undefined} />)

    // Should show dashes or loading state
    const values = screen.getAllByText('-')
    expect(values).toHaveLength(2)
  })

  it('handles zero consecutive weeks', () => {
    const statsWithZeroWeeks = { ...mockStats, consecutiveWeeks: 0 }
    render(<ProgramStatsGrid stats={statsWithZeroWeeks} />)

    expect(screen.getByText('0 weeks')).toBeInTheDocument()
  })

  it('handles missing last workout date', () => {
    const statsNoDate = { ...mockStats, lastWorkoutDate: undefined }
    render(<ProgramStatsGrid stats={statsNoDate} />)

    expect(screen.getByText('Last Workout')).toBeInTheDocument()
    expect(screen.getByText('-')).toBeInTheDocument()
  })

  it('shows loading state when isLoading is true', () => {
    render(<ProgramStatsGrid stats={mockStats} isLoading />)

    const skeletons = screen.getAllByTestId(/skeleton/)
    expect(skeletons.length).toBeGreaterThan(0)
  })

  it('uses responsive grid layout', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    const grid = screen.getByTestId('program-stats-grid')
    expect(grid).toHaveClass('grid', 'grid-cols-2')
  })

  it('does not show total workouts', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.queryByText('Total Workouts')).not.toBeInTheDocument()
    expect(screen.queryByText(/24/)).not.toBeInTheDocument()
  })

  it('does not show days completed', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.queryByText('Days Completed')).not.toBeInTheDocument()
  })

  it('does not show current week', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.queryByText('Current Week')).not.toBeInTheDocument()
  })

  it('does not show completion percentage', () => {
    render(<ProgramStatsGrid stats={mockStats} />)

    expect(screen.queryByText('Completion')).not.toBeInTheDocument()
    expect(screen.queryByText('%')).not.toBeInTheDocument()
  })

  it('formats dates consistently', () => {
    const { rerender } = render(
      <ProgramStatsGrid
        stats={{ ...mockStats, lastWorkoutDate: '2024-01-01' }}
      />
    )

    // Should show a formatted date
    const dateElements = screen.getAllByText(/^[A-Z][a-z]{2} \d{1,2}$/)
    expect(dateElements.length).toBeGreaterThan(0)

    // Test different date
    rerender(
      <ProgramStatsGrid
        stats={{ ...mockStats, lastWorkoutDate: '2024-12-25' }}
      />
    )
    const newDateElements = screen.getAllByText(/^[A-Z][a-z]{2} \d{1,2}$/)
    expect(newDateElements.length).toBeGreaterThan(0)
  })
})
