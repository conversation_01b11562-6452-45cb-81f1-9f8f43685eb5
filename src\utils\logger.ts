/**
 * Logger utility for Dr<PERSON> Mu<PERSON>cle X
 * Only logs in development mode to keep production clean
 */

// const isDevelopment = process.env.NODE_ENV === 'development'

export const logger = {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  log: (..._: unknown[]) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.log(...args)
    // }
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  info: (..._: unknown[]) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.info(...args)
    // }
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  warn: (..._: unknown[]) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.warn(...args)
    // }
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  error: (..._: unknown[]) => {
    // Temporarily disabled to reduce console noise
    // Always log errors, even in production
    // // eslint-disable-next-line no-console
    // console.error(...args)
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  debug: (..._: unknown[]) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.debug(...args)
    // }
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  group: (_: string) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.group(label)
    // }
  },

  groupEnd: () => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.groupEnd()
    // }
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  table: (_: unknown) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.table(data)
    // }
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  time: (_: string) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.time(label)
    // }
  },

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  timeEnd: (_: string) => {
    // Temporarily disabled to reduce console noise
    // if (isDevelopment) {
    //   // eslint-disable-next-line no-console
    //   console.timeEnd(label)
    // }
  },
}
