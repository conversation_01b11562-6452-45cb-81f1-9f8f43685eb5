/**
 * Metrics collection utilities for program performance
 */

import type { PerformanceMetrics, PerformanceWithMemory } from './constants'

export function getPerformanceMetrics(): PerformanceMetrics {
  if (typeof performance === 'undefined') {
    return {
      pageLoad: 0,
      componentRenders: {},
      dataFetch: 0,
      totalRenderTime: 0,
    }
  }

  const entries = performance.getEntriesByType('measure')
  const metrics: PerformanceMetrics = {
    pageLoad: 0,
    componentRenders: {},
    dataFetch: 0,
    totalRenderTime: 0,
  }

  entries.forEach((entry) => {
    if (entry.name === 'program-page-load') {
      metrics.pageLoad = entry.duration
    } else if (
      entry.name.includes('program-component-') &&
      entry.name.includes('-render')
    ) {
      const componentName = entry.name.match(
        /program-component-(.+)-render/
      )?.[1]
      if (componentName) {
        metrics.componentRenders[componentName] = entry.duration
        metrics.totalRenderTime += entry.duration
      }
    } else if (entry.name === 'program-data-fetch') {
      metrics.dataFetch = entry.duration
    }
  })

  // Add memory metrics if available (Chrome only)
  if ('memory' in performance) {
    const perfWithMemory = performance as PerformanceWithMemory
    const { memory } = perfWithMemory
    if (memory) {
      metrics.memory = {
        used: Math.round(memory.usedJSHeapSize / 1048576), // Convert to MB
        total: Math.round(memory.totalJSHeapSize / 1048576),
        limit: Math.round(memory.jsHeapSizeLimit / 1048576),
        percentage: Math.round(
          (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
        ),
      }
    }
  }

  return metrics
}
