/**
 * Glassmorphism Theme Tokens
 * Future tech with frosted glass effects and aurora gradients
 */

import { Theme } from '../types'

export const glassmorphismTheme: Theme = {
  name: 'glassmorphism',
  colors: {
    background: {
      primary: '#0A0F1B',
      secondary: 'rgba(255, 255, 255, 0.05)',
      tertiary: 'rgba(255, 255, 255, 0.1)',
      overlay: 'rgba(10, 15, 27, 0.8)',
    },
    text: {
      primary: '#FFFFFF',
      secondary: 'rgba(255, 255, 255, 0.8)',
      tertiary: 'rgba(255, 255, 255, 0.6)',
      inverse: '#0A0F1B',
    },
    brand: {
      primary: '#00D4FF', // Cyan glow
      secondary: '#FF00FF', // Magenta glow
      accent: '#7B00FF', // Purple glow
    },
    status: {
      success: '#00FF88',
      error: '#FF0055',
      warning: '#FFAA00',
      info: '#00AAFF',
    },
    interactive: {
      hover: 'rgba(0, 212, 255, 0.2)',
      active: 'rgba(0, 212, 255, 0.3)',
      disabled: 'rgba(255, 255, 255, 0.05)',
    },
  },
  typography: {
    fonts: {
      heading:
        '"SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
      body: '"SF Pro Text", -apple-system, BlinkMacSystemFont, sans-serif',
      mono: '"SF Mono", monospace',
    },
    sizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '2.25rem',
      '4xl': '3.5rem',
    },
    weights: {
      light: 200,
      regular: 300,
      medium: 400,
      semibold: 500,
      bold: 600,
    },
    lineHeights: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },
  spacing: {
    unit: 4,
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
    '4xl': '6rem',
  },
  shadows: {
    none: 'none',
    sm: '0 2px 8px rgba(0, 0, 0, 0.1)',
    md: '0 4px 16px rgba(0, 0, 0, 0.1)',
    lg: '0 8px 32px rgba(0, 0, 0, 0.1)',
    xl: '0 16px 64px rgba(0, 0, 0, 0.1)',
    inner: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)',
  },
  animations: {
    duration: {
      fast: '200ms',
      normal: '400ms',
      slow: '600ms',
    },
    easing: {
      default: 'cubic-bezier(0.4, 0, 0.2, 1)',
      smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
      spring: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
    },
  },
  borders: {
    radius: {
      none: '0',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      full: '9999px',
    },
    width: {
      hairline: '0.5px',
      thin: '1px',
      medium: '2px',
      thick: '4px',
    },
  },
}
