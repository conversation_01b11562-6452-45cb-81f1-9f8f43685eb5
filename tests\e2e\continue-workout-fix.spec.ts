import { test, expect } from '@playwright/test'

test.describe('Continue to Workout Navigation Fix', () => {
  test('should navigate to workout page when clicking Continue to workout', async ({
    page,
  }) => {
    // Login
    await page.goto('/login')
    await page.fill(
      'input[type="email"]',
      process.env.TEST_EMAIL || '<EMAIL>'
    )
    await page.fill(
      'input[type="password"]',
      process.env.TEST_PASSWORD || 'Dr123456'
    )
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('/program')

    // Ensure page is loaded
    await page.waitForSelector('text=Continue to Workout', { timeout: 5000 })

    // Click Continue to workout button
    await page.click('text=Continue to Workout')

    // Should navigate to workout page
    await page.waitForURL('/workout', { timeout: 10000 })

    // Verify we're on the workout page
    expect(page.url()).toContain('/workout')

    // Verify workout page content is visible
    await expect(page.locator('button:has-text("Start Workout")')).toBeVisible({
      timeout: 5000,
    })
  })

  test('should handle API response with root-level data', async ({ page }) => {
    // Monitor API calls
    const responsePromise = page.waitForResponse(
      (response) =>
        response.url().includes('GetUserWorkoutProgramTimeZoneInfo') &&
        response.status() === 200
    )

    // Login
    await page.goto('/login')
    await page.fill(
      'input[type="email"]',
      process.env.TEST_EMAIL || '<EMAIL>'
    )
    await page.fill(
      'input[type="password"]',
      process.env.TEST_PASSWORD || 'Dr123456'
    )
    await page.click('button[type="submit"]')

    // Wait for the API response
    const response = await responsePromise
    const responseData = await response.json()

    // Log the response for debugging
    console.log('API Response:', {
      statusCode: responseData.StatusCode,
      hasResult: 'Result' in responseData,
      hasData: 'Data' in responseData,
      keys: Object.keys(responseData),
    })

    // Verify navigation to program page works
    await page.waitForURL('/program')
    await expect(page.locator('text=Continue to Workout')).toBeVisible()
  })
})
