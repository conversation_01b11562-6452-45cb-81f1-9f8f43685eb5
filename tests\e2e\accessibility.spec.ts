import { test, expect } from '@playwright/test'
import { injectAxe, checkA11y } from 'axe-playwright'

test.describe('Accessibility Testing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await injectAxe(page)
  })

  test('login page should be accessible', async ({ page }) => {
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: {
        html: true,
      },
    })
  })

  test('should support keyboard navigation', async ({ page }) => {
    // Tab through login form
    await page.keyboard.press('Tab') // Focus email
    await expect(page.getByLabel('Email')).toBeFocused()
    
    await page.keyboard.press('Tab') // Focus password
    await expect(page.getByLabel('Password')).toBeFocused()
    
    await page.keyboard.press('Tab') // Focus show password button
    await expect(page.getByRole('button', { name: /show password/i })).toBeFocused()
    
    await page.keyboard.press('Tab') // Focus login button
    await expect(page.getByRole('button', { name: 'Login' })).toBeFocused()
  })

  test('should have proper ARIA labels', async ({ page }) => {
    // Check form inputs
    const emailInput = page.getByLabel('Email')
    await expect(emailInput).toHaveAttribute('aria-required', 'true')
    
    const passwordInput = page.getByLabel('Password')
    await expect(passwordInput).toHaveAttribute('aria-required', 'true')
    
    // Check buttons
    const loginButton = page.getByRole('button', { name: 'Login' })
    await expect(loginButton).toHaveAttribute('aria-label')
  })

  test('workout page should be accessible', async ({ page }) => {
    // Login first
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: 'Login' }).click()
    
    await page.waitForURL('/workout')
    await injectAxe(page)
    
    await checkA11y(page, null, {
      detailedReport: true,
    })
  })

  test('should announce errors to screen readers', async ({ page }) => {
    // Try to submit empty form
    await page.getByRole('button', { name: 'Login' }).click()
    
    // Error should have alert role
    const errorMessage = page.getByText(/email is required/i)
    await expect(errorMessage).toHaveAttribute('role', 'alert')
  })

  test('should have sufficient color contrast', async ({ page }) => {
    await checkA11y(page, null, {
      runOnly: {
        type: 'tag',
        values: ['wcag2aa', 'wcag21aa'],
      },
    })
  })

  test('touch targets should be at least 44x44 pixels', async ({ page }) => {
    // Check button sizes
    const loginButton = page.getByRole('button', { name: 'Login' })
    const box = await loginButton.boundingBox()
    
    expect(box?.height).toBeGreaterThanOrEqual(44)
    expect(box?.width).toBeGreaterThanOrEqual(44)
  })

  test('should support screen reader navigation in workout', async ({ page }) => {
    // Login
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: 'Login' }).click()
    
    await page.waitForURL('/workout')
    
    // Check workout page structure
    await expect(page.getByRole('heading', { name: "Today's Workout" })).toBeVisible()
    await expect(page.getByRole('button', { name: 'Start Workout' })).toBeVisible()
    
    // Navigation should have proper landmarks
    await expect(page.getByRole('main')).toBeVisible()
  })
})