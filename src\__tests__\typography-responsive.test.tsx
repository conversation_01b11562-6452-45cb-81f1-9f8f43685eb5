import { describe, it, expect, beforeEach } from 'vitest'
import { render } from '@testing-library/react'

// Mock component to test typography classes
function TypographyTestComponent() {
  return (
    <div className="p-4">
      <h1 className="text-4xl sm:text-5xl lg:text-6xl font-heading">
        Heading 1
      </h1>
      <h2 className="text-3xl sm:text-4xl lg:text-5xl font-heading">
        Heading 2
      </h2>
      <h3 className="text-2xl sm:text-3xl lg:text-4xl font-heading">
        Heading 3
      </h3>
      <h4 className="text-xl sm:text-2xl lg:text-3xl font-heading">
        Heading 4
      </h4>
      <p className="text-base sm:text-lg lg:text-xl">Body text</p>
      <p className="text-sm sm:text-base lg:text-lg">Small text</p>

      {/* Login specific elements */}
      <div className="font-heading text-lg sm:text-xl lg:text-2xl">
        World's Fastest AI Personal Trainer
      </div>

      {/* Form elements */}
      <label className="text-sm sm:text-base font-medium">Email</label>
      <input
        className="text-base sm:text-lg h-12 sm:h-14"
        placeholder="Enter email"
      />
      <button className="text-base sm:text-lg font-medium h-12 sm:h-14">
        Login
      </button>
    </div>
  )
}

describe('Typography Responsiveness', () => {
  beforeEach(() => {
    // Reset viewport
    global.innerWidth = 375 // Mobile default
  })

  describe('Font Size Scaling', () => {
    it('should have mobile-first font sizes', () => {
      const { container } = render(<TypographyTestComponent />)

      const h1 = container.querySelector('h1')
      const bodyText = container.querySelector('p')

      // Base mobile sizes
      expect(h1?.className).toContain('text-4xl')
      expect(bodyText?.className).toContain('text-base')
    })

    it('should scale up on tablet breakpoint', () => {
      const { container } = render(<TypographyTestComponent />)

      const h1 = container.querySelector('h1')
      const h2 = container.querySelector('h2')

      // Should have sm: breakpoint classes
      expect(h1?.className).toMatch(/sm:text-5xl/)
      expect(h2?.className).toMatch(/sm:text-4xl/)
    })

    it('should scale up on desktop breakpoint', () => {
      const { container } = render(<TypographyTestComponent />)

      const h1 = container.querySelector('h1')
      const h2 = container.querySelector('h2')

      // Should have lg: breakpoint classes
      expect(h1?.className).toMatch(/lg:text-6xl/)
      expect(h2?.className).toMatch(/lg:text-5xl/)
    })
  })

  describe('Line Height and Spacing', () => {
    it('should have appropriate line heights for readability', () => {
      const { container } = render(<TypographyTestComponent />)

      // Tailwind's text classes include appropriate line-height
      const bodyText = container.querySelector('p')
      expect(bodyText?.className).toMatch(/text-base/)

      // text-base has line-height of 1.5rem (24px) for 16px font
    })

    it('should maintain readable line lengths', () => {
      const { container } = render(
        <div className="max-w-prose mx-auto">
          <p className="text-base">
            This is a long paragraph that should have a maximum width to ensure
            optimal readability. The max-w-prose class limits the width to
            approximately 65 characters per line.
          </p>
        </div>
      )

      const paragraph = container.querySelector('.max-w-prose')
      expect(paragraph).toBeTruthy()
    })
  })

  describe('Touch Target Sizes', () => {
    it('should have minimum touch target sizes on mobile', () => {
      const { container } = render(<TypographyTestComponent />)

      const button = container.querySelector('button')
      const input = container.querySelector('input')

      // Minimum height of 44px (h-12 = 48px)
      expect(button?.className).toContain('h-12')
      expect(input?.className).toContain('h-12')
    })

    it('should increase touch targets on larger screens', () => {
      const { container } = render(<TypographyTestComponent />)

      const button = container.querySelector('button')
      const input = container.querySelector('input')

      // Larger height on tablet/desktop
      expect(button?.className).toContain('sm:h-14')
      expect(input?.className).toContain('sm:h-14')
    })
  })

  describe('Font Hierarchy', () => {
    it('should maintain clear visual hierarchy', () => {
      const { container } = render(<TypographyTestComponent />)

      const h1 = container.querySelector('h1')
      const h2 = container.querySelector('h2')
      const h3 = container.querySelector('h3')
      const h4 = container.querySelector('h4')

      // Each heading should be progressively smaller
      expect(h1?.className).toContain('text-4xl')
      expect(h2?.className).toContain('text-3xl')
      expect(h3?.className).toContain('text-2xl')
      expect(h4?.className).toContain('text-xl')
    })

    it('should use heading font for all headings', () => {
      const { container } = render(<TypographyTestComponent />)

      const headings = container.querySelectorAll('h1, h2, h3, h4')
      headings.forEach((heading) => {
        expect(heading.className).toContain('font-heading')
      })
    })
  })

  describe('Login Page Specific Typography', () => {
    it('should scale tagline appropriately', () => {
      const { container } = render(<TypographyTestComponent />)

      const tagline = container.querySelector('.font-heading.text-lg')
      expect(tagline?.className).toContain('text-lg')
      expect(tagline?.className).toContain('sm:text-xl')
      expect(tagline?.className).toContain('lg:text-2xl')
    })

    it('should have readable form labels', () => {
      const { container } = render(<TypographyTestComponent />)

      const label = container.querySelector('label')
      expect(label?.className).toContain('text-sm')
      expect(label?.className).toContain('sm:text-base')
      expect(label?.className).toContain('font-medium')
    })
  })

  describe('Landscape Orientation', () => {
    it('should handle landscape orientation appropriately', () => {
      // Set landscape viewport
      global.innerWidth = 812
      global.innerHeight = 375

      const { container } = render(
        <div className="landscape:max-h-screen landscape:overflow-y-auto">
          <TypographyTestComponent />
        </div>
      )

      const wrapper = container.firstChild as HTMLElement
      expect(wrapper?.className).toContain('landscape:max-h-screen')
    })
  })

  describe('Accessibility', () => {
    it('should maintain WCAG contrast ratios', () => {
      const { container } = render(<TypographyTestComponent />)

      // Check that text colors have sufficient contrast
      const bodyText = container.querySelector('p')
      const button = container.querySelector('button')

      // These would be tested with actual contrast calculation in production
      expect(bodyText).toBeTruthy()
      expect(button).toBeTruthy()
    })

    it('should support user font size preferences', () => {
      const { container } = render(
        <div className="text-base">
          <p>This text should scale with user preferences</p>
        </div>
      )

      // rem-based sizing respects user preferences
      const text = container.querySelector('p')
      expect(text?.parentElement?.className).toContain('text-base')
    })
  })
})
