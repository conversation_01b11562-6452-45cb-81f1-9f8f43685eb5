import React from 'react'

interface ExerciseItemSkeletonProps {
  className?: string
}

export function ExerciseItemSkeleton({
  className = '',
}: ExerciseItemSkeletonProps) {
  return (
    <div
      data-testid="exercise-item-skeleton"
      role="status"
      aria-busy="true"
      aria-label="Loading exercise"
      className={`rounded-lg bg-white p-4 shadow-sm animate-pulse ${className}`}
    >
      {/* Exercise name skeleton */}
      <div
        data-testid="exercise-name-skeleton"
        className="h-6 w-2/3 bg-gray-200 rounded"
      />
    </div>
  )
}
