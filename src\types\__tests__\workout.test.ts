import { describe, it, expect } from 'vitest'
import type {
  ExerciseWorkSetsModel,
  WorkoutLogSerieModel,
  MultiUnityWeight,
} from '../workout'

describe('ExerciseWorkSetsModel', () => {
  describe('Type Safety', () => {
    it('should accept valid ExerciseWorkSetsModel data', () => {
      const validExercise: ExerciseWorkSetsModel = {
        Id: 1,
        Label: 'Bench Press',
        BodyPartId: 2,
        IsFinished: false,
        IsNextExercise: true,
        isLoadingSets: false,
        setsError: null,
        lastSetsUpdate: Date.now(),
        sets: [],
      }

      expect(validExercise.Id).toBe(1)
      expect(validExercise.Label).toBe('Bench Press')
      expect(validExercise.sets).toEqual([])
    })

    it('should accept exercise with sets', () => {
      const weight: MultiUnityWeight = {
        Value: 100,
        Unit: 'lbs',
      }

      const set: WorkoutLogSerieModel = {
        ExerciseId: 1,
        Weight: weight,
        Reps: 10,
        RIR: 2,
        IsWarmups: false,
        IsNext: false,
        IsFinished: true,
      }

      const exercise: ExerciseWorkSetsModel = {
        Id: 1,
        Label: 'Squat',
        BodyPartId: 3,
        IsFinished: false,
        IsNextExercise: false,
        isLoadingSets: false,
        setsError: null,
        lastSetsUpdate: Date.now(),
        sets: [set],
      }

      expect(exercise.sets).toHaveLength(1)
      expect(exercise.sets[0].Reps).toBe(10)
    })
  })

  describe('Default Values', () => {
    it('should handle minimal required properties', () => {
      const minimalExercise: ExerciseWorkSetsModel = {
        Id: 1,
        Label: 'Exercise',
        BodyPartId: 1,
        IsFinished: false,
        IsNextExercise: false,
        isLoadingSets: false,
        setsError: null,
        lastSetsUpdate: 0,
        sets: [],
      }

      expect(minimalExercise.isLoadingSets).toBe(false)
      expect(minimalExercise.setsError).toBeNull()
      expect(minimalExercise.sets).toEqual([])
    })
  })

  describe('Loading State Transitions', () => {
    it('should transition from idle to loading', () => {
      const exercise: ExerciseWorkSetsModel = {
        Id: 1,
        Label: 'Deadlift',
        BodyPartId: 4,
        IsFinished: false,
        IsNextExercise: false,
        isLoadingSets: false,
        setsError: null,
        lastSetsUpdate: 0,
        sets: [],
      }

      // Simulate loading start
      exercise.isLoadingSets = true
      expect(exercise.isLoadingSets).toBe(true)
      expect(exercise.setsError).toBeNull()
    })

    it('should transition from loading to success', () => {
      const exercise: ExerciseWorkSetsModel = {
        Id: 1,
        Label: 'Pull-up',
        BodyPartId: 5,
        IsFinished: false,
        IsNextExercise: false,
        isLoadingSets: true,
        setsError: null,
        lastSetsUpdate: 0,
        sets: [],
      }

      // Simulate successful load
      const newSet: WorkoutLogSerieModel = {
        ExerciseId: 1,
        Weight: { Value: 0, Unit: 'lbs' },
        Reps: 8,
        RIR: 1,
        IsWarmups: false,
        IsNext: false,
        IsFinished: true,
      }

      exercise.isLoadingSets = false
      exercise.sets = [newSet]
      exercise.lastSetsUpdate = Date.now()

      expect(exercise.isLoadingSets).toBe(false)
      expect(exercise.sets).toHaveLength(1)
      expect(exercise.lastSetsUpdate).toBeGreaterThan(0)
    })

    it('should transition from loading to error', () => {
      const exercise: ExerciseWorkSetsModel = {
        Id: 1,
        Label: 'Dips',
        BodyPartId: 2,
        IsFinished: false,
        IsNextExercise: false,
        isLoadingSets: true,
        setsError: null,
        lastSetsUpdate: 0,
        sets: [],
      }

      // Simulate error
      exercise.isLoadingSets = false
      exercise.setsError = 'Failed to load sets'

      expect(exercise.isLoadingSets).toBe(false)
      expect(exercise.setsError).toBe('Failed to load sets')
      expect(exercise.sets).toEqual([])
    })
  })

  describe('Partial Data Handling', () => {
    it('should handle exercise without sets gracefully', () => {
      const exercise: ExerciseWorkSetsModel = {
        Id: 1,
        Label: 'New Exercise',
        BodyPartId: 1,
        IsFinished: false,
        IsNextExercise: false,
        isLoadingSets: false,
        setsError: null,
        lastSetsUpdate: Date.now(),
        sets: [],
      }

      expect(exercise.sets).toEqual([])
      expect(exercise.setsError).toBeNull()
    })

    it('should handle partial set data', () => {
      const partialSet: WorkoutLogSerieModel = {
        ExerciseId: 1,
        Weight: { Value: 50, Unit: 'kg' },
        Reps: 0, // No reps yet
        IsWarmups: true,
        IsNext: true,
        IsFinished: false,
      }

      const exercise: ExerciseWorkSetsModel = {
        Id: 1,
        Label: 'Warm-up Exercise',
        BodyPartId: 1,
        IsFinished: false,
        IsNextExercise: true,
        isLoadingSets: false,
        setsError: null,
        lastSetsUpdate: Date.now(),
        sets: [partialSet],
      }

      expect(exercise.sets[0].Reps).toBe(0)
      expect(exercise.sets[0].IsFinished).toBe(false)
      expect(exercise.sets[0].RIR).toBeUndefined()
    })

    it('should preserve existing sets when error occurs', () => {
      const existingSet: WorkoutLogSerieModel = {
        ExerciseId: 1,
        Weight: { Value: 100, Unit: 'lbs' },
        Reps: 10,
        IsWarmups: false,
        IsNext: false,
        IsFinished: true,
      }

      const exercise: ExerciseWorkSetsModel = {
        Id: 1,
        Label: 'Bench Press',
        BodyPartId: 2,
        IsFinished: false,
        IsNextExercise: false,
        isLoadingSets: false,
        setsError: 'Network error',
        lastSetsUpdate: Date.now(),
        sets: [existingSet], // Preserve existing data
      }

      expect(exercise.sets).toHaveLength(1)
      expect(exercise.setsError).toBe('Network error')
      expect(exercise.sets[0].Weight.Value).toBe(100)
    })
  })
})
