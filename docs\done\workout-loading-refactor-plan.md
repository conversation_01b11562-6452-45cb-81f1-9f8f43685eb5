# Workout Loading Refactor Plan

## Progress Summary

**Status:** 100% Complete (12/12 prompts implemented) ✅

### Completed (Phase 1 - Foundation):

- ✅ Prompt 1: ExerciseWorkSetsModel Type and Tests
- ✅ Prompt 2: Exercise Loading State Manager
- ✅ Prompt 3: Progressive Set Loader
- ✅ Prompt 4: Cache-Safe Workout Store Tests
- ✅ Prompt 5: Exercise Card with Progressive Loading
- ✅ Prompt 6: Set List Component

### Completed (Phase 2 - Integration):

- ✅ Prompt 7: Refactor useWorkout Hook - Part 1 (Data Structure)
  - Implemented direct ExerciseWorkSetsModel[] structure
  - Removed migration complexity per KISS principle
  - Progressive loading fully functional
  - Clean, simple implementation

- ✅ Prompt 8: Refactor useWorkout Hook - Part 2 (Loading Logic)
  - Implemented progressive loading for sets AND recommendations
  - Added loadingRecommendations tracking for all exercises
  - Fixed loadingStates.recommendation to reflect all exercise loading

- ✅ Prompt 9: Add Retry Logic with Exponential Backoff
  - Already implemented in SetLoader and utils/retry.ts
  - Full test coverage (28 tests) for retry utilities
  - Supports configurable retries, jitter, and abort capability

- ✅ Prompt 10: Update WorkoutOverview Component
  - Integrated ExerciseCard components for hierarchical display
  - Added pull-to-refresh functionality
  - Updated all tests to use new data structure (all passing)

- ✅ Prompt 11: Implement Error Boundaries
  - Created WorkoutErrorBoundary component with workout-specific error handling
  - Network and API error detection with tailored messages
  - Retry functionality with proper state reset
  - Full test coverage (21 tests passing)

- ✅ Prompt 12: Final Integration and Cleanup
  - Fixed TypeScript compilation errors in test files
  - Removed deprecated TODOs and cleaned up imports
  - Created comprehensive E2E test for workout loading flow
  - Verified performance targets are met
  - Updated all documentation

### Key Achievements:

- All foundation components built and tested (77 tests passing)
- Cache-safe patterns verified to prevent data loss
- Mobile-first UI enhancements (pull-to-refresh, touch feedback)
- Progressive loading infrastructure ready for integration

### Implementation Notes:

- Simplified Prompt 7 implementation based on user feedback
- Direct implementation without migration complexity (KISS principle)
- useWorkout hook now directly uses ExerciseWorkSetsModel[] structure
- Progressive loading fully integrated and functional
- All tests passing for implemented components

## Overview

This plan addresses the workout loading issues where workouts disappear after showing "Checking for updates". We'll implement a robust hierarchical loading system inspired by the mobile app's ExerciseWorkSetsModel pattern.

## Current Issues

1. **Data Loss on Refresh**: Cached workout data is cleared when API calls fail
2. **Poor Error Handling**: Any API failure results in "No workout available"
3. **Inefficient Loading**: All data must be loaded before showing anything
4. **No Granular Loading**: Can't show exercises while loading sets

## Solution Architecture

### Phase 1: Data Model Foundation

- Create `ExerciseWorkSetsModel` type that combines exercise with its sets
- Implement proper null safety and default values
- Add loading states per exercise and per set

### Phase 2: Hierarchical Loading System

- Load exercises immediately from cache/API
- Load sets for each exercise progressively
- Show exercise cards with set skeletons during loading

### Phase 3: Robust Error Handling

- Never clear valid cached data on API errors
- Implement retry logic with exponential backoff
- Show stale data with "offline" indicator rather than empty state

### Phase 4: Granular UI Updates

- Exercise-level skeleton loading
- Set-level skeleton loading within each exercise
- Progressive enhancement as data loads

## Implementation Prompts

### Prompt 1: Create ExerciseWorkSetsModel Type and Tests ✅ COMPLETED

Create a TypeScript type called ExerciseWorkSetsModel that mirrors the mobile app's data structure. This type should:

1. Extend or contain an array of WorkoutLogSerieModel (sets)
2. Include exercise properties: Id, Label, BodyPartId, IsFinished, IsNextExercise
3. Add loading state properties: isLoadingSets, setsError, lastSetsUpdate
4. Include methods or helpers for common operations

Also create comprehensive unit tests that verify:

- Type safety and proper typing
- Default values are correctly set
- Loading state transitions work correctly
- The model can handle partial data gracefully

Place the type in src/types/workout.ts and tests in src/types/**tests**/workout.test.ts

### Prompt 2: Implement Exercise Loading State Manager ✅ COMPLETED

Create an ExerciseLoadingStateManager class that tracks loading states for individual exercises. This should:

1. Track loading states per exercise ID
2. Support operations: startLoading, completeLoading, failLoading
3. Provide getLoadingState and isLoading helpers
4. Clean up old states to prevent memory leaks

Write comprehensive tests that verify:

- State transitions are atomic
- Multiple exercises can be tracked simultaneously
- Failed states include error information
- Memory cleanup works correctly

Place the implementation in src/utils/exerciseLoadingState.ts with tests in the **tests** folder.

### Prompt 3: Build Progressive Set Loader ✅ COMPLETED

Create a SetLoader class that implements progressive loading of sets for exercises. Requirements:

1. Load sets for a single exercise with retry logic
2. Support batch loading multiple exercises' sets
3. Implement exponential backoff on failures (max 3 retries)
4. Cache successful loads with TTL of 5 minutes
5. Return partial data if some sets load successfully

Include tests for:

- Single exercise set loading
- Batch loading with mixed success/failure
- Retry logic with backoff timing
- Cache hit/miss scenarios
- Partial data handling

Implementation in src/api/setLoader.ts with corresponding tests.

### Prompt 4: Create Cache-Safe Workout Store ✅ COMPLETED

Refactor the workout store to implement cache-safe operations. Changes needed:

1. Never clear cached workout data on API errors
2. Add separate clearCache action (user-initiated only)
3. Implement stale-while-revalidate pattern
4. Add isStale and lastSuccessfulFetch timestamps
5. Support partial updates (update only what changed)

Write tests to verify:

- Cache persists through API failures
- Stale data is marked but not removed
- Partial updates don't overwrite good data
- Clear cache only works when explicitly called

Update src/stores/workoutStore.ts and add new tests.

### Prompt 5: Build Exercise Card with Progressive Loading ✅ COMPLETED

Create an ExerciseCard component that displays exercise info with progressive set loading:

1. Show exercise name/info immediately
2. Display set skeletons while loading
3. Show actual sets as they load
4. Handle error states gracefully
5. Support pull-to-refresh gesture

Component features:

- Optimistic UI updates
- Smooth skeleton to content transitions
- Error retry button per exercise
- Accessibility with proper ARIA labels

Tests should cover:

- Initial loading state
- Progressive set appearance
- Error state handling
- User interactions

Create in src/components/workout/ExerciseCard.tsx with tests.

### Prompt 6: Implement Set List Component ✅ COMPLETED

Build a SetList component to display sets within an ExerciseCard:

1. Render WorkoutLogSerieModel items
2. Show weight, reps, and other set data
3. Support skeleton loading for individual sets
4. Handle empty state (no sets yet)
5. Enable set interactions (tap to edit)

Requirements:

- Smooth animations for set appearance
- Proper touch targets (44px minimum)
- Loading shimmer effect
- Graceful degradation

Include tests for:

- Various set configurations
- Loading states
- Empty states
- User interactions

Place in src/components/workout/SetList.tsx with tests.

### Prompt 7: Refactor useWorkout Hook - Part 1 (Data Structure) ✅ COMPLETED

Refactored useWorkout hook to use the new hierarchical loading pattern with simplified implementation:

1. ✅ Replaced flat exercise array with ExerciseWorkSetsModel[]
2. ✅ Integrated ExerciseLoadingStateManager
3. ✅ Updated type definitions throughout
4. ✅ Direct implementation without migration complexity
5. ✅ Progressive loading fully functional

**Implementation Details**:

- Removed dual-track complexity per user feedback (KISS principle)
- Hook directly converts store exercises to ExerciseWorkSetsModel[]
- Progressive loading of sets works seamlessly
- Clean, maintainable code without migration helpers
- All existing functionality preserved

**Result**: Clean implementation that directly uses the modern data structure

### Prompt 8: Refactor useWorkout Hook - Part 2 (Loading Logic)

Continue useWorkout refactoring with new loading logic:

1. Implement progressive loading using SetLoader
2. Load exercises first, then sets per exercise
3. Update cache with partial data as it arrives
4. Handle failures without clearing existing data
5. Add refresh logic that preserves cache

Tests should verify:

- Progressive loading works correctly
- Partial updates don't break state
- Error handling preserves data
- Refresh maintains user context

Continue updating src/hooks/useWorkout.ts with new tests.

### Prompt 9: Add Retry Logic with Exponential Backoff

Implement a retry utility with exponential backoff for API calls:

1. Create retryWithBackoff function
2. Support configurable max retries (default 3)
3. Implement jitter to prevent thundering herd
4. Allow custom backoff calculation
5. Include abort capability

Features:

- Base delay: 1000ms
- Max delay: 30000ms
- Jitter: ±20% of calculated delay
- Exponential factor: 2

Write tests for:

- Successful retry after failures
- Max retry limit
- Backoff timing
- Abort functionality

Create src/utils/retry.ts with comprehensive tests.

### Prompt 10: Update WorkoutOverview Component

Refactor WorkoutOverview to use the new hierarchical loading system:

1. Use new ExerciseCard components
2. Remove full-page loading states
3. Show exercises immediately when cached
4. Implement pull-to-refresh properly
5. Add offline mode indicator

Changes needed:

- Replace flat exercise list with ExerciseCards
- Update loading logic to be progressive
- Improve error handling UI
- Add visual feedback for refresh

Tests should cover:

- Initial load experience
- Progressive enhancement
- Error scenarios
- User interactions

Update src/components/workout/WorkoutOverview.tsx with tests.

### Prompt 11: Implement Error Boundaries

Add React Error Boundaries to prevent cascading failures:

1. Create WorkoutErrorBoundary component
2. Implement fallback UI for workout section
3. Add error recovery actions
4. Log errors properly for debugging
5. Preserve as much UI as possible

Requirements:

- Graceful error messages
- Retry action button
- Preserve navigation and header
- Report errors to monitoring

Include tests for:

- Error catching
- Fallback UI rendering
- Recovery actions
- Error logging

Create src/components/ErrorBoundary/WorkoutErrorBoundary.tsx

### Prompt 12: Integration and Cleanup

Final integration step to wire everything together:

1. Update all imports to use new components
2. Remove old/deprecated code
3. Update tests for full integration
4. Add E2E test for complete flow
5. Verify performance targets are met

Tasks:

- Clean up unused imports
- Remove deprecated types
- Update documentation
- Add performance monitoring
- Create E2E test scenario

Final testing should verify:

- Full workout loading flow works
- Performance targets are met
- No regressions in functionality
- Error scenarios handled gracefully

This completes the refactoring implementation.

## Development Safety Guidelines

### Preventing System Crashes During Implementation

1. **Work in Smaller Chunks**
   - Make incremental changes instead of large file modifications
   - Save and test frequently (after every 50-100 lines of code)
   - Split complex operations across multiple steps
   - Commit to git after each successful change

2. **Memory Management**
   - Avoid loading entire files into memory at once
   - Use streaming/chunking for large data operations
   - Clear caches periodically if working with large datasets
   - Monitor memory usage during development

3. **File Management**
   - Keep backup copies before major changes
   - Use version control commits more frequently
   - Split large files (>400 lines) into smaller modules
   - Avoid editing multiple large files simultaneously

4. **Testing Strategy**
   - Run unit tests after each small change
   - Use focused test runs instead of full suite when possible
   - Monitor for memory leaks in tests
   - Use `.only` to run specific test suites during development

5. **Development Practices**
   - Use multiple smaller terminal sessions vs one large session
   - Restart development server periodically
   - Clear node_modules/.cache if build issues occur
   - Close unused files in editor to reduce memory usage

## Implementation Order

Each prompt builds on the previous one:

1. Types and data models (foundation)
2. State management utilities
3. API and loading logic
4. Cache safety mechanisms
   5-6. UI components (bottom-up)
   7-8. Hook refactoring (core logic) - **Split into smaller sub-tasks**
5. Retry mechanism (reliability)
6. Main UI update (integration)
7. Error handling (safety)
8. Final integration (completion)

## Success Criteria

1. Workout data persists through refreshes
2. Exercises show immediately if cached
3. Sets load progressively per exercise
4. API failures don't clear valid cached data
5. Clear visual feedback for loading/error states

## Performance Targets

- Initial exercise list display: < 50ms (from cache)
- Progressive set loading: < 100ms per exercise
- Full workout load: < 2s on 3G connection
- Zero data loss on transient failures

## Test Coverage Requirements

- Unit tests for all new types and utilities
- Integration tests for data loading flows
- E2E tests for full workout loading scenarios
- Error scenario coverage > 90%
