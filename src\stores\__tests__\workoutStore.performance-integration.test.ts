import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useWorkoutStore } from '../workoutStore'
import { PerformanceMonitor } from '@/utils/performance'

// Mock the PerformanceMonitor
vi.mock('@/utils/performance', () => ({
  PerformanceMonitor: {
    trackCacheMetrics: vi.fn(),
  },
}))

describe('WorkoutStore - Performance Integration', () => {
  beforeEach(() => {
    // Reset store to initial state
    useWorkoutStore.setState({
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      hasHydrated: false,
      cacheStats: {
        hits: 0,
        misses: 0,
        hitRate: 0,
        operationCount: 0,
        averageLatency: 0,
        totalLatency: 0,
        totalSize: 0,
        itemCount: 0,
        oldestDataAge: 0,
        freshDataCount: 0,
        staleDataCount: 0,
        hydrationTime: 0,
      },
    })

    // Clear all mocks
    vi.clearAllMocks()
  })

  it('should provide cache stats compatible with PerformanceMonitor', () => {
    // Given: Store with cache statistics
    useWorkoutStore.setState({
      hasHydrated: true,
      cacheStats: {
        hits: 25,
        misses: 5,
        hitRate: 25 / 30,
        operationCount: 30,
        averageLatency: 15 / 30,
        totalLatency: 15,
        totalSize: 0,
        itemCount: 0,
        oldestDataAge: 0,
        freshDataCount: 0,
        staleDataCount: 0,
        hydrationTime: 100,
      },
    })

    // When: Getting cache stats
    const store = useWorkoutStore.getState()
    const stats = store.getCacheStats()

    // Then: Stats should be in the correct format for PerformanceMonitor
    expect(stats).toMatchObject({
      hits: 25,
      misses: 5,
      hitRate: 25 / 30,
      operationCount: 30,
      averageLatency: 15 / 30,
      totalLatency: 15,
      totalSize: expect.any(Number),
      itemCount: 0,
      oldestDataAge: expect.any(Number),
      freshDataCount: 0,
      staleDataCount: 0,
      hydrationTime: 100,
    })

    // Verify it can be passed to PerformanceMonitor
    PerformanceMonitor.trackCacheMetrics(stats)
    expect(PerformanceMonitor.trackCacheMetrics).toHaveBeenCalledWith(stats)
  })

  it('should track cache operations and provide updated metrics', () => {
    // Given: Store with cached data
    const store = useWorkoutStore.getState()
    store.setCachedUserProgramInfo({
      RecommendedProgram: {
        Id: 1,
        Label: 'Beginner Program',
        RemainingToLevelUp: 10,
        IconUrl: 'https://example.com/icon.png',
      },
      NextWorkoutTemplate: {
        Id: 1,
        Label: 'Push Day',
        IsSystemExercise: false,
        ScheduledDate: '2024-01-01',
        Exercises: [],
      },
      WorkoutTemplates: [],
    })
    store.setHasHydrated(true)

    // When: Performing cache operations
    store.getCachedUserProgramInfo() // Hit
    store.getCachedUserWorkouts() // Miss
    store.getCachedTodaysWorkout() // Miss

    // Then: Metrics should be updated
    const stats = store.getCacheStats()
    expect(stats.hits).toBe(1)
    expect(stats.misses).toBe(2)
    expect(stats.hitRate).toBeCloseTo(1 / 3, 2)
    expect(stats.operationCount).toBe(3)

    // Verify format is compatible with PerformanceMonitor
    PerformanceMonitor.trackCacheMetrics(stats)
    expect(PerformanceMonitor.trackCacheMetrics).toHaveBeenCalledWith(
      expect.objectContaining({
        hits: 1,
        misses: 2,
        hitRate: expect.any(Number),
        averageLatency: expect.any(Number),
        totalSize: expect.any(Number),
        itemCount: expect.any(Number),
      })
    )
  })

  it('should provide cache health information', () => {
    // Given: Store with poor cache performance
    useWorkoutStore.setState({
      hasHydrated: true,
      cacheStats: {
        hits: 2,
        misses: 18,
        hitRate: 2 / 20,
        operationCount: 20,
        averageLatency: 40 / 20,
        totalLatency: 40,
        totalSize: 0,
        itemCount: 0,
        oldestDataAge: 0,
        freshDataCount: 0,
        staleDataCount: 0,
        hydrationTime: 50,
      },
    })

    // When: Getting cache health
    const store = useWorkoutStore.getState()
    const health = store.getCacheHealth()

    // Then: Should detect poor cache performance
    expect(health.isHealthy).toBe(false)
    expect(health.warnings).toContain('Low cache hit rate')

    // Cache stats should still be available for performance monitoring
    const stats = store.getCacheStats()
    expect(stats.hitRate).toBe(0.1) // 10% hit rate
  })

  it('should reset cache statistics', () => {
    // Given: Store with existing stats
    useWorkoutStore.setState({
      hasHydrated: true,
      cacheStats: {
        hits: 100,
        misses: 50,
        hitRate: 100 / 150,
        operationCount: 150,
        averageLatency: 75 / 150,
        totalLatency: 75,
        totalSize: 0,
        itemCount: 0,
        oldestDataAge: 0,
        freshDataCount: 0,
        staleDataCount: 0,
        hydrationTime: 200,
      },
    })

    // When: Resetting cache stats
    const store = useWorkoutStore.getState()
    store.resetCacheStats()

    // Then: Stats should be reset (except hydrationTime)
    const stats = store.getCacheStats()
    expect(stats.hits).toBe(0)
    expect(stats.misses).toBe(0)
    expect(stats.operationCount).toBe(0)
    expect(stats.totalLatency).toBe(0)
    expect(stats.hitRate).toBe(0)
    expect(stats.averageLatency).toBe(0)
  })

  it('should provide debug logging for development', () => {
    // Given: Development environment
    vi.stubEnv('NODE_ENV', 'development')
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation()

    // When: Logging cache contents
    const store = useWorkoutStore.getState()
    store.logCacheContents()

    // Then: Should log cache stats and contents
    expect(consoleSpy).toHaveBeenCalledWith('Cache Stats:', expect.any(Object))
    expect(consoleSpy).toHaveBeenCalledWith(
      'Cache Contents:',
      expect.any(Object)
    )

    // Cleanup
    consoleSpy.mockRestore()
    // NODE_ENV will be restored automatically by vitest
  })
})
