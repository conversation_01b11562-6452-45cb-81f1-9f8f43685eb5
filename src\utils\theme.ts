import { themeConfig } from '@/config/theme'
import { cn } from '@/lib/utils'

/**
 * Generate theme-aware component classes
 */
export function getThemeClasses(
  component: keyof typeof themeConfig.components,
  variant?: string,
  state?: 'base' | 'hover' | 'active' | 'disabled'
) {
  const componentConfig = themeConfig.components[component]

  if ('variants' in componentConfig && variant) {
    const variantConfig =
      componentConfig.variants[variant as keyof typeof componentConfig.variants]
    if (!variantConfig) return ''

    // Return specific state or all states
    if (state) {
      return variantConfig[state] || ''
    }

    // Return all states combined
    return cn(
      variantConfig.base,
      variantConfig.hover,
      variantConfig.active,
      variantConfig.disabled
    )
  }

  // For non-variant components like card
  if ('base' in componentConfig) {
    if (state === 'hover' && 'hover' in componentConfig) {
      return componentConfig.hover as string
    }
    return componentConfig.base
  }

  return ''
}

/**
 * Get semantic color class
 */
export function getSemanticColor(
  type: 'error' | 'warning' | 'success' | 'info',
  opacity?: number
) {
  if (opacity) {
    return `${type}/${opacity}`
  }
  return type
}

/**
 * Get dynamic theme value
 */
export function getThemeValue(path: string): string {
  const parts = path.split('.')
  let value: Record<string, unknown> | string | undefined = themeConfig.themes[
    themeConfig.defaultTheme
  ] as Record<string, unknown>

  parts.forEach((part) => {
    if (typeof value === 'object' && value !== null) {
      value = value[part] as Record<string, unknown> | string | undefined
    } else {
      value = undefined
    }
  })

  return typeof value === 'string' ? value : ''
}
