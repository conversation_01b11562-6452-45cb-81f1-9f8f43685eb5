# Dr. Muscle X - Plan Review & Enhancement Prompts

## Overview

This document tracks enhancement prompts and fixes identified during development and user feedback of plan.md.

## Active Enhancement Prompts

### Prompt 15: Network Error Resolution & API Reliability

**Priority:** Critical
**Status:** In Progress

**Problem:**

- Users experiencing "Network error" when logging in with correct credentials
- Generic error messages don't help users understand specific issues
- No environment-specific API configuration

**Solution:**

- ✅ Enhanced error messages with specific network failure details
- ✅ Environment-based API URL configuration (localhost for dev)
- ✅ Improved retry logic with user-friendly messages
- 🔄 Add connection testing utility
- 📋 Add comprehensive network error testing

**Acceptance Criteria:**

- [ ] Users see helpful error messages (e.g., "Check internet connection")
- [ ] Development environment uses localhost API
- [ ] Network failures retry automatically with backoff
- [ ] Tests cover various network failure scenarios

### Prompt 16: OAuth Authentication Integration

**Priority:** High
**Status:** UI Added, Backend Integration Needed

**Problem:**

- Login page missing "Continue with Google" and Apple sign-in options
- Users expect modern OAuth authentication methods
- Current email/password only approach limits user adoption

**Solution:**

- ✅ Added Google and Apple OAuth buttons to LoginForm
- ✅ Added proper styling and accessibility
- 📋 Implement Google OAuth integration with backend
- 📋 Implement Apple Sign In integration with backend
- 📋 Add OAuth error handling and fallback flows

**Acceptance Criteria:**

- [x] OAuth buttons visible on login page
- [ ] Google OAuth fully functional
- [ ] Apple Sign In fully functional
- [ ] Proper error handling for OAuth failures
- [ ] Seamless fallback to email/password

### Prompt 20: Plate Calculator Implementation

**Priority:** Medium
**Status:** Temporarily Disabled

**Problem:**

- Plate calculator functionality currently uses placeholder logic
- UI exists but calculations are not accurate
- Need to implement actual calculation logic from mobile app

**Current State:**

- ✅ Plate calculator UI commented out in SetLoggingModal.tsx and SetInputs.tsx
- ✅ Placeholder import from `@/lib/plateCalculator` commented out
- 📋 Need to copy actual calculation logic from mobile app codebase

**Mobile App Reference:**

The mobile app contains proper plate calculation logic that accounts for:

- Different weight standards (lbs/kg)
- Available plate weights (45lb, 25lb, 10lb, 5lb, 2.5lb, etc.)
- Barbell weight (45lb standard Olympic bar)
- Optimal plate combinations to minimize plate count

**Implementation Required:**

- [ ] Copy plate calculation algorithm from mobile app
- [ ] Implement proper `calculatePlates()` function in `@/lib/plateCalculator.ts`
- [ ] Handle both metric (kg) and imperial (lbs) weight systems
- [ ] Add unit tests for plate calculations
- [ ] Un-comment plate calculator UI components
- [ ] Test plate calculator with various weight combinations

**Acceptance Criteria:**

- [ ] Accurate plate calculations matching mobile app behavior
- [ ] Support for both kg and lbs weight systems
- [ ] Minimal plate count optimization
- [ ] Proper handling of edge cases (weights that can't be achieved)
- [ ] User-friendly display of required plates
- [ ] Tests covering various weight scenarios

## Implementation Notes

### Network Error Fixes Applied:

1. **Enhanced API Client** (`src/api/client.ts`):
   - Added environment detection for API URL
   - Improved error messages based on error type
   - Better retry logic with specific failure reasons

2. **Error Message Improvements**:
   - "No internet connection" for offline state
   - "Connection timeout" for timeout errors
   - "Unable to connect to servers" for connection refused

### OAuth UI Added:

1. **LoginForm Updates** (`src/components/LoginForm.tsx`):
   - Added visual divider "Or continue with"
   - Google OAuth button with official Google colors and icon
   - Apple Sign In button with Apple branding
   - Temporary alerts explaining features coming soon

## Testing Requirements

### Network Error Testing:

- [ ] Test offline scenarios
- [ ] Test timeout scenarios
- [ ] Test connection refused scenarios
- [ ] Test CORS errors
- [ ] Test invalid API endpoints

### OAuth Testing:

- [ ] Test OAuth button rendering
- [ ] Test OAuth error handling
- [ ] Test OAuth success flows
- [ ] Test OAuth cancellation
- [ ] Test fallback to email/password

## Future Enhancements

### Prompt 17: Advanced Authentication Features

- Two-factor authentication
- Biometric authentication (Face ID, Touch ID)
- Social login with Facebook, Twitter
- Enterprise SSO integration

### Prompt 18: Network Optimization

- Intelligent retry strategies
- Offline queue improvements
- Background sync capabilities
- Network quality detection

## Notes

- OAuth backend integration requires API endpoint development
- Consider using libraries like `next-auth` for OAuth implementation
- Network error improvements should reduce support tickets significantly

### Prompt 19: Other todos

On login page:
Add logo
Change font style for "Dr. Muscle X - Login to continue your workout" to a modern pair that matches the "X" angle and slogan.
Replace "Login to continue your workout" with "World's Fastest AI Personal Trainer"

On open exercise page for the fist time, I see loader. Show skeletton loading instead.
