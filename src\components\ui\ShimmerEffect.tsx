'use client'

import React from 'react'

export interface ShimmerEffectProps {
  /** Width of the shimmer area */
  width?: string
  /** Height of the shimmer area */
  height?: string
  /** Duration of the shimmer animation in seconds */
  duration?: number
  /** Delay before starting animation in milliseconds */
  delay?: number
  /** Custom className for additional styling */
  className?: string
  /** ARIA label for accessibility */
  ariaLabel?: string
}

/**
 * ShimmerEffect component that creates a moving gradient shine effect
 * Used for progressive loading states in AnimatedCounter and other components
 */
export function ShimmerEffect({
  width = '100%',
  height = '100%',
  duration = 1.5,
  delay = 0,
  className = '',
  ariaLabel = 'Loading',
}: ShimmerEffectProps) {
  return (
    <div
      className={`shimmer-wrapper ${className}`}
      style={{
        width,
        height,
        position: 'relative',
        overflow: 'hidden',
      }}
      role="status"
      aria-label={ariaLabel}
      aria-busy="true"
    >
      <span className="sr-only">{ariaLabel}</span>
      <div
        className="shimmer-effect"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: `linear-gradient(
            90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 20%,
            rgba(255, 255, 255, 0.5) 50%,
            rgba(255, 255, 255, 0.3) 80%,
            transparent 100%
          )`,
          animation: `shimmer ${duration}s ease-in-out infinite`,
          animationDelay: `${delay}ms`,
        }}
        aria-hidden="true"
      />
      <style jsx>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }

        @media (prefers-reduced-motion: reduce) {
          .shimmer-effect {
            animation: none !important;
            opacity: 0.5;
          }
        }
      `}</style>
    </div>
  )
}

/**
 * ShimmerOverlay component that can be positioned over content
 * Designed to overlay on numbers in AnimatedCounter
 */
export function ShimmerOverlay({
  show = false,
  duration = 1.5,
  delay = 0,
  className = '',
  ariaLabel = 'Loading value',
}: {
  show?: boolean
  duration?: number
  delay?: number
  className?: string
  ariaLabel?: string
}) {
  if (!show) return null

  return (
    <div
      className={`absolute inset-0 pointer-events-none transition-opacity duration-300 ${
        show ? 'opacity-100' : 'opacity-0'
      } ${className}`}
      data-testid="shimmer-overlay"
      aria-hidden="true"
    >
      <ShimmerEffect duration={duration} delay={delay} ariaLabel={ariaLabel} />
    </div>
  )
}
