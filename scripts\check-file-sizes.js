#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

const MAX_LINES = 225;
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];
const IGNORE_PATTERNS = [
  '**/node_modules/**',
  '**/dist/**',
  '**/build/**',
  '**/.next/**',
  '**/coverage/**',
  '**/*.test.*',
  '**/*.spec.*',
  '**/*.d.ts',
  '**/generated/**'
];

function countLines(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8');
  return content.split('\n').length;
}

function checkFileSizes() {
  const pattern = `src/**/*{${EXTENSIONS.join(',')}}`;
  const files = glob.sync(pattern, { ignore: IGNORE_PATTERNS });
  
  const oversizedFiles = [];
  
  files.forEach(file => {
    const lineCount = countLines(file);
    if (lineCount > MAX_LINES) {
      oversizedFiles.push({ file, lineCount });
    }
  });
  
  if (oversizedFiles.length > 0) {
    console.error('\n❌ Files exceeding the maximum line limit of', MAX_LINES, 'lines:\n');
    oversizedFiles.forEach(({ file, lineCount }) => {
      console.error(`  ${file}: ${lineCount} lines (${lineCount - MAX_LINES} over limit)`);
    });
    console.error('\nPlease split these files into smaller, more manageable modules.');
    console.error('See docs/architecture.md for component structure guidelines.\n');
    return false;
  }
  
  console.log('✅ All files are within the line limit of', MAX_LINES, 'lines');
  return true;
}

// Run the check
const success = checkFileSizes();
process.exit(success ? 0 : 1);