import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import MockAdapter from 'axios-mock-adapter'
import { apiClient } from '@/api/client'
import { workoutApi } from '@/api/workouts'
import type {
  WorkoutTemplateGroupModel,
  RecommendationModel,
  WorkoutLogSerieModel,
} from '@/types'

// Create mock adapter
const mock = new MockAdapter(apiClient)

// Mock data
const mockWorkoutGroup: WorkoutTemplateGroupModel = {
  Name: 'Week 1',
  Workouts: [
    {
      Id: 'workout-1',
      Label: 'Push Day',
      Exercises: [
        {
          Id: 1,
          Name: 'Bench Press',
          TargetReps: 8,
          TargetWeight: { Mass: 100, MassUnit: 'lbs' },
          Path: 'chest/benchpress',
          IsWarmup: false,
          IsMedium: false,
          IsBodyweight: false,
          IsNormalSet: true,
          IsDeload: false,
          IsFinished: false,
          IsMediumFinished: false,
          IsEasy: false,
          IsFirstMedium: false,
          IsFirstEasy: false,
          BodyWeight: null,
          EquipmentId: 'barbell',
          TimeSinceLastSet: '00:00:00',
          PreviousExercise: null,
          SwapExerciseTargetPath: null,
          RecommendationInKgRange: null,
          FirstWorkSet: null,
          From1RM: null,
          OneRM: null,
          IsMultiUnity: false,
          IsRecommended: true,
          HasPastLogs: true,
          IsTimeBased: false,
          IsPlate: false,
        },
      ],
      IsFinished: false,
      RepsAtTop: false,
      IsQuickMode: false,
      RecommendationInKgRange: null,
      WorkoutInfo: '',
      MusclesUsed: 'Chest, Shoulders, Triceps',
      DayOfWeek: 1,
      WorkoutTemplateId: 'template-1',
    },
  ],
  Id: 1,
}

const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 8,
  Weight: {
    Lb: 105,
    Kg: 47.6,
  },
  OneRMProgress: 85,
  RecommendationInKg: 47.6,
  OneRMPercentage: 80,
  WarmUpReps1: 5,
  WarmUpReps2: 3,
  WarmUpWeightSet1: {
    Lb: 65,
    Kg: 29.5,
  },
  WarmUpWeightSet2: {
    Lb: 85,
    Kg: 38.6,
  },
  WarmUpsList: [],
  WarmupsCount: 2,
  RpRest: 120,
  NbPauses: 0,
  NbRepsPauses: 0,
  IsEasy: false,
  IsMedium: false,
  IsBodyweight: false,
  Increments: {
    Lb: 5,
    Kg: 2.5,
  },
  Max: {
    Lb: 225,
    Kg: 102,
  },
  Min: {
    Lb: 45,
    Kg: 20,
  },
  IsNormalSets: true,
  IsDeload: false,
  IsBackOffSet: false,
  BackOffSetWeight: {
    Lb: 0,
    Kg: 0,
  },
  IsMaxChallenge: false,
  IsLightSession: false,
}

describe('Workout API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    mock.reset()
  })

  describe('getTodaysWorkout', () => {
    it("should fetch today's workout successfully", async () => {
      // Given
      mock
        .onPost('/api/Workout/GetUserWorkoutTemplateGroup')
        .reply(200, { Data: [mockWorkoutGroup] })

      // When
      const result = await workoutApi.getTodaysWorkout()

      // Then
      expect(result).toEqual([mockWorkoutGroup])
      expect(mock.history.post[0].url).toBe(
        '/api/Workout/GetUserWorkoutTemplateGroup'
      )
    })

    it('should handle empty workout response', async () => {
      // Given
      mock
        .onPost('/api/Workout/GetUserWorkoutTemplateGroup')
        .reply(200, { Data: [] })

      // When
      const result = await workoutApi.getTodaysWorkout()

      // Then
      expect(result).toEqual([])
    })

    it('should handle API errors', async () => {
      // Given
      mock.onPost('/api/Workout/GetUserWorkoutTemplateGroup').reply(500)

      // When/Then
      await expect(workoutApi.getTodaysWorkout()).rejects.toThrow()
    })
  })

  describe('getWorkoutDetails', () => {
    it('should fetch workout details by ID', async () => {
      // Given
      const workoutId = 'workout-1'
      mock
        .onGet(`/api/Workout/GetWorkoutDetails/${workoutId}`)
        .reply(200, { Data: mockWorkoutGroup.Workouts[0] })

      // When
      const result = await workoutApi.getWorkoutDetails(workoutId)

      // Then
      expect(result).toEqual(mockWorkoutGroup.Workouts[0])
    })

    it('should handle workout not found', async () => {
      // Given
      const workoutId = 'non-existent'
      mock.onGet(`/api/Workout/GetWorkoutDetails/${workoutId}`).reply(404)

      // When/Then
      await expect(workoutApi.getWorkoutDetails(workoutId)).rejects.toThrow()
    })
  })

  describe('getExerciseRecommendation', () => {
    it('should fetch exercise recommendation', async () => {
      // Given
      const exerciseId = 1
      // Mock localStorage to return auth state with user email
      const mockAuthState = {
        user: { email: '<EMAIL>' },
        token: 'test-token',
      }
      // Use the global localStorage mock from setup.ts
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'drmuscle-auth') {
          return JSON.stringify(mockAuthState)
        }
        return null
      })

      mock
        .onPost(
          '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'
        )
        .reply(200, { StatusCode: 200, Result: mockRecommendation })

      // Mock workout store to provide workoutId
      const mockWorkoutState = {
        state: {
          workout: { Id: 123 },
          exercises: [
            {
              Id: exerciseId,
              SetStyle: 'Normal',
              IsFlexibility: false,
            },
          ],
        },
      }
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'drmuscle-auth') {
          return JSON.stringify(mockAuthState)
        }
        if (key === 'workout-storage') {
          return JSON.stringify(mockWorkoutState)
        }
        return null
      })

      // When
      const result = await workoutApi.getExerciseRecommendation(exerciseId)

      // Then
      expect(result).toEqual(mockRecommendation)
      expect(mock.history.post[0].data).toBe(
        JSON.stringify({
          Username: '<EMAIL>',
          ExerciseId: exerciseId,
          WorkoutId: 123,
          SetStyle: 'Normal',
          IsFlexibility: false,
        })
      )
    })

    it('should handle missing recommendation', async () => {
      // Given
      const exerciseId = 999
      // Mock localStorage to return auth state with user email
      const mockAuthState = {
        user: { email: '<EMAIL>' },
        token: 'test-token',
      }
      // Mock workout store to provide workoutId
      const mockWorkoutState = {
        state: {
          workout: { Id: 123 },
          exercises: [
            {
              Id: exerciseId,
              SetStyle: 'Normal',
              IsFlexibility: false,
            },
          ],
        },
      }
      vi.mocked(localStorage.getItem).mockImplementation((key) => {
        if (key === 'drmuscle-auth') {
          return JSON.stringify(mockAuthState)
        }
        if (key === 'workout-storage') {
          return JSON.stringify(mockWorkoutState)
        }
        return null
      })

      mock
        .onPost(
          '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'
        )
        .reply(200, { StatusCode: 200, Result: null })

      // When
      const result = await workoutApi.getExerciseRecommendation(exerciseId)

      // Then
      expect(result).toBeNull()
    })
  })

  describe('saveWorkoutSet', () => {
    it('should save workout set successfully', async () => {
      // Given
      const setData: WorkoutLogSerieModel = {
        ExerciseId: 1,
        Weight: { Lb: 100, Kg: 45.36 },
        Reps: 8,
        RIR: 2,
        IsWarmups: false,
        IsNext: false,
        IsFinished: true,
      }

      mock.onPost('/api/Exercise/AddWorkoutLogSerieNew').reply(200, {
        Result: true,
        Code: 'OK',
      })

      // When
      const result = await workoutApi.saveWorkoutSet(setData)

      // Then
      expect(result).toEqual({
        Result: true,
        Code: 'OK',
      })
      const sentData = JSON.parse(mock.history.post[0].data)
      expect(sentData.ExerciseId).toBe(setData.ExerciseId)
      expect(sentData.Weight).toEqual(setData.Weight)
      expect(sentData.Reps).toBe(setData.Reps)
    })

    it('should handle save failure', async () => {
      // Given
      const setData: WorkoutLogSerieModel = {
        ExerciseId: 1,
        Weight: { Lb: 100, Kg: 45.36 },
        Reps: 8,
        RIR: 2,
        IsWarmups: false,
        IsNext: false,
        IsFinished: true,
      }

      mock.onPost('/api/Exercise/AddWorkoutLogSerieNew').reply(500, {
        Message: 'Internal server error',
        ErrorMessage: 'Failed to save set',
      })

      // When/Then
      await expect(workoutApi.saveWorkoutSet(setData)).rejects.toThrow()
    })

    it('should accept bodyweight exercises with zero weight', async () => {
      // Given - bodyweight exercise with 0 weight
      const setData: WorkoutLogSerieModel = {
        ExerciseId: 1,
        Weight: { Lb: 0, Kg: 0 },
        Reps: 10,
        IsWarmups: false,
        IsNext: false,
        IsFinished: false,
      }

      mock.onPost('/api/Exercise/AddWorkoutLogSerieNew').reply(200, {
        Result: true,
        Code: 200,
      })

      // When
      const result = await workoutApi.saveWorkoutSet(setData)

      // Then
      expect(result).toEqual({
        Result: true,
        Code: 200,
      })
    })

    it('should reject exercises with missing weight', async () => {
      // Given - exercise with null weight
      const setData: WorkoutLogSerieModel = {
        ExerciseId: 1,
        Weight: null as any,
        Reps: 10,
        IsWarmups: false,
        IsNext: false,
        IsFinished: false,
      }

      // When/Then
      await expect(workoutApi.saveWorkoutSet(setData)).rejects.toThrow(
        'Invalid Weight data'
      )
    })

    it('should reject exercises with undefined weight values', async () => {
      // Given - exercise with undefined Lb or Kg
      const setData: WorkoutLogSerieModel = {
        ExerciseId: 1,
        Weight: { Lb: undefined as any, Kg: 0 },
        Reps: 10,
        IsWarmups: false,
        IsNext: false,
        IsFinished: false,
      }

      // When/Then
      await expect(workoutApi.saveWorkoutSet(setData)).rejects.toThrow(
        'Invalid Weight data'
      )
    })

    it('should accept non-zero weight for regular exercises', async () => {
      // Given
      const setData: WorkoutLogSerieModel = {
        ExerciseId: 1,
        Weight: { Lb: 0.1, Kg: 0.045 }, // Very small but non-zero weight
        Reps: 8,
        IsWarmups: false,
        IsNext: false,
        IsFinished: false,
      }

      mock.onPost('/api/Exercise/AddWorkoutLogSerieNew').reply(200, {
        Result: true,
        Code: 200,
      })

      // When
      const result = await workoutApi.saveWorkoutSet(setData)

      // Then
      expect(result).toEqual({
        Result: true,
        Code: 200,
      })
    })
  })

  describe('completeWorkout', () => {
    it('should complete workout successfully', async () => {
      // Given
      const workoutData = {
        WorkoutId: 'workout-1',
        StartTime: new Date(Date.now() - 3600000).toISOString(),
        EndTime: new Date().toISOString(),
        TotalSets: 12,
        TotalExercises: 3,
        Notes: 'Great workout!',
      }

      mock.onPost('/api/Workout/SaveWorkoutV3Pro').reply(200, {
        Result: true,
        Code: 200,
        ErrorMessage: null,
      })

      // When
      const result = await workoutApi.completeWorkout(workoutData)

      // Then
      expect(result).toEqual({
        Result: true,
        Code: 200,
        ErrorMessage: null,
        NextWorkoutDate: undefined,
      })
    })

    it('should handle completion failure', async () => {
      // Given
      const workoutData = {
        WorkoutId: 'workout-1',
        StartTime: new Date().toISOString(),
        EndTime: new Date().toISOString(),
        TotalSets: 0,
        TotalExercises: 0,
        Notes: '',
      }

      mock.onPost('/api/Workout/SaveWorkoutV3Pro').reply(400, {
        Result: false,
        Code: 400,
        ErrorMessage: 'No sets completed',
      })

      // When/Then
      await expect(workoutApi.completeWorkout(workoutData)).rejects.toThrow()
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      // Given
      mock.onPost('/api/Workout/GetUserWorkoutTemplateGroup').networkError()

      // When/Then
      await expect(workoutApi.getTodaysWorkout()).rejects.toThrow()
    }, 10000) // Increase timeout for retry logic

    it('should handle timeout errors', async () => {
      // Given
      mock.onPost('/api/Workout/GetUserWorkoutTemplateGroup').timeout()

      // When/Then
      await expect(workoutApi.getTodaysWorkout()).rejects.toThrow()
    }, 10000) // Increase timeout for retry logic

    it('should handle malformed responses', async () => {
      // Given - Return valid JSON but with unexpected structure
      mock
        .onPost('/api/Workout/GetUserWorkoutTemplateGroup')
        .reply(200, { invalidKey: 'invalid data' })

      // When
      const result = await workoutApi.getTodaysWorkout()

      // Then - Should return empty array when Data is missing
      expect(result).toEqual([])
    })
  })
})
