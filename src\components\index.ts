// Program components
export { ProgressRing } from './ProgressRing'
export type { ProgressRingProps } from './ProgressRing'

export { StatCard } from './StatCard'
export type { StatCardProps } from './StatCard'

// Temporarily disabled - component not currently used
// export { ProgramStatsGrid } from './ProgramStatsGrid'
// export type { ProgramStatsGridProps } from './ProgramStatsGrid'

export { ProgramDescription } from './ProgramDescription'
export type { ProgramDescriptionProps } from './ProgramDescription'

export { ProgramHeader } from './ProgramHeader'
export type { ProgramHeaderProps } from './ProgramHeader'

export { WelcomeHeader } from './WelcomeHeader'
export type { WelcomeHeaderProps } from './WelcomeHeader'

export { ProgramTags } from './ProgramTags'
export type { ProgramTagsProps } from './ProgramTags'

export { AnimatedCounter } from './AnimatedCounter'
export type { AnimatedCounterProps } from './AnimatedCounter'

export { WelcomeCard } from './WelcomeCard'
export { RecoveryProgress } from './RecoveryProgress'

// UI components
export { ShimmerEffect, ShimmerOverlay } from './ui/ShimmerEffect'
export type { ShimmerEffectProps } from './ui/ShimmerEffect'

// Branding components
export { Logo } from './Logo'
export type { LogoProps } from './Logo'

// Program error components
export { ProgramErrorState } from './program/ProgramErrorState'
export type { ProgramErrorStateProps } from './program/ProgramErrorState'

export { CompletedProgramState } from './program/CompletedProgramState'
export type { CompletedProgramStateProps } from './program/CompletedProgramState'

export { PartialDataError } from './program/PartialDataError'
export type { PartialDataErrorProps } from './program/PartialDataError'

// Program skeleton components
export {
  ProgramHeaderSkeleton,
  ProgressRingSkeleton,
  ProgramStatsSkeleton,
  ProgramDescriptionSkeleton,
  ProgramOverviewSkeleton,
} from './ProgramSkeletons'
