import { describe, it, expect, vi, beforeEach } from 'vitest'
import { getExerciseRecommendation } from '../workout'
import { apiClient } from '@/api/client'
import type { GetRecommendationForExerciseRequest } from '../workout'

vi.mock('@/api/client')
vi.mock('@/lib/auth-utils', () => ({
  getCurrentUserEmail: vi.fn(() => '<EMAIL>'),
}))

describe('getExerciseRecommendation - correct endpoints', () => {
  const mockRecommendation = {
    Series: 3,
    Reps: 10,
    Weight: { Kg: 50, Lb: 110 },
    WarmUpsList: [],
    RpRest: 90,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should use WithoutWarmupsNew endpoint for normal sets', async () => {
    const request: GetRecommendationForExerciseRequest = {
      Username: '<EMAIL>',
      ExerciseId: 123,
      WorkoutId: 456,
      SetStyle: 'Normal',
      IsFlexibility: false,
    }

    vi.mocked(apiClient.post).mockResolvedValueOnce({
      data: { StatusCode: 200, Result: mockRecommendation },
    })

    await getExerciseRecommendation(request)

    expect(apiClient.post).toHaveBeenCalledWith(
      '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
      request
    )
  })

  it('should use WithoutWarmupsNew endpoint for flexibility exercises', async () => {
    const request: GetRecommendationForExerciseRequest = {
      Username: '<EMAIL>',
      ExerciseId: 123,
      WorkoutId: 456,
      SetStyle: 'RestPause',
      IsFlexibility: true,
    }

    vi.mocked(apiClient.post).mockResolvedValueOnce({
      data: { StatusCode: 200, Result: mockRecommendation },
    })

    await getExerciseRecommendation(request)

    expect(apiClient.post).toHaveBeenCalledWith(
      '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
      request
    )
  })

  it('should use RestPause WithoutWarmupsNew endpoint for rest-pause sets', async () => {
    const request: GetRecommendationForExerciseRequest = {
      Username: '<EMAIL>',
      ExerciseId: 123,
      WorkoutId: 456,
      SetStyle: 'RestPause',
      IsFlexibility: false,
    }

    vi.mocked(apiClient.post).mockResolvedValueOnce({
      data: { StatusCode: 200, Result: mockRecommendation },
    })

    await getExerciseRecommendation(request)

    expect(apiClient.post).toHaveBeenCalledWith(
      '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew',
      request
    )
  })

  it('should handle API errors gracefully', async () => {
    const request: GetRecommendationForExerciseRequest = {
      Username: '<EMAIL>',
      ExerciseId: 123,
      WorkoutId: 456,
      SetStyle: 'Normal',
    }

    vi.mocked(apiClient.post).mockRejectedValueOnce(new Error('API Error'))

    const result = await getExerciseRecommendation(request)

    expect(result).toBeNull()
  })
})
