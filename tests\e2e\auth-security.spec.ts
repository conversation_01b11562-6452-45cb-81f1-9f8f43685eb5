/* eslint-disable no-await-in-loop, no-useless-escape */
import { test, expect, Page } from '@playwright/test'

test.describe('Authentication Security Tests', () => {
  let page: Page

  test.beforeEach(async ({ page: p }) => {
    page = p
    await page.goto('/')
  })

  test('should not expose tokens in console logs', async () => {
    const consoleLogs: string[] = []
    page.on('console', (msg) => {
      consoleLogs.push(msg.text())
    })

    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Check console logs for sensitive data
    const sensitivePatterns = [
      /token.*:.*[A-Za-z0-9]{20,}/i,
      /password.*:.*\w+/i,
      /bearer\s+[A-Za-z0-9\-._~+/]+=*/i,
      /refresh.*token.*:.*\w+/i,
    ]

    consoleLogs.forEach((log) => {
      sensitivePatterns.forEach((pattern) => {
        expect(log).not.toMatch(pattern)
      })
    })
  })

  test('should sanitize error messages', async () => {
    await page.goto('/login')

    // Inject XSS attempt in email
    const xssPayload = '<script>alert("XSS")</script>@test.com'
    await page.fill('input[type="email"]', xssPayload)
    await page.fill('input[type="password"]', 'password123')

    // Mock API to return error with user input
    await page.route('**/api/token', (route) => {
      route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          error: `Invalid email: ${xssPayload}`,
        }),
      })
    })

    await page.click('button[type="submit"]')

    // Check that script tag is not executed
    const alertDialog = page
      .waitForEvent('dialog', { timeout: 5000 })
      .catch(() => null)
    const result = await alertDialog
    expect(result).toBeNull()

    // Error should be sanitized
    const errorText = await page.locator('[role="alert"]').textContent()
    expect(errorText).not.toContain('<script>')
    expect(errorText).not.toContain('</script>')
  })

  test('should validate OAuth state parameter', async () => {
    await page.goto('/login')

    // Intercept OAuth redirect
    await page.route('**/oauth/callback**', async (route) => {
      const url = new URL(route.request().url())
      const state = url.searchParams.get('state')

      // Verify state parameter exists and is properly formatted
      expect(state).toBeTruthy()
      expect(state?.length).toBeGreaterThan(20)

      route.continue()
    })

    // Click OAuth sign in
    await page.click('button:has-text("Continue with Google")')

    // Wait for popup
    const popupPromise = page.waitForEvent('popup')
    const popup = await popupPromise
    await popup.close()
  })

  test('should prevent CSRF attacks', async () => {
    // Login first
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Try to make a request without proper headers
    const response = await page.evaluate(async () => {
      try {
        const res = await fetch('/api/userStats', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            // Missing CSRF token
          },
          body: JSON.stringify({ malicious: 'data' }),
        })
        return { status: res.status, ok: res.ok }
      } catch (error) {
        return { error: error.message }
      }
    })

    // Should be rejected without proper CSRF protection
    expect(response.ok).toBeFalsy()
  })

  test('should not store sensitive data in localStorage', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Check localStorage
    const localStorageData = await page.evaluate(() => {
      const data: Record<string, any> = {}
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key) {
          data[key] = localStorage.getItem(key)
        }
      }
      return JSON.stringify(data)
    })

    // Should not contain passwords or sensitive tokens in plain text
    expect(localStorageData).not.toContain('Dr123456')
    expect(localStorageData).not.toContain('"password"')

    // Tokens should be present but we're checking they're not in an insecure format
    const hasToken = localStorageData.includes('token')
    expect(hasToken).toBeTruthy()
  })

  test('should handle XSS in OAuth responses', async () => {
    await page.goto('/login')

    // Mock OAuth response with XSS attempt
    await page.route('**/api/oauth/**', (route) => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          token: 'valid-token',
          user: {
            id: 'user-123',
            name: '<img src=x onerror=alert("XSS")>',
            email: '<EMAIL>',
          },
        }),
      })
    })

    // Simulate OAuth callback
    await page.evaluate(() => {
      window.postMessage(
        {
          type: 'oauth-success',
          provider: 'google',
          data: {
            userId: 'google-123',
            email: '<EMAIL>',
            name: '<img src=x onerror=alert("XSS")>',
          },
        },
        '*'
      )
    })

    // Check no alert is triggered
    const alertDialog = page
      .waitForEvent('dialog', { timeout: 5000 })
      .catch(() => null)
    const result = await alertDialog
    expect(result).toBeNull()

    // Name should be escaped when displayed
    await page.waitForURL('/program')
    const userNameElement = await page
      .locator('[data-testid="user-name"]')
      .first()
    const innerHTML = await userNameElement.innerHTML()
    expect(innerHTML).not.toContain('<img')
    expect(innerHTML).not.toContain('onerror')
  })

  test('should validate redirect URLs', async () => {
    // Try to login with malicious redirect
    await page.goto('/login?redirect=https://evil.com')

    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Should redirect to safe default, not the malicious URL
    await expect(page).toHaveURL(/^https?:\/\/[^\/]+\/program$/)
    expect(page.url()).not.toContain('evil.com')
  })

  test('should protect against timing attacks', async () => {
    await page.goto('/login')

    const timings: number[] = []

    // Setup route handler
    await page.route('**/api/token', (route) => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Invalid credentials' }),
      })
    })

    // Process attempts sequentially using while loop to avoid eslint issues
    let attemptIndex = 0
    while (attemptIndex < 5) {
      const email =
        attemptIndex % 2 === 0 ? '<EMAIL>' : 'invalid-email-format'

      const startTime = Date.now()
      await page.fill('input[type="email"]', email)
      await page.fill('input[type="password"]', 'password123')

      await page.click('button[type="submit"]')
      await page.waitForSelector('[role="alert"]')

      const endTime = Date.now()
      timings.push(endTime - startTime)

      // Clear for next attempt
      await page.fill('input[type="email"]', '')
      await page.fill('input[type="password"]', '')

      attemptIndex += 1
    }

    // Response times should be consistent (within 200ms variance)
    const avgTime = timings.reduce((a, b) => a + b) / timings.length
    const maxVariance = Math.max(...timings.map((t) => Math.abs(t - avgTime)))
    expect(maxVariance).toBeLessThan(200)
  })

  test('should implement rate limiting', async () => {
    await page.goto('/login')

    let attemptCount = 0
    await page.route('**/api/token', (route) => {
      attemptCount++
      if (attemptCount > 5) {
        route.fulfill({
          status: 429,
          contentType: 'application/json',
          body: JSON.stringify({
            error: 'Too many attempts. Please try again later.',
            retryAfter: 300,
          }),
        })
      } else {
        route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Invalid credentials' }),
        })
      }
    })

    // Make multiple failed attempts using while loop
    let loginAttemptIndex = 0
    while (loginAttemptIndex < 7) {
      await page.fill('input[type="email"]', '<EMAIL>')
      await page.fill('input[type="password"]', 'wrong-password')
      await page.click('button[type="submit"]')

      if (loginAttemptIndex < 5) {
        await expect(page.locator('text=Invalid credentials')).toBeVisible()
      }

      loginAttemptIndex += 1
    }

    // Should show rate limit message
    await expect(page.locator('text=Too many attempts')).toBeVisible()

    // Login button should be disabled
    await expect(page.locator('button[type="submit"]')).toBeDisabled()
  })

  test('should secure OAuth popup communication', async () => {
    await page.goto('/login')

    // Listen for postMessage events
    await page.evaluateOnNewDocument(() => {
      window.addEventListener('message', (event) => {
        ;(window as any).__receivedMessages =
          (window as any).__receivedMessages || []
        ;(window as any).__receivedMessages.push({
          origin: event.origin,
          data: event.data,
        })
      })
    })

    // Click OAuth sign in
    const popupPromise = page.waitForEvent('popup')
    await page.click('button:has-text("Continue with Google")')
    const popup = await popupPromise

    // Simulate malicious message from wrong origin
    await page.evaluate(() => {
      window.postMessage(
        {
          type: 'oauth-success',
          provider: 'google',
          data: { userId: 'malicious-user', token: 'stolen-token' },
        },
        '*'
      )
    })

    await popup.close()
    await page.waitForTimeout(1000)

    // Check that only messages from trusted origins are processed
    const receivedMessages = await page.evaluate(
      () => (window as any).__receivedMessages || []
    )

    // Should validate message origin
    receivedMessages.forEach((msg: any) => {
      if (msg.data?.type === 'oauth-success') {
        expect([
          'https://accounts.google.com',
          'https://appleid.apple.com',
          page.url(),
        ]).toContain(msg.origin)
      }
    })
  })

  test('should clear sensitive data on logout', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Logout
    await page.click('button[aria-label="User menu"]')
    await page.click('text=Sign out')
    await page.waitForURL('/login')

    // Check all storage is cleared
    const storageData = await page.evaluate(() => {
      return {
        localStorage: Object.keys(localStorage),
        sessionStorage: Object.keys(sessionStorage),
        cookies: document.cookie,
      }
    })

    // Should not have any auth-related data
    expect(storageData.localStorage).not.toContain('auth-storage')
    expect(storageData.localStorage).not.toContain('token')
    expect(storageData.sessionStorage).not.toContain('auth')
    expect(storageData.cookies).not.toContain('token')
    expect(storageData.cookies).not.toContain('session')
  })
})
