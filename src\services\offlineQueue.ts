import { apiClient } from '@/api/client'
import { AxiosRequestConfig } from 'axios'

interface QueuedRequest {
  id: string
  url: string
  method: string
  data?: unknown
  headers?: Record<string, string>
  timestamp: number
}

export class OfflineQueue extends EventTarget {
  private static instance: OfflineQueue

  private readonly STORAGE_KEY = 'offline_queue'

  private readonly MAX_QUEUE_SIZE = 100

  private readonly MAX_AGE_MS = 7 * 24 * 60 * 60 * 1000 // 7 days

  public isProcessing = false

  private constructor() {
    super()
    this.initializeEventListeners()
  }

  static getInstance(): OfflineQueue {
    if (!OfflineQueue.instance) {
      OfflineQueue.instance = new OfflineQueue()
    }
    return OfflineQueue.instance
  }

  private initializeEventListeners() {
    window.addEventListener('online', () => {
      this.processQueue()
    })
  }

  private getQueue(): QueuedRequest[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.error('Failed to parse offline queue:', error)
      this.clearQueue()
      return []
    }
  }

  private saveQueue(queue: QueuedRequest[]) {
    try {
      // Limit queue size
      const limitedQueue = queue.slice(-this.MAX_QUEUE_SIZE)
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(limitedQueue))

      // Emit queue change event
      this.dispatchEvent(
        new CustomEvent('queuechange', {
          detail: { queueLength: limitedQueue.length },
        })
      )
    } catch (error) {
      console.error('Failed to save offline queue:', error)
      if (
        error instanceof Error &&
        error.message.includes('QuotaExceededError')
      ) {
        // Try to save a smaller queue
        this.saveQueue(queue.slice(-10))
      }
    }
  }

  private clearQueue() {
    localStorage.setItem(this.STORAGE_KEY, '[]')
  }

  addToQueue(request: AxiosRequestConfig): string | Promise<unknown> {
    // If online, execute immediately
    if (navigator.onLine) {
      return apiClient.request(request)
    }

    // Generate unique ID
    const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    const queuedRequest: QueuedRequest = {
      id,
      url: request.url!,
      method: request.method!,
      data: request.data,
      headers: request.headers as Record<string, string> | undefined,
      timestamp: Date.now(),
    }

    const queue = this.getQueue()

    // Prioritize workout operations
    if (request.url?.includes('/Workout/') || request.url?.includes('/Set/')) {
      queue.unshift(queuedRequest)
    } else {
      queue.push(queuedRequest)
    }

    this.saveQueue(queue)
    return id
  }

  async processQueue(): Promise<void> {
    // Prevent concurrent processing
    if (this.isProcessing || !navigator.onLine) {
      return
    }

    this.isProcessing = true

    try {
      const queue = this.getQueue()
      const now = Date.now()
      const validQueue = queue.filter(
        (item) => now - item.timestamp <= this.MAX_AGE_MS
      )

      const failed: QueuedRequest[] = []

      // Process requests sequentially to avoid await-in-loop issues
      await validQueue.reduce(async (promise, request) => {
        await promise
        try {
          await apiClient.request({
            url: request.url,
            method: request.method as AxiosRequestConfig['method'],
            data: request.data,
            headers: request.headers,
          })

          // Emit success event
          this.dispatchEvent(
            new CustomEvent('requestsuccess', {
              detail: { requestId: request.id },
            })
          )
        } catch (error) {
          console.error(
            `Failed to process queued request ${request.id}:`,
            error
          )
          failed.push(request)

          // Emit failure event
          this.dispatchEvent(
            new CustomEvent('requestfailure', {
              detail: { requestId: request.id, error },
            })
          )
        }
      }, Promise.resolve())

      // Save failed requests back to queue
      this.saveQueue(failed)
    } finally {
      this.isProcessing = false
    }
  }

  getQueueLength(): number {
    return this.getQueue().length
  }

  removeFromQueue(id: string) {
    const queue = this.getQueue()
    const filtered = queue.filter((item) => item.id !== id)
    this.saveQueue(filtered)
  }
}

// Export singleton instance
export const offlineQueue = OfflineQueue.getInstance()
