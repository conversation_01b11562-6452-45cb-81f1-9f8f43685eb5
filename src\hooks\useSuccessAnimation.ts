import { useState, useEffect, useCallback, useRef } from 'react'
import type {
  AnimationState,
  SuccessAnimationOptions,
  UseSuccessAnimationReturn,
} from '@/types/animations'
import { ANIMATION_DURATION } from '@/types/animations'

/**
 * Custom hook for managing success animation states
 * Handles animation lifecycle and respects reduced motion preferences
 */
export function useSuccessAnimation(
  options: SuccessAnimationOptions = {}
): UseSuccessAnimationReturn {
  const { respectReducedMotion = true, onComplete, timing = {} } = options

  const [state, setState] = useState<AnimationState>('idle')
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const isMountedRef = useRef(true)

  // Check for reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches)
    }

    // Modern browsers
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange)
    } else {
      // Legacy browsers
      mediaQuery.addListener(handleChange)
    }

    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange)
      } else {
        // Legacy browsers
        mediaQuery.removeListener(handleChange)
      }
    }
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true
    return () => {
      isMountedRef.current = false
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  // Calculate actual duration based on reduced motion preference
  const getDuration = useCallback(
    (baseDuration: number) => {
      if (respectReducedMotion && prefersReducedMotion) {
        return 0 // Instant transition
      }
      return timing.duration ?? baseDuration
    },
    [prefersReducedMotion, respectReducedMotion, timing.duration]
  )

  // Start animation sequence
  const start = useCallback(() => {
    if (state !== 'idle' && state !== 'complete') {
      return // Animation already in progress
    }

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Start entering phase
    setState('entering')

    // Transition to active state
    timeoutRef.current = setTimeout(
      () => {
        if (!isMountedRef.current) return
        setState('active')

        // Complete animation after total duration
        const totalDuration = getDuration(
          timing.duration ?? ANIMATION_DURATION.TOTAL
        )

        timeoutRef.current = setTimeout(() => {
          if (!isMountedRef.current) return
          setState('complete')
          onComplete?.()
        }, totalDuration)
      },
      getDuration(timing.delay ?? 0)
    )
  }, [state, getDuration, timing.duration, timing.delay, onComplete])

  // Reset animation to initial state
  const reset = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setState('idle')
  }, [])

  // Determine if animation is currently running
  const isAnimating = state !== 'idle' && state !== 'complete'

  return {
    state,
    isAnimating,
    prefersReducedMotion,
    start,
    reset,
  }
}
