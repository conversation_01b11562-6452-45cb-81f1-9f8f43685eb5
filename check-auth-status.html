<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Status Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 15px; margin: 10px 0; border-radius: 8px; }
        .success { background: #e8f5e9; color: #2e7d32; }
        .error { background: #ffebee; color: #c62828; }
        .warning { background: #fff3e0; color: #ef6c00; }
        .info { background: #e3f2fd; color: #1565c0; }
        button { padding: 10px 20px; margin: 5px; background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #1976d2; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>DrMuscle Authentication Status</h1>
    
    <div>
        <button onclick="checkAuthStatus()">Check Auth Status</button>
        <button onclick="loginUser()">Login Test User</button>
        <button onclick="testWorkoutAPI()">Test Workout API</button>
        <button onclick="clearAll()">Clear All</button>
    </div>

    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearAll() {
            document.getElementById('results').innerHTML = '';
        }

        async function checkAuthStatus() {
            addResult('🔍 Checking authentication status...', 'info');
            
            try {
                const response = await fetch('/api/auth/token', {
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                if (data.authenticated && data.token) {
                    addResult(`✅ User is authenticated`, 'success');
                    addResult(`📋 Token: ${data.token.substring(0, 20)}...`, 'info');
                    
                    // Check if token works with Dr. Muscle API
                    try {
                        const testResponse = await fetch('https://drmuscle.azurewebsites.net/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${data.token}`
                            },
                            body: JSON.stringify({
                                TimeZoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
                                Offset: new Date().getTimezoneOffset() / -60,
                                IsDaylightSaving: false,
                            })
                        });
                        
                        if (testResponse.ok) {
                            const workoutData = await testResponse.json();
                            addResult('✅ Token works with Dr. Muscle API', 'success');
                            
                            const workoutId = workoutData?.Result?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Id;
                            if (workoutId) {
                                addResult(`📋 Found workout ID: ${workoutId}`, 'success');
                                
                                const exercises = workoutData?.Result?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Exercises || [];
                                addResult(`🏋️ Found ${exercises.length} exercises`, 'success');
                            } else {
                                addResult('⚠️ No workout ID found in response', 'warning');
                            }
                        } else {
                            addResult(`❌ Token failed with Dr. Muscle API: ${testResponse.status}`, 'error');
                        }
                    } catch (apiError) {
                        addResult(`❌ API test error: ${apiError.message}`, 'error');
                    }
                } else {
                    addResult('❌ User is not authenticated', 'error');
                    addResult('💡 Please log in first', 'info');
                }
            } catch (error) {
                addResult(`❌ Auth check failed: ${error.message}`, 'error');
            }
        }

        async function loginUser() {
            addResult('🔑 Attempting to log in test user...', 'info');
            
            try {
                // First, get the login token
                const tokenResponse = await fetch('https://drmuscle.azurewebsites.net/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: 'grant_type=password&username=<EMAIL>&password=Dr123456'
                });

                if (tokenResponse.ok) {
                    const tokenData = await tokenResponse.json();
                    
                    if (tokenData.access_token) {
                        addResult('✅ Got access token from Dr. Muscle API', 'success');
                        
                        // Now exchange it with our app
                        const exchangeResponse = await fetch('/api/auth/exchange', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            credentials: 'include',
                            body: JSON.stringify({
                                access_token: tokenData.access_token,
                                refresh_token: tokenData.refresh_token || null,
                                email: '<EMAIL>'
                            })
                        });

                        if (exchangeResponse.ok) {
                            addResult('✅ Token exchange successful', 'success');
                            addResult('🎉 User is now logged in!', 'success');
                            
                            // Check auth status again
                            setTimeout(() => checkAuthStatus(), 1000);
                        } else {
                            const errorText = await exchangeResponse.text();
                            addResult(`❌ Token exchange failed: ${exchangeResponse.status} - ${errorText}`, 'error');
                        }
                    } else {
                        addResult('❌ No access token in response', 'error');
                    }
                } else {
                    const errorText = await tokenResponse.text();
                    addResult(`❌ Login failed: ${tokenResponse.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Login error: ${error.message}`, 'error');
            }
        }

        async function testWorkoutAPI() {
            addResult('🏋️ Testing workout API endpoints...', 'info');
            
            try {
                // Test the Next.js API routes
                const endpoints = [
                    '/api/workouts/today',
                    '/api/workouts/program-info',
                    '/api/workouts/user-workouts'
                ];

                for (const endpoint of endpoints) {
                    try {
                        const response = await fetch(endpoint, {
                            credentials: 'include'
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            addResult(`✅ ${endpoint}: OK`, 'success');
                        } else {
                            addResult(`❌ ${endpoint}: ${response.status}`, 'error');
                        }
                    } catch (error) {
                        addResult(`❌ ${endpoint}: ${error.message}`, 'error');
                    }
                }
            } catch (error) {
                addResult(`❌ Workout API test error: ${error.message}`, 'error');
            }
        }

        // Auto-check auth status on page load
        window.onload = () => {
            addResult('🚀 Auth status checker loaded', 'info');
            setTimeout(() => checkAuthStatus(), 500);
        };
    </script>
</body>
</html>
