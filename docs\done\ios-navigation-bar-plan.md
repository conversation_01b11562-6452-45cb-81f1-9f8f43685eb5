# iOS Navigation Bar Implementation Plan

## Overview

Implement a consistent iOS-style navigation bar across the entire Dr. Muscle X PWA, replacing the current inconsistent navigation patterns with a unified, native iOS experience.

## Current State Analysis

- **MobileNavHeader**: Shows app name and user avatar (fixed at top)
- **NavigationWrapper**: Controls when navigation is shown
- **Inconsistent navigation**: Each screen has different navigation styles
- No standard back button component
- No iOS-style blur effects or animations

## Design Specifications

### iOS Navigation Bar Standards

- **Height**: 44px (standard iOS navigation bar height)
- **Background**: Translucent with blur effect (`backdrop-filter: blur(10px)`)
- **Layout**: Three sections (left, center, right)
- **Back Button**: Chevron icon (< shape) with optional previous screen title
- **Title**: Centered, with support for large titles
- **Touch Targets**: Minimum 44x44px for all interactive elements
- **Safe Area**: Proper handling for notches and status bars

### Navigation Hierarchy

```
Login/Signup (no nav)
└── Program (root - no back button)
    └── Workout Overview
        ├── Set Screen (for each exercise)
        │   └── Rest Timer
        └── Workout Complete
```

## Implementation Blueprint

### Phase 1: Core Components (Foundation)

1. Create iOS chevron icon component
2. Create iOS navigation bar component with blur effects
3. Create navigation context for managing state
4. Add iOS-specific global styles

### Phase 2: Integration Layer

1. Update NavigationWrapper to use new iOS navigation bar
2. Create navigation configuration system
3. Implement navigation hierarchy tracking
4. Add transition animations

### Phase 3: Screen Updates

1. Update Program page navigation
2. Update Workout Overview navigation
3. Update Set Screen navigation
4. Update Rest Timer navigation
5. Update Workout Complete navigation

### Phase 4: Polish & Testing

1. Add haptic feedback for navigation actions
2. Implement swipe-back gesture support
3. Update all navigation tests
4. E2E testing on iOS devices

## Detailed Step-by-Step Implementation

### Step 1: Create ChevronLeftIcon Component

```text
Create a new iOS-style chevron left icon component at src/components/icons/ChevronLeftIcon.tsx.
This should be a simple SVG icon that matches the iOS chevron style (thin stroke, specific angle).
The icon should be 24x24px with proper accessibility attributes.
Include TypeScript props for className and onClick handler.
Write unit tests to verify the icon renders correctly and handles clicks.
```

### Step 2: Create Basic IOSNavigationBar Component

```text
Create src/components/navigation/IOSNavigationBar.tsx with a basic structure.
Implement the three-section layout (left, center, right) using flexbox.
Add fixed positioning and proper z-index.
Set the height to 44px as per iOS standards.
Add basic props for title, showBackButton, and onBackClick.
Write unit tests for basic rendering and prop handling.
```

### Step 3: Add iOS Blur Effects

```text
Enhance the IOSNavigationBar component with iOS-style blur effects.
Add backdrop-filter CSS for the blur effect.
Implement semi-transparent background colors.
Add border-bottom for subtle separation.
Ensure proper fallbacks for browsers that don't support backdrop-filter.
Update tests to verify blur styles are applied.
```

### Step 4: Create Navigation Context

```text
Create src/contexts/NavigationContext.tsx for managing navigation state.
Track current screen title and navigation hierarchy.
Implement methods for setting title and handling back navigation.
Provide context to all child components.
Write tests for context state management and updates.
```

### Step 5: Create Navigation Configuration System

```text
Create src/config/navigationConfig.ts to define navigation for each route.
Define title, back button behavior, and right actions for each screen.
Export configuration object with route patterns.
Create TypeScript types for navigation configuration.
Write tests to verify configuration structure.
```

### Step 6: Update NavigationWrapper

```text
Modify NavigationWrapper to use the new IOSNavigationBar.
Replace MobileNavHeader with IOSNavigationBar on authenticated pages.
Pass navigation configuration based on current route.
Maintain the 56px spacer for content offset.
Update tests for NavigationWrapper with new navigation bar.
```

### Step 7: Implement Back Navigation Logic

```text
Add proper back navigation handling in IOSNavigationBar.
Integrate with Next.js router for navigation.
Implement confirmation dialogs where needed (e.g., during workout).
Add navigation hierarchy tracking.
Write tests for back navigation behavior.
```

### Step 8: Update Program Page

```text
Update the Program page to work with new navigation.
Remove any custom navigation elements.
Set appropriate title in navigation context.
Ensure user avatar appears in right section.
Update Program page tests.
```

### Step 9: Update Workout Overview

```text
Replace custom header in WorkoutOverview with new navigation.
Remove the existing back button implementation.
Set "Today's Workout" as the title.
Configure back navigation to Program page.
Update WorkoutOverview tests.
```

### Step 10: Update Set Screen

```text
Replace custom navigation in SetScreen with new system.
Use exercise name as dynamic title.
Implement back confirmation dialog.
Remove existing header code.
Update SetScreen navigation tests.
```

### Step 11: Update Rest Timer

```text
Add navigation bar to Rest Timer screen.
Set "Rest Timer" as title.
Add settings icon to right section.
Implement back confirmation during active timer.
Create tests for timer navigation.
```

### Step 12: Update Workout Complete

```text
Add navigation to Workout Complete screen.
Use "Done" button instead of back button.
Add share icon to right section.
Configure navigation to return to Program.
Write tests for completion navigation.
```

### Step 13: Add Transition Animations

```text
Implement smooth title transitions when navigating.
Add slide animations for screen transitions.
Ensure animations respect prefers-reduced-motion.
Test animation performance on low-end devices.
```

### Step 14: Add Haptic Feedback

```text
Integrate haptic feedback for navigation actions.
Use Vibration API for back button taps.
Add subtle feedback for successful navigation.
Ensure feedback works only on supported devices.
Test haptic feedback on iOS devices.
```

### Step 15: Final Integration & Testing

```text
Wire all components together.
Ensure no orphaned navigation code remains.
Run full test suite.
Perform manual testing on iOS devices.
Update documentation.
```

## Test-Driven Development Approach

Each step should follow this pattern:

1. Write failing tests for the new functionality
2. Implement minimal code to pass tests
3. Refactor for code quality
4. Update integration tests
5. Manual testing on devices

## Success Criteria

- [ ] Consistent iOS navigation bar on all authenticated screens
- [ ] Proper navigation hierarchy maintained
- [ ] All touch targets meet 44x44px minimum
- [ ] Blur effects work on iOS Safari 15+
- [ ] Back navigation works correctly with confirmations
- [ ] All existing tests pass
- [ ] New navigation tests provide >90% coverage
- [ ] Performance metrics maintained (<100ms transitions)

## Rollback Plan

If issues arise:

1. The feature branch can be abandoned
2. NavigationWrapper changes can be reverted
3. Original navigation components remain unchanged initially
4. Gradual rollout allows testing on subset of screens

## File Structure After Implementation

```
src/
├── components/
│   ├── navigation/
│   │   ├── IOSNavigationBar.tsx (new)
│   │   ├── IOSNavigationBar.test.tsx (new)
│   │   └── index.ts (new)
│   ├── icons/
│   │   ├── ChevronLeftIcon.tsx (new)
│   │   ├── ChevronLeftIcon.test.tsx (new)
│   │   └── index.ts (updated)
│   └── NavigationWrapper.tsx (modified)
├── contexts/
│   ├── NavigationContext.tsx (new)
│   └── NavigationContext.test.tsx (new)
├── config/
│   └── navigationConfig.ts (new)
└── app/
    ├── program/page.tsx (modified)
    └── workout/
        ├── page.tsx (modified)
        ├── exercise/[id]/page.tsx (modified)
        ├── rest-timer/page.tsx (modified)
        └── complete/page.tsx (modified)
```
