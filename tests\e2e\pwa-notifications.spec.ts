import { test, expect, Page, BrowserContext } from '@playwright/test'

test.describe('PWA Push Notifications Tests', () => {
  let page: Page
  let context: BrowserContext
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page: p, context: c }) => {
    page = p
    context = c

    // Grant notification permission
    await context.grantPermissions(['notifications'])
  })

  test('should request notification permission', async () => {
    await page.goto('/')

    // Check current permission
    const currentPermission = await page.evaluate(() => {
      return Notification.permission
    })

    // If not granted, should show permission request UI
    if (currentPermission === 'default') {
      const permissionButton = await page
        .locator('[data-testid="enable-notifications"]')
        .isVisible()
        .catch(() => false)

      if (permissionButton) {
        // Mock permission grant
        await context.grantPermissions(['notifications'])

        // Click enable notifications
        await page.click('[data-testid="enable-notifications"]')

        // Permission should be granted
        const newPermission = await page.evaluate(() => Notification.permission)
        expect(newPermission).toBe('granted')
      }
    }
  })

  test('should register for push notifications when permitted', async () => {
    // Login
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')

    // Check if push subscription exists
    const hasSubscription = await page.evaluate(async () => {
      if ('serviceWorker' in navigator && 'PushManager' in window) {
        const registration = await navigator.serviceWorker.ready
        const subscription = await registration.pushManager.getSubscription()
        return !!subscription
      }
      return false
    })

    // Should have push subscription if notifications are supported
    if ('PushManager' in window) {
      expect(hasSubscription).toBe(true)
    }
  })

  test('should handle push notification for rest timer', async () => {
    // Login and navigate to workout
    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')
    await page.waitForURL('/program')
    await page.click('button:has-text("Continue to workout")')
    await page.waitForURL('/workout')

    // Complete a set to trigger rest timer
    await page.click('[data-testid="exercise-item"]')
    await page.fill('input[name="weight"]', '100')
    await page.fill('input[name="reps"]', '10')
    await page.click('button:has-text("Save")')

    // Should navigate to rest timer
    await expect(page).toHaveURL(/\/timer/)

    // Listen for notification
    const notificationPromise = page.evaluate(() => {
      return new Promise((resolve) => {
        // Override Notification constructor to capture calls
        const OriginalNotification = window.Notification
        window.Notification = function (
          title: string,
          options?: NotificationOptions
        ) {
          resolve({ title, body: options?.body })
          return new OriginalNotification(title, options)
        } as any
        window.Notification.permission = OriginalNotification.permission
        window.Notification.requestPermission =
          OriginalNotification.requestPermission
      })
    })

    // Wait for rest timer to complete (use short timer for test)
    await page.waitForTimeout(3000)

    // Check if notification was triggered
    const notification = await Promise.race([
      notificationPromise,
      page.waitForTimeout(5000).then(() => null),
    ])

    if (notification) {
      expect(notification).toHaveProperty('title')
      expect(notification.title).toContain('Rest')
    }
  })
})
