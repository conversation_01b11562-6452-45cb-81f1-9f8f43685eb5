import { ExercisePageClient } from './ExercisePageClient'

// Ensure this route is always rendered dynamically on the server so that
// arbitrary exercise IDs work in production (otherwise it could be treated
// as a static route at build time and return 404 for unknown ids).
export const dynamic = 'force-dynamic'

interface ExercisePageProps {
  params: Promise<{
    id: string
  }>
}

export default async function ExercisePage({ params }: ExercisePageProps) {
  const { id } = await params
  return <ExercisePageClient exerciseId={parseInt(id)} />
}
