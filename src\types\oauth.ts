/**
 * OAuth Authentication Types for Dr. Muscle X
 *
 * Shared type definitions for OAuth integration.
 * Provider-specific types are in googleOAuthTypes.ts and appleOAuthTypes.ts
 */

import type { LoginSuccessResult, LoginSuccessResultAlt } from './api'
import type {
  GoogleCredentialResponse,
  GoogleTokenPayload,
  GoogleOAuthConfig,
} from './googleOAuthTypes'
import type {
  AppleSignInResponse,
  AppleTokenPayload,
  AppleOAuthConfig,
} from './appleOAuthTypes'

// Re-export provider-specific types for backward compatibility
export type {
  GoogleCredentialResponse,
  GoogleTokenPayload,
  AppleSignInResponse,
  AppleTokenPayload,
}
export { isGoogleCredentialResponse } from './googleOAuthTypes'
export { isAppleSignInResponse } from './appleOAuthTypes'

/**
 * Supported OAuth providers
 */
export type OAuthProvider = 'google' | 'apple'

/**
 * Unified OAuth user data structure
 * Normalizes data from different OAuth providers
 */
export interface OAuthUserData {
  /** OAuth provider type */
  provider: OAuthProvider
  /** Provider-specific user ID */
  providerId: string
  /** User's email address */
  email: string
  /** Whether email is verified by provider */
  emailVerified: boolean
  /** User's display name */
  name?: string
  /** User's first name */
  firstName?: string
  /** User's last name */
  lastName?: string
  /** Profile picture URL (if available) */
  pictureUrl?: string
  /** Raw token from provider */
  rawToken: string
  /** Decoded token payload */
  tokenPayload: GoogleTokenPayload | AppleTokenPayload
  /** Additional provider-specific data */
  metadata?: Record<string, unknown>
}

/**
 * OAuth login request to backend
 */
export interface OAuthLoginRequest {
  /** OAuth provider */
  provider: OAuthProvider
  /** Provider-specific token/credential */
  token: string
  /** Optional authorization code (for Apple) */
  authorizationCode?: string
  /** Optional state for CSRF protection */
  state?: string
  /** Device/client information */
  deviceInfo?: {
    platform: string
    userAgent: string
  }
}

/**
 * OAuth login response from backend
 */
export interface OAuthLoginResponse extends LoginSuccessResult {
  /** Whether this is a new user registration */
  isNewUser?: boolean
  /** User profile data */
  userProfile?: {
    id: string
    email: string
    name?: string
    firstName?: string
    lastName?: string
    pictureUrl?: string
  }
}

/**
 * Alternative OAuth login response structure
 */
export interface OAuthLoginResponseAlt extends LoginSuccessResultAlt {
  /** Whether this is a new user registration */
  isNewUser?: boolean
}

/**
 * OAuth error response
 */
export interface OAuthError {
  /** Error code */
  code:
    | 'oauth_failed'
    | 'invalid_token'
    | 'provider_error'
    | 'network_error'
    | 'server_error'
    | 'user_cancelled'
  /** Human-readable error message */
  message: string
  /** OAuth provider where error occurred */
  provider: OAuthProvider
  /** Original error from provider */
  providerError?: unknown
  /** Additional error details */
  details?: Record<string, unknown>
}

/**
 * OAuth success callback function type
 */
export type OAuthSuccessCallback = (data: OAuthUserData) => void | Promise<void>

/**
 * OAuth error callback function type
 */
export type OAuthErrorCallback = (error: OAuthError) => void

/**
 * OAuth configuration options
 */
export interface OAuthConfig {
  /** Google OAuth configuration */
  google?: GoogleOAuthConfig
  /** Apple OAuth configuration */
  apple?: AppleOAuthConfig
  /** Common OAuth callbacks */
  onSuccess?: OAuthSuccessCallback
  onError?: OAuthErrorCallback
  /** Whether to show loading state during OAuth flow */
  showLoading?: boolean
}

/**
 * OAuth button configuration
 */
export interface OAuthButtonConfig {
  /** OAuth provider */
  provider: OAuthProvider
  /** Button text override */
  text?: string
  /** Button style variant */
  variant?: 'filled' | 'outline' | 'icon'
  /** Button size */
  size?: 'small' | 'medium' | 'large'
  /** Button theme */
  theme?: 'light' | 'dark' | 'auto'
  /** Custom CSS classes */
  className?: string
  /** Disabled state */
  disabled?: boolean
  /** Loading state */
  loading?: boolean
  /** Icon position */
  iconPosition?: 'left' | 'right'
  /** Custom onClick handler (overrides default OAuth flow) */
  onClick?: () => void
  /** Accessibility label */
  ariaLabel?: string
}

/**
 * OAuth state for state management
 */
export interface OAuthState {
  /** Current OAuth loading state */
  isLoading: boolean
  /** Current OAuth provider being processed */
  currentProvider: OAuthProvider | null
  /** Last OAuth error */
  error: OAuthError | null
  /** OAuth configuration */
  config: OAuthConfig
}

/**
 * OAuth hook return type
 */
export interface UseOAuthReturn {
  /** Sign in with Google */
  signInWithGoogle: () => Promise<void>
  /** Sign in with Apple */
  signInWithApple: () => Promise<void>
  /** Generic sign in method */
  signIn: (provider: OAuthProvider) => Promise<void>
  /** Current loading state */
  isLoading: boolean
  /** Current error state */
  error: OAuthError | null
  /** Clear error */
  clearError: () => void
  /** Check if provider is available */
  isProviderAvailable: (provider: OAuthProvider) => boolean
}

/**
 * OAuth token validation result
 */
export interface OAuthTokenValidation {
  /** Whether token is valid */
  isValid: boolean
  /** Decoded token payload */
  payload?: GoogleTokenPayload | AppleTokenPayload
  /** Validation error message */
  error?: string
  /** Token expiration time */
  expiresAt?: Date
}

/**
 * OAuth provider availability
 */
export interface OAuthProviderAvailability {
  /** Whether Google Sign-In is available */
  google: boolean
  /** Whether Apple Sign-In is available */
  apple: boolean
}

/**
 * OAuth utilities interface
 */
export interface OAuthUtils {
  /** Decode JWT token */
  decodeToken: (token: string) => GoogleTokenPayload | AppleTokenPayload | null
  /** Validate OAuth token */
  validateToken: (
    token: string,
    provider: OAuthProvider
  ) => OAuthTokenValidation
  /** Check provider availability */
  checkProviderAvailability: () => OAuthProviderAvailability
  /** Get provider name for display */
  getProviderDisplayName: (provider: OAuthProvider) => string
  /** Get provider icon component */
  getProviderIcon: (
    provider: OAuthProvider
  ) => React.ComponentType<{ className?: string }>
}

/**
 * OAuth context value
 */
export interface OAuthContextValue extends UseOAuthReturn {
  /** OAuth configuration */
  config: OAuthConfig
  /** Update OAuth configuration */
  updateConfig: (config: Partial<OAuthConfig>) => void
  /** OAuth utilities */
  utils: OAuthUtils
}

/**
 * Type guard for OAuth error
 */
export function isOAuthError(error: unknown): error is OAuthError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'code' in error &&
    'message' in error &&
    'provider' in error
  )
}
