'use client'

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useMemo,
} from 'react'
import { useRouter } from 'next/navigation'

interface NavigationContextValue {
  title: string
  setTitle: (title: string) => void
  canGoBack: boolean
  goBack: () => void
  navigationStack: string[]
  pushRoute: (route: string) => void
  popRoute: () => void
}

const NavigationContext = createContext<NavigationContextValue | null>(null)

interface NavigationProviderProps {
  children: React.ReactNode
}

export function NavigationProvider({ children }: NavigationProviderProps) {
  const router = useRouter()
  const [title, setTitle] = useState('')
  const [navigationStack, setNavigationStack] = useState<string[]>([])

  const canGoBack = navigationStack.length > 0

  const pushRoute = useCallback((route: string) => {
    setNavigationStack((prev) => [...prev, route])
  }, [])

  const popRoute = useCallback(() => {
    setNavigationStack((prev) => prev.slice(0, -1))
  }, [])

  const goBack = useCallback(() => {
    if (canGoBack && navigationStack.length > 0) {
      const previousRoute = navigationStack[navigationStack.length - 1]
      popRoute()
      if (previousRoute) {
        router.push(previousRoute)
      }
    }
  }, [canGoBack, navigationStack, popRoute, router])

  const value: NavigationContextValue = useMemo(
    () => ({
      title,
      setTitle,
      canGoBack,
      goBack,
      navigationStack,
      pushRoute,
      popRoute,
    }),
    [title, setTitle, canGoBack, goBack, navigationStack, pushRoute, popRoute]
  )

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  )
}

export function useNavigation() {
  const context = useContext(NavigationContext)
  if (!context) {
    throw new Error('useNavigation must be used within NavigationProvider')
  }
  return context
}
