import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { ErrorBoundary } from '@/components/ErrorBoundary'
import React from 'react'

// Component that throws an error
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error')
  }
  return <div>No error</div>
}

// Component with async error
const ThrowAsyncError = () => {
  React.useEffect(() => {
    throw new Error('Async error')
  }, [])
  return <div>Loading...</div>
}

describe('ErrorBoundary', () => {
  const mockOnError = vi.fn()
  const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {})

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    mockConsoleError.mockRestore()
  })

  describe('Error Catching', () => {
    it('should catch and display errors from child components', () => {
      // Given
      const { rerender } = render(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      )

      // When - component throws error
      rerender(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Then
      expect(screen.getByRole('alert')).toBeInTheDocument()
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument()
      expect(screen.getByText(/Test error/i)).toBeInTheDocument()
    })

    it('should display user-friendly error message', () => {
      // Given/When
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Then
      expect(screen.getByText(/Oops! Something went wrong/i)).toBeInTheDocument()
      expect(screen.getByText(/We've encountered an unexpected error/i)).toBeInTheDocument()
    })

    it('should provide recovery button', () => {
      // Given
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Then
      const retryButton = screen.getByRole('button', { name: /try again/i })
      expect(retryButton).toBeInTheDocument()
      expect(retryButton).toHaveClass('h-14') // Mobile-friendly size
    })

    it('should call onError callback when error occurs', () => {
      // Given/When
      render(
        <ErrorBoundary onError={mockOnError}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Then
      expect(mockOnError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      )
    })
  })

  describe('Error Recovery', () => {
    it('should reset error state when retry button is clicked', () => {
      // Given
      let throwError = true
      const TestComponent = () => <ThrowError shouldThrow={throwError} />
      
      const { rerender } = render(
        <ErrorBoundary>
          <TestComponent />
        </ErrorBoundary>
      )

      expect(screen.getByRole('alert')).toBeInTheDocument()

      // When - click retry and update to not throw
      throwError = false
      fireEvent.click(screen.getByRole('button', { name: /try again/i }))

      // Then - error boundary resets and shows content
      rerender(
        <ErrorBoundary>
          <TestComponent />
        </ErrorBoundary>
      )

      expect(screen.queryByRole('alert')).not.toBeInTheDocument()
      expect(screen.getByText('No error')).toBeInTheDocument()
    })

    it('should preserve error info for debugging', () => {
      // Given
      const error = new Error('Detailed error message')
      error.stack = 'Error stack trace'

      // When
      render(
        <ErrorBoundary showDetails={true}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Then
      const detailsButton = screen.getByRole('button', { name: /show details/i })
      fireEvent.click(detailsButton)

      expect(screen.getByText(/Test error/i)).toBeInTheDocument()
      expect(screen.getByText(/Component Stack:/i)).toBeInTheDocument()
    })
  })

  describe('Fallback UI', () => {
    it('should render custom fallback component if provided', () => {
      // Given
      const CustomFallback = ({ error, retry }: any) => (
        <div>
          <h1>Custom Error</h1>
          <p>{error.message}</p>
          <button onClick={retry}>Custom Retry</button>
        </div>
      )

      // When
      render(
        <ErrorBoundary fallback={CustomFallback}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Then
      expect(screen.getByText('Custom Error')).toBeInTheDocument()
      expect(screen.getByText('Test error')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /custom retry/i })).toBeInTheDocument()
    })

    it('should handle errors in fallback component gracefully', () => {
      // Given - fallback that also throws
      const BadFallback = () => {
        throw new Error('Fallback error')
      }

      // When
      render(
        <ErrorBoundary fallback={BadFallback}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Then - should show minimal fallback
      expect(screen.getByText(/Critical Error/i)).toBeInTheDocument()
      expect(screen.getByText(/Please refresh the page/i)).toBeInTheDocument()
    })
  })

  describe('Error Types', () => {
    it('should handle different error types appropriately', () => {
      // Test network error
      const NetworkError = () => {
        const error = new Error('Network request failed')
        error.name = 'NetworkError'
        throw error
      }

      render(
        <ErrorBoundary>
          <NetworkError />
        </ErrorBoundary>
      )

      expect(screen.getByText(/connection issue/i)).toBeInTheDocument()
    })

    it('should handle chunk load errors for code splitting', () => {
      // Given
      const ChunkError = () => {
        const error = new Error('Loading chunk failed')
        error.name = 'ChunkLoadError'
        throw error
      }

      // When
      render(
        <ErrorBoundary>
          <ChunkError />
        </ErrorBoundary>
      )

      // Then
      expect(screen.getByText(/update available/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /refresh page/i })).toBeInTheDocument()
    })
  })

  describe('Mobile Optimization', () => {
    it('should have touch-friendly button sizes', () => {
      // Given/When
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Then
      const retryButton = screen.getByRole('button', { name: /try again/i })
      expect(retryButton).toHaveClass('h-14', 'px-8') // 56px height, good padding
    })

    it('should display mobile-optimized error layout', () => {
      // Given/When
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Then
      const container = screen.getByRole('alert')
      expect(container).toHaveClass('p-6') // Good padding for mobile
      expect(container.parentElement).toHaveClass('min-h-screen') // Full screen
    })
  })

  describe('Error Logging', () => {
    it('should log errors for debugging', () => {
      // Given/When
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Then
      expect(mockConsoleError).toHaveBeenCalledWith(
        'ErrorBoundary caught an error:',
        expect.any(Error),
        expect.any(Object)
      )
    })

    it('should include workout context in error reports', () => {
      // Given
      const workoutContext = {
        exerciseId: 123,
        setNumber: 2,
        workoutId: 'abc'
      }

      // When
      render(
        <ErrorBoundary context={workoutContext} onError={mockOnError}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // Then
      expect(mockOnError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String),
          context: workoutContext
        })
      )
    })
  })
})