// Quick test script to check if our fixes are working
// Run this in the browser console on localhost:3000

console.log('🧪 Starting DrMuscle fix validation...');

// Track console messages
let messageCount = 0;
let errorCount = 0;
let warningCount = 0;
let loopDetection = new Map();

const originalConsole = {
    log: console.log,
    warn: console.warn,
    error: console.error
};

// Override console methods
console.log = function(...args) {
    messageCount++;
    trackMessage('LOG', args[0]);
    originalConsole.log(...args);
};

console.warn = function(...args) {
    messageCount++;
    warningCount++;
    trackMessage('WARN', args[0]);
    originalConsole.warn(...args);
};

console.error = function(...args) {
    messageCount++;
    errorCount++;
    trackMessage('ERROR', args[0]);
    originalConsole.error(...args);
};

function trackMessage(type, message) {
    const messageKey = String(message).substring(0, 50);
    const now = Date.now();
    
    if (!loopDetection.has(messageKey)) {
        loopDetection.set(messageKey, []);
    }
    
    const times = loopDetection.get(messageKey);
    times.push(now);
    
    // Keep only messages from last 5 seconds
    const recent = times.filter(time => now - time < 5000);
    loopDetection.set(messageKey, recent);
    
    // If more than 10 similar messages in 5 seconds, it's likely a loop
    if (recent.length > 10) {
        originalConsole.error(`🔄 INFINITE LOOP DETECTED: ${messageKey}`);
    }
}

// Monitor network requests
let networkCount = 0;
const originalFetch = window.fetch;
window.fetch = function(...args) {
    networkCount++;
    return originalFetch.apply(this, args);
};

// Test function
function runQuickTest() {
    console.log('🚀 Quick test started');
    
    setTimeout(() => {
        console.log('📊 Test Results:');
        console.log(`   Messages: ${messageCount}`);
        console.log(`   Errors: ${errorCount}`);
        console.log(`   Warnings: ${warningCount}`);
        console.log(`   Network Requests: ${networkCount}`);
        
        // Check for issues
        if (errorCount === 0) {
            console.log('✅ No errors detected');
        } else {
            console.log(`❌ ${errorCount} errors detected`);
        }
        
        if (messageCount < 50) {
            console.log('✅ Message count is reasonable');
        } else if (messageCount < 200) {
            console.log(`⚠️ High message count: ${messageCount}`);
        } else {
            console.log(`❌ Very high message count: ${messageCount}`);
        }
        
        // Check for specific issues
        const hasInfiniteLoop = Array.from(loopDetection.values()).some(times => times.length > 10);
        if (!hasInfiniteLoop) {
            console.log('✅ No infinite loops detected');
        } else {
            console.log('❌ Potential infinite loops detected');
        }
        
        console.log('🏁 Quick test completed');
    }, 30000); // Run for 30 seconds
}

// Auto-start test
runQuickTest();

// Instructions
console.log('📝 Instructions:');
console.log('1. Navigate to workout page');
console.log('2. Try opening an exercise');
console.log('3. Wait for test to complete (30 seconds)');
console.log('4. Check results above');
