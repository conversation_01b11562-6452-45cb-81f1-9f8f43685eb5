import type { ProgramModel, ProgramProgress, ProgramStats } from '@/types'

/**
 * Cache validity constants (in milliseconds)
 */
export const CACHE_TTL = {
  program: 24 * 60 * 60 * 1000, // 24 hours
  progress: 60 * 60 * 1000, // 1 hour (updates more frequently)
  stats: 24 * 60 * 60 * 1000, // 24 hours
} as const

/**
 * Interface for cached program data with timestamps
 */
export interface CachedProgramData {
  program: ProgramModel | null
  progress: ProgramProgress | null
  stats: ProgramStats | null
  lastUpdated: {
    program: number
    progress: number
    stats: number
  }
}

/**
 * Initial state for cached program data
 */
export const initialCachedData: CachedProgramData = {
  program: null,
  progress: null,
  stats: null,
  lastUpdated: {
    program: 0,
    progress: 0,
    stats: 0,
  },
}

/**
 * Cache utility functions
 */
export const cacheUtils = {
  /**
   * Check if cache is stale based on TTL
   */
  isCacheStale(lastUpdated: number, ttl: number): boolean {
    return Date.now() - lastUpdated > ttl
  },

  /**
   * Clear expired cache entries
   */
  clearExpiredEntries(data: CachedProgramData): CachedProgramData {
    const now = Date.now()
    const newData = { ...data }

    if (now - data.lastUpdated.program > CACHE_TTL.program) {
      newData.program = null
      newData.lastUpdated.program = 0
    }

    if (now - data.lastUpdated.progress > CACHE_TTL.progress) {
      newData.progress = null
      newData.lastUpdated.progress = 0
    }

    if (now - data.lastUpdated.stats > CACHE_TTL.stats) {
      newData.stats = null
      newData.lastUpdated.stats = 0
    }

    return newData
  },

  /**
   * Calculate cache age
   */
  getCacheAge(lastUpdated: number): number | null {
    if (lastUpdated === 0) return null
    return Date.now() - lastUpdated
  },

  /**
   * Get cache statistics
   */
  getCacheStats(data: CachedProgramData, isHydrated: boolean) {
    const cacheSize = JSON.stringify(data).length

    return {
      totalSize: cacheSize,
      programAge: this.getCacheAge(data.lastUpdated.program),
      progressAge: this.getCacheAge(data.lastUpdated.progress),
      statsAge: this.getCacheAge(data.lastUpdated.stats),
      isHydrated,
    }
  },
}
