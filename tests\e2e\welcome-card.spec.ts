import { test, expect } from '@playwright/test'

test.describe('Welcome Card', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page first
    await page.goto('http://localhost:3000/login')

    // Login with test account
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('**/program', { timeout: 10000 })
  })

  test('should display welcome card at top of program page', async ({
    page,
  }) => {
    // Wait for the page to load
    await page.waitForSelector('[data-testid="program-overview-page"]', {
      timeout: 10000,
    })

    // Wait a bit for API calls to complete
    await page.waitForTimeout(3000)

    // Check if welcome card is present
    const welcomeCard = await page.locator('[data-testid="welcome-card"]')

    // Log what we find on the page for debugging
    const welcomeCardCount = await welcomeCard.count()
    // eslint-disable-next-line no-console
    console.log('Welcome card count:', welcomeCardCount)

    // Take a screenshot for debugging
    await page.screenshot({ path: 'welcome-card-test.png', fullPage: true })

    // Verify welcome card is displayed
    await expect(welcomeCard).toBeVisible({ timeout: 5000 })

    // Verify welcome card content
    await expect(welcomeCard).toContainText('Welcome back')

    // Check for recovery progress indicator
    const recoveryProgress = welcomeCard.locator('svg')
    await expect(recoveryProgress).toBeVisible()

    // Check for coach message
    await expect(welcomeCard).toContainText('Coach says:')

    // Check for action button
    const actionButton = welcomeCard.locator('button')
    await expect(actionButton).toBeVisible()

    // Button should say either "Start Workout" or "Got it, Coach!"
    const buttonText = await actionButton.textContent()
    expect(['Start Workout', 'Got it, Coach!']).toContain(buttonText?.trim())
  })
})
