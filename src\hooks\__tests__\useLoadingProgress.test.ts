import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useLoadingProgress } from '../useLoadingProgress'

describe('useLoadingProgress', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should initialize with 0 progress', () => {
    const { result } = renderHook(() => useLoadingProgress())

    expect(result.current.progress).toBe(0)
    expect(result.current.status).toBe('')
    expect(result.current.isComplete).toBe(false)
  })

  it('should track single task progress', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('auth', 'Authenticating...')
    })

    expect(result.current.tasks).toHaveProperty('auth')
    expect(result.current.tasks.auth).toEqual({
      progress: 0,
      status: 'Authenticating...',
      weight: 1,
    })
  })

  it('should update task progress', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('auth', 'Authenticating...')
      result.current.updateTask('auth', 50)
    })

    expect(result.current.tasks.auth.progress).toBe(50)
    expect(result.current.progress).toBe(50)
  })

  it('should calculate weighted progress for multiple tasks', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('auth', 'Authenticating...', 1)
      result.current.addTask('data', 'Loading data...', 2)
    })

    act(() => {
      result.current.updateTask('auth', 100)
      result.current.updateTask('data', 50)
    })

    // Weighted calculation: (100 * 1 + 50 * 2) / (1 + 2) = 66.67
    expect(result.current.progress).toBeCloseTo(66.67, 0)
  })

  it('should update status when task status changes', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('auth', 'Starting...')
    })

    act(() => {
      result.current.updateTask('auth', 50, 'Authenticating...')
    })

    expect(result.current.status).toBe('Authenticating...')
    expect(result.current.tasks.auth.status).toBe('Authenticating...')
  })

  it('should mark as complete when all tasks reach 100%', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('auth', 'Auth')
      result.current.addTask('data', 'Data')
    })

    act(() => {
      result.current.updateTask('auth', 100)
      result.current.updateTask('data', 100)
    })

    expect(result.current.progress).toBe(100)
    expect(result.current.isComplete).toBe(true)
  })

  it('should not be complete if any task is under 100%', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('auth', 'Auth')
      result.current.addTask('data', 'Data')
    })

    act(() => {
      result.current.updateTask('auth', 100)
      result.current.updateTask('data', 99)
    })

    expect(result.current.progress).toBe(99.5)
    expect(result.current.isComplete).toBe(false)
  })

  it('should remove tasks', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('auth', 'Auth')
      result.current.addTask('data', 'Data')
    })

    act(() => {
      result.current.removeTask('auth')
    })

    expect(result.current.tasks).not.toHaveProperty('auth')
    expect(result.current.tasks).toHaveProperty('data')
  })

  it('should reset all progress', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('auth', 'Auth')
      result.current.updateTask('auth', 50)
    })

    act(() => {
      result.current.reset()
    })

    expect(result.current.progress).toBe(0)
    expect(result.current.status).toBe('')
    expect(result.current.tasks).toEqual({})
    expect(result.current.isComplete).toBe(false)
  })

  it('should handle updating non-existent task gracefully', () => {
    const { result } = renderHook(() => useLoadingProgress())

    expect(() => {
      act(() => {
        result.current.updateTask('nonexistent', 50)
      })
    }).not.toThrow()
  })

  it('should clamp progress values between 0 and 100', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('test', 'Test')
    })

    act(() => {
      result.current.updateTask('test', 150)
    })

    expect(result.current.tasks.test.progress).toBe(100)

    act(() => {
      result.current.updateTask('test', -50)
    })

    expect(result.current.tasks.test.progress).toBe(0)
  })

  it('should use most recent status as overall status', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('auth', 'First status')
      result.current.addTask('data', 'Second status')
      result.current.addTask('workout', 'Third status')
    })

    expect(result.current.status).toBe('Third status')

    act(() => {
      result.current.updateTask('data', 50, 'Updated status')
    })

    expect(result.current.status).toBe('Updated status')
  })

  it('should calculate progress as 0 when no tasks exist', () => {
    const { result } = renderHook(() => useLoadingProgress())

    expect(result.current.progress).toBe(0)
    expect(result.current.isComplete).toBe(false)
  })

  it('should handle tasks with zero weight', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('task1', 'Task 1', 0)
      result.current.addTask('task2', 'Task 2', 1)
    })

    act(() => {
      result.current.updateTask('task1', 100)
      result.current.updateTask('task2', 50)
    })

    // Only task2 should contribute to progress since task1 has 0 weight
    expect(result.current.progress).toBe(50)
  })

  it('should preserve task order for status priority', () => {
    const { result } = renderHook(() => useLoadingProgress())

    act(() => {
      result.current.addTask('task1', 'Status 1')
      result.current.addTask('task2', 'Status 2')
      result.current.addTask('task3', 'Status 3')
    })

    // Should show the most recently updated task's status
    act(() => {
      result.current.updateTask('task1', 10, 'Updated 1')
    })

    expect(result.current.status).toBe('Updated 1')
  })
})
