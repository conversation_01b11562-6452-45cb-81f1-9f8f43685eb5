<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Console Error Monitor</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .monitor { background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { background: #ffebee; color: #c62828; }
        .warning { background: #fff3e0; color: #ef6c00; }
        .info { background: #e3f2fd; color: #1565c0; }
        .count { font-weight: bold; font-size: 1.2em; }
        button { padding: 10px 20px; margin: 5px; background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #1976d2; }
        .log-entry { margin: 5px 0; padding: 5px; border-left: 3px solid #ddd; font-family: monospace; font-size: 12px; }
        .log-error { border-left-color: #f44336; }
        .log-warning { border-left-color: #ff9800; }
        .log-info { border-left-color: #2196f3; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat { text-align: center; }
    </style>
</head>
<body>
    <h1>DrMuscle Console Error Monitor</h1>
    
    <div class="stats">
        <div class="stat">
            <div class="count" id="error-count">0</div>
            <div>Errors</div>
        </div>
        <div class="stat">
            <div class="count" id="warning-count">0</div>
            <div>Warnings</div>
        </div>
        <div class="stat">
            <div class="count" id="info-count">0</div>
            <div>Info</div>
        </div>
        <div class="stat">
            <div class="count" id="total-count">0</div>
            <div>Total</div>
        </div>
    </div>

    <div>
        <button onclick="clearLogs()">Clear Logs</button>
        <button onclick="testWorkoutFlow()">Test Workout Flow</button>
        <button onclick="checkFor404s()">Check for 404s</button>
        <button onclick="checkForLoops()">Check for Loops</button>
    </div>

    <div class="monitor">
        <h3>Console Monitor (Last 100 messages)</h3>
        <div id="console-logs"></div>
    </div>

    <script>
        let errorCount = 0;
        let warningCount = 0;
        let infoCount = 0;
        let totalCount = 0;
        let logs = [];
        let loopDetection = new Map();

        function updateStats() {
            document.getElementById('error-count').textContent = errorCount;
            document.getElementById('warning-count').textContent = warningCount;
            document.getElementById('info-count').textContent = infoCount;
            document.getElementById('total-count').textContent = totalCount;
        }

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = { timestamp, message, type };
            logs.push(logEntry);
            
            // Keep only last 100 logs
            if (logs.length > 100) {
                logs.shift();
            }

            // Update counts
            totalCount++;
            if (type === 'error') errorCount++;
            else if (type === 'warning') warningCount++;
            else infoCount++;

            // Check for potential loops
            checkForPotentialLoop(message);

            updateStats();
            renderLogs();
        }

        function checkForPotentialLoop(message) {
            const key = message.substring(0, 100); // Use first 100 chars as key
            const now = Date.now();
            
            if (!loopDetection.has(key)) {
                loopDetection.set(key, { count: 1, firstSeen: now, lastSeen: now });
            } else {
                const data = loopDetection.get(key);
                data.count++;
                data.lastSeen = now;
                
                // If we see the same message more than 10 times in 5 seconds, it's likely a loop
                if (data.count > 10 && (now - data.firstSeen) < 5000) {
                    addLog(`🔄 POTENTIAL LOOP DETECTED: "${key}" repeated ${data.count} times`, 'error');
                }
            }

            // Clean up old entries
            for (const [k, v] of loopDetection.entries()) {
                if (now - v.lastSeen > 10000) { // 10 seconds
                    loopDetection.delete(k);
                }
            }
        }

        function renderLogs() {
            const container = document.getElementById('console-logs');
            container.innerHTML = logs.slice(-50).map(log => 
                `<div class="log-entry log-${log.type}">
                    [${log.timestamp}] ${log.message}
                </div>`
            ).join('');
            container.scrollTop = container.scrollHeight;
        }

        function clearLogs() {
            logs = [];
            errorCount = 0;
            warningCount = 0;
            infoCount = 0;
            totalCount = 0;
            loopDetection.clear();
            updateStats();
            renderLogs();
        }

        // Override console methods to capture logs
        const originalError = console.error;
        const originalWarn = console.warn;
        const originalLog = console.log;
        const originalInfo = console.info;

        console.error = function(...args) {
            const message = args.join(' ');
            addLog(`❌ ${message}`, 'error');
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            const message = args.join(' ');
            addLog(`⚠️ ${message}`, 'warning');
            originalWarn.apply(console, args);
        };

        console.log = function(...args) {
            const message = args.join(' ');
            // Filter out some noisy logs
            if (!message.includes('Download the React DevTools') && 
                !message.includes('webpack-internal://')) {
                addLog(`📝 ${message}`, 'info');
            }
            originalLog.apply(console, args);
        };

        console.info = function(...args) {
            const message = args.join(' ');
            addLog(`ℹ️ ${message}`, 'info');
            originalInfo.apply(console, args);
        };

        async function testWorkoutFlow() {
            addLog('🧪 Starting workout flow test...', 'info');
            
            try {
                // Check if user is logged in
                const authResponse = await fetch('/api/auth/token');
                const authData = await authResponse.json();
                
                if (!authData.authenticated) {
                    addLog('❌ User not authenticated - please log in first', 'error');
                    return;
                }

                addLog('✅ User is authenticated', 'info');

                // Navigate to workout page
                addLog('📍 Navigating to workout page...', 'info');
                window.location.href = '/workout';
                
            } catch (error) {
                addLog(`❌ Test failed: ${error.message}`, 'error');
            }
        }

        async function checkFor404s() {
            addLog('🔍 Checking for 404 errors in recent logs...', 'info');
            
            const recent404s = logs.filter(log => 
                log.message.includes('404') || 
                log.message.includes('Not Found') ||
                log.message.includes('Failed to fetch')
            );

            if (recent404s.length === 0) {
                addLog('✅ No 404 errors found in recent logs', 'info');
            } else {
                addLog(`⚠️ Found ${recent404s.length} potential 404 errors:`, 'warning');
                recent404s.forEach(log => {
                    addLog(`   - ${log.message}`, 'warning');
                });
            }
        }

        function checkForLoops() {
            addLog('🔄 Checking for potential infinite loops...', 'info');
            
            if (loopDetection.size === 0) {
                addLog('✅ No potential loops detected', 'info');
            } else {
                addLog(`⚠️ Found ${loopDetection.size} potential loop patterns:`, 'warning');
                for (const [key, data] of loopDetection.entries()) {
                    if (data.count > 5) {
                        addLog(`   - "${key.substring(0, 50)}..." repeated ${data.count} times`, 'warning');
                    }
                }
            }
        }

        // Monitor network requests
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            addLog(`🌐 Fetch: ${url}`, 'info');
            
            return originalFetch.apply(this, args).then(response => {
                if (!response.ok) {
                    addLog(`❌ Fetch failed: ${response.status} ${url}`, 'error');
                }
                return response;
            }).catch(error => {
                addLog(`❌ Fetch error: ${error.message} for ${url}`, 'error');
                throw error;
            });
        };

        // Initial message
        addLog('🚀 Console monitor started. Navigate to the workout page to test.', 'info');
        updateStats();
    </script>
</body>
</html>
