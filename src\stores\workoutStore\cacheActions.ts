/**
 * Cache-related actions for the workout store
 * This file re-exports cache actions from separate modules
 */

import type { WorkoutState } from './types'
import { createGetCacheActions } from './getCacheActions'
import { createSetCacheActions } from './setCacheActions'
import { createCacheStatsActions } from './cacheStatsActions'

export const createCacheActions = (
  set: (
    partial:
      | Partial<WorkoutState>
      | ((state: WorkoutState) => Partial<WorkoutState>)
  ) => void,
  get: () => WorkoutState
) => ({
  ...createGetCacheActions(set, get),
  ...createSetCacheActions(set, get),
  ...createCacheStatsActions(set, get),
})
