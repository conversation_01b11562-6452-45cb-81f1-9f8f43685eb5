import { test, expect } from '@playwright/test'

// Use mobile viewport by default
test.use({
  viewport: { width: 390, height: 844 }, // iPhone 13
  userAgent:
    'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
})

test.describe('Workout Display After Continue', () => {
  test('should display exercises after clicking Continue to workout', async ({
    page,
  }) => {
    // Login first
    await page.goto('/login')
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('/program', { timeout: 10000 })

    // Navigate to program page
    await page.goto('/program')

    // Wait for program to load
    await page.waitForSelector('text=/Week \\d+, Day \\d+/')

    // Click Continue to workout button
    await page.click('button:has-text("Continue to workout")')

    // Wait for navigation to workout page
    await page.waitForURL('/workout')

    // Wait for "checking for updates" to appear and disappear
    await page.waitForSelector('text=checking for updates', {
      state: 'visible',
    })
    await page.waitForSelector('text=checking for updates', {
      state: 'hidden',
      timeout: 10000,
    })

    // Verify exercises are displayed (not "0 exercises")
    await expect(page.locator('text=/\\d+ exercises?/').first()).toBeVisible()

    // Verify the exercise count is not 0
    const exerciseText = await page
      .locator('text=/\\d+ exercises?/')
      .first()
      .textContent()
    const exerciseCount = parseInt(exerciseText?.match(/(\d+)/)?.[1] || '0')
    expect(exerciseCount).toBeGreaterThan(0)

    // Verify at least one exercise is visible
    await expect(
      page.locator('[data-testid="exercise-card"]').first()
    ).toBeVisible()

    // Check console for any errors
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // Wait a bit to catch any delayed errors
    await page.waitForTimeout(2000)

    // Verify no "Result is not an array" error in console
    const hasResultError = consoleErrors.some((error) =>
      error.includes('GetUserWorkout: Result is not an array')
    )
    expect(hasResultError).toBe(false)
  })
})
