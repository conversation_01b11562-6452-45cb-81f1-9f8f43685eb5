/**
 * Example usage of AuthStore UserInfo caching functionality
 *
 * The authStore now includes a 1-week cache for UserInfo data to support
 * progressive loading on the program page.
 */

import { useAuthStore } from './authStore'

// Example 1: Setting cached UserInfo after API call
export function cacheUserInfoExample() {
  const { setCachedUserInfo } = useAuthStore.getState()

  // After receiving UserInfo from API
  const userInfoFromAPI = {
    firstName: 'John',
    lastName: 'Doe',
    customField: 'Additional data from API',
    // ... other fields
  }

  // Cache the data (automatically adds timestamp and version)
  setCachedUserInfo(userInfoFromAPI)
}

// Example 2: Retrieving cached UserInfo
export function getCachedUserInfoExample() {
  const { getCachedUserInfo, isCacheStale } = useAuthStore.getState()

  // Check if cache is stale (older than 1 week)
  if (isCacheStale()) {
    // Cache is stale, should fetch fresh data
  }

  // Get cached data (returns null if stale or version mismatch)
  const cachedData = getCachedUserInfo()

  if (cachedData) {
    // Using cached UserInfo
    // Use cached.firstName, cached.lastName, etc.
  } else {
    // No valid cache, need to fetch from API
  }

  return cachedData
}

// Simulated API call for examples
async function fetchUserInfoFromAPI() {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  return {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    customField: 'API data',
  }
}

// Example 3: Progressive loading pattern
export function useProgressiveUserInfo() {
  const { user, updateUser, getCachedUserInfo, setCachedUserInfo } =
    useAuthStore()

  // 1. Check cache first (but don't display cached values initially per requirements)
  const cached = getCachedUserInfo()
  const needsFetch = !cached || useAuthStore.getState().isCacheStale()

  // 2. If needed, fetch fresh data from API
  if (needsFetch) {
    // Simulate API call
    fetchUserInfoFromAPI()
      .then((freshData) => {
        // 3. Update cache
        setCachedUserInfo(freshData)

        // 4. Update user in store for immediate display
        updateUser({
          firstName: freshData.firstName,
          lastName: freshData.lastName,
        })
      })
      .catch(() => {
        // Handle error silently in example
      })
  }

  return {
    isLoading: needsFetch,
    firstName: user?.firstName,
    lastName: user?.lastName,
  }
}

// Example 4: Cache is automatically cleared on logout
export function logoutExample() {
  const { logout } = useAuthStore.getState()

  // This will clear all auth data including cached UserInfo
  logout()
}

// Example 5: Manual cache clearing if needed
export function clearCacheExample() {
  const { clearUserInfoCache } = useAuthStore.getState()

  // Manually clear just the UserInfo cache
  clearUserInfoCache()
}

/**
 * Cache Features:
 *
 * 1. **1-Week TTL**: Cache expires after 7 days
 * 2. **Version Management**: Cache is invalidated on version changes
 * 3. **Persistence**: Cache survives page refreshes (stored in localStorage)
 * 4. **Performance Tracking**: Operations are timed and logged in development
 * 5. **Auto-clear on Logout**: Cache is cleared when user logs out
 * 6. **Type-safe**: Full TypeScript support with CachedUserInfo interface
 */
