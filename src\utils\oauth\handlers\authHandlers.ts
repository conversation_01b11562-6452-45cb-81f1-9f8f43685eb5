import { User, UserCredential, AuthError } from 'firebase/auth'
import type {
  OAuthUserData,
  OAuthSuccessCallback,
  OAuthErrorCallback,
  OAuthError,
  OAuthProvider as OAuthProviderType,
  GoogleTokenPayload,
  AppleTokenPayload,
} from '../../../types/oauth'

export class AuthHandlers {
  static async handleAuthSuccess(
    userCredential: UserCredential,
    providerType: OAuthProviderType,
    onSuccess: OAuthSuccessCallback,
    onError: OAuthErrorCallback
  ): Promise<void> {
    try {
      const { user } = userCredential
      const userData = await this.extractUserData(
        user,
        userCredential,
        providerType
      )
      onSuccess(userData)
    } catch (error) {
      const oauthError: OAuthError = {
        code: 'invalid_token',
        message: 'Failed to process authentication data',
        provider: providerType,
        providerError: error,
      }
      onError(oauthError)
    }
  }

  static async extractUserData(
    user: User,
    credential: UserCredential,
    providerType: OAuthProviderType
  ): Promise<OAuthUserData> {
    const idToken = await user.getIdToken()

    const baseTokenPayload = {
      sub: user.uid,
      email: user.email || '',
      email_verified: user.emailVerified,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600,
      iss:
        providerType === 'google'
          ? 'accounts.google.com'
          : 'https://appleid.apple.com',
      aud:
        providerType === 'google'
          ? '************-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com'
          : 'com.drmaxmuscle.max',
    }

    const userData: OAuthUserData = {
      provider: providerType,
      providerId: user.uid,
      email: user.email || '',
      emailVerified: user.emailVerified,
      name: user.displayName || undefined,
      pictureUrl: user.photoURL || undefined,
      rawToken: idToken,
      tokenPayload:
        providerType === 'google'
          ? ({
              ...baseTokenPayload,
              name: user.displayName,
              picture: user.photoURL,
            } as GoogleTokenPayload)
          : (baseTokenPayload as AppleTokenPayload),
      metadata: {
        firebaseUid: user.uid,
        providerId: credential.providerId,
      },
    }

    if (providerType === 'google' && credential.providerId === 'google.com') {
      this.extractGoogleData(userData, credential)
    } else if (
      providerType === 'apple' &&
      credential.providerId === 'apple.com'
    ) {
      this.extractAppleData(userData, credential, user)
    }

    return userData
  }

  private static extractGoogleData(
    userData: OAuthUserData,
    credential: UserCredential
  ): void {
    const credentialWithResponse = credential as UserCredential & {
      _tokenResponse?: {
        firstName?: string
        given_name?: string
        lastName?: string
        family_name?: string
        idToken?: string
      }
    }
    const profile = credentialWithResponse._tokenResponse
    if (profile) {
      userData.firstName = profile.firstName || profile.given_name
      userData.lastName = profile.lastName || profile.family_name
      if (userData.metadata) {
        userData.metadata.googleIdToken = profile.idToken
      }
    }
  }

  private static extractAppleData(
    userData: OAuthUserData,
    credential: UserCredential,
    user: User
  ): void {
    const credentialWithResponse = credential as UserCredential & {
      _tokenResponse?: {
        fullName?: string
        federatedId?: string
        idToken?: string
      }
    }
    const profile = credentialWithResponse._tokenResponse
    if (profile) {
      if (profile.fullName) {
        const names = profile.fullName.split(' ')
        const [firstName, ...lastNameParts] = names
        userData.firstName = firstName
        userData.lastName = lastNameParts.join(' ')
      }
      if (userData.metadata) {
        userData.metadata.appleUserId = profile.federatedId || user.uid
        userData.metadata.appleIdToken = profile.idToken
      }
    }
  }

  static handleAuthError(
    error: AuthError,
    providerType: OAuthProviderType,
    onError: OAuthErrorCallback
  ): void {
    let oauthError: OAuthError

    switch (error.code) {
      case 'auth/popup-blocked':
      case 'auth/popup-closed-by-user':
      case 'auth/cancelled-popup-request':
        oauthError = {
          code: 'user_cancelled',
          message: 'Sign-in cancelled by user',
          provider: providerType,
          providerError: error,
        }
        break

      case 'auth/network-request-failed':
        oauthError = {
          code: 'network_error',
          message: 'Network error during sign-in',
          provider: providerType,
          providerError: error,
        }
        break

      case 'auth/invalid-credential':
      case 'auth/invalid-oauth-provider':
      case 'auth/invalid-oauth-client-id':
        oauthError = {
          code: 'invalid_token',
          message: 'Invalid authentication credentials',
          provider: providerType,
          providerError: error,
        }
        break

      case 'auth/unauthorized-domain':
        oauthError = {
          code: 'provider_error',
          message: 'This domain is not authorized for OAuth operations',
          provider: providerType,
          providerError: error,
          details: {
            hint: 'Add this domain to Firebase Console > Authentication > Settings > Authorized domains',
          },
        }
        break

      default:
        oauthError = {
          code: 'oauth_failed',
          message: error.message || 'Authentication failed',
          provider: providerType,
          providerError: error,
        }
    }

    onError(oauthError)
  }
}
