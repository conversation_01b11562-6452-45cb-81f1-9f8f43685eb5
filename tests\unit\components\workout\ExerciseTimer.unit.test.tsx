import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, act } from '@testing-library/react'
import { ExerciseTimer } from '@/components/workout/ExerciseTimer'

describe('ExerciseTimer Unit Tests', () => {
  const mockOnComplete = vi.fn()

  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.clearAllTimers()
    vi.useRealTimers()
  })

  it('should display initial rest time', () => {
    render(<ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />)
    expect(screen.getByText('1:30')).toBeInTheDocument()
  })

  it('should start timer on button click', () => {
    render(<ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />)

    const startButton = screen.getByRole('button', { name: /start/i })
    act(() => {
      startButton.click()
    })

    expect(screen.getByRole('button', { name: /pause/i })).toBeInTheDocument()
  })

  it('should countdown when timer is running', () => {
    render(<ExerciseTimer restDuration={3} onRestComplete={mockOnComplete} />)

    // Start timer
    const startButton = screen.getByRole('button', { name: /start/i })
    act(() => {
      startButton.click()
    })

    // Initial state
    expect(screen.getByText('0:03')).toBeInTheDocument()

    // After 1 second
    act(() => {
      vi.advanceTimersByTime(1000)
    })
    expect(screen.getByText('0:02')).toBeInTheDocument()

    // After 2 seconds
    act(() => {
      vi.advanceTimersByTime(1000)
    })
    expect(screen.getByText('0:01')).toBeInTheDocument()
  })

  it('should call onComplete when timer reaches zero', () => {
    render(<ExerciseTimer restDuration={1} onRestComplete={mockOnComplete} />)

    const startButton = screen.getByRole('button', { name: /start/i })
    act(() => {
      startButton.click()
    })

    // Advance timer to completion
    act(() => {
      vi.advanceTimersByTime(1000)
    })

    expect(mockOnComplete).toHaveBeenCalled()
    expect(screen.getByText('0:00')).toBeInTheDocument()
  })

  it('should pause timer', () => {
    render(<ExerciseTimer restDuration={10} onRestComplete={mockOnComplete} />)

    // Start timer
    const startButton = screen.getByRole('button', { name: /start/i })
    act(() => {
      startButton.click()
    })

    // Advance 2 seconds
    act(() => {
      vi.advanceTimersByTime(2000)
    })
    expect(screen.getByText('0:08')).toBeInTheDocument()

    // Pause
    const pauseButton = screen.getByRole('button', { name: /pause/i })
    act(() => {
      pauseButton.click()
    })

    // Advance time while paused
    act(() => {
      vi.advanceTimersByTime(2000)
    })

    // Time should not change
    expect(screen.getByText('0:08')).toBeInTheDocument()
  })

  it('should reset timer', () => {
    render(<ExerciseTimer restDuration={10} onRestComplete={mockOnComplete} />)

    // Start timer
    const startButton = screen.getByRole('button', { name: /start/i })
    act(() => {
      startButton.click()
    })

    // Advance timer
    act(() => {
      vi.advanceTimersByTime(5000)
    })
    expect(screen.getByText('0:05')).toBeInTheDocument()

    // Reset
    const resetButton = screen.getByRole('button', { name: /reset/i })
    act(() => {
      resetButton.click()
    })

    expect(screen.getByText('0:10')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /start/i })).toBeInTheDocument()
  })

  it('should skip rest timer', () => {
    render(<ExerciseTimer restDuration={90} onRestComplete={mockOnComplete} />)

    const skipButton = screen.getByRole('button', { name: /skip rest/i })
    act(() => {
      skipButton.click()
    })

    expect(mockOnComplete).toHaveBeenCalled()
  })

  it('should display exercise timer when exercising', () => {
    render(
      <ExerciseTimer
        isExercising
        restDuration={90}
        onRestComplete={mockOnComplete}
      />
    )

    expect(screen.getByText(/exercise time/i)).toBeInTheDocument()
    expect(screen.getByText('0:00')).toBeInTheDocument()
  })

  it('should show progress bar when timer is running', () => {
    render(<ExerciseTimer restDuration={10} onRestComplete={mockOnComplete} />)

    const startButton = screen.getByRole('button', { name: /start/i })
    act(() => {
      startButton.click()
    })

    const progressBar = screen.getByRole('progressbar')
    expect(progressBar).toBeInTheDocument()
  })
})
