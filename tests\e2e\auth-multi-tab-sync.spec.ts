import { test, expect, BrowserContext, Page } from '@playwright/test'

const TEST_USER = {
  email: '<EMAIL>',
  password: 'Dr123456',
}

test.describe('Multi-tab Session Synchronization', () => {
  let context: BrowserContext
  let page1: Page
  let page2: Page

  test.beforeEach(async ({ browser }) => {
    // Create a new browser context with localStorage enabled
    context = await browser.newContext({
      storageState: undefined,
    })

    // Create two tabs
    page1 = await context.newPage()
    page2 = await context.newPage()

    // Navigate both tabs to the app
    await page1.goto('/')
    await page2.goto('/')
  })

  test.afterEach(async () => {
    await context.close()
  })

  test('should sync login across tabs', async () => {
    // Login in first tab
    await page1.goto('/login')
    await page1.fill('input[type="email"]', TEST_USER.email)
    await page1.fill('input[type="password"]', TEST_USER.password)
    await page1.click('button[type="submit"]')

    // Wait for successful login
    await page1.waitForURL('/program', { timeout: 10000 })

    // Check that second tab is also logged in
    await page2.reload()
    await page2.waitForTimeout(1000) // Wait for storage event

    // Second tab should redirect to program page when accessing protected route
    await page2.goto('/workout')
    await expect(page2).toHaveURL('/workout')

    // Verify auth state in localStorage is synced
    const authState1 = await page1.evaluate(() =>
      localStorage.getItem('auth-storage')
    )
    const authState2 = await page2.evaluate(() =>
      localStorage.getItem('auth-storage')
    )
    expect(authState1).toEqual(authState2)
  })

  test('should sync logout across tabs', async () => {
    // First login in tab 1
    await page1.goto('/login')
    await page1.fill('input[type="email"]', TEST_USER.email)
    await page1.fill('input[type="password"]', TEST_USER.password)
    await page1.click('button[type="submit"]')
    await page1.waitForURL('/program')

    // Verify tab 2 is also logged in
    await page2.reload()
    await page2.waitForTimeout(1000)
    await page2.goto('/program')
    await expect(page2).toHaveURL('/program')

    // Logout from tab 1
    await page1.click('button[aria-label="User menu"]')
    await page1.click('text=Sign out')
    await page1.waitForURL('/login')

    // Check tab 2 is logged out
    await page2.waitForTimeout(1000) // Wait for storage event
    await page2.reload()
    await expect(page2).toHaveURL('/login')

    // Verify auth state is cleared in both tabs
    const authState1 = await page1.evaluate(() =>
      localStorage.getItem('auth-storage')
    )
    const authState2 = await page2.evaluate(() =>
      localStorage.getItem('auth-storage')
    )
    expect(authState1).toBeNull()
    expect(authState2).toBeNull()
  })

  test('should sync token refresh across tabs', async () => {
    // Login in first tab
    await page1.goto('/login')
    await page1.fill('input[type="email"]', TEST_USER.email)
    await page1.fill('input[type="password"]', TEST_USER.password)
    await page1.click('button[type="submit"]')
    await page1.waitForURL('/program')

    // Get initial token
    const initialToken = await page1.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      return authState ? JSON.parse(authState).state.token : null
    })
    expect(initialToken).toBeTruthy()

    // Simulate token refresh by modifying localStorage
    await page1.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      if (authState) {
        const parsed = JSON.parse(authState)
        parsed.state.token = 'refreshed-token-123'
        localStorage.setItem('auth-storage', JSON.stringify(parsed))
        // Dispatch storage event manually
        window.dispatchEvent(
          new StorageEvent('storage', {
            key: 'auth-storage',
            newValue: JSON.stringify(parsed),
            oldValue: authState,
            storageArea: localStorage,
          })
        )
      }
    })

    // Wait for sync
    await page2.waitForTimeout(1000)

    // Check token is updated in second tab
    const updatedToken = await page2.evaluate(() => {
      const authState = localStorage.getItem('auth-storage')
      return authState ? JSON.parse(authState).state.token : null
    })
    expect(updatedToken).toBe('refreshed-token-123')
  })

  test('should handle concurrent OAuth logins', async () => {
    // Start OAuth login process in both tabs simultaneously
    await Promise.all([page1.goto('/login'), page2.goto('/login')])

    // Click Google sign in on both tabs
    const [popup1] = await Promise.all([
      page1.waitForEvent('popup'),
      page1.click('button:has-text("Continue with Google")'),
    ])

    const [popup2] = await Promise.all([
      page2.waitForEvent('popup'),
      page2.click('button:has-text("Continue with Google")'),
    ])

    // Close one popup to simulate user canceling
    await popup2.close()

    // Complete OAuth in first popup (mock the OAuth flow)
    // In real test, you would complete the Google OAuth flow
    await popup1.close()

    // Mock successful OAuth callback
    await page1.evaluate(() => {
      const authState = {
        state: {
          token: 'oauth-token',
          user: { id: 'user-123', email: '<EMAIL>' },
          isAuthenticated: true,
        },
      }
      localStorage.setItem('auth-storage', JSON.stringify(authState))
      window.dispatchEvent(
        new StorageEvent('storage', {
          key: 'auth-storage',
          newValue: JSON.stringify(authState),
          storageArea: localStorage,
        })
      )
    })

    // Both tabs should be logged in
    await page1.waitForURL('/program')
    await page2.reload()
    await page2.goto('/program')
    await expect(page2).toHaveURL('/program')
  })

  test('should maintain session after browser restart', async () => {
    // Login in first tab
    await page1.goto('/login')
    await page1.fill('input[type="email"]', TEST_USER.email)
    await page1.fill('input[type="password"]', TEST_USER.password)
    await page1.click('button[type="submit"]')
    await page1.waitForURL('/program')

    // Save storage state
    const storageState = await context.storageState()

    // Close context and create new one with saved state
    await context.close()
    context = await test.browser!.newContext({
      storageState,
    })

    // Open new tab in new context
    const newPage = await context.newPage()
    await newPage.goto('/program')

    // Should remain logged in
    await expect(newPage).toHaveURL('/program')
    expect(await newPage.textContent('body')).not.toContain('Sign in')
  })

  test('should handle storage quota exceeded', async () => {
    // Fill localStorage near quota
    await page1.evaluate(() => {
      const largeData = 'x'.repeat(1024 * 1024 * 4) // 4MB
      try {
        for (let i = 0; i < 5; i++) {
          localStorage.setItem(`large-data-${i}`, largeData)
        }
      } catch (e) {
        // Quota exceeded, which is what we want
      }
    })

    // Try to login
    await page1.goto('/login')
    await page1.fill('input[type="email"]', TEST_USER.email)
    await page1.fill('input[type="password"]', TEST_USER.password)
    await page1.click('button[type="submit"]')

    // Should handle gracefully and still login
    await expect(page1).toHaveURL('/program')

    // Clean up
    await page1.evaluate(() => {
      for (let i = 0; i < 5; i++) {
        localStorage.removeItem(`large-data-${i}`)
      }
    })
  })

  test('should handle storage disabled', async () => {
    // Create context with cookies disabled
    await context.close()
    context = await test.browser!.newContext({
      acceptDownloads: false,
      javaScriptEnabled: true,
      // Disable storage
      permissions: [],
    })

    const page = await context.newPage()

    // Override localStorage to throw
    await page.addInitScript(() => {
      Object.defineProperty(window, 'localStorage', {
        get() {
          throw new Error('localStorage is disabled')
        },
      })
    })

    await page.goto('/login')
    await page.fill('input[type="email"]', TEST_USER.email)
    await page.fill('input[type="password"]', TEST_USER.password)
    await page.click('button[type="submit"]')

    // Should show appropriate error message
    await expect(page.locator('text=Storage is disabled')).toBeVisible()
  })

  test('should sync user preferences across tabs', async () => {
    // Login first
    await page1.goto('/login')
    await page1.fill('input[type="email"]', TEST_USER.email)
    await page1.fill('input[type="password"]', TEST_USER.password)
    await page1.click('button[type="submit"]')
    await page1.waitForURL('/program')

    // Change theme in tab 1
    await page1.click('button[aria-label="Toggle theme"]')

    // Check theme is synced to tab 2
    await page2.reload()
    await page2.waitForTimeout(1000)

    const theme1 = await page1.evaluate(
      () => document.documentElement.dataset.theme
    )
    const theme2 = await page2.evaluate(
      () => document.documentElement.dataset.theme
    )
    expect(theme1).toEqual(theme2)
  })

  test('should handle race conditions during simultaneous updates', async () => {
    // Login first
    await page1.goto('/login')
    await page1.fill('input[type="email"]', TEST_USER.email)
    await page1.fill('input[type="password"]', TEST_USER.password)
    await page1.click('button[type="submit"]')
    await page1.waitForURL('/program')

    await page2.reload()

    // Simulate simultaneous updates
    await Promise.all([
      page1.evaluate(() => {
        const authState = localStorage.getItem('auth-storage')
        if (authState) {
          const parsed = JSON.parse(authState)
          parsed.state.lastUpdate = 'tab1'
          localStorage.setItem('auth-storage', JSON.stringify(parsed))
        }
      }),
      page2.evaluate(() => {
        const authState = localStorage.getItem('auth-storage')
        if (authState) {
          const parsed = JSON.parse(authState)
          parsed.state.lastUpdate = 'tab2'
          localStorage.setItem('auth-storage', JSON.stringify(parsed))
        }
      }),
    ])

    // Wait for potential conflicts to resolve
    await page1.waitForTimeout(1000)
    await page2.waitForTimeout(1000)

    // Both tabs should have consistent state (last write wins)
    const state1 = await page1.evaluate(() =>
      localStorage.getItem('auth-storage')
    )
    const state2 = await page2.evaluate(() =>
      localStorage.getItem('auth-storage')
    )
    expect(state1).toEqual(state2)
  })
})
