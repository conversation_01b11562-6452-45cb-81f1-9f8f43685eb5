import { describe, it, expect, expectTypeOf } from 'vitest'
import type {
  WorkoutTemplateGroupModel,
  WorkoutTemplateModel,
  ExerciseModel,
  ExerciseModelWithReco,
  RecommendationModel,
  WarmUps,
  LoginModel,
  LoginSuccessResult,
  RegisterModel,
  WorkoutLogSerieModel,
  NewExerciceLogModel,
  GetRecommendationForExerciseModel,
  GetUserWorkoutLogAverageResponse,
  GetUserWorkoutLogDate,
  MultiUnityWeight,
  ApiResponse,
  BooleanModel,
} from '@/types/api'

describe('API Types', () => {
  describe('Workout Models', () => {
    it('should correctly type WorkoutTemplateGroupModel', () => {
      const workoutGroup: WorkoutTemplateGroupModel = {
        Id: 1,
        IsFeaturedProgram: true,
        UserId: 'user123',
        Label: 'Beginner Program',
        WorkoutTemplates: [],
        IsSystemExercise: false,
        RequiredWorkoutToLevelUp: 5,
        Level: 1,
        RemainingToLevelUp: 3,
        NextProgramId: 2,
        ProgramId: 1,
      }

      expectTypeOf(workoutGroup).toMatchTypeOf<WorkoutTemplateGroupModel>()
      expect(workoutGroup.Id).toBe(1)
      expect(workoutGroup.Label).toBe('Beginner Program')
    })

    it('should correctly type WorkoutTemplateModel', () => {
      const workout: WorkoutTemplateModel = {
        Id: 1,
        UserId: 'user123',
        Label: 'Push Day',
        Exercises: [],
        IsSystemExercise: false,
        WorkoutSettingsModel: {} as any, // Will be defined later
      }

      expectTypeOf(workout).toMatchTypeOf<WorkoutTemplateModel>()
      expect(workout.Label).toBe('Push Day')
    })

    it('should correctly type ExerciseModel', () => {
      const exercise: ExerciseModel = {
        Id: 1,
        Label: 'Bench Press',
        IsSystemExercise: true,
        IsSwapTarget: false,
        IsFinished: false,
        BodyPartId: 1,
        IsUnilateral: false,
        IsTimeBased: false,
        EquipmentId: 2,
        IsEasy: false,
        IsMedium: true,
        IsBodyweight: false,
        VideoUrl: 'https://example.com/video.mp4',
        IsNextExercise: true,
        IsPlate: true,
        IsWeighted: true,
        IsPyramid: false,
        RepsMaxValue: 15,
        RepsMinValue: 8,
        Timer: 90,
        IsNormalSets: true,
        WorkoutGroupId: 1,
        IsBodypartPriority: false,
        IsFlexibility: false,
        IsOneHanded: false,
        LocalVideo: '/videos/bench-press.mp4',
        IsAssisted: false,
      }

      expectTypeOf(exercise).toMatchTypeOf<ExerciseModel>()
      expect(exercise.Label).toBe('Bench Press')
    })

    it('should correctly type ExerciseModelWithReco', () => {
      const exerciseWithReco: ExerciseModelWithReco = {
        Id: 1,
        Label: 'Bench Press',
        IsSystemExercise: true,
        IsSwapTarget: false,
        IsFinished: false,
        IsUnilateral: false,
        IsTimeBased: false,
        IsEasy: false,
        IsMedium: true,
        IsBodyweight: false,
        VideoUrl: 'https://example.com/video.mp4',
        IsNextExercise: true,
        IsPlate: true,
        IsWeighted: true,
        IsPyramid: false,
        IsNormalSets: true,
        IsBodypartPriority: false,
        IsFlexibility: false,
        IsOneHanded: false,
        LocalVideo: '/videos/bench-press.mp4',
        IsAssisted: false,
        RecoModel: {} as RecommendationModel,
        IsSelected: true,
      }

      expectTypeOf(exerciseWithReco).toMatchTypeOf<ExerciseModelWithReco>()
      expect(exerciseWithReco.IsSelected).toBe(true)
    })
  })

  describe('Recommendation Models', () => {
    it('should correctly type RecommendationModel', () => {
      const recommendation: RecommendationModel = {
        Series: 3,
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        OneRMProgress: 85.5,
        RecommendationInKg: 61.23,
        OneRMPercentage: 75,
        WarmUpReps1: 5,
        WarmUpReps2: 3,
        WarmUpWeightSet1: { Lb: 95, Kg: 43.09 },
        WarmUpWeightSet2: { Lb: 115, Kg: 52.16 },
        WarmUpsList: [],
        WarmupsCount: 2,
        RpRest: 180,
        NbPauses: 1,
        NbRepsPauses: 3,
        IsEasy: false,
        IsMedium: true,
        IsBodyweight: false,
        Increments: { Lb: 5, Kg: 2.5 },
        Max: { Lb: 200, Kg: 90.72 },
        Min: { Lb: 45, Kg: 20.41 },
        IsNormalSets: true,
        IsDeload: false,
        IsBackOffSet: false,
        BackOffSetWeight: { Lb: 0, Kg: 0 },
        IsMaxChallenge: false,
        IsLightSession: false,
        FirstWorkSetReps: 10,
        FirstWorkSetWeight: { Lb: 135, Kg: 61.23 },
        FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
        IsPyramid: false,
        IsReversePyramid: false,
        HistorySet: [],
        ReferenceSetHistory: {} as WorkoutLogSerieModel,
        MinReps: 8,
        MaxReps: 12,
        isPlateAvailable: true,
        isDumbbellAvailable: false,
        isPulleyAvailable: false,
        isBandsAvailable: false,
        Speed: 3,
        IsManual: false,
        ReferenseReps: 10,
        ReferenseWeight: { Lb: 135, Kg: 61.23 },
        IsDropSet: false,
      }

      expectTypeOf(recommendation).toMatchTypeOf<RecommendationModel>()
      expect(recommendation.Series).toBe(3)
      expect(recommendation.Reps).toBe(10)
    })

    it('should correctly type WarmUps', () => {
      const warmup: WarmUps = {
        WarmUpReps: 5,
        WarmUpWeightSet: { Lb: 95, Kg: 43.09 },
      }

      expectTypeOf(warmup).toMatchTypeOf<WarmUps>()
      expect(warmup.WarmUpReps).toBe(5)
    })
  })

  describe('Authentication Models', () => {
    it('should correctly type LoginModel', () => {
      const loginData: LoginModel = {
        Username: '<EMAIL>', // This is the email
        Password: 'securePassword123',
        NewPassword: 'newSecurePassword456',
        Validation: 'validationCode',
      }

      expectTypeOf(loginData).toMatchTypeOf<LoginModel>()
      expect(loginData.Username).toBe('<EMAIL>')
    })

    it('should correctly type LoginSuccessResult', () => {
      const loginResult: LoginSuccessResult = {
        access_token: 'eyJhbGciOiJIUzI1NiIsInR...',
        token_type: 'Bearer',
        expires_in: 86400,
        userName: '<EMAIL>',
        issued: '2024-01-01T00:00:00Z',
        expires: '2024-01-02T00:00:00Z',
      }

      expectTypeOf(loginResult).toMatchTypeOf<LoginSuccessResult>()
      expect(loginResult.token_type).toBe('Bearer')
    })

    it('should correctly type RegisterModel', () => {
      const registerData: RegisterModel = {
        Email: '<EMAIL>',
        Password: 'securePassword123',
        ConfirmPassword: 'securePassword123',
        Firstname: 'John',
        Lastname: 'Doe',
        BirthDate: new Date('1990-01-01'),
        MassUnit: 'lbs',
        Gender: 'Male',
        IsAthlete: true,
        IsVegan: false,
        IsPurchasedPlan: false,
        BodyWeight: { Lb: 180, Kg: 81.65 },
        IsHumanSupport: false,
      }

      expectTypeOf(registerData).toMatchTypeOf<RegisterModel>()
      expect(registerData.Email).toBe('<EMAIL>')
    })
  })

  describe('Logging Models', () => {
    it('should correctly type WorkoutLogSerieModel', () => {
      const setLog: WorkoutLogSerieModel = {
        Id: 1,
        UserId: 'user123',
        ExerciseId: 5,
        Reps: 10,
        Weight: { Lb: 135, Kg: 61.23 },
        RIR: 2,
        IsWarmups: false,
        IsNext: false,
        IsFinished: true,
        IsEditing: false,
        SetNo: '1',
        RepsValue: '10',
        WeightValue: '135',
      }

      expectTypeOf(setLog).toMatchTypeOf<WorkoutLogSerieModel>()
      expect(setLog.Reps).toBe(10)
    })

    it('should correctly type NewExerciceLogModel', () => {
      const newLog: NewExerciceLogModel = {
        ExerciceId: 5,
        Weight1: { Lb: 135, Kg: 61.23 },
        Weight2: { Lb: 135, Kg: 61.23 },
        Reps1: '10',
        Reps2: '8',
        Username: 'user123',
        RIR: 2,
        VersionNo: 1,
      }

      expectTypeOf(newLog).toMatchTypeOf<NewExerciceLogModel>()
      expect(newLog.ExerciceId).toBe(5)
    })
  })

  describe('Utility Models', () => {
    it('should correctly type MultiUnityWeight', () => {
      const weight: MultiUnityWeight = {
        Lb: 135,
        Kg: 61.23,
      }

      expectTypeOf(weight).toMatchTypeOf<MultiUnityWeight>()
      expect(weight.Lb).toBe(135)
      expect(weight.Kg).toBeCloseTo(61.23, 2)
    })

    it('should correctly type ApiResponse', () => {
      const response: ApiResponse<LoginSuccessResult> = {
        Result: true,
        Code: 200,
        Data: {
          access_token: 'token',
          token_type: 'Bearer',
          expires_in: 86400,
          userName: '<EMAIL>',
          issued: '2024-01-01T00:00:00Z',
          expires: '2024-01-02T00:00:00Z',
        },
      }

      expectTypeOf(response).toMatchTypeOf<ApiResponse<LoginSuccessResult>>()
      expect(response.Result).toBe(true)
    })

    it('should correctly type BooleanModel', () => {
      const boolResult: BooleanModel = {
        Result: true,
        Code: 200,
        ErrorMessage: undefined,
      }

      expectTypeOf(boolResult).toMatchTypeOf<BooleanModel>()
      expect(boolResult.Result).toBe(true)
    })
  })

  describe('Optional fields', () => {
    it('should allow optional fields to be undefined', () => {
      const minimalExercise: ExerciseModel = {
        Id: 1,
        Label: 'Push-ups',
        IsSystemExercise: false,
        IsSwapTarget: false,
        IsFinished: false,
        IsUnilateral: false,
        IsTimeBased: false,
        IsEasy: true,
        IsMedium: false,
        IsBodyweight: true,
        VideoUrl: '',
        IsNextExercise: false,
        IsPlate: false,
        IsWeighted: false,
        IsPyramid: false,
        IsNormalSets: true,
        IsBodypartPriority: false,
        IsFlexibility: false,
        IsOneHanded: false,
        LocalVideo: '',
        IsAssisted: false,
        // Optional fields omitted
      }

      expectTypeOf(minimalExercise).toMatchTypeOf<ExerciseModel>()
      expect(minimalExercise.BodyPartId).toBeUndefined()
      expect(minimalExercise.EquipmentId).toBeUndefined()
    })
  })

  describe('API Request Models', () => {
    it('should correctly type GetRecommendationForExerciseModel', () => {
      const request: GetRecommendationForExerciseModel = {
        Username: 'user123',
        ExerciceId: 5,
        IsNormalSets: true,
        Set: [],
        Increments: 5,
        IsBodyweight: false,
        EquipmentId: 2,
        IsFromServer: true,
        RepsMinValue: 8,
        RepsMaxValue: 12,
        IsPlate: true,
        BodypartId: 1,
        IsTimeBased: false,
        IsUnilateral: false,
        Days: 7,
        IsQuickMode: false,
        WeightInKG: 61.23,
        IsEasy: false,
        IsMedium: true,
        IsReversePyramid: false,
        IsNewStarted: false,
        IsLightSession: false,
        ReprangeType: 'normal',
        Weight: { Lb: 135, Kg: 61.23 },
        Reps: 10,
        IsBackOffSet: false,
        IsFromPreworkout: false,
        BackOffSetWeight: { Lb: 115, Kg: 52.16 },
        SetStyle: 'normal',
        IsMaxChallenge: false,
        IsFromChallenge: false,
        IsRIREnabled: true,
        RIR: 2,
        Plate: '45lbs',
        IsDeload: false,
        IsFromSaveWorkout: false,
        Version: '1.0',
      }

      expectTypeOf(request).toMatchTypeOf<GetRecommendationForExerciseModel>()
      expect(request.Username).toBe('user123')
      expect(request.ExerciceId).toBe(5)
    })

    it('should correctly type GetUserWorkoutLogAverageResponse', () => {
      const response: GetUserWorkoutLogAverageResponse = {
        Sets: [],
        Histograms: [],
        AvgWorkoutTime: undefined,
        LastWorkoutDate: new Date('2024-01-01'),
        NewRecords: [],
        ConsecutiveWeeks: 5,
        HistoryExerciseModel: [],
        AllTimeHistoryExerciseModel: [],
        HistoryModel: [],
        TotalWorkoutCompleted: 50,
        LastWorkoutDateStr: '2024-01-01',
        LastConsecutiveWeek: new Date('2024-01-01'),
      }

      expectTypeOf(response).toMatchTypeOf<GetUserWorkoutLogAverageResponse>()
      expect(response.ConsecutiveWeeks).toBe(5)
    })

    it('should correctly type GetUserWorkoutLogDate', () => {
      const workoutLogDate: GetUserWorkoutLogDate = {
        Date: '2024-01-01',
        Sets: [],
        OneRMAverage: undefined,
        IsBodyweight: false,
      }

      expectTypeOf(workoutLogDate).toMatchTypeOf<GetUserWorkoutLogDate>()
      expect(workoutLogDate.Date).toBe('2024-01-01')
    })
  })

  describe('Type Guards', () => {
    it('should have proper type guards for API responses', () => {
      // This will test runtime type guards once implemented
      const apiResponse: ApiResponse<LoginSuccessResult> = {
        Result: true,
        Code: 200,
        Data: {
          access_token: 'token',
          token_type: 'Bearer',
          expires_in: 86400,
          userName: '<EMAIL>',
          issued: '2024-01-01T00:00:00Z',
          expires: '2024-01-02T00:00:00Z',
        },
      }

      // Type guard tests will be added when we implement the guards
      expect(apiResponse.Result).toBe(true)
    })
  })
})
