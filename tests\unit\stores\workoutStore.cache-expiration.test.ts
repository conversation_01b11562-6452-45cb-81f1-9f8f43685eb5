import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useWorkoutStore } from '@/stores/workoutStore'
import type {
  GetUserProgramInfoResponseModel,
  WorkoutTemplateModel,
  RecommendationModel,
} from '@/types'

// Mock data
const mockUserProgramInfo: GetUserProgramInfoResponseModel = {
  UserId: 'test-user',
  WeeklyStatus: 'Week 1',
  ProgramLabel: 'Test Program',
  NbDaysInTheWeek: 5,
  NbNonTrainingDays: 2,
  MondayIsFirst: false,
  TimeLogged: '2024-01-01T10:00:00',
  NextWorkoutDayText: 'Today',
  IsInIntroWorkout: false,
  IsInFirstWeek: true,
  TodaysWorkoutId: '1234',
  TodaysWorkoutText: 'Push Day',
  RecommendedProgram: {
    Id: 1,
    Label: 'Beginner Program',
    SubLabel: '3 days per week',
    UserStat: null,
  },
  NextNonTrainingDay: '2024-01-03',
  MondayHere: '2024-01-01',
  NextIntensityTechnique: 'Standard',
  ServerTimeUtc: '2024-01-01T10:00:00Z',
  MaxWorkoutSets: 20,
  NbMediumSets: 5,
  NbChallenges: 3,
}

const mockWorkout: WorkoutTemplateModel = {
  Id: 1,
  UserId: 'test-user',
  Label: 'Push Day',
  Exercises: [],
  IsSystemExercise: false,
  WorkoutSettingsModel: {
    Id: 1,
    Pause: 120,
    Equipment: '',
    ChildWorkoutTemplateId: null,
    SetsModel: null,
    WorkoutProgramId: 1,
    IsFirstSet: false,
    IsFail: false,
    NbRepsMinimalInc: null,
    AvgDuration: null,
    IsNotRealData: false,
  },
}

const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 100, Kg: 45.36 },
  Increments: { Lb: 5, Kg: 2.5 },
  LastLogDate: '2024-01-01',
  LastReps: 8,
  LastWeight: { Lb: 95, Kg: 43.09 },
  LastSeries: 3,
  IsWaitingForSwap: false,
  RM: 105,
  RIR: 2,
  History: [],
  IsBodyweight: false,
  IsTimeBased: false,
  IsUnilateral: false,
  IsPlate: false,
  RecommendedCountdown: null,
  NegativeWeight: null,
  PartialWeight: null,
  IsNegative: false,
  IsPartial: false,
}

describe('Workout Store - Cache Expiration & Staleness Detection', () => {
  beforeEach(() => {
    // Reset store state
    useWorkoutStore.setState({
      cachedData: {
        userProgramInfo: null,
        userWorkouts: null,
        todaysWorkout: null,
        exerciseRecommendations: {},
        lastUpdated: {
          userProgramInfo: 0,
          userWorkouts: 0,
          todaysWorkout: 0,
          exerciseRecommendations: {},
        },
      },
      hasHydrated: true,
    })

    // Mock Date.now()
    vi.useFakeTimers()
    vi.setSystemTime(new Date('2024-01-01T12:00:00'))
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('Cache Expiry Constants', () => {
    it('should define cache expiry times for different data types', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Test that methods exist (they will be implemented)
      expect(result.current).toHaveProperty('isCacheStale')
      expect(result.current).toHaveProperty('clearExpiredCache')
    })
  })

  describe('Staleness Detection', () => {
    it('should detect stale userProgramInfo cache after 24 hours', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Set cached data
      act(() => {
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
      })

      // Check cache is fresh
      expect(result.current.isCacheStale('userProgramInfo')).toBe(false)

      // Advance time by 25 hours
      vi.advanceTimersByTime(25 * 60 * 60 * 1000)

      // Check cache is stale
      expect(result.current.isCacheStale('userProgramInfo')).toBe(true)
    })

    it('should detect stale userWorkouts cache after 24 hours', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Set cached data
      act(() => {
        result.current.setCachedUserWorkouts([mockWorkout])
      })

      // Check cache is fresh
      expect(result.current.isCacheStale('userWorkouts')).toBe(false)

      // Advance time by 25 hours
      vi.advanceTimersByTime(25 * 60 * 60 * 1000)

      // Check cache is stale
      expect(result.current.isCacheStale('userWorkouts')).toBe(true)
    })

    it('should detect stale exercise recommendation cache after 1 hour', () => {
      const { result } = renderHook(() => useWorkoutStore())
      const exerciseId = 123

      // Set cached recommendation
      act(() => {
        result.current.setCachedExerciseRecommendation(
          exerciseId,
          mockRecommendation
        )
      })

      // Check cache is fresh
      expect(
        result.current.isCacheStale('exerciseRecommendation', exerciseId)
      ).toBe(false)

      // Advance time by 61 minutes
      vi.advanceTimersByTime(61 * 60 * 1000)

      // Check cache is stale
      expect(
        result.current.isCacheStale('exerciseRecommendation', exerciseId)
      ).toBe(true)
    })

    it('should handle missing timestamps (return stale)', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Check cache with no timestamp is considered stale
      expect(result.current.isCacheStale('userProgramInfo')).toBe(true)
      expect(result.current.isCacheStale('exerciseRecommendation', 999)).toBe(
        true
      )
    })

    it('should handle edge case with zero timestamps', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Manually set timestamp to 0
      act(() => {
        useWorkoutStore.setState({
          cachedData: {
            ...useWorkoutStore.getState().cachedData,
            lastUpdated: {
              userProgramInfo: 0,
              userWorkouts: 0,
              todaysWorkout: 0,
              exerciseRecommendations: { 123: 0 },
            },
          },
        })
      })

      // Zero timestamps should be considered stale
      expect(result.current.isCacheStale('userProgramInfo')).toBe(true)
      expect(result.current.isCacheStale('exerciseRecommendation', 123)).toBe(
        true
      )
    })

    it('should handle todaysWorkout staleness check', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Set cached data
      act(() => {
        result.current.setCachedTodaysWorkout([])
      })

      // Check cache is fresh
      expect(result.current.isCacheStale('todaysWorkout')).toBe(false)

      // Advance time by 25 hours
      vi.advanceTimersByTime(25 * 60 * 60 * 1000)

      // Check cache is stale
      expect(result.current.isCacheStale('todaysWorkout')).toBe(true)
    })
  })

  describe('Cache Cleanup', () => {
    it('should clear expired userProgramInfo cache', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Set cached data
      act(() => {
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
      })

      // Verify data is cached
      expect(result.current.getCachedUserProgramInfo()).toEqual(
        mockUserProgramInfo
      )

      // Advance time to make cache stale
      vi.advanceTimersByTime(25 * 60 * 60 * 1000)

      // Clear expired cache
      act(() => {
        result.current.clearExpiredCache()
      })

      // Verify expired data is removed
      expect(result.current.getCachedUserProgramInfo()).toBeNull()
    })

    it('should clear expired exercise recommendations selectively', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Set multiple recommendations at different times
      act(() => {
        result.current.setCachedExerciseRecommendation(1, mockRecommendation)
      })

      // Advance 30 minutes
      vi.advanceTimersByTime(30 * 60 * 1000)

      act(() => {
        result.current.setCachedExerciseRecommendation(2, mockRecommendation)
      })

      // Advance another 35 minutes (total 65 minutes for first, 35 for second)
      vi.advanceTimersByTime(35 * 60 * 1000)

      // Clear expired cache
      act(() => {
        result.current.clearExpiredCache()
      })

      // First recommendation should be cleared (>1 hour old)
      expect(result.current.getCachedExerciseRecommendation(1)).toBeNull()

      // Second recommendation should remain (<1 hour old)
      expect(result.current.getCachedExerciseRecommendation(2)).toEqual(
        mockRecommendation
      )
    })

    it('should preserve fresh data during cleanup', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Set all types of cached data
      act(() => {
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
        result.current.setCachedUserWorkouts([mockWorkout])
        result.current.setCachedExerciseRecommendation(1, mockRecommendation)
      })

      // Advance time but not enough to expire
      vi.advanceTimersByTime(30 * 60 * 1000) // 30 minutes

      // Clear expired cache
      act(() => {
        result.current.clearExpiredCache()
      })

      // All data should remain
      expect(result.current.getCachedUserProgramInfo()).toEqual(
        mockUserProgramInfo
      )
      expect(result.current.getCachedUserWorkouts()).toEqual([mockWorkout])
      expect(result.current.getCachedExerciseRecommendation(1)).toEqual(
        mockRecommendation
      )
    })

    it('should handle cleanup with no cached data', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Clear expired cache on empty cache
      expect(() => {
        act(() => {
          result.current.clearExpiredCache()
        })
      }).not.toThrow()

      // Verify cache remains empty
      expect(result.current.getCachedUserProgramInfo()).toBeNull()
      expect(result.current.getCachedUserWorkouts()).toBeNull()
    })

    it('should call clearExpiredCache on rehydration', () => {
      // Set up store with stale data
      const { result } = renderHook(() => useWorkoutStore())

      // Set cached data with old timestamp
      act(() => {
        useWorkoutStore.setState({
          cachedData: {
            userProgramInfo: mockUserProgramInfo,
            userWorkouts: null,
            todaysWorkout: null,
            exerciseRecommendations: {},
            lastUpdated: {
              userProgramInfo: Date.now() - 25 * 60 * 60 * 1000, // 25 hours ago
              userWorkouts: 0,
              todaysWorkout: 0,
              exerciseRecommendations: {},
            },
          },
        })
      })

      // Verify data exists before rehydration
      expect(result.current.getCachedUserProgramInfo()).toEqual(
        mockUserProgramInfo
      )

      // Manually call clearExpiredCache (which would be called on rehydration)
      act(() => {
        result.current.clearExpiredCache()
      })

      // Verify expired data was cleared
      expect(result.current.getCachedUserProgramInfo()).toBeNull()
    })
  })

  describe('Performance', () => {
    it('should complete staleness check in under 1ms', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Set up some cached data
      act(() => {
        result.current.setCachedUserProgramInfo(mockUserProgramInfo)
        for (let i = 1; i <= 10; i++) {
          result.current.setCachedExerciseRecommendation(i, mockRecommendation)
        }
      })

      // Measure staleness check performance
      const start = performance.now()
      const isStale = result.current.isCacheStale('userProgramInfo')
      const duration = performance.now() - start

      expect(isStale).toBe(false)
      expect(duration).toBeLessThan(1)
    })

    it('should complete cache cleanup efficiently', () => {
      const { result } = renderHook(() => useWorkoutStore())

      // Set up many cached recommendations
      act(() => {
        for (let i = 1; i <= 50; i++) {
          result.current.setCachedExerciseRecommendation(i, mockRecommendation)
        }
      })

      // Make half of them stale
      vi.advanceTimersByTime(2 * 60 * 60 * 1000)

      act(() => {
        for (let i = 51; i <= 100; i++) {
          result.current.setCachedExerciseRecommendation(i, mockRecommendation)
        }
      })

      // Measure cleanup performance
      const start = performance.now()
      act(() => {
        result.current.clearExpiredCache()
      })
      const duration = performance.now() - start

      expect(duration).toBeLessThan(10) // Should be very fast
    })
  })
})
