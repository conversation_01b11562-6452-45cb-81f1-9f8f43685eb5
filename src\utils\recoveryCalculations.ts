export type ProgramType = 'split' | 'full-body' | 'powerlifting' | 'other'

export interface RecoveryInfo {
  percentage: number
  message: string
  isReady: boolean
  remainingTime: string
  hoursElapsed: number
  requiredHours: number
}

/**
 * Calculate recovery hours needed based on program type and user age
 */
export function calculateRecoveryHours(
  programType: ProgramType,
  userAge?: number,
  isConsecutiveWorkout: boolean = false
): number {
  // If working out on consecutive days, always need 42 hours
  if (isConsecutiveWorkout) {
    return 42
  }

  switch (programType) {
    case 'split':
      // Split programs: 18 hours recovery
      return 18
    case 'full-body':
    case 'powerlifting':
      // Full body and powerlifting: 18h if under 30, 42h otherwise
      return userAge && userAge < 30 ? 18 : 42
    default:
      // Default: 24 hours
      return 24
  }
}

/**
 * Get coach message based on recovery percentage
 */
export function getCoachMessage(percentage: number, isReady: boolean): string {
  if (isReady) {
    return "Let's train!"
  }

  if (percentage < 25) {
    return 'Rest and recover'
  } else if (percentage < 50) {
    return 'Keep resting'
  } else if (percentage < 75) {
    return 'Almost ready'
  } else {
    return 'Nearly there!'
  }
}

/**
 * Format remaining time in human-readable format
 */
export function formatTimeRemaining(hours: number): string {
  if (hours <= 0) {
    return 'Ready now!'
  }

  if (hours < 1) {
    const minutes = Math.round(hours * 60)
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`
  }

  if (hours < 24) {
    const wholeHours = Math.floor(hours)
    const minutes = Math.round((hours - wholeHours) * 60)

    if (minutes === 0) {
      return `${wholeHours} hour${wholeHours !== 1 ? 's' : ''}`
    }

    return `${wholeHours}h ${minutes}m`
  }

  const days = Math.floor(hours / 24)
  const remainingHours = Math.round(hours % 24)

  if (remainingHours === 0) {
    return `${days} day${days !== 1 ? 's' : ''}`
  }

  return `${days}d ${remainingHours}h`
}

/**
 * Calculate recovery information based on last workout date
 */
export function calculateRecoveryInfo(
  lastWorkoutDate: string | Date,
  programType: ProgramType,
  userAge?: number
): RecoveryInfo {
  const now = new Date()
  const lastWorkout = new Date(lastWorkoutDate)
  const hoursElapsed =
    (now.getTime() - lastWorkout.getTime()) / (1000 * 60 * 60)

  // Check if this would be a consecutive day workout
  // TODO: This should be based on workout history pattern, not just time elapsed
  // For now, we'll disable this logic
  const isConsecutive = false

  const requiredHours = calculateRecoveryHours(
    programType,
    userAge,
    isConsecutive
  )
  const percentage = Math.min(
    Math.round((hoursElapsed / requiredHours) * 100),
    100
  )
  const isReady = percentage >= 100

  const remainingHours = Math.max(0, requiredHours - hoursElapsed)
  const remainingTime = formatTimeRemaining(remainingHours)

  const message = getCoachMessage(percentage, isReady)

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('[calculateRecoveryInfo] Details:', {
      now: now.toISOString(),
      lastWorkout: lastWorkout.toISOString(),
      hoursElapsed,
      isConsecutive,
      requiredHours,
      percentage,
      isReady,
      remainingHours,
      message,
    })
  }

  return {
    percentage,
    message,
    isReady,
    remainingTime,
    hoursElapsed,
    requiredHours,
  }
}

/**
 * Determine program type from program name
 */
export function getProgramType(programName?: string): ProgramType {
  if (!programName) return 'other'

  const name = programName.toLowerCase()

  if (
    name.includes('split') ||
    name.includes('upper') ||
    name.includes('lower') ||
    name.includes('push') ||
    name.includes('pull') ||
    name.includes('legs')
  ) {
    return 'split'
  }

  if (name.includes('full body') || name.includes('fullbody')) {
    return 'full-body'
  }

  if (name.includes('powerlifting') || name.includes('strength')) {
    return 'powerlifting'
  }

  return 'other'
}
