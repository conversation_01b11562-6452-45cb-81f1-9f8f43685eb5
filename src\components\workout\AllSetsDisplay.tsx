'use client'

import React from 'react'
import type {
  RecommendationModel,
  WorkoutLogSerieModel,
  MultiUnityWeight,
} from '@/types'
import { formatWeight } from '@/utils/weightUtils'

interface SetDisplayProps {
  setNumber: number
  reps: number
  weight: MultiUnityWeight
  isCompleted: boolean
  isWarmup: boolean
  isCurrent: boolean
  isNext?: boolean
  onClick: () => void
  completedSet?: WorkoutLogSerieModel
  isRestPause?: boolean
}

function SetDisplay({
  setNumber,
  reps,
  weight,
  isCompleted,
  isWarmup,
  isCurrent,
  isNext = false,
  onClick,
  completedSet,
  isRestPause = false,
}: SetDisplayProps) {
  return (
    <button
      onClick={onClick}
      className={`w-full rounded-lg border p-4 text-left transition-all duration-200 ${(() => {
        if (isWarmup)
          return 'border-orange-200 bg-orange-50 hover:bg-orange-100'
        if (isCurrent)
          return 'border-blue-500 bg-blue-50 ring-2 ring-blue-500 ring-opacity-50'
        if (isCompleted) return 'border-green-200 bg-green-50'
        if (isNext) return 'border-blue-300 bg-blue-25 hover:bg-blue-50'
        return 'border-gray-200 bg-white hover:bg-gray-50'
      })()}`}
      aria-label={`Set ${setNumber}: ${reps} reps at ${formatWeight(weight)}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Set Number */}
          <div
            className={`flex h-10 w-10 items-center justify-center rounded-full text-sm font-medium ${(() => {
              if (isWarmup) return 'bg-orange-200 text-orange-700'
              if (isCurrent) return 'bg-blue-500 text-white'
              if (isCompleted) return 'bg-green-500 text-white'
              return 'bg-gray-200 text-gray-700'
            })()}`}
          >
            {isWarmup ? `W${setNumber}` : setNumber}
          </div>

          {/* Set Details */}
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-900">
                {completedSet ? completedSet.Reps : reps} reps
              </span>
              {!isWarmup && weight && (weight.Lb > 0 || weight.Kg > 0) && (
                <>
                  <span className="text-gray-500">×</span>
                  <span className="font-medium text-gray-900">
                    {completedSet
                      ? formatWeight(completedSet.Weight)
                      : formatWeight(weight)}
                  </span>
                </>
              )}
              {isWarmup && (
                <span className="text-xs text-orange-600 font-medium ml-2">
                  Warmup
                </span>
              )}
            </div>
            {completedSet && completedSet.RIR !== undefined && (
              <div className="text-sm text-gray-500">
                RIR: {completedSet.RIR}
              </div>
            )}
            {/* Show rest-pause indicator */}
            {isRestPause && (
              <div className="text-xs text-blue-600 font-medium">
                Rest-Pause
              </div>
            )}
          </div>
        </div>

        {/* Status Indicator */}
        <div className="flex items-center">
          {(() => {
            if (isCompleted) {
              return (
                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-500">
                  <svg
                    className="h-4 w-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              )
            }
            if (isCurrent) {
              return (
                <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-500">
                  <div className="h-2 w-2 rounded-full bg-white" />
                </div>
              )
            }
            return (
              <div className="h-6 w-6 rounded-full border-2 border-gray-300" />
            )
          })()}
        </div>
      </div>
    </button>
  )
}

interface AllSetsDisplayProps {
  recommendation: RecommendationModel
  currentSetIndex: number
  completedSets: WorkoutLogSerieModel[]
  onSetClick: (setIndex: number) => void
}

interface SetData {
  setNumber: number
  reps: number
  weight: MultiUnityWeight
  isWarmup: boolean
  setIndex: number
  isRestPause?: boolean
}

export function AllSetsDisplay({
  recommendation,
  currentSetIndex,
  completedSets,
  onSetClick,
}: AllSetsDisplayProps) {
  const totalSets = recommendation.Series || 0
  const warmupCount = recommendation.WarmupsCount || 0

  // Early return if no sets
  if (totalSets === 0) {
    return (
      <div className="px-4 py-6 text-center">
        <p className="text-gray-500">No sets available for this exercise</p>
      </div>
    )
  }

  // Generate all sets (warmups + work sets)
  const allSets: SetData[] = []

  // Add warmup sets
  for (let i = 0; i < warmupCount; i++) {
    const warmupSet = recommendation.WarmUpsList?.[i]
    allSets.push({
      setNumber: i + 1,
      reps: warmupSet?.WarmUpReps || recommendation.WarmUpReps1 || 5,
      weight: warmupSet?.WarmUpWeightSet || recommendation.WarmUpWeightSet1,
      isWarmup: true,
      setIndex: i,
    })
  }

  // Add work sets
  for (let i = 0; i < totalSets; i++) {
    // Check if this is a rest-pause set
    const isRestPause =
      !recommendation.IsNormalSets && recommendation.NbPauses > 0
    const reps = isRestPause ? recommendation.NbRepsPauses : recommendation.Reps

    allSets.push({
      setNumber: i + 1, // Work sets numbered 1, 2, 3, etc.
      reps,
      weight: recommendation.Weight,
      isWarmup: false,
      setIndex: warmupCount + i,
      isRestPause,
    })
  }

  return (
    <div className="space-y-3">
      <div className="px-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-1">
          All Sets ({allSets.length})
        </h3>
        <p className="text-sm text-gray-600">Tap any set to start or edit it</p>
      </div>

      <div className="px-4 space-y-2">
        {allSets.map((set) => {
          const isCompleted = completedSets.some(
            (completedSet) =>
              parseInt(completedSet.SetNo || '0') === set.setIndex + 1
          )
          const isCurrent = currentSetIndex === set.setIndex
          const completedSet = completedSets.find(
            (completedSet) =>
              parseInt(completedSet.SetNo || '0') === set.setIndex + 1
          )

          // Determine if this is the next set to complete
          const isNext =
            !isCompleted &&
            !isCurrent &&
            allSets
              .slice(0, allSets.indexOf(set))
              .every((prevSet) =>
                completedSets.some(
                  (cs) => parseInt(cs.SetNo || '0') === prevSet.setIndex + 1
                )
              )

          return (
            <SetDisplay
              key={set.setIndex}
              setNumber={set.setNumber}
              reps={set.reps}
              weight={set.weight}
              isCompleted={isCompleted}
              isWarmup={set.isWarmup}
              isCurrent={isCurrent}
              isNext={isNext}
              onClick={() => onSetClick(set.setIndex)}
              completedSet={completedSet}
              isRestPause={set.isRestPause}
            />
          )
        })}
      </div>

      {/* Progress Summary */}
      <div className="px-4 pt-2 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            Progress: {completedSets.length} of {allSets.length} sets completed
          </span>
          <div className="flex items-center space-x-1">
            <div className="h-2 w-16 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-green-500 transition-all duration-300"
                style={{
                  width: `${(completedSets.length / allSets.length) * 100}%`,
                }}
              />
            </div>
            <span className="text-gray-500 text-xs">
              {Math.round((completedSets.length / allSets.length) * 100)}%
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
