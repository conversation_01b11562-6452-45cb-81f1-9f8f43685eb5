import { useAuthStore } from '@/stores/authStore'

/**
 * Get the current user's email from the auth store
 * This replaces direct localStorage access
 */
export function getCurrentUserEmail(): string | null {
  const state = useAuthStore.getState()
  return state.user?.email || null
}

/**
 * Get the current auth state
 * This replaces direct localStorage access
 */
export function getCurrentAuthState() {
  const state = useAuthStore.getState()
  return {
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    token: state.token,
    refreshToken: state.refreshToken,
  }
}
