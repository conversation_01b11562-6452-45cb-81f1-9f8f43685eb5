/**
 * UserInfo Performance tracking module
 * Monitors the progressive loading flow on the program page
 */

import { UserInfoPerformanceTracker } from './tracker'
import { UserInfoPerformanceMarks } from './types'

export * from './types'
export { UserInfoPerformanceTracker } from './tracker'

// Export singleton instance
export const userInfoPerformance = UserInfoPerformanceTracker.getInstance()

// Session management functions
export const startSession = (): string => {
  const sessionId = userInfoPerformance.generateSessionId()
  userInfoPerformance.mark(UserInfoPerformanceMarks.LOGIN_SUCCESS, {
    sessionId,
  })
  return sessionId
}

export const endSession = (
  sessionId: string,
  success: boolean,
  error?: string
): void => {
  userInfoPerformance.mark(UserInfoPerformanceMarks.PROGRAM_PAGE_READY, {
    sessionId,
    success,
    error,
  })

  if (success) {
    userInfoPerformance.trackPageReady()
  }
}

// Cache metrics recording
export const recordCacheMetrics = (
  operation: 'hit' | 'miss',
  duration?: number
): void => {
  userInfoPerformance.trackCacheOperation(operation, duration)
}

// Error tracking
export const trackError = (error: Error, context: string): void => {
  userInfoPerformance.mark(`error-${context}`, {
    error: error.message,
    stack: error.stack,
  })
}

// Convenience functions for easy usage
export const trackLoginSuccess = () => userInfoPerformance.trackLoginSuccess()
export const trackUserInfoFetch = (isRetry = false) =>
  userInfoPerformance.trackUserInfoFetch(isRetry)
export const trackUserInfoFirstByte = () =>
  userInfoPerformance.trackUserInfoFirstByte()
export const trackUserInfoComplete = (fromCache = false) =>
  userInfoPerformance.trackUserInfoComplete(fromCache)
export const trackStatsFetch = (isRetry = false) =>
  userInfoPerformance.trackStatsFetch(isRetry)
export const trackStatsComplete = () => userInfoPerformance.trackStatsComplete()
export const trackMetricLoaded = (metric: 'streak' | 'workouts' | 'volume') =>
  userInfoPerformance.trackMetricLoaded(metric)
export const trackCacheOperation = (
  operation: 'hit' | 'miss' | 'write',
  duration?: number
) => userInfoPerformance.trackCacheOperation(operation, duration)
export const trackPageReady = () => userInfoPerformance.trackPageReady()
export const getUserInfoMetrics = () => userInfoPerformance.getMetrics()
export const reportUserInfoPerformance = () => {
  userInfoPerformance.reportToConsole()
  userInfoPerformance.sendToAnalytics()
}
