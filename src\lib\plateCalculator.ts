interface Plate {
  weight: number
  count: number
}

export function calculatePlates(
  targetWeight: number,
  unit: 'lbs' | 'kg'
): Plate[] {
  // Standard barbell weight
  const barbellWeight = unit === 'kg' ? 20 : 45

  // If target weight is less than or equal to barbell weight, no plates needed
  if (targetWeight <= barbellWeight) {
    return []
  }

  // Calculate weight to be loaded (excluding barbell)
  const plateWeight = targetWeight - barbellWeight

  // Available plate sizes (in descending order)
  const availablePlates =
    unit === 'kg' ? [20, 10, 5, 2.5, 1.25] : [45, 25, 10, 5, 2.5]

  const plates: Plate[] = []

  // Calculate plates needed
  let remainingWeight = plateWeight

  availablePlates.forEach((plateSize) => {
    if (remainingWeight >= plateSize * 2) {
      // Need pairs of plates
      const count = Math.floor(remainingWeight / (plateSize * 2)) * 2
      plates.push({ weight: plateSize, count })
      remainingWeight -= plateSize * count
    }
  })

  // Handle any remaining weight (should be minimal due to rounding)
  if (remainingWeight > 0.1) {
    // Try to find the smallest plate that can handle the remainder
    const smallestPlate = availablePlates[availablePlates.length - 1]
    if (smallestPlate && remainingWeight >= smallestPlate) {
      plates.push({ weight: smallestPlate, count: 2 })
    }
  }

  return plates
}
