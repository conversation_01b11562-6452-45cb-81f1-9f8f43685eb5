import { test, expect } from '@playwright/test'

// Use mobile viewport by default
test.use({
  viewport: { width: 390, height: 844 }, // iPhone 13
  userAgent:
    'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
})

test.describe('Workout Display Fix Verification', () => {
  test('should display exercises after login and navigation to workout page', async ({
    page,
  }) => {
    // Start at login page
    await page.goto('/login')

    // Login with test credentials
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'Dr123456')
    await page.click('button[type="submit"]')

    // Wait for navigation to program page
    await page.waitForURL('/program', { timeout: 10000 })

    // Wait for program to load
    await page.waitForSelector('text=/Week \\d+, Day \\d+/', { timeout: 10000 })

    // Click Continue to workout button
    await page.click('button:has-text("Continue to workout")')

    // Wait for navigation to workout page
    await page.waitForURL('/workout')

    // Wait for "checking for updates" to appear and disappear
    const checkingText = page.locator('text=checking for updates')
    await expect(checkingText).toBeVisible({ timeout: 5000 })
    await expect(checkingText).toBeHidden({ timeout: 15000 })

    // Verify exercises are displayed (not "0 exercises")
    const exerciseText = page.locator('text=/\\d+ exercises?/').first()
    await expect(exerciseText).toBeVisible({ timeout: 5000 })

    // Get the exercise count text
    const text = await exerciseText.textContent()
    const exerciseCount = parseInt(text?.match(/(\d+)/)?.[1] || '0')
    console.log(`Found ${exerciseCount} exercises`)

    // Verify the count is greater than 0
    expect(exerciseCount).toBeGreaterThan(0)

    // Check for any console errors
    const consoleErrors: string[] = []
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        const errorText = msg.text()
        consoleErrors.push(errorText)
        console.log('Console error:', errorText)
      }
    })

    // Wait a bit to catch any delayed errors
    await page.waitForTimeout(3000)

    // Verify no "Result is not an array" errors
    const hasResultError = consoleErrors.some((error) =>
      error.includes('GetUserWorkout: Result is not an array')
    )
    if (hasResultError) {
      console.log('Found GetUserWorkout error in console')
    }
    expect(hasResultError).toBe(false)

    // Take a screenshot for verification
    await page.screenshot({
      path: 'test-results/workout-display-success.png',
      fullPage: true,
    })
  })
})
