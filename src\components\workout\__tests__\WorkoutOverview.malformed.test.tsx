import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { WorkoutOverview } from '../WorkoutOverview'
import * as useWorkoutModule from '@/hooks/useWorkout'

// Mock next/navigation
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock pull to refresh hook
vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isPulling: false,
    isRefreshing: false,
  }),
}))

describe('WorkoutOverview - Malformed Data Handling', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should show "No Workout Available" when workout data exists but has no WorkoutTemplates', async () => {
    // Given - API returns workout data with missing WorkoutTemplates
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue({
      todaysWorkout: [
        {
          // Malformed workout group - missing WorkoutTemplates
          Id: 1,
          Name: 'Test Workout',
          // WorkoutTemplates is missing or undefined
        },
      ],
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: {
        hasData: true,
      },
      exercises: [],
      exerciseWorkSetsModels: [],
      expectedExerciseCount: 0,
      hasInitialData: true,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null,
      finishWorkout: vi.fn(),
      isLoading: false,
    } as any)

    // When - Component renders
    const { container } = render(<WorkoutOverview />)

    // Then - Should show "No Workout Available" message, not blank
    expect(screen.getByText('No Workout Available')).toBeInTheDocument()
    expect(
      screen.getByText(
        /It looks like you don't have a workout program assigned yet/
      )
    ).toBeInTheDocument()

    // Component should remain mounted
    expect(container.querySelector('div')).toBeInTheDocument()
  })

  it('should show "No Workout Available" when WorkoutTemplates array is empty', async () => {
    // Given - API returns workout with empty WorkoutTemplates array
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue({
      todaysWorkout: [
        {
          Id: 1,
          Name: 'Test Workout',
          WorkoutTemplates: [], // Empty array
        },
      ],
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: {
        hasData: true,
      },
      exercises: [],
      exerciseWorkSetsModels: [],
      expectedExerciseCount: 0,
      hasInitialData: true,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null,
      finishWorkout: vi.fn(),
      isLoading: false,
    } as any)

    // When - Component renders
    render(<WorkoutOverview />)

    // Then - Should show "No Workout Available" message
    expect(screen.getByText('No Workout Available')).toBeInTheDocument()
  })

  it('should show "No Workout Available" immediately for malformed data, regardless of loading state', async () => {
    // Given - Malformed workout data with isLoadingFresh true
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue({
      todaysWorkout: [
        {
          Id: 1,
          Name: 'Test Workout',
          // Malformed - no WorkoutTemplates
        },
      ],
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: {
        hasData: true,
      },
      exercises: [],
      exerciseWorkSetsModels: [],
      expectedExerciseCount: 0,
      hasInitialData: true,
      isLoadingFresh: true, // Even when loading recommendations
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null,
      finishWorkout: vi.fn(),
      isLoading: false,
    } as any)

    // When - Component renders
    render(<WorkoutOverview />)

    // Then - Should show "No Workout Available" immediately (not loading message)
    expect(screen.getByText('No Workout Available')).toBeInTheDocument()

    // Should NOT show loading recommendations message
    expect(
      screen.queryByText('Loading exercise recommendations...')
    ).not.toBeInTheDocument()
  })
})
