# Dr. Muscle Web Application - API Reference Guide

## Overview

This comprehensive API reference guide documents all endpoints, data models, and authentication mechanisms used by the Dr. Muscle MAUI application. This guide enables Web application developers to implement the same functionality without reverse-engineering API calls.

**Base URLs:**

- Production: `https://drmuscle.azurewebsites.net/`
- Development: `https://drmuscle2.azurewebsites.net/`

**API Version:** 1

## Authentication

### OAuth Token Authentication

The primary authentication method uses OAuth 2.0 with form-encoded parameters.

**Endpoint:** `POST /token`

**Content-Type:** `application/x-www-form-urlencoded`

**Parameters:**

```
grant_type=password
username={email}
password={password}
```

**Response:**

```json
{
  "access_token": "string",
  "token_type": "bearer",
  "expires_in": 3600
}
```

**Authorization Header:**
All authenticated requests must include:

```
Authorization: bearer {access_token}
```

### Apple Sign In (Alternative)

**Endpoint:** `POST /api/Account/RegisterWithApple`

**Request:**

```json
{
  "token": "apple_id_token"
}
```

**Response:** `UserInfosModel`

## Core Data Models

### UserInfosModel

**Note:** This is the complete model returned by GetUserInfoPyramid endpoint. All fields are at the top level of the JSON response.

```json
{
  "Email": "string",
  "UId": "string",
  "Firstname": "string", // Note: No capital 'N' - it's "Firstname" not "FirstName"
  "Lastname": "string", // Note: No capital 'N' - it's "Lastname" not "LastName"
  "Gender": "string",
  "MassUnit": "string", // Not "WeightUnit"
  "IsNormalSet": "boolean | null",
  "Password": "string | null",
  "ReminderDays": "string",
  "TimeZone": "string",
  "ReminderTime": "string | null", // Format: "HH:MM:SS"
  "CreationDate": "string", // ISO 8601 datetime
  "RepsMinimum": "number",
  "RepsMaximum": "number",
  "ReprangeType": "number",
  "IsQuickMode": "boolean | null",
  "IsReminderEmail": "boolean",
  "IsReferenseSet": "boolean",
  "ReminderBeforeHours": "number",
  "ReferenceSetReps": "number",
  "WarmupsValue": "number | null",
  "Age": "number | null",
  "Height": "number | null",
  "LastChallengeDate": "string | null", // ISO 8601 datetime
  "BodyWeight": "MultiUnityWeight | null",
  "Fat": "number | null",
  "WeightGoal": "MultiUnityWeight | null",
  "Increments": "MultiUnityWeight | null",
  "Min": "MultiUnityWeight | null",
  "Max": "MultiUnityWeight | null",
  "IsVibrate": "boolean",
  "IsSound": "boolean",
  "IsRepsSound": "boolean",
  "IsTimer321": "boolean",
  "IsFullscreen": "boolean",
  "IsBetweenExercises": "boolean | null",
  "IsAutoStart": "boolean",
  "IsAutomatchReps": "boolean",
  "TimeCount": "number",
  "IsBackOffSet": "boolean",
  "IsCardio": "boolean",
  "IsReminder": "boolean",
  "SetCount": "number | null",
  "IsStrength": "boolean",
  "BodyPartPrioriy": "string | null",
  "SwappedJson": "string | null",
  "EquipmentModel": "EquipmentModel | null",
  "IsMobility": "boolean | null",
  "IsExerciseQuickMode": "boolean | null",
  "Is1By1Side": "boolean", // Default: true
  "MobilityRep": "number | null",
  "WeeklyExerciseCount": "number | null",
  "DailyExerciseCount": "number | null",
  "WorkoutDuration": "number",
  "MobilityLevel": "string | null",
  "MainGoal": "string | null",
  "IsPyramid": "boolean",
  "LastWorkoutWas": "string",
  "LastActiveDate": "string", // ISO 8601 datetime
  "IsRecommendedReminder": "boolean | null",
  "TargetIntake": "number | null",
  "LastTargetIntake": "number | null",
  "KgBarWeight": "number | null",
  "LbBarWeight": "number | null",
  "IsDropSet": "boolean",
  "DmmMeal": "array", // Collection of meal entries
  "DmmMealPlan": "array" // Collection of meal plans
}
```

**Critical Field Name Differences:**

- ✅ Use `Firstname` (not `FirstName`)
- ✅ Use `Lastname` (not `LastName`)
- ✅ Use `MassUnit` (not `WeightUnit`)
- ✅ Use `UId` (not `Id` or `UserId`)

### WorkoutTemplateGroupModel

```json
{
  "Id": "number",
  "UserId": "string",
  "Label": "string",
  "WorkoutTemplates": ["WorkoutTemplateModel"],
  "IsSystemExercise": "boolean",
  "IsFeaturedProgram": "boolean",
  "RequiredWorkoutToLevelUp": "number",
  "Level": "number",
  "RemainingToLevelUp": "number",
  "NextProgramId": "number",
  "ProgramId": "number"
}
```

### WorkoutTemplateModel

```json
{
  "Id": "number",
  "UserId": "string",
  "Label": "string",
  "Exercises": ["ExerciceModel"],
  "IsSystemExercise": "boolean",
  "WorkoutSettingsModel": "WorkoutTemplateSettingsModel"
}
```

### ExerciceModel

```json
{
  "Id": "number",
  "Label": "string",
  "IsSystemExercise": "boolean",
  "IsSwapTarget": "boolean",
  "IsFinished": "boolean",
  "BodyPartId": "number",
  "IsUnilateral": "boolean",
  "IsTimeBased": "boolean",
  "EquipmentId": "number",
  "IsEasy": "boolean",
  "IsMedium": "boolean",
  "IsBodyweight": "boolean",
  "VideoUrl": "string",
  "IsNextExercise": "boolean",
  "IsPlate": "boolean",
  "IsWeighted": "boolean",
  "IsPyramid": "boolean",
  "RepsMaxValue": "number",
  "RepsMinValue": "number",
  "Timer": "number",
  "IsNormalSets": "boolean",
  "WorkoutGroupId": "number",
  "IsBodypartPriority": "boolean",
  "IsFlexibility": "boolean",
  "IsOneHanded": "boolean",
  "LocalVideo": "string",
  "IsAssisted": "boolean"
}
```

### RecommendationModel

```json
{
  "Series": "number",
  "Reps": "number",
  "Weight": "MultiUnityWeight",
  "OneRMProgress": "number",
  "RecommendationInKg": "number",
  "OneRMPercentage": "number",
  "WarmUpReps1": "number",
  "WarmUpReps2": "number",
  "WarmUpWeightSet1": "MultiUnityWeight",
  "WarmUpWeightSet2": "MultiUnityWeight",
  "WarmUpsList": ["WarmUps"],
  "WarmupsCount": "number",
  "RpRest": "number",
  "NbPauses": "number",
  "NbRepsPauses": "number",
  "IsEasy": "boolean",
  "IsMedium": "boolean",
  "IsBodyweight": "boolean",
  "Increments": "MultiUnityWeight",
  "Max": "MultiUnityWeight",
  "Min": "MultiUnityWeight",
  "IsNormalSets": "boolean",
  "IsDeload": "boolean",
  "IsBackOffSet": "boolean",
  "IsDefaultUnilateral": "boolean",
  "BackOffSetWeight": "MultiUnityWeight",
  "IsMaxChallenge": "boolean",
  "IsLightSession": "boolean",
  "LastLogDate": "datetime",
  "FirstWorkSetReps": "number",
  "FirstWorkSetWeight": "MultiUnityWeight",
  "FirstWorkSet1RM": "MultiUnityWeight",
  "IsPyramid": "boolean",
  "IsReversePyramid": "boolean",
  "HistorySet": ["WorkoutLogSerieModel"],
  "ReferenceSetHistory": "WorkoutLogSerieModel",
  "MinReps": "number",
  "MaxReps": "number",
  "isPlateAvailable": "boolean",
  "isDumbbellAvailable": "boolean",
  "isPulleyAvailable": "boolean",
  "isBandsAvailable": "boolean",
  "Speed": "number",
  "days": "number",
  "RIR": "number",
  "IsManual": "boolean",
  "Id": "number",
  "ReferenseReps": "number",
  "ReferenseWeight": "MultiUnityWeight",
  "IsDropSet": "boolean"
}
```

### WorkoutLogSerieModel

**Description:** Core model for tracking individual sets in a workout. Each instance represents one completed set.

```json
{
  "Id": "number", // Unique identifier for the set
  "Exercice": "ExerciceModel", // Exercise details
  "BodypartId": "number | null",
  "UserId": "string", // User who performed the set
  "LogDate": "string", // ISO 8601 datetime when set was performed
  "Reps": "number", // Number of repetitions completed
  "Weight": "MultiUnityWeight", // Weight used (supports kg/lbs)
  "OneRM": "MultiUnityWeight", // Calculated one-rep max
  "IsWarmups": "boolean", // True if this is a warmup set
  "Isbodyweight": "boolean", // True if bodyweight exercise
  "IsPlateAvailable": "boolean", // Equipment availability flags
  "IsDumbbellAvailable": "boolean",
  "IsBandsAvailable": "boolean",
  "IsPulleyAvailable": "boolean",
  "NbPause": "number", // Rest pause count
  "RIR": "number", // Reps in Reserve
  "IsOneHanded": "boolean", // True if single-arm/leg exercise
  "IsReferenceSet": "boolean" // True if this is a reference set
}
```

### RecommendationModel

**Description:** Contains workout recommendations for an exercise, including suggested weights, reps, and set structure.

```json
{
  "OneRMProgress": "number", // Progress percentage based on 1RM
  "IsMaxChallenge": "boolean", // True if this is a max effort attempt
  "IsLightSession": "boolean", // True if deload/light session
  "LastLogDate": "string | null", // Last time exercise was performed
  "FirstWorkSetReps": "number", // Recommended reps for first work set
  "FirstWorkSetWeight": "MultiUnityWeight", // Recommended weight for first work set
  "FirstWorkSet1RM": "MultiUnityWeight", // Calculated 1RM for first work set
  "IsPyramid": "boolean", // True for pyramid sets (ascending weight)
  "IsReversePyramid": "boolean", // True for reverse pyramid (descending weight)
  "HistorySet": ["WorkoutLogSerieModel"], // Previous sets for reference
  "ReferenceSetHistory": "WorkoutLogSerieModel", // Last reference set
  "MinReps": "number", // Minimum rep target
  "MaxReps": "number", // Maximum rep target
  "isPlateAvailable": "boolean", // Equipment availability
  "isDumbbellAvailable": "boolean",
  "isPulleyAvailable": "boolean",
  "isBandsAvailable": "boolean",
  "Speed": "number", // Tempo/speed recommendation
  "days": "number", // Days since last workout
  "RIR": "number", // Target Reps in Reserve
  "IsManual": "boolean", // True if manually adjusted
  "Id": "number",
  "ReferenseReps": "number", // Reference set reps
  "ReferenseWeight": "MultiUnityWeight", // Reference set weight
  "IsDropSet": "boolean" // True if drop set protocol
}
```

### MultiUnityWeight

```json
{
  "Value": "number",
  "Unit": "string"
}
```

### BooleanModel

```json
{
  "Result": "boolean",
  "IsFreePlan": "boolean",
  "IsTraining": "boolean",
  "IsMealPlan": "boolean"
}
```

## Account Management Endpoints

### Get User Info (Primary)

**Endpoint:** `POST /api/Account/GetUserInfoPyramid`
**Request:** `null`
**Response:** `UserInfosModel`

**Description:** Returns the complete user profile information. This is the primary endpoint for fetching user data after login.

**Important Notes:**

- Returns `Firstname` directly at the top level (not nested in a `Result` property)
- Field names are case-sensitive: use `Firstname` not `FirstName`
- No transformations are applied to the name fields
- This endpoint should be called after successful authentication

**Example Response:**

````json
{
  "Email": "<EMAIL>",
  "UId": "abc-123",
  "Firstname": "John",
  "Lastname": "Smith",
  "Gender": "Male",
  "MassUnit": "kg",
  "IsNormalSet": true,
  "RepsMinimum": 8,
  "RepsMaximum": 12,
  "IsVibrate": true,
  "WorkoutDuration": 60,
  "LastActiveDate": "2024-07-01T09:30:00",
  // ... additional fields omitted for brevity
}

### Get User Info

**Endpoint:** `POST /api/Account/GetUserInfo`
**Request:** `null`
**Response:** `UserInfosModel`

**Description:** Alternative endpoint that returns the same `UserInfosModel` structure as GetUserInfoPyramid.

### Get User Info with Meal Plan

**Endpoint:** `POST /api/Account/GetUserInfoWithMealPlan`
**Request:** `null`
**Response:** `UserInfoMealPlanModel`

**Response Structure:**
```json
{
  "MealPlan": "MealPlanModel",
  "UserInfos": "UserInfosModel",  // Same structure as GetUserInfoPyramid
  "BillingCycle": "string",
  "PaymentMethod": "string"
}
````

### User Registration

**Endpoint:** `POST /api/Account/Register`
**Request:** `RegisterModel`
**Response:** `BooleanModel`

### User Registration (Demo)

**Endpoint:** `POST /api/Account/RegisterUserBeforeDemo`
**Request:** `RegisterModel`
**Response:** `BooleanModel`

### User Registration (After Demo)

**Endpoint:** `POST /api/Account/RegisterUserAfterDemoV2`
**Request:** `RegisterModel`
**Response:** `UserInfosModel`

### User Registration (With User Info)

**Endpoint:** `POST /api/Account/RegisterWithUser`
**Request:** `RegisterModel`
**Response:** `UserInfosModel`

### Check V1 User

**Endpoint:** `POST /api/Account/IsV1User`
**Request:** `null`
**Response:** `BooleanModel`

### Check V1 User by Email

**Endpoint:** `POST /api/Account/IsV1UserEmail?email={email}`
**Request:** `string`
**Response:** `BooleanModel`

### Get User Weights

**Endpoint:** `POST /api/Account/GetUserWeights`
**Request:** `null`
**Response:** `List<UserWeight>`

### Check Email Exists

**Endpoint:** `POST /api/Account/IsEmailAlreadyExist`
**Request:** `IsEmailAlreadyExistModel`
**Response:** `BooleanModel`

### Forgot Password

**Endpoint:** `POST /api/Account/ForgotPassword`
**Request:** `ForgotPasswordModel`
**Response:** `BooleanModel`

### Delete Account

**Endpoint:** `POST /api/Account/DeleteUser`
**Request:** `null`
**Response:** `BooleanModel`

### Check Monthly Subscription

**Endpoint:** `POST /api/Account/IsMonthly`
**Request:** `null`
**Response:** `BooleanModel`

### Subscription Details

**Endpoint:** `POST /api/Account/SubscriptionDetail`
**Request:** `SubscriptionModel`
**Response:** `BooleanModel`

### Subscription Details with ID

**Endpoint:** `POST /api/Account/SubscriptionDetailWithId`
**Request:** `SubscriptionModel`
**Response:** `BooleanModel`

### User Profile Updates

**Endpoint:** `POST /api/Account/SetUserAge`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

**Endpoint:** `POST /api/Account/SetUserHeight`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

**Endpoint:** `POST /api/Account/SetUserWorkoutDuration`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

**Endpoint:** `POST /api/Account/SetUserAB`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

**Endpoint:** `POST /api/Account/SetUserTimerOptionsV2`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

**Endpoint:** `POST /api/Account/SetUserTimerOptionsV3`
**Request:** `UserInfosModel`
**Response:** `UserInfosModel`

### Device Token Management

**Endpoint:** `POST /api/Account/AddDeviceToken`
**Request:** `DeviceModel`
**Response:** `BooleanModel`

**Endpoint:** `POST /api/Account/RemoveDeviceToken`
**Request:** `DeviceModel`
**Response:** `BooleanModel`

### Health Check

**Endpoint:** `POST /api/Account/IsAlive`
**Request:** `null`
**Response:** `BooleanModel`

### Satisfaction Survey

**Endpoint:** `POST /api/Account/AddSatisfactionSurvey`
**Request:** `SatisfactionSurveyModel`
**Response:** `BooleanModel`

## Workout Management Endpoints

### Get User Workout Groups

**Endpoint:** `POST /api/Workout/GetUserWorkoutTemplateGroup`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get Featured Programs

**Endpoint:** `POST /api/Workout/GetFeaturedProgramForUserV2`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get System Workout Groups

**Endpoint:** `POST /api/Workout/GetSystemWorkoutTemplateGroup`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get Only System Workout Groups

**Endpoint:** `POST /api/Workout/GetOnlySystemWorkoutTemplateGroup`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get Customized System Workout Groups

**Endpoint:** `POST /api/Workout/GetCustomizedSystemWorkoutGroup`
**Request:** `EquipmentModel`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get Only Customized System Workout Groups

**Endpoint:** `POST /api/Workout/GetOnlyCustomizedSystemWorkoutGroup`
**Request:** `EquipmentModel`
**Response:** `GetUserWorkoutTemplateGroupResponseModel`

### Get User Workout

**Endpoint:** `POST /api/Workout/GetUserWorkout`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateResponseModel`

### Get Custom Workouts for User

**Endpoint:** `POST /api/Workout/GetCustomWorkoutsForUser`
**Request:** `null`
**Response:** `GetUserWorkoutTemplateResponseModel`

### Get User Customized Current Workout

**Endpoint:** `POST /api/Workout/GetUserCustomizedCurrentWorkout`
**Request:** `number` (workoutid)
**Response:** `WorkoutTemplateModel`

### Save Workout (V3)

**Endpoint:** `POST /api/Workout/SaveWorkoutV3Pro`
**Request:** `SaveWorkoutModel`
**Response:** `BooleanModel`

### Save and Get Workout Info

**Endpoint:** `POST /api/Workout/SaveGetWorkoutInfoPro`
**Request:** `SaveWorkoutModel`
**Response:** `GetUserProgramInfoResponseModel`

### Create New Workout Template Group

**Endpoint:** `POST /api/Workout/CreateNewWorkoutTemplateGroup`
**Request:** `WorkoutTemplateGroupModel`
**Response:** `BooleanModel`

### Restore User Workout Template

**Endpoint:** `POST /api/Workout/RestoreUserWorkoutTemplate`
**Request:** `WorkoutTemplateGroupModel`
**Response:** `BooleanModel`

### Rename Workout Template

**Endpoint:** `POST /api/Workout/RenameWorkoutTemplate`
**Request:** `WorkoutTemplateModel`
**Response:** `BooleanModel`

## Exercise Management Endpoints

### Get Exercise Recommendation

**Endpoint:** `POST /api/Exercise/GetRecommendationForExercise`
**Request:** `GetRecommendationForExerciseModel`
**Response:** `RecommendationModel`

### Get Rest Pause Recommendation

**Endpoint:** `POST /api/Exercise/GetRecommendationRestPauseForExercise`
**Request:** `GetRecommendationForExerciseModel`
**Response:** `RecommendationModel`

### Add New Exercise Log

**Endpoint:** `POST /api/Exercise/AddNewExerciseLog`
**Request:** `NewExerciceLogModel`
**Response:** `BooleanModel`

### Add New Exercise Log with More Sets

**Endpoint:** `POST /api/Exercise/AddNewExerciseLogWithMoreSet`
**Request:** `NewExerciceLogModel`
**Response:** `BooleanModel`

### Add Workout Log Serie

**Endpoint:** `POST /api/Exercise/AddWorkoutLogSerieNew`
**Request:** `WorkoutLogSerieModel`
**Response:** `BooleanModel`

### Add Workout Log Serie List

**Endpoint:** `POST /api/Exercise/AddWorkoutLogSerieListNew`
**Request:** `List<WorkoutLogSerieModel>`
**Response:** `BooleanModel`

### Get Workout History (All Time)

**Endpoint:** `POST /api/Exercise/GetWorkoutHistoryForAlltime`
**Request:** `GetUserWorkoutLogAverageForExerciseRequest`
**Response:** `List<HistoryModel>`

### Get Workout History (By Date)

**Endpoint:** `POST /api/Exercise/GetWorkoutHistoryForDate`
**Request:** `GetUserWorkoutLogAverageForExerciseRequest`
**Response:** `List<HistoryModel>`

### Get One RM for Exercise

**Endpoint:** `POST /api/Exercise/GetOneRMForExercise`
**Request:** `GetOneRMforExerciseModel`
**Response:** `OneRMModel`

### Get Custom Exercises for User

**Endpoint:** `POST /api/Exercise/GetCustomExerciseForUser`
**Request:** `string` (userName)
**Response:** `GetUserExerciseResponseModel`

### Delete Exercise

**Endpoint:** `POST /api/Exercise/DeleteExercise`
**Request:** `ExerciceModel`
**Response:** `BooleanModel`

### Validate Featured Program Code

**Endpoint:** `POST /api/Exercise/IsValidFeatureProgramCode`
**Request:** `FeaturedProgramModel`
**Response:** `BooleanModel`

### Validate Featured Program Code V2

**Endpoint:** `POST /api/Exercise/IsValidFeatureProgramCodeV2`
**Request:** `FeaturedProgramModel`
**Response:** `UnlockCodeResponseModel`

## Exercise Endpoints

### Get Recommendation for Exercise

**Endpoint:** `POST /api/Exercise/GetRecommendationForExercise`
**Request:** `GetRecommendationForExerciseModel`
**Response:** `RecommendationModel`

**Description:** Get workout recommendations for a specific exercise based on user's history and settings.

### Get Recommendation (Normal RIR)

**Endpoint:** `POST /api/Exercise/GetRecommendationNormalRIRForExercise`
**Request:** `GetRecommendationForExerciseModel`
**Response:** `RecommendationModel`

**Description:** Get recommendations using Reps in Reserve (RIR) methodology for normal sets.

### Get Recommendation (Rest Pause)

**Endpoint:** `POST /api/Exercise/GetRecommendationRestPauseForExercise`
**Request:** `GetRecommendationForExerciseModel`
**Response:** `RecommendationModel`

**Description:** Get recommendations for rest-pause training protocol.

### Get Recommendation (Rest Pause 2)

**Endpoint:** `POST /api/Exercise/GetRecommendationRestPause2ForExercise`
**Request:** `GetRecommendationForExerciseModel`
**Response:** `RecommendationModel`

**Description:** Alternative rest-pause recommendation algorithm.

### Get Recommendation (Rest Pause RIR)

**Endpoint:** `POST /api/Exercise/GetRecommendationRestPauseRIRForExercise`
**Request:** `GetRecommendationForExerciseModel`
**Response:** `RecommendationModel`

**Description:** Rest-pause recommendations with RIR targets.

### Get User Exercises

**Endpoint:** `POST /api/Exercise/GetUserExercise`
**Request:** `null`
**Response:** `GetUserExerciseResponseModel`

**Description:** Retrieve all exercises available to the user.

### Get Exercise History

**Endpoint:** `POST /api/Exercise/GetExerciseHistory`
**Request:** `ExerciseModel`
**Response:** `GetExercisesLogResponseModel`

**Description:** Get complete workout history for a specific exercise.

### Save Workout Set

**Endpoint:** `POST /api/Exercise/SaveWorkoutSet`
**Request:** `WorkoutLogSerieModel`
**Response:** `BooleanModel`

**Description:** Save a completed workout set to the user's log.

### Update Workout Set

**Endpoint:** `POST /api/Exercise/UpdateWorkoutSet`
**Request:** `WorkoutLogSerieModel`
**Response:** `BooleanModel`

**Description:** Update an existing workout set.

### Delete Workout Set

**Endpoint:** `POST /api/Exercise/DeleteWorkoutSet`
**Request:** `WorkoutLogSerieModel`
**Response:** `BooleanModel`

**Description:** Delete a workout set from the log.

## Workout Log Endpoints

### Get User Workout Log Average

**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogAverageV2`
**Request:** `null`
**Response:** `GetUserWorkoutLogAverageResponse`

### Get User Workout Log Average with User Stats

**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2`
**Request:** `null`
**Response:** `GetUserWorkoutLogAverageResponse`

### Get User Workout Log Average with Sets

**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogAverageWithSetsTimeZoneInfoV2`
**Request:** `TimeZoneInfo`
**Response:** `GetUserWorkoutLogAverageResponse`

### Get User Workout Log Average for Exercise

**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogAverageForExercise`
**Request:** `GetUserWorkoutLogAverageForExerciseRequest`
**Response:** `GetUserWorkoutLogAverageResponse`

### Get User Workout Log Average for Exercise (Period)

**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogAverageForExerciseForPeriod`
**Request:** `GetUserWorkoutLogAverageForExerciseRequest`
**Response:** `GetUserWorkoutLogAverageResponse`

### Get User Workout Stats

**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutStats`
**Request:** `null`
**Response:** `HistoryExerciseModel`

### Get User Workout Log Date

**Endpoint:** `POST /api/WorkoutLog/GetUserWorkoutLogDate`
**Request:** `null`
**Response:** `GetUserWorkoutLogDate`

## Chat and Messaging Endpoints

### Send Message

**Endpoint:** `POST /api/Account/SendMessage`
**Request:** `ChatModel`
**Response:** `BooleanModel`

### Send Admin Message

**Endpoint:** `POST /api/Account/SendAdminMessage`
**Request:** `ChatModel`
**Response:** `BooleanModel`

### Fetch Inbox

**Endpoint:** `POST /api/Account/FetchInbox`
**Request:** `null`
**Response:** `List<ChatRoomModel>`

### Fetch Inbox by Type

**Endpoint:** `POST /api/Account/FetchInboxByType`
**Request:** `number` (0 for unread, 1 for read)
**Response:** `List<ChatRoomModel>`

### Fetch Chat Box

**Endpoint:** `POST /api/Account/FetchChatBox`
**Request:** `ChatModel`
**Response:** `List<ChatModel>`

### Fetch Group Messages

**Endpoint:** `POST /api/Account/FetchGroupMessages`
**Request:** `GroupChatModel`
**Response:** `List<GroupChatModel>`

### Delete Group Chat Message

**Endpoint:** `POST /api/Account/DeleteChatMessage`
**Request:** `GroupChatModel`
**Response:** `BooleanModel`

### Mark Message as Read

**Endpoint:** `POST /api/Account/MarkMessageAsRead`
**Request:** `ChatModel`
**Response:** `BooleanModel`

### Get Muted Users

**Endpoint:** `POST /api/Account/GetMutedUserList`
**Request:** `null`
**Response:** `List<string>`

## Request Models

### RegisterModel

```json
{
  "Firstname": "string",
  "Lastname": "string",
  "EmailAddress": "string",
  "Password": "string",
  "ConfirmPassword": "string",
  "SelectedGender": "string",
  "MassUnit": "string",
  "RepsMinimum": "number",
  "RepsMaximum": "number",
  "Age": "number",
  "Increments": "number",
  "Min": "number",
  "Max": "number",
  "IsQuickMode": "boolean",
  "SetStyle": "boolean",
  "IsHumanSupport": "boolean",
  "IsPyramid": "boolean",
  "IsDropSet": "boolean",
  "IsCardio": "boolean",
  "MainGoal": "string",
  "BodyPartPrioriy": "string",
  "AIMessage": "string",
  "ReminderDays": "string",
  "ReminderTime": "timespan",
  "TimeBeforeWorkout": "number",
  "IsReminderEmail": "boolean",
  "BodyWeight": "MultiUnityWeight",
  "WeightGoal": "MultiUnityWeight",
  "LearnMoreDetails": "LearnMore",
  "EquipmentModel": "EquipmentModel",
  "ProgramId": "number",
  "IsMobility": "boolean",
  "MobilityLevel": "string",
  "IsExerciseQuickMode": "boolean",
  "IsRecommendedReminder": "boolean"
}
```

### LoginModel

```json
{
  "Username": "string",
  "Password": "string",
  "NewPassword": "string",
  "Validation": "string"
}
```

### GetRecommendationForExerciseModel

```json
{
  "Username": "string",
  "ExerciseId": "number",
  "WorkoutId": "number",
  "IsQuickMode": "boolean",
  "LightSessionDays": "number",
  "SwapedExId": "number",
  "IsStrengthPhashe": "boolean",
  "IsFreePlan": "boolean",
  "IsFirstWorkoutOfStrengthPhase": "boolean",
  "VersionNo": "number"
}
```

### SaveWorkoutModel

```json
{
  "WorkoutId": "number",
  "WorkoutTemplateId": "number"
}
```

### ChatModel

```json
{
  "Id": "number",
  "Message": "string",
  "UserId": "string",
  "UserName": "string",
  "MessageDate": "datetime",
  "IsRead": "boolean",
  "IsFromAdmin": "boolean"
}
```

### DeviceModel

```json
{
  "DeviceToken": "string",
  "DeviceType": "string",
  "UserId": "string"
}
```

## Error Handling

### HTTP Status Codes

- **200 OK**: Request successful
- **400 Bad Request**: Invalid request format or parameters
- **401 Unauthorized**: Authentication required or token expired
- **500 Internal Server Error**: Server-side error

### Error Response Format

```json
{
  "error": "string",
  "error_description": "string"
}
```

### Common Error Scenarios

1. **Token Expiration**: Returns 401 status code
2. **Network Connectivity**: Implement retry logic with exponential backoff
3. **Server Timeout**: Default timeout is 100 seconds, 200 seconds for heavy operations
4. **Rate Limiting**: Implement appropriate delays between requests

### Retry Logic Implementation

The MAUI app implements retry logic for failed requests:

```csharp
// Retry up to 4 times for failed requests
if (attempNr > 0)
    return await PostJson<T>(route, model, attempNr - 1);

// Show user-friendly error dialog
var result = await DisplayCustomPopupForResult(
    "Loading error",
    "Slow or no connection. Please check and try again.",
    "Retry loading",
    "Cancel"
);

if (result == PopupAction.OK) {
    attempNr = 4;
    return await PostJson<T>(route, model, attempNr - 1);
}
```

## Special Workout Features

### Weight Unit Handling

The API uses `MultiUnityWeight` to support both metric (kg) and imperial (lbs) units:

```json
{
  "Value": 100,
  "Unit": "kg" // or "lbs"
}
```

**Conversion:**

- 1 kg = 2.20462 lbs
- Weight is stored in both units in the backend
- User's preference is stored in `MassUnit` field of `UserInfosModel`

### Equipment Types

Each exercise and workout set can specify available equipment:

- `IsPlateAvailable`: Barbell with plates
- `IsDumbbellAvailable`: Dumbbells
- `IsBandsAvailable`: Resistance bands
- `IsPulleyAvailable`: Cable/pulley machines

### Special Set Types

**Warmup Sets:**

- Marked with `IsWarmups: true`
- Not counted in working set calculations
- Typically lighter weight with higher reps

**Bodyweight Exercises:**

- Marked with `Isbodyweight: true`
- Weight can include additional load (weighted pull-ups, etc.)
- User's bodyweight is factored into calculations

**Pyramid Sets:**

- `IsPyramid: true`: Weight increases each set
- `IsReversePyramid: true`: Weight decreases each set
- Automatically calculated based on first set recommendation

**Drop Sets:**

- `IsDropSet: true`
- Multiple sets with decreasing weight
- No rest between drops

### Rest Time Recommendations

Rest times are calculated based on rep ranges and exercise type:

```javascript
// Rest time logic from backend
function getRestTime(reps, isSuperset, exerciseType) {
  if (exerciseType === 'flexibility') {
    return 25 // seconds
  }

  // Strength sets (1-3 reps)
  if (reps <= 3) {
    return isSuperset ? 20 : 180 // 3 minutes for heavy sets
  }

  // Hypertrophy sets (4-12 reps)
  if (reps <= 12) {
    return isSuperset ? 20 : 120 // 2 minutes
  }

  // Endurance sets (13+ reps)
  return isSuperset ? 20 : 60 // 1 minute
}
```

### RIR (Reps in Reserve)

The recommendation system uses RIR to auto-regulate intensity:

- `RIR: 0` - Maximum effort (failure)
- `RIR: 1` - Stop 1 rep before failure
- `RIR: 2` - Stop 2 reps before failure
- `RIR: 3+` - Lighter/technique work

### Workout Phases

**Normal Sets:**

- Standard straight sets
- Use `GetRecommendationNormalRIRForExercise`

**Rest-Pause:**

- Perform set to near failure
- Rest 10-15 seconds
- Continue for more reps
- Use `GetRecommendationRestPauseForExercise`

**Strength Phase:**

- Lower reps (1-5)
- Longer rest periods
- Focus on progressive overload
- Flag: `IsStrengthPhashe: true` (note the typo in API)

### Quick Mode

Two types of quick mode:

1. **Workout Quick Mode** (`IsQuickMode`):
   - Fewer exercises per workout
   - Reduced volume

2. **Exercise Quick Mode** (`IsExerciseQuickMode`):
   - Simplified set/rep schemes
   - Faster workout completion

## Recommendation Algorithm Details

### Core Algorithm: GetRecommendationNormalRIRWithoutWarmupsNew

The recommendation engine uses a sophisticated algorithm that analyzes workout history and applies progressive overload principles.

#### Key Input Parameters

**Workout History:**

```csharp
decimal Weight0  // Last workout weight
decimal Weight1  // Second last workout weight
decimal Weight2  // Third last workout weight
int Reps0       // Last workout reps
int Reps1       // Second last workout reps
int Reps2       // Third last workout reps
```

**Training Parameters:**

```csharp
int RepsMinimum     // Minimum target reps (e.g., 8)
int RepsMaximum     // Maximum target reps (e.g., 12)
int? RIR            // Reps in Reserve (0-4)
bool IsEasy         // Easy difficulty level
bool IsMedium       // Medium difficulty level
bool strengthPhase  // Whether in strength training phase
```

**Equipment Parameters:**

```csharp
string availablePlates     // "2.5,5,10,15,20,25"
string availableDumbbells  // "5,10,15,20,25,30"
string availablePulley     // Pulley weight increments
string availableBands      // Resistance band options
decimal barWeight         // Barbell weight (typically 20kg/45lbs)
```

#### Algorithm Steps

**1. Calculate One Rep Max (1RM):**

```javascript
// 1RM calculation based on weight and reps
function computeOneRM(weight, reps) {
  if (reps === 1) return weight
  return weight * (1 + reps / 30) // Simplified formula
}
```

**2. Rep Range Alternation:**

```javascript
// Alternate between high and low rep ranges
const spreadReps = repsMaximum - repsMinimum
const halfSpreadReps = Math.floor(spreadReps / 2)

if (lastReps <= repsMaximum - halfSpreadReps) {
  // Go high - upper half of rep range
  targetReps = randomBetween(repsMaximum - halfSpreadReps, repsMaximum)
} else {
  // Go low - lower half of rep range
  targetReps = randomBetween(repsMinimum, repsMinimum + halfSpreadReps)
}
```

**3. RIR-Based Weight Progression:**

The algorithm adjusts weight based on RIR feedback:

| Exercise Type | RIR | Weight Adjustment |
| ------------- | --- | ----------------- |
| Easy          | 0   | -6% (too hard)    |
| Easy          | 1   | -3%               |
| Easy          | 2   | -1%               |
| Easy          | 3   | 0% (perfect)      |
| Easy          | 4   | +1% (too easy)    |
| Medium        | 0   | -9%               |
| Medium        | 1   | -6%               |
| Medium        | 2   | -3%               |
| Medium        | 3   | 0%                |
| Medium        | 4   | +3%               |
| Hard          | 0   | -12%              |
| Hard          | 1   | -9%               |
| Hard          | 2   | -6%               |
| Hard          | 3   | -3%               |
| Hard          | 4   | +6%               |

**4. Bodyweight Exercise Logic:**

```javascript
if (isBodyweight) {
  // Adjust reps based on RIR
  if (isEasy) {
    if (RIR === 0) targetReps = lastReps - 3
    if (RIR === 1) targetReps = lastReps - 2
    if (RIR === 2) targetReps = lastReps - 1
    if (RIR === 3) targetReps = lastReps
    if (RIR === 4) targetReps = lastReps + 1
  }
  // Weight is user's bodyweight (+ any additional load)
  recommendedWeight = bodyWeight
}
```

**5. Strength Phase Adjustments:**

```javascript
if (strengthPhase && !isBodyweight) {
  // Cap reps at lower range for strength
  if (targetReps >= 5) {
    targetReps = 4
    // Increase weight to compensate
    recommendedWeight = computeWeight(targetReps, oneRM)
  }
}
```

### Warm-up Set Calculation

The algorithm automatically calculates warm-up sets:

```javascript
// Warm-up percentages of working weight
const warmup1Weight = workingWeight * 0.5 // 50%
const warmup2Weight = workingWeight * 0.75 // 75%

// Warm-up reps
let warmup1Reps = Math.floor(workingReps * 0.75)
let warmup2Reps = Math.floor(workingReps * 0.5)

// Minimum warm-up reps
if (warmup1Reps < 5) warmup1Reps = 5
if (warmup2Reps < 3) warmup2Reps = 3

// Cap warm-up reps at 10
if (warmup1Reps > 10) warmup1Reps = 10
if (warmup2Reps > 10) warmup2Reps = 10
```

### Weight Rounding Logic

Weights are rounded to available equipment:

```javascript
function roundToAvailableWeight(targetWeight, equipment, min, max) {
  // For plates: round to nearest plate combination
  // For dumbbells: round to nearest available dumbbell
  // For bands: select appropriate band resistance

  // Ensure within min/max limits
  if (targetWeight < min) return min
  if (targetWeight > max) return max

  return nearestAvailable
}
```

### Special Cases

**Light Sessions:**

- Reduce weight by 15-20%
- Maintain same rep range
- Used for deload weeks

**Max Challenge:**

- Test true 1RM
- Single rep attempts
- Longer rest periods

**First Workout:**

- Conservative starting weights
- Focus on form over load
- Establish baseline for progression

### Implementation Example

```javascript
async function getRecommendation(exerciseId, workoutHistory) {
  const request = {
    Username: userEmail,
    ExerciseId: exerciseId,
    WorkoutId: currentWorkoutId,
    IsQuickMode: userSettings.isQuickMode,
    LightSessionDays: 0,
    IsStrengthPhashe: userSettings.isStrength,
    IsFreePlan: false,
    VersionNo: 1,
  }

  const recommendation = await api.post(
    '/api/Exercise/GetRecommendationNormalRIRForExercise',
    request
  )

  return {
    weight: recommendation.FirstWorkSetWeight,
    reps: recommendation.FirstWorkSetReps,
    sets: recommendation.Series,
    warmups: recommendation.WarmUpsList,
    restTime: calculateRestTime(recommendation.FirstWorkSetReps),
  }
}
```

## Implementation Guidelines

### HTTP Client Configuration

1. **Base Address**: Set to appropriate environment URL
2. **Authorization Header**: Include bearer token for authenticated requests
3. **Content-Type**: Use `application/json` for POST requests
4. **Timeout**: Configure appropriate timeouts (100-200 seconds)

### Request/Response Handling

1. **Serialization**: Use JSON serialization for request/response bodies
2. **Error Handling**: Implement proper error handling for all status codes
3. **Logging**: Log requests and responses for debugging (exclude sensitive data)
4. **Retry Logic**: Implement retry mechanism for transient failures

### Authentication Flow

1. **Login**: Call `/token` endpoint with credentials
2. **Store Token**: Securely store the access token
3. **Include Token**: Add bearer token to all subsequent requests
4. **Token Refresh**: Handle token expiration and refresh as needed
5. **Logout**: Clear stored tokens and session data

### Data Validation

1. **Input Validation**: Validate all user inputs before sending to API
2. **Response Validation**: Validate API responses before processing
3. **Type Safety**: Use strongly-typed models for all API interactions
4. **Null Handling**: Handle null/undefined values appropriately

## Common Issues and Debugging

### UserInfosModel Field Name Issues

The most common issue when integrating with the API is using incorrect field names. The backend uses specific casing that differs from common conventions:

**Correct Field Names:**

- `Firstname` (NOT `FirstName`, `firstName`, or `first_name`)
- `Lastname` (NOT `LastName`, `lastName`, or `last_name`)
- `MassUnit` (NOT `WeightUnit`)
- `UId` (NOT `Id`, `UserId`, or `uid`)

**Example - Common Mistake:**

```javascript
// ❌ WRONG - This will return undefined
const firstName = userInfo.FirstName
const lastName = userInfo.LastName

// ✅ CORRECT
const firstName = userInfo.Firstname
const lastName = userInfo.Lastname
```

### Debugging User Info Issues

If you cannot display the user's first name after calling GetUserInfoPyramid:

1. **Check Response Structure**: The API returns fields at the top level, not nested:

   ```javascript
   // ❌ WRONG - No Result wrapper
   const name = response.Result.Firstname

   // ✅ CORRECT - Direct access
   const name = response.Firstname
   ```

2. **Verify Field Names**: Use browser DevTools to inspect the actual response:

   ```javascript
   const response = await apiClient.getUserInfoPyramid()
   console.log('Full response:', JSON.stringify(response, null, 2))
   console.log('Firstname field:', response.Firstname) // Note the exact casing
   ```

3. **Check Authentication**: Ensure the bearer token is included in the request header

4. **Handle Null Values**: Many fields can be null, implement proper null checking:
   ```javascript
   const displayName = userInfo.Firstname || 'User'
   ```

### Related Endpoints Comparison

| Endpoint                                | Returns User Profile Fields | Use Case                           |
| --------------------------------------- | --------------------------- | ---------------------------------- |
| GetUserInfoPyramid                      | Yes - Full UserInfosModel   | Primary user profile endpoint      |
| GetUserInfo                             | Yes - Full UserInfosModel   | Alternative to GetUserInfoPyramid  |
| GetUserInfoWithMealPlan                 | Yes - Nested in `UserInfos` | When meal plan data is also needed |
| GetUserWorkoutLogAverageWithUserStatsV2 | No                          | For workout statistics only        |
| Login (/token)                          | No                          | Returns auth token only            |

## Example Implementation (JavaScript/TypeScript)

```typescript
class DrMuscleApiClient {
  private baseUrl: string
  private accessToken: string | null = null

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }

  async login(username: string, password: string): Promise<LoginSuccessResult> {
    const formData = new URLSearchParams()
    formData.append('grant_type', 'password')
    formData.append('username', username)
    formData.append('password', password)

    const response = await fetch(`${this.baseUrl}/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData,
    })

    if (!response.ok) {
      throw new Error('Login failed')
    }

    const result = await response.json()
    this.accessToken = result.access_token
    return result
  }

  async postJson<T>(route: string, model: any): Promise<T> {
    const response = await fetch(`${this.baseUrl}/${route}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: this.accessToken ? `bearer ${this.accessToken}` : '',
      },
      body: JSON.stringify(model),
    })

    if (!response.ok) {
      if (response.status === 401) {
        // Handle unauthorized - redirect to login
        throw new Error('Unauthorized')
      }
      throw new Error(`API Error: ${response.status}`)
    }

    return await response.json()
  }

  async getUserWorkoutGroup(): Promise<GetUserWorkoutTemplateGroupResponseModel> {
    return this.postJson('api/Workout/GetUserWorkoutTemplateGroup', null)
  }

  async getRecommendationForExercise(
    model: GetRecommendationForExerciseModel
  ): Promise<RecommendationModel> {
    return this.postJson('api/Exercise/GetRecommendationForExercise', model)
  }

  async addWorkoutLogSerie(model: WorkoutLogSerieModel): Promise<BooleanModel> {
    return this.postJson('api/Exercise/AddWorkoutLogSerieNew', model)
  }
}
```

## Testing Recommendations

1. **Unit Tests**: Test API client methods with mock responses
2. **Integration Tests**: Test against actual API endpoints
3. **Error Scenarios**: Test error handling and retry logic
4. **Performance Tests**: Test with large datasets and slow connections
5. **Authentication Tests**: Test token handling and refresh scenarios

## Security Considerations

1. **Token Storage**: Store access tokens securely (encrypted storage)
2. **HTTPS Only**: Always use HTTPS for API communication
3. **Input Sanitization**: Sanitize all user inputs before sending to API
4. **Sensitive Data**: Never log sensitive information (passwords, tokens)
5. **Token Expiration**: Handle token expiration gracefully

---

_This API reference guide is based on the Dr. Muscle MAUI application codebase analysis. For the most up-to-date API specifications, consult the backend API documentation or contact the development team._
