import { useState, useCallback, useMemo } from 'react'

interface LoadingTask {
  progress: number
  status: string
  weight: number
}

interface LoadingTasks {
  [key: string]: LoadingTask
}

interface UseLoadingProgressReturn {
  /** Current overall progress (0-100) */
  progress: number
  /** Current status message */
  status: string
  /** All tracked tasks */
  tasks: LoadingTasks
  /** Whether all tasks are complete */
  isComplete: boolean
  /** Add a new task to track */
  addTask: (id: string, status: string, weight?: number) => void
  /** Update task progress and optionally status */
  updateTask: (id: string, progress: number, status?: string) => void
  /** Remove a task from tracking */
  removeTask: (id: string) => void
  /** Reset all progress */
  reset: () => void
}

/**
 * Hook for tracking loading progress across multiple tasks
 * Calculates weighted progress and manages status messages
 */
export function useLoadingProgress(): UseLoadingProgressReturn {
  const [tasks, setTasks] = useState<LoadingTasks>({})
  const [lastUpdatedTask, setLastUpdatedTask] = useState<string | null>(null)

  // Add a new task to track
  const addTask = useCallback((id: string, status: string, weight = 1) => {
    setTasks((prev) => ({
      ...prev,
      [id]: {
        progress: 0,
        status,
        weight,
      },
    }))
    setLastUpdatedTask(id)
  }, [])

  // Update task progress and optionally status
  const updateTask = useCallback(
    (id: string, progress: number, status?: string) => {
      setTasks((prev) => {
        if (!prev[id]) return prev

        return {
          ...prev,
          [id]: {
            ...prev[id],
            progress: Math.max(0, Math.min(100, progress)),
            status: status || prev[id].status,
          },
        }
      })
      setLastUpdatedTask(id)
    },
    []
  )

  // Remove a task from tracking
  const removeTask = useCallback((id: string) => {
    setTasks((prev) => {
      const { [id]: removed, ...rest } = prev
      return rest
    })
  }, [])

  // Reset all progress
  const reset = useCallback(() => {
    setTasks({})
    setLastUpdatedTask(null)
  }, [])

  // Calculate weighted progress
  const progress = useMemo(() => {
    const taskList = Object.values(tasks)
    if (taskList.length === 0) return 0

    const totalWeight = taskList.reduce((sum, task) => sum + task.weight, 0)
    if (totalWeight === 0) return 0

    const weightedProgress = taskList.reduce(
      (sum, task) => sum + task.progress * task.weight,
      0
    )

    return weightedProgress / totalWeight
  }, [tasks])

  // Get current status (from most recently updated task)
  const status = useMemo(() => {
    if (lastUpdatedTask && tasks[lastUpdatedTask]) {
      return tasks[lastUpdatedTask].status
    }

    // Fallback to last task in the list
    const taskList = Object.values(tasks)
    if (taskList.length > 0) {
      const lastTask = taskList[taskList.length - 1]
      return lastTask?.status || ''
    }
    return ''
  }, [tasks, lastUpdatedTask])

  // Check if all tasks are complete
  const isComplete = useMemo(() => {
    const taskList = Object.values(tasks)
    return (
      taskList.length > 0 && taskList.every((task) => task.progress === 100)
    )
  }, [tasks])

  return {
    progress,
    status,
    tasks,
    isComplete,
    addTask,
    updateTask,
    removeTask,
    reset,
  }
}
