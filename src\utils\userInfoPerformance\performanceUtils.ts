/**
 * Performance utility functions
 */

export function mark(
  markName: string,
  metadata?: Record<string, unknown>
): void {
  if (typeof performance === 'undefined') return

  try {
    performance.mark(markName)

    // Store metadata if provided
    if (metadata && typeof window !== 'undefined') {
      window.sessionStorage.setItem(
        `perf-meta-${markName}`,
        JSON.stringify(metadata)
      )
    }
  } catch (error) {
    // Silently fail
  }
}

export function measure(
  measureName: string,
  startMark: string,
  endMark: string
): number | null {
  if (typeof performance === 'undefined') return null

  try {
    performance.measure(measureName, startMark, endMark)
    const entries = performance.getEntriesByName(measureName)
    return entries.length > 0 ? entries[entries.length - 1]!.duration : null
  } catch (error) {
    return null
  }
}

export function generateSessionId(): string {
  return `userinfo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}
