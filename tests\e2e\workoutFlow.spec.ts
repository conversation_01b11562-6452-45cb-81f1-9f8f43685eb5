import { test, expect } from '@playwright/test'

test.describe('Complete Workout Flow E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 390, height: 844 })
    await page.goto('/')
  })

  test('should complete full workout flow from login to completion', async ({ page }) => {
    // Step 1: Login
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: 'Login' }).click()

    // Wait for redirect to workout page
    await expect(page).toHaveURL('/workout')
    
    // Step 2: Start Workout
    await expect(page.getByText("Today's Workout")).toBeVisible()
    await page.getByRole('button', { name: 'Start Workout' }).click()

    // Step 3: Complete First Exercise
    await expect(page).toHaveURL(/\/workout\/exercise\/\d+/)
    
    // Complete warmup sets
    for (let i = 0; i < 2; i++) {
      await page.getByRole('button', { name: 'Save Set' }).click()
      await page.waitForTimeout(100)
    }

    // Complete work sets
    for (let i = 0; i < 3; i++) {
      await page.getByRole('button', { name: 'Save Set' }).click()
      
      // Handle RIR picker on first work set
      if (i === 0) {
        await page.getByText('2-3 reps left').click()
      }
      
      // Wait for rest timer
      await expect(page).toHaveURL('/workout/rest-timer')
      await page.getByRole('button', { name: 'Skip Rest' }).click()
    }

    // Step 4: Complete Workout
    await expect(page).toHaveURL('/workout/complete')
    await expect(page.getByText('Workout Complete!')).toBeVisible()
    await expect(page.getByText(/Total Duration/)).toBeVisible()
    await expect(page.getByText(/Sets Completed/)).toBeVisible()
  })

  test('should handle network errors gracefully', async ({ page, context }) => {
    // Login first
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: 'Login' }).click()
    await expect(page).toHaveURL('/workout')

    // Start workout
    await page.getByRole('button', { name: 'Start Workout' }).click()

    // Simulate offline mode
    await context.setOffline(true)

    // Try to save a set
    await page.getByRole('button', { name: 'Save Set' }).click()

    // Should show offline message
    await expect(page.getByText(/offline/i)).toBeVisible()

    // Come back online
    await context.setOffline(false)

    // Data should sync
    await page.reload()
    await expect(page.getByText(/synced/i)).toBeVisible()
  })

  test('should preserve workout data on browser refresh', async ({ page }) => {
    // Login and start workout
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Password').fill('password123')
    await page.getByRole('button', { name: 'Login' }).click()
    await page.getByRole('button', { name: 'Start Workout' }).click()

    // Save a set
    await page.getByRole('button', { name: 'Save Set' }).click()

    // Refresh the page
    await page.reload()

    // Should still be on the same exercise
    await expect(page).toHaveURL(/\/workout\/exercise\/\d+/)
    await expect(page.getByText(/Set 2/)).toBeVisible()
  })

  test('should show proper loading states', async ({ page }) => {
    await page.goto('/workout')

    // Should show loading spinner
    await expect(page.getByTestId('loading-spinner')).toBeVisible()

    // Should eventually show content
    await expect(page.getByText("Today's Workout")).toBeVisible({ timeout: 5000 })
  })
})