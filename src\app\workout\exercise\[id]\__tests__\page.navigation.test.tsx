import { render } from '@testing-library/react'
import { vi, describe, it, expect } from 'vitest'
import ExercisePage from '../page'

// Mock the ExercisePageClient to avoid complex dependencies
vi.mock('../ExercisePageClient', () => ({
  ExercisePageClient: vi.fn(({ exerciseId }) => (
    <div data-testid="exercise-client">Exercise {exerciseId}</div>
  )),
}))

describe('ExercisePage Navigation', () => {
  it('should handle async params correctly for RSC', async () => {
    // Given: An async params promise
    const params = Promise.resolve({ id: '30431' })

    // When: Rendering the async page
    const { getByTestId } = render(<ExercisePage params={params} />)

    // Then: Should render with the correct ID
    // Note: In a real RSC scenario, this would be server-rendered
    expect(getByTestId('exercise-client')).toHaveTextContent('Exercise 30431')
  })

  it('should not cause RSC fetch errors', async () => {
    // This test verifies the page structure is compatible with RSC
    const params = Promise.resolve({ id: '30431' })

    // The page should be renderable without throwing
    expect(() => render(<ExercisePage params={params} />)).not.toThrow()
  })
})
