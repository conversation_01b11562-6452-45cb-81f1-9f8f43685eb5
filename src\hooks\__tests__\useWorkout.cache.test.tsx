import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React from 'react'
import { useWorkout } from '../useWorkout'
import * as workoutApiModule from '@/api/workouts'
import * as workoutCacheModule from '@/utils/workoutCache'
import { useAuthStore } from '@/stores/authStore'
import type { WorkoutTemplateGroupModel } from '@/types'

// Mock dependencies
vi.mock('@/api/workouts', () => ({
  workoutApi: {
    getUserProgramInfo: vi.fn(),
    getUserWorkout: vi.fn(),
    getExerciseRecommendation: vi.fn(),
    saveWorkoutSet: vi.fn(),
    completeWorkout: vi.fn(),
  },
}))

vi.mock('@/stores/authStore', () => ({
  useAuthStore: vi.fn(() => ({
    isAuthenticated: true,
  })),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(() => ({
    currentWorkout: null,
    currentSetIndex: 0,
    currentExerciseIndex: 0,
    exercises: [],
    workoutSession: null,
    isLoading: false,
    error: null,
    setWorkout: vi.fn(),
    startWorkout: vi.fn(),
    nextSet: vi.fn(),
    nextExercise: vi.fn(),
    saveSet: vi.fn(),
    completeWorkout: vi.fn(),
    setError: vi.fn(),
    getCurrentExercise: vi.fn(),
    getNextExercise: vi.fn(),
    getRestDuration: vi.fn(),
  })),
}))

// Mock workout cache
vi.mock('@/utils/workoutCache')

const mockCachedWorkout: WorkoutTemplateGroupModel[] = [
  {
    Id: 1,
    Label: 'Cached Program',
    WorkoutTemplates: [
      {
        Id: 1,
        Label: 'Cached Workout',
        IsSystemExercise: true,
        UserId: '',
        Exercises: [{ Id: 1, Label: 'Cached Exercise' }],
        WorkoutSettingsModel: {},
      },
    ],
    IsFeaturedProgram: false,
    UserId: '',
    IsSystemExercise: true,
    RequiredWorkoutToLevelUp: 5,
    ProgramId: 1,
  },
]

const mockFreshWorkout = {
  GetUserProgramInfoResponseModel: {
    RecommendedProgram: { Id: 2, Label: 'Fresh Program' },
    NextWorkoutTemplate: {
      Id: 2,
      Label: 'Fresh Workout',
      Exercises: [{ Id: 2, Label: 'Fresh Exercise' }],
    },
    WorkoutTemplates: [],
  },
}

describe('useWorkout - Cache Integration', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )

  it('should show cached data immediately while fetching fresh data', async () => {
    // Setup cache to return cached data
    vi.spyOn(workoutCacheModule.WorkoutCache, 'get').mockReturnValue(
      mockCachedWorkout
    )
    vi.spyOn(workoutCacheModule.WorkoutCache, 'set').mockImplementation(
      () => {}
    )

    // Setup API to return fresh data after delay
    vi.spyOn(
      workoutApiModule.workoutApi,
      'getUserProgramInfo'
    ).mockImplementation(
      () =>
        new Promise((resolve) => {
          setTimeout(() => resolve(mockFreshWorkout), 100)
        })
    )
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockResolvedValue(
      []
    )

    const { result } = renderHook(() => useWorkout(), { wrapper })

    // Initial render might not show cached data immediately, wait for it
    await waitFor(
      () => {
        expect(result.current.todaysWorkout).toEqual(mockCachedWorkout)
      },
      { timeout: 100 }
    )

    expect(result.current.isLoadingWorkout).toBe(true) // Still loading fresh data

    // Wait for fresh data
    await waitFor(() => {
      expect(result.current.isLoadingWorkout).toBe(false)
    })

    // Should now show fresh data
    expect(result.current.todaysWorkout?.[0].Label).toBe('Fresh Program')

    // Should update cache with fresh data
    expect(workoutCacheModule.WorkoutCache.set).toHaveBeenCalled()
  })

  it('should work without cache when localStorage is not available', async () => {
    // Cache returns null (localStorage not available)
    vi.spyOn(workoutCacheModule.WorkoutCache, 'get').mockReturnValue(null)
    vi.spyOn(workoutCacheModule.WorkoutCache, 'set').mockImplementation(
      () => {}
    )

    vi.spyOn(
      workoutApiModule.workoutApi,
      'getUserProgramInfo'
    ).mockResolvedValue(mockFreshWorkout)
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockResolvedValue(
      []
    )

    const { result } = renderHook(() => useWorkout(), { wrapper })

    // Should start with no data
    expect(result.current.todaysWorkout).toBeNull()
    expect(result.current.isLoadingWorkout).toBe(true)

    // Wait for API data
    await waitFor(() => {
      expect(result.current.isLoadingWorkout).toBe(false)
    })

    // Should show API data
    expect(result.current.todaysWorkout?.[0].Label).toBe('Fresh Program')
  })

  it('should clear cache on logout', async () => {
    const clearSpy = vi.spyOn(workoutCacheModule.WorkoutCache, 'clear')

    // Start with authenticated state
    const authStoreMock = vi.mocked(
      useAuthStore as vi.MockedFunction<typeof useAuthStore>
    )
    authStoreMock.mockReturnValue({ isAuthenticated: true })

    const { rerender } = renderHook(() => useWorkout(), { wrapper })

    // Change to logged out state
    authStoreMock.mockReturnValue({ isAuthenticated: false })
    rerender()

    // Should clear cache on logout
    await waitFor(() => {
      expect(clearSpy).toHaveBeenCalled()
    })
  })

  it('should update cache after successful API fetch', async () => {
    vi.spyOn(workoutCacheModule.WorkoutCache, 'get').mockReturnValue(null)
    const setSpy = vi
      .spyOn(workoutCacheModule.WorkoutCache, 'set')
      .mockImplementation(() => {})

    vi.spyOn(
      workoutApiModule.workoutApi,
      'getUserProgramInfo'
    ).mockResolvedValue(mockFreshWorkout)
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockResolvedValue([
      {
        Id: 2,
        Label: 'Fresh Workout',
        Exercises: [{ Id: 2, Label: 'Fresh Exercise' }],
      },
    ])

    const { result } = renderHook(() => useWorkout(), { wrapper })

    await waitFor(() => {
      expect(result.current.isLoadingWorkout).toBe(false)
    })

    // Should cache the fetched data
    expect(setSpy).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          Label: 'Fresh Program',
          WorkoutTemplates: expect.arrayContaining([
            expect.objectContaining({
              Label: 'Fresh Workout',
            }),
          ]),
        }),
      ])
    )
  })

  it('should not update cache on API error', async () => {
    vi.spyOn(workoutCacheModule.WorkoutCache, 'get').mockReturnValue(
      mockCachedWorkout
    )
    const setSpy = vi
      .spyOn(workoutCacheModule.WorkoutCache, 'set')
      .mockImplementation(() => {})

    vi.spyOn(
      workoutApiModule.workoutApi,
      'getUserProgramInfo'
    ).mockRejectedValue(new Error('API Error'))
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockResolvedValue(
      []
    )

    const { result } = renderHook(() => useWorkout(), { wrapper })

    // Should show cached data
    expect(result.current.todaysWorkout).toEqual(mockCachedWorkout)

    await waitFor(() => {
      expect(result.current.workoutError).toBeTruthy()
    })

    // Should not update cache on error
    expect(setSpy).not.toHaveBeenCalled()
  })

  it('should handle race conditions between cache and API', async () => {
    // Start with no cache data
    vi.spyOn(workoutCacheModule.WorkoutCache, 'get').mockReturnValue(null)
    vi.spyOn(workoutCacheModule.WorkoutCache, 'set').mockImplementation(
      () => {}
    )

    // API resolves faster than cache
    vi.spyOn(
      workoutApiModule.workoutApi,
      'getUserProgramInfo'
    ).mockImplementation(
      () =>
        new Promise((resolve) => {
          setTimeout(() => resolve(mockFreshWorkout), 10)
        })
    )
    vi.spyOn(workoutApiModule.workoutApi, 'getUserWorkout').mockResolvedValue(
      []
    )

    const { result } = renderHook(() => useWorkout(), { wrapper })

    // Should start with no data
    expect(result.current.todaysWorkout).toBeNull()

    // Wait for API to resolve first
    await waitFor(() => {
      expect(result.current.todaysWorkout?.[0].Label).toBe('Fresh Program')
    })

    // Verify data remains fresh
    expect(result.current.todaysWorkout?.[0].Label).toBe('Fresh Program')
  })
})
