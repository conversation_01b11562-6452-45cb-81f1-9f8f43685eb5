import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useRIR } from '@/hooks/useRIR'
import { useWorkoutStore } from '@/stores/workoutStore'
import type { ExerciseModel } from '@/types'

// Mock the workout store
vi.mock('@/stores/workoutStore')

const mockExercise: ExerciseModel = {
  Id: 1,
  Name: 'Bench Press',
  TargetReps: 8,
  TargetWeight: { Mass: 100, MassUnit: 'lbs' },
  Path: 'chest/benchpress',
  IsWarmup: false,
  IsMedium: false,
  IsBodyweight: false,
  IsNormalSet: true,
  IsDeload: false,
  IsFinished: false,
  IsMediumFinished: false,
  IsEasy: false,
  IsFirstMedium: false,
  IsFirstEasy: false,
  BodyWeight: null,
  EquipmentId: 'barbell',
  TimeSinceLastSet: '00:00:00',
  PreviousExercise: null,
  SwapExerciseTargetPath: null,
  RecommendationInKgRange: null,
  FirstWorkSet: null,
  From1RM: null,
  OneRM: null,
  IsMultiUnity: false,
  IsRecommended: true,
  HasPastLogs: true,
  IsTimeBased: false,
  IsPlate: false,
}

describe('useRIR', () => {
  const mockGetCurrentExercise = vi.fn()
  const mockGetExerciseProgress = vi.fn()
  const mockUpdateSetRIR = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useWorkoutStore).mockReturnValue({
      getCurrentExercise: mockGetCurrentExercise,
      getExerciseProgress: mockGetExerciseProgress,
      updateSetRIR: mockUpdateSetRIR,
      currentExerciseIndex: 0,
      currentSetIndex: 0,
      workoutSession: {
        id: 'session-1',
        startTime: new Date(),
        exercises: [],
      },
    } as ReturnType<typeof useWorkoutStore>)
  })

  describe('RIR Detection', () => {
    it('should not show RIR for warmup sets', () => {
      // Given
      mockGetCurrentExercise.mockReturnValue({
        ...mockExercise,
        IsWarmup: true,
      })
      mockGetExerciseProgress.mockReturnValue({
        totalSets: 4,
        completedSets: 0,
        isFirstWorkSet: false,
        currentSetIsWarmup: true,
      })

      // When
      const { result } = renderHook(() => useRIR())

      // Then
      expect(result.current.shouldShowRIR).toBe(false)
    })

    it('should show RIR for first work set only', () => {
      // Given
      mockGetCurrentExercise.mockReturnValue(mockExercise)
      mockGetExerciseProgress.mockReturnValue({
        totalSets: 4,
        completedSets: 2, // 2 warmup sets done
        isFirstWorkSet: true,
        currentSetIsWarmup: false,
      })

      // When
      const { result } = renderHook(() => useRIR())

      // Then
      expect(result.current.shouldShowRIR).toBe(true)
    })

    it('should not show RIR for subsequent work sets', () => {
      // Given
      mockGetCurrentExercise.mockReturnValue(mockExercise)
      mockGetExerciseProgress.mockReturnValue({
        totalSets: 4,
        completedSets: 3, // 2 warmup + 1 work set done
        isFirstWorkSet: false,
        currentSetIsWarmup: false,
      })

      // When
      const { result } = renderHook(() => useRIR())

      // Then
      expect(result.current.shouldShowRIR).toBe(false)
    })

    it('should not show RIR if already captured for current exercise', () => {
      // Given
      mockGetCurrentExercise.mockReturnValue(mockExercise)
      mockGetExerciseProgress.mockReturnValue({
        totalSets: 4,
        completedSets: 2,
        isFirstWorkSet: true,
        currentSetIsWarmup: false,
        hasRIR: true, // RIR already captured
      })

      // When
      const { result } = renderHook(() => useRIR())

      // Then
      expect(result.current.shouldShowRIR).toBe(false)
    })
  })

  describe('RIR Value Mapping', () => {
    it('should map descriptive RIR to numeric values', () => {
      // When
      const { result } = renderHook(() => useRIR())

      // Then
      expect(result.current.getRIRValue('Very hard (0 left)')).toBe(0)
      expect(result.current.getRIRValue('Could do 1-2 more')).toBe(2)
      expect(result.current.getRIRValue('Could do 3-4 more')).toBe(4)
      expect(result.current.getRIRValue('Could do 5-6 more')).toBe(6)
      expect(result.current.getRIRValue('Could do 7+ more')).toBe(8)
    })

    it('should return undefined for invalid RIR description', () => {
      // When
      const { result } = renderHook(() => useRIR())

      // Then
      expect(result.current.getRIRValue('Invalid')).toBeUndefined()
    })
  })

  describe('RIR Options', () => {
    it('should provide all 5 RIR options', () => {
      // When
      const { result } = renderHook(() => useRIR())

      // Then
      expect(result.current.rirOptions).toHaveLength(5)
      expect(result.current.rirOptions).toEqual([
        { label: 'Very hard (0 left)', value: 0, color: 'red' },
        { label: 'Could do 1-2 more', value: 2, color: 'orange' },
        { label: 'Could do 3-4 more', value: 4, color: 'yellow' },
        { label: 'Could do 5-6 more', value: 6, color: 'blue' },
        { label: 'Could do 7+ more', value: 8, color: 'green' },
      ])
    })
  })

  describe('RIR Saving', () => {
    it('should save RIR value to workout store', async () => {
      // Given
      mockGetCurrentExercise.mockReturnValue(mockExercise)
      const { result } = renderHook(() => useRIR())

      // When
      await act(async () => {
        await result.current.saveRIR()
      })

      // Then
      expect(mockUpdateSetRIR).toHaveBeenCalledWith(1)
    })

    it('should handle save errors gracefully', async () => {
      // Given
      mockGetCurrentExercise.mockReturnValue(mockExercise)
      mockUpdateSetRIR.mockRejectedValue(new Error('Save failed'))
      const { result } = renderHook(() => useRIR())

      // When/Then - Should not throw
      await expect(
        act(async () => {
          await result.current.saveRIR()
        })
      ).resolves.not.toThrow()
    })
  })

  describe('Exercise Context', () => {
    it('should handle no current exercise', () => {
      // Given
      mockGetCurrentExercise.mockReturnValue(null)

      // When
      const { result } = renderHook(() => useRIR())

      // Then
      expect(result.current.shouldShowRIR).toBe(false)
      expect(result.current.currentExercise).toBeNull()
    })

    it('should update when exercise changes', () => {
      // Given
      mockGetCurrentExercise.mockReturnValue(mockExercise)
      const { result, rerender } = renderHook(() => useRIR())

      // Initial state
      expect(result.current.currentExercise?.Label).toBe('Bench Press')

      // When - Exercise changes
      const newExercise = { ...mockExercise, Id: 2, Label: 'Squat' }
      mockGetCurrentExercise.mockReturnValue(newExercise)
      rerender()

      // Then
      expect(result.current.currentExercise?.Label).toBe('Squat')
    })
  })

  describe('Time-Based Exercises', () => {
    it('should not show RIR for time-based exercises', () => {
      // Given
      mockGetCurrentExercise.mockReturnValue({
        ...mockExercise,
        IsTimeBased: true,
      })
      mockGetExerciseProgress.mockReturnValue({
        totalSets: 4,
        completedSets: 2,
        isFirstWorkSet: true,
        currentSetIsWarmup: false,
      })

      // When
      const { result } = renderHook(() => useRIR())

      // Then
      expect(result.current.shouldShowRIR).toBe(false)
    })
  })

  describe('Edge Cases', () => {
    it('should handle missing workout session', () => {
      // Given
      vi.mocked(useWorkoutStore).mockReturnValue({
        getCurrentExercise: mockGetCurrentExercise,
        getExerciseProgress: mockGetExerciseProgress,
        updateSetRIR: mockUpdateSetRIR,
        currentExerciseIndex: 0,
        currentSetIndex: 0,
        workoutSession: null, // No session
      } as ReturnType<typeof useWorkoutStore>)

      // When
      const { result } = renderHook(() => useRIR())

      // Then
      expect(result.current.shouldShowRIR).toBe(false)
    })

    it('should handle exercise with no sets', () => {
      // Given
      mockGetCurrentExercise.mockReturnValue(mockExercise)
      mockGetExerciseProgress.mockReturnValue({
        totalSets: 0,
        completedSets: 0,
        isFirstWorkSet: false,
        currentSetIsWarmup: false,
      })

      // When
      const { result } = renderHook(() => useRIR())

      // Then
      expect(result.current.shouldShowRIR).toBe(false)
    })
  })
})
