'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: 'default' | 'elevated' | 'outlined'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  interactive?: boolean
}

export function Card({
  children,
  variant = 'default',
  padding = 'md',
  interactive = false,
  className,
  ...props
}: CardProps) {
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  }

  const variantClasses = {
    default: 'bg-bg-secondary shadow-theme-sm',
    elevated: 'bg-bg-secondary shadow-theme-lg',
    outlined: 'bg-bg-primary border border-brand-primary/20',
  }

  return (
    <div
      className={cn(
        // Base styles
        'rounded-theme',
        paddingClasses[padding],
        variantClasses[variant],
        // Interactive states
        interactive && [
          'cursor-pointer',
          'transition-all duration-300',
          'hover:shadow-theme-xl',
          'hover:border-brand-primary/40',
          'active:scale-[0.99]',
        ],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}
