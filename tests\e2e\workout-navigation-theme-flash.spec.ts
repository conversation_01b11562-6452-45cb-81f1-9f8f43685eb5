import { test, expect } from '@playwright/test'
import { login } from './helpers'

test.describe('Workout Navigation Theme Flash', () => {
  test.beforeEach(async ({ page }) => {
    // Login first
    await login(page)

    // Navigate to program page
    await page.goto('/program')
    await page.waitForSelector('[data-testid="program-overview-page"]')

    // Wait for program data to load
    await page.waitForSelector('[data-testid="cta-container"]', {
      timeout: 10000,
    })
  })

  test('should not show white background flash when navigating to workout page', async ({
    page,
  }) => {
    // Set up route interception to monitor navigation
    let navigationStarted = false
    let whiteFlashDetected = false

    // Monitor page background color during navigation
    await page.addInitScript(() => {
      // Create a MutationObserver to watch for style changes
      const observer = new MutationObserver(() => {
        const bodyBgColor = window.getComputedStyle(
          document.body
        ).backgroundColor
        const htmlBgColor = window.getComputedStyle(
          document.documentElement
        ).backgroundColor

        // Check if background is white or close to white
        const isWhite = (color: string) => {
          return (
            color === 'white' ||
            color === 'rgb(255, 255, 255)' ||
            color === '#ffffff' ||
            color === '#fff' ||
            color === 'rgba(255, 255, 255, 1)'
          )
        }

        if (isWhite(bodyBgColor) || isWhite(htmlBgColor)) {
          // Store the white flash detection in window
          ;(window as any).__whiteFlashDetected = true
        }
      })

      // Start observing
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['style', 'class'],
        subtree: true,
        childList: true,
      })

      // Also check body when it's available
      if (document.body) {
        observer.observe(document.body, {
          attributes: true,
          attributeFilter: ['style', 'class'],
        })
      }
    })

    // Listen for navigation start
    page.on('framenavigated', () => {
      navigationStarted = true
    })

    // Click the "Open Workout" button
    await page.click('button:has-text("Open Workout")')

    // Wait for navigation to complete
    await page.waitForURL('/workout', { waitUntil: 'domcontentloaded' })

    // Check if white flash was detected
    whiteFlashDetected = await page.evaluate(() => {
      return (window as any).__whiteFlashDetected || false
    })

    // Verify navigation happened
    expect(navigationStarted).toBe(true)

    // Verify no white flash occurred
    expect(whiteFlashDetected).toBe(false)

    // Additional check: Verify the workout page has proper theme styling
    const bodyBgColor = await page.evaluate(() => {
      return window.getComputedStyle(document.body).backgroundColor
    })

    // Should have dark theme background (not white)
    expect(bodyBgColor).not.toBe('rgb(255, 255, 255)')
    expect(bodyBgColor).not.toBe('white')

    // Verify we're on the workout page
    await expect(page).toHaveURL('/workout')

    // Verify workout page content is visible with proper theme
    const workoutContainer = page
      .locator('[data-testid="workout-overview"]')
      .first()
    await expect(workoutContainer).toBeVisible({ timeout: 5000 })

    // Check that the container has the correct background
    const containerBgColor = await workoutContainer.evaluate((el) => {
      return window.getComputedStyle(el).backgroundColor
    })

    // Should not be white
    expect(containerBgColor).not.toBe('rgb(255, 255, 255)')
  })

  test('should maintain theme consistency during fast navigation', async ({
    page,
  }) => {
    // Perform multiple rapid navigations to test theme persistence
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < 3; i++) {
      // Navigate to workout
      // eslint-disable-next-line no-await-in-loop
      await page.click('button:has-text("Open Workout")')
      // eslint-disable-next-line no-await-in-loop
      await page.waitForURL('/workout')

      // Check background is not white
      // eslint-disable-next-line no-await-in-loop
      const bgColor = await page.evaluate(() => {
        return window.getComputedStyle(document.body).backgroundColor
      })
      expect(bgColor).not.toBe('rgb(255, 255, 255)')

      // Go back to program
      // eslint-disable-next-line no-await-in-loop
      await page.goBack()
      // eslint-disable-next-line no-await-in-loop
      await page.waitForURL('/program')
    }
  })

  test('should apply theme styles before page content renders', async ({
    page,
  }) => {
    // Intercept the navigation request
    await page.route('/workout', async (route) => {
      // Check styles before continuing
      const styles = await page.evaluate(() => {
        return {
          bodyBg: window.getComputedStyle(document.body).backgroundColor,
          themeCssVar: getComputedStyle(
            document.documentElement
          ).getPropertyValue('--color-bg-primary'),
        }
      })

      // Continue the request
      await route.continue()

      // Verify theme was already applied
      expect(styles.themeCssVar).toBeTruthy()
      expect(styles.bodyBg).not.toBe('rgb(255, 255, 255)')
    })

    // Trigger navigation
    await page.click('button:has-text("Open Workout")')
    await page.waitForURL('/workout')
  })
})
