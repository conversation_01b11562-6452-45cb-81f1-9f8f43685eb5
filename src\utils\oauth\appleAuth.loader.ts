/**
 * Apple OAuth SDK Loader
 * Handles loading and initialization of Apple Sign-In SDK
 */

import { AppleAuthConfig } from './appleAuth.config'
// import { PerformanceMonitor } from '../performance'

export class AppleAuthLoader {
  private static isInitialized = false

  private static isLoading = false

  private static loadPromise: Promise<void> | null = null

  private static authConfig: AppleID.auth.ClientConfigObject | null = null

  /**
   * Load Apple Sign-In SDK dynamically
   */
  static async loadAppleSDK(): Promise<void> {
    // If already loading, return the existing promise
    if (this.loadPromise) {
      return this.loadPromise
    }

    // If already loaded and window.AppleID exists, resolve immediately
    if (typeof window !== 'undefined' && window.AppleID && this.isInitialized) {
      return Promise.resolve()
    }

    this.isLoading = true

    this.loadPromise = new Promise<void>((resolve, reject) => {
      if (typeof window === 'undefined') {
        this.isLoading = false
        reject(new Error('Window is not defined'))
        return
      }

      const script = document.createElement('script')
      script.src = AppleAuthConfig.APPLE_SDK_URL
      script.async = true
      script.defer = true

      script.onload = () => {
        console.error('[Apple OAuth] SDK loaded successfully')
        this.isLoading = false
        resolve()
      }

      script.onerror = (error) => {
        console.error('[Apple OAuth] Failed to load SDK:', error)
        this.isLoading = false
        reject(new Error('Failed to load Apple Sign-In SDK'))
      }

      document.head.appendChild(script)
    })

    return this.loadPromise
  }

  /**
   * Initialize Apple Sign-In
   */
  static async initialize(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _onSuccess: (response: AppleID.auth.SignInResponse) => void,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _onError: (error: AppleID.auth.SignInError) => void
  ): Promise<void> {
    console.error('[Apple OAuth] Initializing')

    try {
      // Load SDK if not already loaded
      await this.loadAppleSDK()

      if (!window.AppleID) {
        throw new Error('AppleID is not available on window')
      }

      const state = AppleAuthConfig.generateState()
      const nonce = AppleAuthConfig.generateNonce()

      // Store state for verification
      sessionStorage.setItem('apple_oauth_state', state)
      sessionStorage.setItem('apple_oauth_nonce', nonce)

      this.authConfig = {
        clientId: AppleAuthConfig.getClientId(),
        redirectURI: AppleAuthConfig.getRedirectUri(),
        scope: 'name email',
        state,
        nonce,
        usePopup: true,
      }

      console.error('[Apple OAuth] Config:', {
        ...this.authConfig,
        nonce: 'hidden',
      })

      window.AppleID.auth.init(this.authConfig)

      this.isInitialized = true
      console.error('[Apple OAuth] Initialization complete')
    } catch (error) {
      console.error('[Apple OAuth] Initialization failed:', error)
      throw error
    }
  }

  /**
   * Get initialization state
   */
  static getState() {
    return {
      isInitialized: this.isInitialized,
      isLoading: this.isLoading,
      authConfig: this.authConfig,
    }
  }

  /**
   * Check if SDK is available
   */
  static isAvailable(): boolean {
    return typeof window !== 'undefined' && !!window.AppleID?.auth
  }

  /**
   * Check if ready to sign in
   */
  static isReady(): boolean {
    return this.isInitialized && this.isAvailable()
  }
}

// Type augmentation for window.AppleID
// Type definitions are in global.d.ts
