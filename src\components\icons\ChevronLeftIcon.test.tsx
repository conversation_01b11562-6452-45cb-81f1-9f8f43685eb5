import { describe, it, expect, vi } from 'vitest'
import { render } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ChevronLeftIcon } from './ChevronLeftIcon'

describe('ChevronLeftIcon', () => {
  it('renders with default size', () => {
    const { container } = render(<ChevronLeftIcon />)
    const svg = container.querySelector('svg')

    expect(svg).toBeTruthy()
    expect(svg?.getAttribute('width')).toBe('24')
    expect(svg?.getAttribute('height')).toBe('24')
  })

  it('renders with custom size', () => {
    const { container } = render(<ChevronLeftIcon size={32} />)
    const svg = container.querySelector('svg')

    expect(svg?.getAttribute('width')).toBe('32')
    expect(svg?.getAttribute('height')).toBe('32')
  })

  it('applies custom className', () => {
    const { container } = render(<ChevronLeftIcon className="text-blue-500" />)
    const svg = container.querySelector('svg')

    expect(svg?.classList.contains('text-blue-500')).toBe(true)
  })

  it('renders iOS-style chevron path', () => {
    const { container } = render(<ChevronLeftIcon />)
    const path = container.querySelector('path')

    expect(path?.getAttribute('d')).toBe('M15 18l-6-6 6-6')
  })

  it('has proper stroke attributes for iOS style', () => {
    const { container } = render(<ChevronLeftIcon />)
    const svg = container.querySelector('svg')

    expect(svg?.getAttribute('stroke')).toBe('currentColor')
    expect(svg?.getAttribute('stroke-width')).toBe('2')
    expect(svg?.getAttribute('stroke-linecap')).toBe('round')
    expect(svg?.getAttribute('stroke-linejoin')).toBe('round')
    expect(svg?.getAttribute('fill')).toBe('none')
  })

  it('handles onClick when provided', async () => {
    const handleClick = vi.fn()
    const { container } = render(<ChevronLeftIcon onClick={handleClick} />)
    const svg = container.querySelector('svg')

    await userEvent.click(svg!)

    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('has proper accessibility attributes', () => {
    const { container } = render(<ChevronLeftIcon />)
    const svg = container.querySelector('svg')

    expect(svg?.getAttribute('aria-hidden')).toBe('true')
  })
})
