import { test, expect } from '@playwright/test'

test.describe('Finish Workout Button', () => {
  // Use mobile viewport
  test.use({
    viewport: { width: 390, height: 844 },
    userAgent:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
  })

  test.beforeEach(async ({ page }) => {
    // Navigate to login and authenticate
    await page.goto('/')
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.locator('input[type="password"]').fill('Dr123456')
    await page.getByRole('button', { name: /sign in/i }).click()

    // Wait for navigation to complete
    await page.waitForURL('/program', { timeout: 30000 })
  })

  test('should change from Continue Workout to Finish workout after saving sets', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Start workout by clicking the button
    const startButton = page.getByRole('button', {
      name: /Start a new workout session/i,
    })
    await expect(startButton).toBeVisible()
    await startButton.click()

    // Click on first exercise
    const firstExercise = page.getByTestId('exercise-card').first()
    await expect(firstExercise).toBeVisible()
    await firstExercise.click()

    // Wait for exercise page to load
    await page.waitForURL(/\/workout\/exercise\/\d+/)

    // Fill in weight and reps
    const weightInput = page.getByLabel(/weight/i)
    const repsInput = page.getByLabel(/reps/i)

    await weightInput.fill('135')
    await repsInput.fill('10')

    // Save the set
    const saveButton = page.getByRole('button', { name: /save set/i })
    await saveButton.click()

    // Navigate back to workout overview
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Verify button now shows "Finish and save workout"
    const finishButton = page.getByRole('button', {
      name: /Finish and save workout/i,
    })
    await expect(finishButton).toBeVisible()
    await expect(finishButton).toHaveText('Finish and save workout')
  })

  test('should show Continue Workout when no sets are saved', async ({
    page,
  }) => {
    // Navigate to workout page
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Start workout
    const startButton = page.getByRole('button', {
      name: /Start a new workout session/i,
    })
    await expect(startButton).toBeVisible()
    await startButton.click()

    // Click on first exercise
    const firstExercise = page.getByTestId('exercise-card').first()
    await expect(firstExercise).toBeVisible()
    await firstExercise.click()

    // Navigate back without saving any sets
    await page.goto('/workout')
    await page.waitForLoadState('networkidle')

    // Verify button shows "Continue Workout"
    const continueButton = page.getByRole('button', {
      name: /Continue your current workout/i,
    })
    await expect(continueButton).toBeVisible()
    await expect(continueButton).toHaveText('Continue Workout')
  })
})
