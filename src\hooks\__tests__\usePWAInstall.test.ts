import { describe, it, expect, vi, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { usePWAInstall } from '../usePWAInstall'

// Mock haptic utility
vi.mock('@/utils/haptic', () => ({
  triggerHaptic: vi.fn(),
}))

describe('usePWAInstall', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()

    // Mock window methods
    global.window.addEventListener = vi.fn()
    global.window.removeEventListener = vi.fn()
    global.window.matchMedia = vi.fn(() => ({
      matches: false,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    })) as any
  })

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => usePWAInstall())

    expect(result.current.isInstallable).toBe(false)
    expect(result.current.isInstalling).toBe(false)
    expect(result.current.isInstalled).toBe(false)
    expect(result.current.canInstall).toBe(false)
    expect(typeof result.current.installPrompt).toBe('function')
    expect(typeof result.current.checkInstallStatus).toBe('function')
  })

  it('should detect if app is already installed', () => {
    // Mock standalone mode
    global.window.matchMedia = vi.fn(() => ({
      matches: true,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    })) as any

    const { result } = renderHook(() => usePWAInstall())

    expect(result.current.isInstalled).toBe(true)
  })

  it('should set up event listeners on mount', () => {
    renderHook(() => usePWAInstall())

    expect(window.addEventListener).toHaveBeenCalledWith(
      'beforeinstallprompt',
      expect.any(Function)
    )
    expect(window.addEventListener).toHaveBeenCalledWith(
      'appinstalled',
      expect.any(Function)
    )
  })

  it('should return false when no prompt is available', async () => {
    const { result } = renderHook(() => usePWAInstall())

    const installResult = await act(async () => {
      return result.current.installPrompt()
    })

    expect(installResult).toBe(false)
  })

  it('should handle beforeinstallprompt event correctly', () => {
    const { result } = renderHook(() => usePWAInstall())

    // Get the event handler that was registered
    const { calls } = (window.addEventListener as any).mock
    const beforeInstallPromptCall = calls.find(
      (call: any) => call[0] === 'beforeinstallprompt'
    )

    expect(beforeInstallPromptCall).toBeTruthy()

    if (beforeInstallPromptCall) {
      const eventHandler = beforeInstallPromptCall[1]
      const mockEvent = {
        preventDefault: vi.fn(),
        prompt: vi.fn(),
        userChoice: Promise.resolve({ outcome: 'accepted' }),
      }

      // Simulate the event
      act(() => {
        eventHandler(mockEvent)
      })

      expect(mockEvent.preventDefault).toHaveBeenCalled()
      expect(result.current.isInstallable).toBe(true)
      expect(result.current.canInstall).toBe(true)
    }
  })

  it('should clean up event listeners on unmount', () => {
    const { unmount } = renderHook(() => usePWAInstall())

    unmount()

    expect(window.removeEventListener).toHaveBeenCalledWith(
      'beforeinstallprompt',
      expect.any(Function)
    )
    expect(window.removeEventListener).toHaveBeenCalledWith(
      'appinstalled',
      expect.any(Function)
    )
  })
})
