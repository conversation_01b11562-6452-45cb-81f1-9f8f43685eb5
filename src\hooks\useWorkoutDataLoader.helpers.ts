import type { WorkoutTemplateGroupModel, ExerciseModel } from '@/types'
import { logger } from '@/utils/logger'

/**
 * Extract exercises from workout groups
 * Handles both 'Exercises' and 'Exercices' field names due to API localization
 */
export function extractExercisesFromWorkoutGroups(
  workoutGroups: WorkoutTemplateGroupModel[] | null
): ExerciseModel[] {
  if (!workoutGroups || workoutGroups.length === 0) {
    logger.log('[extractExercisesFromWorkoutGroups] No groups, returning []')
    return []
  }

  const firstGroup = workoutGroups[0]
  if (
    !firstGroup ||
    !firstGroup.WorkoutTemplates ||
    firstGroup.WorkoutTemplates.length === 0
  ) {
    logger.log('[extractExercisesFromWorkoutGroups] No templates, returning []')
    return []
  }

  const firstTemplate = firstGroup.WorkoutTemplates[0]
  // Check both 'Exercises' (English) and 'Exercices' (French) field names
  // The API might return either field name due to localization
  interface WorkoutTemplateWithExercices
    extends Omit<typeof firstTemplate, 'Exercises'> {
    Exercises?: ExerciseModel[]
    Exercices?: ExerciseModel[]
  }
  const exercises =
    firstTemplate?.Exercises ||
    (firstTemplate as WorkoutTemplateWithExercices)?.Exercices ||
    []

  logger.log('[extractExercisesFromWorkoutGroups] Result:', {
    hasExercises: !!firstTemplate?.Exercises,
    hasExercices: !!(firstTemplate as WorkoutTemplateWithExercices)?.Exercices,
    exerciseCount: exercises.length,
    firstExercise: exercises[0]?.Label || 'none',
  })

  return exercises
}
