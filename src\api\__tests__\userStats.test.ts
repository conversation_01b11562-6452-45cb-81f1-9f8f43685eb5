import { describe, it, expect, beforeEach, vi } from 'vitest'
import { fetchUserStats, extractUserStats } from '../userStats'
import { apiClient } from '../client'
import { UserStats } from '@/types/userStats'

// Mock the API client
vi.mock('../client', () => ({
  apiClient: {
    post: vi.fn(),
  },
}))

// Mock the apiRetry utility
vi.mock('@/utils/apiRetry', () => ({
  retryApiCall: vi.fn((fn) => fn()),
  STATS_RETRY_OPTIONS: {},
}))

// Mock logger
vi.mock('@/utils/logger', () => ({
  logger: {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}))

describe('userStats API', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('fetchUserStats', () => {
    it('should fetch and return user stats successfully', async () => {
      const mockTimezoneResponse = {
        data: {
          // Timezone response data
        },
      }

      const mockStatsResponse = {
        data: {
          HistoryExerciseModel: {
            TotalWorkoutCompleted: 25,
            TotalWeight: {
              Lb: 10500.5,
              Kg: 4763.0,
            },
            ConsecutiveWeeks: 5,
          },
        },
      }

      // Mock first call (timezone)
      vi.mocked(apiClient.post).mockResolvedValueOnce(mockTimezoneResponse)
      // Mock second call (stats)
      vi.mocked(apiClient.post).mockResolvedValueOnce(mockStatsResponse)

      const result = await fetchUserStats()

      expect(result).toEqual<UserStats>({
        weekStreak: 5,
        workoutsCompleted: 25,
        lbsLifted: 10501,
      })

      // Check first call
      expect(apiClient.post).toHaveBeenNthCalledWith(
        1,
        '/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
        expect.objectContaining({
          standardName: expect.any(String),
        })
      )

      // Check second call
      expect(apiClient.post).toHaveBeenNthCalledWith(
        2,
        '/api/WorkoutLog/GetLogAverageWithSetsV2',
        {}
      )
    })

    it('should handle API errors', async () => {
      const mockError = new Error('Network error')
      // Mock first call to succeed
      vi.mocked(apiClient.post).mockResolvedValueOnce({ data: {} })
      // Mock second call to fail
      vi.mocked(apiClient.post).mockRejectedValueOnce(mockError)
      // Mock fallback call to also fail
      vi.mocked(apiClient.post).mockRejectedValueOnce(mockError)

      await expect(fetchUserStats()).rejects.toThrow('Network error')
    })

    it('should handle empty response', async () => {
      const mockTimezoneResponse = {
        data: {},
      }

      const mockEmptyResponse = {
        data: {},
      }

      // Mock first call (timezone)
      vi.mocked(apiClient.post).mockResolvedValueOnce(mockTimezoneResponse)
      // Mock second call (stats) with empty data
      vi.mocked(apiClient.post).mockResolvedValueOnce(mockEmptyResponse)

      const result = await fetchUserStats()

      expect(result).toEqual<UserStats>({
        weekStreak: 0,
        workoutsCompleted: 0,
        lbsLifted: 0,
      })
    })

    it('should fallback to original API when new pattern fails', async () => {
      const mockFallbackResponse = {
        data: {
          ConsecutiveWeeks: 3,
          WorkoutCount: 15,
          HistoryExerciseModel: {
            TotalWeight: {
              Lb: 5000,
            },
          },
        },
      }

      // Mock first call to succeed
      vi.mocked(apiClient.post).mockResolvedValueOnce({ data: {} })
      // Mock second call to fail
      vi.mocked(apiClient.post).mockRejectedValueOnce(new Error('API Error'))
      // Mock fallback call to succeed
      vi.mocked(apiClient.post).mockResolvedValueOnce(mockFallbackResponse)

      const result = await fetchUserStats()

      expect(result).toEqual<UserStats>({
        weekStreak: 3,
        workoutsCompleted: 15,
        lbsLifted: 5000,
      })

      // Should have called the fallback endpoint
      expect(apiClient.post).toHaveBeenLastCalledWith(
        '/api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2',
        expect.objectContaining({
          TimeZoneId: expect.any(String),
          Offset: expect.any(Number),
          IsDaylightSaving: expect.any(Boolean),
        })
      )
    })

    it('should handle first call failure gracefully', async () => {
      const mockStatsResponse = {
        data: {
          HistoryExerciseModel: {
            TotalWorkoutCompleted: 20,
            TotalWeight: {
              Lb: 8000,
            },
            ConsecutiveWeeks: 4,
          },
        },
      }

      // Mock first call to fail (but continue)
      vi.mocked(apiClient.post).mockRejectedValueOnce(
        new Error('Timezone API Error')
      )
      // Mock second call to succeed
      vi.mocked(apiClient.post).mockResolvedValueOnce(mockStatsResponse)

      const result = await fetchUserStats()

      expect(result).toEqual<UserStats>({
        weekStreak: 4,
        workoutsCompleted: 20,
        lbsLifted: 8000,
      })
    })
  })

  describe('extractUserStats', () => {
    it('should extract stats from standard response format', () => {
      const data = {
        ConsecutiveWeeks: 5,
        WorkoutCount: 25,
        HistoryExerciseModel: {
          TotalWeight: {
            Lb: 10500,
          },
        },
      }

      const result = extractUserStats(data)

      expect(result).toEqual<UserStats>({
        weekStreak: 5,
        workoutsCompleted: 25,
        lbsLifted: 10500,
      })
    })

    it('should handle ConsecutiveWeeks as array of objects', () => {
      const data = {
        ConsecutiveWeeks: [{ ConsecutiveWeeks: 8 }, { ConsecutiveWeeks: 5 }],
        WorkoutCount: 30,
        HistoryExerciseModel: {
          TotalWeight: { Lb: 15000 },
        },
      }

      const result = extractUserStats(data)

      expect(result).toEqual<UserStats>({
        weekStreak: 8,
        workoutsCompleted: 30,
        lbsLifted: 15000,
      })
    })

    it('should handle ConsecutiveWeeksModel format', () => {
      const data = {
        ConsecutiveWeeksModel: {
          ConsecutiveWeeks: 12,
        },
        WorkoutCount: 50,
        HistoryExerciseModel: {
          TotalWeight: { Lb: 25000 },
        },
      }

      const result = extractUserStats(data)

      expect(result).toEqual<UserStats>({
        weekStreak: 12,
        workoutsCompleted: 50,
        lbsLifted: 25000,
      })
    })

    it('should handle HistoryExerciseModel as array', () => {
      const data = {
        ConsecutiveWeeks: 3,
        WorkoutCount: 15,
        HistoryExerciseModel: [
          {
            TotalWeight: { Lb: 8000 },
          },
          {
            TotalWeight: { Lb: 5000 },
          },
        ],
      }

      const result = extractUserStats(data)

      expect(result).toEqual<UserStats>({
        weekStreak: 3,
        workoutsCompleted: 15,
        lbsLifted: 8000, // Takes first element
      })
    })

    it('should fallback to TotalWorkoutCompleted if WorkoutCount missing', () => {
      const data = {
        ConsecutiveWeeks: 5,
        TotalWorkoutCompleted: 20,
        HistoryExerciseModel: {
          TotalWeight: { Lb: 9000 },
        },
      }

      const result = extractUserStats(data)

      expect(result).toEqual<UserStats>({
        weekStreak: 5,
        workoutsCompleted: 20,
        lbsLifted: 9000,
      })
    })

    it('should check Averages object for total weight', () => {
      const data = {
        ConsecutiveWeeks: 4,
        WorkoutCount: 18,
        Averages: {
          TotalWeight: 12000,
        },
      }

      const result = extractUserStats(data)

      expect(result).toEqual<UserStats>({
        weekStreak: 4,
        workoutsCompleted: 18,
        lbsLifted: 12000,
      })
    })

    it('should check multiple fields in Averages object', () => {
      const data = {
        ConsecutiveWeeks: 6,
        WorkoutCount: 22,
        Averages: {
          TotalVolume: 13500,
        },
      }

      const result = extractUserStats(data)

      expect(result).toEqual<UserStats>({
        weekStreak: 6,
        workoutsCompleted: 22,
        lbsLifted: 13500,
      })
    })

    it('should handle all missing data', () => {
      const data = {}

      const result = extractUserStats(data)

      expect(result).toEqual<UserStats>({
        weekStreak: 0,
        workoutsCompleted: 0,
        lbsLifted: 0,
      })
    })

    it('should handle malformed data gracefully', () => {
      const data = {
        ConsecutiveWeeks: 'invalid',
        WorkoutCount: 'not a number',
        HistoryExerciseModel: {
          TotalWeight: {
            Lb: 'not a number',
          },
        },
      }

      const result = extractUserStats(data)

      expect(result).toEqual<UserStats>({
        weekStreak: 0,
        workoutsCompleted: 0,
        lbsLifted: 0,
      })
    })

    it('should round lbs lifted to nearest integer', () => {
      const data = {
        ConsecutiveWeeks: 5,
        WorkoutCount: 25,
        HistoryExerciseModel: {
          TotalWeight: {
            Lb: 10500.7,
          },
        },
      }

      const result = extractUserStats(data)

      expect(result.lbsLifted).toBe(10501)
    })
  })
})
