import { describe, it, expect, vi, beforeEach } from 'vitest'
import { apiClient } from '@/api/client'
import { getTodaysWorkout } from '../read'
import type { WorkoutTemplateGroupModel } from '@/types'

vi.mock('@/api/client')
vi.mock('@/lib/auth-utils', () => ({
  getCurrentUserEmail: () => '<EMAIL>',
}))

describe('getTodaysWorkout', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should handle wrapped response with Data property', async () => {
    const mockWorkoutGroups: WorkoutTemplateGroupModel[] = [
      {
        Id: 1,
        Label: "Today's Workout",
        WorkoutTemplates: [
          {
            Id: 101,
            UserId: '<EMAIL>',
            Label: 'Upper Body A',
            Exercises: [
              {
                Id: 1001,
                Label: 'Bench Press',
                Sets: 3,
                Reps: [8, 8, 8],
                Weight: { Lb: 135, Kg: 61 },
                Rest: 180,
                Validated: false,
              },
            ],
            IsSystemExercise: false,
            WorkoutSettingsModel: {
              Id: 1,
              LightSessionDays: [],
              MediumSessionDays: [],
              NbDaysInCycle: 7,
              PercentageLightSession: 80,
              PercentageMediumSession: 90,
            },
          },
        ],
        IsFeaturedProgram: false,
        UserId: '<EMAIL>',
        IsSystemExercise: false,
        RequiredWorkoutToLevelUp: 0,
        ProgramId: 1,
      },
    ]

    // Mock API response wrapped in Data property
    vi.mocked(apiClient.post).mockResolvedValueOnce({
      data: {
        Data: mockWorkoutGroups,
      },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    })

    const result = await getTodaysWorkout()

    expect(apiClient.post).toHaveBeenCalledWith(
      '/api/Workout/GetUserWorkoutTemplateGroup',
      {}
    )
    expect(result).toEqual(mockWorkoutGroups)
  })

  it('should handle direct array response', async () => {
    const mockWorkoutGroups: WorkoutTemplateGroupModel[] = [
      {
        Id: 1,
        Label: "Today's Workout",
        WorkoutTemplates: [],
        IsFeaturedProgram: false,
        UserId: '<EMAIL>',
        IsSystemExercise: false,
        RequiredWorkoutToLevelUp: 0,
        ProgramId: 1,
      },
    ]

    // Mock API response as direct array
    vi.mocked(apiClient.post).mockResolvedValueOnce({
      data: mockWorkoutGroups,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    })

    const result = await getTodaysWorkout()

    expect(result).toEqual(mockWorkoutGroups)
  })

  it('should handle response with StatusCode and Result wrapper', async () => {
    const mockWorkoutGroups: WorkoutTemplateGroupModel[] = [
      {
        Id: 1,
        Label: "Today's Workout",
        WorkoutTemplates: [],
        IsFeaturedProgram: false,
        UserId: '<EMAIL>',
        IsSystemExercise: false,
        RequiredWorkoutToLevelUp: 0,
        ProgramId: 1,
      },
    ]

    // Mock API response with StatusCode/Result wrapper
    vi.mocked(apiClient.post).mockResolvedValueOnce({
      data: {
        StatusCode: 200,
        Result: mockWorkoutGroups,
        ErrorMessage: null,
      },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    })

    const result = await getTodaysWorkout()

    expect(result).toEqual(mockWorkoutGroups)
  })

  it('should fall back to getUserWorkout when GetUserWorkoutTemplateGroup fails', async () => {
    // Mock GetUserWorkoutTemplateGroup to fail
    vi.mocked(apiClient.post).mockRejectedValueOnce(
      new Error('GetUserWorkoutTemplateGroup failed')
    )

    // Mock getUserWorkout response
    vi.mocked(apiClient.post).mockResolvedValueOnce({
      data: {
        StatusCode: 200,
        Result: [
          {
            Id: 101,
            UserId: '<EMAIL>',
            Label: 'Upper Body A',
            Exercises: [],
            IsSystemExercise: false,
            WorkoutSettingsModel: {
              Id: 1,
              LightSessionDays: [],
              MediumSessionDays: [],
              NbDaysInCycle: 7,
              PercentageLightSession: 80,
              PercentageMediumSession: 90,
            },
          },
        ],
      },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    })

    const result = await getTodaysWorkout()

    // Should call GetUserWorkout as fallback
    expect(apiClient.post).toHaveBeenCalledWith(
      '/api/Workout/GetUserWorkout',
      {},
      expect.any(Object)
    )

    // Result should be wrapped in a template group
    expect(result).toHaveLength(1)
    expect(result[0].Label).toBe("Today's Workout")
    expect(result[0].WorkoutTemplates).toHaveLength(1)
  })

  it('should handle empty or null response gracefully', async () => {
    // Mock API response with null data
    vi.mocked(apiClient.post).mockResolvedValueOnce({
      data: null,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    })

    // Fall back to getUserWorkout which also returns empty
    vi.mocked(apiClient.post).mockResolvedValueOnce({
      data: {
        StatusCode: 200,
        Result: [],
      },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    })

    const result = await getTodaysWorkout()

    // Should return wrapped empty array
    expect(result).toEqual([
      {
        Id: 1,
        Label: "Today's Workout",
        WorkoutTemplates: [],
        IsFeaturedProgram: false,
        UserId: '<EMAIL>',
        IsSystemExercise: false,
        RequiredWorkoutToLevelUp: 0,
        ProgramId: 1,
      },
    ])
  })
})
