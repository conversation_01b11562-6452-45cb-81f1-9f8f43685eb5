import React from 'react'
import { useComponentPerformance } from '@/hooks/useComponentPerformance'
import { useCounterAnimation } from '@/hooks/useCounterAnimation'
import { formatCounterNumber } from '@/utils/numberFormatting'
import { CounterDisplay } from './ui/CounterDisplay'
import { CounterLabel } from './ui/CounterLabel'
import { ShimmerOverlay } from './ui/ShimmerEffect'

export interface AnimatedCounterProps {
  /** Target value to animate to */
  targetValue: number | null | undefined
  /** Label to display below the counter */
  label?: string
  /** Size variant */
  size?: 'small' | 'medium' | 'large'
  /** Duration of animation in milliseconds */
  duration?: number
  /** Loading state */
  isLoading?: boolean
  /** Show shimmer effect overlay */
  showShimmer?: boolean
  /** Offset delay for staggered shimmer animations (ms) */
  shimmerOffset?: number
  /** Optional icon to display */
  icon?: React.ReactNode
  /** Additional CSS classes */
  className?: string
}

function getSizeClass(size: 'small' | 'medium' | 'large'): string {
  switch (size) {
    case 'small':
      return 'text-2xl'
    case 'medium':
      return 'text-4xl'
    default:
      return 'text-6xl'
  }
}

export function AnimatedCounter({
  targetValue,
  label,
  size = 'large',
  duration = 400,
  isLoading = false,
  showShimmer = false,
  shimmerOffset = 0,
  icon,
  className = '',
}: AnimatedCounterProps) {
  // Track component performance
  useComponentPerformance('AnimatedCounter')

  // Debug logging in development
  if (process.env.NODE_ENV === 'development' && label) {
    // eslint-disable-next-line no-console
    console.log(`[AnimatedCounter] ${label}:`, { targetValue, isLoading })
  }

  const { displayValue, hasAnimated, showGlow, hasStarted } =
    useCounterAnimation({
      targetValue,
      duration,
      label,
      showShimmer,
    })

  if (isLoading) {
    return (
      <div
        data-testid="animated-counter"
        className={`text-center ${className}`}
        role="status"
        aria-busy="true"
        aria-label={`Loading ${label || 'value'}`}
      >
        <div className="relative inline-block">
          <div
            data-testid="animated-counter-value"
            className={`
              ${getSizeClass(size)} 
              font-bold 
              text-text-tertiary
              tabular-nums 
              relative
              overflow-hidden
              min-w-[8rem]
            `}
            aria-hidden="true"
          >
            0
            <ShimmerOverlay show delay={shimmerOffset} className="rounded" />
          </div>
        </div>
        {label && <CounterLabel label={label} icon={icon} size={size} />}
        <span className="sr-only">Loading {label || 'value'}</span>
      </div>
    )
  }

  // Calculate shimmer visibility
  const shouldShowShimmer =
    showShimmer && !hasStarted && (targetValue ?? 0) === 0

  return (
    <div
      data-testid="animated-counter"
      className={`text-center ${className}`}
      role="group"
      aria-label={label || 'Counter'}
    >
      <CounterDisplay
        value={formatCounterNumber(displayValue, label)}
        size={size}
        hasAnimated={hasAnimated}
        showGlow={showGlow}
        showShimmer={shouldShowShimmer}
        shimmerOffset={shimmerOffset}
        label={label}
      />
      {label && <CounterLabel label={label} icon={icon} size={size} />}
    </div>
  )
}
