/**
 * Font Loading Optimization Utilities
 * Monitors font loading and provides performance metrics
 */

export interface FontLoadMetrics {
  fontFamily: string
  loadTime: number
  status: 'loading' | 'loaded' | 'failed'
  timestamp: number
}

class FontLoader {
  private metrics: Map<string, FontLoadMetrics> = new Map()

  private startTime: number = Date.now()

  /**
   * Monitor font loading using FontFaceSet API
   */
  async monitorFontLoading(): Promise<void> {
    if (typeof window === 'undefined' || !document.fonts) {
      return
    }

    // Track initial font status
    const fontFamilies = ['Inter', 'Space Grotesk']

    fontFamilies.forEach((family) => {
      this.metrics.set(family, {
        fontFamily: family,
        loadTime: 0,
        status: 'loading',
        timestamp: Date.now(),
      })
    })

    // Monitor font loading
    try {
      await document.fonts.ready

      // Update metrics for loaded fonts
      fontFamilies.forEach((family) => {
        const metric = this.metrics.get(family)
        if (metric) {
          metric.status = 'loaded'
          metric.loadTime = Date.now() - metric.timestamp
        }
      })

      // Report performance metrics in development
      if (process.env.NODE_ENV === 'development') {
        this.reportMetrics()
      }
    } catch (error) {
      console.error('Font loading error:', error)

      // Mark fonts as failed
      fontFamilies.forEach((family) => {
        const metric = this.metrics.get(family)
        if (metric) {
          metric.status = 'failed'
        }
      })
    }
  }

  /**
   * Check if specific font is loaded
   */
  // eslint-disable-next-line class-methods-use-this
  isFontLoaded(fontFamily: string): boolean {
    if (typeof window === 'undefined' || !document.fonts) {
      return false
    }

    return document.fonts.check(`16px "${fontFamily}"`)
  }

  /**
   * Wait for specific font to load with timeout
   */
  async waitForFont(fontFamily: string, timeout = 3000): Promise<boolean> {
    if (typeof window === 'undefined' || !document.fonts) {
      return false
    }

    const startTime = Date.now()

    while (Date.now() - startTime < timeout) {
      if (this.isFontLoaded(fontFamily)) {
        return true
      }
      // eslint-disable-next-line no-await-in-loop
      await new Promise<void>((resolve) => {
        setTimeout(resolve, 50)
      })
    }

    return false
  }

  /**
   * Get font loading metrics
   */
  getMetrics(): FontLoadMetrics[] {
    return Array.from(this.metrics.values())
  }

  /**
   * Report font loading performance
   */
  private reportMetrics(): void {
    const metrics = this.getMetrics()
    const totalLoadTime = Date.now() - this.startTime

    // eslint-disable-next-line no-console
    console.group('Font Loading Performance')
    // eslint-disable-next-line no-console
    console.log(`Total time: ${totalLoadTime}ms`)

    metrics.forEach((metric) => {
      // eslint-disable-next-line no-console
      console.log(
        `${metric.fontFamily}: ${metric.status} (${metric.loadTime}ms)`
      )
    })

    // eslint-disable-next-line no-console
    console.groupEnd()
  }

  /**
   * Preload critical fonts
   */
  static preloadFonts(): void {
    if (typeof window === 'undefined') {
      return
    }

    // Critical font files that should be preloaded
    const criticalFonts = [
      // Inter Regular - most common weight
      'https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2',
      // Space Grotesk Medium - for headings
      'https://fonts.gstatic.com/s/spacegrotesk/v16/V8mDoQDjQSkFtoMM3T6r8E7mPb54C_k3HqU.woff2',
    ]

    criticalFonts.forEach((fontUrl) => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'font'
      link.type = 'font/woff2'
      link.href = fontUrl
      link.crossOrigin = 'anonymous'
      document.head.appendChild(link)
    })
  }
}

// Export singleton instance
export const fontLoader = new FontLoader()

// Export for use in components
export const monitorFontLoading = () => fontLoader.monitorFontLoading()
export const isFontLoaded = (family: string) => fontLoader.isFontLoaded(family)
export const waitForFont = (family: string, timeout?: number) =>
  fontLoader.waitForFont(family, timeout)
export const getFontMetrics = () => fontLoader.getMetrics()

// Export FontLoader class for testing
export { FontLoader }
