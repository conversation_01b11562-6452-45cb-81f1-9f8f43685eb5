import { describe, it, expect } from 'vitest'
import type { ProgramModel, ProgramProgress, ProgramStats } from '../program'
import { isProgramModel, isProgramProgress, isProgramStats } from '../program'
import type {
  GetUserProgramInfoResponseModel,
  GetUserWorkoutLogAverageResponse,
} from '../api'

describe('Program Types', () => {
  describe('ProgramModel', () => {
    it('should have correct structure', () => {
      const program: ProgramModel = {
        id: 1,
        name: 'Beginner Program',
        description: 'A great program for beginners',
        category: 'Strength',
        totalDays: 90,
        currentDay: 15,
        workoutsCompleted: 10,
        totalWorkouts: 36,
        startDate: '2024-01-01',
        imageUrl: '/images/beginner.jpg',
      }

      expect(program.id).toBe(1)
      expect(program.name).toBe('Beginner Program')
      expect(program.description).toBe('A great program for beginners')
      expect(program.category).toBe('Strength')
      expect(program.totalDays).toBe(90)
      expect(program.currentDay).toBe(15)
      expect(program.workoutsCompleted).toBe(10)
      expect(program.startDate).toBe('2024-01-01')
      expect(program.imageUrl).toBe('/images/beginner.jpg')
    })

    it('should handle optional fields', () => {
      const minimalProgram: ProgramModel = {
        id: 1,
        name: 'Basic Program',
        description: '',
        category: 'General',
        totalDays: 0,
        currentDay: 0,
        workoutsCompleted: 0,
        totalWorkouts: 0,
        startDate: new Date().toISOString(),
      }

      expect(minimalProgram.imageUrl).toBeUndefined()
    })
  })

  describe('ProgramProgress', () => {
    it('should have correct structure', () => {
      const progress: ProgramProgress = {
        percentage: 25,
        daysCompleted: 15,
        totalWorkouts: 30,
        currentWeek: 3,
        workoutsThisWeek: 2,
        remainingWorkouts: 20,
      }

      expect(progress.percentage).toBe(25)
      expect(progress.daysCompleted).toBe(15)
      expect(progress.totalWorkouts).toBe(30)
      expect(progress.currentWeek).toBe(3)
      expect(progress.workoutsThisWeek).toBe(2)
      expect(progress.remainingWorkouts).toBe(20)
    })
  })

  describe('ProgramStats', () => {
    it('should have correct structure', () => {
      const stats: ProgramStats = {
        averageWorkoutTime: 45,
        totalVolume: 50000,
        personalRecords: 5,
        consecutiveWeeks: 4,
        lastWorkoutDate: '2024-01-15',
        totalWorkoutsCompleted: 25,
      }

      expect(stats.averageWorkoutTime).toBe(45)
      expect(stats.totalVolume).toBe(50000)
      expect(stats.personalRecords).toBe(5)
      expect(stats.consecutiveWeeks).toBe(4)
      expect(stats.lastWorkoutDate).toBe('2024-01-15')
      expect(stats.totalWorkoutsCompleted).toBe(25)
    })

    it('should handle optional fields', () => {
      const minimalStats: ProgramStats = {
        averageWorkoutTime: 0,
        totalVolume: 0,
        personalRecords: 0,
        consecutiveWeeks: 0,
        totalWorkoutsCompleted: 0,
      }

      expect(minimalStats.lastWorkoutDate).toBeUndefined()
    })
  })

  describe('Type Guards', () => {
    describe('isProgramModel', () => {
      it('should return true for valid ProgramModel', () => {
        const validProgram = {
          id: 1,
          name: 'Test Program',
          description: 'Test',
          category: 'Strength',
          totalDays: 90,
          currentDay: 1,
          workoutsCompleted: 0,
          startDate: '2024-01-01',
        }

        expect(isProgramModel(validProgram)).toBe(true)
      })

      it('should return false for invalid objects', () => {
        expect(isProgramModel(null)).toBe(false)
        expect(isProgramModel(undefined)).toBe(false)
        expect(isProgramModel({})).toBe(false)
        expect(isProgramModel({ id: 'not-a-number' })).toBe(false)
        expect(isProgramModel({ id: 1, name: 123 })).toBe(false)
      })
    })

    describe('isProgramProgress', () => {
      it('should return true for valid ProgramProgress', () => {
        const validProgress = {
          percentage: 50,
          daysCompleted: 45,
          totalWorkouts: 90,
          currentWeek: 7,
          workoutsThisWeek: 3,
          remainingWorkouts: 45,
        }

        expect(isProgramProgress(validProgress)).toBe(true)
      })

      it('should return false for invalid objects', () => {
        expect(isProgramProgress(null)).toBe(false)
        expect(isProgramProgress(undefined)).toBe(false)
        expect(isProgramProgress({})).toBe(false)
        expect(isProgramProgress({ percentage: 'fifty' })).toBe(false)
      })
    })

    describe('isProgramStats', () => {
      it('should return true for valid ProgramStats', () => {
        const validStats = {
          averageWorkoutTime: 45,
          totalVolume: 50000,
          personalRecords: 5,
          consecutiveWeeks: 4,
          totalWorkoutsCompleted: 25,
        }

        expect(isProgramStats(validStats)).toBe(true)
      })

      it('should return false for invalid objects', () => {
        expect(isProgramStats(null)).toBe(false)
        expect(isProgramStats(undefined)).toBe(false)
        expect(isProgramStats({})).toBe(false)
        expect(isProgramStats({ averageWorkoutTime: 'forty-five' })).toBe(false)
      })
    })
  })

  describe('API Response Transformations', () => {
    it('should transform GetUserProgramInfoResponseModel to ProgramModel', () => {
      const apiResponse: GetUserProgramInfoResponseModel = {
        RecommendedProgram: {
          Id: 1,
          Label: 'Beginner Strength',
          RemainingToLevelUp: 20,
          IconUrl: '/icons/strength.png',
        },
        NextWorkoutTemplate: {
          Id: 1,
          Label: 'Push Day A',
          IsSystemExercise: true,
        },
      }

      // This test documents expected transformation behavior
      const expectedProgram: Partial<ProgramModel> = {
        id: apiResponse.RecommendedProgram.Id,
        name: apiResponse.RecommendedProgram.Label,
        imageUrl: apiResponse.RecommendedProgram.IconUrl,
      }

      expect(expectedProgram.id).toBe(1)
      expect(expectedProgram.name).toBe('Beginner Strength')
      expect(expectedProgram.imageUrl).toBe('/icons/strength.png')
    })

    it('should calculate ProgramProgress from API data', () => {
      const mockResponse: Partial<GetUserWorkoutLogAverageResponse> = {
        TotalWorkoutCompleted: 10,
        ConsecutiveWeeks: 3,
        GetUserProgramInfoResponseModel: {
          RecommendedProgram: {
            Id: 1,
            Label: 'Test Program',
            RemainingToLevelUp: 20,
          },
          NextWorkoutTemplate: {
            Id: 1,
            Label: 'Workout A',
            IsSystemExercise: true,
          },
        },
      }

      // Document expected calculation
      const totalWorkouts = 30 // From program definition
      const completed = mockResponse.TotalWorkoutCompleted || 0
      const expectedPercentage = Math.round((completed / totalWorkouts) * 100)

      expect(expectedPercentage).toBe(33)
    })
  })
})
